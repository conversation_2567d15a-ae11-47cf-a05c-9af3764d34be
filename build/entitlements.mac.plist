<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
  <!-- 启用硬化运行时 -->
  <key>com.apple.security.cs.allow-jit</key>
  <true/>
  
  <!-- 允许无符号可执行内存 -->
  <key>com.apple.security.cs.allow-unsigned-executable-memory</key>
  <true/>
  
  <!-- 允许DYLD环境变量 -->
  <key>com.apple.security.cs.allow-dyld-environment-variables</key>
  <true/>
  
  <!-- 允许调试器附加 -->
  <key>com.apple.security.cs.debugger</key>
  <true/>
  
  <!-- 禁用库验证 -->
  <key>com.apple.security.cs.disable-library-validation</key>
  <true/>
  
  <!-- 网络权限 -->
  <key>com.apple.security.network.client</key>
  <true/>
  
  <key>com.apple.security.network.server</key>
  <true/>
  
  <!-- 摄像头权限 -->
  <key>com.apple.security.device.camera</key>
  <true/>
  
  <!-- 麦克风权限 -->
  <key>com.apple.security.device.microphone</key>
  <true/>
  
  <!-- 文件系统访问 -->
  <key>com.apple.security.files.user-selected.read-write</key>
  <true/>
  
  <key>com.apple.security.files.downloads.read-write</key>
  <true/>
  
  <!-- 应用沙盒临时例外 -->
  <key>com.apple.security.temporary-exception.apple-events</key>
  <array>
    <string>com.apple.systemevents</string>
  </array>
  
  <!-- GPU访问权限 -->
  <key>com.apple.security.device.gpu</key>
  <true/>
  
  <!-- 用户通知 -->
  <key>com.apple.security.personal-information.notifications</key>
  <true/>
</dict>
</plist> 