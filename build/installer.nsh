; 自律农场安装程序自定义脚本
; 注意：不要重复定义MUI_ICON等已在electron-builder中定义的变量

; 安装完成后的操作
Function .onInstSuccess
  ; 创建桌面快捷方式
  CreateShortCut "$DESKTOP\自律农场.lnk" "$INSTDIR\自律农场.exe"
  
  ; 创建开始菜单快捷方式
  CreateDirectory "$SMPROGRAMS\自律农场"
  CreateShortCut "$SMPROGRAMS\自律农场\自律农场.lnk" "$INSTDIR\自律农场.exe"
  CreateShortCut "$SMPROGRAMS\自律农场\卸载自律农场.lnk" "$INSTDIR\Uninstall.exe"
  
  ; 注册文件关联（如果需要）
  WriteRegStr HKCR ".selfgame" "" "SelfGameFile"
  WriteRegStr HKCR "SelfGameFile" "" "自律农场存档文件"
  WriteRegStr HKCR "SelfGameFile\shell\open\command" "" '"$INSTDIR\自律农场.exe" "%1"'
FunctionEnd

; 卸载前的操作
Function un.onInit
  MessageBox MB_ICONQUESTION|MB_YESNO|MB_DEFBUTTON2 "您确定要完全移除自律农场及其所有组件吗？" IDYES +2
  Abort
FunctionEnd

; 卸载完成后的操作
Function un.onUninstSuccess
  ; 删除桌面快捷方式
  Delete "$DESKTOP\自律农场.lnk"
  
  ; 删除开始菜单快捷方式
  RMDir /r "$SMPROGRAMS\自律农场"
  
  ; 清理注册表
  DeleteRegKey HKCR ".selfgame"
  DeleteRegKey HKCR "SelfGameFile"
  
  MessageBox MB_ICONINFORMATION "自律农场已成功从您的计算机中移除。"
FunctionEnd