# 自律农场 - 系统要求

## 📋 概述

自律农场致力于为用户提供流畅的跨平台体验。本文档详细说明了在不同平台上运行应用所需的最低和推荐系统配置。

## 🌐 Web版本（浏览器）

### 支持的浏览器

#### 桌面浏览器
- **Chrome**: 版本 90 及以上 ⭐ **推荐**
- **Firefox**: 版本 88 及以上
- **Safari**: 版本 14 及以上 (macOS)
- **Edge**: 版本 90 及以上
- **Opera**: 版本 75 及以上

#### 移动浏览器
- **Chrome Mobile**: 版本 90 及以上
- **Safari Mobile**: iOS 14 及以上
- **Firefox Mobile**: 版本 88 及以上
- **Edge Mobile**: 版本 90 及以上

### 最低系统要求
- **内存**: 4GB RAM
- **显示器**: 1024 x 768 分辨率
- **网络**: 稳定的互联网连接（2 Mbps 或更高）
- **存储**: 50MB 浏览器缓存空间

### 推荐系统配置
- **内存**: 8GB RAM 或更高
- **显示器**: 1920 x 1080 分辨率或更高
- **网络**: 高速宽带连接（10 Mbps 或更高）
- **存储**: 100MB 可用空间

### 必需的浏览器功能
- **JavaScript**: 必须启用
- **LocalStorage**: 用于本地数据存储
- **Web Audio API**: 音效和背景音乐支持
- **WebGL**: 3D农场渲染（可选，提升体验）
- **通知API**: 提醒和通知功能
- **IndexedDB**: 高级数据存储

## 🖥️ Windows 桌面版

### 支持的 Windows 版本
- **Windows 10**: 版本 1903 (May 2019 Update) 及以上 ⭐ **推荐**
- **Windows 11**: 所有版本

### 最低系统要求
- **处理器**: Intel Core i3 或 AMD 同等级别
- **内存**: 4GB RAM
- **显卡**: DirectX 11 兼容
- **存储**: 500MB 可用硬盘空间
- **显示器**: 1024 x 768 分辨率
- **网络**: 可选（云同步功能需要）

### 推荐系统配置
- **处理器**: Intel Core i5 第8代 或 AMD Ryzen 5
- **内存**: 8GB RAM 或更高
- **显卡**: 独立显卡（提升 3D 渲染性能）
- **存储**: 1GB 可用硬盘空间
- **显示器**: 1920 x 1080 分辨率或更高
- **音响**: 立体声音响或耳机

### 其他要求
- **.NET Framework**: 版本 4.8 或更高
- **Visual C++ Redistributable**: 2019 或更高
- **Windows Defender**: 建议保持更新

## 🍎 macOS 桌面版

### 支持的 macOS 版本
- **macOS Big Sur**: 11.0 及以上
- **macOS Monterey**: 12.0 及以上 ⭐ **推荐**
- **macOS Ventura**: 13.0 及以上
- **macOS Sonoma**: 14.0 及以上

### 最低系统要求
- **处理器**: Intel Core i5 或 Apple M1
- **内存**: 4GB RAM
- **存储**: 500MB 可用磁盘空间
- **显示器**: 1280 x 800 分辨率
- **网络**: 可选（云同步功能需要）

### 推荐系统配置
- **处理器**: Intel Core i7 或 Apple M1/M2
- **内存**: 8GB RAM 或更高
- **存储**: 1GB 可用磁盘空间
- **显示器**: Retina 显示器或 1920 x 1080 分辨率
- **音响**: 内置音响或高质量耳机

### 特殊说明
- **Apple Silicon**: 原生支持 M1/M2 芯片
- **Intel Mac**: 通过 Rosetta 2 运行（性能略降）
- **权限**: 需要授权文件访问和通知权限

## 🐧 Linux 桌面版

### 支持的发行版
- **Ubuntu**: 20.04 LTS 及以上 ⭐ **推荐**
- **Debian**: 11 (Bullseye) 及以上
- **Fedora**: 34 及以上
- **CentOS**: 8 及以上
- **Arch Linux**: 滚动发布
- **openSUSE**: Leap 15.3 及以上

### 最低系统要求
- **处理器**: x86_64 架构
- **内存**: 4GB RAM
- **显卡**: OpenGL 2.1 支持
- **存储**: 500MB 可用磁盘空间
- **桌面环境**: GNOME、KDE、XFCE 或其他

### 推荐系统配置
- **处理器**: 多核 x86_64 处理器
- **内存**: 8GB RAM 或更高
- **显卡**: 现代显卡（提升渲染性能）
- **存储**: 1GB 可用磁盘空间
- **桌面环境**: GNOME 3.38+ 或 KDE Plasma 5.20+

### 依赖库
- **libgtk-3**: GTK+ 3.0 库
- **libasound2**: ALSA 音频库
- **libpulse**: PulseAudio 支持
- **libx11**: X11 显示服务器
- **OpenGL**: 硬件加速支持

## 📱 移动设备支持

### iOS 设备（WebApp）
- **iOS 版本**: 14.0 及以上
- **设备**: iPhone 7 及更新机型
- **Safari**: 必需浏览器
- **存储**: 100MB 可用空间
- **网络**: Wi-Fi 或蜂窝数据

### Android 设备（WebApp）
- **Android 版本**: 8.0 (API 26) 及以上
- **内存**: 3GB RAM 推荐
- **浏览器**: Chrome 90+ 推荐
- **存储**: 100MB 可用空间
- **网络**: Wi-Fi 或蜂窝数据

## ⚡ 性能优化建议

### 硬件优化
1. **内存管理**：
   - 关闭不必要的后台应用
   - 保持至少 2GB 可用内存
   - 定期重启设备清理内存

2. **存储优化**：
   - 保持至少 1GB 可用存储空间
   - 定期清理临时文件
   - 使用 SSD 硬盘获得更好性能

3. **网络优化**：
   - 使用稳定的网络连接
   - 避免高延迟的网络环境
   - 考虑使用有线连接（桌面设备）

### 软件优化
1. **浏览器设置**：
   - 启用硬件加速
   - 清理浏览器缓存
   - 禁用不必要的扩展

2. **系统设置**：
   - 保持操作系统更新
   - 关闭不必要的视觉效果
   - 调整电源管理为高性能模式

## 🔧 故障排除

### 常见问题
1. **应用加载缓慢**：
   - 检查网络连接速度
   - 清理浏览器缓存
   - 尝试刷新页面

2. **音效不工作**：
   - 检查设备音量设置
   - 确认浏览器允许音频播放
   - 检查音频输出设备

3. **3D 渲染问题**：
   - 更新显卡驱动程序
   - 在浏览器中启用硬件加速
   - 降低显示质量设置

### 性能问题诊断
1. **检查系统资源**：
   - 监控 CPU 使用率
   - 查看内存使用情况
   - 检查网络连接状态

2. **浏览器诊断**：
   - 使用开发者工具检查错误
   - 查看网络请求状态
   - 监控 JavaScript 性能

## 📊 兼容性测试

### 测试平台
我们在以下平台上进行了全面测试：
- Windows 10/11 + Chrome/Edge
- macOS Big Sur/Monterey + Safari/Chrome
- Ubuntu 20.04/22.04 + Firefox/Chrome
- iOS 14+ Safari
- Android 10+ Chrome

### 性能基准
- **启动时间**: < 3 秒（推荐配置）
- **页面响应**: < 100ms
- **内存使用**: < 200MB（正常使用）
- **CPU 使用**: < 10%（空闲状态）

## 📞 技术支持

如果您遇到兼容性问题或性能问题，请联系我们：

- **技术支持邮箱**: <EMAIL>
- **系统报告**: 请包含操作系统、浏览器版本等信息
- **错误日志**: 提供浏览器控制台的错误信息
- **性能报告**: 描述具体的性能问题

---

**我们承诺为所有支持的平台提供优质的用户体验！** 🚀

*系统要求可能随应用更新而调整，建议定期查看最新信息。* 