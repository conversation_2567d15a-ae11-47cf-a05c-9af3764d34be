const path = require('path');

// 获取环境变量
const isProduction = process.env.NODE_ENV === 'production';
const isDevelopment = process.env.NODE_ENV === 'development';

// 代码签名配置
const getCodeSignConfig = () => {
  if (isProduction) {
    return {
      // 生产环境使用真实证书
      win: {
        certificateFile: process.env.WIN_CSC_LINK,
        certificatePassword: process.env.WIN_CSC_KEY_PASSWORD,
        certificateSubjectName: process.env.WIN_CSC_NAME || "Self Game Developer",
        signingHashAlgorithms: ['sha256'],
        timeStampServer: 'http://timestamp.digicert.com',
        additionalCertificateFile: process.env.WIN_CSC_INTERMEDIATE_LINK
      },
      mac: {
        identity: process.env.MAC_CSC_NAME || "Self Game Developer",
        hardenedRuntime: true,
        entitlements: "build/entitlements.mac.plist",
        entitlementsInherit: "build/entitlements.mac.plist",
        gatekeeperAssess: false,
        notarize: {
          teamId: process.env.APPLE_TEAM_ID,
          tool: "notarytool",
          appleId: process.env.APPLE_ID,
          appleIdPassword: process.env.APPLE_APP_PASSWORD
        }
      }
    };
  } else {
    return {
      // 开发环境使用自签名或跳过签名
      win: {
        sign: false  // 开发环境跳过Windows签名
      },
      mac: {
        identity: null  // 开发环境跳过macOS签名
      }
    };
  }
};

const config = {
  // 应用基本信息
  appId: "com.selfgame.discipline-farm",
  productName: "自律农场",
  copyright: "Copyright © 2024 Self Game Developer",
  
  // 构建目录配置
  directories: {
    output: "dist-packages",
    buildResources: "build"
  },
  
  // 包含的文件
  files: [
    "dist/**/*",
    "dist-electron/**/*",
    "assets/**/*",
    "!src/**/*",
    "!config/**/*",
    "!scripts/**/*",
    "!node_modules/.cache/**/*",
    "!**/*.{ts,tsx,js.map}",
    "!README.md",
    "!LICENSE"
  ],
  
  // 额外的资源文件
  extraResources: [
    {
      from: "assets/",
      to: "assets/",
      filter: ["**/*"]
    }
  ],
  
  // Windows配置
  win: {
    target: [
      {
        target: "nsis",
        arch: ["x64", "ia32"]
      },
      {
        target: "zip",
        arch: ["x64"]
      }
    ],
    icon: "build/icon.ico",
    publisherName: "Self Game Developer",
    verifyUpdateCodeSignature: isProduction,
    ...getCodeSignConfig().win
  },
  
  // NSIS安装程序配置
  nsis: {
    oneClick: false,
    allowToChangeInstallationDirectory: true,
    allowElevation: true,
    createDesktopShortcut: true,
    createStartMenuShortcut: true,
    shortcutName: "自律农场",
    uninstallDisplayName: "自律农场",
    license: "LICENSE",
    installerIcon: "build/installer.ico",
    uninstallerIcon: "build/uninstaller.ico",
    installerHeaderIcon: "build/installerHeader.ico",
    installerSidebar: "build/installerSidebar.bmp",
    uninstallerSidebar: "build/uninstallerSidebar.bmp",
    // 多语言支持
    language: "2052", // 简体中文
    // 安装程序自定义
    warningsAsErrors: false,
    differentialPackage: false
  },
  
  // macOS配置
  mac: {
    target: [
      {
        target: "dmg",
        arch: ["x64", "arm64"]
      },
      {
        target: "zip",
        arch: ["x64", "arm64"]
      }
    ],
    icon: "build/icon.icns",
    category: "public.app-category.productivity",
    type: "distribution",
    minimumSystemVersion: "10.14.0",
    darkModeSupport: true,
    ...getCodeSignConfig().mac
  },
  
  // DMG配置
  dmg: {
    sign: isProduction,
    title: "自律农场安装包",
    background: "build/dmg-background.png",
    iconSize: 100,
    window: {
      width: 660,
      height: 400
    },
    contents: [
      {
        x: 180,
        y: 170,
        type: "file"
      },
      {
        x: 480,
        y: 170,
        type: "link",
        path: "/Applications"
      }
    ]
  },
  
  // Linux配置
  linux: {
    target: [
      {
        target: "AppImage",
        arch: ["x64"]
      },
      {
        target: "deb",
        arch: ["x64"]
      },
      {
        target: "rpm",
        arch: ["x64"]
      }
    ],
    icon: "build/icons/",
    category: "Productivity",
    desktop: {
      Name: "自律农场",
      Comment: "智能习惯养成游戏",
      Keywords: "game;productivity;habit;self-discipline;",
      StartupWMClass: "自律农场"
    },
    synopsis: "一款通过摄像头监测行为帮助用户建立自律习惯的农场经营游戏",
    description: "自律农场是一款创新的生产力游戏，通过计算机视觉技术监测用户行为，帮助建立良好的自律习惯。游戏采用农场经营的形式，让习惯养成变得有趣且有效。"
  },
  
  // 应用更新配置
  publish: isProduction ? [
    {
      provider: "github",
      owner: "selfgame",
      repo: "discipline-farm",
      private: false
    }
  ] : null,
  
  // 压缩配置
  compression: "maximum",
  
  // 构建前后脚本
  beforeBuild: async (context) => {
    console.log("🔨 开始构建前准备...");
    
    // 确保构建目录存在
    const buildDir = path.join(context.appDir, 'build');
    if (!require('fs').existsSync(buildDir)) {
      require('fs').mkdirSync(buildDir, { recursive: true });
    }
    
    // 检查证书配置
    if (isProduction) {
      console.log("📋 检查代码签名证书配置...");
      
      if (process.platform === 'win32') {
        if (!process.env.WIN_CSC_LINK) {
          console.warn("⚠️ 警告: 未配置Windows代码签名证书");
        }
      } else if (process.platform === 'darwin') {
        if (!process.env.MAC_CSC_NAME) {
          console.warn("⚠️ 警告: 未配置macOS代码签名证书");
        }
      }
    }
  },
  
  afterSign: async (context) => {
    if (isProduction) {
      console.log("✅ 代码签名完成");
      
      // 验证签名
      const { execSync } = require('child_process');
      try {
        if (process.platform === 'win32') {
          execSync(`signtool verify /pa "${context.appOutDir}\\${context.packager.appInfo.productFilename}.exe"`);
          console.log("✅ Windows签名验证通过");
        } else if (process.platform === 'darwin') {
          execSync(`codesign --verify --verbose "${context.appOutDir}/${context.packager.appInfo.productFilename}.app"`);
          console.log("✅ macOS签名验证通过");
        }
      } catch (error) {
        console.warn("⚠️ 签名验证失败:", error.message);
      }
    }
  },
  
  afterPack: async (context) => {
    console.log("📦 打包完成");
    
    // 生成打包报告
    const fs = require('fs');
    const report = {
      timestamp: new Date().toISOString(),
      platform: context.electronPlatformName,
      arch: context.arch,
      version: context.packager.appInfo.version,
      outDir: context.outDir,
      appOutDir: context.appOutDir,
      packager: {
        productName: context.packager.appInfo.productName,
        buildVersion: context.packager.appInfo.buildVersion
      }
    };
    
    fs.writeFileSync(
      path.join(context.outDir, 'pack-report.json'),
      JSON.stringify(report, null, 2)
    );
  },
  
  // 安全配置
  electronDownload: {
    cache: path.join(__dirname, '.electron-cache')
  },
  
  // 其他配置
  forceCodeSigning: isProduction,
  buildDependenciesFromSource: false,
  nodeGypRebuild: false,
  npmRebuild: true
};

module.exports = config; 