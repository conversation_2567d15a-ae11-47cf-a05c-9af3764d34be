# Task ID: 3
# Title: 基础姿态检测系统
# Status: done
# Dependencies: 2
# Priority: high
# Description: 使用MediaPipe实现基础的人体姿态检测和坐姿识别
# Details:
集成MediaPipe库，实现实时的人体关键点检测。开发算法判断用户是否坐在电脑前，是否保持专注姿态。建立姿态数据的处理和分析pipeline。

# Test Strategy:
测试不同坐姿和环境光线下的检测准确性，确保检测延迟小于100ms，准确率大于90%。

# Subtasks:
## 1. 安装和配置MediaPipe依赖 [done]
### Dependencies: None
### Description: 安装MediaPipe JavaScript库，配置必要的依赖项
### Details:
使用npm安装@mediapipe/pose和相关依赖，配置模块导入和类型定义

## 2. 创建姿态检测Hook [done]
### Dependencies: None
### Description: 开发usePoseDetection Hook管理MediaPipe姿态检测功能
### Details:
实现姿态检测初始化、关键点检测、坐姿识别算法和状态管理

## 3. 姿态可视化组件 [done]
### Dependencies: None
### Description: 创建PoseOverlay组件在摄像头画面上显示检测到的关键点
### Details:
绘制骨架连接线、关键点标记，提供视觉反馈显示检测质量

## 4. 专注状态分析算法 [done]
### Dependencies: None
### Description: 开发算法分析姿态数据判断用户专注状态
### Details:
基于头部位置、肩膀角度、身体倾斜等参数判断是否保持专注坐姿，并提供专注分数

