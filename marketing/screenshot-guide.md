# 📸 自律农场功能截图拍摄指南

本文档为自律农场应用的营销截图提供详细的拍摄指导，确保所有截图质量一致、内容丰富、视觉吸引人。

## 📋 目录

1. [截图目标和用途](#截图目标和用途)
2. [技术要求](#技术要求)
3. [界面准备](#界面准备)
4. [核心功能截图](#核心功能截图)
5. [后期处理](#后期处理)
6. [使用场景](#使用场景)

---

## 🎯 截图目标和用途

### 主要目标
- **展示核心功能**: 突出应用的独特价值
- **吸引目标用户**: 激发下载和试用兴趣
- **建立信任感**: 展示专业和可靠的产品形象
- **支持营销**: 为各种营销材料提供素材

### 使用场景
- **应用商店**: App Store、Google Play 展示页面
- **官方网站**: 产品介绍页面的功能展示
- **社交媒体**: 微博、朋友圈、抖音等推广
- **新闻媒体**: 媒体报道和产品评测
- **宣传资料**: 海报、传单、PPT等材料

---

## 🔧 技术要求

### 设备设置
- **显示器**: 24英寸或以上，1920x1080分辨率
- **应用分辨率**: 1920x1080窗口模式
- **截图工具**: Snagit / Lightshot / 系统截图工具
- **文件格式**: PNG（无损格式）
- **色彩模式**: sRGB

### 质量标准
- **分辨率**: 最低1920x1080，推荐更高
- **清晰度**: 所有文字清晰可读
- **无瑕疵**: 无模糊、噪点或压缩伪影
- **色彩准确**: 保持应用原始色彩
- **完整性**: 界面元素完整显示

---

## 🎨 界面准备

### 数据准备
```json
{
  "用户资料": {
    "用户名": "张小明",
    "头像": "专业友好的头像图片",
    "等级": 15,
    "经验值": 2840,
    "总专注时长": "128小时30分钟"
  },
  "农场数据": {
    "已种植作物": 32,
    "收获次数": 18,
    "连续专注天数": 12,
    "当前在种作物": 8
  },
  "成就数据": [
    "专注新手", "习惯养成者", "农场主", "效率达人",
    "坚持达人", "专注大师", "收获达人", "连击王"
  ]
}
```

### 界面美化
- **清理界面**: 隐藏调试信息和开发工具
- **优化布局**: 确保所有元素对齐美观
- **数据丰富**: 使用真实而积极的数据
- **状态最佳**: 确保应用处于最佳显示状态

---

## 📱 核心功能截图

### 1. 应用主界面 (Hero Shot)

**文件名**: `hero-main-interface.png`

**拍摄要求**:
- 展示完整的农场界面
- 显示多种作物的不同生长阶段
- 包含用户等级、经验值等信息
- 摄像头检测窗口处于激活状态

**重点元素**:
- 农场布局美观
- 作物种类丰富
- UI界面清晰
- 专注度显示

**营销文案配合**:
"在自己的专属农场中，通过专注训练培育各种作物"

---

### 2. 摄像头检测功能

**文件名**: `camera-detection-demo.png`

**拍摄要求**:
- 显示摄像头预览窗口
- 展示姿态检测关键点
- 专注度分数显示为85+（良好状态）
- 用户坐姿端正

**重点元素**:
- 检测精度高
- 界面简洁
- 隐私友好设计
- 实时反馈

**营销文案配合**:
"智能摄像头检测，实时监测你的专注状态"

---

### 3. 作物种植界面

**文件名**: `crop-planting-interface.png`

**拍摄要求**:
- 显示作物选择菜单
- 展示四种不同作物类型
- 包含作物详细信息
- 种植按钮突出显示

**重点元素**:
- 作物种类多样
- 信息清晰详细
- 操作简单直观
- 视觉设计精美

**营销文案配合**:
"四种作物类型，对应不同的自律训练场景"

---

### 4. 作物生长动画

**文件名**: `crop-growth-animation.png`

**拍摄要求**:
- 展示作物从幼苗到成熟的状态
- 显示生长进度条
- 包含专注时间统计
- 生长效果明显

**重点元素**:
- 生长变化明显
- 进度指示清晰
- 时间关联显示
- 成就感强烈

**营销文案配合**:
"专注时间越长，作物生长越快"

---

### 5. 数据统计界面

**文件名**: `statistics-dashboard.png`

**拍摄要求**:
- 显示专注度趋势图表
- 展示每日/每周数据
- 包含习惯养成进度
- 数据可视化美观

**重点元素**:
- 图表专业美观
- 数据真实可信
- 趋势积极向上
- 界面清晰易读

**营销文案配合**:
"详细的数据分析，清晰了解自己的专注习惯"

---

### 6. 成就系统界面

**文件名**: `achievement-system.png`

**拍摄要求**:
- 展示已解锁的成就徽章
- 显示成就获取条件
- 包含进度条和奖励信息
- 设计精美有吸引力

**重点元素**:
- 成就种类丰富
- 徽章设计精美
- 进度清晰可见
- 激励效果强

**营销文案配合**:
"丰富的成就系统，让每一次专注都有收获"

---

### 7. 设置配置界面

**文件名**: `settings-configuration.png`

**拍摄要求**:
- 显示主要设置分类
- 展示个性化选项
- 包含隐私设置
- 界面布局清晰

**重点元素**:
- 设置选项丰富
- 分类逻辑清晰
- 隐私保护突出
- 操作简单明了

**营销文案配合**:
"丰富的个性化设置，满足不同用户需求"

---

### 8. 多设备同步

**文件名**: `multi-device-sync.png`

**拍摄要求**:
- 同时显示电脑和手机界面
- 展示数据同步状态
- 显示云端备份图标
- 跨平台兼容性

**重点元素**:
- 同步状态清晰
- 多平台支持
- 数据安全可靠
- 无缝体验

**营销文案配合**:
"跨设备数据同步，随时随地继续你的专注训练"

---

### 9. 新手引导界面

**文件名**: `onboarding-tutorial.png`

**拍摄要求**:
- 显示引导步骤指示
- 展示友好的引导文案
- 包含进度指示器
- 界面简洁清晰

**重点元素**:
- 引导流程清晰
- 文案友好易懂
- 进度指示明确
- 用户体验优秀

**营销文案配合**:
"贴心的新手引导，零门槛快速上手"

---

### 10. 主题个性化

**文件名**: `theme-customization.png`

**拍摄要求**:
- 展示不同主题风格
- 显示颜色搭配选项
- 包含预览效果
- 个性化元素丰富

**重点元素**:
- 主题多样化
- 预览效果真实
- 个性化程度高
- 视觉效果佳

**营销文案配合**:
"多种主题风格，打造你的专属农场"

---

## 🎨 后期处理

### 基础处理
- **尺寸调整**: 根据使用场景调整到合适尺寸
- **色彩校正**: 确保颜色鲜艳但不过度饱和
- **锐化**: 适度锐化增强清晰度
- **压缩**: 在保证质量的前提下适当压缩

### 营销优化
- **添加设备框架**: 为手机截图添加设备外框
- **背景优化**: 为部分截图添加渐变背景
- **文字标注**: 在关键功能上添加说明文字
- **箭头指示**: 用箭头突出重要功能

### 版本输出
```
原始版本: 完整分辨率，无处理
网站版本: 1200x800, 轻度压缩
社交版本: 1080x1080, 正方形裁剪
移动版本: 750x1334, 竖屏适配
缩略图版: 400x300, 高度压缩
```

---

## 📐 使用场景规格

### App Store截图
```
iPhone (6.5"): 1242x2688 或 1284x2778
iPhone (5.5"): 1242x2208
iPad Pro (12.9"): 2048x2732
iPad Pro (11"): 1668x2388
```

### Google Play截图
```
手机: 最小320px, 最大3840px
平板: 最小320px, 最大3840px
推荐比例: 16:9 或 9:16
```

### 官网展示
```
英雄图: 1920x1080
功能展示: 1200x800
缩略图: 400x300
```

### 社交媒体
```
微博配图: 1080x1080 (正方形)
微信朋友圈: 1080x1080
抖音封面: 1080x1920 (9:16)
小红书: 1080x1080 或 3:4
```

---

## 🎯 拍摄检查清单

### 拍摄前检查
- [ ] 应用运行正常，无Bug或错误
- [ ] 测试数据准备完整且真实
- [ ] 界面元素对齐，布局美观
- [ ] 摄像头权限已授予并正常工作
- [ ] 屏幕分辨率设置正确

### 拍摄中检查
- [ ] 所有文字清晰可读
- [ ] 界面完整，无截断元素
- [ ] 颜色还原准确
- [ ] 无不必要的调试信息
- [ ] 操作状态为最佳展示效果

### 拍摄后检查
- [ ] 文件命名规范正确
- [ ] 图片质量符合要求
- [ ] 备份原始文件
- [ ] 整理到对应文件夹
- [ ] 记录拍摄说明

---

## 📊 质量评估标准

### 技术质量 (40%)
- **清晰度**: 文字和图像清晰锐利
- **色彩**: 颜色准确，无偏色
- **完整性**: 界面元素完整无缺失
- **无瑕疵**: 无模糊、噪点等问题

### 内容质量 (35%)
- **功能展示**: 核心功能突出明显
- **数据真实**: 使用真实可信的数据
- **状态最佳**: 展示应用最佳状态
- **信息完整**: 必要信息完整显示

### 营销效果 (25%)
- **视觉吸引**: 界面美观有吸引力
- **卖点突出**: 独特功能突出显示
- **情感联系**: 能引起用户共鸣
- **行动导向**: 激发用户尝试欲望

---

## 📂 文件组织

### 文件夹结构
```
marketing/screenshots/
├── original/           # 原始高分辨率文件
├── web/               # 网站使用版本
├── social/            # 社交媒体版本
├── appstore/          # 应用商店版本
└── print/             # 印刷品使用版本
```

### 命名规范
```
格式: [功能]_[场景]_[版本].[格式]
示例: 
- main-interface_hero_original.png
- camera-detection_demo_web.jpg
- statistics_dashboard_social.png
```

---

**拍摄团队**:
- **UI设计师**: 确保界面美观
- **产品经理**: 确认功能展示重点
- **摄影师**: 负责拍摄质量
- **营销人员**: 确认营销需求

**预计工作量**:
- 准备工作: 2小时
- 拍摄工作: 4小时
- 后期处理: 3小时
- 质量检查: 1小时
- **总计**: 10小时

*截图指南版本: 1.0*  
*最后更新: 2024年6月24日* 