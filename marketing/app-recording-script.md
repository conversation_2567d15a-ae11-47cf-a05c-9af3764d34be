# 📱 自律农场应用功能录制脚本

本文档提供自律农场应用功能演示的详细录制脚本，用于制作产品演示视频中的应用界面部分。

## 📋 录制准备

### 设备设置
- **屏幕分辨率**: 1920x1080 或更高
- **录制软件**: OBS Studio / Camtasia / ScreenFlow
- **录制设置**: 60fps, 无损格式
- **鼠标指针**: 开启高亮效果

### 应用准备
- 清理缓存，确保应用干净状态
- 准备测试数据（用户资料、农场进度等）
- 确保摄像头和麦克风权限已授予
- 关闭所有不必要的通知

---

## 🎬 录制流程

### 场景1: 应用启动和欢迎界面 (5秒)

**操作步骤**:
1. 双击应用图标启动
2. 等待Loading动画完成
3. 显示欢迎界面Logo
4. 停留2秒展示启动界面

**录制要点**:
- 确保启动动画流畅
- Logo显示清晰
- 无卡顿或错误信息

**脚本说明**: 
```
"现在，让我们介绍一款革命性的自律工具——自律农场！"
```

---

### 场景2: 摄像头权限和设置 (10秒)

**操作步骤**:
1. 点击"开始使用"按钮
2. 弹出摄像头权限请求对话框
3. 点击"允许"按钮
4. 显示摄像头预览画面
5. 展示摄像头设置界面（2秒）

**录制要点**:
- 权限请求对话框要完整显示
- 摄像头预览要清晰
- 展示用户面部检测效果

**脚本说明**:
```
"首次使用时，需要允许摄像头访问权限"
```

---

### 场景3: 姿态检测演示 (15秒)

**操作步骤**:
1. 坐在摄像头前，保持良好坐姿
2. 显示姿态检测关键点覆盖
3. 展示专注度分数 (90+)
4. 故意做分心动作（低头看手机）
5. 显示专注度分数下降 (40-)
6. 恢复正常坐姿，分数回升

**录制要点**:
- 关键点检测要准确显示
- 专注度数值变化要明显
- 分心动作要自然真实

**脚本说明**:
```
"通过先进的计算机视觉技术，实时监测你的专注状态"
```

---

### 场景4: 农场界面展示 (10秒)

**操作步骤**:
1. 进入主要农场界面
2. 展示整个农场布局（3秒）
3. 缓慢平移镜头展示农场细节
4. 显示不同作物类型
5. 展示UI界面元素

**录制要点**:
- 农场布局要美观
- 作物状态要多样化
- UI元素要清晰可见

**脚本说明**:
```
"欢迎来到你的专属自律农场"
```

---

### 场景5: 种植作物演示 (12秒)

**操作步骤**:
1. 点击空白土地
2. 显示作物选择菜单
3. 选择"知识花"
4. 播放种植动画
5. 显示幼苗状态
6. 展示作物信息面板

**录制要点**:
- 种植动画要流畅
- 作物信息要详细
- 操作反馈要及时

**脚本说明**:
```
"选择你喜欢的作物开始种植"
```

---

### 场景6: 专注训练和作物生长 (20秒)

**操作步骤**:
1. 开始专注训练模式
2. 保持良好坐姿5秒
3. 显示作物缓慢生长动画
4. 展示生长进度条
5. 故意分心3秒
6. 显示生长暂停提示
7. 恢复专注状态
8. 继续生长动画

**录制要点**:
- 生长动画要明显
- 暂停效果要清晰
- 时间压缩要自然

**脚本说明**:
```
"专注时，作物茁壮成长；分心时，生长暂停"
```

---

### 场景7: 作物成熟和收获 (8秒)

**操作步骤**:
1. 显示成熟的作物
2. 点击成熟的作物
3. 播放收获动画
4. 显示获得的奖励
5. 展示经验值增加

**录制要点**:
- 成熟作物要醒目
- 收获动画要有成就感
- 奖励显示要吸引人

**脚本说明**:
```
"收获成熟的作物，获得经验奖励"
```

---

### 场景8: 数据统计界面 (12秒)

**操作步骤**:
1. 点击统计按钮
2. 显示专注度趋势图表
3. 展示每日专注时长
4. 显示习惯养成进度
5. 展示成就列表

**录制要点**:
- 图表要有实际数据
- 数据要呈现积极趋势
- 界面要专业美观

**脚本说明**:
```
"详细的数据分析，帮你了解自己的专注习惯"
```

---

### 场景9: 设置和个性化 (8秒)

**操作步骤**:
1. 打开设置界面
2. 展示各种设置选项
3. 调整专注敏感度
4. 展示主题切换
5. 显示隐私设置

**录制要点**:
- 设置选项要丰富
- 操作要流畅
- 界面要清晰

**脚本说明**:
```
"丰富的个性化设置，满足不同需求"
```

---

### 场景10: 多种作物展示 (10秒)

**操作步骤**:
1. 展示四种不同作物
2. 知识花（学习专注）
3. 力量树（运动锻炼）
4. 时间菜（时间管理）
5. 冥想莲（冥想放松）

**录制要点**:
- 每种作物特色要明显
- 生长状态要不同
- 视觉效果要丰富

**脚本说明**:
```
"四种作物类型，对应不同的自律训练"
```

---

## 🎨 录制技巧

### 屏幕录制设置
```
录制分辨率: 1920x1080
帧率: 60fps
编码格式: H.264
质量: 高质量（CRF 18-23）
音频: 无（后期添加配音）
```

### 鼠标操作
- **点击**: 突出显示点击位置
- **移动**: 缓慢平滑移动
- **悬停**: 适当停留展示悬停效果
- **拖拽**: 清晰展示拖拽路径

### 操作节奏
- **快速操作**: 菜单切换、按钮点击
- **慢速操作**: 重要功能展示、动画播放
- **停留时间**: 重要界面停留2-3秒
- **过渡时间**: 界面切换使用1秒过渡

### 数据准备
```json
{
  "userProfile": {
    "name": "张小明",
    "level": 15,
    "experience": 2840,
    "focusTime": "120小时"
  },
  "farmData": {
    "cropsPlanted": 28,
    "harvestCount": 15,
    "currentStreak": 7
  },
  "achievements": [
    "专注新手",
    "习惯养成者", 
    "农场主",
    "效率达人"
  ]
}
```

---

## 📊 后期处理要点

### 时间压缩
- **实际录制**: 各场景可录制完整版本
- **剪辑压缩**: 保留关键动作，剪掉等待时间
- **动画加速**: 作物生长动画可适当加速

### 画面增强
- **高亮效果**: 重要按钮添加高亮边框
- **文字标注**: 关键功能添加文字说明
- **动画指示**: 箭头指向重要区域

### 质量检查
- **画面清晰**: 确保所有文字清晰可读
- **操作流畅**: 剪掉卡顿和错误操作
- **色彩一致**: 保持应用界面色彩准确

---

## 🔍 质量标准

### 技术标准
- ✅ 分辨率: 1080p或以上
- ✅ 帧率: 稳定60fps
- ✅ 无卡顿或闪烁
- ✅ 颜色还原准确

### 内容标准
- ✅ 功能演示完整
- ✅ 操作流程清晰
- ✅ 用户界面美观
- ✅ 数据真实可信

### 用户体验
- ✅ 操作易于理解
- ✅ 功能亮点突出
- ✅ 视觉效果吸引人
- ✅ 整体体验流畅

---

## 📱 特殊场景录制

### 摄像头检测效果
```
拍摄角度: 侧面45度
展示内容: 
- 用户操作电脑
- 屏幕显示检测结果
- 实时专注度变化
录制时长: 30秒原始素材
```

### 分心行为演示
```
分心动作:
1. 拿起手机查看 (5秒)
2. 离开座位走动 (3秒)  
3. 趴在桌子上 (3秒)
4. 与他人交谈 (5秒)
检测反应: 实时显示专注度下降
```

### 多设备同步展示
```
设备: 电脑 + 手机
展示: 跨端数据同步
操作: 在一个设备上操作，另一个设备显示更新
```

---

**录制团队配置**：
- **录制操作员**: 熟悉应用操作
- **摄像师**: 负责用户操作拍摄  
- **技术支持**: 确保应用正常运行
- **质量检查**: 检查录制质量

**估计时间**：
- 准备工作: 2小时
- 录制时间: 4小时
- 质量检查: 1小时
- 总计: 7小时

*录制脚本版本：1.0*  
*最后更新：2024年6月24日* 