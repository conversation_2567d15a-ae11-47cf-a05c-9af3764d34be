# 🌱 自律农场 (Self Discipline Farm)

一款通过摄像头监测行为帮助用户建立自律习惯的农场经营游戏。

## 🎮 游戏概念

通过游戏化的农场经营体验，将现实中的自律行为转化为虚拟农场中的种植、收获等游戏机制：

- 📚 **知识花**: 学习专注时种植，收获智慧点数
- 💪 **力量树**: 运动时种植，收获体能点数  
- ⏰ **时间菜**: 作息规律时种植，收获时间管理点数
- 🧘 **冥想莲**: 专注冥想时种植，收获专注力点数

## 🛠️ 技术栈

- **前端**: React + TypeScript + Phaser.js
- **构建工具**: Vite
- **样式**: CSS3 + 响应式设计
- **计算机视觉**: MediaPipe (计划中)
- **状态管理**: Zustand (计划中)

## 📦 安装和运行

### 环境要求
- Node.js >= 16.0.0
- npm >= 8.0.0

### 安装依赖
```bash
npm install
```

### 开发环境运行
```bash
npm run dev
```

项目将在 `http://localhost:3000` 启动

### 构建生产版本
```bash
npm run build
```

### 代码检查
```bash
npm run lint
npm run lint:fix
```

### 代码格式化
```bash
npm run format
```

## 🎯 开发计划

### ✅ 已完成
- [x] 项目环境搭建
- [x] 基础农场界面
- [x] Phaser.js游戏引擎集成
- [x] 简单的作物系统展示

### 🚧 进行中
- [ ] 摄像头集成和权限管理
- [ ] 基础姿态检测系统
- [ ] 行为检测与游戏逻辑连接

### 📋 待开发
- [ ] 完整作物生长系统
- [ ] 用户数据存储
- [ ] 成就和奖励系统
- [ ] 天气系统
- [ ] 多种作物类型
- [ ] 农场升级系统

## 🌟 当前功能

### 农场界面
- 4x4网格农场布局
- 基础作物展示（种子、幼苗、知识花）
- 交互式土地点击
- 美观的UI设计

### 游戏机制
- 点击土地有视觉反馈
- 作物生长阶段展示
- 基础动画效果

## 🎮 操作说明

1. 启动游戏后可以看到农场界面
2. 点击土地格子会有金色光圈效果
3. 观察不同的作物生长阶段
4. 右侧面板显示专注状态和农场统计

## 📁 项目结构

```
src/
├── components/          # React组件
├── game/               # Phaser.js游戏逻辑
│   └── scenes/         # 游戏场景
├── utils/              # 工具函数
├── types/              # TypeScript类型定义
├── stores/             # 状态管理
├── assets/             # 静态资源
├── App.tsx             # 主应用组件
├── main.tsx            # 应用入口
└── index.css           # 全局样式
```

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🔗 相关链接

- [产品需求文档](.taskmaster/docs/prd.txt)
- [任务管理系统](.taskmaster/tasks/)
- [项目规划](https://github.com/your-username/self-discipline-farm/projects)

---

**让我们一起通过游戏化的方式培养更好的自律习惯！** 🌱💪 

# 期货游戏系统

## 增强农场系统 - 作物状态持久化功能

### 🎯 问题解决

**问题描述**: 切换农场品种场景后，种植的农产品会丢失

**解决方案**: 实现了完整的农场数据持久化系统

### 🔧 实现的功能

#### 1. **LocalStorage持久化存储**
- 每个农场类型(混合农场、玉米专业农场、小麦专业农场等)都有独立的存储空间
- 自动保存农场数据到浏览器本地存储
- 页面刷新后自动恢复农场状态

#### 2. **作物状态无缝切换**
- 在切换农场类型之前，自动保存当前所有种植作物的状态
- 包括作物种类、生长阶段、健康度、地块土壤肥力等
- 切换到新农场后，自动恢复这些作物到对应地块

#### 3. **智能数据管理**
- 保存当前农场数据到对应存储位置
- 从新农场类型的存储位置加载数据
- 如果是首次使用某种农场类型，创建优化的初始数据

#### 4. **实时自动保存**
- 每次作物状态更新时自动保存到localStorage
- 组件卸载时确保数据保存
- 无需手动保存操作

### 🎮 使用方法

1. **种植作物**: 在任意农场类型中种植作物
2. **切换农场**: 点击农场类型按钮切换到其他农场
3. **验证持久化**: 作物状态会自动保持，不会丢失
4. **页面刷新测试**: 刷新页面后农场状态依然保持

### 🔄 切换流程

```
当前农场(有作物) → 保存作物状态 → 保存农场数据 → 切换到新农场 → 加载新农场数据 → 恢复作物状态
```

### 💾 存储结构

```javascript
localStorage存储键值:
- "enhanced_farm_mixed" : 混合农场数据
- "enhanced_farm_corn-farm" : 玉米专业农场数据  
- "enhanced_farm_wheat-farm" : 小麦专业农场数据
- ... 其他专业农场数据
```

### ✅ 测试验证

1. 在混合农场种植几种不同作物
2. 切换到玉米专业农场 - 作物状态保持
3. 再切换回混合农场 - 作物状态依然存在
4. 刷新页面 - 所有农场的作物状态都保持

---

## 项目启动

```bash
npm start
```

农场收集系统现在支持完整的数据持久化，解决了切换场景后作物丢失的问题！🎉 