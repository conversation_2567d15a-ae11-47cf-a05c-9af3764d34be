{"name": "self-discipline-farm", "version": "0.1.0", "description": "一款通过摄像头监测行为帮助用户建立自律习惯的农场经营游戏", "main": "dist-electron/main.js", "homepage": "./", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "build-no-check": "vite build", "build:prod": "npm run clean && npm run type-check && npm run lint && vite build --mode production", "build:test": "npm run clean && npm run type-check && vite build --mode test", "build:analyze": "npm run build:prod && npx vite-bundle-analyzer", "build:optimize": "node scripts/build-optimization.js", "build:full-analyze": "ANALYZE=true npm run build:prod && npm run build:optimize", "performance:check": "node scripts/performance-check.js", "performance:lighthouse": "npm run build && lighthouse http://localhost:5173 --view --output=html --output-path=./lighthouse-report.html --chrome-flags=\"--headless\"", "cdn:status": "node scripts/cdn-manager.js status", "cdn:test": "node scripts/cdn-manager.js test", "cdn:test:jsdelivr": "node scripts/cdn-manager.js test jsdelivr", "cdn:test:cloudflare": "node scripts/cdn-manager.js test cloudflare", "cdn:test:aws": "node scripts/cdn-manager.js test aws", "cdn:deploy": "node scripts/cdn-manager.js deploy", "cdn:deploy:jsdelivr": "node scripts/cdn-manager.js deploy jsdelivr", "cdn:deploy:cloudflare": "node scripts/cdn-manager.js deploy cloudflare", "cdn:deploy:aws": "node scripts/cdn-manager.js deploy aws", "cdn:monitor": "node scripts/cdn-manager.js monitor", "cdn:monitor:jsdelivr": "node scripts/cdn-manager.js monitor jsdelivr 120", "cdn:monitor:cloudflare": "node scripts/cdn-manager.js monitor cloudflare 120", "cdn:purge": "node scripts/cdn-manager.js purge", "cdn:purge:cloudflare": "node scripts/cdn-manager.js purge cloudflare", "cdn:purge:aws": "node scripts/cdn-manager.js purge aws", "build:cdn": "npm run build && npm run cdn:status", "deploy:full": "npm run build && npm run cdn:deploy:jsdelivr && npm run cdn:test:jsdelivr", "preview": "vite preview", "preview:build": "npm run build && vite preview", "clean": "rimraf dist dist-electron dist-packages", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint src --ext ts,tsx --fix", "format": "prettier --write src/**/*.{ts,tsx,css,json}", "format:check": "prettier --check src/**/*.{ts,tsx,css,json}", "type-check": "tsc --noEmit", "type-check:watch": "tsc --noEmit --watch", "test": "echo \"No tests specified\"", "precommit": "npm run format && npm run lint:fix && npm run type-check", "electron:dev": "concurrently \"npm run dev\" \"wait-on http://localhost:5173 && electron .\"", "code-sign:check": "node scripts/code-signing.js check", "code-sign:init": "node scripts/code-signing.js init", "code-sign:guide": "node scripts/code-signing.js guide", "electron:build": "npm run build && tsc electron/main.ts --outDir dist-electron && tsc electron/preload.ts --outDir dist-electron", "electron:build:signed": "npm run code-sign:init && npm run electron:build", "electron:pack": "npm run electron:build && electron-builder --config electron-builder.config.js", "electron:pack:win": "npm run electron:build && electron-builder --win --config electron-builder.config.js", "electron:pack:mac": "npm run electron:build && electron-builder --mac --config electron-builder.config.js", "electron:pack:linux": "npm run electron:build && electron-builder --linux --config electron-builder.config.js", "electron:pack:signed": "npm run electron:build:signed && electron-builder --config electron-builder.config.js", "electron:icons": "node scripts/generate-app-icons.js", "electron:test:full": "node scripts/electron-package-test.js full", "electron:test:build": "node scripts/electron-package-test.js build", "electron:test:package": "node scripts/electron-package-test.js package", "electron:test:dev": "node scripts/electron-package-test.js dev", "electron:test:report": "node scripts/electron-package-test.js report", "electron:prepare": "npm run electron:icons && npm run build && npm run electron:build", "dist": "npm run electron:prepare && electron-builder --publish=never --config electron-builder.config.js", "dist:all": "npm run electron:prepare && electron-builder --publish=never --win --mac --linux --config electron-builder.config.js", "dist:signed": "npm run electron:build:signed && electron-builder --publish=never --config electron-builder.config.js", "dev:agriculture": "vite --mode agriculture", "build:agriculture": "vite build --mode agriculture", "preview:agriculture": "vite preview --mode agriculture", "test:agriculture": "vitest run src/managers/*.test.ts src/game/**/*.test.ts", "changeset": "changeset", "version-packages": "changeset version", "release": "npm run build && changeset publish", "commit": "git-cz"}, "dependencies": {"@mediapipe/camera_utils": "^0.3.1675466862", "@mediapipe/drawing_utils": "^0.3.1675466124", "@mediapipe/pose": "^0.5.1675469404", "date-fns": "^2.30.0", "eventemitter3": "^5.0.1", "lucide-react": "^0.263.1", "phaser": "^3.90.0", "react": "^18.2.0", "react-dom": "^18.2.0", "recharts": "^2.8.0", "zustand": "^4.4.1"}, "devDependencies": {"@types/node": "^20.0.0", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-legacy": "^6.1.1", "@vitejs/plugin-react": "^4.0.3", "autoprefixer": "^10.4.14", "concurrently": "^9.1.2", "cssnano": "^7.0.7", "electron": "^36.4.0", "electron-builder": "^26.0.12", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "postcss": "^8.4.27", "prettier": "^3.0.0", "rollup-plugin-visualizer": "^6.0.3", "tailwindcss": "^3.3.3", "typescript": "^5.0.2", "node-fetch": "^3.3.2", "@types/node-fetch": "^2.6.11", "vite": "^6.3.5", "vite-bundle-analyzer": "^0.22.3", "vite-plugin-compression2": "^2.0.1", "vite-plugin-pwa": "^1.0.0", "vite-plugin-windicss": "^1.9.4", "wait-on": "^8.0.3"}, "keywords": ["game", "self-discipline", "farm", "computer-vision", "habit-building", "gamification"], "author": "Self Game Developer", "license": "MIT", "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "build": {"appId": "com.selfgame.discipline-farm", "productName": "自律农场", "copyright": "Copyright © 2024 Self Game Developer", "directories": {"output": "dist-packages"}, "files": ["dist/**/*", "dist-electron/**/*", "assets/**/*"], "mac": {"category": "public.app-category.productivity", "target": [{"target": "dmg", "arch": ["x64", "arm64"]}]}, "win": {"target": [{"target": "nsis", "arch": ["x64"]}]}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}]}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true}}}