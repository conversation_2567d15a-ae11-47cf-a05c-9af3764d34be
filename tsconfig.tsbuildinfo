{"root": ["./src/app.optimized.tsx", "./src/app.tsx", "./src/main.tsx", "./src/adapters/farmsystemintegrator.ts", "./src/audio/audiocache.ts", "./src/audio/audiomanager.ts", "./src/audio/audioperformancemonitor.ts", "./src/audio/audioconfig.ts", "./src/audio/audioplaceholders.ts", "./src/audio/optimizedaudiomanager.ts", "./src/components/appwithaudio.tsx", "./src/components/audioperformancemonitorcomponent.tsx", "./src/components/audiosettings.tsx", "./src/components/audiostatusindicator.tsx", "./src/components/audiotestsuite.tsx", "./src/components/behaviordetectiontest.tsx", "./src/components/behaviordetector.tsx", "./src/components/cameraview.tsx", "./src/components/chinesefuturesinventory.tsx", "./src/components/cropselectionmodal.tsx", "./src/components/cropvisualizer.tsx", "./src/components/decorationdashboard.tsx", "./src/components/decorationmode.tsx", "./src/components/decorationshop.tsx", "./src/components/enhancedfarmui.tsx", "./src/components/enhancedlootboxdemo.tsx", "./src/components/enhancedplantingsystem.tsx", "./src/components/enhancedsynthesisworkbench.tsx", "./src/components/errorboundary.tsx", "./src/components/farminterface.tsx", "./src/components/farmupgradeinterface.tsx", "./src/components/feedbackanalysis.tsx", "./src/components/futuresproducttooltip.tsx", "./src/components/gameaudiointegration.tsx", "./src/components/gamecanvas.tsx", "./src/components/gamedemo.tsx", "./src/components/inventorypanel.tsx", "./src/components/inventorytooltip.tsx", "./src/components/inventorywithsynthesis.tsx", "./src/components/itemcard.tsx", "./src/components/loadingspinner.tsx", "./src/components/lootboxtester.tsx", "./src/components/maingameinterface.tsx", "./src/components/musicplayercontrol.tsx", "./src/components/optimizedaudiointegration.tsx", "./src/components/plantingsystem.tsx", "./src/components/poseoverlay.tsx", "./src/components/responsivelayout.tsx", "./src/components/simpledragsynthesis.tsx", "./src/components/simplelootboxdemo.tsx", "./src/components/synthesispanel.tsx", "./src/components/synthesisworkbench.tsx", "./src/components/thememanager.tsx", "./src/components/tooltip.tsx", "./src/components/unifiedinventorypanel.tsx", "./src/components/achievements/achievementdisplay.tsx", "./src/components/achievements/achievementexample.tsx", "./src/components/achievements/achievementnotification.tsx", "./src/components/achievements/achievementsystemexample.tsx", "./src/components/achievements/datamanagement.tsx", "./src/components/achievements/rewardsoverview.tsx", "./src/components/achievements/index.ts", "./src/components/focus/focusmode.tsx", "./src/components/monitoring/applicationmonitor.tsx", "./src/components/privacy/privacysettings.tsx", "./src/components/recommendations/recommendationinterface.tsx", "./src/components/settings/appearancesettings.tsx", "./src/components/settings/audiosettings.tsx", "./src/components/settings/camerasettings.tsx", "./src/components/settings/datasettings.tsx", "./src/components/settings/focussettings.tsx", "./src/components/settings/generalsettings.tsx", "./src/components/settings/notificationsettings.tsx", "./src/components/settings/privacysettings.tsx", "./src/components/settings/settingsnavigation.tsx", "./src/components/settings/settingspanel.tsx", "./src/components/sync/syncstatus.tsx", "./src/components/testing/testplan.tsx", "./src/components/testing/usertesting.tsx", "./src/components/tutorial/plantingcelebration.tsx", "./src/components/tutorial/tutorialoverlay.tsx", "./src/components/ui/button.tsx", "./src/components/ui/card.tsx", "./src/components/ui/input.tsx", "./src/components/ui/modal.tsx", "./src/components/ui/progressbar.tsx", "./src/components/ui/toast.tsx", "./src/components/ui/index.ts", "./src/config/tutorialsteps.ts", "./src/contexts/gamecontext.tsx", "./src/contexts/tutorialcontext.tsx", "./src/core/abstractions/ievents.ts", "./src/core/abstractions/iitem.ts", "./src/core/abstractions/irepository.ts", "./src/core/abstractions/iservices.ts", "./src/core/domain/item.ts", "./src/core/infrastructure/configmanager.ts", "./src/core/infrastructure/dicontainer.ts", "./src/core/infrastructure/eventbus.ts", "./src/data/achievementsconfig.ts", "./src/data/agriculturalitems.ts", "./src/data/chinesefuturesproducts.ts", "./src/data/cropspecifications.ts", "./src/data/decorationitems.ts", "./src/data/enhancedsynthesisrecipes.ts", "./src/data/lootboxconfigs.ts", "./src/data/lootboxitems.ts", "./src/data/plantingdata.ts", "./src/data/synthesisfailure.ts", "./src/data/synthesisrecipes.ts", "./src/data/weatherachievements.ts", "./src/examples/cdnexample.tsx", "./src/examples/decorationstorageexample.tsx", "./src/game/gameconfig.ts", "./src/game/managers/animationmanager.ts", "./src/game/managers/harvestmanager.ts", "./src/game/managers/plantingmanager.ts", "./src/game/objects/cropsprite.ts", "./src/game/objects/plantingui.ts", "./src/game/scenes/agriculturalfarmscene.ts", "./src/game/scenes/animationtestscene.ts", "./src/game/scenes/enhancedfarmscene.ts", "./src/game/scenes/enhancedplantingscene.ts", "./src/game/scenes/harvesttestscene.ts", "./src/game/scenes/plantingtestscene.ts", "./src/game/scenes/statetestscene.ts", "./src/game/scenes/unifiedagriculturalscene.ts", "./src/hooks/useaudiomanager.ts", "./src/hooks/usecamera.ts", "./src/hooks/usefocussyncservice.ts", "./src/hooks/useoptimizedaudio.ts", "./src/hooks/useposedetection.ts", "./src/managers/achievementmanager.ts", "./src/managers/croptimemanager.ts", "./src/managers/enhancedfarmmanager.ts", "./src/managers/eventmanager.ts", "./src/managers/farmmanager.ts", "./src/managers/focusawarecropmanager.ts", "./src/managers/focustokenmanager.ts", "./src/managers/gamestatemanager.ts", "./src/managers/gamestatemanager_fixed.ts", "./src/managers/gamestatemanager_old.ts", "./src/managers/itemintegrationmanager.ts", "./src/managers/levelmanager.ts", "./src/managers/lootboxmanager.ts", "./src/managers/posebehaviorconnector.ts", "./src/managers/storagemanager.ts", "./src/managers/synthesismanager.ts", "./src/managers/unifieditemmanager.ts", "./src/pages/agriculturaldemo.tsx", "./src/pages/unifiedgamesystem.tsx", "./src/security/csp.ts", "./src/services/achievementdataservice.ts", "./src/services/achievementservice.ts", "./src/services/audioservice.ts", "./src/services/behavioranalyticsservice.ts", "./src/services/cameradeviceservice.ts", "./src/services/dataanalysisengine.ts", "./src/services/dataanalyticscollector.ts", "./src/services/datasecurityservice.ts", "./src/services/decorationintegrationservice.ts", "./src/services/decorationstorageservice.ts", "./src/services/enhancedgrowthsystem.ts", "./src/services/farmupgradedataservice.ts", "./src/services/focusdetectionservice.ts", "./src/services/gameprogressservice.ts", "./src/services/localimageprocessor.ts", "./src/services/personalizedrecommendationengine.ts", "./src/services/privacymanager.ts", "./src/services/recommendationservice.ts", "./src/services/reportgenerator.ts", "./src/services/reportservice.ts", "./src/services/settingsservice.ts", "./src/services/syncservice.ts", "./src/services/trendanalysisengine.ts", "./src/services/tutorialprogressservice.ts", "./src/services/userprofileservice.ts", "./src/services/weatherachievementintegration.ts", "./src/services/weatheraudiomanager.ts", "./src/services/weathereffectcoordinator.ts", "./src/services/weathereffectprocessor.ts", "./src/services/weatherforecastservice.ts", "./src/services/weatherintegrationservice.ts", "./src/services/weathermanager.ts", "./src/services/weathervisualmanager.ts", "./src/storage/databasemanager.ts", "./src/storage/localstorageadapter.ts", "./src/stores/gamestore.ts", "./src/styles/theme.ts", "./src/systems/croprewardsystem.ts", "./src/systems/decorationsystem.ts", "./src/systems/enhancedfarmsystem.ts", "./src/systems/enhancedfarmsystemmanager.ts", "./src/systems/farmunlocksystem.ts", "./src/systems/farmupgradeanimations.ts", "./src/systems/farmupgradedatamanager.ts", "./src/systems/farmupgradesystem.ts", "./src/systems/incentivesystem.ts", "./src/systems/inventorysystem.ts", "./src/systems/livestocksystem.ts", "./src/systems/plantingsystemmanager.ts", "./src/systems/synthesissystem.ts", "./src/test/plantingsystemtest.tsx", "./src/tests/farmsystemintegration.test.ts", "./src/types/achievements.ts", "./src/types/agriculture.ts", "./src/types/crop.ts", "./src/types/currency.ts", "./src/types/decoration.ts", "./src/types/electron.d.ts", "./src/types/enhanced-items.ts", "./src/types/futures.ts", "./src/types/gameitems.ts", "./src/types/gamemodels.ts", "./src/types/inventory.ts", "./src/types/livestock.ts", "./src/types/lootbox.ts", "./src/types/planting.ts", "./src/types/pose.ts", "./src/types/settings.types.ts", "./src/types/synthesis.ts", "./src/types/tutorial.ts", "./src/types/user.ts", "./src/types/weather.ts", "./src/types/weathervisuals.ts", "./src/utils/cameraadapterservice.ts", "./src/utils/experiencesystem.ts", "./src/utils/filesystemadapter.ts", "./src/utils/itemfactory.ts", "./src/utils/platformdetector.ts", "./src/utils/platformoptimizer.ts", "./src/utils/responsiveadapter.ts", "./src/utils/testframework.ts", "./src/utils/adaptivelearning.ts", "./src/utils/cropgrowth.ts", "./src/utils/enhancedposeanalysis.ts", "./src/utils/environmentaladaptation.ts", "./src/utils/lootboxgenerator.ts", "./src/utils/multiframeanalysis.ts", "./src/utils/poseanalysis.ts", "./src/utils/responsive.ts", "./src/utils/timeseriesanalysis.ts", "./src/utils/performance/animationoptimizer.ts", "./src/utils/performance/dataprocessor.ts", "./src/utils/performance/eventsystemoptimizer.ts", "./src/utils/performance/memorymanager.ts", "./src/utils/performance/reactperformanceanalyzer.ts", "./src/utils/performance/timermanager.ts"], "errors": true, "version": "5.8.3"}