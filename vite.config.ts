import { defineConfig, loadEnv } from 'vite'
import react from '@vitejs/plugin-react'
import { resolve } from 'path'
import { VitePWA } from 'vite-plugin-pwa'
import legacy from '@vitejs/plugin-legacy'
import { visualizer } from 'rollup-plugin-visualizer'
import { compression } from 'vite-plugin-compression2'
import { cdnPlugin } from './scripts/vite-cdn-plugin'

// https://vitejs.dev/config/
export default defineConfig(({ command, mode }) => {
  // 加载环境变量
  const env = loadEnv(mode, process.cwd(), '')
  
  const isProduction = mode === 'production'
  const isDevelopment = mode === 'development'

  return {
    plugins: [
      react(),
      
      // PWA配置
      VitePWA({
        registerType: 'autoUpdate',
        workbox: {
          maximumFileSizeToCacheInBytes: 15 * 1024 * 1024, // 15MB
          globPatterns: ['**/*.{js,css,html,ico,png,svg,json,vue,txt,woff2}'],
          runtimeCaching: [
            {
              urlPattern: /^https:\/\/fonts\.googleapis\.com\/.*/i,
              handler: 'CacheFirst',
              options: {
                cacheName: 'google-fonts-cache',
                expiration: {
                  maxEntries: 10,
                  maxAgeSeconds: 60 * 60 * 24 * 365 // 365 days
                }
              }
            },
            {
              urlPattern: /^https:\/\/api\.selfgame\.com\/.*/i,
              handler: 'NetworkFirst',
              options: {
                cacheName: 'api-cache',
                expiration: {
                  maxEntries: 100,
                  maxAgeSeconds: 60 * 60 * 24 // 24 hours
                }
              }
            }
          ]
        },
        manifest: {
          name: '自律农场',
          short_name: '自律农场',
          description: '通过虚拟农场培养自律习惯的应用',
          theme_color: '#4ade80',
          background_color: '#ffffff',
          display: 'standalone',
          orientation: 'portrait',
          scope: '/',
          start_url: '/',
          icons: [
            {
              src: 'icons/icon-72x72.png',
              sizes: '72x72',
              type: 'image/png'
            },
            {
              src: 'icons/icon-96x96.png',
              sizes: '96x96',
              type: 'image/png'
            },
            {
              src: 'icons/icon-128x128.png',
              sizes: '128x128',
              type: 'image/png'
            },
            {
              src: 'icons/icon-144x144.png',
              sizes: '144x144',
              type: 'image/png'
            },
            {
              src: 'icons/icon-152x152.png',
              sizes: '152x152',
              type: 'image/png'
            },
            {
              src: 'icons/icon-192x192.png',
              sizes: '192x192',
              type: 'image/png'
            },
            {
              src: 'icons/icon-384x384.png',
              sizes: '384x384',
              type: 'image/png'
            },
            {
              src: 'icons/icon-512x512.png',
              sizes: '512x512',
              type: 'image/png'
            }
          ]
        }
      }),
      
      // 浏览器兼容性支持
      legacy({
        targets: ['defaults', 'not IE 11']
      }),
      
      // Gzip和Brotli压缩
      compression({
        algorithms: ['gzip', 'brotliCompress'],
        deleteOriginalAssets: false
      }),
      
      // 构建分析器（仅在分析模式下启用）
      ...(process.env.ANALYZE ? [
        visualizer({
          filename: 'dist/stats.html',
          open: true,
          gzipSize: true,
          brotliSize: true
        })
      ] : []),
      
      // CDN插件（仅在生产环境启用）
      ...(isProduction ? [
        cdnPlugin({
          environment: mode,
          versioning: {
            enabled: true,
            format: 'hash'
          },
          include: [
            '.*\\.(js|css|png|jpe?g|gif|svg|woff2?|ttf|eot|ico|webp|mp4|webm|ogg|mp3|wav)$'
          ]
        })
      ] : [])
    ],
    
    resolve: {
      alias: {
        '@': resolve(__dirname, 'src'),
        '@/components': resolve(__dirname, 'src/components'),
        '@/utils': resolve(__dirname, 'src/utils'),
        '@/types': resolve(__dirname, 'src/types'),
        '@/stores': resolve(__dirname, 'src/stores'),
        '@/assets': resolve(__dirname, 'src/assets'),
        '@/config': resolve(__dirname, 'config'),
      },
    },
    
    server: {
      port: 5173,
      open: false, // 在Electron模式下不自动打开浏览器
      cors: true,
      // 开发环境代理配置
      proxy: isDevelopment ? {
        '/api': {
          target: 'http://localhost:3000',
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api/, '')
        }
      } : undefined
    },
    
    build: {
      outDir: 'dist',
      assetsDir: 'assets',
      sourcemap: isDevelopment || env.VITE_SOURCE_MAP === 'true',
      minify: isProduction ? 'terser' : false,
      
      // 生产环境优化
      terserOptions: isProduction ? {
        compress: {
          drop_console: true, // 移除console
          drop_debugger: true, // 移除debugger
          pure_funcs: ['console.log'], // 移除特定函数调用
          passes: 2, // 多次压缩
        },
        mangle: {
          safari10: true, // 支持Safari 10
        },
        format: {
          comments: false, // 移除注释
        }
      } : undefined,
      
      // 构建优化
      rollupOptions: {
        external: ['electron'],
        output: {
          // 代码分割
          manualChunks: isProduction ? {
            // 将React相关库打包到一个chunk
            'react-vendor': ['react', 'react-dom'],
            // 将游戏引擎单独打包
            'phaser-vendor': ['phaser'],
            // 将UI组件库单独打包
            'ui-vendor': ['lucide-react', 'recharts'],
            // 将工具库单独打包
            'utils-vendor': ['date-fns', 'zustand'],
            // 将媒体处理库单独打包
            'media-vendor': [
              '@mediapipe/camera_utils',
              '@mediapipe/drawing_utils',
              '@mediapipe/pose'
            ]
          } : undefined,
          
          // 资源文件命名
          chunkFileNames: isProduction ? 'js/[name]-[hash].js' : 'js/[name].js',
          entryFileNames: isProduction ? 'js/[name]-[hash].js' : 'js/[name].js',
          assetFileNames: (assetInfo) => {
            const info = assetInfo.name?.split('.') || [];
            const ext = info[info.length - 1];
            
            if (/\.(mp4|webm|ogg|mp3|wav|flac|aac)(\?.*)?$/i.test(assetInfo.name || '')) {
              return `media/[name]-[hash].${ext}`;
            }
            if (/\.(png|jpe?g|gif|svg|webp|avif)(\?.*)?$/i.test(assetInfo.name || '')) {
              return `images/[name]-[hash].${ext}`;
            }
            if (/\.(woff2?|eot|ttf|otf)(\?.*)?$/i.test(assetInfo.name || '')) {
              return `fonts/[name]-[hash].${ext}`;
            }
            return `assets/[name]-[hash].${ext}`;
          },
        },
        onwarn: (warning, warn) => {
          // 忽略TypeScript错误
          if (warning.code === 'TYPESCRIPT_ERROR') {
            return
          }
          warn(warning)
        }
      },
      
      // 构建大小限制警告
      chunkSizeWarningLimit: 800,
      
      // 压缩图片和其他资源
      assetsInlineLimit: 4096, // 小于4kb的资源内联为base64
      
      // 构建性能优化
      reportCompressedSize: false, // 禁用gzip压缩大小报告
      cssCodeSplit: true, // 启用CSS代码分割
    },
    
    // 为Electron环境设置base路径
    base: './',
    
    // 优化开发体验
    optimizeDeps: {
      exclude: ['electron'],
      include: [
        'react',
        'react-dom',
        'zustand',
        'date-fns',
        'lucide-react',
        'recharts'
      ],
      // 强制预构建
      force: false,
    },
    
    // CSS配置
    css: {
      modules: {
        localsConvention: 'camelCase'
      },
      preprocessorOptions: {
        scss: {
          additionalData: `@import "src/styles/variables.scss";`
        }
      },
      // 生产环境CSS压缩
      postcss: {
        plugins: isProduction ? [
          require('autoprefixer'),
          require('cssnano')({
            preset: ['default', {
              discardComments: {
                removeAll: true,
              },
              normalizeWhitespace: false,
            }],
          }),
        ] : [require('autoprefixer')]
      },
      devSourcemap: isDevelopment,
    },
    
    // 环境变量
    define: {
      __APP_VERSION__: JSON.stringify(process.env.npm_package_version),
      __BUILD_TIME__: JSON.stringify(new Date().toISOString()),
      __COMMIT_HASH__: JSON.stringify(process.env.COMMIT_HASH || 'unknown'),
    },
    
    // 构建信息
    logLevel: isDevelopment ? 'info' : 'warn',
    
    // 清理输出目录
    clearScreen: false,
    
    // 性能预算
    esbuild: {
      // 在生产环境中移除所有console和debugger
      drop: isProduction ? ['console', 'debugger'] : [],
      // 设置目标浏览器
      target: 'esnext',
      // 压缩空白
      minifyWhitespace: isProduction,
      // 压缩标识符
      minifyIdentifiers: isProduction,
      // 压缩语法
      minifySyntax: isProduction,
      // 忽略TypeScript类型检查错误
      ignoreAnnotations: true
    },
  }
}) 