<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>期货盲盒系统测试</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            max-width: 800px;
            width: 90%;
            text-align: center;
        }
        
        h1 {
            color: #2c5530;
            font-size: 2.5rem;
            margin-bottom: 30px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        
        .icons {
            font-size: 4rem;
            margin: 30px 0;
            display: flex;
            justify-content: center;
            gap: 20px;
        }
        
        .description {
            color: #666;
            font-size: 1.2rem;
            line-height: 1.6;
            margin-bottom: 40px;
        }
        
        .boxes {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        
        .box {
            background: linear-gradient(145deg, #f0f0f0, #ffffff);
            border-radius: 15px;
            padding: 30px 20px;
            box-shadow: 5px 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .box:hover {
            transform: translateY(-5px);
            box-shadow: 8px 8px 25px rgba(0,0,0,0.15);
        }
        
        .box-icon {
            font-size: 3rem;
            margin-bottom: 15px;
        }
        
        .box h3 {
            margin: 10px 0;
            font-size: 1.3rem;
        }
        
        .box p {
            color: #888;
            font-size: 0.9rem;
            margin: 0;
        }
        
        .status {
            background: linear-gradient(145deg, #fff3e0, #ffe0b2);
            border: 2px solid #FF9800;
            border-radius: 15px;
            padding: 20px;
            color: #E65100;
            font-weight: bold;
            font-size: 1.1rem;
        }
        
        .agricultural { color: #4CAF50; }
        .industrial { color: #2196F3; }
        .equipment { color: #FF9800; }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
        
        .icons span {
            animation: float 3s ease-in-out infinite;
        }
        
        .icons span:nth-child(1) { animation-delay: 0s; }
        .icons span:nth-child(2) { animation-delay: 0.5s; }
        .icons span:nth-child(3) { animation-delay: 1s; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎁 期货盲盒系统</h1>
        
        <div class="icons">
            <span>📦</span>
            <span>🌾</span>
            <span>💰</span>
        </div>
        
        <p class="description">
            这是期货游戏盲盒系统的演示页面。系统基于中国真实期货品种，
            包含农业产品、工业产品和装备道具，提供完整的合成和品质系统。
        </p>
        
        <div class="boxes">
            <div class="box">
                <div class="box-icon">🌾</div>
                <h3 class="agricultural">农业盒</h3>
                <p>玉米、小麦、大豆、棉花、白糖、菜籽油等12种农业期货品种</p>
            </div>
            
            <div class="box">
                <div class="box-icon">🏭</div>
                <h3 class="industrial">工业盒</h3>
                <p>铜、铝、黄金、螺纹钢、橡胶、原油等17种工业期货品种</p>
            </div>
            
            <div class="box">
                <div class="box-icon">⚡</div>
                <h3 class="equipment">装备盒</h3>
                <p>聚焦眼镜、专注耳机、能量手环、自律时钟等专注装备</p>
            </div>
        </div>
        
        <div class="status">
            ✅ 测试页面加载成功！
            <br><br>
            如果您看到这个页面，说明基础环境运行正常。
            <br>
            主应用中的盲盒系统应该也能正常工作。
        </div>
    </div>
</body>
</html> 