# 🌾 农产品道具系统

一个完整的农场游戏系统，包含专注代币、盲盒抽卡、道具合成和农田种植功能。

## 🎯 核心功能

### 1. 专注代币系统
- **获取方式**: 保持专注状态获得代币
- **每日限制**: 防止过度刷取，保持游戏平衡
- **实时监控**: 检测用户专注状态，自动发放奖励
- **用途**: 购买盲盒、解锁农田、购买道具

### 2. 盲盒抽卡系统
- **多种盲盒**: 不同稀有度和主题的盲盒
- **随机奖励**: 基于概率的道具生成
- **保底机制**: 确保玩家获得最低价值奖励
- **动画效果**: 丰富的开箱动画体验

### 3. 道具合成系统
- **2:1合成**: 两个同品质同品种道具合成更高品质
- **失败机制**: 包含多种失败后果和保护机制
- **成功率递减**: 品质越高成功率越低
- **保护道具**: 提供保险和成功率加成

### 4. 农田种植系统
- **农田管理**: 解锁、升级农田槽位
- **作物生长**: 真实的生长周期和环境影响
- **照料系统**: 浇水、施肥影响产量
- **收获奖励**: 获得农产品和经验

### 5. 农产品品种
基于中国期货市场上市的农产品品种：
- **大连商品交易所**: 玉米、大豆、豆粕、豆油、棕榈油、玉米淀粉、鸡蛋等
- **郑州商品交易所**: 小麦、水稻、菜籽、棉花、白糖、苹果、红枣等
- **上海期货交易所**: 天然橡胶

## 🎮 品质等级系统

| 品质 | 颜色 | 产量范围 | 成功率 | 特色 |
|------|------|----------|--------|------|
| 🔘 普通 | 灰色 | 1-3/天 | 95% | 新手友好 |
| 🟢 优良 | 绿色 | 4-6/天 | 90% | 稳定产出 |
| 🔵 稀有 | 蓝色 | 7-10/天 | 85% | 高效收益 |
| 🟠 史诗 | 橙色 | 12-16/天 | 75% | 精英道具 |
| 🟡 传说 | 金色 | 18-25/天 | 60% | 顶级珍品 |
| 🔴 神话 | 金红 | 30-40/天 | - | 至高神器 |

## 🚀 快速开始

### 环境要求
- Node.js 18+
- TypeScript 5.0+
- React 18+
- Phaser 3.70+

### 安装依赖
```bash
npm install phaser@^3.70.0 eventemitter3@^5.0.1
npm install -D @types/node@^20.0.0
```

### 运行项目
```bash
# 开发模式
npm run dev:agriculture

# 构建生产版本
npm run build:agriculture

# 预览构建结果
npm run preview:agriculture

# 运行测试
npm run test:agriculture
```

### 访问演示
启动项目后，访问 `/agricultural-demo` 路径查看完整演示。

## 📁 项目结构

```
src/
├── types/
│   └── agriculture.ts          # 核心类型定义
├── data/
│   ├── agriculturalItems.ts    # 农产品配置
│   └── synthesisFailure.ts     # 合成失败配置
├── managers/
│   ├── FocusTokenManager.ts    # 专注代币管理
│   ├── LootBoxManager.ts       # 盲盒管理
│   ├── SynthesisManager.ts     # 合成管理
│   └── FarmManager.ts          # 农田管理
├── game/
│   ├── scenes/
│   │   └── FarmScene.ts        # 农场场景
│   └── GameConfig.ts           # 游戏配置
└── pages/
    └── AgriculturalDemo.tsx    # 演示页面
```

## 🎯 核心设计理念

### 1. 数值平衡
- **经济循环**: 专注→代币→盲盒→道具→合成→高级道具
- **风险收益**: 高品质合成风险大但收益高
- **时间投入**: 真实的生长周期鼓励持续参与

### 2. 用户体验
- **渐进式**: 从简单到复杂的学习曲线
- **即时反馈**: 每个操作都有明确的视觉反馈
- **成就感**: 合成成功和收获的满足感

### 3. 游戏性
- **策略性**: 选择合成时机和保护策略
- **收集性**: 收集不同品种和品质的道具
- **社交性**: 可扩展交易和排行榜功能

## 🔧 技术架构

### 管理器模式
每个功能模块都有独立的管理器类，负责：
- 状态管理
- 业务逻辑
- 事件发布
- 数据持久化

### 事件驱动
使用 EventEmitter 模式实现模块间通信：
```typescript
// 监听事件
farmManager.on('cropHarvested', (data) => {
  // 处理收获事件
})

// 发布事件
this.emit('cropHarvested', harvestData)
```

### Phaser 集成
- **场景管理**: 模块化的游戏场景
- **资源管理**: 动态纹理生成和管理
- **输入处理**: 鼠标、键盘、触摸支持
- **动画系统**: 流畅的过渡和反馈动画

## 📊 数值策划

### 专注代币获取
- **基础收益**: 1代币/分钟
- **专注奖励**: 连续60秒后开始计算
- **每日上限**: 500代币（可配置）
- **加成机制**: VIP等级、活动加成

### 盲盒价格体系
- **基础盲盒**: 50代币 - 普通道具为主
- **高级盲盒**: 200代币 - 稀有道具概率提升
- **限定盲盒**: 500代币 - 特殊主题和高价值道具

### 合成成本
- **金币消耗**: 100 → 500 → 2000 → 8000 → 30000
- **时间消耗**: 30秒 → 1分钟 → 3分钟 → 10分钟 → 30分钟
- **等级限制**: 1级 → 10级 → 25级 → 40级 → 60级

## 🎨 美术设计

### 视觉风格
- **简洁明快**: 卡通风格，色彩丰富
- **层次分明**: 清晰的品质区分
- **动态感强**: 丰富的动画效果

### 颜色系统
- **品质区分**: 每个品质有独特的颜色标识
- **状态提示**: 绿色=正常，红色=需要照料，金色=可收获
- **UI主题**: 自然绿色为主，蓝色为辅

## 🔮 扩展功能

### 1. 市场系统
- **玩家交易**: P2P道具交易
- **价格波动**: 基于供需的动态定价
- **拍卖行**: 竞价获得稀有道具

### 2. 公会系统
- **团队合作**: 共同完成大型项目
- **资源共享**: 公会仓库和援助
- **竞争排名**: 公会间的比拼

### 3. 季节活动
- **限时主题**: 节日特殊农产品
- **活动任务**: 额外奖励和成就
- **排行榜**: 个人和服务器排名

### 4. NFT集成
- **区块链资产**: 珍稀道具NFT化
- **跨平台流通**: 不同游戏间的资产互通
- **真实价值**: 现实经济价值的数字资产

## 📈 商业模式

### 1. 内购道具
- **时间加速**: 加快生长和合成时间
- **保护道具**: 提升合成成功率
- **装饰物品**: 个性化农场外观

### 2. 订阅服务
- **VIP特权**: 代币获取加成、专属盲盒
- **无广告**: 去除广告获得更好体验
- **云存档**: 多设备同步游戏进度

### 3. 广告变现
- **奖励视频**: 观看广告获得代币或道具
- **Banner展示**: 非干扰性的广告展示
- **赞助活动**: 品牌合作的特殊活动

## 🧪 测试策略

### 单元测试
```bash
npm run test:agriculture
```

### 功能测试
- 专注代币获取和消耗
- 盲盒开启和道具生成
- 合成成功和失败流程
- 农田种植和收获

### 性能测试
- 大量道具时的渲染性能
- 长时间运行的内存使用
- 网络请求的响应时间

## 📱 移动端适配

### 响应式设计
- **自适应布局**: 支持各种屏幕尺寸
- **触摸优化**: 友好的触摸操作
- **性能优化**: 移动设备性能考虑

### PWA支持
- **离线功能**: 基础功能离线可用
- **桌面图标**: 可安装到主屏幕
- **推送通知**: 作物成熟提醒

## 🌍 国际化

### 多语言支持
- 中文（简体/繁体）
- 英文
- 日文
- 韩文

### 本地化考虑
- **农产品名称**: 适应不同地区的农业文化
- **节日活动**: 结合当地传统节日
- **支付方式**: 支持本地支付渠道

## 🚀 部署方案

### 开发环境
```bash
npm run dev:agriculture
```

### 测试环境
```bash
npm run build:agriculture
npm run preview:agriculture
```

### 生产环境
- **CDN部署**: 静态资源CDN加速
- **容器化**: Docker容器部署
- **监控告警**: 实时性能监控

## 📞 技术支持

如有问题，请通过以下方式联系：
- GitHub Issues
- 技术文档
- 开发者论坛

---

## 📝 更新日志

### v1.0.0 (2024-01-XX)
- ✅ 实现专注代币系统
- ✅ 实现盲盒抽卡系统  
- ✅ 实现道具合成系统
- ✅ 实现农田种植系统
- ✅ 集成Phaser游戏引擎
- ✅ 完成基础UI界面
- ✅ 支持中国期货农产品品种

---

🌾 **农产品道具系统** - 专注代币 + 盲盒抽卡 + 道具合成 + 农田种植的完整游戏体验！ 