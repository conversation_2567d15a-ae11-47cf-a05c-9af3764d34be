# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

**自律农场 (Self Discipline Farm)** is a habit-building application that combines computer vision-based behavior monitoring with gamified farming simulation. Built as both a web app and Electron desktop application.

## Development Commands

### Essential Development Workflow
```bash
# Start development server
npm run dev                    # Web development on port 5173
npm run electron:dev           # Electron development with hot reload

# Build for production
npm run build                  # Web build
npm run electron:build         # Electron build
npm run dist                   # Package Electron app (all platforms)
npm run dist:all               # Package for Windows, macOS, and Linux

# Code quality
npm run lint                   # ESLint check
npm run lint:fix               # ESLint auto-fix
npm run format                 # Prettier formatting
npm run type-check             # TypeScript type checking
npm run precommit              # Run format + lint + type-check
```

### Testing and Analysis
```bash
npm run electron:test:full     # Full Electron testing suite
npm run performance:check      # Performance analysis
npm run build:analyze          # Bundle analysis with visualizer
```

### Specialized Builds
```bash
npm run build:prod             # Production build with full optimization
npm run build:test             # Test environment build
npm run dev:agriculture        # Agriculture-specific development mode
```

## Technology Stack & Architecture

### Core Technologies
- **Frontend**: React 18 + TypeScript
- **Game Engine**: Phaser.js 3.90 for 2D farming simulation
- **Build Tool**: Vite 6.3.5 with advanced optimization
- **Desktop**: Electron 36.4 for cross-platform distribution
- **Computer Vision**: MediaPipe (@mediapipe/pose, @mediapipe/camera_utils)
- **State Management**: Zustand 4.4.1

### Key Application Structure
```
src/
├── App.tsx                        # Main React application entry
├── pages/
│   ├── UnifiedGameSystem.tsx      # Primary game system page
│   └── AgriculturalDemo.tsx       # Agricultural demonstration
├── game/                          # Phaser.js game engine logic
│   ├── scenes/                    # Game scenes (Farm, Planting, Agricultural)
│   ├── managers/                  # Game state and resource managers
│   └── objects/                   # Game objects and sprites
├── components/                    # React UI components
├── managers/                      # Business logic managers
├── services/                      # External service integrations
├── stores/                        # Zustand state management
└── types/                         # TypeScript type definitions

electron/
├── main.ts                        # Electron main process
├── preload.ts                     # Preload script for security
└── services/                      # Electron-specific services
```

## Core Game Systems

### Multi-Scene Architecture
The game uses a sophisticated scene management system:
- **Enhanced Farm Scene**: Main farming simulation with 8x6 grid layout
- **Agricultural Scene**: Specialized crop management interface
- **Planting Scene**: Enhanced planting mechanics with growth stages

### Plant Types & Growth System
Four main plant categories tied to self-discipline habits:
- **Knowledge Flowers** (知识花): Represents learning and study habits
- **Strength Trees** (力量树): Physical exercise and health tracking
- **Time Vegetables** (时间菜): Time management and productivity
- **Meditation Lotus** (冥想莲): Mindfulness and focus practices

### Computer Vision Integration
- MediaPipe pose detection for posture monitoring
- Real-time camera feed processing for behavior analysis
- Focus session tracking tied to plant growth mechanics

### Futures Trading Mini-Game
- Chinese agricultural commodities simulation (corn, wheat, soybeans)
- Lootbox system with rarity-based rewards
- Complex inventory management with synthesis mechanics

## Important Configuration Details

### Development Environment
- **Development server**: Runs on port 5173
- **Hot reload**: Supported for both web and Electron
- **TypeScript**: Configured with relaxed strict mode for rapid development
- **Path aliases**: `@/` maps to `src/`, `@/components` to `src/components`, etc.

### Build System Features
- **Multi-platform Electron packaging**: Windows (NSIS), macOS (DMG), Linux (AppImage)
- **Advanced bundle optimization**: Code splitting, compression, tree shaking
- **CDN integration**: Multi-provider support (jsDelivr, Cloudflare, AWS)
- **PWA support**: Service worker, offline capabilities
- **Performance monitoring**: Bundle analysis and optimization tools

### Data Persistence
The application implements comprehensive localStorage-based persistence:
- **Farm state**: Each farm type has independent storage (`enhanced_farm_mixed`, `enhanced_farm_corn-farm`, etc.)
- **Crop data**: Growth stages, health, soil fertility automatically saved
- **Seamless switching**: Farm type changes preserve all planted crops

## Key Implementation Patterns

### Game Scene Management
When working with Phaser scenes, always:
- Save current farm data before scene transitions using the farm's `saveFarmData()` method
- Load appropriate data for new scenes from localStorage
- Ensure crop states persist across scene switches

### State Management with Zustand
- Use stores in `src/stores/` for global application state
- Game-specific state is managed within Phaser scenes
- Persistence layer automatically handles farm data

### Computer Vision Integration
- MediaPipe components are initialized in relevant React components
- Pose detection results feed into focus tracking systems
- Camera permissions and error handling are built into the UI

### Electron Security
- Context isolation enabled with proper preload scripts
- No node integration in renderer processes
- IPC communication follows security best practices

## Development Best Practices

### Working with the Game Engine
- Phaser scenes are in `src/game/scenes/`
- Game objects and sprites have dedicated files in `src/game/objects/`
- Use the existing game managers for resource and state management

### React Component Guidelines
- UI components should integrate cleanly with Phaser game canvas
- Use the established component patterns in `src/components/`
- Leverage Zustand stores for cross-component state

### Build and Deployment
- Always run type-check before production builds
- Use `npm run build:analyze` to monitor bundle sizes
- Electron builds require proper code signing setup (see scripts/)

### Performance Considerations
- Game assets are optimized for web and Electron deployment
- Bundle splitting ensures optimal loading for different components
- Monitor build output for size warnings (limit: 800kb chunks)

## Common Development Tasks

### Adding New Game Features
1. Implement game logic in appropriate Phaser scene (`src/game/scenes/`)
2. Add UI components in React (`src/components/`)
3. Update state management in relevant stores
4. Ensure data persistence if needed
5. Test in both web and Electron environments

### Computer Vision Features
1. Extend MediaPipe integration in relevant components
2. Update pose detection logic for new behaviors
3. Connect detection results to game progression systems
4. Test camera permissions and error handling

### Electron-Specific Features
1. Add new services in `electron/services/`
2. Update preload script if renderer communication needed
3. Ensure security practices are maintained
4. Test packaging for all target platforms

## File Organization Notes

- Game assets in `src/assets/` and `public/assets/`
- Electron build outputs to `dist-electron/`
- Package outputs to `dist-packages/`
- Development uses Vite's built-in HMR
- Production builds are optimized with terser and compression

The codebase demonstrates sophisticated integration of web technologies, game development, computer vision, and desktop application deployment in a cohesive habit-building platform.