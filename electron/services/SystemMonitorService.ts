import { exec, spawn, ChildProcess } from 'child_process';
import { promises as fs } from 'fs';
import { join } from 'path';
import { app } from 'electron';
import { RewardControlService, RewardType } from './RewardControlService';

// 应用程序信息接口
export interface AppInfo {
  name: string;
  processId: number;
  windowTitle: string;
  executablePath: string;
  isActive: boolean;
  timestamp: number;
}

// 系统监控配置
export interface MonitorConfig {
  interval: number; // 监控间隔（毫秒）
  enableLogging: boolean; // 是否启用日志
  maxLogEntries: number; // 最大日志条目数
  trackInactiveApps: boolean; // 是否跟踪非活跃应用
}

// 监控事件类型
export type MonitorEvent = 'app-changed' | 'app-started' | 'app-closed' | 'violation-detected';

// 事件监听器
type EventListener = (data: any) => void;

/**
 * 系统应用监控服务
 * 支持跨平台的应用程序监控和管理
 */
export class SystemMonitorService {
  private static instance: SystemMonitorService | null = null;
  
  private config: MonitorConfig;
  private isMonitoring = false;
  private monitoringInterval: NodeJS.Timeout | null = null;
  private currentApp: AppInfo | null = null;
  private appHistory: AppInfo[] = [];
  private eventListeners = new Map<MonitorEvent, EventListener[]>();
  
  // 平台特定的监控进程
  private monitoringProcess: ChildProcess | null = null;
  
  // 白名单应用
  private whitelistApps: string[] = [];
  private violationStartTime: number | null = null;
  private violationThreshold = 5 * 60 * 1000; // 5分钟
  
  // 奖励控制服务
  private rewardControlService: RewardControlService;
  
  private constructor(config: Partial<MonitorConfig> = {}) {
    this.config = {
      interval: 1000, // 默认1秒检查一次
      enableLogging: true,
      maxLogEntries: 1000,
      trackInactiveApps: false,
      ...config
    };
    
    // 初始化奖励控制服务
    this.rewardControlService = RewardControlService.getInstance();
    this.setupRewardControlListeners();
  }
  
  static getInstance(config?: Partial<MonitorConfig>): SystemMonitorService {
    if (!this.instance) {
      this.instance = new SystemMonitorService(config);
    }
    return this.instance;
  }
  
  /**
   * 开始监控系统应用
   */
  async startMonitoring(): Promise<void> {
    if (this.isMonitoring) {
      throw new Error('监控已在运行中');
    }
    
    this.isMonitoring = true;
    
    // 根据平台启动不同的监控方式
    switch (process.platform) {
      case 'win32':
        await this.startWindowsMonitoring();
        break;
      case 'darwin':
        await this.startMacOSMonitoring();
        break;
      case 'linux':
        await this.startLinuxMonitoring();
        break;
      default:
        throw new Error(`不支持的操作系统: ${process.platform}`);
    }
    
    this.log('开始系统应用监控');
  }
  
  /**
   * 停止监控
   */
  async stopMonitoring(): Promise<void> {
    if (!this.isMonitoring) return;
    
    this.isMonitoring = false;
    
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }
    
    if (this.monitoringProcess) {
      this.monitoringProcess.kill();
      this.monitoringProcess = null;
    }
    
    this.log('停止系统应用监控');
  }
  
  /**
   * Windows平台监控
   */
  private async startWindowsMonitoring(): Promise<void> {
    this.monitoringInterval = setInterval(async () => {
      try {
        const appInfo = await this.getWindowsActiveApp();
        await this.handleAppChange(appInfo);
      } catch (error) {
        this.log(`Windows监控错误: ${error}`);
      }
    }, this.config.interval);
  }
  
  /**
   * macOS平台监控
   */
  private async startMacOSMonitoring(): Promise<void> {
    this.monitoringInterval = setInterval(async () => {
      try {
        const appInfo = await this.getMacOSActiveApp();
        await this.handleAppChange(appInfo);
      } catch (error) {
        this.log(`macOS监控错误: ${error}`);
      }
    }, this.config.interval);
  }
  
  /**
   * Linux平台监控
   */
  private async startLinuxMonitoring(): Promise<void> {
    this.monitoringInterval = setInterval(async () => {
      try {
        const appInfo = await this.getLinuxActiveApp();
        await this.handleAppChange(appInfo);
      } catch (error) {
        this.log(`Linux监控错误: ${error}`);
      }
    }, this.config.interval);
  }
  
  /**
   * 获取Windows活跃应用
   */
  private async getWindowsActiveApp(): Promise<AppInfo> {
    return new Promise((resolve, reject) => {
      const command = `
        $proc = Get-Process | Where-Object {$_.MainWindowTitle -ne ""} | Select-Object -First 1
        if ($proc) {
          $obj = @{
            Name = $proc.ProcessName
            Id = $proc.Id
            WindowTitle = $proc.MainWindowTitle
            Path = $proc.Path
          }
          $obj | ConvertTo-Json
        }
      `;
      
      exec(`powershell -Command "${command}"`, (error, stdout) => {
        if (error) {
          reject(error);
          return;
        }
        
        try {
          const data = JSON.parse(stdout.trim());
          resolve({
            name: data.Name || 'Unknown',
            processId: data.Id || 0,
            windowTitle: data.WindowTitle || '',
            executablePath: data.Path || '',
            isActive: true,
            timestamp: Date.now()
          });
        } catch (parseError) {
          reject(parseError);
        }
      });
    });
  }
  
  /**
   * 获取macOS活跃应用
   */
  private async getMacOSActiveApp(): Promise<AppInfo> {
    return new Promise((resolve, reject) => {
      const script = `
        tell application "System Events"
          set frontApp to name of first application process whose frontmost is true
          set frontAppID to unix id of first application process whose frontmost is true
          return frontApp & "|" & frontAppID
        end tell
      `;
      
      exec(`osascript -e '${script}'`, (error, stdout) => {
        if (error) {
          reject(error);
          return;
        }
        
        const [appName, processId] = stdout.trim().split('|');
        resolve({
          name: appName || 'Unknown',
          processId: parseInt(processId) || 0,
          windowTitle: appName || '',
          executablePath: '',
          isActive: true,
          timestamp: Date.now()
        });
      });
    });
  }
  
  /**
   * 获取Linux活跃应用
   */
  private async getLinuxActiveApp(): Promise<AppInfo> {
    return new Promise((resolve, reject) => {
      // 先获取活跃窗口ID
      exec('xprop -root _NET_ACTIVE_WINDOW', (error, stdout) => {
        if (error) {
          reject(error);
          return;
        }
        
        const windowId = stdout.match(/0x[0-9a-f]+/)?.[0];
        if (!windowId) {
          reject(new Error('无法获取活跃窗口ID'));
          return;
        }
        
        // 获取窗口信息
        exec(`xprop -id ${windowId} WM_NAME _NET_WM_PID`, (error2, stdout2) => {
          if (error2) {
            reject(error2);
            return;
          }
          
          const nameMatch = stdout2.match(/WM_NAME\(.*?\) = "(.*)"/);
          const pidMatch = stdout2.match(/_NET_WM_PID\(.*?\) = (\d+)/);
          
          const windowTitle = nameMatch?.[1] || 'Unknown';
          const processId = parseInt(pidMatch?.[1] || '0');
          
          // 获取进程名
          exec(`ps -p ${processId} -o comm=`, (error3, stdout3) => {
            const appName = stdout3.trim() || 'Unknown';
            
            resolve({
              name: appName,
              processId,
              windowTitle,
              executablePath: '',
              isActive: true,
              timestamp: Date.now()
            });
          });
        });
      });
    });
  }
  
  /**
   * 处理应用变化
   */
  private async handleAppChange(newApp: AppInfo): Promise<void> {
    const previousApp = this.currentApp;
    
    // 检查是否真的有变化
    if (previousApp && previousApp.name === newApp.name && 
        previousApp.processId === newApp.processId) {
      return;
    }
    
    this.currentApp = newApp;
    this.addToHistory(newApp);
    
    // 触发应用变化事件
    this.emit('app-changed', { previous: previousApp, current: newApp });
    
    // 检查白名单违规
    await this.checkViolation(newApp);
    
    this.log(`应用切换: ${previousApp?.name || 'None'} -> ${newApp.name}`);
  }
  
  /**
   * 检查白名单违规
   */
  private async checkViolation(app: AppInfo): Promise<void> {
    const isWhitelisted = this.isAppWhitelisted(app.name);
    const isSelfApp = app.name.toLowerCase().includes('electron') || 
                     app.windowTitle.includes('自律农场');
    
    if (isWhitelisted || isSelfApp) {
      // 应用在白名单中或是自己的应用，结束违规检测
      if (this.violationStartTime) {
        this.rewardControlService.endViolation();
        this.violationStartTime = null;
        this.log('回到白名单应用，结束违规检测');
      }
    } else {
      // 应用不在白名单中，开始或继续违规检测
      if (!this.violationStartTime) {
        this.violationStartTime = Date.now();
        this.rewardControlService.startViolation(app.name);
        this.log(`开始违规检测: ${app.name}`);
      }
      // RewardControlService会自动处理后续的计时和奖励控制
    }
  }
  
  /**
   * 检查应用是否在白名单中
   */
  private isAppWhitelisted(appName: string): boolean {
    return this.whitelistApps.some(whiteApp => 
      appName.toLowerCase().includes(whiteApp.toLowerCase()) ||
      whiteApp.toLowerCase().includes(appName.toLowerCase())
    );
  }
  
  /**
   * 设置白名单应用
   */
  async setWhitelistApps(apps: string[]): Promise<void> {
    this.whitelistApps = apps;
    await this.saveWhitelistToFile();
    this.log(`更新白名单: ${apps.join(', ')}`);
  }
  
  /**
   * 获取白名单应用
   */
  getWhitelistApps(): string[] {
    return [...this.whitelistApps];
  }
  
  /**
   * 获取已安装应用列表
   */
  async getInstalledApps(): Promise<string[]> {
    switch (process.platform) {
      case 'win32':
        return this.getWindowsInstalledApps();
      case 'darwin':
        return this.getMacOSInstalledApps();
      case 'linux':
        return this.getLinuxInstalledApps();
      default:
        return [];
    }
  }
  
  /**
   * 获取Windows已安装应用
   */
  private async getWindowsInstalledApps(): Promise<string[]> {
    return new Promise((resolve) => {
      const command = `
        Get-WmiObject -Class Win32_Product | 
        Select-Object Name | 
        Sort-Object Name | 
        ForEach-Object { $_.Name }
      `;
      
      exec(`powershell -Command "${command}"`, (error, stdout) => {
        if (error) {
          resolve([]);
          return;
        }
        
        const apps = stdout.trim().split('\n')
          .map(line => line.trim())
          .filter(line => line && line !== 'Name');
        
        resolve(apps);
      });
    });
  }
  
  /**
   * 获取macOS已安装应用
   */
  private async getMacOSInstalledApps(): Promise<string[]> {
    return new Promise((resolve) => {
      exec('ls /Applications | grep .app | sed "s/.app$//"', (error, stdout) => {
        if (error) {
          resolve([]);
          return;
        }
        
        const apps = stdout.trim().split('\n').filter(Boolean);
        resolve(apps);
      });
    });
  }
  
  /**
   * 获取Linux已安装应用
   */
  private async getLinuxInstalledApps(): Promise<string[]> {
    return new Promise((resolve) => {
      const command = `
        find /usr/share/applications ~/.local/share/applications -name "*.desktop" 2>/dev/null | 
        xargs grep -l "Type=Application" | 
        xargs grep "^Name=" | 
        cut -d= -f2 | 
        sort | 
        uniq
      `;
      
      exec(command, (error, stdout) => {
        if (error) {
          resolve([]);
          return;
        }
        
        const apps = stdout.trim().split('\n').filter(Boolean);
        resolve(apps);
      });
    });
  }
  
  /**
   * 添加到历史记录
   */
  private addToHistory(app: AppInfo): void {
    this.appHistory.push(app);
    
    // 限制历史记录数量
    if (this.appHistory.length > this.config.maxLogEntries) {
      this.appHistory = this.appHistory.slice(-this.config.maxLogEntries);
    }
  }
  
  /**
   * 获取应用使用历史
   */
  getAppHistory(hours = 24): AppInfo[] {
    const cutoffTime = Date.now() - (hours * 60 * 60 * 1000);
    return this.appHistory.filter(app => app.timestamp >= cutoffTime);
  }
  
  /**
   * 获取当前应用信息
   */
  getCurrentApp(): AppInfo | null {
    return this.currentApp;
  }
  
  /**
   * 获取监控状态
   */
  getMonitoringStatus(): {
    isMonitoring: boolean;
    currentApp: AppInfo | null;
    whitelistApps: string[];
    violationStartTime: number | null;
    violationThreshold: number;
  } {
    return {
      isMonitoring: this.isMonitoring,
      currentApp: this.currentApp,
      whitelistApps: this.whitelistApps,
      violationStartTime: this.violationStartTime,
      violationThreshold: this.violationThreshold
    };
  }
  
  /**
   * 事件监听
   */
  on(event: MonitorEvent, listener: EventListener): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event)!.push(listener);
  }
  
  /**
   * 移除事件监听
   */
  off(event: MonitorEvent, listener: EventListener): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      const index = listeners.indexOf(listener);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }
  
  /**
   * 触发事件
   */
  private emit(event: MonitorEvent, data: any): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(listener => {
        try {
          listener(data);
        } catch (error) {
          console.error(`事件监听器错误 (${event}):`, error);
        }
      });
    }
  }
  
  /**
   * 保存白名单到文件
   */
  private async saveWhitelistToFile(): Promise<void> {
    try {
      const filePath = join(app.getPath('userData'), 'whitelist.json');
      await fs.writeFile(filePath, JSON.stringify(this.whitelistApps, null, 2));
    } catch (error) {
      this.log(`保存白名单失败: ${error}`);
    }
  }
  
  /**
   * 从文件加载白名单
   */
  async loadWhitelistFromFile(): Promise<void> {
    try {
      const filePath = join(app.getPath('userData'), 'whitelist.json');
      const data = await fs.readFile(filePath, 'utf-8');
      this.whitelistApps = JSON.parse(data);
      this.log(`加载白名单: ${this.whitelistApps.join(', ')}`);
    } catch (error) {
      // 文件不存在或读取失败，使用默认空白名单
      this.whitelistApps = [];
      this.log('白名单文件不存在，使用空白名单');
    }
  }
  
  /**
   * 日志记录
   */
  private log(message: string): void {
    if (this.config.enableLogging) {
      console.log(`[SystemMonitor] ${new Date().toISOString()}: ${message}`);
    }
  }
  
  /**
   * 清理资源
   */
  async cleanup(): Promise<void> {
    await this.stopMonitoring();
    this.eventListeners.clear();
    this.appHistory = [];
    this.currentApp = null;
    
    // 清理奖励控制服务
    if (this.rewardControlService) {
      this.rewardControlService.destroy();
    }
    
    SystemMonitorService.instance = null;
  }

  /**
   * 设置奖励控制服务监听器
   */
  private setupRewardControlListeners(): void {
    // 监听违规警告
    this.rewardControlService.on('violation-warning', (data) => {
      this.log(`⚠️ 违规警告: ${data.application}, 持续时间: ${data.duration}ms`);
      this.emit('violation-detected', {
        type: 'warning',
        application: data.application,
        duration: data.duration
      });
    });

    // 监听奖励被阻止
    this.rewardControlService.on('rewards-blocked', (data) => {
      this.log(`🚫 奖励已阻止: ${data.application}, 阻止的奖励: ${data.blockedRewards.join(', ')}`);
      this.emit('violation-detected', {
        type: 'blocked',
        application: data.application,
        blockedRewards: data.blockedRewards,
        violationDuration: data.violationDuration
      });
    });

    // 监听奖励恢复
    this.rewardControlService.on('rewards-unblocked', (data) => {
      this.log(`✅ 奖励已恢复, 强制恢复: ${data.forced}`);
      this.emit('violation-detected', {
        type: 'unblocked',
        forced: data.forced
      });
    });

    // 监听恢复过程开始
    this.rewardControlService.on('recovery-started', (data) => {
      this.log(`🔄 恢复过程开始, 恢复时间: ${data.recoveryTime}ms`);
      this.emit('violation-detected', {
        type: 'recovery-started',
        recoveryTime: data.recoveryTime,
        violationDuration: data.violationDuration
      });
    });
  }

  /**
   * 获取奖励控制状态
   */
  getRewardControlState(): any {
    return this.rewardControlService.getState();
  }

  /**
   * 检查特定奖励是否被阻止
   */
  isRewardBlocked(rewardType: RewardType): boolean {
    return this.rewardControlService.isRewardBlocked(rewardType);
  }

  /**
   * 获取今日违规统计
   */
  getTodayViolationStats(): any {
    return this.rewardControlService.getTodayViolationStats();
  }

  /**
   * 强制解除奖励阻止
   */
  forceUnblockRewards(): void {
    this.rewardControlService.forceUnblockRewards();
  }

  /**
   * 更新违规配置
   */
  updateViolationConfig(config: any): void {
    this.rewardControlService.updateConfig(config);
  }
} 