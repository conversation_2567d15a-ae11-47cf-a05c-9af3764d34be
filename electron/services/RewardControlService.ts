import { EventEmitter } from 'events';

// 奖励控制状态接口
interface RewardControlState {
  isRewardBlocked: boolean;
  violationStartTime: number | null;
  violationDuration: number; // 毫秒
  totalViolationTime: number; // 累计违规时间（毫秒）
  blockedRewards: string[]; // 被阻止的奖励类型
  violationHistory: ViolationRecord[];
}

// 违规记录接口
interface ViolationRecord {
  id: string;
  startTime: number;
  endTime: number | null;
  duration: number;
  application: string;
  severity: 'minor' | 'moderate' | 'severe';
  recovered: boolean;
}

// 奖励类型枚举
export enum RewardType {
  GROWTH_POINTS = 'growth_points',
  EXPERIENCE = 'experience',
  COINS = 'coins',
  ACHIEVEMENTS = 'achievements',
  LEVEL_UP = 'level_up',
  CROP_HARVEST = 'crop_harvest',
  BONUS_REWARDS = 'bonus_rewards'
}

// 违规配置接口
interface ViolationConfig {
  warningThreshold: number; // 警告阈值（毫秒）
  blockThreshold: number; // 阻止奖励阈值（毫秒）
  severeThreshold: number; // 严重违规阈值（毫秒）
  maxDailyViolations: number; // 每日最大违规次数
  recoveryPenaltyMultiplier: number; // 恢复惩罚倍数
}

export class RewardControlService extends EventEmitter {
  private static instance: RewardControlService | null = null;
  private state: RewardControlState;
  private config: ViolationConfig;
  private violationTimer: NodeJS.Timeout | null = null;
  private recoveryTimer: NodeJS.Timeout | null = null;
  private persistencePath: string;

  private constructor() {
    super();
    
    this.persistencePath = 'reward-control-state.json';
    
    // 默认配置
    this.config = {
      warningThreshold: 3 * 60 * 1000, // 3分钟警告
      blockThreshold: 5 * 60 * 1000, // 5分钟阻止奖励
      severeThreshold: 15 * 60 * 1000, // 15分钟严重违规
      maxDailyViolations: 5, // 每日最多5次违规
      recoveryPenaltyMultiplier: 2.0 // 恢复需要双倍时间
    };

    // 初始状态
    this.state = {
      isRewardBlocked: false,
      violationStartTime: null,
      violationDuration: 0,
      totalViolationTime: 0,
      blockedRewards: [],
      violationHistory: []
    };

    this.loadState();
  }

  public static getInstance(): RewardControlService {
    if (!RewardControlService.instance) {
      RewardControlService.instance = new RewardControlService();
    }
    return RewardControlService.instance;
  }

  /**
   * 开始违规检测
   * @param application 违规应用名称
   */
  public startViolation(application: string): void {
    if (this.state.violationStartTime) {
      return; // 已经在违规状态中
    }

    const now = Date.now();
    this.state.violationStartTime = now;
    this.state.violationDuration = 0;

    console.log(`🚨 开始违规检测: ${application}`);
    
    // 创建新的违规记录
    const violationRecord: ViolationRecord = {
      id: this.generateViolationId(),
      startTime: now,
      endTime: null,
      duration: 0,
      application,
      severity: 'minor',
      recovered: false
    };

    this.state.violationHistory.push(violationRecord);
    
    // 启动违规计时器
    this.startViolationTimer(application);
    
    this.emit('violation-started', {
      application,
      startTime: now,
      recordId: violationRecord.id
    });

    this.saveState();
  }

  /**
   * 结束违规检测
   */
  public endViolation(): void {
    if (!this.state.violationStartTime) {
      return; // 没有正在进行的违规
    }

    const now = Date.now();
    const violationDuration = now - this.state.violationStartTime;
    
    // 更新最后一个违规记录
    const lastViolation = this.state.violationHistory[this.state.violationHistory.length - 1];
    if (lastViolation && !lastViolation.endTime) {
      lastViolation.endTime = now;
      lastViolation.duration = violationDuration;
    }

    console.log(`✅ 结束违规检测，持续时间: ${Math.round(violationDuration / 1000)}秒`);

    // 清除违规计时器
    if (this.violationTimer) {
      clearTimeout(this.violationTimer);
      this.violationTimer = null;
    }

    // 如果奖励被阻止，启动恢复过程
    if (this.state.isRewardBlocked) {
      this.startRecoveryProcess(violationDuration);
    }

    // 重置违规状态
    this.state.violationStartTime = null;
    this.state.violationDuration = 0;

    this.emit('violation-ended', {
      duration: violationDuration,
      totalViolationTime: this.state.totalViolationTime,
      isRewardBlocked: this.state.isRewardBlocked
    });

    this.saveState();
  }

  /**
   * 检查特定奖励类型是否被阻止
   * @param rewardType 奖励类型
   * @returns 是否被阻止
   */
  public isRewardBlocked(rewardType: RewardType): boolean {
    if (!this.state.isRewardBlocked) {
      return false;
    }
    return this.state.blockedRewards.includes(rewardType);
  }

  /**
   * 获取当前状态
   */
  public getState(): RewardControlState {
    return { ...this.state };
  }

  /**
   * 获取违规配置
   */
  public getConfig(): ViolationConfig {
    return { ...this.config };
  }

  /**
   * 更新违规配置
   * @param newConfig 新的配置
   */
  public updateConfig(newConfig: Partial<ViolationConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.saveState();
    
    this.emit('config-updated', this.config);
  }

  /**
   * 获取今日违规统计
   */
  public getTodayViolationStats(): {
    count: number;
    totalDuration: number;
    averageDuration: number;
    severity: { minor: number; moderate: number; severe: number };
  } {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const todayStart = today.getTime();

    const todayViolations = this.state.violationHistory.filter(
      v => v.startTime >= todayStart
    );

    const totalDuration = todayViolations.reduce((sum, v) => sum + v.duration, 0);
    const severity = {
      minor: todayViolations.filter(v => v.severity === 'minor').length,
      moderate: todayViolations.filter(v => v.severity === 'moderate').length,
      severe: todayViolations.filter(v => v.severity === 'severe').length
    };

    return {
      count: todayViolations.length,
      totalDuration,
      averageDuration: todayViolations.length > 0 ? totalDuration / todayViolations.length : 0,
      severity
    };
  }

  /**
   * 强制解除奖励阻止（管理员功能）
   */
  public forceUnblockRewards(): void {
    if (this.recoveryTimer) {
      clearTimeout(this.recoveryTimer);
      this.recoveryTimer = null;
    }

    this.state.isRewardBlocked = false;
    this.state.blockedRewards = [];

    console.log('🔓 强制解除奖励阻止');
    
    this.emit('rewards-unblocked', { forced: true });
    this.saveState();
  }

  /**
   * 启动违规计时器
   * @param application 应用名称
   */
  private startViolationTimer(application: string): void {
    // 警告阶段检查
    const warningTimer = setTimeout(() => {
      if (this.state.violationStartTime) {
        console.log(`⚠️ 违规警告: 使用 ${application} 已超过 ${this.config.warningThreshold / 1000} 秒`);
        this.emit('violation-warning', {
          application,
          duration: this.config.warningThreshold
        });
      }
    }, this.config.warningThreshold);

    // 阻止奖励阶段
    this.violationTimer = setTimeout(() => {
      if (this.state.violationStartTime) {
        this.blockRewards(application);
      }
    }, this.config.blockThreshold);
  }

  /**
   * 阻止奖励
   * @param application 违规应用名称
   */
  private blockRewards(application: string): void {
    this.state.isRewardBlocked = true;
    this.state.blockedRewards = Object.values(RewardType);
    this.state.totalViolationTime += this.config.blockThreshold;

    // 更新违规记录的严重程度
    const lastViolation = this.state.violationHistory[this.state.violationHistory.length - 1];
    if (lastViolation) {
      const currentDuration = Date.now() - lastViolation.startTime;
      if (currentDuration >= this.config.severeThreshold) {
        lastViolation.severity = 'severe';
      } else if (currentDuration >= this.config.blockThreshold) {
        lastViolation.severity = 'moderate';
      }
    }

    console.log(`🚫 奖励已被阻止，违规应用: ${application}`);
    
    this.emit('rewards-blocked', {
      application,
      blockedRewards: this.state.blockedRewards,
      violationDuration: this.config.blockThreshold
    });

    this.saveState();
  }

  /**
   * 启动恢复过程
   * @param violationDuration 违规持续时间
   */
  private startRecoveryProcess(violationDuration: number): void {
    // 计算恢复时间（违规时间的倍数）
    const recoveryTime = Math.min(
      violationDuration * this.config.recoveryPenaltyMultiplier,
      30 * 60 * 1000 // 最长30分钟恢复时间
    );

    console.log(`🔄 开始恢复过程，需要 ${Math.round(recoveryTime / 1000)} 秒`);

    this.recoveryTimer = setTimeout(() => {
      this.unblockRewards();
    }, recoveryTime);

    this.emit('recovery-started', {
      recoveryTime,
      violationDuration
    });
  }

  /**
   * 解除奖励阻止
   */
  private unblockRewards(): void {
    this.state.isRewardBlocked = false;
    this.state.blockedRewards = [];

    // 标记最后一个违规记录为已恢复
    const lastViolation = this.state.violationHistory[this.state.violationHistory.length - 1];
    if (lastViolation) {
      lastViolation.recovered = true;
    }

    console.log('🎉 奖励恢复正常');
    
    this.emit('rewards-unblocked', { forced: false });
    this.saveState();
  }

  /**
   * 生成违规记录ID
   */
  private generateViolationId(): string {
    return `violation_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 保存状态到文件
   */
  private async saveState(): Promise<void> {
    try {
      const fs = await import('fs').then(m => m.promises);
      const data = JSON.stringify({
        state: this.state,
        config: this.config,
        lastSaved: Date.now()
      }, null, 2);
      
      await fs.writeFile(this.persistencePath, data, 'utf8');
    } catch (error) {
      console.error('保存奖励控制状态失败:', error);
    }
  }

  /**
   * 从文件加载状态
   */
  private async loadState(): Promise<void> {
    try {
      const fs = await import('fs').then(m => m.promises);
      const data = await fs.readFile(this.persistencePath, 'utf8');
      const saved = JSON.parse(data);
      
      if (saved.state) {
        this.state = { ...this.state, ...saved.state };
      }
      if (saved.config) {
        this.config = { ...this.config, ...saved.config };
      }
      
      console.log('✅ 奖励控制状态加载完成');
    } catch (error) {
      console.log('📝 创建新的奖励控制状态文件');
      // 文件不存在或损坏，使用默认状态
      await this.saveState();
    }
  }

  /**
   * 清理历史记录（保留最近30天）
   */
  public cleanupHistory(): void {
    const thirtyDaysAgo = Date.now() - (30 * 24 * 60 * 60 * 1000);
    const originalCount = this.state.violationHistory.length;
    
    this.state.violationHistory = this.state.violationHistory.filter(
      record => record.startTime > thirtyDaysAgo
    );
    
    const cleanedCount = originalCount - this.state.violationHistory.length;
    if (cleanedCount > 0) {
      console.log(`🧹 清理了 ${cleanedCount} 条历史违规记录`);
      this.saveState();
    }
  }

  /**
   * 销毁服务实例
   */
  public destroy(): void {
    if (this.violationTimer) {
      clearTimeout(this.violationTimer);
      this.violationTimer = null;
    }
    
    if (this.recoveryTimer) {
      clearTimeout(this.recoveryTimer);
      this.recoveryTimer = null;
    }
    
    this.removeAllListeners();
    RewardControlService.instance = null;
  }
} 