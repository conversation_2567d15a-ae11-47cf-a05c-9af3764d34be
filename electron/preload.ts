import { contextBridge, ipc<PERSON><PERSON>er } from 'electron';

// 定义API接口类型
export interface ElectronAPI {
  // 平台信息
  getPlatform: () => Promise<{
    platform: string;
    arch: string;
    version: string;
  }>;
  
  // 应用监控相关
  startMonitoring: () => Promise<{ success: boolean; message: string }>;
  stopMonitoring: () => Promise<{ success: boolean; message: string }>;
  getMonitoringStatus: () => Promise<{
    isMonitoring: boolean;
    currentActiveApp: string;
    nonWhitelistTime: number;
    whitelistApps: string[];
  }>;
  
  // 白名单管理
  setWhitelist: (apps: string[]) => Promise<{ success: boolean; message: string }>;
  getWhitelist: () => Promise<{ success: boolean; data: string[] }>;
  getInstalledApps: () => Promise<string[]>;
  
  // 奖励控制相关
  getRewardControlState: () => Promise<any>;
  isRewardBlocked: (rewardType: string) => Promise<boolean>;
  getViolationStats: () => Promise<any>;
  forceUnblockRewards: () => Promise<{ success: boolean; message: string }>;
  updateViolationConfig: (config: any) => Promise<{ success: boolean; message: string }>;
  
  // 事件监听
  onMonitoringUpdate: (callback: (data: {
    activeApp: string;
    isWhitelisted: boolean;
    nonWhitelistTime: number;
    isViolation: boolean;
  }) => void) => void;
  
  onViolationDetected: (callback: (data: {
    activeApp: string;
    violationTime: number;
  }) => void) => void;
  
  // 移除事件监听
  removeAllListeners: (channel: string) => void;
  
  // 应用历史记录
  getAppHistory: (hours?: number) => Promise<any[]>;
}

// 暴露安全的API到渲染进程
const electronAPI: ElectronAPI = {
  // 获取平台信息
  getPlatform: () => ipcRenderer.invoke('get-platform'),
  
  // 监控控制
  startMonitoring: () => ipcRenderer.invoke('start-monitoring'),
  stopMonitoring: () => ipcRenderer.invoke('stop-monitoring'),
  getMonitoringStatus: () => ipcRenderer.invoke('get-monitoring-status'),
  
  // 白名单管理
  setWhitelist: (apps: string[]) => ipcRenderer.invoke('set-whitelist', apps),
  getWhitelist: () => ipcRenderer.invoke('get-whitelist'),
  getInstalledApps: () => ipcRenderer.invoke('get-installed-apps'),
  
  // 事件监听
  onMonitoringUpdate: (callback) => {
    ipcRenderer.on('monitoring-update', (event, data) => callback(data));
  },
  
  onViolationDetected: (callback) => {
    ipcRenderer.on('violation-detected', (event, data) => callback(data));
  },
  
  // 移除事件监听
  removeAllListeners: (channel: string) => {
    ipcRenderer.removeAllListeners(channel);
  },
  
  // 应用历史记录
  getAppHistory: (hours?: number) => ipcRenderer.invoke('get-app-history', hours),
  
  // 奖励控制相关
  getRewardControlState: () => ipcRenderer.invoke('get-reward-control-state'),
  isRewardBlocked: (rewardType: string) => ipcRenderer.invoke('is-reward-blocked', rewardType),
  getViolationStats: () => ipcRenderer.invoke('get-violation-stats'),
  forceUnblockRewards: () => ipcRenderer.invoke('force-unblock-rewards'),
  updateViolationConfig: (config: any) => ipcRenderer.invoke('update-violation-config', config)
};

// 使用contextBridge安全地暴露API
contextBridge.exposeInMainWorld('electronAPI', electronAPI);

// 类型声明，供TypeScript使用
declare global {
  interface Window {
    electronAPI: ElectronAPI;
  }
} 