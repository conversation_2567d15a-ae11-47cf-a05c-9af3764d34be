{"env": {"browser": true, "es2020": true, "node": true}, "extends": ["eslint:recommended", "@typescript-eslint/recommended", "plugin:react-hooks/recommended"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": "latest", "sourceType": "module", "ecmaFeatures": {"jsx": true}}, "plugins": ["react-refresh", "@typescript-eslint"], "rules": {"react-refresh/only-export-components": ["warn", {"allowConstantExport": true}], "@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "^_"}], "@typescript-eslint/no-explicit-any": "warn", "no-console": "off"}, "ignorePatterns": ["dist", "node_modules", "*.js"]}