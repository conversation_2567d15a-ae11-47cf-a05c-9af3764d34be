{"timestamp": "2025-06-23T07:50:01.635Z", "issues": [], "suggestions": [{"category": "代码", "message": "大文件需要拆分: src\\App.tsx", "priority": "high", "actions": ["文件有 958 行，建议拆分", "将组件拆分成更小的子组件", "使用懒加载", "提取业务逻辑到hooks"]}, {"category": "代码", "message": "大文件需要拆分: src\\audio\\AudioManager.ts", "priority": "high", "actions": ["文件有 619 行，建议拆分", "将组件拆分成更小的子组件", "使用懒加载", "提取业务逻辑到hooks"]}, {"category": "代码", "message": "大文件需要拆分: src\\components\\achievements\\DataManagement.tsx", "priority": "high", "actions": ["文件有 540 行，建议拆分", "将组件拆分成更小的子组件", "使用懒加载", "提取业务逻辑到hooks"]}, {"category": "代码", "message": "大文件需要拆分: src\\components\\ChineseFuturesInventory.tsx", "priority": "high", "actions": ["文件有 792 行，建议拆分", "将组件拆分成更小的子组件", "使用懒加载", "提取业务逻辑到hooks"]}, {"category": "代码", "message": "大文件需要拆分: src\\components\\FarmInterface.tsx", "priority": "high", "actions": ["文件有 560 行，建议拆分", "将组件拆分成更小的子组件", "使用懒加载", "提取业务逻辑到hooks"]}, {"category": "代码", "message": "大文件需要拆分: src\\components\\FarmUpgradeInterface.tsx", "priority": "high", "actions": ["文件有 773 行，建议拆分", "将组件拆分成更小的子组件", "使用懒加载", "提取业务逻辑到hooks"]}, {"category": "代码", "message": "大文件需要拆分: src\\components\\FeedbackAnalysis.tsx", "priority": "high", "actions": ["文件有 814 行，建议拆分", "将组件拆分成更小的子组件", "使用懒加载", "提取业务逻辑到hooks"]}, {"category": "代码", "message": "大文件需要拆分: src\\components\\focus\\FocusMode.tsx", "priority": "high", "actions": ["文件有 707 行，建议拆分", "将组件拆分成更小的子组件", "使用懒加载", "提取业务逻辑到hooks"]}, {"category": "代码", "message": "大文件需要拆分: src\\components\\InventoryWithSynthesis.tsx", "priority": "high", "actions": ["文件有 636 行，建议拆分", "将组件拆分成更小的子组件", "使用懒加载", "提取业务逻辑到hooks"]}, {"category": "代码", "message": "大文件需要拆分: src\\components\\LootboxTester.tsx", "priority": "high", "actions": ["文件有 1181 行，建议拆分", "将组件拆分成更小的子组件", "使用懒加载", "提取业务逻辑到hooks"]}, {"category": "代码", "message": "大文件需要拆分: src\\components\\MainGameInterface.tsx", "priority": "high", "actions": ["文件有 551 行，建议拆分", "将组件拆分成更小的子组件", "使用懒加载", "提取业务逻辑到hooks"]}, {"category": "代码", "message": "大文件需要拆分: src\\components\\recommendations\\RecommendationInterface.tsx", "priority": "high", "actions": ["文件有 806 行，建议拆分", "将组件拆分成更小的子组件", "使用懒加载", "提取业务逻辑到hooks"]}, {"category": "代码", "message": "大文件需要拆分: src\\components\\SimpleDragSynthesis.tsx", "priority": "high", "actions": ["文件有 637 行，建议拆分", "将组件拆分成更小的子组件", "使用懒加载", "提取业务逻辑到hooks"]}, {"category": "代码", "message": "大文件需要拆分: src\\components\\testing\\FeedbackAnalysis.tsx", "priority": "high", "actions": ["文件有 720 行，建议拆分", "将组件拆分成更小的子组件", "使用懒加载", "提取业务逻辑到hooks"]}, {"category": "代码", "message": "大文件需要拆分: src\\components\\testing\\TestPlan.tsx", "priority": "high", "actions": ["文件有 970 行，建议拆分", "将组件拆分成更小的子组件", "使用懒加载", "提取业务逻辑到hooks"]}, {"category": "代码", "message": "大文件需要拆分: src\\components\\testing\\UserTesting.tsx", "priority": "high", "actions": ["文件有 835 行，建议拆分", "将组件拆分成更小的子组件", "使用懒加载", "提取业务逻辑到hooks"]}, {"category": "代码", "message": "大文件需要拆分: src\\components\\ui\\Input.tsx", "priority": "high", "actions": ["文件有 509 行，建议拆分", "将组件拆分成更小的子组件", "使用懒加载", "提取业务逻辑到hooks"]}, {"category": "代码", "message": "大文件需要拆分: src\\components\\UnifiedInventoryPanel.tsx", "priority": "high", "actions": ["文件有 856 行，建议拆分", "将组件拆分成更小的子组件", "使用懒加载", "提取业务逻辑到hooks"]}, {"category": "代码", "message": "大文件需要拆分: src\\data\\agriculturalItems.ts", "priority": "high", "actions": ["文件有 583 行，建议拆分", "将组件拆分成更小的子组件", "使用懒加载", "提取业务逻辑到hooks"]}, {"category": "代码", "message": "大文件需要拆分: src\\data\\synthesisFailure.ts", "priority": "high", "actions": ["文件有 504 行，建议拆分", "将组件拆分成更小的子组件", "使用懒加载", "提取业务逻辑到hooks"]}, {"category": "代码", "message": "大文件需要拆分: src\\game\\managers\\HarvestManager.ts", "priority": "high", "actions": ["文件有 602 行，建议拆分", "将组件拆分成更小的子组件", "使用懒加载", "提取业务逻辑到hooks"]}, {"category": "代码", "message": "大文件需要拆分: src\\game\\objects\\CropSprite.ts", "priority": "high", "actions": ["文件有 597 行，建议拆分", "将组件拆分成更小的子组件", "使用懒加载", "提取业务逻辑到hooks"]}, {"category": "代码", "message": "大文件需要拆分: src\\game\\scenes\\AnimationTestScene.ts", "priority": "high", "actions": ["文件有 621 行，建议拆分", "将组件拆分成更小的子组件", "使用懒加载", "提取业务逻辑到hooks"]}, {"category": "代码", "message": "大文件需要拆分: src\\game\\scenes\\EnhancedFarmScene.ts", "priority": "high", "actions": ["文件有 592 行，建议拆分", "将组件拆分成更小的子组件", "使用懒加载", "提取业务逻辑到hooks"]}, {"category": "代码", "message": "大文件需要拆分: src\\game\\scenes\\StateTestScene.ts", "priority": "high", "actions": ["文件有 518 行，建议拆分", "将组件拆分成更小的子组件", "使用懒加载", "提取业务逻辑到hooks"]}, {"category": "代码", "message": "大文件需要拆分: src\\game\\scenes\\UnifiedAgriculturalScene.ts", "priority": "high", "actions": ["文件有 528 行，建议拆分", "将组件拆分成更小的子组件", "使用懒加载", "提取业务逻辑到hooks"]}, {"category": "代码", "message": "大文件需要拆分: src\\managers\\EnhancedFarmManager.ts", "priority": "high", "actions": ["文件有 736 行，建议拆分", "将组件拆分成更小的子组件", "使用懒加载", "提取业务逻辑到hooks"]}, {"category": "代码", "message": "大文件需要拆分: src\\managers\\EventManager.ts", "priority": "high", "actions": ["文件有 667 行，建议拆分", "将组件拆分成更小的子组件", "使用懒加载", "提取业务逻辑到hooks"]}, {"category": "代码", "message": "大文件需要拆分: src\\managers\\FarmManager.ts", "priority": "high", "actions": ["文件有 604 行，建议拆分", "将组件拆分成更小的子组件", "使用懒加载", "提取业务逻辑到hooks"]}, {"category": "代码", "message": "大文件需要拆分: src\\managers\\GameStateManager.ts", "priority": "high", "actions": ["文件有 510 行，建议拆分", "将组件拆分成更小的子组件", "使用懒加载", "提取业务逻辑到hooks"]}, {"category": "代码", "message": "大文件需要拆分: src\\managers\\GameStateManager_fixed.ts", "priority": "high", "actions": ["文件有 809 行，建议拆分", "将组件拆分成更小的子组件", "使用懒加载", "提取业务逻辑到hooks"]}, {"category": "代码", "message": "大文件需要拆分: src\\managers\\GameStateManager_old.ts", "priority": "high", "actions": ["文件有 838 行，建议拆分", "将组件拆分成更小的子组件", "使用懒加载", "提取业务逻辑到hooks"]}, {"category": "代码", "message": "大文件需要拆分: src\\managers\\ItemIntegrationManager.ts", "priority": "high", "actions": ["文件有 635 行，建议拆分", "将组件拆分成更小的子组件", "使用懒加载", "提取业务逻辑到hooks"]}, {"category": "代码", "message": "大文件需要拆分: src\\managers\\LootBoxManager.ts", "priority": "high", "actions": ["文件有 550 行，建议拆分", "将组件拆分成更小的子组件", "使用懒加载", "提取业务逻辑到hooks"]}, {"category": "代码", "message": "大文件需要拆分: src\\managers\\StorageManager.ts", "priority": "high", "actions": ["文件有 736 行，建议拆分", "将组件拆分成更小的子组件", "使用懒加载", "提取业务逻辑到hooks"]}, {"category": "代码", "message": "大文件需要拆分: src\\managers\\SynthesisManager.ts", "priority": "high", "actions": ["文件有 562 行，建议拆分", "将组件拆分成更小的子组件", "使用懒加载", "提取业务逻辑到hooks"]}, {"category": "代码", "message": "大文件需要拆分: src\\managers\\UnifiedItemManager.ts", "priority": "high", "actions": ["文件有 598 行，建议拆分", "将组件拆分成更小的子组件", "使用懒加载", "提取业务逻辑到hooks"]}, {"category": "代码", "message": "大文件需要拆分: src\\pages\\AgriculturalDemo.tsx", "priority": "high", "actions": ["文件有 816 行，建议拆分", "将组件拆分成更小的子组件", "使用懒加载", "提取业务逻辑到hooks"]}, {"category": "代码", "message": "大文件需要拆分: src\\services\\AchievementDataService.ts", "priority": "high", "actions": ["文件有 621 行，建议拆分", "将组件拆分成更小的子组件", "使用懒加载", "提取业务逻辑到hooks"]}, {"category": "代码", "message": "大文件需要拆分: src\\services\\BehaviorAnalyticsService.ts", "priority": "high", "actions": ["文件有 534 行，建议拆分", "将组件拆分成更小的子组件", "使用懒加载", "提取业务逻辑到hooks"]}, {"category": "代码", "message": "大文件需要拆分: src\\services\\DataAnalysisEngine.ts", "priority": "high", "actions": ["文件有 1124 行，建议拆分", "将组件拆分成更小的子组件", "使用懒加载", "提取业务逻辑到hooks"]}, {"category": "代码", "message": "大文件需要拆分: src\\services\\DataSecurityService.ts", "priority": "high", "actions": ["文件有 610 行，建议拆分", "将组件拆分成更小的子组件", "使用懒加载", "提取业务逻辑到hooks"]}, {"category": "代码", "message": "大文件需要拆分: src\\services\\FarmUpgradeDataService.ts", "priority": "high", "actions": ["文件有 777 行，建议拆分", "将组件拆分成更小的子组件", "使用懒加载", "提取业务逻辑到hooks"]}, {"category": "代码", "message": "大文件需要拆分: src\\services\\PersonalizedRecommendationEngine.ts", "priority": "high", "actions": ["文件有 549 行，建议拆分", "将组件拆分成更小的子组件", "使用懒加载", "提取业务逻辑到hooks"]}, {"category": "代码", "message": "大文件需要拆分: src\\services\\PrivacyManager.ts", "priority": "high", "actions": ["文件有 616 行，建议拆分", "将组件拆分成更小的子组件", "使用懒加载", "提取业务逻辑到hooks"]}, {"category": "代码", "message": "大文件需要拆分: src\\services\\ReportGenerator.ts", "priority": "high", "actions": ["文件有 1103 行，建议拆分", "将组件拆分成更小的子组件", "使用懒加载", "提取业务逻辑到hooks"]}, {"category": "代码", "message": "大文件需要拆分: src\\services\\ReportService.ts", "priority": "high", "actions": ["文件有 517 行，建议拆分", "将组件拆分成更小的子组件", "使用懒加载", "提取业务逻辑到hooks"]}, {"category": "代码", "message": "大文件需要拆分: src\\services\\SyncService.ts", "priority": "high", "actions": ["文件有 579 行，建议拆分", "将组件拆分成更小的子组件", "使用懒加载", "提取业务逻辑到hooks"]}, {"category": "代码", "message": "大文件需要拆分: src\\services\\TrendAnalysisEngine.ts", "priority": "high", "actions": ["文件有 784 行，建议拆分", "将组件拆分成更小的子组件", "使用懒加载", "提取业务逻辑到hooks"]}, {"category": "代码", "message": "大文件需要拆分: src\\services\\UserProfileService.ts", "priority": "high", "actions": ["文件有 571 行，建议拆分", "将组件拆分成更小的子组件", "使用懒加载", "提取业务逻辑到hooks"]}, {"category": "代码", "message": "大文件需要拆分: src\\services\\WeatherAchievementIntegration.ts", "priority": "high", "actions": ["文件有 588 行，建议拆分", "将组件拆分成更小的子组件", "使用懒加载", "提取业务逻辑到hooks"]}, {"category": "代码", "message": "大文件需要拆分: src\\services\\WeatherEffectCoordinator.ts", "priority": "high", "actions": ["文件有 621 行，建议拆分", "将组件拆分成更小的子组件", "使用懒加载", "提取业务逻辑到hooks"]}, {"category": "代码", "message": "大文件需要拆分: src\\services\\WeatherEffectProcessor.ts", "priority": "high", "actions": ["文件有 663 行，建议拆分", "将组件拆分成更小的子组件", "使用懒加载", "提取业务逻辑到hooks"]}, {"category": "代码", "message": "大文件需要拆分: src\\services\\WeatherForecastService.ts", "priority": "high", "actions": ["文件有 764 行，建议拆分", "将组件拆分成更小的子组件", "使用懒加载", "提取业务逻辑到hooks"]}, {"category": "代码", "message": "大文件需要拆分: src\\services\\WeatherIntegrationService.ts", "priority": "high", "actions": ["文件有 714 行，建议拆分", "将组件拆分成更小的子组件", "使用懒加载", "提取业务逻辑到hooks"]}, {"category": "代码", "message": "大文件需要拆分: src\\services\\WeatherManager.ts", "priority": "high", "actions": ["文件有 1015 行，建议拆分", "将组件拆分成更小的子组件", "使用懒加载", "提取业务逻辑到hooks"]}, {"category": "代码", "message": "大文件需要拆分: src\\services\\WeatherVisualManager.ts", "priority": "high", "actions": ["文件有 881 行，建议拆分", "将组件拆分成更小的子组件", "使用懒加载", "提取业务逻辑到hooks"]}, {"category": "代码", "message": "大文件需要拆分: src\\systems\\CropRewardSystem.ts", "priority": "high", "actions": ["文件有 799 行，建议拆分", "将组件拆分成更小的子组件", "使用懒加载", "提取业务逻辑到hooks"]}, {"category": "代码", "message": "大文件需要拆分: src\\systems\\FarmUnlockSystem.ts", "priority": "high", "actions": ["文件有 742 行，建议拆分", "将组件拆分成更小的子组件", "使用懒加载", "提取业务逻辑到hooks"]}, {"category": "代码", "message": "大文件需要拆分: src\\systems\\FarmUpgradeAnimations.ts", "priority": "high", "actions": ["文件有 815 行，建议拆分", "将组件拆分成更小的子组件", "使用懒加载", "提取业务逻辑到hooks"]}, {"category": "代码", "message": "大文件需要拆分: src\\systems\\FarmUpgradeSystem.ts", "priority": "high", "actions": ["文件有 825 行，建议拆分", "将组件拆分成更小的子组件", "使用懒加载", "提取业务逻辑到hooks"]}, {"category": "代码", "message": "大文件需要拆分: src\\systems\\IncentiveSystem.ts", "priority": "high", "actions": ["文件有 581 行，建议拆分", "将组件拆分成更小的子组件", "使用懒加载", "提取业务逻辑到hooks"]}, {"category": "代码", "message": "大文件需要拆分: src\\types\\crop.ts", "priority": "high", "actions": ["文件有 620 行，建议拆分", "将组件拆分成更小的子组件", "使用懒加载", "提取业务逻辑到hooks"]}, {"category": "代码", "message": "大文件需要拆分: src\\types\\futures.ts", "priority": "high", "actions": ["文件有 519 行，建议拆分", "将组件拆分成更小的子组件", "使用懒加载", "提取业务逻辑到hooks"]}, {"category": "代码", "message": "大文件需要拆分: src\\utils\\adaptiveLearning.ts", "priority": "high", "actions": ["文件有 693 行，建议拆分", "将组件拆分成更小的子组件", "使用懒加载", "提取业务逻辑到hooks"]}, {"category": "代码", "message": "大文件需要拆分: src\\utils\\CameraAdapterService.ts", "priority": "high", "actions": ["文件有 985 行，建议拆分", "将组件拆分成更小的子组件", "使用懒加载", "提取业务逻辑到hooks"]}, {"category": "代码", "message": "大文件需要拆分: src\\utils\\enhancedPoseAnalysis.ts", "priority": "high", "actions": ["文件有 1021 行，建议拆分", "将组件拆分成更小的子组件", "使用懒加载", "提取业务逻辑到hooks"]}, {"category": "代码", "message": "大文件需要拆分: src\\utils\\environmentalAdaptation.ts", "priority": "high", "actions": ["文件有 556 行，建议拆分", "将组件拆分成更小的子组件", "使用懒加载", "提取业务逻辑到hooks"]}, {"category": "代码", "message": "大文件需要拆分: src\\utils\\multiFrameAnalysis.ts", "priority": "high", "actions": ["文件有 720 行，建议拆分", "将组件拆分成更小的子组件", "使用懒加载", "提取业务逻辑到hooks"]}, {"category": "代码", "message": "大文件需要拆分: src\\utils\\performance\\DataProcessor.ts", "priority": "high", "actions": ["文件有 715 行，建议拆分", "将组件拆分成更小的子组件", "使用懒加载", "提取业务逻辑到hooks"]}, {"category": "代码", "message": "大文件需要拆分: src\\utils\\performance\\EventSystemOptimizer.ts", "priority": "high", "actions": ["文件有 542 行，建议拆分", "将组件拆分成更小的子组件", "使用懒加载", "提取业务逻辑到hooks"]}, {"category": "代码", "message": "大文件需要拆分: src\\utils\\performance\\MemoryManager.ts", "priority": "high", "actions": ["文件有 598 行，建议拆分", "将组件拆分成更小的子组件", "使用懒加载", "提取业务逻辑到hooks"]}, {"category": "代码", "message": "大文件需要拆分: src\\utils\\performance\\TimerManager.ts", "priority": "high", "actions": ["文件有 682 行，建议拆分", "将组件拆分成更小的子组件", "使用懒加载", "提取业务逻辑到hooks"]}, {"category": "代码", "message": "大文件需要拆分: src\\utils\\PlatformDetector.ts", "priority": "high", "actions": ["文件有 673 行，建议拆分", "将组件拆分成更小的子组件", "使用懒加载", "提取业务逻辑到hooks"]}, {"category": "代码", "message": "大文件需要拆分: src\\utils\\PlatformOptimizer.ts", "priority": "high", "actions": ["文件有 751 行，建议拆分", "将组件拆分成更小的子组件", "使用懒加载", "提取业务逻辑到hooks"]}, {"category": "代码", "message": "大文件需要拆分: src\\utils\\ResponsiveAdapter.ts", "priority": "high", "actions": ["文件有 635 行，建议拆分", "将组件拆分成更小的子组件", "使用懒加载", "提取业务逻辑到hooks"]}, {"category": "代码", "message": "大文件需要拆分: src\\utils\\TestFramework.ts", "priority": "high", "actions": ["文件有 528 行，建议拆分", "将组件拆分成更小的子组件", "使用懒加载", "提取业务逻辑到hooks"]}, {"category": "组件", "message": "考虑使用懒加载", "priority": "medium", "actions": ["检测到 59 个组件", "大型组件可以使用React.lazy懒加载", "减少初始包大小", "提升首屏加载速度"]}, {"category": "TypeScript", "message": "启用增量编译", "priority": "low", "actions": ["在tsconfig.json中设置 \"incremental\": true", "提升重复构建速度"]}, {"category": "TypeScript", "message": "考虑启用严格模式", "priority": "low", "actions": ["启用strict模式可以提高代码质量", "有助于在编译时发现更多问题"]}], "summary": {"totalIssues": 0, "totalSuggestions": 80, "highPriorityItems": 77}}