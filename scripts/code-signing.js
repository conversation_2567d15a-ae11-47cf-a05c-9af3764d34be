#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const crypto = require('crypto');

// 代码签名管理器
class CodeSigningManager {
  constructor() {
    this.platform = process.platform;
    this.isProduction = process.env.NODE_ENV === 'production';
    this.isDevelopment = !this.isProduction;
  }

  // 日志函数
  log(message, type = 'info') {
    const timestamp = new Date().toISOString();
    const prefix = {
      info: '📋',
      success: '✅',
      warning: '⚠️',
      error: '❌',
      debug: '🔍'
    }[type];
    
    console.log(`${prefix} [${timestamp}] ${message}`);
  }

  // 检查签名工具
  checkSigningTools() {
    this.log('检查代码签名工具...');
    
    try {
      if (this.platform === 'win32') {
        // 检查signtool
        execSync('signtool /?', { stdio: 'ignore' });
        this.log('Windows SignTool 可用', 'success');
        return true;
      } else if (this.platform === 'darwin') {
        // 检查codesign
        execSync('codesign --version', { stdio: 'ignore' });
        this.log('macOS codesign 可用', 'success');
        
        // 检查notarytool
        try {
          execSync('xcrun notarytool --version', { stdio: 'ignore' });
          this.log('macOS notarytool 可用', 'success');
        } catch (error) {
          this.log('macOS notarytool 不可用，公证功能将被禁用', 'warning');
        }
        return true;
      } else {
        this.log('Linux平台不需要代码签名', 'info');
        return true;
      }
    } catch (error) {
      this.log(`代码签名工具不可用: ${error.message}`, 'error');
      return false;
    }
  }

  // 检查证书配置
  checkCertificateConfig() {
    this.log('检查证书配置...');
    
    if (this.isDevelopment) {
      this.log('开发环境，跳过证书检查', 'info');
      return { valid: true, development: true };
    }

    const config = {
      valid: true,
      certificates: {},
      missing: []
    };

    if (this.platform === 'win32') {
      // Windows证书检查
      const winCert = {
        certificateFile: process.env.WIN_CSC_LINK,
        certificatePassword: process.env.WIN_CSC_KEY_PASSWORD,
        certificateSubjectName: process.env.WIN_CSC_NAME,
        timeStampServer: process.env.WIN_TIMESTAMP_SERVER || 'http://timestamp.digicert.com'
      };

      config.certificates.windows = winCert;

      if (!winCert.certificateFile) {
        config.missing.push('WIN_CSC_LINK (Windows证书文件路径)');
        config.valid = false;
      }
      if (!winCert.certificatePassword) {
        config.missing.push('WIN_CSC_KEY_PASSWORD (Windows证书密码)');
        config.valid = false;
      }

      // 检查证书文件是否存在
      if (winCert.certificateFile && !fs.existsSync(winCert.certificateFile)) {
        this.log(`Windows证书文件不存在: ${winCert.certificateFile}`, 'error');
        config.valid = false;
      }

    } else if (this.platform === 'darwin') {
      // macOS证书检查
      const macCert = {
        identity: process.env.MAC_CSC_NAME,
        teamId: process.env.APPLE_TEAM_ID,
        appleId: process.env.APPLE_ID,
        appleIdPassword: process.env.APPLE_APP_PASSWORD
      };

      config.certificates.mac = macCert;

      if (!macCert.identity) {
        config.missing.push('MAC_CSC_NAME (macOS证书身份)');
        config.valid = false;
      }

      // 公证配置检查（可选）
      if (macCert.teamId && macCert.appleId && macCert.appleIdPassword) {
        this.log('macOS公证配置完整', 'success');
      } else {
        this.log('macOS公证配置不完整，公证功能将被禁用', 'warning');
        if (!macCert.teamId) config.missing.push('APPLE_TEAM_ID (可选，用于公证)');
        if (!macCert.appleId) config.missing.push('APPLE_ID (可选，用于公证)');
        if (!macCert.appleIdPassword) config.missing.push('APPLE_APP_PASSWORD (可选，用于公证)');
      }
    }

    if (config.valid) {
      this.log('证书配置检查通过', 'success');
    } else {
      this.log('证书配置不完整', 'warning');
      this.log(`缺少环境变量: ${config.missing.join(', ')}`, 'warning');
    }

    return config;
  }

  // 生成自签名证书（开发环境）
  async generateSelfSignedCertificate() {
    if (this.isProduction) {
      this.log('生产环境不能使用自签名证书', 'error');
      return false;
    }

    this.log('生成开发环境自签名证书...');

    try {
      if (this.platform === 'win32') {
        // Windows自签名证书
        await this.generateWindowsSelfSignedCert();
      } else if (this.platform === 'darwin') {
        // macOS自签名证书
        await this.generateMacOSSelfSignedCert();
      }
      
      this.log('自签名证书生成完成', 'success');
      return true;
    } catch (error) {
      this.log(`自签名证书生成失败: ${error.message}`, 'error');
      return false;
    }
  }

  // 生成Windows自签名证书
  async generateWindowsSelfSignedCert() {
    const certDir = path.join(__dirname, '..', 'certificates', 'dev');
    if (!fs.existsSync(certDir)) {
      fs.mkdirSync(certDir, { recursive: true });
    }

    const certFile = path.join(certDir, 'selfsigned.p12');
    const password = 'dev-password-' + crypto.randomBytes(8).toString('hex');

    // 使用PowerShell生成自签名证书
    const script = `
$cert = New-SelfSignedCertificate -Type CodeSigningCert -Subject "CN=Self Game Developer (Dev)" -KeyUsage DigitalSignature -FriendlyName "Self Game Developer Development Certificate" -CertStoreLocation "Cert:\\CurrentUser\\My" -KeyLength 2048 -Provider "Microsoft Enhanced RSA and AES Cryptographic Provider" -KeyExportPolicy Exportable -KeySpec Signature -HashAlgorithm SHA256 -NotAfter (Get-Date).AddYears(2)
$password = ConvertTo-SecureString -String "${password}" -Force -AsPlainText
Export-PfxCertificate -cert "Cert:\\CurrentUser\\My\\$($cert.Thumbprint)" -FilePath "${certFile.replace(/\\/g, '\\\\')}" -Password $password
`;

    fs.writeFileSync(path.join(certDir, 'generate.ps1'), script);
    
    try {
      execSync(`powershell -ExecutionPolicy Bypass -File "${path.join(certDir, 'generate.ps1')}"`, { stdio: 'inherit' });
      
      // 保存证书信息
      const certInfo = {
        file: certFile,
        password: password,
        generated: new Date().toISOString(),
        type: 'self-signed',
        platform: 'windows'
      };
      
      fs.writeFileSync(
        path.join(certDir, 'cert-info.json'),
        JSON.stringify(certInfo, null, 2)
      );

      this.log(`Windows自签名证书生成完成: ${certFile}`, 'success');
      this.log(`证书密码: ${password}`, 'info');
      
    } catch (error) {
      this.log(`Windows自签名证书生成失败: ${error.message}`, 'error');
      throw error;
    }
  }

  // 生成macOS自签名证书
  async generateMacOSSelfSignedCert() {
    const identity = "Self Game Developer (Development)";
    
    try {
      // 检查是否已存在证书
      try {
        execSync(`security find-identity -v -p codesigning | grep "${identity}"`, { stdio: 'ignore' });
        this.log('macOS开发证书已存在', 'info');
        return;
      } catch (error) {
        // 证书不存在，需要创建
      }

      // 创建自签名证书
      const script = `
security create-certificate \\
  -c "${identity}" \\
  -t 1 \\
  -k ~/Library/Keychains/login.keychain-db \\
  -r \\
  -e 3650 \\
  -l "Self Game Developer Development Certificate"
`;

      execSync(script, { stdio: 'inherit' });
      this.log('macOS自签名证书创建完成', 'success');
      
    } catch (error) {
      this.log(`macOS自签名证书生成失败: ${error.message}`, 'error');
      throw error;
    }
  }

  // 验证签名
  async verifySignature(filePath) {
    this.log(`验证签名: ${filePath}`);

    try {
      if (this.platform === 'win32') {
        // Windows签名验证
        execSync(`signtool verify /pa "${filePath}"`, { stdio: 'inherit' });
        this.log('Windows签名验证通过', 'success');
        
      } else if (this.platform === 'darwin') {
        // macOS签名验证
        execSync(`codesign --verify --deep --strict --verbose=2 "${filePath}"`, { stdio: 'inherit' });
        this.log('macOS签名验证通过', 'success');
        
        // 显示签名信息
        const output = execSync(`codesign -dv "${filePath}"`, { encoding: 'utf8' });
        this.log(`签名信息:\n${output}`, 'debug');
      }
      
      return true;
    } catch (error) {
      this.log(`签名验证失败: ${error.message}`, 'error');
      return false;
    }
  }

  // 主要的初始化函数
  async initialize() {
    this.log('初始化代码签名环境...');

    // 检查签名工具
    if (!this.checkSigningTools()) {
      return false;
    }

    // 检查证书配置
    const certConfig = this.checkCertificateConfig();
    
    if (this.isDevelopment) {
      // 开发环境生成自签名证书
      if (!certConfig.valid && !certConfig.development) {
        this.log('开发环境缺少证书，准备生成自签名证书...');
        await this.generateSelfSignedCertificate();
      }
    } else if (this.isProduction) {
      // 生产环境需要有效证书
      if (!certConfig.valid) {
        this.log('生产环境需要有效的代码签名证书', 'error');
        this.log('请配置以下环境变量:', 'info');
        certConfig.missing.forEach(item => {
          this.log(`  - ${item}`, 'info');
        });
        return false;
      }
    }

    this.log('代码签名环境初始化完成', 'success');
    return true;
  }

  // 生成证书配置文档
  generateCertificateGuide() {
    const guide = `
# 代码签名证书配置指南

## Windows 证书配置

### 获取证书
1. 从证书颁发机构购买代码签名证书（如 DigiCert、Sectigo、Comodo）
2. 下载 .p12 或 .pfx 格式的证书文件
3. 记录证书密码

### 环境变量配置
\`\`\`bash
# Windows证书文件路径（绝对路径）
WIN_CSC_LINK=/path/to/certificate.p12

# Windows证书密码
WIN_CSC_KEY_PASSWORD=your_certificate_password

# Windows证书主题名称（可选）
WIN_CSC_NAME="Your Company Name"

# 时间戳服务器（可选）
WIN_TIMESTAMP_SERVER=http://timestamp.digicert.com
\`\`\`

## macOS 证书配置

### 获取证书
1. 注册 Apple Developer 账号
2. 在 Keychain Access 中申请 Developer ID Application 证书
3. 或使用 Xcode 自动管理证书

### 环境变量配置
\`\`\`bash
# macOS证书身份名称
MAC_CSC_NAME="Developer ID Application: Your Name (TEAM_ID)"

# Apple Team ID（用于公证）
APPLE_TEAM_ID=YOUR_TEAM_ID

# Apple ID（用于公证）
APPLE_ID=<EMAIL>

# App专用密码（用于公证）
APPLE_APP_PASSWORD=your_app_specific_password
\`\`\`

### 公证配置
1. 登录 https://appleid.apple.com/
2. 生成 App专用密码
3. 配置 APPLE_APP_PASSWORD 环境变量

## 开发环境

开发环境会自动生成自签名证书，无需配置生产证书。

## 证书安全建议

1. 不要将证书文件提交到版本控制系统
2. 使用环境变量或安全的密钥管理服务存储证书信息
3. 定期更新证书
4. 限制证书文件的访问权限
5. 在CI/CD环境中使用加密的环境变量

## 故障排除

### Windows
- 确保 Windows SDK 已安装（包含 signtool）
- 检查证书文件路径和密码
- 验证证书是否在有效期内

### macOS
- 确保 Xcode Command Line Tools 已安装
- 检查证书是否在 Keychain 中
- 验证 Apple Developer 账号状态
`;

    const guidePath = path.join(__dirname, '..', 'docs', 'code-signing-guide.md');
    const docsDir = path.dirname(guidePath);
    
    if (!fs.existsSync(docsDir)) {
      fs.mkdirSync(docsDir, { recursive: true });
    }
    
    fs.writeFileSync(guidePath, guide);
    this.log(`证书配置指南已生成: ${guidePath}`, 'success');
  }
}

// 命令行接口
async function main() {
  const manager = new CodeSigningManager();
  
  const command = process.argv[2];
  
  switch (command) {
    case 'init':
      await manager.initialize();
      break;
      
    case 'check':
      manager.checkSigningTools();
      manager.checkCertificateConfig();
      break;
      
    case 'generate-dev-cert':
      await manager.generateSelfSignedCertificate();
      break;
      
    case 'verify':
      const filePath = process.argv[3];
      if (!filePath) {
        console.error('请提供要验证的文件路径');
        process.exit(1);
      }
      await manager.verifySignature(filePath);
      break;
      
    case 'guide':
      manager.generateCertificateGuide();
      break;
      
    default:
      console.log(`
用法: node code-signing.js <command>

命令:
  init              - 初始化代码签名环境
  check             - 检查签名工具和证书配置
  generate-dev-cert - 生成开发环境自签名证书
  verify <file>     - 验证文件签名
  guide             - 生成证书配置指南

示例:
  node code-signing.js init
  node code-signing.js check
  node code-signing.js verify ./dist-packages/MyApp.exe
      `);
      break;
  }
}

// 如果作为主模块运行
if (require.main === module) {
  main().catch(error => {
    console.error('❌ 错误:', error.message);
    process.exit(1);
  });
}

module.exports = CodeSigningManager; 