import { Plugin } from 'vite'
import { CDNConfig, CDNUrlGenerator, getEnvironmentCDNConfig } from '../config/cdn.config'

interface CDNPluginOptions {
  config?: CDNConfig
  environment?: string
  include?: string[]
  exclude?: string[]
  fallback?: boolean
  versioning?: {
    enabled: boolean
    versionKey?: string
    format?: 'hash' | 'timestamp' | 'semver'
  }
}

interface AssetReplacement {
  original: string
  cdn: string
  type: string
  size?: number
}

/**
 * Vite CDN插件
 * 在构建时自动将静态资源URL替换为CDN URL
 */
export function cdnPlugin(options: CDNPluginOptions = {}): Plugin {
  let cdnConfig: CDNConfig
  let urlGenerator: CDNUrlGenerator
  let assetReplacements: AssetReplacement[] = []
  let isProduction = false

  return {
    name: 'vite-cdn-plugin',
    
    configResolved(config) {
      isProduction = config.command === 'build'
      
      // 获取CDN配置
      cdnConfig = options.config || getEnvironmentCDNConfig(
        options.environment || config.mode || 'development'
      )
      
      // 创建URL生成器
      urlGenerator = new CDNUrlGenerator(cdnConfig)
      
      console.log(`🚀 CDN Plugin initialized - Provider: ${cdnConfig.provider}, Enabled: ${cdnConfig.enabled}`)
    },

    generateBundle(outputOptions, bundle) {
      if (!cdnConfig.enabled || !isProduction) {
        return
      }

      // 收集所有资源文件
      const assets = Object.keys(bundle).filter(fileName => 
        bundle[fileName].type === 'asset' || 
        (bundle[fileName].type === 'chunk' && fileName.endsWith('.js'))
      )

      console.log(`📦 Processing ${assets.length} assets for CDN distribution`)

      // 处理每个资源文件
      for (const fileName of assets) {
        const asset = bundle[fileName]
        
        // 检查是否应该包含此资源
        if (!shouldIncludeAsset(fileName, options)) {
          continue
        }

        // 生成CDN URL
        const cdnUrl = urlGenerator.generateAssetUrl(fileName, getAssetVersion(fileName, options))
        
        // 记录替换信息
        assetReplacements.push({
          original: fileName,
          cdn: cdnUrl,
          type: getAssetType(fileName),
          size: getAssetSize(asset)
        })

        // 更新HTML文件中的引用
        updateHtmlReferences(bundle, fileName, cdnUrl)
        
        // 更新CSS文件中的引用
        updateCssReferences(bundle, fileName, cdnUrl)
        
        // 更新JS文件中的引用
        updateJsReferences(bundle, fileName, cdnUrl)
      }

      // 生成资源映射文件
      generateAssetManifest(bundle, assetReplacements)
      
      // 生成预加载提示
      generatePreloadHints(bundle, assetReplacements)
      
      console.log(`✅ CDN processing completed - ${assetReplacements.length} assets processed`)
    },

    writeBundle() {
      if (assetReplacements.length > 0) {
        console.log('📊 CDN Asset Replacement Summary:')
        console.table(assetReplacements.map(r => ({
          File: r.original,
          Type: r.type,
          'CDN URL': r.cdn.substring(0, 50) + (r.cdn.length > 50 ? '...' : ''),
          'Size (KB)': r.size ? Math.round(r.size / 1024) : 'N/A'
        })))
      }
    }
  }
}

/**
 * 检查资源是否应该包含在CDN中
 */
function shouldIncludeAsset(fileName: string, options: CDNPluginOptions): boolean {
  // 检查排除列表
  if (options.exclude) {
    for (const pattern of options.exclude) {
      if (fileName.match(new RegExp(pattern))) {
        return false
      }
    }
  }

  // 检查包含列表
  if (options.include) {
    for (const pattern of options.include) {
      if (fileName.match(new RegExp(pattern))) {
        return true
      }
    }
    return false
  }

  // 默认包含静态资源
  return /\.(js|css|png|jpe?g|gif|svg|woff2?|ttf|eot|ico|webp|mp4|webm|ogg|mp3|wav)$/i.test(fileName)
}

/**
 * 获取资源版本号
 */
function getAssetVersion(fileName: string, options: CDNPluginOptions): string | undefined {
  if (!options.versioning?.enabled) {
    return undefined
  }

  const versionKey = options.versioning.versionKey || 'version'
  
  switch (options.versioning.format) {
    case 'hash':
      // 从文件名中提取hash
      const hashMatch = fileName.match(/-([a-f0-9]{8,})\./)
      return hashMatch ? hashMatch[1].substring(0, 8) : undefined
      
    case 'timestamp':
      return Date.now().toString()
      
    case 'semver':
      return process.env.npm_package_version || '1.0.0'
      
    default:
      return undefined
  }
}

/**
 * 获取资源类型
 */
function getAssetType(fileName: string): string {
  const ext = fileName.split('.').pop()?.toLowerCase()
  
  switch (ext) {
    case 'js':
      return 'script'
    case 'css':
      return 'stylesheet'
    case 'png':
    case 'jpg':
    case 'jpeg':
    case 'gif':
    case 'svg':
    case 'webp':
      return 'image'
    case 'woff':
    case 'woff2':
    case 'ttf':
    case 'eot':
      return 'font'
    case 'mp4':
    case 'webm':
    case 'ogg':
      return 'video'
    case 'mp3':
    case 'wav':
    case 'flac':
      return 'audio'
    default:
      return 'resource'
  }
}

/**
 * 获取资源大小
 */
function getAssetSize(asset: any): number | undefined {
  if (asset.type === 'asset' && asset.source) {
    return asset.source.length
  } else if (asset.type === 'chunk' && asset.code) {
    return asset.code.length
  }
  return undefined
}

/**
 * 更新HTML文件中的资源引用
 */
function updateHtmlReferences(bundle: any, fileName: string, cdnUrl: string) {
  Object.keys(bundle).forEach(key => {
    const asset = bundle[key]
    if (asset.type === 'asset' && key.endsWith('.html')) {
      let html = asset.source.toString()
      
      // 替换脚本标签
      html = html.replace(
        new RegExp(`<script[^>]*src="[^"]*${fileName}"[^>]*>`, 'g'),
        (match) => match.replace(fileName, cdnUrl)
      )
      
      // 替换样式表链接
      html = html.replace(
        new RegExp(`<link[^>]*href="[^"]*${fileName}"[^>]*>`, 'g'),
        (match) => match.replace(fileName, cdnUrl)
      )
      
      // 替换图片源
      html = html.replace(
        new RegExp(`<img[^>]*src="[^"]*${fileName}"[^>]*>`, 'g'),
        (match) => match.replace(fileName, cdnUrl)
      )
      
      asset.source = html
    }
  })
}

/**
 * 更新CSS文件中的资源引用
 */
function updateCssReferences(bundle: any, fileName: string, cdnUrl: string) {
  Object.keys(bundle).forEach(key => {
    const asset = bundle[key]
    if ((asset.type === 'asset' && key.endsWith('.css')) || 
        (asset.type === 'chunk' && asset.code)) {
      
      let content = asset.type === 'asset' ? asset.source.toString() : asset.code
      
      // 替换URL引用
      content = content.replace(
        new RegExp(`url\\([^)]*${fileName}[^)]*\\)`, 'g'),
        (match) => match.replace(fileName, cdnUrl)
      )
      
      if (asset.type === 'asset') {
        asset.source = content
      } else {
        asset.code = content
      }
    }
  })
}

/**
 * 更新JS文件中的资源引用
 */
function updateJsReferences(bundle: any, fileName: string, cdnUrl: string) {
  Object.keys(bundle).forEach(key => {
    const asset = bundle[key]
    if (asset.type === 'chunk') {
      // 替换动态导入和资源引用
      asset.code = asset.code.replace(
        new RegExp(`["'\`][^"'\`]*${fileName}[^"'\`]*["'\`]`, 'g'),
        (match) => match.replace(fileName, cdnUrl)
      )
    }
  })
}

/**
 * 生成资源映射文件
 */
function generateAssetManifest(bundle: any, replacements: AssetReplacement[]) {
  const manifest = {
    version: process.env.npm_package_version || '1.0.0',
    timestamp: new Date().toISOString(),
    cdnProvider: 'configured',
    assets: replacements.reduce((acc, replacement) => {
      acc[replacement.original] = {
        cdn: replacement.cdn,
        type: replacement.type,
        size: replacement.size
      }
      return acc
    }, {} as Record<string, any>)
  }

  bundle['cdn-manifest.json'] = {
    type: 'asset',
    name: undefined,
    source: JSON.stringify(manifest, null, 2),
    fileName: 'cdn-manifest.json'
  }
}

/**
 * 生成预加载提示
 */
function generatePreloadHints(bundle: any, replacements: AssetReplacement[]) {
  // 找到主HTML文件
  const htmlFiles = Object.keys(bundle).filter(key => 
    bundle[key].type === 'asset' && key.endsWith('.html')
  )

  htmlFiles.forEach(htmlFile => {
    const asset = bundle[htmlFile]
    let html = asset.source.toString()
    
    // 生成关键资源的预加载链接
    const criticalAssets = replacements.filter(r => 
      r.type === 'script' || r.type === 'stylesheet'
    ).slice(0, 5) // 限制预加载数量
    
    const preloadLinks = criticalAssets.map(asset => 
      `  <link rel="preload" href="${asset.cdn}" as="${asset.type === 'script' ? 'script' : 'style'}" crossorigin>`
    ).join('\n')
    
    if (preloadLinks && html.includes('<head>')) {
      html = html.replace('<head>', `<head>\n${preloadLinks}`)
      asset.source = html
    }
  })
}

/**
 * 创建CDN回退机制
 */
export function createCDNFallback(originalUrl: string, cdnUrl: string): string {
  return `
// CDN fallback for ${originalUrl}
(function() {
  function loadFallback() {
    var script = document.createElement('script');
    script.src = '${originalUrl}';
    script.onerror = function() {
      console.error('Failed to load both CDN and fallback for ${originalUrl}');
    };
    document.head.appendChild(script);
  }
  
  var script = document.createElement('script');
  script.src = '${cdnUrl}';
  script.onerror = loadFallback;
  script.onload = function() {
    // CDN loaded successfully
  };
  document.head.appendChild(script);
})();
  `.trim()
}

export default cdnPlugin 