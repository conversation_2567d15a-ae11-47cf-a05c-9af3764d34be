#!/usr/bin/env node

const fs = require('fs')
const path = require('path')

class PerformanceChecker {
  constructor() {
    this.projectRoot = process.cwd()
    this.issues = []
    this.suggestions = []
  }

  async run() {
    console.log('🔍 开始性能检查...\n')
    
    // 检查各个方面
    await this.checkViteConfig()
    await this.checkPackageJson()
    await this.checkSourceCode()
    await this.checkAssets()
    await this.checkTypeScript()
    
    // 生成报告
    this.generateReport()
  }

  async checkViteConfig() {
    console.log('📋 检查Vite配置...')
    
    const viteConfigPath = path.join(this.projectRoot, 'vite.config.ts')
    if (!fs.existsSync(viteConfigPath)) {
      this.addIssue('配置', 'Vite配置文件不存在', 'high')
      return
    }

    const config = fs.readFileSync(viteConfigPath, 'utf8')
    
    // 检查代码分割
    if (!config.includes('manualChunks')) {
      this.addSuggestion('配置', '启用手动代码分割', 'medium', [
        '在vite.config.ts中配置manualChunks',
        '将vendor库分离到独立chunk',
        '按功能模块分割代码'
      ])
    } else {
      console.log('  ✅ 已配置代码分割')
    }

    // 检查压缩
    if (!config.includes('compression')) {
      this.addSuggestion('配置', '启用文件压缩', 'medium', [
        '安装 vite-plugin-compression2',
        '配置gzip和brotli压缩'
      ])
    } else {
      console.log('  ✅ 已配置文件压缩')
    }

    // 检查PWA
    if (!config.includes('VitePWA')) {
      this.addSuggestion('配置', '考虑启用PWA', 'low', [
        '安装 vite-plugin-pwa',
        '配置service worker',
        '启用缓存策略'
      ])
    } else {
      console.log('  ✅ 已配置PWA')
    }

    // 检查构建优化
    if (!config.includes('terserOptions')) {
      this.addSuggestion('配置', '优化Terser配置', 'medium', [
        '移除console.log语句',
        '启用更积极的压缩',
        '优化代码混淆'
      ])
    } else {
      console.log('  ✅ 已配置Terser优化')
    }
  }

  async checkPackageJson() {
    console.log('📦 检查依赖配置...')
    
    const packagePath = path.join(this.projectRoot, 'package.json')
    const packageData = JSON.parse(fs.readFileSync(packagePath, 'utf8'))
    
    // 检查依赖大小
    const largeDeps = [
      'lodash',
      'moment', 
      'jquery',
      'bootstrap',
      '@material-ui/core'
    ]
    
    const installedLargeDeps = largeDeps.filter(dep => 
      packageData.dependencies?.[dep] || packageData.devDependencies?.[dep]
    )
    
    if (installedLargeDeps.length > 0) {
      this.addSuggestion('依赖', '考虑替换大型依赖库', 'medium', [
        `检测到大型依赖: ${installedLargeDeps.join(', ')}`,
        '考虑使用更轻量的替代品',
        'lodash → lodash-es 或 tree-shaking',
        'moment → date-fns 或 dayjs',
        '按需导入库的部分功能'
      ])
    }

    // 检查未使用的依赖
    console.log('  💡 建议运行 npm ls 检查未使用的依赖')
    
    // 检查构建脚本
    const scripts = packageData.scripts || {}
    if (!scripts['build:analyze']) {
      this.addSuggestion('脚本', '添加构建分析脚本', 'low', [
        '添加构建体积分析工具',
        '定期检查包大小变化'
      ])
    }
  }

  async checkSourceCode() {
    console.log('💻 检查源代码...')
    
    const srcPath = path.join(this.projectRoot, 'src')
    if (!fs.existsSync(srcPath)) {
      this.addIssue('代码', '源代码目录不存在', 'high')
      return
    }

    // 检查大文件
    await this.checkLargeFiles(srcPath)
    
    // 检查导入模式
    await this.checkImportPatterns(srcPath)
    
    // 检查组件结构
    await this.checkComponentStructure(srcPath)
  }

  async checkLargeFiles(dir) {
    const walkDir = (currentDir) => {
      const files = fs.readdirSync(currentDir)
      
      files.forEach(file => {
        const filePath = path.join(currentDir, file)
        const stat = fs.statSync(filePath)
        
        if (stat.isDirectory()) {
          walkDir(filePath)
        } else if (file.endsWith('.tsx') || file.endsWith('.ts')) {
          // 检查大组件文件 (>500行)
          const content = fs.readFileSync(filePath, 'utf8')
          const lines = content.split('\n').length
          
          if (lines > 500) {
            this.addSuggestion('代码', `大文件需要拆分: ${path.relative(this.projectRoot, filePath)}`, 'high', [
              `文件有 ${lines} 行，建议拆分`,
              '将组件拆分成更小的子组件',
              '使用懒加载',
              '提取业务逻辑到hooks'
            ])
          }
        }
      })
    }
    
    walkDir(dir)
  }

  async checkImportPatterns(dir) {
    const issues = []
    
    const walkDir = (currentDir) => {
      const files = fs.readdirSync(currentDir)
      
      files.forEach(file => {
        const filePath = path.join(currentDir, file)
        const stat = fs.statSync(filePath)
        
        if (stat.isDirectory()) {
          walkDir(filePath)
        } else if (file.endsWith('.tsx') || file.endsWith('.ts')) {
          const content = fs.readFileSync(filePath, 'utf8')
          
          // 检查完整库导入
          if (content.includes('import * as React')) {
            issues.push(`${path.relative(this.projectRoot, filePath)}: 使用 import * as React`)
          }
          
          // 检查lodash完整导入
          if (content.includes('import _ from \'lodash\'')) {
            issues.push(`${path.relative(this.projectRoot, filePath)}: 完整导入lodash`)
          }
          
          // 检查material-ui完整导入
          if (content.includes('import {') && content.includes('} from \'@mui/material\'')) {
            // 这是好的，按需导入
          } else if (content.includes('import * from \'@mui/material\'')) {
            issues.push(`${path.relative(this.projectRoot, filePath)}: 完整导入@mui/material`)
          }
        }
      })
    }
    
    walkDir(dir)
    
    if (issues.length > 0) {
      this.addSuggestion('导入', '优化导入模式', 'medium', [
        '发现可优化的导入:',
        ...issues.slice(0, 5), // 只显示前5个
        '使用按需导入减少包大小',
        '考虑使用babel-plugin-import'
      ])
    }
  }

  async checkComponentStructure(dir) {
    const componentsDir = path.join(dir, 'components')
    if (!fs.existsSync(componentsDir)) {
      return
    }

    const componentFiles = []
    
    const walkDir = (currentDir) => {
      const files = fs.readdirSync(currentDir)
      
      files.forEach(file => {
        const filePath = path.join(currentDir, file)
        const stat = fs.statSync(filePath)
        
        if (stat.isDirectory()) {
          walkDir(filePath)
        } else if (file.endsWith('.tsx')) {
          componentFiles.push(filePath)
        }
      })
    }
    
    walkDir(componentsDir)
    
    // 检查是否有懒加载
    let hasLazyLoading = false
    componentFiles.forEach(file => {
      const content = fs.readFileSync(file, 'utf8')
      if (content.includes('React.lazy') || content.includes('lazy(')) {
        hasLazyLoading = true
      }
    })
    
    if (!hasLazyLoading && componentFiles.length > 10) {
      this.addSuggestion('组件', '考虑使用懒加载', 'medium', [
        `检测到 ${componentFiles.length} 个组件`,
        '大型组件可以使用React.lazy懒加载',
        '减少初始包大小',
        '提升首屏加载速度'
      ])
    }
  }

  async checkAssets() {
    console.log('🖼️  检查资源文件...')
    
    const assetsDir = path.join(this.projectRoot, 'src', 'assets')
    const publicDir = path.join(this.projectRoot, 'public')
    
    const checkDir = (dir, dirName) => {
      if (!fs.existsSync(dir)) return
      
      const walkDir = (currentDir) => {
        const files = fs.readdirSync(currentDir)
        
        files.forEach(file => {
          const filePath = path.join(currentDir, file)
          const stat = fs.statSync(filePath)
          
          if (stat.isDirectory()) {
            walkDir(filePath)
          } else {
            const ext = path.extname(file).toLowerCase()
            
            // 检查大图片文件
            if (['.png', '.jpg', '.jpeg', '.gif'].includes(ext) && stat.size > 500 * 1024) {
              this.addSuggestion('资源', `优化大图片: ${path.relative(this.projectRoot, filePath)}`, 'medium', [
                `文件大小: ${this.formatSize(stat.size)}`,
                '使用WebP格式',
                '启用图片压缩',
                '考虑使用响应式图片'
              ])
            }
            
            // 检查视频文件
            if (['.mp4', '.webm', '.avi'].includes(ext) && stat.size > 2 * 1024 * 1024) {
              this.addSuggestion('资源', `优化大视频: ${path.relative(this.projectRoot, filePath)}`, 'high', [
                `文件大小: ${this.formatSize(stat.size)}`,
                '考虑使用CDN',
                '启用视频压缩',
                '使用懒加载'
              ])
            }
          }
        })
      }
      
      walkDir(dir)
    }
    
    checkDir(assetsDir, 'assets')
    checkDir(publicDir, 'public')
  }

  async checkTypeScript() {
    console.log('📝 检查TypeScript配置...')
    
    const tsconfigPath = path.join(this.projectRoot, 'tsconfig.json')
    if (!fs.existsSync(tsconfigPath)) {
      this.addIssue('TypeScript', 'tsconfig.json不存在', 'medium')
      return
    }

    try {
      const tsconfigContent = fs.readFileSync(tsconfigPath, 'utf8')
      // 移除JSON中可能的注释和多余空白
      const cleanedContent = tsconfigContent
        .replace(/\/\*[\s\S]*?\*\//g, '') // 移除块注释
        .replace(/\/\/.*$/gm, '') // 移除行注释
        .trim()
      
      const tsconfig = JSON.parse(cleanedContent)
      const compilerOptions = tsconfig.compilerOptions || {}
      
      // 检查性能相关配置
      if (!compilerOptions.incremental) {
        this.addSuggestion('TypeScript', '启用增量编译', 'low', [
          '在tsconfig.json中设置 "incremental": true',
          '提升重复构建速度'
        ])
      } else {
        console.log('  ✅ 已启用增量编译')
      }
      
      if (compilerOptions.target !== 'ES2020' && compilerOptions.target !== 'ES2022') {
        this.addSuggestion('TypeScript', '优化编译目标', 'low', [
          `当前目标: ${compilerOptions.target || 'ES5'}`,
          '考虑使用ES2020或ES2022',
          '减少polyfill需求'
        ])
      } else {
        console.log(`  ✅ 编译目标已优化: ${compilerOptions.target}`)
      }
      
      if (!compilerOptions.strict) {
        this.addSuggestion('TypeScript', '考虑启用严格模式', 'low', [
          '启用strict模式可以提高代码质量',
          '有助于在编译时发现更多问题'
        ])
      }
      
    } catch (error) {
      this.addIssue('TypeScript', `tsconfig.json解析失败: ${error.message}`, 'medium')
      console.log(`  ⚠️  TypeScript配置解析失败，跳过检查`)
    }
  }

  addIssue(category, message, priority) {
    this.issues.push({ category, message, priority })
  }

  addSuggestion(category, message, priority, actions = []) {
    this.suggestions.push({ category, message, priority, actions })
  }

  generateReport() {
    console.log('\n📊 性能检查报告\n')
    
    if (this.issues.length === 0 && this.suggestions.length === 0) {
      console.log('✅ 未发现明显的性能问题！')
      return
    }
    
    // 显示问题
    if (this.issues.length > 0) {
      console.log('🚨 发现的问题:')
      this.issues.forEach((issue, index) => {
        const priority = issue.priority === 'high' ? '🔴' : 
                        issue.priority === 'medium' ? '🟡' : '🟢'
        console.log(`  ${index + 1}. ${priority} [${issue.category}] ${issue.message}`)
      })
      console.log()
    }
    
    // 显示建议
    if (this.suggestions.length > 0) {
      console.log('💡 优化建议:')
      this.suggestions.forEach((suggestion, index) => {
        const priority = suggestion.priority === 'high' ? '🔴' : 
                        suggestion.priority === 'medium' ? '🟡' : '🟢'
        console.log(`  ${index + 1}. ${priority} [${suggestion.category}] ${suggestion.message}`)
        
        if (suggestion.actions.length > 0) {
          suggestion.actions.forEach(action => {
            console.log(`     • ${action}`)
          })
        }
        console.log()
      })
    }
    
    // 生成JSON报告
    const report = {
      timestamp: new Date().toISOString(),
      issues: this.issues,
      suggestions: this.suggestions,
      summary: {
        totalIssues: this.issues.length,
        totalSuggestions: this.suggestions.length,
        highPriorityItems: [...this.issues, ...this.suggestions].filter(item => item.priority === 'high').length
      }
    }
    
    const reportPath = path.join(this.projectRoot, 'performance-check-report.json')
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2))
    console.log(`📋 详细报告已保存到: ${reportPath}`)
  }

  formatSize(bytes) {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }
}

// 主执行函数
async function main() {
  const checker = new PerformanceChecker()
  await checker.run()
}

// 如果直接运行脚本
if (require.main === module) {
  main().catch(console.error)
}

module.exports = PerformanceChecker 