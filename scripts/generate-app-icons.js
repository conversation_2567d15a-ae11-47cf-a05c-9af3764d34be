#!/usr/bin/env node

const fs = require('fs')
const path = require('path')

/**
 * 生成应用图标和构建资源
 * 为Electron应用创建必要的图标和安装程序资源
 */
class IconGenerator {
  constructor() {
    this.buildDir = path.join(process.cwd(), 'build')
    this.iconsDir = path.join(this.buildDir, 'icons')
    this.assetsDir = path.join(process.cwd(), 'assets')
  }

  /**
   * 生成所有必要的图标和资源
   */
  async generateAll() {
    console.log('🎨 开始生成应用图标和构建资源...\n')

    // 确保目录存在
    this.ensureDirectories()

    // 生成Windows图标
    await this.generateWindowsIcons()

    // 生成macOS图标
    await this.generateMacOSIcons()

    // 生成Linux图标
    await this.generateLinuxIcons()

    // 生成安装程序资源
    await this.generateInstallerResources()

    console.log('\n✅ 所有图标和资源生成完成!')
  }

  /**
   * 确保必要的目录存在
   */
  ensureDirectories() {
    const dirs = [this.buildDir, this.iconsDir, this.assetsDir]
    
    dirs.forEach(dir => {
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true })
        console.log(`📁 创建目录: ${dir}`)
      }
    })
  }

  /**
   * 生成Windows图标
   */
  async generateWindowsIcons() {
    console.log('🪟 生成Windows图标...')

    // 创建ICO格式的应用图标
    const icoContent = this.generateICOPlaceholder()
    fs.writeFileSync(path.join(this.buildDir, 'icon.ico'), icoContent)
    console.log('  ✅ icon.ico')

    // 安装程序图标
    fs.writeFileSync(path.join(this.buildDir, 'installer.ico'), icoContent)
    console.log('  ✅ installer.ico')

    // 卸载程序图标
    fs.writeFileSync(path.join(this.buildDir, 'uninstaller.ico'), icoContent)
    console.log('  ✅ uninstaller.ico')

    // 安装程序头部图标
    fs.writeFileSync(path.join(this.buildDir, 'installerHeader.ico'), icoContent)
    console.log('  ✅ installerHeader.ico')
  }

  /**
   * 生成macOS图标
   */
  async generateMacOSIcons() {
    console.log('🍎 生成macOS图标...')

    // 创建ICNS格式的应用图标
    const icnsContent = this.generateICNSPlaceholder()
    fs.writeFileSync(path.join(this.buildDir, 'icon.icns'), icnsContent)
    console.log('  ✅ icon.icns')

    // DMG背景图
    const dmgBackground = this.generateDMGBackground()
    fs.writeFileSync(path.join(this.buildDir, 'dmg-background.png'), dmgBackground)
    console.log('  ✅ dmg-background.png')
  }

  /**
   * 生成Linux图标
   */
  async generateLinuxIcons() {
    console.log('🐧 生成Linux图标...')

    // 创建不同尺寸的PNG图标
    const iconSizes = [16, 24, 32, 48, 64, 96, 128, 256, 512]
    
    iconSizes.forEach(size => {
      const pngContent = this.generatePNGPlaceholder(size)
      const filename = `${size}x${size}.png`
      fs.writeFileSync(path.join(this.iconsDir, filename), pngContent)
      console.log(`  ✅ ${filename}`)
    })
  }

  /**
   * 生成安装程序资源
   */
  async generateInstallerResources() {
    console.log('📦 生成安装程序资源...')

    // Windows NSIS安装脚本
    const nsisScript = this.generateNSISScript()
    fs.writeFileSync(path.join(this.buildDir, 'installer.nsh'), nsisScript)
    console.log('  ✅ installer.nsh')

    // 安装程序侧边栏图像
    const sidebarImage = this.generateSidebarImage()
    fs.writeFileSync(path.join(this.buildDir, 'installerSidebar.bmp'), sidebarImage)
    fs.writeFileSync(path.join(this.buildDir, 'uninstallerSidebar.bmp'), sidebarImage)
    console.log('  ✅ installerSidebar.bmp')
    console.log('  ✅ uninstallerSidebar.bmp')

    // 应用许可证
    const license = this.generateLicense()
    fs.writeFileSync(path.join(process.cwd(), 'LICENSE'), license)
    console.log('  ✅ LICENSE')
  }

  /**
   * 生成ICO格式占位符
   */
  generateICOPlaceholder() {
    // 创建多尺寸ICO文件（16x16, 32x32, 48x48, 256x256）
    const sizes = [16, 32, 48, 256]
    const images = []
    let dataOffset = 6 + (sizes.length * 16) // 文件头 + 图像目录表
    
    // ICO文件头
    const header = Buffer.alloc(6)
    header.writeUInt16LE(0, 0)           // Reserved
    header.writeUInt16LE(1, 2)           // Type (1 for ICO)
    header.writeUInt16LE(sizes.length, 4) // Number of images
    
    // 为每个尺寸生成图像数据
    const imageDataBuffers = []
    const directoryEntries = []
    
    sizes.forEach(size => {
      const pixelData = this.generateICOImageData(size)
      imageDataBuffers.push(pixelData)
      
      // 目录条目
      const entry = Buffer.alloc(16)
      entry.writeUInt8(size === 256 ? 0 : size, 0)  // Width (0 for 256)
      entry.writeUInt8(size === 256 ? 0 : size, 1)  // Height (0 for 256)
      entry.writeUInt8(0, 2)                        // Color count
      entry.writeUInt8(0, 3)                        // Reserved
      entry.writeUInt16LE(1, 4)                     // Color planes
      entry.writeUInt16LE(32, 6)                    // Bits per pixel
      entry.writeUInt32LE(pixelData.length, 8)      // Image size
      entry.writeUInt32LE(dataOffset, 12)           // Image offset
      
      directoryEntries.push(entry)
      dataOffset += pixelData.length
    })
    
    return Buffer.concat([
      header,
      ...directoryEntries,
      ...imageDataBuffers
    ])
  }

  /**
   * 生成特定尺寸的ICO图像数据
   */
  generateICOImageData(size) {
    const pixelCount = size * size * 4 // RGBA
    const pixelData = Buffer.alloc(pixelCount)
    
    // 创建绿色渐变圆形图标（农场主题）
    const centerX = size / 2
    const centerY = size / 2
    const radius = size * 0.4
    
    for (let y = 0; y < size; y++) {
      for (let x = 0; x < size; x++) {
        const offset = (y * size + x) * 4
        const distance = Math.sqrt((x - centerX) ** 2 + (y - centerY) ** 2)
        
        if (distance <= radius) {
          // 内部：绿色到深绿色渐变
          const intensity = Math.max(0.3, 1 - (distance / radius) * 0.7)
          pixelData[offset] = 0x00                    // Blue
          pixelData[offset + 1] = Math.floor(intensity * 200) // Green
          pixelData[offset + 2] = Math.floor(intensity * 50)  // Red
          pixelData[offset + 3] = 0xFF                // Alpha
        } else if (distance <= radius * 1.1) {
          // 边缘：半透明绿色
          pixelData[offset] = 0x00     // Blue
          pixelData[offset + 1] = 0x80 // Green
          pixelData[offset + 2] = 0x20 // Red
          pixelData[offset + 3] = 0x80 // Alpha (半透明)
        } else {
          // 外部：透明
          pixelData[offset] = 0x00     // Blue
          pixelData[offset + 1] = 0x00 // Green
          pixelData[offset + 2] = 0x00 // Red
          pixelData[offset + 3] = 0x00 // Alpha (透明)
        }
      }
    }
    
    // 简化的DIB头部（用于ICO内部的位图）
    const dibHeader = Buffer.alloc(40)
    dibHeader.writeUInt32LE(40, 0)        // Header size
    dibHeader.writeInt32LE(size, 4)       // Width
    dibHeader.writeInt32LE(size * 2, 8)   // Height (doubled for ICO)
    dibHeader.writeUInt16LE(1, 12)        // Planes
    dibHeader.writeUInt16LE(32, 14)       // Bits per pixel
    dibHeader.writeUInt32LE(0, 16)        // Compression
    dibHeader.writeUInt32LE(pixelCount, 20) // Image size
    
    // ICO格式需要倒序行数据
    const flippedPixelData = Buffer.alloc(pixelCount)
    for (let y = 0; y < size; y++) {
      const sourceY = size - 1 - y
      for (let x = 0; x < size; x++) {
        const sourceOffset = (sourceY * size + x) * 4
        const targetOffset = (y * size + x) * 4
        flippedPixelData[targetOffset] = pixelData[sourceOffset]     // Blue
        flippedPixelData[targetOffset + 1] = pixelData[sourceOffset + 1] // Green
        flippedPixelData[targetOffset + 2] = pixelData[sourceOffset + 2] // Red
        flippedPixelData[targetOffset + 3] = pixelData[sourceOffset + 3] // Alpha
      }
    }
    
    // AND mask（全零，因为我们使用alpha通道）
    const andMask = Buffer.alloc(Math.ceil(size * size / 8))
    
    return Buffer.concat([dibHeader, flippedPixelData, andMask])
  }

  /**
   * 生成ICNS格式占位符
   */
  generateICNSPlaceholder() {
    // 简单的ICNS文件头
    const header = Buffer.from([
      0x69, 0x63, 0x6e, 0x73, // 'icns' signature
      0x00, 0x00, 0x04, 0x08  // File size
    ])

    // 添加一个简单的16x16图标
    const iconHeader = Buffer.from([
      0x69, 0x63, 0x31, 0x36, // 'ic16' type
      0x00, 0x00, 0x04, 0x00  // Size
    ])

    const iconData = Buffer.alloc(1024, 0x00)
    
    return Buffer.concat([header, iconHeader, iconData])
  }

  /**
   * 生成PNG格式占位符
   */
  generatePNGPlaceholder(size) {
    // PNG文件签名
    const signature = Buffer.from([0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A])
    
    // IHDR chunk（图像头）
    const ihdrData = Buffer.alloc(13)
    ihdrData.writeUInt32BE(size, 0)     // Width
    ihdrData.writeUInt32BE(size, 4)     // Height
    ihdrData.writeUInt8(8, 8)           // Bit depth
    ihdrData.writeUInt8(6, 9)           // Color type (RGBA)
    ihdrData.writeUInt8(0, 10)          // Compression
    ihdrData.writeUInt8(0, 11)          // Filter
    ihdrData.writeUInt8(0, 12)          // Interlace

    const ihdrChunk = this.createPNGChunk('IHDR', ihdrData)

    // 简单的绿色像素数据
    const pixelCount = size * size * 4 // RGBA
    const imageData = Buffer.alloc(pixelCount)
    
    for (let i = 0; i < pixelCount; i += 4) {
      imageData[i] = 0x00     // Red
      imageData[i + 1] = 0x80 // Green
      imageData[i + 2] = 0x00 // Blue
      imageData[i + 3] = 0xFF // Alpha
    }

    const idatChunk = this.createPNGChunk('IDAT', imageData)
    const iendChunk = this.createPNGChunk('IEND', Buffer.alloc(0))

    return Buffer.concat([signature, ihdrChunk, idatChunk, iendChunk])
  }

  /**
   * 创建PNG数据块
   */
  createPNGChunk(type, data) {
    const length = Buffer.alloc(4)
    length.writeUInt32BE(data.length, 0)
    
    const typeBuffer = Buffer.from(type, 'ascii')
    const crc = this.calculateCRC(Buffer.concat([typeBuffer, data]))
    const crcBuffer = Buffer.alloc(4)
    crcBuffer.writeUInt32BE(crc, 0)

    return Buffer.concat([length, typeBuffer, data, crcBuffer])
  }

  /**
   * 计算CRC32
   */
  calculateCRC(data) {
    let crc = 0xFFFFFFFF
    for (let i = 0; i < data.length; i++) {
      crc ^= data[i]
      for (let j = 0; j < 8; j++) {
        crc = (crc & 1) ? (0xEDB88320 ^ (crc >>> 1)) : (crc >>> 1)
      }
    }
    return (crc ^ 0xFFFFFFFF) >>> 0
  }

  /**
   * 生成DMG背景图
   */
  generateDMGBackground() {
    // 创建一个简单的660x400像素的PNG作为DMG背景
    return this.generatePNGPlaceholder(660) // 简化为正方形
  }

  /**
   * 生成侧边栏图像
   */
  generateSidebarImage() {
    // 简单的BMP文件头（164x314像素）
    const width = 164
    const height = 314
    const pixelDataSize = width * height * 3 // RGB

    const header = Buffer.alloc(54)
    header.write('BM', 0) // BMP signature
    header.writeUInt32LE(54 + pixelDataSize, 2) // File size
    header.writeUInt32LE(54, 10) // Pixel data offset
    header.writeUInt32LE(40, 14) // Header size
    header.writeUInt32LE(width, 18) // Width
    header.writeUInt32LE(height, 22) // Height
    header.writeUInt16LE(1, 26) // Planes
    header.writeUInt16LE(24, 28) // Bits per pixel

    // 创建渐变绿色背景
    const pixelData = Buffer.alloc(pixelDataSize)
    for (let y = 0; y < height; y++) {
      for (let x = 0; x < width; x++) {
        const offset = (y * width + x) * 3
        const intensity = Math.floor((y / height) * 128 + 64)
        pixelData[offset] = 0x00 // Blue
        pixelData[offset + 1] = intensity // Green
        pixelData[offset + 2] = 0x00 // Red
      }
    }

    return Buffer.concat([header, pixelData])
  }

  /**
   * 生成NSIS安装脚本
   */
  generateNSISScript() {
    return `
; 自律农场安装程序自定义脚本
; 注意：不要重复定义MUI_ICON等已在electron-builder中定义的变量

; 安装完成后的操作
Function .onInstSuccess
  ; 创建桌面快捷方式
  CreateShortCut "$DESKTOP\\自律农场.lnk" "$INSTDIR\\自律农场.exe"
  
  ; 创建开始菜单快捷方式
  CreateDirectory "$SMPROGRAMS\\自律农场"
  CreateShortCut "$SMPROGRAMS\\自律农场\\自律农场.lnk" "$INSTDIR\\自律农场.exe"
  CreateShortCut "$SMPROGRAMS\\自律农场\\卸载自律农场.lnk" "$INSTDIR\\Uninstall.exe"
  
  ; 注册文件关联（如果需要）
  WriteRegStr HKCR ".selfgame" "" "SelfGameFile"
  WriteRegStr HKCR "SelfGameFile" "" "自律农场存档文件"
  WriteRegStr HKCR "SelfGameFile\\shell\\open\\command" "" '"$INSTDIR\\自律农场.exe" "%1"'
FunctionEnd

; 卸载前的操作
Function un.onInit
  MessageBox MB_ICONQUESTION|MB_YESNO|MB_DEFBUTTON2 "您确定要完全移除自律农场及其所有组件吗？" IDYES +2
  Abort
FunctionEnd

; 卸载完成后的操作
Function un.onUninstSuccess
  ; 删除桌面快捷方式
  Delete "$DESKTOP\\自律农场.lnk"
  
  ; 删除开始菜单快捷方式
  RMDir /r "$SMPROGRAMS\\自律农场"
  
  ; 清理注册表
  DeleteRegKey HKCR ".selfgame"
  DeleteRegKey HKCR "SelfGameFile"
  
  MessageBox MB_ICONINFORMATION "自律农场已成功从您的计算机中移除。"
FunctionEnd
    `.trim()
  }

  /**
   * 生成许可证文件
   */
  generateLicense() {
    return `
MIT License

Copyright (c) 2024 Self Game Developer

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.

---

自律农场 - 智能习惯养成游戏

这是一款通过计算机视觉技术帮助用户建立自律习惯的创新游戏。
通过监测用户的计算机使用行为，游戏能够引导用户形成良好的工作和学习习惯。

特性：
- 智能应用监控和白名单管理
- 游戏化奖励机制
- 跨平台支持（Windows、macOS、Linux）
- 数据隐私保护

使用本软件即表示您同意上述MIT许可协议的条款。
    `.trim()
  }
}

// CLI界面
async function main() {
  const generator = new IconGenerator()
  
  try {
    await generator.generateAll()
    console.log('\n🎉 图标和资源生成完成!')
    console.log('\n📋 生成的文件:')
    console.log('  Windows: build/icon.ico, build/installer.ico, build/uninstaller.ico')
    console.log('  macOS: build/icon.icns, build/dmg-background.png')
    console.log('  Linux: build/icons/*.png')
    console.log('  Installer: build/installer.nsh, build/*Sidebar.bmp')
    console.log('  License: LICENSE')
    
    console.log('\n💡 提示:')
    console.log('  - 这些是基础的占位符图标和资源文件')
    console.log('  - 建议使用专业工具创建高质量的图标')
    console.log('  - 可以替换这些文件为自定义设计的图标')
    console.log('  - 运行 npm run electron:pack 开始打包应用')
    
  } catch (error) {
    console.error('❌ 生成失败:', error.message)
    process.exit(1)
  }
}

if (require.main === module) {
  main()
}

module.exports = IconGenerator 