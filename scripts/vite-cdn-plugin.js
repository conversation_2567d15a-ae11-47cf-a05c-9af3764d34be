import { CDNUrlGenerator, getEnvironmentCDNConfig } from '../config/cdn.config';
/**
 * Vite CDN插件
 * 在构建时自动将静态资源URL替换为CDN URL
 */
export function cdnPlugin(options) {
    if (options === void 0) { options = {}; }
    var cdnConfig;
    var urlGenerator;
    var assetReplacements = [];
    var isProduction = false;
    return {
        name: 'vite-cdn-plugin',
        configResolved: function (config) {
            isProduction = config.command === 'build';
            // 获取CDN配置
            cdnConfig = options.config || getEnvironmentCDNConfig(options.environment || config.mode || 'development');
            // 创建URL生成器
            urlGenerator = new CDNUrlGenerator(cdnConfig);
            console.log("\uD83D\uDE80 CDN Plugin initialized - Provider: ".concat(cdnConfig.provider, ", Enabled: ").concat(cdnConfig.enabled));
        },
        generateBundle: function (outputOptions, bundle) {
            if (!cdnConfig.enabled || !isProduction) {
                return;
            }
            // 收集所有资源文件
            var assets = Object.keys(bundle).filter(function (fileName) {
                return bundle[fileName].type === 'asset' ||
                    (bundle[fileName].type === 'chunk' && fileName.endsWith('.js'));
            });
            console.log("\uD83D\uDCE6 Processing ".concat(assets.length, " assets for CDN distribution"));
            // 处理每个资源文件
            for (var _i = 0, assets_1 = assets; _i < assets_1.length; _i++) {
                var fileName = assets_1[_i];
                var asset = bundle[fileName];
                // 检查是否应该包含此资源
                if (!shouldIncludeAsset(fileName, options)) {
                    continue;
                }
                // 生成CDN URL
                var cdnUrl = urlGenerator.generateAssetUrl(fileName, getAssetVersion(fileName, options));
                // 记录替换信息
                assetReplacements.push({
                    original: fileName,
                    cdn: cdnUrl,
                    type: getAssetType(fileName),
                    size: getAssetSize(asset)
                });
                // 更新HTML文件中的引用
                updateHtmlReferences(bundle, fileName, cdnUrl);
                // 更新CSS文件中的引用
                updateCssReferences(bundle, fileName, cdnUrl);
                // 更新JS文件中的引用
                updateJsReferences(bundle, fileName, cdnUrl);
            }
            // 生成资源映射文件
            generateAssetManifest(bundle, assetReplacements);
            // 生成预加载提示
            generatePreloadHints(bundle, assetReplacements);
            console.log("\u2705 CDN processing completed - ".concat(assetReplacements.length, " assets processed"));
        },
        writeBundle: function () {
            if (assetReplacements.length > 0) {
                console.log('📊 CDN Asset Replacement Summary:');
                console.table(assetReplacements.map(function (r) { return ({
                    File: r.original,
                    Type: r.type,
                    'CDN URL': r.cdn.substring(0, 50) + (r.cdn.length > 50 ? '...' : ''),
                    'Size (KB)': r.size ? Math.round(r.size / 1024) : 'N/A'
                }); }));
            }
        }
    };
}
/**
 * 检查资源是否应该包含在CDN中
 */
function shouldIncludeAsset(fileName, options) {
    // 检查排除列表
    if (options.exclude) {
        for (var _i = 0, _a = options.exclude; _i < _a.length; _i++) {
            var pattern = _a[_i];
            if (fileName.match(new RegExp(pattern))) {
                return false;
            }
        }
    }
    // 检查包含列表
    if (options.include) {
        for (var _b = 0, _c = options.include; _b < _c.length; _b++) {
            var pattern = _c[_b];
            if (fileName.match(new RegExp(pattern))) {
                return true;
            }
        }
        return false;
    }
    // 默认包含静态资源
    return /\.(js|css|png|jpe?g|gif|svg|woff2?|ttf|eot|ico|webp|mp4|webm|ogg|mp3|wav)$/i.test(fileName);
}
/**
 * 获取资源版本号
 */
function getAssetVersion(fileName, options) {
    var _a;
    if (!((_a = options.versioning) === null || _a === void 0 ? void 0 : _a.enabled)) {
        return undefined;
    }
    var versionKey = options.versioning.versionKey || 'version';
    switch (options.versioning.format) {
        case 'hash':
            // 从文件名中提取hash
            var hashMatch = fileName.match(/-([a-f0-9]{8,})\./);
            return hashMatch ? hashMatch[1].substring(0, 8) : undefined;
        case 'timestamp':
            return Date.now().toString();
        case 'semver':
            return process.env.npm_package_version || '1.0.0';
        default:
            return undefined;
    }
}
/**
 * 获取资源类型
 */
function getAssetType(fileName) {
    var _a;
    var ext = (_a = fileName.split('.').pop()) === null || _a === void 0 ? void 0 : _a.toLowerCase();
    switch (ext) {
        case 'js':
            return 'script';
        case 'css':
            return 'stylesheet';
        case 'png':
        case 'jpg':
        case 'jpeg':
        case 'gif':
        case 'svg':
        case 'webp':
            return 'image';
        case 'woff':
        case 'woff2':
        case 'ttf':
        case 'eot':
            return 'font';
        case 'mp4':
        case 'webm':
        case 'ogg':
            return 'video';
        case 'mp3':
        case 'wav':
        case 'flac':
            return 'audio';
        default:
            return 'resource';
    }
}
/**
 * 获取资源大小
 */
function getAssetSize(asset) {
    if (asset.type === 'asset' && asset.source) {
        return asset.source.length;
    }
    else if (asset.type === 'chunk' && asset.code) {
        return asset.code.length;
    }
    return undefined;
}
/**
 * 更新HTML文件中的资源引用
 */
function updateHtmlReferences(bundle, fileName, cdnUrl) {
    Object.keys(bundle).forEach(function (key) {
        var asset = bundle[key];
        if (asset.type === 'asset' && key.endsWith('.html')) {
            var html = asset.source.toString();
            // 替换脚本标签
            html = html.replace(new RegExp("<script[^>]*src=\"[^\"]*".concat(fileName, "\"[^>]*>"), 'g'), function (match) { return match.replace(fileName, cdnUrl); });
            // 替换样式表链接
            html = html.replace(new RegExp("<link[^>]*href=\"[^\"]*".concat(fileName, "\"[^>]*>"), 'g'), function (match) { return match.replace(fileName, cdnUrl); });
            // 替换图片源
            html = html.replace(new RegExp("<img[^>]*src=\"[^\"]*".concat(fileName, "\"[^>]*>"), 'g'), function (match) { return match.replace(fileName, cdnUrl); });
            asset.source = html;
        }
    });
}
/**
 * 更新CSS文件中的资源引用
 */
function updateCssReferences(bundle, fileName, cdnUrl) {
    Object.keys(bundle).forEach(function (key) {
        var asset = bundle[key];
        if ((asset.type === 'asset' && key.endsWith('.css')) ||
            (asset.type === 'chunk' && asset.code)) {
            var content = asset.type === 'asset' ? asset.source.toString() : asset.code;
            // 替换URL引用
            content = content.replace(new RegExp("url\\([^)]*".concat(fileName, "[^)]*\\)"), 'g'), function (match) { return match.replace(fileName, cdnUrl); });
            if (asset.type === 'asset') {
                asset.source = content;
            }
            else {
                asset.code = content;
            }
        }
    });
}
/**
 * 更新JS文件中的资源引用
 */
function updateJsReferences(bundle, fileName, cdnUrl) {
    Object.keys(bundle).forEach(function (key) {
        var asset = bundle[key];
        if (asset.type === 'chunk') {
            // 替换动态导入和资源引用
            asset.code = asset.code.replace(new RegExp("[\"'`][^\"'`]*".concat(fileName, "[^\"'`]*[\"'`]"), 'g'), function (match) { return match.replace(fileName, cdnUrl); });
        }
    });
}
/**
 * 生成资源映射文件
 */
function generateAssetManifest(bundle, replacements) {
    var manifest = {
        version: process.env.npm_package_version || '1.0.0',
        timestamp: new Date().toISOString(),
        cdnProvider: 'configured',
        assets: replacements.reduce(function (acc, replacement) {
            acc[replacement.original] = {
                cdn: replacement.cdn,
                type: replacement.type,
                size: replacement.size
            };
            return acc;
        }, {})
    };
    bundle['cdn-manifest.json'] = {
        type: 'asset',
        name: undefined,
        source: JSON.stringify(manifest, null, 2),
        fileName: 'cdn-manifest.json'
    };
}
/**
 * 生成预加载提示
 */
function generatePreloadHints(bundle, replacements) {
    // 找到主HTML文件
    var htmlFiles = Object.keys(bundle).filter(function (key) {
        return bundle[key].type === 'asset' && key.endsWith('.html');
    });
    htmlFiles.forEach(function (htmlFile) {
        var asset = bundle[htmlFile];
        var html = asset.source.toString();
        // 生成关键资源的预加载链接
        var criticalAssets = replacements.filter(function (r) {
            return r.type === 'script' || r.type === 'stylesheet';
        }).slice(0, 5); // 限制预加载数量
        var preloadLinks = criticalAssets.map(function (asset) {
            return "  <link rel=\"preload\" href=\"".concat(asset.cdn, "\" as=\"").concat(asset.type === 'script' ? 'script' : 'style', "\" crossorigin>");
        }).join('\n');
        if (preloadLinks && html.includes('<head>')) {
            html = html.replace('<head>', "<head>\n".concat(preloadLinks));
            asset.source = html;
        }
    });
}
/**
 * 创建CDN回退机制
 */
export function createCDNFallback(originalUrl, cdnUrl) {
    return "\n// CDN fallback for ".concat(originalUrl, "\n(function() {\n  function loadFallback() {\n    var script = document.createElement('script');\n    script.src = '").concat(originalUrl, "';\n    script.onerror = function() {\n      console.error('Failed to load both CDN and fallback for ").concat(originalUrl, "');\n    };\n    document.head.appendChild(script);\n  }\n  \n  var script = document.createElement('script');\n  script.src = '").concat(cdnUrl, "';\n  script.onerror = loadFallback;\n  script.onload = function() {\n    // CDN loaded successfully\n  };\n  document.head.appendChild(script);\n})();\n  ").trim();
}
export default cdnPlugin;
