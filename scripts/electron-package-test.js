#!/usr/bin/env node

const fs = require('fs')
const path = require('path')
const { execSync, spawn } = require('child_process')

/**
 * Electron应用打包测试和验证工具
 */
class ElectronPackageTest {
  constructor() {
    this.projectRoot = process.cwd()
    this.distElectronPath = path.join(this.projectRoot, 'dist-electron')
    this.distPath = path.join(this.projectRoot, 'dist')
    this.distPackagesPath = path.join(this.projectRoot, 'dist-packages')
    this.buildPath = path.join(this.projectRoot, 'build')
  }

  /**
   * 运行完整的打包测试流程
   */
  async runFullTest() {
    console.log('🔬 开始Electron应用打包测试\n')

    try {
      // 1. 预检查
      await this.preCheck()
      
      // 2. 清理环境
      await this.cleanup()
      
      // 3. 生成资源文件
      await this.generateResources()
      
      // 4. 构建React应用
      await this.buildReactApp()
      
      // 5. 构建Electron主进程
      await this.buildElectronMain()
      
      // 6. 验证构建结果
      await this.validateBuild()
      
      // 7. 快速打包测试
      await this.quickPackageTest()
      
      // 8. 完整打包（可选）
      const shouldFullPackage = process.argv.includes('--full')
      if (shouldFullPackage) {
        await this.fullPackageTest()
      }
      
      console.log('\n✅ Electron打包测试完成!')
      
    } catch (error) {
      console.error('\n❌ 打包测试失败:', error.message)
      process.exit(1)
    }
  }

  /**
   * 预检查环境和依赖
   */
  async preCheck() {
    console.log('1️⃣ 检查环境和依赖...')
    
    // 检查Node.js版本
    const nodeVersion = process.version
    console.log(`   Node.js版本: ${nodeVersion}`)
    
    // 检查npm版本
    try {
      const npmVersion = execSync('npm --version', { encoding: 'utf8' }).trim()
      console.log(`   npm版本: ${npmVersion}`)
    } catch (error) {
      throw new Error('npm未安装或无法访问')
    }
    
    // 检查Electron依赖
    const packageJson = require(path.join(this.projectRoot, 'package.json'))
    const electronVersion = packageJson.devDependencies?.electron
    if (!electronVersion) {
      throw new Error('Electron依赖未安装')
    }
    console.log(`   Electron版本: ${electronVersion}`)
    
    // 检查electron-builder依赖
    const electronBuilderVersion = packageJson.devDependencies?.[`electron-builder`]
    if (!electronBuilderVersion) {
      throw new Error('electron-builder依赖未安装')
    }
    console.log(`   electron-builder版本: ${electronBuilderVersion}`)
    
    // 检查必要文件
    const requiredFiles = [
      'electron/main.ts',
      'electron/preload.ts',
      'electron-builder.config.js',
      'src/App.tsx'
    ]
    
    for (const file of requiredFiles) {
      const filePath = path.join(this.projectRoot, file)
      if (!fs.existsSync(filePath)) {
        throw new Error(`必要文件缺失: ${file}`)
      }
    }
    
    console.log('   ✅ 环境检查通过')
  }

  /**
   * 清理构建目录
   */
  async cleanup() {
    console.log('\n2️⃣ 清理构建目录...')
    
    const dirsToClean = [
      this.distElectronPath,
      this.distPath,
      this.distPackagesPath
    ]
    
    for (const dir of dirsToClean) {
      if (fs.existsSync(dir)) {
        fs.rmSync(dir, { recursive: true, force: true })
        console.log(`   🗑️ 已清理: ${path.relative(this.projectRoot, dir)}`)
      }
    }
    
    console.log('   ✅ 清理完成')
  }

  /**
   * 生成必要的资源文件
   */
  async generateResources() {
    console.log('\n3️⃣ 生成资源文件...')
    
    try {
      // 运行图标生成脚本
      execSync('node scripts/generate-app-icons.js', { 
        stdio: 'inherit',
        cwd: this.projectRoot 
      })
      console.log('   ✅ 资源文件生成完成')
    } catch (error) {
      throw new Error(`资源文件生成失败: ${error.message}`)
    }
  }

  /**
   * 构建React应用
   */
  async buildReactApp() {
    console.log('\n4️⃣ 构建React应用...')
    
    try {
      console.log('   📦 运行 npm run build...')
      execSync('npm run build', { 
        stdio: 'inherit',
        cwd: this.projectRoot 
      })
      
      // 验证dist目录
      if (!fs.existsSync(this.distPath)) {
        throw new Error('React应用构建失败：dist目录不存在')
      }
      
      // 检查关键文件
      const indexHtml = path.join(this.distPath, 'index.html')
      if (!fs.existsSync(indexHtml)) {
        throw new Error('React应用构建失败：index.html不存在')
      }
      
      console.log('   ✅ React应用构建完成')
    } catch (error) {
      throw new Error(`React应用构建失败: ${error.message}`)
    }
  }

  /**
   * 构建Electron主进程
   */
  async buildElectronMain() {
    console.log('\n5️⃣ 构建Electron主进程...')
    
    try {
      console.log('   🔧 编译TypeScript文件...')
      
      // 编译主进程
      execSync('npx tsc electron/main.ts --outDir dist-electron --module commonjs --target es2020 --esModuleInterop --skipLibCheck', {
        stdio: 'inherit',
        cwd: this.projectRoot
      })
      
      // 编译预加载脚本
      execSync('npx tsc electron/preload.ts --outDir dist-electron --module commonjs --target es2020 --esModuleInterop --skipLibCheck', {
        stdio: 'inherit',
        cwd: this.projectRoot
      })
      
      // 复制服务文件
      const servicesPath = path.join(this.projectRoot, 'electron', 'services')
      if (fs.existsSync(servicesPath)) {
        const distServicesPath = path.join(this.distElectronPath, 'services')
        fs.mkdirSync(distServicesPath, { recursive: true })
        
        // 编译服务文件
        execSync(`npx tsc electron/services/*.ts --outDir dist-electron/services --module commonjs --target es2020 --esModuleInterop --skipLibCheck`, {
          stdio: 'inherit',
          cwd: this.projectRoot
        })
      }
      
      console.log('   ✅ Electron主进程构建完成')
    } catch (error) {
      throw new Error(`Electron主进程构建失败: ${error.message}`)
    }
  }

  /**
   * 验证构建结果
   */
  async validateBuild() {
    console.log('\n6️⃣ 验证构建结果...')
    
    // 检查Electron构建文件
    const electronFiles = [
      path.join(this.distElectronPath, 'main.js'),
      path.join(this.distElectronPath, 'preload.js')
    ]
    
    for (const file of electronFiles) {
      if (!fs.existsSync(file)) {
        throw new Error(`Electron文件缺失: ${path.relative(this.projectRoot, file)}`)
      }
      console.log(`   ✅ ${path.relative(this.projectRoot, file)}`)
    }
    
    // 检查React构建文件
    const reactFiles = [
      path.join(this.distPath, 'index.html'),
      path.join(this.distPath, 'assets')
    ]
    
    for (const file of reactFiles) {
      if (!fs.existsSync(file)) {
        throw new Error(`React文件缺失: ${path.relative(this.projectRoot, file)}`)
      }
      console.log(`   ✅ ${path.relative(this.projectRoot, file)}`)
    }
    
    // 检查构建资源
    const buildFiles = [
      path.join(this.buildPath, 'icon.ico'),
      path.join(this.buildPath, 'icon.icns'),
      path.join(this.buildPath, 'entitlements.mac.plist')
    ]
    
    for (const file of buildFiles) {
      if (fs.existsSync(file)) {
        console.log(`   ✅ ${path.relative(this.projectRoot, file)}`)
      }
    }
    
    console.log('   ✅ 构建验证完成')
  }

  /**
   * 快速打包测试（不进行代码签名）
   */
  async quickPackageTest() {
    console.log('\n7️⃣ 快速打包测试...')
    
    try {
      console.log('   📦 运行electron-builder（跳过签名）...')
      
      // 设置环境变量跳过签名
      const env = { ...process.env, NODE_ENV: 'development' }
      
      // 只为当前平台打包
      const platform = process.platform === 'win32' ? '--win' :
                      process.platform === 'darwin' ? '--mac' : '--linux'
      
      execSync(`npx electron-builder ${platform} --publish=never`, {
        stdio: 'inherit',
        cwd: this.projectRoot,
        env
      })
      
      console.log('   ✅ 快速打包完成')
      
      // 列出生成的文件
      this.listPackageOutput()
      
    } catch (error) {
      throw new Error(`快速打包失败: ${error.message}`)
    }
  }

  /**
   * 完整打包测试（包含代码签名）
   */
  async fullPackageTest() {
    console.log('\n8️⃣ 完整打包测试...')
    
    try {
      console.log('   📦 运行完整打包（包含签名）...')
      
      // 设置生产环境
      const env = { ...process.env, NODE_ENV: 'production' }
      
      execSync('npx electron-builder --publish=never', {
        stdio: 'inherit',
        cwd: this.projectRoot,
        env
      })
      
      console.log('   ✅ 完整打包完成')
      
      // 列出生成的文件
      this.listPackageOutput()
      
    } catch (error) {
      console.warn(`⚠️ 完整打包失败（可能是签名配置问题）: ${error.message}`)
    }
  }

  /**
   * 列出打包输出文件
   */
  listPackageOutput() {
    console.log('\n📋 打包输出文件:')
    
    if (!fs.existsSync(this.distPackagesPath)) {
      console.log('   📁 无打包输出文件')
      return
    }
    
    const files = fs.readdirSync(this.distPackagesPath)
    files.forEach(file => {
      const filePath = path.join(this.distPackagesPath, file)
      const stats = fs.statSync(filePath)
      const sizeInMB = (stats.size / (1024 * 1024)).toFixed(2)
      console.log(`   📦 ${file} (${sizeInMB} MB)`)
    })
  }

  /**
   * 启动Electron开发模式测试
   */
  async devModeTest() {
    console.log('\n🔧 Electron开发模式测试...')
    
    try {
      console.log('   启动开发服务器...')
      
      // 启动Vite开发服务器
      const viteProcess = spawn('npm', ['run', 'dev'], {
        stdio: 'pipe',
        cwd: this.projectRoot
      })
      
      // 等待服务器启动
      await new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          viteProcess.kill()
          reject(new Error('Vite服务器启动超时'))
        }, 30000)
        
        viteProcess.stdout.on('data', (data) => {
          const output = data.toString()
          if (output.includes('Local:') || output.includes('localhost')) {
            clearTimeout(timeout)
            resolve()
          }
        })
        
        viteProcess.stderr.on('data', (data) => {
          console.error('Vite错误:', data.toString())
        })
      })
      
      console.log('   启动Electron应用...')
      
      // 启动Electron
      const electronProcess = spawn('npx', ['electron', '.'], {
        stdio: 'inherit',
        cwd: this.projectRoot
      })
      
      // 等待用户关闭应用
      await new Promise((resolve) => {
        electronProcess.on('close', () => {
          viteProcess.kill()
          resolve()
        })
        
        // 10秒后自动关闭（自动化测试）
        if (process.argv.includes('--auto')) {
          setTimeout(() => {
            electronProcess.kill()
            viteProcess.kill()
            resolve()
          }, 10000)
        }
      })
      
      console.log('   ✅ 开发模式测试完成')
      
    } catch (error) {
      throw new Error(`开发模式测试失败: ${error.message}`)
    }
  }

  /**
   * 生成打包报告
   */
  generateReport() {
    console.log('\n📊 生成打包报告...')
    
    const report = {
      timestamp: new Date().toISOString(),
      platform: process.platform,
      arch: process.arch,
      nodeVersion: process.version,
      electronVersion: require(path.join(this.projectRoot, 'package.json')).devDependencies?.electron,
      buildStatus: {
        reactApp: fs.existsSync(this.distPath),
        electronMain: fs.existsSync(path.join(this.distElectronPath, 'main.js')),
        buildResources: fs.existsSync(this.buildPath),
        packageOutput: fs.existsSync(this.distPackagesPath)
      },
      outputFiles: []
    }
    
    if (fs.existsSync(this.distPackagesPath)) {
      const files = fs.readdirSync(this.distPackagesPath)
      report.outputFiles = files.map(file => {
        const filePath = path.join(this.distPackagesPath, file)
        const stats = fs.statSync(filePath)
        return {
          name: file,
          size: stats.size,
          sizeInMB: (stats.size / (1024 * 1024)).toFixed(2)
        }
      })
    }
    
    const reportPath = path.join(this.projectRoot, 'electron-package-report.json')
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2))
    
    console.log(`   📋 报告已保存: ${reportPath}`)
  }
}

// CLI界面
async function main() {
  const tester = new ElectronPackageTest()
  const command = process.argv[2]
  
  try {
    switch (command) {
      case 'full':
        await tester.runFullTest()
        break
        
      case 'dev':
        await tester.devModeTest()
        break
        
      case 'build':
        await tester.preCheck()
        await tester.cleanup()
        await tester.generateResources()
        await tester.buildReactApp()
        await tester.buildElectronMain()
        await tester.validateBuild()
        break
        
      case 'package':
        await tester.quickPackageTest()
        break
        
      case 'report':
        tester.generateReport()
        break
        
      default:
        console.log(`
🔬 Electron打包测试工具

使用方法:
  node scripts/electron-package-test.js <command> [options]

命令:
  full        运行完整测试流程（构建+打包）
  build       仅运行构建测试
  package     仅运行打包测试
  dev         启动开发模式测试
  report      生成打包报告

选项:
  --full      运行完整打包（包含代码签名）
  --auto      自动化测试（开发模式10秒后自动关闭）

示例:
  npm run electron:test:full
  npm run electron:test:dev
  npm run electron:test:build
        `)
    }
  } catch (error) {
    console.error('❌ 测试失败:', error.message)
    process.exit(1)
  }
}

if (require.main === module) {
  main()
}

module.exports = ElectronPackageTest 