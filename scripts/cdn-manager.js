#!/usr/bin/env node

const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')
const fetch = require('node-fetch').default || require('node-fetch')

// CDN配置
const CDN_CONFIGS = {
  jsdelivr: {
    name: 'jsDelivr',
    baseUrl: 'https://cdn.jsdelivr.net',
    uploadCommand: null, // jsDelivr通过npm发布自动同步
    testUrl: 'https://cdn.jsdelivr.net/npm/selfgame@latest/',
    features: ['free', 'global', 'npm-sync']
  },
  cloudflare: {
    name: 'Cloudflare',
    baseUrl: 'https://your-zone.cloudflareaccess.com',
    uploadCommand: 'wrangler publish',
    testUrl: 'https://your-zone.cloudflareaccess.com/',
    features: ['paid', 'custom-domain', 'analytics']
  },
  aws: {
    name: 'AWS CloudFront',
    baseUrl: 'https://d123456789.cloudfront.net',
    uploadCommand: 'aws s3 sync dist/ s3://your-bucket --delete',
    testUrl: 'https://d123456789.cloudfront.net/',
    features: ['paid', 'global', 'edge-locations']
  }
}

class CDNManager {
  constructor() {
    this.distPath = path.join(process.cwd(), 'dist')
    this.configPath = path.join(process.cwd(), 'config', 'cdn.config.ts')
    this.manifestPath = path.join(this.distPath, 'cdn-manifest.json')
  }

  /**
   * 显示CDN状态
   */
  async status() {
    console.log('🔍 CDN Status Check\n')
    
    // 检查构建产物
    const distExists = fs.existsSync(this.distPath)
    console.log(`📦 Build Output: ${distExists ? '✅ Available' : '❌ Missing'}`)
    
    if (!distExists) {
      console.log('💡 Run "npm run build" first to generate build output')
      return
    }

    // 检查CDN配置
    const configExists = fs.existsSync(this.configPath)
    console.log(`⚙️  CDN Config: ${configExists ? '✅ Available' : '❌ Missing'}`)
    
    // 检查资源清单
    const manifestExists = fs.existsSync(this.manifestPath)
    console.log(`📋 Asset Manifest: ${manifestExists ? '✅ Available' : '❌ Missing'}`)
    
    if (manifestExists) {
      const manifest = JSON.parse(fs.readFileSync(this.manifestPath, 'utf8'))
      console.log(`📊 Assets: ${Object.keys(manifest.assets || {}).length} files mapped`)
      console.log(`🏷️  Version: ${manifest.version}`)
      console.log(`⏰ Generated: ${new Date(manifest.timestamp).toLocaleString()}`)
    }

    console.log('\n🌐 CDN Providers:')
    for (const [key, config] of Object.entries(CDN_CONFIGS)) {
      console.log(`   ${config.name}: ${config.features.join(', ')}`)
    }
  }

  /**
   * 测试CDN连接
   */
  async test(provider = 'jsdelivr') {
    console.log(`🧪 Testing CDN: ${CDN_CONFIGS[provider]?.name || provider}\n`)
    
    const config = CDN_CONFIGS[provider]
    if (!config) {
      console.error(`❌ Unknown CDN provider: ${provider}`)
      return false
    }

    const testResults = []
    
    // 测试基本连接
    console.log('1️⃣ Testing basic connectivity...')
    try {
      const start = Date.now()
      const response = await fetch(config.testUrl, { method: 'HEAD' })
      const latency = Date.now() - start
      
      testResults.push({
        test: 'Basic Connectivity',
        status: response.ok ? 'PASS' : 'FAIL',
        details: `${response.status} (${latency}ms)`
      })
      
      console.log(`   ${response.ok ? '✅' : '❌'} HTTP ${response.status} in ${latency}ms`)
    } catch (error) {
      testResults.push({
        test: 'Basic Connectivity',
        status: 'ERROR',
        details: error.message
      })
      console.log(`   ❌ Connection failed: ${error.message}`)
    }

    // 测试资源加载
    if (fs.existsSync(this.manifestPath)) {
      console.log('\n2️⃣ Testing asset loading...')
      const manifest = JSON.parse(fs.readFileSync(this.manifestPath, 'utf8'))
      const assetKeys = Object.keys(manifest.assets || {}).slice(0, 3) // 测试前3个资源
      
      for (const assetKey of assetKeys) {
        const asset = manifest.assets[assetKey]
        try {
          const start = Date.now()
          const response = await fetch(asset.cdn, { method: 'HEAD' })
          const latency = Date.now() - start
          
          testResults.push({
            test: `Asset: ${assetKey}`,
            status: response.ok ? 'PASS' : 'FAIL',
            details: `${response.status} (${latency}ms)`
          })
          
          console.log(`   ${response.ok ? '✅' : '❌'} ${assetKey}: ${response.status} in ${latency}ms`)
        } catch (error) {
          testResults.push({
            test: `Asset: ${assetKey}`,
            status: 'ERROR',
            details: error.message
          })
          console.log(`   ❌ ${assetKey}: ${error.message}`)
        }
      }
    }

    // 测试地理位置性能
    console.log('\n3️⃣ Testing geographic performance...')
    const testUrls = [
      { region: 'Global', url: config.testUrl },
      { region: 'US East', url: config.testUrl.replace('://', '://us-east.') },
      { region: 'EU West', url: config.testUrl.replace('://', '://eu-west.') },
      { region: 'Asia Pacific', url: config.testUrl.replace('://', '://ap-southeast.') }
    ]

    for (const { region, url } of testUrls) {
      try {
        const start = Date.now()
        const response = await fetch(url, { 
          method: 'HEAD',
          timeout: 5000 
        }).catch(() => ({ ok: false, status: 'TIMEOUT' }))
        const latency = Date.now() - start
        
        testResults.push({
          test: `Geographic: ${region}`,
          status: response.ok ? 'PASS' : 'FAIL',
          details: `${latency}ms`
        })
        
        if (latency < 5000) {
          console.log(`   ${response.ok ? '✅' : '❌'} ${region}: ${latency}ms`)
        }
      } catch (error) {
        console.log(`   ⏭️  ${region}: Skipped (${error.message})`)
      }
    }

    // 生成测试报告
    console.log('\n📊 Test Summary:')
    console.table(testResults)
    
    const passCount = testResults.filter(r => r.status === 'PASS').length
    const totalCount = testResults.length
    const successRate = Math.round((passCount / totalCount) * 100)
    
    console.log(`\n🎯 Success Rate: ${passCount}/${totalCount} (${successRate}%)`)
    
    return successRate >= 80
  }

  /**
   * 部署到CDN
   */
  async deploy(provider = 'jsdelivr') {
    console.log(`🚀 Deploying to CDN: ${CDN_CONFIGS[provider]?.name || provider}\n`)
    
    const config = CDN_CONFIGS[provider]
    if (!config) {
      console.error(`❌ Unknown CDN provider: ${provider}`)
      return false
    }

    // 检查构建产物
    if (!fs.existsSync(this.distPath)) {
      console.error('❌ Build output not found. Run "npm run build" first.')
      return false
    }

    try {
      switch (provider) {
        case 'jsdelivr':
          return await this.deployToJsDelivr()
        case 'cloudflare':
          return await this.deployToCloudflare()
        case 'aws':
          return await this.deployToAWS()
        default:
          console.error(`❌ Deployment not implemented for ${provider}`)
          return false
      }
    } catch (error) {
      console.error(`❌ Deployment failed: ${error.message}`)
      return false
    }
  }

  /**
   * 部署到jsDelivr (通过npm发布)
   */
  async deployToJsDelivr() {
    console.log('📦 Deploying to jsDelivr via npm...')
    
    // 检查npm登录状态
    try {
      execSync('npm whoami', { stdio: 'pipe' })
    } catch (error) {
      console.error('❌ Not logged in to npm. Run "npm login" first.')
      return false
    }

    // 创建发布目录
    const publishDir = path.join(process.cwd(), 'npm-publish')
    if (fs.existsSync(publishDir)) {
      fs.rmSync(publishDir, { recursive: true })
    }
    fs.mkdirSync(publishDir)

    // 复制构建产物
    execSync(`cp -r ${this.distPath}/* ${publishDir}/`)
    
    // 创建package.json
    const packageJson = {
      name: 'selfgame',
      version: process.env.npm_package_version || '1.0.0',
      description: '自律农场 - 通过虚拟农场培养自律习惯的应用',
      main: 'index.html',
      files: ['**/*'],
      repository: {
        type: 'git',
        url: 'https://github.com/yourusername/selfgame.git'
      },
      keywords: ['self-discipline', 'farm', 'productivity', 'game'],
      license: 'MIT'
    }
    
    fs.writeFileSync(
      path.join(publishDir, 'package.json'),
      JSON.stringify(packageJson, null, 2)
    )

    // 发布到npm
    console.log('📤 Publishing to npm...')
    try {
      execSync('npm publish', { cwd: publishDir, stdio: 'inherit' })
      console.log('✅ Successfully published to npm')
      console.log(`🌐 CDN URL: https://cdn.jsdelivr.net/npm/selfgame@${packageJson.version}/`)
      return true
    } catch (error) {
      console.error('❌ npm publish failed')
      return false
    } finally {
      // 清理临时目录
      fs.rmSync(publishDir, { recursive: true })
    }
  }

  /**
   * 部署到Cloudflare
   */
  async deployToCloudflare() {
    console.log('☁️ Deploying to Cloudflare...')
    
    // 检查wrangler CLI
    try {
      execSync('wrangler --version', { stdio: 'pipe' })
    } catch (error) {
      console.error('❌ Wrangler CLI not found. Install with "npm install -g @cloudflare/wrangler"')
      return false
    }

    // 检查wrangler.toml配置
    const wranglerConfig = path.join(process.cwd(), 'wrangler.toml')
    if (!fs.existsSync(wranglerConfig)) {
      console.log('📝 Creating wrangler.toml...')
      const config = `
name = "selfgame-cdn"
main = "dist/index.html"
compatibility_date = "2023-12-01"

[site]
bucket = "./dist"

[build]
command = "npm run build"
[build.upload]
format = "modules"
      `.trim()
      
      fs.writeFileSync(wranglerConfig, config)
    }

    // 部署
    try {
      execSync('wrangler publish', { stdio: 'inherit' })
      console.log('✅ Successfully deployed to Cloudflare')
      return true
    } catch (error) {
      console.error('❌ Cloudflare deployment failed')
      return false
    }
  }

  /**
   * 部署到AWS CloudFront
   */
  async deployToAWS() {
    console.log('🔶 Deploying to AWS CloudFront...')
    
    // 检查AWS CLI
    try {
      execSync('aws --version', { stdio: 'pipe' })
    } catch (error) {
      console.error('❌ AWS CLI not found. Install from https://aws.amazon.com/cli/')
      return false
    }

    // 检查AWS配置
    try {
      execSync('aws sts get-caller-identity', { stdio: 'pipe' })
    } catch (error) {
      console.error('❌ AWS not configured. Run "aws configure" first.')
      return false
    }

    const bucketName = process.env.AWS_S3_BUCKET || 'selfgame-cdn'
    
    // 同步到S3
    try {
      console.log(`📤 Syncing to S3 bucket: ${bucketName}`)
      execSync(`aws s3 sync ${this.distPath}/ s3://${bucketName} --delete`, { stdio: 'inherit' })
      
      // 创建CloudFront失效
      const distributionId = process.env.AWS_CLOUDFRONT_DISTRIBUTION_ID
      if (distributionId) {
        console.log('🔄 Creating CloudFront invalidation...')
        execSync(`aws cloudfront create-invalidation --distribution-id ${distributionId} --paths "/*"`, { stdio: 'inherit' })
      }
      
      console.log('✅ Successfully deployed to AWS')
      return true
    } catch (error) {
      console.error('❌ AWS deployment failed')
      return false
    }
  }

  /**
   * 监控CDN性能
   */
  async monitor(provider = 'jsdelivr', duration = 60) {
    console.log(`📊 Monitoring CDN performance for ${duration} seconds...\n`)
    
    const config = CDN_CONFIGS[provider]
    if (!config) {
      console.error(`❌ Unknown CDN provider: ${provider}`)
      return
    }

    const startTime = Date.now()
    const endTime = startTime + (duration * 1000)
    const metrics = []

    while (Date.now() < endTime) {
      try {
        const testStart = Date.now()
        const response = await fetch(config.testUrl, { method: 'HEAD' })
        const latency = Date.now() - testStart
        
        metrics.push({
          timestamp: new Date().toISOString(),
          latency,
          status: response.status,
          success: response.ok
        })

        process.stdout.write(`⏱️  ${latency}ms | ${response.ok ? '✅' : '❌'} | ${new Date().toLocaleTimeString()}\r`)
        
        await new Promise(resolve => setTimeout(resolve, 5000)) // 5秒间隔
      } catch (error) {
        metrics.push({
          timestamp: new Date().toISOString(),
          latency: -1,
          status: 'ERROR',
          success: false,
          error: error.message
        })
      }
    }

    // 生成监控报告
    console.log('\n\n📈 Performance Report:')
    
    const successfulRequests = metrics.filter(m => m.success)
    const failedRequests = metrics.filter(m => !m.success)
    
    if (successfulRequests.length > 0) {
      const latencies = successfulRequests.map(m => m.latency)
      const avgLatency = latencies.reduce((a, b) => a + b, 0) / latencies.length
      const minLatency = Math.min(...latencies)
      const maxLatency = Math.max(...latencies)
      
      console.log(`📊 Requests: ${metrics.length} total, ${successfulRequests.length} successful, ${failedRequests.length} failed`)
      console.log(`⚡ Latency: ${Math.round(avgLatency)}ms avg, ${minLatency}ms min, ${maxLatency}ms max`)
      console.log(`📈 Success Rate: ${Math.round((successfulRequests.length / metrics.length) * 100)}%`)
    } else {
      console.log('❌ No successful requests recorded')
    }

    // 保存详细报告
    const reportPath = path.join(process.cwd(), 'cdn-performance-report.json')
    fs.writeFileSync(reportPath, JSON.stringify(metrics, null, 2))
    console.log(`📋 Detailed report saved to: ${reportPath}`)
  }

  /**
   * 清理CDN缓存
   */
  async purge(provider = 'cloudflare') {
    console.log(`🧹 Purging CDN cache: ${CDN_CONFIGS[provider]?.name || provider}`)
    
    switch (provider) {
      case 'cloudflare':
        try {
          execSync('wrangler kv:namespace delete --preview false', { stdio: 'inherit' })
          console.log('✅ Cloudflare cache purged')
        } catch (error) {
          console.error('❌ Cache purge failed')
        }
        break
        
      case 'aws':
        const distributionId = process.env.AWS_CLOUDFRONT_DISTRIBUTION_ID
        if (distributionId) {
          try {
            execSync(`aws cloudfront create-invalidation --distribution-id ${distributionId} --paths "/*"`, { stdio: 'inherit' })
            console.log('✅ AWS CloudFront cache purged')
          } catch (error) {
            console.error('❌ Cache purge failed')
          }
        } else {
          console.error('❌ AWS_CLOUDFRONT_DISTRIBUTION_ID not set')
        }
        break
        
      default:
        console.log('💡 Cache purging not available for this provider')
    }
  }
}

// CLI界面
async function main() {
  const command = process.argv[2]
  const provider = process.argv[3]
  const manager = new CDNManager()

  switch (command) {
    case 'status':
      await manager.status()
      break
      
    case 'test':
      await manager.test(provider)
      break
      
    case 'deploy':
      await manager.deploy(provider)
      break
      
    case 'monitor':
      const duration = parseInt(process.argv[4]) || 60
      await manager.monitor(provider, duration)
      break
      
    case 'purge':
      await manager.purge(provider)
      break
      
    default:
      console.log(`
🌐 CDN Manager for SelfGame

Usage:
  node scripts/cdn-manager.js <command> [provider] [options]

Commands:
  status                    Show CDN status and configuration
  test [provider]          Test CDN connectivity and performance
  deploy [provider]        Deploy to CDN (default: jsdelivr)
  monitor [provider] [sec] Monitor CDN performance (default: 60s)
  purge [provider]         Purge CDN cache

Providers:
  jsdelivr                 jsDelivr (free, via npm)
  cloudflare              Cloudflare (requires account)
  aws                     AWS CloudFront (requires account)

Examples:
  npm run cdn:status
  npm run cdn:test jsdelivr
  npm run cdn:deploy cloudflare
  npm run cdn:monitor aws 120
      `)
  }
}

if (require.main === module) {
  main().catch(console.error)
}

module.exports = CDNManager 