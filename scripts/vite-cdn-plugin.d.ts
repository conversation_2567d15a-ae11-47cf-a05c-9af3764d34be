import { Plugin } from 'vite';
import { CDNConfig } from '../config/cdn.config';
interface CDNPluginOptions {
    config?: CDNConfig;
    environment?: string;
    include?: string[];
    exclude?: string[];
    fallback?: boolean;
    versioning?: {
        enabled: boolean;
        versionKey?: string;
        format?: 'hash' | 'timestamp' | 'semver';
    };
}
/**
 * Vite CDN插件
 * 在构建时自动将静态资源URL替换为CDN URL
 */
export declare function cdnPlugin(options?: CDNPluginOptions): Plugin;
/**
 * 创建CDN回退机制
 */
export declare function createCDNFallback(originalUrl: string, cdnUrl: string): string;
export default cdnPlugin;
