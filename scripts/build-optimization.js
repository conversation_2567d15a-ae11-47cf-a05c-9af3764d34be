#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');
const { promisify } = require('util');

const execAsync = promisify(exec);

class BuildOptimizer {
  constructor() {
    this.projectRoot = process.cwd();
    this.distPath = path.join(this.projectRoot, 'dist');
    this.analysisResults = {};
  }

  async run() {
    console.log('🚀 开始Web应用部署优化...\n');
    
    try {
      // 1. 清理旧构建
      await this.cleanBuild();
      
      // 2. 运行生产构建
      await this.buildProduction();
      
      // 3. 分析构建结果
      await this.analyzeBuild();
      
      // 4. 生成优化报告
      await this.generateReport();
      
      // 5. 提供优化建议
      this.provideOptimizations();
      
    } catch (error) {
      console.error('❌ 优化过程中出现错误:', error.message);
      process.exit(1);
    }
  }

  async cleanBuild() {
    console.log('🧹 清理旧构建文件...');
    
    if (fs.existsSync(this.distPath)) {
      await execAsync(`rimraf ${this.distPath}`);
      console.log('✅ 清理完成');
    } else {
      console.log('ℹ️  无需清理，未找到旧构建文件');
    }
  }

  async buildProduction() {
    console.log('🔨 执行生产构建...');
    
    try {
      const { stdout } = await execAsync('npm run build');
      console.log('✅ 构建完成');
      
      // 检查是否启用了构建分析
      if (process.env.ANALYZE) {
        console.log('📊 构建分析已启用，报告将在构建完成后打开');
      }
    } catch (error) {
      // 即使有错误也继续，因为可能是TypeScript错误但JS仍能构建
      console.log('⚠️  构建过程中有错误，但继续分析已生成的文件...');
    }
  }

  async analyzeBuild() {
    console.log('📊 分析构建结果...');
    
    if (!fs.existsSync(this.distPath)) {
      throw new Error('构建目录不存在，构建可能失败');
    }

    // 分析文件大小
    this.analysisResults.fileAnalysis = await this.analyzeFiles();
    
    // 分析资源类型
    this.analysisResults.assetAnalysis = await this.analyzeAssets();
    
    // 分析代码分割效果
    this.analysisResults.chunkAnalysis = await this.analyzeChunks();
    
    console.log('✅ 分析完成');
  }

  async analyzeFiles() {
    const analysis = {
      totalSize: 0,
      compressedSize: 0,
      fileCount: 0,
      largeFiles: []
    };

    const walkDir = (dir) => {
      const files = fs.readdirSync(dir);
      
      files.forEach(file => {
        const filePath = path.join(dir, file);
        const stat = fs.statSync(filePath);
        
        if (stat.isDirectory()) {
          walkDir(filePath);
        } else {
          analysis.fileCount++;
          analysis.totalSize += stat.size;
          
          // 检查大文件 (>500KB)
          if (stat.size > 500 * 1024) {
            analysis.largeFiles.push({
              file: path.relative(this.distPath, filePath),
              size: this.formatSize(stat.size)
            });
          }
          
          // 检查压缩文件
          if (file.endsWith('.gz') || file.endsWith('.br')) {
            analysis.compressedSize += stat.size;
          }
        }
      });
    };

    walkDir(this.distPath);
    return analysis;
  }

  async analyzeAssets() {
    const analysis = {
      js: { count: 0, size: 0 },
      css: { count: 0, size: 0 },
      images: { count: 0, size: 0 },
      media: { count: 0, size: 0 },
      fonts: { count: 0, size: 0 },
      other: { count: 0, size: 0 }
    };

    const walkDir = (dir) => {
      const files = fs.readdirSync(dir);
      
      files.forEach(file => {
        const filePath = path.join(dir, file);
        const stat = fs.statSync(filePath);
        
        if (stat.isDirectory()) {
          walkDir(filePath);
        } else {
          const ext = path.extname(file).toLowerCase();
          let category = 'other';
          
          if (['.js', '.mjs'].includes(ext)) category = 'js';
          else if (['.css'].includes(ext)) category = 'css';
          else if (['.png', '.jpg', '.jpeg', '.gif', '.svg', '.webp', '.avif'].includes(ext)) category = 'images';
          else if (['.mp4', '.webm', '.mp3', '.wav', '.ogg'].includes(ext)) category = 'media';
          else if (['.woff', '.woff2', '.ttf', '.otf', '.eot'].includes(ext)) category = 'fonts';
          
          analysis[category].count++;
          analysis[category].size += stat.size;
        }
      });
    };

    walkDir(this.distPath);
    return analysis;
  }

  async analyzeChunks() {
    const analysis = {
      entryChunks: [],
      vendorChunks: [],
      dynamicChunks: [],
      totalChunks: 0
    };

    const jsDir = path.join(this.distPath, 'js');
    if (fs.existsSync(jsDir)) {
      const files = fs.readdirSync(jsDir);
      
      files.forEach(file => {
        if (file.endsWith('.js')) {
          const filePath = path.join(jsDir, file);
          const stat = fs.statSync(filePath);
          
          const chunkInfo = {
            name: file,
            size: this.formatSize(stat.size),
            rawSize: stat.size
          };
          
          if (file.includes('vendor')) {
            analysis.vendorChunks.push(chunkInfo);
          } else if (file.includes('index') || file.includes('main')) {
            analysis.entryChunks.push(chunkInfo);
          } else {
            analysis.dynamicChunks.push(chunkInfo);
          }
          
          analysis.totalChunks++;
        }
      });
    }

    return analysis;
  }

  async generateReport() {
    console.log('📋 生成优化报告...');
    
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        totalSize: this.formatSize(this.analysisResults.fileAnalysis.totalSize),
        fileCount: this.analysisResults.fileAnalysis.fileCount,
        compressionRatio: this.calculateCompressionRatio(),
        chunkCount: this.analysisResults.chunkAnalysis.totalChunks
      },
      details: this.analysisResults,
      recommendations: this.generateRecommendations()
    };

    const reportPath = path.join(this.projectRoot, 'build-optimization-report.json');
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    
    console.log(`✅ 报告已生成: ${reportPath}`);
    return report;
  }

  calculateCompressionRatio() {
    const { totalSize, compressedSize } = this.analysisResults.fileAnalysis;
    if (compressedSize === 0) return '未启用压缩';
    
    const ratio = ((totalSize - compressedSize) / totalSize * 100).toFixed(1);
    return `${ratio}%`;
  }

  generateRecommendations() {
    const recommendations = [];
    const { fileAnalysis, assetAnalysis, chunkAnalysis } = this.analysisResults;

    // 文件大小建议
    if (fileAnalysis.totalSize > 5 * 1024 * 1024) { // 5MB
      recommendations.push({
        type: 'size',
        priority: 'high',
        message: '应用总大小较大，建议进一步优化资源',
        actions: [
          '启用更积极的代码分割',
          '移除未使用的依赖',
          '优化图片资源',
          '使用更小的替代库'
        ]
      });
    }

    // 大文件建议
    if (fileAnalysis.largeFiles.length > 0) {
      recommendations.push({
        type: 'large-files',
        priority: 'medium',
        message: '检测到大文件，可能影响加载性能',
        files: fileAnalysis.largeFiles,
        actions: [
          '考虑懒加载大文件',
          '分割大文件为多个小文件',
          '使用动态导入'
        ]
      });
    }

    // JS包大小建议
    if (assetAnalysis.js.size > 1 * 1024 * 1024) { // 1MB
      recommendations.push({
        type: 'javascript',
        priority: 'high',
        message: 'JavaScript包过大，建议优化',
        size: this.formatSize(assetAnalysis.js.size),
        actions: [
          '增加代码分割策略',
          '使用Tree Shaking移除死代码',
          '考虑使用更轻量的库替代',
          '启用更积极的压缩'
        ]
      });
    }

    // 图片优化建议
    if (assetAnalysis.images.size > 2 * 1024 * 1024) { // 2MB
      recommendations.push({
        type: 'images',
        priority: 'medium',
        message: '图片资源较大，建议优化',
        size: this.formatSize(assetAnalysis.images.size),
        actions: [
          '使用WebP格式',
          '启用图片压缩',
          '实现响应式图片',
          '考虑使用CDN'
        ]
      });
    }

    // Chunk分析建议
    const largeChunks = [...chunkAnalysis.vendorChunks, ...chunkAnalysis.entryChunks]
      .filter(chunk => chunk.rawSize > 500 * 1024); // 500KB

    if (largeChunks.length > 0) {
      recommendations.push({
        type: 'chunks',
        priority: 'medium',
        message: '存在较大的代码块，建议进一步分割',
        chunks: largeChunks,
        actions: [
          '将大的vendor库分离到独立chunk',
          '使用动态导入分割路由',
          '按功能模块分割代码'
        ]
      });
    }

    return recommendations;
  }

  provideOptimizations() {
    console.log('\n🎯 优化建议:');
    
    const recommendations = this.generateRecommendations();
    
    if (recommendations.length === 0) {
      console.log('✅ 构建已经优化得很好！');
      return;
    }

    recommendations.forEach((rec, index) => {
      console.log(`\n${index + 1}. ${rec.message} (优先级: ${rec.priority})`);
      rec.actions.forEach(action => {
        console.log(`   • ${action}`);
      });
    });

    console.log('\n📊 构建统计:');
    console.log(`   总大小: ${this.formatSize(this.analysisResults.fileAnalysis.totalSize)}`);
    console.log(`   文件数: ${this.analysisResults.fileAnalysis.fileCount}`);
    console.log(`   JS大小: ${this.formatSize(this.analysisResults.assetAnalysis.js.size)}`);
    console.log(`   CSS大小: ${this.formatSize(this.analysisResults.assetAnalysis.css.size)}`);
    console.log(`   代码块数: ${this.analysisResults.chunkAnalysis.totalChunks}`);
  }

  formatSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}

// 主执行函数
async function main() {
  const optimizer = new BuildOptimizer();
  await optimizer.run();
}

// 如果直接运行脚本
if (require.main === module) {
  main().catch(console.error);
}

module.exports = BuildOptimizer; 