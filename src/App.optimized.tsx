import React, { Suspense, lazy } from 'react'
import ErrorBoundary from './components/ErrorBoundary'
import { GameProvider } from './contexts/GameContext'
import { TutorialProvider } from './contexts/TutorialContext'
import './App.css'

// 简单的加载组件
const LoadingFallback: React.FC = () => (
  <div className="loading-fallback" style={{
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    padding: '40px',
    fontSize: '16px',
    color: '#666'
  }}>
    <div style={{
      width: '40px',
      height: '40px',
      border: '4px solid #f3f3f3',
      borderTop: '4px solid #4CAF50',
      borderRadius: '50%',
      animation: 'spin 1s linear infinite',
      marginBottom: '16px'
    }} />
    <p>正在加载组件...</p>
    <style>{`
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    `}</style>
  </div>
)

// 懒加载主要组件
const MainGameInterface = lazy(() => import('./components/MainGameInterface'))
const TutorialOverlay = lazy(() => 
  import('./components/tutorial/TutorialOverlay').then(module => ({ 
    default: module.TutorialOverlay || module.default 
  }))
)

// 主App组件
const App: React.FC = () => {
  return (
    <TutorialProvider>
      <GameProvider>
        <ErrorBoundary>
          <div className="app">
            <Suspense fallback={<LoadingFallback />}>
              <MainGameInterface />
            </Suspense>
            
            <Suspense fallback={<LoadingFallback />}>
              <TutorialOverlay />
            </Suspense>
          </div>
        </ErrorBoundary>
      </GameProvider>
    </TutorialProvider>
  )
}

export default App 