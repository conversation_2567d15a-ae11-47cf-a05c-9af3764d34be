// 数据库管理器 - 统一管理所有存储操作

import { LocalStorageAdapter, StorageAdapter } from './LocalStorageAdapter'
import { 
  UserProfile, 
  GameProgress, 
  BehaviorRecord, 
  StorageConfig,
  BackupInfo,
  DataIntegrityCheck,
  UserPreferences,
  UserStatistics
} from '../types/user'

export class DatabaseManager {
  private storage: StorageAdapter
  private config: StorageConfig
  private isInitialized: boolean = false
  private backupTimer: number | null = null

  constructor(config?: Partial<StorageConfig>) {
    this.config = {
      encryptionEnabled: false,
      compressionEnabled: true,
      backupEnabled: true,
      backupInterval: 24, // 24小时
      maxBackupFiles: 5,
      dataRetentionDays: 30,
      ...config
    }

    this.storage = new LocalStorageAdapter(this.config)
  }

  /**
   * 初始化数据库
   */
  async initialize(): Promise<boolean> {
    try {
      // 检查数据完整性（仅LocalStorageAdapter支持）
      if (this.storage instanceof LocalStorageAdapter) {
        const integrityCheck = await this.storage.checkDataIntegrity()
        if (!integrityCheck.isValid) {
          console.warn('Data integrity issues found:', integrityCheck.errors)
        }
      }

      // 启动自动备份
      if (this.config.backupEnabled) {
        this.startAutoBackup()
      }

      this.isInitialized = true
      console.log('DatabaseManager initialized successfully')
      return true
    } catch (error) {
      console.error('Failed to initialize DatabaseManager:', error)
      return false
    }
  }

  /**
   * 关闭数据库连接
   */
  async close(): Promise<void> {
    if (this.backupTimer !== null) {
      clearInterval(this.backupTimer)
      this.backupTimer = null
    }
    this.isInitialized = false
    console.log('DatabaseManager closed')
  }

  // 用户档案管理

  /**
   * 保存用户档案
   */
  async saveUserProfile(profile: UserProfile): Promise<boolean> {
    this.ensureInitialized()
    const success = await this.storage.save('user_profile', profile)
    if (success) {
      await this.logBehavior({
        type: 'profile_updated',
        category: 'system',
        userId: profile.id
      })
    }
    return success
  }

  /**
   * 获取用户档案
   */
  async getUserProfile(): Promise<UserProfile | null> {
    this.ensureInitialized()
    return await this.storage.load<UserProfile>('user_profile')
  }

  /**
   * 更新用户偏好设置
   */
  async updateUserPreferences(preferences: UserPreferences): Promise<boolean> {
    const profile = await this.getUserProfile()
    if (!profile) {
      console.error('No user profile found')
      return false
    }

    profile.preferences = preferences
    return await this.saveUserProfile(profile)
  }

  /**
   * 获取用户偏好设置
   */
  async getUserPreferences(): Promise<UserPreferences | null> {
    const profile = await this.getUserProfile()
    return profile?.preferences || null
  }

  /**
   * 更新用户统计数据
   */
  async updateUserStatistics(statistics: Partial<UserStatistics>): Promise<boolean> {
    const profile = await this.getUserProfile()
    if (!profile) {
      console.error('No user profile found')
      return false
    }

    profile.statistics = {
      ...profile.statistics,
      ...statistics,
      lastUpdated: Date.now()
    }

    return await this.saveUserProfile(profile)
  }

  // 游戏进度管理

  /**
   * 保存游戏进度
   */
  async saveGameProgress(progress: GameProgress): Promise<boolean> {
    this.ensureInitialized()
    progress.lastSaved = Date.now()
    const success = await this.storage.save('game_progress', progress)
    
    if (success) {
      await this.logBehavior({
        type: 'game_saved',
        category: 'system',
        userId: progress.userId
      })
    }
    
    return success
  }

  /**
   * 获取游戏进度
   */
  async getGameProgress(): Promise<GameProgress | null> {
    this.ensureInitialized()
    return await this.storage.load<GameProgress>('game_progress')
  }

  /**
   * 创建新游戏进度
   */
  async createNewGameProgress(userId: string, username: string): Promise<GameProgress> {
    const newProgress: GameProgress = {
      userId,
      gameId: `game_${Date.now()}`,
      level: 1,
      experience: 0,
      gameTime: 0,
      farmGrid: {},
      crops: {},
      inventory: {
        seeds: { tomato: 5, carrot: 3, potato: 2 },
        harvested: {},
        tools: {},
        currency: 100
      },
      achievements: [],
      lastSaved: Date.now(),
      version: '1.0.0'
    }

    await this.saveGameProgress(newProgress)
    return newProgress
  }

  // 行为统计管理

  /**
   * 记录用户行为
   */
  async logBehavior(event: {
    type: string
    category: string
    userId?: string
    value?: number
    details?: string
  }): Promise<boolean> {
    this.ensureInitialized()
    
    const record: BehaviorRecord = {
      id: `behavior_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      userId: event.userId || 'anonymous',
      timestamp: Date.now(),
      event: {
        type: event.type as any,
        category: event.category as any,
        value: event.value,
        details: event.details
      },
      metadata: {
        userAgent: navigator.userAgent,
        timestamp: new Date().toISOString()
      }
    }

    // 获取现有行为记录
    const existingRecords = await this.getBehaviorRecords() || []
    existingRecords.push(record)

    // 清理过期记录
    const retentionTime = Date.now() - (this.config.dataRetentionDays * 24 * 60 * 60 * 1000)
    const filteredRecords = existingRecords.filter(r => r.timestamp > retentionTime)

    return await this.storage.save('behavior_records', filteredRecords)
  }

  /**
   * 获取行为记录
   */
  async getBehaviorRecords(
    userId?: string,
    category?: string,
    limit: number = 100
  ): Promise<BehaviorRecord[]> {
    this.ensureInitialized()
    
    let records = await this.storage.load<BehaviorRecord[]>('behavior_records') || []
    
    // 过滤条件
    if (userId) {
      records = records.filter(r => r.userId === userId)
    }
    
    if (category) {
      records = records.filter(r => r.event.category === category)
    }

    // 按时间戳降序排序并限制数量
    return records
      .sort((a, b) => b.timestamp - a.timestamp)
      .slice(0, limit)
  }

  /**
   * 获取行为统计
   */
  async getBehaviorStatistics(userId?: string): Promise<{
    totalEvents: number
    eventsByCategory: Record<string, number>
    eventsByType: Record<string, number>
    timeRange: { start: number; end: number }
  }> {
    const records = await this.getBehaviorRecords(userId)
    
    const stats = {
      totalEvents: records.length,
      eventsByCategory: {} as Record<string, number>,
      eventsByType: {} as Record<string, number>,
      timeRange: {
        start: records.length > 0 ? Math.min(...records.map(r => r.timestamp)) : 0,
        end: records.length > 0 ? Math.max(...records.map(r => r.timestamp)) : 0
      }
    }

    records.forEach(record => {
      // 按类别统计
      stats.eventsByCategory[record.event.category] = 
        (stats.eventsByCategory[record.event.category] || 0) + 1

      // 按类型统计
      stats.eventsByType[record.event.type] = 
        (stats.eventsByType[record.event.type] || 0) + 1
    })

    return stats
  }

  // 备份和恢复

  /**
   * 创建手动备份
   */
  async createBackup(description?: string): Promise<BackupInfo | null> {
    this.ensureInitialized()
    return await (this.storage as LocalStorageAdapter).createBackup(description)
  }

  /**
   * 获取备份列表
   */
  async getBackupList(): Promise<BackupInfo[]> {
    this.ensureInitialized()
    return await (this.storage as LocalStorageAdapter).getBackupList()
  }

  /**
   * 从备份恢复
   */
  async restoreFromBackup(backupId: string): Promise<boolean> {
    this.ensureInitialized()
    const success = await (this.storage as LocalStorageAdapter).restoreFromBackup(backupId)
    
    if (success) {
      await this.logBehavior({
        type: 'data_restored',
        category: 'system',
        details: `Restored from backup: ${backupId}`
      })
    }
    
    return success
  }

  // 数据完整性和维护

  /**
   * 检查数据完整性
   */
  async checkDataIntegrity(): Promise<DataIntegrityCheck> {
    this.ensureInitialized()
    return await (this.storage as LocalStorageAdapter).checkDataIntegrity()
  }

  /**
   * 清理过期数据
   */
  async cleanupExpiredData(): Promise<boolean> {
    try {
      const retentionTime = Date.now() - (this.config.dataRetentionDays * 24 * 60 * 60 * 1000)
      
      // 清理过期行为记录
      const behaviorRecords = await this.getBehaviorRecords()
      const validRecords = behaviorRecords.filter(r => r.timestamp > retentionTime)
      await this.storage.save('behavior_records', validRecords)

      await this.logBehavior({
        type: 'data_cleanup',
        category: 'system',
        details: `Cleaned up ${behaviorRecords.length - validRecords.length} expired records`
      })

      return true
    } catch (error) {
      console.error('Failed to cleanup expired data:', error)
      return false
    }
  }

  /**
   * 获取存储使用情况
   */
  async getStorageUsage(): Promise<{
    totalSize: number
    itemSizes: Record<string, number>
    availableSpace: number
  }> {
    const keys = await this.storage.getAllKeys()
    const itemSizes: Record<string, number> = {}
    let totalSize = 0

    for (const key of keys) {
      const cleanKey = key.replace('self_farm_', '')
      const size = await this.storage.getSize(cleanKey)
      itemSizes[cleanKey] = size
      totalSize += size
    }

    // LocalStorage 通常有 5-10MB 限制
    const estimatedLimit = 5 * 1024 * 1024 // 5MB
    const availableSpace = Math.max(0, estimatedLimit - totalSize)

    return {
      totalSize,
      itemSizes,
      availableSpace
    }
  }

  // 私有方法

  private ensureInitialized(): void {
    if (!this.isInitialized) {
      throw new Error('DatabaseManager not initialized. Call initialize() first.')
    }
  }

  private startAutoBackup(): void {
    if (this.backupTimer !== null) {
      clearInterval(this.backupTimer)
    }

    this.backupTimer = window.setInterval(async () => {
      try {
        await this.createBackup('Auto backup')
        console.log('Auto backup created successfully')
      } catch (error) {
        console.error('Auto backup failed:', error)
      }
    }, this.config.backupInterval * 60 * 60 * 1000) // 转换为毫秒
  }

  /**
   * 导出所有数据（用于调试或迁移）
   */
  async exportAllData(): Promise<string> {
    const profile = await this.getUserProfile()
    const progress = await this.getGameProgress()
    const behaviors = await this.getBehaviorRecords()
    const backups = await this.getBackupList()

    const exportData = {
      timestamp: Date.now(),
      version: '1.0.0',
      data: {
        profile,
        progress,
        behaviors,
        backups
      }
    }

    return JSON.stringify(exportData, null, 2)
  }

  /**
   * 导入数据（用于调试或迁移）
   */
  async importData(dataString: string): Promise<boolean> {
    try {
      const importData = JSON.parse(dataString)
      
      if (importData.data.profile) {
        await this.saveUserProfile(importData.data.profile)
      }
      
      if (importData.data.progress) {
        await this.saveGameProgress(importData.data.progress)
      }
      
      if (importData.data.behaviors) {
        await this.storage.save('behavior_records', importData.data.behaviors)
      }

      await this.logBehavior({
        type: 'data_imported',
        category: 'system',
        details: `Imported data from ${new Date(importData.timestamp).toISOString()}`
      })

      return true
    } catch (error) {
      console.error('Failed to import data:', error)
      return false
    }
  }
} 