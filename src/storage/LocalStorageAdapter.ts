// 本地存储适配器 - 支持加密、压缩和版本控制

import { StorageConfig, BackupInfo, DataIntegrityCheck } from '../types/user'

export interface StorageAdapter {
  save<T>(key: string, data: T): Promise<boolean>
  load<T>(key: string): Promise<T | null>
  remove(key: string): Promise<boolean>
  exists(key: string): Promise<boolean>
  clear(): Promise<boolean>
  getAllKeys(): Promise<string[]>
  getSize(key: string): Promise<number>
}

export class LocalStorageAdapter implements StorageAdapter {
  private readonly keyPrefix: string
  private readonly config: StorageConfig
  private readonly version: string = '1.0.0'

  constructor(config: StorageConfig, keyPrefix: string = 'self_farm_') {
    this.config = config
    this.keyPrefix = keyPrefix
  }

  /**
   * 保存数据到本地存储
   */
  async save<T>(key: string, data: T): Promise<boolean> {
    try {
      const fullKey = this.getFullKey(key)
      let serializedData = JSON.stringify({
        version: this.version,
        timestamp: Date.now(),
        data
      })

      // 压缩数据（如果启用）
      if (this.config.compressionEnabled) {
        serializedData = await this.compressData(serializedData)
      }

      // 加密数据（如果启用）
      if (this.config.encryptionEnabled) {
        serializedData = await this.encryptData(serializedData)
      }

      localStorage.setItem(fullKey, serializedData)
      return true
    } catch (error) {
      console.error(`Failed to save data for key ${key}:`, error)
      return false
    }
  }

  /**
   * 从本地存储加载数据
   */
  async load<T>(key: string): Promise<T | null> {
    try {
      const fullKey = this.getFullKey(key)
      let serializedData = localStorage.getItem(fullKey)
      
      if (!serializedData) {
        return null
      }

      // 解密数据（如果启用）
      if (this.config.encryptionEnabled) {
        serializedData = await this.decryptData(serializedData)
      }

      // 解压数据（如果启用）
      if (this.config.compressionEnabled) {
        serializedData = await this.decompressData(serializedData)
      }

      const parsedData = JSON.parse(serializedData)
      
      // 版本兼容性检查
      if (parsedData.version !== this.version) {
        console.warn(`Data version mismatch for key ${key}. Expected: ${this.version}, Found: ${parsedData.version}`)
        // 这里可以添加版本迁移逻辑
      }

      return parsedData.data
    } catch (error) {
      console.error(`Failed to load data for key ${key}:`, error)
      return null
    }
  }

  /**
   * 删除指定键的数据
   */
  async remove(key: string): Promise<boolean> {
    try {
      const fullKey = this.getFullKey(key)
      localStorage.removeItem(fullKey)
      return true
    } catch (error) {
      console.error(`Failed to remove data for key ${key}:`, error)
      return false
    }
  }

  /**
   * 检查键是否存在
   */
  async exists(key: string): Promise<boolean> {
    const fullKey = this.getFullKey(key)
    return localStorage.getItem(fullKey) !== null
  }

  /**
   * 清空所有存储数据
   */
  async clear(): Promise<boolean> {
    try {
      const keys = await this.getAllKeys()
      for (const key of keys) {
        await this.remove(key.replace(this.keyPrefix, ''))
      }
      return true
    } catch (error) {
      console.error('Failed to clear storage:', error)
      return false
    }
  }

  /**
   * 获取所有键
   */
  async getAllKeys(): Promise<string[]> {
    const keys: string[] = []
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key && key.startsWith(this.keyPrefix)) {
        keys.push(key)
      }
    }
    return keys
  }

  /**
   * 获取指定键的数据大小
   */
  async getSize(key: string): Promise<number> {
    const fullKey = this.getFullKey(key)
    const data = localStorage.getItem(fullKey)
    return data ? new Blob([data]).size : 0
  }

  /**
   * 创建数据备份
   */
  async createBackup(description?: string): Promise<BackupInfo | null> {
    try {
      const keys = await this.getAllKeys()
      const backupData: Record<string, any> = {}
      
      for (const key of keys) {
        const data = localStorage.getItem(key)
        if (data) {
          backupData[key] = data
        }
      }

      const backupString = JSON.stringify(backupData)
      const checksum = await this.calculateChecksum(backupString)
      const timestamp = Date.now()
      const backupId = `backup_${timestamp}`

      const backupInfo: BackupInfo = {
        id: backupId,
        timestamp,
        size: new Blob([backupString]).size,
        checksum,
        version: this.version,
        description
      }

      // 保存备份数据
      localStorage.setItem(this.getFullKey(`backup_${backupId}`), backupString)
      
      // 保存备份信息
      const backups = await this.getBackupList()
      backups.push(backupInfo)
      await this.save('backups', backups)

      // 清理旧备份
      await this.cleanupOldBackups()

      return backupInfo
    } catch (error) {
      console.error('Failed to create backup:', error)
      return null
    }
  }

  /**
   * 从备份恢复数据
   */
  async restoreFromBackup(backupId: string): Promise<boolean> {
    try {
      const backupData = localStorage.getItem(this.getFullKey(`backup_${backupId}`))
      if (!backupData) {
        throw new Error(`Backup ${backupId} not found`)
      }

      const parsedBackup = JSON.parse(backupData)
      
      // 清空当前数据
      await this.clear()
      
      // 恢复备份数据
      for (const [key, value] of Object.entries(parsedBackup)) {
        localStorage.setItem(key as string, value as string)
      }

      return true
    } catch (error) {
      console.error(`Failed to restore from backup ${backupId}:`, error)
      return false
    }
  }

  /**
   * 获取备份列表
   */
  async getBackupList(): Promise<BackupInfo[]> {
    return (await this.load<BackupInfo[]>('backups')) || []
  }

  /**
   * 数据完整性检查
   */
  async checkDataIntegrity(): Promise<DataIntegrityCheck> {
    const result: DataIntegrityCheck = {
      timestamp: Date.now(),
      isValid: true,
      errors: [],
      warnings: [],
      checkedTables: []
    }

    try {
      const keys = await this.getAllKeys()
      
      for (const key of keys) {
        try {
          const data = localStorage.getItem(key)
          if (data) {
            JSON.parse(data) // 检查JSON格式是否有效
            result.checkedTables.push(key)
          }
        } catch (error) {
          result.isValid = false
          result.errors.push(`Invalid JSON format for key: ${key}`)
        }
      }

      // 检查关键数据是否存在
      const criticalKeys = ['user_profile', 'game_progress', 'user_preferences']
      for (const criticalKey of criticalKeys) {
        if (!(await this.exists(criticalKey))) {
          result.warnings.push(`Critical data missing: ${criticalKey}`)
        }
      }

    } catch (error) {
      result.isValid = false
      result.errors.push(`Data integrity check failed: ${error}`)
    }

    return result
  }

  // 私有方法

  private getFullKey(key: string): string {
    return `${this.keyPrefix}${key}`
  }

  private async compressData(data: string): Promise<string> {
    // 简单的压缩实现（实际项目中可以使用更强的压缩算法）
    try {
      const compressed = btoa(data)
      return `compressed:${compressed}`
    } catch (error) {
      console.warn('Compression failed, using original data:', error)
      return data
    }
  }

  private async decompressData(data: string): Promise<string> {
    if (data.startsWith('compressed:')) {
      try {
        return atob(data.replace('compressed:', ''))
      } catch (error) {
        console.warn('Decompression failed:', error)
        throw error
      }
    }
    return data
  }

  private async encryptData(data: string): Promise<string> {
    // 简单的加密实现（实际项目中应使用更强的加密算法）
    try {
      const encrypted = btoa(data.split('').reverse().join(''))
      return `encrypted:${encrypted}`
    } catch (error) {
      console.warn('Encryption failed, using original data:', error)
      return data
    }
  }

  private async decryptData(data: string): Promise<string> {
    if (data.startsWith('encrypted:')) {
      try {
        const decrypted = atob(data.replace('encrypted:', ''))
        return decrypted.split('').reverse().join('')
      } catch (error) {
        console.warn('Decryption failed:', error)
        throw error
      }
    }
    return data
  }

  private async calculateChecksum(data: string): Promise<string> {
    // 简单的校验和实现
    let hash = 0
    for (let i = 0; i < data.length; i++) {
      const char = data.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // 转换为32位整数
    }
    return hash.toString(16)
  }

  private async cleanupOldBackups(): Promise<void> {
    const backups = await this.getBackupList()
    if (backups.length > this.config.maxBackupFiles) {
      // 按时间戳排序，保留最新的备份
      backups.sort((a, b) => b.timestamp - a.timestamp)
      const toDelete = backups.slice(this.config.maxBackupFiles)
      
      for (const backup of toDelete) {
        await this.remove(`backup_${backup.id}`)
      }
      
      // 更新备份列表
      const remainingBackups = backups.slice(0, this.config.maxBackupFiles)
      await this.save('backups', remainingBackups)
    }
  }
} 