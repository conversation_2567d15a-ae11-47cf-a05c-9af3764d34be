import React, { useEffect, useRef, useState } from 'react'
import Phaser from 'phaser'
import { gameConfig } from '../game/GameConfig'
import { UnifiedAgriculturalScene } from '../game/scenes/UnifiedAgriculturalScene'
import { ItemIntegrationManager } from '../managers/ItemIntegrationManager'
import { UnifiedInventoryPanel } from '../components/UnifiedInventoryPanel'
import SimpleDragSynthesis from '../components/SimpleDragSynthesis'
import { LootboxType, ItemRarity, ItemCategory } from '../types/lootbox'
import { InventoryItem } from '../types/inventory'

export const AgriculturalDemo: React.FC = () => {
  const gameRef = useRef<Phaser.Game | null>(null)
  const phaserRef = useRef<HTMLDivElement>(null)
  const itemManagerRef = useRef<ItemIntegrationManager | null>(null)
  
  const [showInventory, setShowInventory] = useState(false)
  const [showSynthesis, setShowSynthesis] = useState(false)
  const [testItems, setTestItems] = useState<InventoryItem[]>([])
  const [systemStats, setSystemStats] = useState({
    totalItems: 0,
    totalValue: 0,
    plantsGrowing: 0,
    synthesesCompleted: 0
  })

  useEffect(() => {
    if (phaserRef.current && !gameRef.current) {
      // 创建游戏配置
      const config: Phaser.Types.Core.GameConfig = {
        ...gameConfig,
        parent: phaserRef.current,
        scene: [UnifiedAgriculturalScene],
        width: 800,
        height: 600
      }

      // 启动游戏
      gameRef.current = new Phaser.Game(config)
      
      // 创建物品管理器
      itemManagerRef.current = new ItemIntegrationManager()
      
      // 设置事件监听
      setupEventListeners()
      
      console.log('🌾 农产品演示系统已启动')
    }

    return () => {
      if (gameRef.current) {
        gameRef.current.destroy(true)
        gameRef.current = null
      }
    }
  }, [])

  const setupEventListeners = () => {
    if (!itemManagerRef.current) return

    const itemManager = itemManagerRef.current

    // 监听物品变化
    itemManager.onItemAdded(() => updateStats())
    itemManager.onItemRemoved(() => updateStats())
    itemManager.onItemsSynthesized((data) => {
      setSystemStats(prev => ({
        ...prev,
        synthesesCompleted: prev.synthesesCompleted + 1
      }))
      updateStats()
    })
    itemManager.onItemPlanted(() => {
      setSystemStats(prev => ({
        ...prev,
        plantsGrowing: prev.plantsGrowing + 1
      }))
      updateStats()
    })
    itemManager.onItemHarvested(() => {
      setSystemStats(prev => ({
        ...prev,
        plantsGrowing: Math.max(0, prev.plantsGrowing - 1)
      }))
      updateStats()
    })
  }

  const updateStats = () => {
    if (!itemManagerRef.current) return
    
    const items = itemManagerRef.current.getAllItems()
    const totalItems = items.reduce((sum, item) => sum + item.quantity, 0)
    const totalValue = items.reduce((sum, item) => sum + (item.value * item.quantity), 0)
    
    setSystemStats(prev => ({
      ...prev,
      totalItems,
      totalValue
    }))
  }

  const openTestLootbox = async (type: LootboxType) => {
    if (!itemManagerRef.current) return
    
    const result = await itemManagerRef.current.openLootbox(type)
    
    if (result.success) {
      const itemNames = result.items.map(item => item.name).join(', ')
      alert(`🎁 开盒成功！获得：${itemNames}`)
    } else {
      alert(`❌ 开盒失败：${result.error}`)
    }
  }

  const performTestSynthesis = async () => {
    if (!itemManagerRef.current) return
    
    // 获取可合成的物品（前两个）
    const synthesizableItems = itemManagerRef.current.getSynthesizableItems()
    
    if (synthesizableItems.length < 2) {
      alert('需要至少2个物品才能合成！请先开启盲盒获得物品。')
      return
    }
    
    // 找到两个相同品质的物品
    const sameRarityGroups = new Map<ItemRarity, string[]>()
    synthesizableItems.forEach(item => {
      if (!sameRarityGroups.has(item.rarity)) {
        sameRarityGroups.set(item.rarity, [])
      }
      sameRarityGroups.get(item.rarity)!.push(item.id)
    })
    
    // 找到第一个有至少2个物品的品质组
    let selectedItems: string[] = []
    for (const [rarity, items] of sameRarityGroups) {
      if (items.length >= 2) {
        selectedItems = items.slice(0, 2)
        break
      }
    }
    
    if (selectedItems.length < 2) {
      alert('没有找到相同品质的物品进行合成！')
      return
    }
    
    const result = await itemManagerRef.current.synthesizeItems(selectedItems)
    
    if (result.success) {
      alert(`⚗️ 合成成功！获得：${result.resultItem?.name}`)
    } else {
      alert(`❌ 合成失败：${result.error}`)
    }
  }

  const plantTestSeed = async () => {
    if (!itemManagerRef.current) return
    
    const seeds = itemManagerRef.current.getPlantableSeeds()
    
    if (seeds.length === 0) {
      alert('没有可种植的种子！请先开启盲盒获得种子。')
      return
    }
    
    const seed = seeds[0]
    const result = await itemManagerRef.current.plantItem(seed.id, 'slot_0_0')
    
    if (result.success) {
      alert(`🌱 种植成功！${seed.name} 已种植到农田中。`)
    } else {
      alert(`❌ 种植失败：${result.message}`)
    }
  }

  // 添加测试物品
  const addTestItems = () => {
    const testInventoryItems: InventoryItem[] = [
      {
        id: 'test_1',
        itemId: 'wheat_gray',
        name: '普通小麦种子',
        icon: '🌾',
        rarity: ItemRarity.GRAY,
        category: ItemCategory.AGRICULTURAL,
        type: 'SEED' as any,
        quantity: 3,
        description: '普通品质的小麦种子',
        obtainedAt: Date.now()
      },
      {
        id: 'test_2',
        itemId: 'corn_gray',
        name: '普通玉米种子',
        icon: '🌽',
        rarity: ItemRarity.GRAY,
        category: ItemCategory.AGRICULTURAL,
        type: 'SEED' as any,
        quantity: 2,
        description: '普通品质的玉米种子',
        obtainedAt: Date.now()
      },
      {
        id: 'test_3',
        itemId: 'wheat_green',
        name: '优质小麦',
        icon: '🌾',
        rarity: ItemRarity.GREEN,
        category: ItemCategory.AGRICULTURAL,
        type: 'CROP' as any,
        quantity: 2,
        description: '优质品质的小麦',
        obtainedAt: Date.now()
      },
      {
        id: 'test_4',
        itemId: 'rice_blue',
        name: '稀有水稻',
        icon: '🍚',
        rarity: ItemRarity.BLUE,
        category: ItemCategory.AGRICULTURAL,
        type: 'CROP' as any,
        quantity: 1,
        description: '稀有品质的水稻',
        obtainedAt: Date.now()
      }
    ]
    
    setTestItems(testInventoryItems)
    alert('✅ 测试物品已添加！现在可以测试拖拽合成功能了。')
  }

  // 处理合成结果
  const handleSynthesisResult = (resultItem: InventoryItem, consumedItems: InventoryItem[]) => {
    // 从测试物品中移除消耗的物品
    setTestItems(prev => {
      let newItems = [...prev]
      
      consumedItems.forEach(consumedItem => {
        const index = newItems.findIndex(item => item.id === consumedItem.id)
        if (index !== -1) {
          if (newItems[index].quantity > 1) {
            newItems[index] = { ...newItems[index], quantity: newItems[index].quantity - 1 }
          } else {
            newItems.splice(index, 1)
          }
        }
      })
      
      // 添加新的合成结果
      newItems.push(resultItem)
      return newItems
    })

    setSystemStats(prev => ({
      ...prev,
      synthesesCompleted: prev.synthesesCompleted + 1
    }))

    alert(`🎉 合成成功！获得了 ${resultItem.name}`)
  }

  // 处理合成错误
  const handleSynthesisError = (message: string) => {
    alert(`❌ ${message}`)
  }

  const getRarityColor = (rarity: ItemRarity): string => {
    const colors = {
      [ItemRarity.GRAY]: '#9E9E9E',
      [ItemRarity.GREEN]: '#4CAF50',
      [ItemRarity.BLUE]: '#2196F3',
      [ItemRarity.ORANGE]: '#FF9800',
      [ItemRarity.GOLD]: '#FFD700',
      [ItemRarity.GOLD_RED]: '#FF6B6B'
    }
    return colors[rarity] || '#9E9E9E'
  }

  return (
    <div style={{ 
      padding: '20px', 
      backgroundColor: '#f5f5f5', 
      minHeight: '100vh',
      fontFamily: 'Arial, sans-serif'
    }}>
      {/* 标题区域 */}
      <header style={{ 
        textAlign: 'center', 
        marginBottom: '30px',
        backgroundColor: '#ffffff',
        padding: '20px',
        borderRadius: '12px',
        boxShadow: '0 4px 12px rgba(0,0,0,0.1)'
      }}>
        <h1 style={{ 
          fontSize: '2.5rem', 
          color: '#2C5530',
          margin: '0 0 10px 0',
          textShadow: '2px 2px 4px rgba(0,0,0,0.1)'
        }}>
          🌾 农产品物品道具数值策划系统
        </h1>
        <p style={{ 
          fontSize: '1.2rem', 
          color: '#666',
          margin: '0'
        }}>
          完整的农场游戏体验：专注代币 → 盲盒抽卡 → 道具合成 → 农田种植
        </p>
      </header>

      <div style={{ 
        display: 'grid', 
        gridTemplateColumns: '2fr 1fr', 
        gap: '20px',
        maxWidth: '1400px',
        margin: '0 auto'
      }}>
        {/* 游戏区域 */}
        <div style={{ 
          backgroundColor: '#ffffff',
          borderRadius: '12px',
          padding: '20px',
          boxShadow: '0 4px 12px rgba(0,0,0,0.1)'
        }}>
          <h2 style={{ 
            fontSize: '1.5rem', 
            color: '#2C5530', 
            marginBottom: '15px',
            display: 'flex',
            alignItems: 'center',
            gap: '10px'
          }}>
            🎮 游戏场景
            <span style={{ 
              fontSize: '0.8rem', 
              backgroundColor: '#4CAF50', 
              color: 'white', 
              padding: '4px 8px', 
              borderRadius: '12px' 
            }}>
              运行中
            </span>
          </h2>
          <div 
            ref={phaserRef} 
            style={{ 
              border: '2px solid #ddd',
              borderRadius: '8px',
              overflow: 'hidden',
              backgroundColor: '#87CEEB'
            }} 
          />
          <div style={{ 
            marginTop: '15px', 
            padding: '10px', 
            backgroundColor: '#f8f9fa', 
            borderRadius: '6px',
            fontSize: '0.9rem',
            color: '#666'
          }}>
            💡 <strong>操作提示：</strong>
            <br />• 使用数字键 1-4 切换不同功能模式
            <br />• 农场模式：点击土地进行种植或收获
            <br />• 盲盒模式：点击盲盒按钮开启奖励
            <br />• 合成模式：选择物品进行品质升级
            <br />• 按 ESC 返回主菜单
          </div>
        </div>

        {/* 控制面板区域 */}
        <div style={{ display: 'flex', flexDirection: 'column', gap: '20px' }}>
          {/* 系统统计 */}
          <div style={{ 
            backgroundColor: '#ffffff',
            borderRadius: '12px',
            padding: '20px',
            boxShadow: '0 4px 12px rgba(0,0,0,0.1)'
          }}>
            <h3 style={{ 
              fontSize: '1.3rem', 
              color: '#2C5530', 
              marginBottom: '15px',
              display: 'flex',
              alignItems: 'center',
              gap: '8px'
            }}>
              📊 系统统计
            </h3>
            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '10px' }}>
              <div style={{ 
                padding: '10px', 
                backgroundColor: '#e3f2fd', 
                borderRadius: '6px',
                textAlign: 'center'
              }}>
                <div style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#1976d2' }}>
                  {systemStats.totalItems}
                </div>
                <div style={{ fontSize: '0.8rem', color: '#666' }}>总物品数</div>
              </div>
              <div style={{ 
                padding: '10px', 
                backgroundColor: '#e8f5e8', 
                borderRadius: '6px',
                textAlign: 'center'
              }}>
                <div style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#388e3c' }}>
                  ¥{systemStats.totalValue}
                </div>
                <div style={{ fontSize: '0.8rem', color: '#666' }}>总价值</div>
              </div>
              <div style={{ 
                padding: '10px', 
                backgroundColor: '#fff3e0', 
                borderRadius: '6px',
                textAlign: 'center'
              }}>
                <div style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#f57c00' }}>
                  {systemStats.plantsGrowing}
                </div>
                <div style={{ fontSize: '0.8rem', color: '#666' }}>种植中</div>
              </div>
              <div style={{ 
                padding: '10px', 
                backgroundColor: '#fce4ec', 
                borderRadius: '6px',
                textAlign: 'center'
              }}>
                <div style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#c2185b' }}>
                  {systemStats.synthesesCompleted}
                </div>
                <div style={{ fontSize: '0.8rem', color: '#666' }}>合成次数</div>
              </div>
            </div>
          </div>

          {/* 快速操作 */}
          <div style={{ 
            backgroundColor: '#ffffff',
            borderRadius: '12px',
            padding: '20px',
            boxShadow: '0 4px 12px rgba(0,0,0,0.1)'
          }}>
            <h3 style={{ 
              fontSize: '1.3rem', 
              color: '#2C5530', 
              marginBottom: '15px',
              display: 'flex',
              alignItems: 'center',
              gap: '8px'
            }}>
              ⚡ 快速操作
            </h3>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '10px' }}>
              <button
                onClick={() => openTestLootbox(LootboxType.BASIC_FARM)}
                style={{
                  padding: '12px',
                  backgroundColor: '#4CAF50',
                  color: 'white',
                  border: 'none',
                  borderRadius: '6px',
                  cursor: 'pointer',
                  fontSize: '1rem',
                  fontWeight: 'bold',
                  transition: 'all 0.3s ease',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  gap: '8px'
                }}
                onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#45a049'}
                onMouseLeave={(e) => e.currentTarget.style.backgroundColor = '#4CAF50'}
              >
                📦 开启基础农场盒
              </button>
              
              <button
                onClick={() => openTestLootbox(LootboxType.PREMIUM_FARM)}
                style={{
                  padding: '12px',
                  backgroundColor: '#FF9800',
                  color: 'white',
                  border: 'none',
                  borderRadius: '6px',
                  cursor: 'pointer',
                  fontSize: '1rem',
                  fontWeight: 'bold',
                  transition: 'all 0.3s ease',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  gap: '8px'
                }}
                onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#f57c00'}
                onMouseLeave={(e) => e.currentTarget.style.backgroundColor = '#FF9800'}
              >
                🎁 开启高级农场盒
              </button>
              
              <button
                onClick={performTestSynthesis}
                style={{
                  padding: '12px',
                  backgroundColor: '#9C27B0',
                  color: 'white',
                  border: 'none',
                  borderRadius: '6px',
                  cursor: 'pointer',
                  fontSize: '1rem',
                  fontWeight: 'bold',
                  transition: 'all 0.3s ease',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  gap: '8px'
                }}
                onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#7b1fa2'}
                onMouseLeave={(e) => e.currentTarget.style.backgroundColor = '#9C27B0'}
              >
                ⚗️ 执行道具合成
              </button>
              
              <button
                onClick={plantTestSeed}
                style={{
                  padding: '12px',
                  backgroundColor: '#2E7D32',
                  color: 'white',
                  border: 'none',
                  borderRadius: '6px',
                  cursor: 'pointer',
                  fontSize: '1rem',
                  fontWeight: 'bold',
                  transition: 'all 0.3s ease',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  gap: '8px'
                }}
                onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#1b5e20'}
                onMouseLeave={(e) => e.currentTarget.style.backgroundColor = '#2E7D32'}
              >
                🌱 种植测试种子
              </button>
              
              <button
                onClick={() => setShowInventory(!showInventory)}
                style={{
                  padding: '12px',
                  backgroundColor: showInventory ? '#f44336' : '#607D8B',
                  color: 'white',
                  border: 'none',
                  borderRadius: '6px',
                  cursor: 'pointer',
                  fontSize: '1rem',
                  fontWeight: 'bold',
                  transition: 'all 0.3s ease',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  gap: '8px'
                }}
                onMouseEnter={(e) => e.currentTarget.style.opacity = '0.9'}
                onMouseLeave={(e) => e.currentTarget.style.opacity = '1'}
              >
                🎒 {showInventory ? '关闭背包' : '打开背包'}
              </button>
              
              <button
                onClick={addTestItems}
                style={{
                  padding: '12px',
                  backgroundColor: '#3F51B5',
                  color: 'white',
                  border: 'none',
                  borderRadius: '6px',
                  cursor: 'pointer',
                  fontSize: '1rem',
                  fontWeight: 'bold',
                  transition: 'all 0.3s ease',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  gap: '8px'
                }}
                onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#303F9F'}
                onMouseLeave={(e) => e.currentTarget.style.backgroundColor = '#3F51B5'}
              >
                🧪 添加测试物品
              </button>
              
              <button
                onClick={() => setShowSynthesis(!showSynthesis)}
                style={{
                  padding: '12px',
                  backgroundColor: showSynthesis ? '#f44336' : '#8E24AA',
                  color: 'white',
                  border: 'none',
                  borderRadius: '6px',
                  cursor: 'pointer',
                  fontSize: '1rem',
                  fontWeight: 'bold',
                  transition: 'all 0.3s ease',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  gap: '8px'
                }}
                onMouseEnter={(e) => e.currentTarget.style.opacity = '0.9'}
                onMouseLeave={(e) => e.currentTarget.style.opacity = '1'}
              >
                ⚗️ {showSynthesis ? '关闭合成台' : '打开合成台'}
              </button>
            </div>
          </div>

          {/* 品质说明 */}
          <div style={{ 
            backgroundColor: '#ffffff',
            borderRadius: '12px',
            padding: '20px',
            boxShadow: '0 4px 12px rgba(0,0,0,0.1)'
          }}>
            <h3 style={{ 
              fontSize: '1.3rem', 
              color: '#2C5530', 
              marginBottom: '15px',
              display: 'flex',
              alignItems: 'center',
              gap: '8px'
            }}>
              🎨 品质等级
            </h3>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
              {[
                { rarity: ItemRarity.GRAY, name: '普通', production: '1-3/天' },
                { rarity: ItemRarity.GREEN, name: '优质', production: '4-6/天' },
                { rarity: ItemRarity.BLUE, name: '稀有', production: '7-10/天' },
                { rarity: ItemRarity.ORANGE, name: '史诗', production: '12-16/天' },
                { rarity: ItemRarity.GOLD, name: '传说', production: '18-25/天' },
                { rarity: ItemRarity.GOLD_RED, name: '神话', production: '30-40/天' }
              ].map(({ rarity, name, production }) => (
                <div 
                  key={rarity}
                  style={{ 
                    display: 'flex', 
                    alignItems: 'center', 
                    gap: '8px',
                    padding: '6px',
                    borderRadius: '4px',
                    backgroundColor: '#f8f9fa'
                  }}
                >
                  <div 
                    style={{ 
                      width: '16px', 
                      height: '16px', 
                      borderRadius: '50%', 
                      backgroundColor: getRarityColor(rarity) 
                    }} 
                  />
                  <span style={{ fontWeight: 'bold', minWidth: '40px' }}>{name}</span>
                  <span style={{ fontSize: '0.8rem', color: '#666' }}>{production}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* 背包面板 */}
      {showInventory && itemManagerRef.current && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0,0,0,0.7)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000
        }}>
          <div style={{
            backgroundColor: 'white',
            borderRadius: '12px',
            padding: '20px',
            maxWidth: '800px',
            maxHeight: '80vh',
            overflow: 'auto',
            position: 'relative'
          }}>
            <button
              onClick={() => setShowInventory(false)}
              style={{
                position: 'absolute',
                top: '10px',
                right: '10px',
                background: '#f44336',
                color: 'white',
                border: 'none',
                borderRadius: '50%',
                width: '30px',
                height: '30px',
                cursor: 'pointer',
                fontSize: '1.2rem'
              }}
            >
              ×
            </button>
            <UnifiedInventoryPanel 
              itemManager={itemManagerRef.current}
              className="demo-inventory"
            />
          </div>
        </div>
      )}

      {/* 合成工作台界面 */}
      {showSynthesis && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0,0,0,0.7)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000
        }}>
          <div style={{
            backgroundColor: 'white',
            borderRadius: '12px',
            padding: '20px',
            maxWidth: '900px',
            maxHeight: '80vh',
            overflow: 'auto',
            position: 'relative'
          }}>
            <button
              onClick={() => setShowSynthesis(false)}
              style={{
                position: 'absolute',
                top: '10px',
                right: '10px',
                background: '#f44336',
                color: 'white',
                border: 'none',
                borderRadius: '50%',
                width: '30px',
                height: '30px',
                cursor: 'pointer',
                fontSize: '1.2rem'
              }}
            >
              ×
            </button>
            <SimpleDragSynthesis
              inventoryItems={testItems}
              onSynthesis={handleSynthesisResult}
              onError={handleSynthesisError}
            />
          </div>
        </div>
      )}

      {/* 底部说明 */}
      <footer style={{ 
        marginTop: '40px', 
        textAlign: 'center',
        padding: '20px',
        backgroundColor: '#ffffff',
        borderRadius: '12px',
        boxShadow: '0 4px 12px rgba(0,0,0,0.1)'
      }}>
        <h3 style={{ color: '#2C5530', marginBottom: '10px' }}>🎯 核心特性</h3>
        <div style={{ 
          display: 'grid', 
          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', 
          gap: '15px',
          marginTop: '15px'
        }}>
          <div style={{ padding: '10px', backgroundColor: '#f8f9fa', borderRadius: '6px' }}>
            <strong>6品质等级</strong><br />
            <small>从普通到神话的完整品质体系</small>
          </div>
          <div style={{ padding: '10px', backgroundColor: '#f8f9fa', borderRadius: '6px' }}>
            <strong>2:1合成机制</strong><br />
            <small>同品质道具升级为更高品质</small>
          </div>
          <div style={{ padding: '10px', backgroundColor: '#f8f9fa', borderRadius: '6px' }}>
            <strong>期货农产品</strong><br />
            <small>基于中国期货市场的真实品种</small>
          </div>
          <div style={{ padding: '10px', backgroundColor: '#f8f9fa', borderRadius: '6px' }}>
            <strong>完整生态系统</strong><br />
            <small>专注代币→盲盒→合成→种植</small>
          </div>
        </div>
        <div style={{ marginTop: '20px', color: '#666', fontSize: '0.9rem' }}>
          💡 <strong>提示：</strong>这是完整的农产品物品道具数值策划系统演示，包含了从获取到消费的完整游戏循环。
        </div>
      </footer>
    </div>
  )
}

export default AgriculturalDemo 