import React, { useEffect, useRef, useState } from 'react'
import { ItemIntegrationManager } from '../managers/ItemIntegrationManager'
import ChineseFuturesInventory from '../components/ChineseFuturesInventory'
import { LootboxTester } from '../components/LootboxTester'
import { EnhancedFarmUI } from '../components/EnhancedFarmUI'
import { LootboxType, ItemRarity, ItemCategory } from '../types/lootbox'
import { InventoryItem } from '../types/inventory'

export const UnifiedGameSystem: React.FC = () => {
  const itemManagerRef = useRef<ItemIntegrationManager | null>(null)

  const [activeTab, setActiveTab] = useState<'lootbox' | 'inventory' | 'planting'>('lootbox')
  const [systemStats, setSystemStats] = useState({
    totalItems: 0,
    totalValue: 0,
    plantsGrowing: 0,
    synthesesCompleted: 0,
    lootboxesOpened: 0
  })

  useEffect(() => {
    // 防止页面缩放
    const viewport = document.querySelector('meta[name="viewport"]')
    if (viewport) {
      viewport.setAttribute('content', 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no')
    }

    // 总是创建物品管理器（不管在哪个标签）
    if (!itemManagerRef.current) {
      itemManagerRef.current = new ItemIntegrationManager()
      setupEventListeners()
      console.log('🎮 物品管理器已初始化')
    }
  }, [activeTab])

  const setupEventListeners = () => {
    if (!itemManagerRef.current) return

    const itemManager = itemManagerRef.current

    // 监听物品变化
    itemManager.onItemAdded((item) => {
      console.log('物品已添加:', item)
      updateStats()
    })
    itemManager.onItemRemoved((itemId) => {
      console.log('物品已移除:', itemId)
      updateStats()
    })
    itemManager.onItemsSynthesized((data) => {
      console.log('物品已合成:', data)
      setSystemStats(prev => ({
        ...prev,
        synthesesCompleted: prev.synthesesCompleted + 1
      }))
      updateStats()
    })
    itemManager.onItemPlanted((data) => {
      console.log('物品已种植:', data)
      setSystemStats(prev => ({
        ...prev,
        plantsGrowing: prev.plantsGrowing + 1
      }))
      updateStats()
    })
    itemManager.onItemHarvested((data) => {
      console.log('物品已收获:', data)
      setSystemStats(prev => ({
        ...prev,
        plantsGrowing: Math.max(0, prev.plantsGrowing - 1)
      }))
      updateStats()
    })
  }

  const updateStats = () => {
    if (!itemManagerRef.current) return
    
    const items = itemManagerRef.current.getAllItems()
    console.log('更新统计信息，当前物品:', items)
    const totalItems = items.reduce((sum, item) => sum + item.quantity, 0)
    const totalValue = items.reduce((sum, item) => sum + (item.value || 0) * item.quantity, 0)
    
    setSystemStats(prev => ({
      ...prev,
      totalItems,
      totalValue
    }))
  }

  const handleLootboxOpened = (items: InventoryItem[]) => {
    console.log('盲盒开启回调，收到物品:', items)
    setSystemStats(prev => ({
      ...prev,
      lootboxesOpened: prev.lootboxesOpened + 1
    }))
    
    // 物品已经通过LootboxTester的itemManager.addManualItem()添加到系统
    // 这里只需要更新统计信息
    updateStats()
  }

  const sideMenuButtonStyle = (isActive: boolean) => ({
    padding: '16px 20px',
    backgroundColor: isActive ? '#4CAF50' : 'transparent',
    color: isActive ? 'white' : '#2C5530',
    border: 'none',
    borderRadius: '0',
    borderBottom: '1px solid #e0e0e0',
    cursor: 'pointer',
    fontSize: '1rem',
    fontWeight: 'bold',
    transition: 'all 0.3s ease',
    textAlign: 'left' as const,
    width: '100%',
    display: 'flex',
    alignItems: 'center',
    gap: '10px',
    boxShadow: isActive ? 'inset -4px 0 0 #2E7D32' : 'none'
  })



  return (
    <div style={{ 
      width: '100vw',
      height: '100vh',
      overflow: 'hidden',
      backgroundColor: '#f5f5f5',
      fontFamily: 'Arial, sans-serif',
      display: 'flex',
      flexDirection: 'column'
    }}>
      {/* 顶部标题栏 */}
      <header style={{ 
        backgroundColor: '#2C5530',
        color: 'white',
        padding: '15px 30px',
        boxShadow: '0 2px 8px rgba(0,0,0,0.2)',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        <h1 style={{ 
          fontSize: '1.8rem', 
          margin: 0,
          display: 'flex',
          alignItems: 'center',
          gap: '10px'
        }}>
          🌾 农产品期货游戏系统
        </h1>
        
        {/* 统计信息 */}
        <div style={{ 
          display: 'flex', 
          gap: '20px',
          fontSize: '0.9rem'
        }}>
          <div>📦 物品: {systemStats.totalItems}</div>
          <div>💰 价值: ¥{systemStats.totalValue.toLocaleString()}</div>
          <div>🌱 种植: {systemStats.plantsGrowing}</div>
          <div>⚗️ 合成: {systemStats.synthesesCompleted}</div>
          <div>🎁 开盒: {systemStats.lootboxesOpened}</div>
        </div>
      </header>

      {/* 主体内容区 - 左右布局 */}
      <div style={{ 
        flex: 1,
        display: 'flex',
        overflow: 'hidden'
      }}>
        {/* 左侧导航菜单 */}
        <div style={{ 
          width: '260px',
          backgroundColor: '#f8f9fa',
          borderRight: '2px solid #e0e0e0',
          display: 'flex',
          flexDirection: 'column',
          boxShadow: '2px 0 8px rgba(0,0,0,0.1)'
        }}>
          <div style={{
            padding: '20px',
            borderBottom: '2px solid #e0e0e0',
            backgroundColor: '#ffffff',
            fontSize: '1.1rem',
            fontWeight: 'bold',
            color: '#2C5530'
          }}>
            🎯 专注模式菜单
          </div>
          
          <nav style={{ flex: 1 }}>

            <button
              onClick={() => setActiveTab('lootbox')}
              style={sideMenuButtonStyle(activeTab === 'lootbox')}
              onMouseEnter={(e) => {
                if (activeTab !== 'lootbox') {
                  e.currentTarget.style.backgroundColor = '#e8f5e8'
                  e.currentTarget.style.transform = 'translateX(4px)'
                }
              }}
              onMouseLeave={(e) => {
                if (activeTab !== 'lootbox') {
                  e.currentTarget.style.backgroundColor = 'transparent'
                  e.currentTarget.style.transform = 'translateX(0)'
                }
              }}
            >
              <span>🎁</span>
              <span>期货盲盒</span>
            </button>
            <button
              onClick={() => setActiveTab('inventory')}
              style={sideMenuButtonStyle(activeTab === 'inventory')}
              onMouseEnter={(e) => {
                if (activeTab !== 'inventory') {
                  e.currentTarget.style.backgroundColor = '#e8f5e8'
                  e.currentTarget.style.transform = 'translateX(4px)'
                }
              }}
              onMouseLeave={(e) => {
                if (activeTab !== 'inventory') {
                  e.currentTarget.style.backgroundColor = 'transparent'
                  e.currentTarget.style.transform = 'translateX(0)'
                }
              }}
            >
              <span>🎒</span>
              <span>物品背包(含合成)</span>
            </button>
            <button
              onClick={() => setActiveTab('planting')}
              style={sideMenuButtonStyle(activeTab === 'planting')}
              onMouseEnter={(e) => {
                if (activeTab !== 'planting') {
                  e.currentTarget.style.backgroundColor = '#e8f5e8'
                  e.currentTarget.style.transform = 'translateX(4px)'
                }
              }}
              onMouseLeave={(e) => {
                if (activeTab !== 'planting') {
                  e.currentTarget.style.backgroundColor = 'transparent'
                  e.currentTarget.style.transform = 'translateX(0)'
                }
              }}
            >
              <span>🌾</span>
              <span>农场收集系统</span>
            </button>
          </nav>
        </div>

        {/* 右侧内容区域 */}
        <div style={{ 
          flex: 1,
          backgroundColor: 'white',
          padding: '20px',
          overflow: 'auto',
          display: 'flex',
          flexDirection: 'column'
        }}>


          {/* 期货盲盒 */}
          {activeTab === 'lootbox' && itemManagerRef.current && (
            <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
              <LootboxTester 
                onItemsReceived={handleLootboxOpened}
                itemManager={itemManagerRef.current}
              />
            </div>
          )}

          {/* 物品背包(含合成) */}
          {activeTab === 'inventory' && itemManagerRef.current && (
            <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
              <ChineseFuturesInventory 
                itemManager={itemManagerRef.current}
                className="game-inventory"
              />
            </div>
          )}

          {/* 农场收集系统 */}
          {activeTab === 'planting' && (
            <div style={{ 
              height: '100%', 
              display: 'flex', 
              flexDirection: 'column'
            }}>
              {/* 种植系统容器 */}
              <div style={{ 
                flex: 1,
                background: 'white',
                overflow: 'auto',
                minHeight: 0
              }}>
                <EnhancedFarmUI />
              </div>
            </div>
          )}
        </div>
      </div>

      {/* 底部提示 */}
      <footer style={{ 
        backgroundColor: '#333',
        color: '#ccc',
        padding: '10px',
        textAlign: 'center',
        fontSize: '0.8rem'
      }}>
        💡 提示：通过盲盒获得农产品 → 在背包中拖拽相同品种合成升级 → 在农场种植收获 → 体验完整的农场收集系统
      </footer>
    </div>
  )
}

export default UnifiedGameSystem 