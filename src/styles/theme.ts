// 设计系统主题配置
export const theme = {
  // 颜色系统
  colors: {
    // 主色调 - 绿色系（代表生长与生命）
    primary: {
      50: '#F0FDF4',
      100: '#DCFCE7',
      200: '#BBF7D0',
      300: '#86EFAC',
      400: '#4ADE80',
      500: '#22C55E',
      600: '#16A34A',
      700: '#15803D',
      800: '#166534',
      900: '#14532D',
    },
    
    // 辅助色 - 蓝色系（代表专注与冷静）
    secondary: {
      50: '#EFF6FF',
      100: '#DBEAFE',
      200: '#BFDBFE',
      300: '#93C5FD',
      400: '#60A5FA',
      500: '#3B82F6',
      600: '#2563EB',
      700: '#1D4ED8',
      800: '#1E40AF',
      900: '#1E3A8A',
    },
    
    // 警告色 - 黄色系
    warning: {
      50: '#FFFBEB',
      100: '#FEF3C7',
      200: '#FDE68A',
      300: '#FCD34D',
      400: '#FBBF24',
      500: '#F59E0B',
      600: '#D97706',
      700: '#B45309',
      800: '#92400E',
      900: '#78350F',
    },
    
    // 错误色 - 红色系
    error: {
      50: '#FEF2F2',
      100: '#FEE2E2',
      200: '#FECACA',
      300: '#FCA5A5',
      400: '#F87171',
      500: '#EF4444',
      600: '#DC2626',
      700: '#B91C1C',
      800: '#991B1B',
      900: '#7F1D1D',
    },
    
    // 成功色 - 绿色系（同主色）
    success: {
      50: '#F0FDF4',
      100: '#DCFCE7',
      200: '#BBF7D0',
      300: '#86EFAC',
      400: '#4ADE80',
      500: '#22C55E',
      600: '#16A34A',
      700: '#15803D',
      800: '#166534',
      900: '#14532D',
    },
    
    // 中性色系
    gray: {
      50: '#F9FAFB',
      100: '#F3F4F6',
      200: '#E5E7EB',
      300: '#D1D5DB',
      400: '#9CA3AF',
      500: '#6B7280',
      600: '#4B5563',
      700: '#374151',
      800: '#1F2937',
      900: '#111827',
    },
    
    // 语义化颜色
    background: '#FAFAFA',
    surface: '#FFFFFF',
    text: {
      primary: '#1F2937',
      secondary: '#6B7280',
      disabled: '#9CA3AF',
      inverse: '#FFFFFF',
    },
    
    // 游戏特定颜色
    game: {
      soil: '#8B4513',
      grass: '#90EE90',
      sky: '#87CEEB',
      sun: '#FFD700',
      water: '#4169E1',
    }
  },
  
  // 字体系统
  typography: {
    fontFamily: {
      primary: '"Inter", "Segoe UI", "Roboto", "Arial", sans-serif',
      mono: '"Fira Code", "Monaco", "Courier New", monospace',
    },
    fontSize: {
      xs: '0.75rem',    // 12px
      sm: '0.875rem',   // 14px
      base: '1rem',     // 16px
      lg: '1.125rem',   // 18px
      xl: '1.25rem',    // 20px
      '2xl': '1.5rem',  // 24px
      '3xl': '1.875rem', // 30px
      '4xl': '2.25rem', // 36px
      '5xl': '3rem',    // 48px
    },
    fontWeight: {
      light: 300,
      normal: 400,
      medium: 500,
      semibold: 600,
      bold: 700,
      extrabold: 800,
    },
    lineHeight: {
      tight: 1.25,
      normal: 1.5,
      relaxed: 1.75,
    }
  },
  
  // 间距系统（基于 4px 网格）
  spacing: {
    0: '0',
    1: '0.25rem',   // 4px
    2: '0.5rem',    // 8px
    3: '0.75rem',   // 12px
    4: '1rem',      // 16px
    5: '1.25rem',   // 20px
    6: '1.5rem',    // 24px
    8: '2rem',      // 32px
    10: '2.5rem',   // 40px
    12: '3rem',     // 48px
    16: '4rem',     // 64px
    20: '5rem',     // 80px
    24: '6rem',     // 96px
  },
  
  // 圆角系统
  borderRadius: {
    none: '0',
    xs: '0.125rem',   // 2px
    sm: '0.25rem',    // 4px
    base: '0.375rem', // 6px
    md: '0.5rem',     // 8px
    lg: '0.75rem',    // 12px
    xl: '1rem',       // 16px
    '2xl': '1.5rem',  // 24px
    full: '9999px',
  },
  
  // 阴影系统
  boxShadow: {
    xs: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
    sm: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
    base: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
    md: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
    lg: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
    xl: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
    inner: 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.06)',
    glow: '0 0 20px rgba(34, 197, 94, 0.4)',
    none: 'none',
  },
  
  // 过渡动画
  transition: {
    fast: '150ms cubic-bezier(0.4, 0, 0.2, 1)',
    base: '250ms cubic-bezier(0.4, 0, 0.2, 1)',
    slow: '350ms cubic-bezier(0.4, 0, 0.2, 1)',
  },
  
  // 层级系统
  zIndex: {
    hide: -1,
    base: 0,
    dropdown: 1000,
    modal: 1200,
    popover: 1300,
    tooltip: 1400,
    toast: 1500,
  },
  
  // 断点系统
  breakpoints: {
    sm: '640px',
    md: '768px',
    lg: '1024px',
    xl: '1280px',
    '2xl': '1536px',
  },
  
  // 组件特定设计令牌
  components: {
    button: {
      height: {
        sm: '2rem',     // 32px
        base: '2.5rem', // 40px
        lg: '3rem',     // 48px
      },
      padding: {
        sm: '0.5rem 0.75rem',
        base: '0.75rem 1rem',
        lg: '1rem 1.5rem',
      }
    },
    input: {
      height: {
        sm: '2rem',
        base: '2.5rem',
        lg: '3rem',
      }
    },
    card: {
      padding: '1.5rem',
      borderRadius: '0.75rem',
    }
  }
} as const

// 类型定义
export type Theme = typeof theme
export type ColorScale = keyof typeof theme.colors.primary
export type ColorName = keyof typeof theme.colors

// 辅助函数
export const getColor = (color: string, scale?: number): string => {
  const colorPath = color.split('.')
  let colorValue: any = theme.colors
  
  for (const path of colorPath) {
    colorValue = colorValue[path]
  }
  
  if (scale && typeof colorValue === 'object') {
    return colorValue[scale] || colorValue[500]
  }
  
  return colorValue || color
}

export const getSpacing = (space: keyof typeof theme.spacing): string => {
  return theme.spacing[space]
}

export const getFontSize = (size: keyof typeof theme.typography.fontSize): string => {
  return theme.typography.fontSize[size]
}

// CSS-in-JS 帮助函数
export const createGradient = (from: string, to: string, direction = '135deg'): string => {
  return `linear-gradient(${direction}, ${from}, ${to})`
}

export const createBoxShadow = (shadowKey: keyof typeof theme.boxShadow): string => {
  return theme.boxShadow[shadowKey]
} 