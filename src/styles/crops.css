/* 作物视觉样式系统 */

:root {
  --crop-base-scale: 1.0;
  --growth-duration: 1.5s;
  --particle-duration: 2.0s;
  --quality-glow-duration: 0.8s;
}

/* ===================== 作物基础样式 ===================== */

.crop-container {
  position: relative;
  width: 64px;
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: visible;
}

.crop-sprite {
  position: relative;
  width: 100%;
  height: 100%;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  transition: all var(--growth-duration) cubic-bezier(0.4, 0.0, 0.2, 1);
  filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.2));
}

/* ===================== 传统作物类型 ===================== */

/* 知识花 (Knowledge Flower) */
.crop-knowledge-flower {
  background-image: linear-gradient(45deg, #ff6b6b, #4ecdc4);
  border-radius: 50%;
}

.crop-knowledge-flower.seed {
  background: radial-gradient(circle, #8b4513, #654321);
  transform: scale(0.3);
  border-radius: 50%;
}

.crop-knowledge-flower.sprout {
  background: linear-gradient(45deg, #90ee90, #32cd32);
  transform: scale(0.5);
  animation: sprout-wiggle 2s ease-in-out infinite;
}

.crop-knowledge-flower.growing {
  background: linear-gradient(45deg, #ffd700, #ff6b6b);
  transform: scale(0.8);
  animation: growing-pulse 1.5s ease-in-out infinite;
}

.crop-knowledge-flower.mature {
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
  transform: scale(1.0);
  animation: mature-glow 2s ease-in-out infinite;
}

.crop-knowledge-flower.ready {
  background: linear-gradient(45deg, #ffd700, #ff1493);
  transform: scale(1.2);
  animation: ready-sparkle 1s ease-in-out infinite;
  box-shadow: 0 0 20px rgba(255, 212, 0, 0.8);
}

/* 力量树 (Strength Tree) */
.crop-strength-tree {
  background-image: linear-gradient(180deg, #8B4513 60%, #228B22 40%);
  border-radius: 10px 10px 50% 50%;
}

.crop-strength-tree.seed {
  background: radial-gradient(circle, #654321, #8b4513);
  transform: scale(0.3);
  border-radius: 50%;
}

.crop-strength-tree.sprout {
  background: linear-gradient(180deg, #daa520, #228b22);
  transform: scale(0.5);
  animation: tree-sway 3s ease-in-out infinite;
}

.crop-strength-tree.growing {
  background: linear-gradient(180deg, #8b4513 50%, #228b22 50%);
  transform: scale(0.8);
  animation: tree-grow 2s ease-in-out infinite;
}

.crop-strength-tree.mature {
  background: linear-gradient(180deg, #8b4513 60%, #228b22 40%);
  transform: scale(1.0);
  animation: tree-strong 2.5s ease-in-out infinite;
}

.crop-strength-tree.ready {
  background: linear-gradient(180deg, #8b4513 60%, #00ff00 40%);
  transform: scale(1.3);
  animation: tree-power 1.2s ease-in-out infinite;
  box-shadow: 0 0 25px rgba(0, 255, 0, 0.6);
}

/* 时间菜 (Time Veggie) */
.crop-time-veggie {
  background: linear-gradient(135deg, #ff7f50, #32cd32);
  border-radius: 20px;
}

.crop-time-veggie.seed {
  background: radial-gradient(circle, #654321, #8b4513);
  transform: scale(0.3);
  border-radius: 50%;
}

.crop-time-veggie.sprout {
  background: linear-gradient(135deg, #98fb98, #90ee90);
  transform: scale(0.5);
  animation: veggie-emerge 2.5s ease-in-out infinite;
}

.crop-time-veggie.growing {
  background: linear-gradient(135deg, #ffa500, #32cd32);
  transform: scale(0.8);
  animation: veggie-grow 2s ease-in-out infinite;
}

.crop-time-veggie.mature {
  background: linear-gradient(135deg, #ff7f50, #32cd32);
  transform: scale(1.0);
  animation: veggie-ripe 1.8s ease-in-out infinite;
}

.crop-time-veggie.ready {
  background: linear-gradient(135deg, #ff4500, #00ff00);
  transform: scale(1.1);
  animation: veggie-harvest 1s ease-in-out infinite;
  box-shadow: 0 0 20px rgba(255, 69, 0, 0.7);
}

/* 冥想莲 (Meditation Lotus) */
.crop-meditation-lotus {
  background: radial-gradient(circle, #dda0dd, #9370db);
  border-radius: 50%;
}

.crop-meditation-lotus.seed {
  background: radial-gradient(circle, #654321, #8b4513);
  transform: scale(0.3);
  border-radius: 50%;
}

.crop-meditation-lotus.sprout {
  background: radial-gradient(circle, #e6e6fa, #dda0dd);
  transform: scale(0.5);
  animation: lotus-float 3s ease-in-out infinite;
}

.crop-meditation-lotus.growing {
  background: radial-gradient(circle, #dda0dd, #9370db);
  transform: scale(0.8);
  animation: lotus-bloom 2.5s ease-in-out infinite;
}

.crop-meditation-lotus.mature {
  background: radial-gradient(circle, #dda0dd, #9370db);
  transform: scale(1.0);
  animation: lotus-meditate 3s ease-in-out infinite;
}

.crop-meditation-lotus.ready {
  background: radial-gradient(circle, #ff69b4, #9370db);
  transform: scale(1.2);
  animation: lotus-enlighten 1.5s ease-in-out infinite;
  box-shadow: 0 0 30px rgba(221, 160, 221, 0.9);
}

/* ===================== 新作物类型 ===================== */

/* 专注花 (Focus Flower) */
.crop-focus-flower {
  background: radial-gradient(circle, #00bfff, #1e90ff);
  border-radius: 50%;
  position: relative;
}

.crop-focus-flower.seed {
  background: radial-gradient(circle, #654321, #8b4513);
  transform: scale(0.3);
  border-radius: 50%;
}

.crop-focus-flower.sprout {
  background: radial-gradient(circle, #87ceeb, #4682b4);
  transform: scale(0.5);
  animation: focus-emerge 2s ease-in-out infinite;
}

.crop-focus-flower.growing {
  background: radial-gradient(circle, #00bfff, #1e90ff);
  transform: scale(0.8);
  animation: focus-concentrate 1.8s ease-in-out infinite;
}

.crop-focus-flower.mature {
  background: radial-gradient(circle, #00bfff, #0066cc);
  transform: scale(1.0);
  animation: focus-intensity 2s ease-in-out infinite;
}

.crop-focus-flower.ready {
  background: radial-gradient(circle, #00ffff, #0066ff);
  transform: scale(1.3);
  animation: focus-mastery 1s ease-in-out infinite;
  box-shadow: 0 0 35px rgba(0, 191, 255, 0.8);
}

/* 读书藤 (Reading Vine) */
.crop-reading-vine {
  background: linear-gradient(45deg, #228b22, #32cd32);
  border-radius: 10px;
  position: relative;
}

.crop-reading-vine.seed {
  background: radial-gradient(circle, #654321, #8b4513);
  transform: scale(0.3);
  border-radius: 50%;
}

.crop-reading-vine.sprout {
  background: linear-gradient(45deg, #90ee90, #228b22);
  transform: scale(0.5);
  animation: vine-crawl 3s ease-in-out infinite;
}

.crop-reading-vine.growing {
  background: linear-gradient(45deg, #228b22, #32cd32);
  transform: scale(0.8);
  animation: vine-climb 2.5s ease-in-out infinite;
}

.crop-reading-vine.mature {
  background: linear-gradient(45deg, #228b22, #32cd32);
  transform: scale(1.0);
  animation: vine-flourish 2s ease-in-out infinite;
}

.crop-reading-vine.ready {
  background: linear-gradient(45deg, #00ff00, #adff2f);
  transform: scale(1.2);
  animation: vine-wisdom 1.3s ease-in-out infinite;
  box-shadow: 0 0 25px rgba(34, 139, 34, 0.7);
}

/* 社交果 (Social Fruit) */
.crop-social-fruit {
  background: radial-gradient(circle, #ff69b4, #ff1493);
  border-radius: 40%;
}

.crop-social-fruit.seed {
  background: radial-gradient(circle, #654321, #8b4513);
  transform: scale(0.3);
  border-radius: 50%;
}

.crop-social-fruit.sprout {
  background: radial-gradient(circle, #ffb6c1, #ff69b4);
  transform: scale(0.5);
  animation: fruit-bud 2.5s ease-in-out infinite;
}

.crop-social-fruit.growing {
  background: radial-gradient(circle, #ff69b4, #ff1493);
  transform: scale(0.8);
  animation: fruit-develop 2s ease-in-out infinite;
}

.crop-social-fruit.mature {
  background: radial-gradient(circle, #ff69b4, #ff1493);
  transform: scale(1.0);
  animation: fruit-ripen 1.8s ease-in-out infinite;
}

.crop-social-fruit.ready {
  background: radial-gradient(circle, #ff1493, #dc143c);
  transform: scale(1.2);
  animation: fruit-connect 1.2s ease-in-out infinite;
  box-shadow: 0 0 28px rgba(255, 105, 180, 0.8);
}

/* ===================== 动画关键帧 ===================== */

@keyframes sprout-wiggle {
  0%, 100% { transform: scale(0.5) rotate(0deg); }
  25% { transform: scale(0.5) rotate(-2deg); }
  75% { transform: scale(0.5) rotate(2deg); }
}

@keyframes growing-pulse {
  0%, 100% { transform: scale(0.8) scaleY(1); }
  50% { transform: scale(0.8) scaleY(1.1); }
}

@keyframes mature-glow {
  0%, 100% { transform: scale(1.0); filter: brightness(1) drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.2)); }
  50% { transform: scale(1.05); filter: brightness(1.2) drop-shadow(2px 2px 8px rgba(255, 212, 0, 0.4)); }
}

@keyframes ready-sparkle {
  0%, 100% { transform: scale(1.2) rotate(0deg); }
  25% { transform: scale(1.25) rotate(1deg); }
  75% { transform: scale(1.15) rotate(-1deg); }
}

@keyframes tree-sway {
  0%, 100% { transform: scale(0.5) rotate(0deg); }
  50% { transform: scale(0.5) rotate(2deg); }
}

@keyframes tree-grow {
  0%, 100% { transform: scale(0.8) scaleY(1); }
  50% { transform: scale(0.8) scaleY(1.15); }
}

@keyframes tree-strong {
  0%, 100% { transform: scale(1.0); }
  50% { transform: scale(1.05) scaleX(0.95); }
}

@keyframes tree-power {
  0%, 100% { transform: scale(1.3); }
  50% { transform: scale(1.35) scaleY(0.95); }
}

@keyframes veggie-emerge {
  0%, 100% { transform: scale(0.5) translateY(2px); }
  50% { transform: scale(0.5) translateY(-2px); }
}

@keyframes veggie-grow {
  0%, 100% { transform: scale(0.8); }
  50% { transform: scale(0.85) scaleY(1.1); }
}

@keyframes veggie-ripe {
  0%, 100% { transform: scale(1.0); }
  50% { transform: scale(1.05); }
}

@keyframes veggie-harvest {
  0%, 100% { transform: scale(1.1); }
  50% { transform: scale(1.15) scaleY(0.95); }
}

@keyframes lotus-float {
  0%, 100% { transform: scale(0.5) translateY(0px); }
  50% { transform: scale(0.5) translateY(-3px); }
}

@keyframes lotus-bloom {
  0%, 100% { transform: scale(0.8) rotate(0deg); }
  50% { transform: scale(0.8) rotate(2deg); }
}

@keyframes lotus-meditate {
  0%, 100% { transform: scale(1.0); filter: brightness(1); }
  50% { transform: scale(1.02); filter: brightness(1.1); }
}

@keyframes lotus-enlighten {
  0%, 100% { transform: scale(1.2); filter: brightness(1) saturate(1); }
  50% { transform: scale(1.25); filter: brightness(1.3) saturate(1.2); }
}

@keyframes focus-emerge {
  0%, 100% { transform: scale(0.5); filter: blur(1px); }
  50% { transform: scale(0.5); filter: blur(0px); }
}

@keyframes focus-concentrate {
  0%, 100% { transform: scale(0.8); }
  50% { transform: scale(0.82); filter: contrast(1.1); }
}

@keyframes focus-intensity {
  0%, 100% { transform: scale(1.0); filter: brightness(1); }
  50% { transform: scale(1.03); filter: brightness(1.15); }
}

@keyframes focus-mastery {
  0%, 100% { transform: scale(1.3); filter: brightness(1) contrast(1); }
  50% { transform: scale(1.35); filter: brightness(1.3) contrast(1.2); }
}

@keyframes vine-crawl {
  0%, 100% { transform: scale(0.5) skewX(0deg); }
  25% { transform: scale(0.5) skewX(2deg); }
  75% { transform: scale(0.5) skewX(-2deg); }
}

@keyframes vine-climb {
  0%, 100% { transform: scale(0.8) scaleY(1); }
  50% { transform: scale(0.8) scaleY(1.1); }
}

@keyframes vine-flourish {
  0%, 100% { transform: scale(1.0); }
  25% { transform: scale(1.02) skewX(1deg); }
  75% { transform: scale(1.02) skewX(-1deg); }
}

@keyframes vine-wisdom {
  0%, 100% { transform: scale(1.2); }
  50% { transform: scale(1.25) scaleY(1.05); }
}

@keyframes fruit-bud {
  0%, 100% { transform: scale(0.5); border-radius: 50%; }
  50% { transform: scale(0.5); border-radius: 40%; }
}

@keyframes fruit-develop {
  0%, 100% { transform: scale(0.8); }
  50% { transform: scale(0.85); }
}

@keyframes fruit-ripen {
  0%, 100% { transform: scale(1.0); filter: saturate(1); }
  50% { transform: scale(1.03); filter: saturate(1.2); }
}

@keyframes fruit-connect {
  0%, 100% { transform: scale(1.2); }
  33% { transform: scale(1.22) rotate(1deg); }
  66% { transform: scale(1.18) rotate(-1deg); }
}

/* ===================== 品质效果 ===================== */

.crop-quality-common {
  filter: brightness(1) saturate(1);
}

.crop-quality-uncommon {
  filter: brightness(1.1) saturate(1.1);
  animation: quality-shimmer 3s ease-in-out infinite;
}

.crop-quality-rare {
  filter: brightness(1.2) saturate(1.2);
  animation: quality-rare-glow 2.5s ease-in-out infinite;
}

.crop-quality-epic {
  filter: brightness(1.3) saturate(1.3);
  animation: quality-epic-pulse 2s ease-in-out infinite;
}

.crop-quality-legendary {
  filter: brightness(1.4) saturate(1.4);
  animation: quality-legendary-aurora 1.5s ease-in-out infinite;
}

@keyframes quality-shimmer {
  0%, 100% { box-shadow: 0 0 5px rgba(255, 255, 255, 0.3); }
  50% { box-shadow: 0 0 15px rgba(255, 255, 255, 0.6); }
}

@keyframes quality-rare-glow {
  0%, 100% { box-shadow: 0 0 10px rgba(0, 100, 255, 0.4); }
  50% { box-shadow: 0 0 20px rgba(0, 100, 255, 0.8); }
}

@keyframes quality-epic-pulse {
  0%, 100% { box-shadow: 0 0 15px rgba(128, 0, 128, 0.5); }
  50% { box-shadow: 0 0 30px rgba(128, 0, 128, 1.0); }
}

@keyframes quality-legendary-aurora {
  0% { box-shadow: 0 0 20px rgba(255, 215, 0, 0.8); }
  25% { box-shadow: 0 0 25px rgba(255, 0, 255, 0.8); }
  50% { box-shadow: 0 0 20px rgba(0, 255, 255, 0.8); }
  75% { box-shadow: 0 0 25px rgba(255, 105, 180, 0.8); }
  100% { box-shadow: 0 0 20px rgba(255, 215, 0, 0.8); }
}

/* ===================== 粒子效果 ===================== */

.crop-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  overflow: visible;
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  animation: particle-float var(--particle-duration) ease-out infinite;
}

.particle.knowledge { background: rgba(255, 107, 107, 0.8); }
.particle.strength { background: rgba(0, 255, 0, 0.8); }
.particle.time { background: rgba(255, 165, 0, 0.8); }
.particle.meditation { background: rgba(221, 160, 221, 0.8); }
.particle.focus { background: rgba(0, 191, 255, 0.8); }
.particle.reading { background: rgba(34, 139, 34, 0.8); }
.particle.social { background: rgba(255, 105, 180, 0.8); }

@keyframes particle-float {
  0% {
    transform: translateY(0px) scale(0);
    opacity: 0;
  }
  10% {
    transform: translateY(-5px) scale(1);
    opacity: 1;
  }
  90% {
    transform: translateY(-30px) scale(1);
    opacity: 1;
  }
  100% {
    transform: translateY(-40px) scale(0);
    opacity: 0;
  }
}

/* ===================== 特殊状态效果 ===================== */

.crop-paused {
  filter: grayscale(0.5) brightness(0.8);
  animation: paused-pulse 2s ease-in-out infinite;
}

@keyframes paused-pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

.crop-boosted {
  animation: boosted-energy 1s ease-in-out infinite;
}

@keyframes boosted-energy {
  0%, 100% { transform: scale(var(--crop-base-scale)); filter: brightness(1); }
  50% { transform: scale(calc(var(--crop-base-scale) * 1.1)); filter: brightness(1.3); }
}

.crop-withering {
  filter: sepia(0.5) brightness(0.6);
  animation: wither-fade 3s ease-in-out infinite;
}

@keyframes wither-fade {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* ===================== 响应式设计 ===================== */

@media (max-width: 768px) {
  .crop-container {
    width: 48px;
    height: 48px;
  }
  
  :root {
    --crop-base-scale: 0.8;
    --growth-duration: 1.2s;
    --particle-duration: 1.5s;
  }
}

@media (prefers-reduced-motion: reduce) {
  .crop-sprite,
  .crop-container * {
    animation: none !important;
    transition: none !important;
  }
} 