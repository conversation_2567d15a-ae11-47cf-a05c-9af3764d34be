import * as Phaser from 'phaser'
import { EnhancedFarmScene } from './scenes/EnhancedFarmScene'
import { HarvestTestScene } from './scenes/HarvestTestScene'
import { PlantingTestScene } from './scenes/PlantingTestScene'
import { StateTestScene } from './scenes/StateTestScene'
import { AnimationTestScene } from './scenes/AnimationTestScene'
import { AgriculturalFarmScene } from './scenes/AgriculturalFarmScene'
import { UnifiedAgriculturalScene } from './scenes/UnifiedAgriculturalScene'

export const gameConfig: Phaser.Types.Core.GameConfig = {
  type: Phaser.AUTO,
  width: 1200,
  height: 800,
  backgroundColor: 0x87CEEB,
  parent: 'game-container',
  scale: {
    mode: Phaser.Scale.FIT,
    autoCenter: Phaser.Scale.CENTER_BOTH,
    width: 1200,
    height: 800
  },
  physics: {
    default: 'arcade',
    arcade: {
      debug: false,
      gravity: { x: 0, y: 0 }
    }
  },
  scene: [
    UnifiedAgriculturalScene,
    EnhancedFarmScene,
    HarvestTestScene,
    PlantingTestScene,
    StateTestScene,
    AnimationTestScene,
    AgriculturalFarmScene
  ],
  input: {
    mouse: true,
    touch: true,
    keyboard: true
  },
  render: {
    antialias: true,
    pixelArt: false
  }
}

export class AgricultureGame {
  private game: Phaser.Game | null = null

  constructor() {
    this.initGame()
  }

  private initGame(): void {
    if (this.game) {
      this.game.destroy(true)
    }

    this.game = new Phaser.Game(gameConfig)
  }

  public getGame(): Phaser.Game | null {
    return this.game
  }

  public destroy(): void {
    if (this.game) {
      this.game.destroy(true)
      this.game = null
    }
  }

  public restart(): void {
    this.destroy()
    this.initGame()
  }
} 