import Phaser from 'phaser'
import { 
  CropInstance, 
  CropStage, 
  CropType, 
  CropQuality,
  CROP_CONFIGS 
} from '../../types/crop'
import { calculateGrowthProgress } from '../../types/crop'

// 动画配置
export const ANIMATION_CONFIG = {
  // 生长动画持续时间
  GROWTH_DURATION: 800,
  
  // 摇摆动画配置
  SWAY_AMPLITUDE: 3, // 摇摆幅度（像素）
  SWAY_FREQUENCY: 2000, // 摇摆周期（毫秒）
  
  // 闪烁效果
  HIGHLIGHT_DURATION: 300,
  HARVEST_GLOW_DURATION: 1500,
  
  // 粒子效果
  PARTICLE_COUNT: 8,
  PARTICLE_LIFESPAN: 2000,
  
  // 缩放动画
  SCALE_BOUNCE: 0.1,
  SCALE_DURATION: 400
} as const

// 粒子类型
export enum ParticleType {
  GROWTH = 'growth',
  SPARKLE = 'sparkle',
  HARVEST = 'harvest',
  QUALITY_BOOST = 'quality_boost'
}

// 作物精灵类
export class CropSprite extends Phaser.GameObjects.Container {
  private mainSprite!: Phaser.GameObjects.Graphics
  private glowSprite!: Phaser.GameObjects.Graphics
  private progressBar!: Phaser.GameObjects.Graphics
  private qualityIndicator!: Phaser.GameObjects.Text
  private particles: Phaser.GameObjects.Particles.ParticleEmitter[]
  
  private currentStage: CropStage
  private currentQuality: CropQuality
  private animationTween: Phaser.Tweens.Tween | null = null
  private swayTween: Phaser.Tweens.Tween | null = null
  private glowTween: Phaser.Tweens.Tween | null = null
  
  // 视觉状态
  private isHighlighted: boolean = false
  private isHarvestReady: boolean = false
  private lastUpdateTime: number = 0
  
  constructor(
    scene: Phaser.Scene,
    x: number,
    y: number,
    public cropData: CropInstance
  ) {
    super(scene, x, y)
    
    this.currentStage = cropData.stage
    this.currentQuality = cropData.quality
    this.particles = []
    
    this.createVisualElements()
    this.setupInteractivity()
    this.startIdleAnimations()
    
    scene.add.existing(this)
  }
  
  /**
   * 创建视觉元素
   */
  private createVisualElements(): void {
    // 主要精灵
    this.mainSprite = this.scene.add.graphics()
    this.add(this.mainSprite)
    
    // 发光效果
    this.glowSprite = this.scene.add.graphics()
    this.add(this.glowSprite)
    
    // 进度条
    this.progressBar = this.scene.add.graphics()
    this.progressBar.setPosition(0, 40)
    this.add(this.progressBar)
    
    // 品质指示器
    this.qualityIndicator = this.scene.add.text(0, -45, '', {
      fontSize: '12px',
      color: '#ffffff',
      stroke: '#000000',
      strokeThickness: 2
    }).setOrigin(0.5)
    this.add(this.qualityIndicator)
    
    // 初始绘制
    this.updateVisuals()
  }
  
  /**
   * 设置交互性
   */
  private setupInteractivity(): void {
    this.setSize(60, 60)
    this.setInteractive()
    
    // 鼠标悬停效果
    this.on('pointerover', () => {
      this.setHighlight(true)
      this.scene.input.setDefaultCursor('pointer')
    })
    
    this.on('pointerout', () => {
      this.setHighlight(false)
      this.scene.input.setDefaultCursor('default')
    })
    
    // 点击事件
    this.on('pointerdown', () => {
      this.onCropClicked()
    })
  }
  
  /**
   * 更新作物数据
   */
  updateCropData(cropData: CropInstance): void {
    const oldStage = this.currentStage
    const oldQuality = this.currentQuality
    
    this.cropData = cropData
    this.currentStage = cropData.stage
    this.currentQuality = cropData.quality
    
    // 检查阶段变化
    if (oldStage !== this.currentStage) {
      this.playStageTransitionAnimation(oldStage, this.currentStage)
    }
    
    // 检查品质变化
    if (oldQuality !== this.currentQuality) {
      this.playQualityUpgradeAnimation()
    }
    
    // 更新收获状态
    if (cropData.harvestable && !this.isHarvestReady) {
      this.setHarvestReady(true)
    }
    
    this.updateVisuals()
  }
  
  /**
   * 更新视觉效果
   */
  private updateVisuals(): void {
    this.drawMainSprite()
    this.updateProgressBar()
    this.updateQualityIndicator()
    this.updateGlowEffect()
  }
  
  /**
   * 绘制主要精灵
   */
  private drawMainSprite(): void {
    const config = CROP_CONFIGS[this.cropData.type]
    const stageConfig = config.stages.find(s => s.stage === this.currentStage)
    
    if (!stageConfig) return
    
    this.mainSprite.clear()
    
    // 根据作物类型和阶段绘制不同形状
    const scale = stageConfig.visualScale
    const size = 30 * scale
    
    // 获取作物颜色
    const colors = this.getCropColors()
    
    // 绘制作物形状
    switch (this.cropData.type) {
      case CropType.KNOWLEDGE_FLOWER:
        this.drawFlower(colors, size)
        break
      case CropType.STRENGTH_TREE:
        this.drawTree(colors, size)
        break
      case CropType.TIME_VEGGIE:
        this.drawVegetable(colors, size)
        break
      case CropType.MEDITATION_LOTUS:
        this.drawLotus(colors, size)
        break
    }
  }
  
  /**
   * 获取作物颜色配置
   */
  private getCropColors(): { primary: number; secondary: number; accent: number } {
    const qualityColors = {
      [CropQuality.COMMON]: { primary: 0x90EE90, secondary: 0x228B22, accent: 0xFFFFFF },
      [CropQuality.UNCOMMON]: { primary: 0x87CEEB, secondary: 0x4682B4, accent: 0xF0F8FF },
      [CropQuality.RARE]: { primary: 0xFF69B4, secondary: 0xFF1493, accent: 0xFFB6C1 },
      [CropQuality.EPIC]: { primary: 0x9370DB, secondary: 0x8A2BE2, accent: 0xE6E6FA },
      [CropQuality.LEGENDARY]: { primary: 0xFFD700, secondary: 0xFF8C00, accent: 0xFFFACD }
    }
    
    return qualityColors[this.currentQuality]
  }
  
  /**
   * 绘制花朵形状
   */
  private drawFlower(colors: any, size: number): void {
    const { primary, secondary, accent } = colors
    
    // 花茎
    this.mainSprite.fillStyle(secondary)
    this.mainSprite.fillRect(-2, 10, 4, size * 0.6)
    
    // 花瓣
    this.mainSprite.fillStyle(primary)
    const petalCount = this.currentStage === CropStage.SEED ? 0 : 
                      this.currentStage === CropStage.SPROUT ? 3 : 5
    
    for (let i = 0; i < petalCount; i++) {
      const angle = (i / petalCount) * Math.PI * 2
      const petalX = Math.cos(angle) * size * 0.3
      const petalY = Math.sin(angle) * size * 0.3
      this.mainSprite.fillCircle(petalX, petalY - 10, size * 0.2)
    }
    
    // 花心
    if (petalCount > 0) {
      this.mainSprite.fillStyle(accent)
      this.mainSprite.fillCircle(0, -10, size * 0.1)
    }
    
    // 种子状态特殊处理
    if (this.currentStage === CropStage.SEED) {
      this.mainSprite.fillStyle(0x8B4513)
      this.mainSprite.fillCircle(0, 0, size * 0.3)
    }
  }
  
  /**
   * 绘制树木形状
   */
  private drawTree(colors: any, size: number): void {
    const { primary, secondary } = colors
    
    // 树干
    this.mainSprite.fillStyle(secondary)
    const trunkWidth = Math.max(4, size * 0.2)
    const trunkHeight = size * 0.8
    this.mainSprite.fillRect(-trunkWidth/2, 0, trunkWidth, trunkHeight)
    
    // 树冠
    if (this.currentStage !== CropStage.SEED) {
      this.mainSprite.fillStyle(primary)
      const crownSize = size * (this.currentStage === CropStage.SPROUT ? 0.4 : 0.8)
      this.mainSprite.fillCircle(0, -crownSize * 0.3, crownSize)
    } else {
      // 种子状态
      this.mainSprite.fillStyle(0x8B4513)
      this.mainSprite.fillCircle(0, 0, size * 0.3)
    }
  }
  
  /**
   * 绘制蔬菜形状
   */
  private drawVegetable(colors: any, size: number): void {
    const { primary, secondary } = colors
    
    if (this.currentStage === CropStage.SEED) {
      this.mainSprite.fillStyle(0x8B4513)
      this.mainSprite.fillCircle(0, 0, size * 0.3)
      return
    }
    
    // 叶子
    this.mainSprite.fillStyle(primary)
    const leafCount = Math.min(6, Math.floor(size / 8))
    
    for (let i = 0; i < leafCount; i++) {
      const angle = (i / leafCount) * Math.PI * 2
      const leafX = Math.cos(angle) * size * 0.2
      const leafY = Math.sin(angle) * size * 0.2
      
      // 绘制椭圆形叶子
      this.mainSprite.fillEllipse(leafX, leafY, size * 0.3, size * 0.15)
    }
    
    // 中心
    this.mainSprite.fillStyle(secondary)
    this.mainSprite.fillCircle(0, 0, size * 0.15)
  }
  
  /**
   * 绘制莲花形状
   */
  private drawLotus(colors: any, size: number): void {
    const { primary, secondary, accent } = colors
    
    if (this.currentStage === CropStage.SEED) {
      this.mainSprite.fillStyle(0x8B4513)
      this.mainSprite.fillCircle(0, 0, size * 0.3)
      return
    }
    
    // 莲花瓣 - 多层
    const layers = this.currentStage === CropStage.SPROUT ? 1 : 
                   this.currentStage === CropStage.GROWING ? 2 : 3
    
    for (let layer = 0; layer < layers; layer++) {
      const layerSize = size * (1 - layer * 0.2)
      const petalCount = 6 + layer * 2
      
      this.mainSprite.fillStyle(layer === 0 ? primary : secondary)
      
      for (let i = 0; i < petalCount; i++) {
        const angle = (i / petalCount) * Math.PI * 2 + layer * 0.3
        const petalX = Math.cos(angle) * layerSize * 0.4
        const petalY = Math.sin(angle) * layerSize * 0.4
        
        // 绘制花瓣
        this.mainSprite.fillEllipse(petalX, petalY, layerSize * 0.3, layerSize * 0.15)
      }
    }
    
    // 莲心
    if (layers > 1) {
      this.mainSprite.fillStyle(accent)
      this.mainSprite.fillCircle(0, 0, size * 0.1)
    }
  }
  
  /**
   * 更新进度条
   */
  private updateProgressBar(): void {
    this.progressBar.clear()
    
    if (this.currentStage === CropStage.READY_TO_HARVEST || 
        this.currentStage === CropStage.HARVESTED) {
      return // 完成时不显示进度条
    }
    
    const config = CROP_CONFIGS[this.cropData.type]
    const progress = calculateGrowthProgress(this.cropData, config)
    
    // 背景
    this.progressBar.fillStyle(0x333333, 0.8)
    this.progressBar.fillRoundedRect(-20, 0, 40, 4, 2)
    
    // 进度
    const progressColor = this.getProgressColor(progress)
    this.progressBar.fillStyle(progressColor)
    this.progressBar.fillRoundedRect(-20, 0, 40 * progress, 4, 2)
  }
  
  /**
   * 获取进度条颜色
   */
  private getProgressColor(progress: number): number {
    if (progress < 0.3) return 0xFF6B6B // 红色
    if (progress < 0.7) return 0xFFE66D // 黄色
    return 0x4ECDC4 // 绿色
  }
  
  /**
   * 更新品质指示器
   */
  private updateQualityIndicator(): void {
    const qualityEmojis = {
      [CropQuality.COMMON]: '',
      [CropQuality.UNCOMMON]: '⭐',
      [CropQuality.RARE]: '⭐⭐',
      [CropQuality.EPIC]: '⭐⭐⭐',
      [CropQuality.LEGENDARY]: '👑'
    }
    
    this.qualityIndicator.setText(qualityEmojis[this.currentQuality])
  }
  
  /**
   * 更新发光效果
   */
  private updateGlowEffect(): void {
    this.glowSprite.clear()
    
    if (this.isHarvestReady) {
      // 收获就绪时的发光效果
      this.glowSprite.fillStyle(0xFFD700, 0.3)
      this.glowSprite.fillCircle(0, 0, 40)
    } else if (this.isHighlighted) {
      // 悬停时的高亮效果
      this.glowSprite.fillStyle(0x87CEEB, 0.2)
      this.glowSprite.fillCircle(0, 0, 35)
    }
  }
  
  /**
   * 播放阶段转换动画
   */
  private playStageTransitionAnimation(fromStage: CropStage, toStage: CropStage): void {
    // 停止现有动画
    if (this.animationTween) {
      this.animationTween.destroy()
    }
    
    // 缩放弹跳动画
    this.animationTween = this.scene.tweens.add({
      targets: this,
      scaleX: 1 + ANIMATION_CONFIG.SCALE_BOUNCE,
      scaleY: 1 + ANIMATION_CONFIG.SCALE_BOUNCE,
      duration: ANIMATION_CONFIG.SCALE_DURATION / 2,
      ease: 'Back.easeOut',
      yoyo: true,
      onComplete: () => {
        this.animationTween = null
      }
    })
    
    // 粒子效果
    this.emitParticles(ParticleType.GROWTH)
    
    console.log(`作物 ${this.cropData.id} 从 ${fromStage} 进化到 ${toStage}`)
  }
  
  /**
   * 播放品质升级动画
   */
  private playQualityUpgradeAnimation(): void {
    this.emitParticles(ParticleType.QUALITY_BOOST)
    
    // 闪烁效果
    this.scene.tweens.add({
      targets: this,
      alpha: 0.3,
      duration: ANIMATION_CONFIG.HIGHLIGHT_DURATION / 4,
      yoyo: true,
      repeat: 3
    })
  }
  
  /**
   * 开始空闲动画
   */
  private startIdleAnimations(): void {
    // 轻微摇摆动画
    this.swayTween = this.scene.tweens.add({
      targets: this,
      rotation: -0.05,
      duration: ANIMATION_CONFIG.SWAY_FREQUENCY / 2,
      ease: 'Sine.easeInOut',
      yoyo: true,
      repeat: -1
    })
  }
  
  /**
   * 设置高亮状态
   */
  setHighlight(highlighted: boolean): void {
    this.isHighlighted = highlighted
    this.updateGlowEffect()
  }
  
  /**
   * 设置收获就绪状态
   */
  setHarvestReady(ready: boolean): void {
    this.isHarvestReady = ready
    this.updateGlowEffect()
    
    if (ready && !this.glowTween) {
      // 收获就绪时的呼吸灯效果
      this.glowTween = this.scene.tweens.add({
        targets: this.glowSprite,
        alpha: 0.1,
        duration: ANIMATION_CONFIG.HARVEST_GLOW_DURATION / 2,
        ease: 'Sine.easeInOut',
        yoyo: true,
        repeat: -1
      })
    } else if (!ready && this.glowTween) {
      this.glowTween.destroy()
      this.glowTween = null
    }
  }
  
  /**
   * 发射粒子效果
   */
  private emitParticles(type: ParticleType): void {
    // 创建简化的粒子效果，避免复杂的配置
    const colors = this.getCropColors()
    const particleColor = type === ParticleType.GROWTH ? colors.primary : 0xFFD700
    
    // 创建简单的粒子动画
    for (let i = 0; i < ANIMATION_CONFIG.PARTICLE_COUNT; i++) {
      const angle = (i / ANIMATION_CONFIG.PARTICLE_COUNT) * Math.PI * 2
      const distance = 20 + Math.random() * 15
      const targetX = this.x + Math.cos(angle) * distance
      const targetY = this.y + Math.sin(angle) * distance
      
      // 创建单个粒子
      const particle = this.scene.add.graphics()
      particle.fillStyle(particleColor, 0.8)
      particle.fillCircle(0, 0, 2)
      particle.setPosition(this.x, this.y)
      
      // 粒子动画
      this.scene.tweens.add({
        targets: particle,
        x: targetX,
        y: targetY,
        alpha: 0,
        scale: 0,
        duration: ANIMATION_CONFIG.PARTICLE_LIFESPAN / 2,
        ease: 'Quad.easeOut',
        onComplete: () => {
          particle.destroy()
        }
      })
    }
  }
  

  
  /**
   * 作物点击处理
   */
  private onCropClicked(): void {
    if (this.isHarvestReady) {
      this.onHarvestRequested()
    } else {
      this.onCropInspected()
    }
  }
  
  /**
   * 收获请求
   */
  private onHarvestRequested(): void {
    // 播放收获动画
    this.emitParticles(ParticleType.HARVEST)
    
    // 发送收获事件
    this.emit('harvest', this.cropData)
  }
  
  /**
   * 作物检查
   */
  private onCropInspected(): void {
    // 播放检查动画
    this.emitParticles(ParticleType.SPARKLE)
    
    // 发送检查事件
    this.emit('inspect', this.cropData)
  }
  
  /**
   * 销毁精灵
   */
  destroy(): void {
    // 清理动画
    if (this.animationTween) {
      this.animationTween.destroy()
    }
    if (this.swayTween) {
      this.swayTween.destroy()
    }
    if (this.glowTween) {
      this.glowTween.destroy()
    }
    
    // 清理粒子
    this.particles.forEach(emitter => emitter.destroy())
    
    super.destroy()
  }
} 