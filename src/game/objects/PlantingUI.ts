import Phaser from 'phaser'
import { CropType, CROP_CONFIGS } from '../../types/crop'

export interface PlantingUIConfig {
  x: number
  y: number
  visible: boolean
  availableCrops: CropType[]
}

export class PlantingUI extends Phaser.GameObjects.Container {
  private background: Phaser.GameObjects.Rectangle
  private titleText: Phaser.GameObjects.Text
  private seedButtons: Map<CropType, Phaser.GameObjects.Container> = new Map()
  private closeButton!: Phaser.GameObjects.Container
  private selectedCropType: CropType | null = null
  private isVisible: boolean = false
  
  // 事件回调
  private onSeedSelectedCallback?: (cropType: CropType) => void
  private onClosedCallback?: () => void

  constructor(scene: Phaser.Scene, config: PlantingUIConfig) {
    super(scene, config.x, config.y)
    
    // 创建背景
    this.background = scene.add.rectangle(0, 0, 320, 400, 0x2C5530, 0.95)
    this.background.setStrokeStyle(3, 0x90EE90)
    this.add(this.background)
    
    // 创建标题
    this.titleText = scene.add.text(0, -180, '🌱 选择种子', {
      fontSize: '20px',
      color: '#FFFFFF',
      fontFamily: 'Arial',
      fontStyle: 'bold'
    }).setOrigin(0.5)
    this.add(this.titleText)
    
    // 创建种子选择按钮
    this.createSeedButtons(config.availableCrops)
    
    // 创建关闭按钮
    this.createCloseButton()
    
    // 设置初始可见性
    this.setVisible(config.visible)
    this.isVisible = config.visible
    
    // 添加到场景
    scene.add.existing(this)
  }

  private createSeedButtons(availableCrops: CropType[]): void {
    const startY = -120
    const buttonHeight = 70
    const buttonSpacing = 10
    
    availableCrops.forEach((cropType, index) => {
      const config = CROP_CONFIGS[cropType]
      const y = startY + index * (buttonHeight + buttonSpacing)
      
      // 创建按钮容器
      const buttonContainer = this.scene.add.container(0, y)
      
      // 按钮背景
      const buttonBg = this.scene.add.rectangle(0, 0, 280, buttonHeight, 0x4A6741, 0.8)
      buttonBg.setStrokeStyle(2, 0x90EE90, 0.5)
      buttonContainer.add(buttonBg)
      
      // 作物图标
      const iconText = this.scene.add.text(-120, 0, config.icon, {
        fontSize: '24px'
      }).setOrigin(0.5)
      buttonContainer.add(iconText)
      
      // 作物名称和描述
      const nameText = this.scene.add.text(-70, -10, config.name, {
        fontSize: '16px',
        color: '#FFFFFF',
        fontFamily: 'Arial',
        fontStyle: 'bold'
      }).setOrigin(0, 0.5)
      buttonContainer.add(nameText)
      
      const descText = this.scene.add.text(-70, 10, config.description.substring(0, 20) + '...', {
        fontSize: '12px',
        color: '#CCCCCC',
        fontFamily: 'Arial'
      }).setOrigin(0, 0.5)
      buttonContainer.add(descText)
      
      // 生长时间信息
      const timeText = this.scene.add.text(100, 0, `${Math.floor(config.baseGrowthTime / 60000)}分钟`, {
        fontSize: '12px',
        color: '#FFD700',
        fontFamily: 'Arial'
      }).setOrigin(0.5)
      buttonContainer.add(timeText)
      
      // 添加交互
      buttonBg.setInteractive({ useHandCursor: true })
      
      buttonBg.on('pointerover', () => {
        buttonBg.setFillStyle(0x5A7A51, 0.9)
        this.scene.tweens.add({
          targets: buttonContainer,
          scaleX: 1.05,
          scaleY: 1.05,
          duration: 150,
          ease: 'Power2'
        })
      })
      
      buttonBg.on('pointerout', () => {
        if (this.selectedCropType !== cropType) {
          buttonBg.setFillStyle(0x4A6741, 0.8)
        }
        this.scene.tweens.add({
          targets: buttonContainer,
          scaleX: 1,
          scaleY: 1,
          duration: 150,
          ease: 'Power2'
        })
      })
      
      buttonBg.on('pointerdown', () => {
        this.selectSeed(cropType, buttonBg)
      })
      
      this.seedButtons.set(cropType, buttonContainer)
      this.add(buttonContainer)
    })
  }

  private createCloseButton(): void {
    this.closeButton = this.scene.add.container(0, 160)
    
    const closeBg = this.scene.add.rectangle(0, 0, 100, 40, 0xCC4444, 0.8)
    closeBg.setStrokeStyle(2, 0xFF6666)
    this.closeButton.add(closeBg)
    
    const closeText = this.scene.add.text(0, 0, '关闭', {
      fontSize: '14px',
      color: '#FFFFFF',
      fontFamily: 'Arial'
    }).setOrigin(0.5)
    this.closeButton.add(closeText)
    
    closeBg.setInteractive({ useHandCursor: true })
    
    closeBg.on('pointerover', () => {
      closeBg.setFillStyle(0xDD5555, 0.9)
    })
    
    closeBg.on('pointerout', () => {
      closeBg.setFillStyle(0xCC4444, 0.8)
    })
    
    closeBg.on('pointerdown', () => {
      this.closeUI()
    })
    
    this.add(this.closeButton)
  }

  private selectSeed(cropType: CropType, buttonBg: Phaser.GameObjects.Rectangle): void {
    // 清除之前的选择状态
    this.seedButtons.forEach((container, type) => {
      const bg = container.getAt(0) as Phaser.GameObjects.Rectangle
      if (type !== cropType) {
        bg.setFillStyle(0x4A6741, 0.8)
      }
    })
    
    // 设置当前选择状态
    buttonBg.setFillStyle(0x90EE90, 0.9)
    this.selectedCropType = cropType
    
    // 创建选择动画
    const container = this.seedButtons.get(cropType)!
    this.scene.tweens.add({
      targets: container,
      scaleX: 1.1,
      scaleY: 1.1,
      duration: 100,
      yoyo: true,
      ease: 'Power2'
    })
    
    // 延迟执行回调，让用户看到选择效果
    this.scene.time.delayedCall(200, () => {
      if (this.onSeedSelectedCallback) {
        this.onSeedSelectedCallback(cropType)
      }
    })
  }

  public showUI(availableCrops?: CropType[]): void {
    if (availableCrops) {
      this.updateAvailableCrops(availableCrops)
    }
    
    this.setVisible(true)
    this.isVisible = true
    
    // 显示动画
    this.setScale(0.8)
    this.setAlpha(0)
    
    this.scene.tweens.add({
      targets: this,
      scaleX: 1,
      scaleY: 1,
      alpha: 1,
      duration: 300,
      ease: 'Back.easeOut'
    })
  }

  public hideUI(): void {
    this.scene.tweens.add({
      targets: this,
      scaleX: 0.8,
      scaleY: 0.8,
      alpha: 0,
      duration: 250,
      ease: 'Back.easeIn',
      onComplete: () => {
        this.setVisible(false)
        this.isVisible = false
      }
    })
  }

  private closeUI(): void {
    this.hideUI()
    if (this.onClosedCallback) {
      this.onClosedCallback()
    }
  }

  private updateAvailableCrops(availableCrops: CropType[]): void {
    // 清除现有按钮
    this.seedButtons.forEach(container => {
      this.remove(container)
      container.destroy()
    })
    this.seedButtons.clear()
    
    // 创建新按钮
    this.createSeedButtons(availableCrops)
  }

  public getSelectedCropType(): CropType | null {
    return this.selectedCropType
  }

  public isUIVisible(): boolean {
    return this.isVisible
  }

  // 事件监听器设置
  public onSeedSelected(callback: (cropType: CropType) => void): void {
    this.onSeedSelectedCallback = callback
  }

  public onClosed(callback: () => void): void {
    this.onClosedCallback = callback
  }

  public destroy(): void {
    this.seedButtons.forEach(container => container.destroy())
    this.seedButtons.clear()
    super.destroy()
  }
} 