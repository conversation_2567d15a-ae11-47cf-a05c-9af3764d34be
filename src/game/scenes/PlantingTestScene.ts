import Phaser from 'phaser'
import { EnhancedFarmScene } from './EnhancedFarmScene'
import { GameStateManager } from '../../managers/GameStateManager'
import { CropType } from '../../types/crop'

export class PlantingTestScene extends Phaser.Scene {
  private gameStateManager: GameStateManager
  private farmScene!: EnhancedFarmScene
  private testButtons: Phaser.GameObjects.Container[] = []
  private testButton!: Phaser.GameObjects.Text
  private status!: Phaser.GameObjects.Text

  constructor() {
    super({ key: 'PlantingTestScene' })
    this.gameStateManager = new GameStateManager()
  }

  preload() {
    // 加载必要的资源
    this.load.image('background', 'https://via.placeholder.com/800x600/228B22/FFFFFF?text=Test+Background')
    this.load.image('plant_test', 'https://via.placeholder.com/32x32/90EE90/000000?text=P')
  }

  create() {
    // 创建背景
    this.add.image(400, 300, 'background')

    // 创建标题
    this.add.text(400, 50, '种植系统测试', {
      fontSize: '32px',
      color: '#ffffff'
    }).setOrigin(0.5)

    this.startFarmScene()
    this.createUI()
  }

  private startFarmScene() {
    // 启动农场场景
    this.scene.launch('EnhancedFarmScene', { gameStateManager: this.gameStateManager })
    this.farmScene = this.scene.get('EnhancedFarmScene') as EnhancedFarmScene
  }

  private createUI() {
    // 创建测试控制面板
    this.createTestPanel()
    
    // 显示说明
    this.showInstructions()
  }

  private createTestPanel() {
    const panelX = 50
    const panelY = 80
    
    // 测试面板背景
    const panelBg = this.add.rectangle(50, 300, 80, 500, 0x2C5530, 0.9)
    panelBg.setStrokeStyle(2, 0x90EE90)
    
    const panelTitle = this.add.text(50, 90, '测试控制', {
      fontSize: '14px',
      color: '#FFFFFF',
      fontFamily: 'Arial',
      fontStyle: 'bold'
    }).setOrigin(0.5)
    
    // 测试按钮
    const testActions = [
      { label: '重置状态', action: () => this.resetGameState() },
      { label: '种植花', action: () => this.quickPlant(CropType.KNOWLEDGE_FLOWER) },
      { label: '种植树', action: () => this.quickPlant(CropType.STRENGTH_TREE) },
      { label: '模拟生长', action: () => this.simulateGrowth() },
      { label: '全部收获', action: () => this.harvestAll() },
      { label: '增加经验', action: () => this.addExperience() },
      { label: '升级玩家', action: () => this.levelUp() },
      { label: '显示状态', action: () => this.showGameState() }
    ]
    
    testActions.forEach((action, index) => {
      const button = this.createTestButton(
        panelX, 
        panelY + 30 + index * 45, 
        action.label, 
        action.action
      )
      this.testButtons.push(button)
    })
  }

  private createTestButton(x: number, y: number, text: string, onClick: () => void): Phaser.GameObjects.Container {
    const button = this.add.container(x, y)
    
    const buttonBg = this.add.rectangle(0, 0, 70, 35, 0x4169E1, 0.8)
    buttonBg.setStrokeStyle(1, 0x6495ED)
    button.add(buttonBg)
    
    const buttonText = this.add.text(0, 0, text, {
      fontSize: '10px',
      color: '#FFFFFF',
      fontFamily: 'Arial'
    }).setOrigin(0.5)
    button.add(buttonText)
    
    // 交互
    buttonBg.setInteractive({ useHandCursor: true })
    
    buttonBg.on('pointerover', () => {
      buttonBg.setFillStyle(0x5A7FE1, 1)
    })
    
    buttonBg.on('pointerout', () => {
      buttonBg.setFillStyle(0x4169E1, 0.8)
    })
    
    buttonBg.on('pointerdown', () => {
      onClick()
      this.showMessage(`执行: ${text}`)
    })
    
    return button
  }

  private resetGameState() {
    this.gameStateManager.initialize()
    this.gameStateManager.save()
    console.log('游戏状态已重置')
  }

  private quickPlant(cropType: CropType) {
    // 寻找第一个空的位置种植
    const gameState = this.gameStateManager.getGameState()
    
    for (let y = 0; y < 6; y++) {
      for (let x = 0; x < 8; x++) {
        if (gameState.farmGrid.plots[y][x] === null) {
          const success = this.gameStateManager.plantCrop(x, y, cropType)
          if (success) {
            console.log(`在位置(${x},${y})种植了${cropType}`)
            return
          }
        }
      }
    }
    console.log('没有可用的种植位置')
  }

  private simulateGrowth() {
    const gameState = this.gameStateManager.getGameState()
    
    // 快进时间来模拟生长
    gameState.gameTime += 60000 // 1分钟
    this.gameStateManager.updateCropGrowth()
    this.gameStateManager.save()
    
    console.log('模拟生长完成')
  }

  private harvestAll() {
    const gameState = this.gameStateManager.getGameState()
    let harvestedCount = 0
    
    for (let y = 0; y < 6; y++) {
      for (let x = 0; x < 8; x++) {
        const result = this.gameStateManager.harvestCrop(x, y)
        if (result.success) {
          harvestedCount++
        }
      }
    }
    
    console.log(`收获了${harvestedCount}个作物`)
  }

  private addExperience() {
    const gameState = this.gameStateManager.getGameState()
    gameState.experience += 50
    this.gameStateManager.save()
    console.log('增加了50经验')
  }

  private levelUp() {
    const gameState = this.gameStateManager.getGameState()
    gameState.level += 1
    gameState.experience += 100
    this.gameStateManager.save()
    console.log(`玩家升级到${gameState.level}级`)
  }

  private showGameState() {
    const gameState = this.gameStateManager.getGameState()
    const stats = this.gameStateManager.getGameStats()
    
    console.log('=== 游戏状态 ===')
    console.log(`等级: ${gameState.level}`)
    console.log(`经验: ${gameState.experience}`)
    console.log(`作物数量: ${stats.cropsCount}`)
    console.log(`总收获: ${stats.totalCropsHarvested}`)
    console.log(`知识: ${gameState.resources.knowledge}`)
    console.log(`力量: ${gameState.resources.strength}`)
    console.log('================')
  }

  private showMessage(text: string) {
    const message = this.add.container(400, 80)
    
    const bg = this.add.rectangle(0, 0, text.length * 8 + 20, 30, 0x000000, 0.8)
    message.add(bg)
    
    const textObj = this.add.text(0, 0, text, {
      fontSize: '12px',
      color: '#FFFFFF',
      fontFamily: 'Arial'
    }).setOrigin(0.5)
    message.add(textObj)
    
    // 动画
    message.setAlpha(0)
    this.tweens.add({
      targets: message,
      alpha: 1,
      duration: 200,
      onComplete: () => {
        this.time.delayedCall(2000, () => {
          this.tweens.add({
            targets: message,
            alpha: 0,
            duration: 300,
            onComplete: () => message.destroy()
          })
        })
      }
    })
  }

  private showInstructions() {
    const instructions = this.add.text(600, 550, 
      '🎮 使用说明:\n' +
      '• 左侧面板：测试控制功能\n' +
      '• 农场区域：点击"开始种植"体验种植\n' +
      '• 控制台：查看详细操作日志\n' +
      '• ESC键：返回主菜单', {
      fontSize: '12px',
      color: '#2C5530',
      fontFamily: 'Arial',
      align: 'left'
    }).setOrigin(0.5)

    // ESC键监听
    this.input.keyboard!.on('keydown-ESC', () => {
      this.scene.stop('EnhancedFarmScene')
      this.scene.start('MainScene')
    })
  }
} 