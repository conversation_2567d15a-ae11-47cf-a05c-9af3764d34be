import * as Phaser from 'phaser'
import { FarmManager } from '../../managers/FarmManager'
import { FocusTokenManager } from '../../managers/FocusTokenManager'
import { LootBoxManager } from '../../managers/LootBoxManager'
import { SynthesisManager } from '../../managers/SynthesisManager'
import { AgriculturalItem, FarmSlot, GrowthStage, ItemCategory } from '../../types/agriculture'

export class AgriculturalFarmScene extends Phaser.Scene {
  private farmManager!: FarmManager
  private focusTokenManager!: FocusTokenManager
  private lootBoxManager!: LootBoxManager
  private synthesisManager!: SynthesisManager
  
  private farmSlots: Map<string, Phaser.GameObjects.Container> = new Map()
  private selectedSlot: string | null = null
  private uiElements: {
    focusTokenDisplay?: Phaser.GameObjects.Text
    farmLevelDisplay?: Phaser.GameObjects.Text
    weatherDisplay?: Phaser.GameObjects.Text
    timeDisplay?: Phaser.GameObjects.Text
  } = {}

  constructor() {
    super({ key: 'AgriculturalFarmScene' })
  }

  preload(): void {
    this.loadGameAssets()
  }

  create(): void {
    this.initializeManagers()
    this.createGameWorld()
    this.createUI()
    this.setupInputHandlers()
    this.startGameLoop()
  }

  update(time: number): void {
    this.farmManager.updateGameTime(time)
    this.updateUI()
    this.updateFarmDisplay()
  }

  private loadGameAssets(): void {
    // 创建简单的颜色矩形作为纹理
    this.add.graphics()
      .fillStyle(0x8B4513)
      .fillRect(0, 0, 80, 80)
      .generateTexture('soil_empty', 80, 80)

    // 作物生长阶段纹理
    const stages = [
      { name: 'seed', color: 0x8B4513 },
      { name: 'sprout', color: 0x90EE90 },
      { name: 'growing', color: 0x32CD32 },
      { name: 'flowering', color: 0xFF69B4 },
      { name: 'mature', color: 0xFFD700 },
      { name: 'ready', color: 0xFF4500 }
    ]

    stages.forEach(stage => {
      this.add.graphics()
        .fillStyle(stage.color)
        .fillCircle(20, 20, 15)
        .generateTexture(`crop_${stage.name}`, 40, 40)
    })
  }

  private initializeManagers(): void {
    const savedTokenData = FocusTokenManager.loadFromStorage()
    
    this.focusTokenManager = new FocusTokenManager(savedTokenData || undefined)
    this.farmManager = new FarmManager()
    this.lootBoxManager = new LootBoxManager(this.focusTokenManager)
    this.synthesisManager = new SynthesisManager()

    this.setupManagerEvents()
  }

  private setupManagerEvents(): void {
    this.focusTokenManager.on('tokensEarned', (data) => {
      this.showFloatingText(`+${data.amount} 专注代币`, 0x4CAF50)
    })

    this.farmManager.on('cropPlanted', (data) => {
      this.updateSlotDisplay(data.slotId)
      this.showFloatingText('作物已种植', 0x4CAF50)
    })

    this.farmManager.on('cropHarvested', (data) => {
      this.updateSlotDisplay(data.slotId)
      this.showFloatingText(`收获 ${data.harvestItems.length} 个物品`, 0xFFD700)
    })
  }

  private createGameWorld(): void {
    this.cameras.main.setBounds(0, 0, 1200, 800)
    
    this.add.rectangle(600, 400, 1200, 800, 0x87CEEB)
    this.add.rectangle(600, 600, 1200, 400, 0x228B22)

    this.add.text(600, 50, '🌾 农产品道具系统 🌾', {
      fontSize: '32px',
      fontFamily: 'Arial',
      color: '#2E7D32',
      stroke: '#FFFFFF',
      strokeThickness: 2
    }).setOrigin(0.5)

    this.createFarmGrid()
  }

  private createFarmGrid(): void {
    const farm = this.farmManager.getFarmState()
    const gridSize = Math.ceil(Math.sqrt(farm.maxSlots))
    const startX = 300
    const startY = 150
    const slotSize = 80
    const spacing = 20

    farm.slots.forEach((slot, index) => {
      const row = Math.floor(index / gridSize)
      const col = index % gridSize
      const x = startX + col * (slotSize + spacing)
      const y = startY + row * (slotSize + spacing)

      const container = this.createSlotContainer(slot, x, y)
      this.farmSlots.set(slot.id, container)
    })
  }

  private createSlotContainer(slot: FarmSlot, x: number, y: number): Phaser.GameObjects.Container {
    const container = this.add.container(x, y)

    const soil = this.add.rectangle(0, 0, 80, 80, slot.isUnlocked ? 0x8B4513 : 0x666666)
    soil.setStrokeStyle(2, slot.isUnlocked ? 0x654321 : 0x444444)
    container.add(soil)

    const idText = this.add.text(-35, -35, slot.id, {
      fontSize: '12px',
      color: '#FFFFFF'
    })
    container.add(idText)

    if (slot.currentItem) {
      const cropSprite = this.add.circle(0, 0, 15, this.getCropColor(slot.currentItem))
      container.add(cropSprite)
    }

    soil.setInteractive()
    soil.on('pointerdown', () => this.onSlotClicked(slot.id))

    return container
  }

  private getCropColor(item: AgriculturalItem): number {
    const colors: Record<string, number> = {
      'gray': 0x9E9E9E,
      'green': 0x4CAF50,
      'blue': 0x2196F3,
      'orange': 0xFF9800,
      'gold': 0xFFD700,
      'gold-red': 0xFF6B35
    }
    return colors[item.quality.toString()] || 0x9E9E9E
  }

  private createUI(): void {
    this.createTopPanel()
    this.createSidePanel()
  }

  private createTopPanel(): void {
    this.add.rectangle(600, 30, 1200, 60, 0x2E2E2E, 0.8)
    
    this.uiElements.focusTokenDisplay = this.add.text(50, 30, '专注代币: 0', {
      fontSize: '18px',
      color: '#4CAF50'
    }).setOrigin(0, 0.5)

    this.uiElements.farmLevelDisplay = this.add.text(250, 30, '农场等级: 1', {
      fontSize: '18px',
      color: '#FFD700'
    }).setOrigin(0, 0.5)
  }

  private createSidePanel(): void {
    this.add.rectangle(950, 400, 300, 600, 0x2E2E2E, 0.8)
    
    this.add.text(950, 120, '功能面板', {
      fontSize: '24px',
      color: '#FFFFFF',
      fontStyle: 'bold'
    }).setOrigin(0.5)

    // 专注模式按钮
    const focusButton = this.add.container(950, 180)
    const focusButtonBg = this.add.rectangle(0, 0, 200, 40, 0x4CAF50)
    const focusButtonText = this.add.text(0, 0, '开始专注', {
      fontSize: '16px',
      color: '#FFFFFF'
    }).setOrigin(0.5)
    focusButton.add([focusButtonBg, focusButtonText])
    focusButtonBg.setInteractive()
    focusButtonBg.on('pointerdown', () => this.startFocusSession())
  }

  private setupInputHandlers(): void {
    this.input.keyboard?.on('keydown-F', () => {
      this.startFocusSession()
    })
  }

  private onSlotClicked(slotId: string): void {
    this.selectedSlot = slotId
    const slot = this.farmManager.getSlotState(slotId)
    if (!slot) return

    if (!slot.isUnlocked) {
      this.unlockSlot(slotId)
    } else if (!slot.currentItem) {
      this.plantCrop(slotId)
    } else if (slot.currentItem.growth.isReady) {
      this.harvestCrop(slotId)
    }
  }

  private async plantCrop(slotId: string): Promise<void> {
    try {
      const seed = this.createMockSeed()
      const result = await this.farmManager.plantCrop(slotId, seed)
      if (!result.success) {
        this.showNotification('种植失败: ' + result.error, 0xF44336)
      }
    } catch (error) {
      this.showNotification('种植失败: ' + (error as Error).message, 0xF44336)
    }
  }

  private async harvestCrop(slotId: string): Promise<void> {
    try {
      const result = await this.farmManager.harvestCrop(slotId)
      if (!result.success) {
        this.showNotification('收获失败: ' + result.error, 0xF44336)
      }
    } catch (error) {
      this.showNotification('收获失败: ' + (error as Error).message, 0xF44336)
    }
  }

  private async unlockSlot(slotId: string): Promise<void> {
    try {
      const result = await this.farmManager.unlockSlot(slotId)
      if (!result.success) {
        this.showNotification('解锁失败: ' + result.error, 0xF44336)
      }
    } catch (error) {
      this.showNotification('解锁失败: ' + (error as Error).message, 0xF44336)
    }
  }

  private startFocusSession(): void {
    this.focusTokenManager.startFocusSession()
    this.showNotification('专注模式已开启！', 0x4CAF50)
  }

  private updateUI(): void {
    const tokens = this.focusTokenManager.getTokenAmount()
    const farm = this.farmManager.getFarmState()
    
    if (this.uiElements.focusTokenDisplay) {
      this.uiElements.focusTokenDisplay.setText(`专注代币: ${tokens}`)
    }
    
    if (this.uiElements.farmLevelDisplay) {
      this.uiElements.farmLevelDisplay.setText(`农场等级: ${farm.level}`)
    }
  }

  private updateFarmDisplay(): void {
    const farm = this.farmManager.getFarmState()
    
    farm.slots.forEach(slot => {
      this.updateSlotDisplay(slot.id)
    })
  }

  private updateSlotDisplay(slotId: string): void {
    const container = this.farmSlots.get(slotId)
    const slot = this.farmManager.getSlotState(slotId)
    
    if (!container || !slot) return

    container.removeAll(true)

    const soil = this.add.rectangle(0, 0, 80, 80, slot.isUnlocked ? 0x8B4513 : 0x666666)
    soil.setStrokeStyle(2, slot.isUnlocked ? 0x654321 : 0x444444)
    container.add(soil)

    const idText = this.add.text(-35, -35, slot.id, {
      fontSize: '12px',
      color: '#FFFFFF'
    })
    container.add(idText)

    if (slot.currentItem) {
      const cropSprite = this.add.circle(0, 0, 15, this.getCropColor(slot.currentItem))
      container.add(cropSprite)
    }

    soil.setInteractive()
    soil.on('pointerdown', () => this.onSlotClicked(slot.id))
  }

  private showNotification(message: string, color: number = 0xFFFFFF): void {
    const notification = this.add.container(600, 100)
    
    const bg = this.add.rectangle(0, 0, 400, 60, 0x000000, 0.8)
    const text = this.add.text(0, 0, message, {
      fontSize: '16px',
      color: '#' + color.toString(16).padStart(6, '0'),
      wordWrap: { width: 380 }
    }).setOrigin(0.5)
    
    notification.add([bg, text])
    
    notification.setAlpha(0)
    this.tweens.add({
      targets: notification,
      alpha: 1,
      duration: 300,
      ease: 'Power2'
    })
    
    this.time.delayedCall(3000, () => {
      this.tweens.add({
        targets: notification,
        alpha: 0,
        duration: 300,
        ease: 'Power2',
        onComplete: () => notification.destroy()
      })
    })
  }

  private showFloatingText(text: string, color: number, x: number = 600, y: number = 300): void {
    const floatingText = this.add.text(x, y, text, {
      fontSize: '20px',
      color: '#' + color.toString(16).padStart(6, '0'),
      fontStyle: 'bold'
    }).setOrigin(0.5)

    this.tweens.add({
      targets: floatingText,
      y: y - 50,
      alpha: 0,
      duration: 1500,
      ease: 'Power2',
      onComplete: () => floatingText.destroy()
    })
  }

  private createMockSeed(): AgriculturalItem {
    return {
      id: `seed_${Date.now()}`,
      name: '玉米种子',
      nameEn: 'Corn Seed',
      description: '优质玉米种子，适合春季种植',
      rarity: 'gray' as any,
      category: ItemCategory.SEED,
      variety: 'corn',
      level: 1,
      quality: 1,
      production: {
        minDaily: 1,
        maxDaily: 3
      },
      value: {
        basePrice: 10,
        currentPrice: 10,
        marketDemand: 1,
        priceHistory: []
      },
      growth: {
        growthTime: 24,
        currentStage: GrowthStage.SEED,
        isReady: false,
        needsWater: true,
        needsFertilizer: false
      },
      sprite: 'seed',
      location: {}
    }
  }

  private startGameLoop(): void {
    this.time.addEvent({
      delay: 1000,
      callback: () => {
        // 专注代币管理器自动处理更新
      },
      loop: true
    })

    this.time.addEvent({
      delay: 5000,
      callback: () => {
        // FocusTokenManager自动保存
      },
      loop: true
    })
  }
} 