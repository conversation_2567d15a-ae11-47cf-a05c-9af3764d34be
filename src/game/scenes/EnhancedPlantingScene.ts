import Phaser from 'phaser'
import { PlantingSystemManager } from '../../systems/PlantingSystemManager'
import { PlantingSlot, PlantingStage, HarvestResult } from '../../types/planting'
import { ItemRarity } from '../../types/lootbox'
import { getFuturesProductById } from '../../data/chineseFuturesProducts'
import { getPlantingItem } from '../../data/plantingData'

export class EnhancedPlantingScene extends Phaser.Scene {
  private plantingManager!: PlantingSystemManager
  private farmSlots: Map<string, Phaser.GameObjects.Container> = new Map()
  private gridSize = { width: 3, height: 3 }
  private slotSize = 120
  private startX = 150
  private startY = 150
  
  // UI 容器
  private uiContainer!: Phaser.GameObjects.Container
  private infoPanel!: Phaser.GameObjects.Container
  private harvestResultModal!: Phaser.GameObjects.Container
  
  // 状态
  private selectedSlotId: string | null = null
  private showingHarvestResult: boolean = false

  constructor() {
    super({ key: 'EnhancedPlantingScene' })
  }

  preload() {
    // 创建简单的颜色纹理
    this.load.image('slot_empty', 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==')
  }

  create() {
    console.log('🌾 增强种植场景启动')
    
    // 创建简单背景
    this.add.rectangle(400, 300, 800, 600, 0x87CEEB)
    
    // 标题
    this.add.text(400, 50, '🌾 农场收集系统 - 游戏场景', {
      fontSize: '24px',
      color: '#2C5530',
      fontFamily: 'Arial',
      fontStyle: 'bold'
    }).setOrigin(0.5)
    
    // 提示文本
    this.add.text(400, 300, '种植系统游戏场景开发中...\n\n点击左侧菜单的 "🌾 农场收集系统" 体验完整功能！', {
      fontSize: '18px',
      color: '#4A5568',
      fontFamily: 'Arial',
      align: 'center'
    }).setOrigin(0.5)
  }

  private initializePlantingManager(): void {
    this.plantingManager = new PlantingSystemManager()
    
    // 监听种植系统事件
    this.plantingManager.addUpdateCallback(() => {
      this.updateFarmDisplay()
      this.updateInfoPanel()
    })
  }

  private createBackground(): void {
    // 创建渐变背景
    const graphics = this.add.graphics()
    graphics.fillGradientStyle(0x87CEEB, 0x87CEEB, 0x98FB98, 0x98FB98, 1)
    graphics.fillRect(0, 0, 800, 600)
    
    // 添加云朵
    this.createClouds()
    
    // 添加太阳
    const sun = this.add.circle(700, 100, 40, 0xFFD700)
    sun.setStrokeStyle(3, 0xFFA500)
    
    // 太阳光芒动画
    this.tweens.add({
      targets: sun,
      scaleX: 1.1,
      scaleY: 1.1,
      duration: 2000,
      yoyo: true,
      repeat: -1,
      ease: 'Sine.easeInOut'
    })
  }

  private createClouds(): void {
    const cloudPositions = [
      { x: 100, y: 80 },
      { x: 300, y: 60 },
      { x: 500, y: 90 }
    ]
    
    cloudPositions.forEach((pos, index) => {
      const cloud = this.add.container(pos.x, pos.y)
      
      // 创建云朵形状
      const cloudParts = [
        { x: 0, y: 0, radius: 25 },
        { x: -20, y: 5, radius: 20 },
        { x: 20, y: 5, radius: 20 },
        { x: -10, y: -10, radius: 15 },
        { x: 10, y: -10, radius: 15 }
      ]
      
      cloudParts.forEach(part => {
        const cloudPart = this.add.circle(part.x, part.y, part.radius, 0xFFFFFF, 0.8)
        cloud.add(cloudPart)
      })
      
      // 云朵飘动动画
      this.tweens.add({
        targets: cloud,
        x: pos.x + 50,
        duration: 8000 + index * 2000,
        yoyo: true,
        repeat: -1,
        ease: 'Sine.easeInOut'
      })
    })
  }

  private createUI(): void {
    // 创建主要UI容器
    this.uiContainer = this.add.container(0, 0)
    
    // 添加提示文本
    this.add.text(400, 520, '点击空地种植作物，点击成熟作物收获', {
      fontSize: '16px',
      color: '#4A5568',
      fontFamily: 'Arial',
      align: 'center'
    }).setOrigin(0.5)
  }

  private createFarmGrid(): void {
    // 创建农田背景
    const farmBg = this.add.rectangle(400, 320, 400, 400, 0x8B4513, 0.3)
    farmBg.setStrokeStyle(3, 0x654321)
    
    // 创建农田网格
    for (let y = 0; y < this.gridSize.height; y++) {
      for (let x = 0; x < this.gridSize.width; x++) {
        const slotId = `slot_${x}_${y}`
        const worldX = this.startX + x * (this.slotSize + 20)
        const worldY = this.startY + y * (this.slotSize + 20)
        
        const slot = this.add.container(worldX, worldY)
        
        // 土地背景
        const bg = this.add.rectangle(0, 0, this.slotSize, this.slotSize, 0x8B4513, 0.8)
        bg.setStrokeStyle(3, 0x654321)
        
        // 作物图标（初始隐藏）
        const cropIcon = this.add.text(0, -10, '', {
          fontSize: '48px'
        }).setOrigin(0.5).setVisible(false)
        
        // 进度条背景
        const progressBg = this.add.rectangle(0, 40, this.slotSize - 20, 8, 0x333333, 0.5)
        progressBg.setVisible(false)
        
        // 进度条填充
        const progressFill = this.add.rectangle(-(this.slotSize - 20)/2, 40, 0, 8, 0x4CAF50)
        progressFill.setOrigin(0, 0.5)
        progressFill.setVisible(false)
        
        // 品质光环
        const qualityAura = this.add.circle(0, 0, this.slotSize/2 + 10, 0xFFFFFF, 0)
        qualityAura.setStrokeStyle(4, 0xFFFFFF, 0)
        qualityAura.setVisible(false)
        
        // 时间文本
        const timeText = this.add.text(0, 20, '', {
          fontSize: '12px',
          color: '#FFFFFF',
          fontFamily: 'Arial',
          backgroundColor: 'rgba(0,0,0,0.7)',
          padding: { x: 6, y: 3 }
        }).setOrigin(0.5).setVisible(false)
        
        slot.add([bg, cropIcon, progressBg, progressFill, qualityAura, timeText])
        slot.setData('slotId', slotId)
        slot.setData('cropIcon', cropIcon)
        slot.setData('progressBg', progressBg)
        slot.setData('progressFill', progressFill)
        slot.setData('qualityAura', qualityAura)
        slot.setData('timeText', timeText)
        slot.setData('bg', bg)
        
        // 添加点击交互
        slot.setInteractive(new Phaser.Geom.Rectangle(-this.slotSize/2, -this.slotSize/2, this.slotSize, this.slotSize), Phaser.Geom.Rectangle.Contains)
        
        slot.on('pointerdown', () => {
          this.handleSlotClick(slotId)
        })
        
        slot.on('pointerover', () => {
          bg.setStrokeStyle(4, 0xFFD700)
          bg.setScale(1.05)
        })
        
        slot.on('pointerout', () => {
          const slotData = this.plantingManager.getSlot(slotId)
          const strokeColor = slotData?.stage === PlantingStage.READY_HARVEST ? 0x4CAF50 : 0x654321
          bg.setStrokeStyle(3, strokeColor)
          bg.setScale(1)
        })
        
        this.farmSlots.set(slotId, slot)
      }
    }
  }

  private createInfoPanel(): void {
    this.infoPanel = this.add.container(600, 150)
    
    // 背景
    const bg = this.add.rectangle(0, 0, 180, 320, 0x2C5530, 0.95)
    bg.setStrokeStyle(2, 0x4CAF50)
    
    // 标题
    const title = this.add.text(0, -140, '🌾 农场状态', {
      fontSize: '18px',
      color: '#FFFFFF',
      fontFamily: 'Arial',
      fontStyle: 'bold'
    }).setOrigin(0.5)
    
    // 统计信息
    const statsText = this.add.text(0, -80, '', {
      fontSize: '12px',
      color: '#E8F5E8',
      fontFamily: 'Arial',
      align: 'center',
      lineSpacing: 8
    }).setOrigin(0.5)
    
    // 快速种植按钮
    const plantButton = this.add.container(0, 20)
    const plantBg = this.add.rectangle(0, 0, 140, 35, 0x4CAF50, 0.9)
    plantBg.setStrokeStyle(2, 0x2E7D32)
    const plantText = this.add.text(0, 0, '🌱 快速种植', {
      fontSize: '14px',
      color: '#FFFFFF',
      fontFamily: 'Arial',
      fontStyle: 'bold'
    }).setOrigin(0.5)
    
    plantButton.add([plantBg, plantText])
    plantButton.setInteractive(new Phaser.Geom.Rectangle(-70, -17.5, 140, 35), Phaser.Geom.Rectangle.Contains)
    plantButton.on('pointerdown', () => this.quickPlant())
    plantButton.on('pointerover', () => plantBg.setFillStyle(0x66BB6A))
    plantButton.on('pointerout', () => plantBg.setFillStyle(0x4CAF50))
    
    // 收获全部按钮
    const harvestButton = this.add.container(0, 70)
    const harvestBg = this.add.rectangle(0, 0, 140, 35, 0xFF9800, 0.9)
    harvestBg.setStrokeStyle(2, 0xF57C00)
    const harvestText = this.add.text(0, 0, '🌾 收获全部', {
      fontSize: '14px',
      color: '#FFFFFF',
      fontFamily: 'Arial',
      fontStyle: 'bold'
    }).setOrigin(0.5)
    
    harvestButton.add([harvestBg, harvestText])
    harvestButton.setInteractive(new Phaser.Geom.Rectangle(-70, -17.5, 140, 35), Phaser.Geom.Rectangle.Contains)
    harvestButton.on('pointerdown', () => this.harvestAll())
    harvestButton.on('pointerover', () => harvestBg.setFillStyle(0xFFB74D))
    harvestButton.on('pointerout', () => harvestBg.setFillStyle(0xFF9800))
    
    this.infoPanel.add([bg, title, statsText, plantButton, harvestButton])
    this.infoPanel.setData('statsText', statsText)
  }

  private handleSlotClick(slotId: string): void {
    const slotData = this.plantingManager.getSlot(slotId)
    
    if (!slotData) return
    
    switch (slotData.stage) {
      case PlantingStage.EMPTY:
        this.plantOnSlot(slotId)
        break
      case PlantingStage.READY_HARVEST:
        this.harvestFromSlot(slotId)
        break
      default:
        this.showSlotInfo(slotId)
        break
    }
  }

  private async plantOnSlot(slotId: string): Promise<void> {
    // 使用基础种子种植玉米
    const basicSeed = getPlantingItem('basic_seed')
    if (basicSeed) {
      const success = await this.plantingManager.plantCrop(slotId, 'corn', basicSeed)
      if (success) {
        this.showNotification('🌱 种植成功！', 0x4CAF50)
        this.addPlantingEffect(slotId)
      } else {
        this.showNotification('❌ 种植失败', 0xFF5722)
      }
    }
  }

  private async harvestFromSlot(slotId: string): Promise<void> {
    const result = await this.plantingManager.harvestCrop(slotId)
    if (result) {
      this.showHarvestResult(result)
      this.addHarvestEffect(slotId)
    }
  }

  private quickPlant(): void {
    const emptySlots = this.plantingManager.getAllSlots().filter(slot => slot.stage === PlantingStage.EMPTY)
    
    if (emptySlots.length === 0) {
      this.showNotification('❌ 没有空地可种植', 0xFFC107)
      return
    }
    
    // 随机种植不同作物
    const crops = ['corn', 'wheat', 'soybean', 'cotton', 'apple']
    let planted = 0
    
    emptySlots.forEach(slot => {
      const randomCrop = crops[Math.floor(Math.random() * crops.length)]
      const basicSeed = getPlantingItem('basic_seed')
      if (basicSeed) {
        this.plantingManager.plantCrop(slot.id, randomCrop, basicSeed)
        this.addPlantingEffect(slot.id)
        planted++
      }
    })
    
    this.showNotification(`🌱 成功种植 ${planted} 个作物！`, 0x4CAF50)
  }

  private harvestAll(): void {
    const readySlots = this.plantingManager.getAllSlots().filter(slot => slot.stage === PlantingStage.READY_HARVEST)
    
    if (readySlots.length === 0) {
      this.showNotification('❌ 没有可收获的作物', 0xFFC107)
      return
    }
    
    let totalCoins = 0
    let totalExp = 0
    
    readySlots.forEach(slot => {
      this.plantingManager.harvestCrop(slot.id).then(result => {
        if (result) {
          totalCoins += result.coins
          totalExp += result.experience
          this.addHarvestEffect(slot.id)
        }
      })
    })
    
    this.time.delayedCall(500, () => {
      this.showNotification(`🌾 收获 ${readySlots.length} 个作物！\n💰 +${totalCoins} 金币 ⭐ +${totalExp} 经验`, 0x4CAF50)
    })
  }

  private updateFarmDisplay(): void {
    this.farmSlots.forEach((slotContainer, slotId) => {
      const slotData = this.plantingManager.getSlot(slotId)
      if (!slotData) return
      
      const cropIcon = slotContainer.getData('cropIcon') as Phaser.GameObjects.Text
      const progressBg = slotContainer.getData('progressBg') as Phaser.GameObjects.Rectangle
      const progressFill = slotContainer.getData('progressFill') as Phaser.GameObjects.Rectangle
      const qualityAura = slotContainer.getData('qualityAura') as Phaser.GameObjects.Arc
      const timeText = slotContainer.getData('timeText') as Phaser.GameObjects.Text
      const bg = slotContainer.getData('bg') as Phaser.GameObjects.Rectangle
      
      // 更新显示
      switch (slotData.stage) {
        case PlantingStage.EMPTY:
          cropIcon.setVisible(false)
          progressBg.setVisible(false)
          progressFill.setVisible(false)
          qualityAura.setVisible(false)
          timeText.setVisible(false)
          bg.setStrokeStyle(3, 0x654321)
          break
          
        case PlantingStage.GROWING:
          const product = getFuturesProductById(slotData.cropId!)
          cropIcon.setText(product?.icon || '🌱')
          cropIcon.setVisible(true)
          
          // 显示进度条
          progressBg.setVisible(true)
          progressFill.setVisible(true)
          const progress = 1 - (slotData.remainingTime / slotData.growthTime)
          progressFill.setDisplaySize((this.slotSize - 20) * progress, 8)
          
          // 显示品质光环
          qualityAura.setVisible(true)
          qualityAura.setStrokeStyle(4, this.getRarityColor(slotData.quality), 0.8)
          
          // 显示剩余时间
          timeText.setVisible(true)
          const minutes = Math.floor(slotData.remainingTime / 60000)
          const seconds = Math.floor((slotData.remainingTime % 60000) / 1000)
          timeText.setText(`${minutes}:${seconds.toString().padStart(2, '0')}`)
          
          bg.setStrokeStyle(3, 0x4A90E2)
          break
          
        case PlantingStage.READY_HARVEST:
          const readyProduct = getFuturesProductById(slotData.cropId!)
          cropIcon.setText(readyProduct?.icon || '🌾')
          cropIcon.setVisible(true)
          
          progressBg.setVisible(false)
          progressFill.setVisible(false)
          timeText.setVisible(false)
          
          // 闪烁的品质光环
          qualityAura.setVisible(true)
          qualityAura.setStrokeStyle(6, this.getRarityColor(slotData.quality), 1)
          
          // 添加闪烁动画
          this.tweens.add({
            targets: qualityAura,
            alpha: 0.3,
            duration: 500,
            yoyo: true,
            repeat: -1
          })
          
          bg.setStrokeStyle(4, 0x4CAF50)
          break
      }
    })
  }

  private updateInfoPanel(): void {
    const farmData = this.plantingManager.getFarmData()
    const statsText = this.infoPanel.getData('statsText') as Phaser.GameObjects.Text
    
    const emptySlots = farmData.slots.filter(slot => slot.stage === PlantingStage.EMPTY).length
    const growingSlots = farmData.slots.filter(slot => slot.stage === PlantingStage.GROWING).length
    const readySlots = farmData.slots.filter(slot => slot.stage === PlantingStage.READY_HARVEST).length
    
    const statsInfo = [
      `等级: ${farmData.level}`,
      `经验: ${farmData.experience}`,
      `金币: ${farmData.coins}`,
      ``,
      `空地: ${emptySlots}`,
      `生长中: ${growingSlots}`,
      `可收获: ${readySlots}`,
      ``,
      `收集进度: ${Object.keys(farmData.collection).length}/78`
    ].join('\n')
    
    statsText.setText(statsInfo)
  }

  private showHarvestResult(result: HarvestResult): void {
    if (this.showingHarvestResult) return
    
    this.showingHarvestResult = true
    
    // 创建收获结果弹窗
    const modal = this.add.container(400, 300)
    
    const bg = this.add.rectangle(0, 0, 300, 200, 0x2C5530, 0.95)
    bg.setStrokeStyle(3, this.getRarityColor(result.quality))
    
    const product = getFuturesProductById(result.cropId)
    const icon = this.add.text(0, -50, product?.icon || '🌾', {
      fontSize: '48px'
    }).setOrigin(0.5)
    
    const title = this.add.text(0, -10, `收获 ${product?.name}！`, {
      fontSize: '18px',
      color: '#FFFFFF',
      fontFamily: 'Arial',
      fontStyle: 'bold'
    }).setOrigin(0.5)
    
    const info = this.add.text(0, 20, `数量: ${result.quantity}\n经验: +${result.experience}\n金币: +${result.coins}`, {
      fontSize: '14px',
      color: '#E8F5E8',
      fontFamily: 'Arial',
      align: 'center'
    }).setOrigin(0.5)
    
    const closeButton = this.add.text(0, 60, '确定', {
      fontSize: '16px',
      color: '#FFFFFF',
      fontFamily: 'Arial',
      backgroundColor: '#4CAF50',
      padding: { x: 20, y: 8 }
    }).setOrigin(0.5).setInteractive()
    
    closeButton.on('pointerdown', () => {
      modal.destroy()
      this.showingHarvestResult = false
    })
    
    modal.add([bg, icon, title, info, closeButton])
    
    // 添加弹出动画
    modal.setScale(0)
    this.tweens.add({
      targets: modal,
      scaleX: 1,
      scaleY: 1,
      duration: 300,
      ease: 'Back.easeOut'
    })
    
    // 自动关闭
    this.time.delayedCall(3000, () => {
      if (this.showingHarvestResult) {
        modal.destroy()
        this.showingHarvestResult = false
      }
    })
  }

  private addPlantingEffect(slotId: string): void {
    const slot = this.farmSlots.get(slotId)
    if (!slot) return
    
    // 添加种植粒子效果
    for (let i = 0; i < 8; i++) {
      const particle = this.add.circle(
        slot.x + (Math.random() - 0.5) * 40,
        slot.y + (Math.random() - 0.5) * 40,
        3,
        0x4CAF50
      )
      
      this.tweens.add({
        targets: particle,
        y: particle.y - 30,
        alpha: 0,
        duration: 1000,
        ease: 'Cubic.easeOut',
        onComplete: () => particle.destroy()
      })
    }
  }

  private addHarvestEffect(slotId: string): void {
    const slot = this.farmSlots.get(slotId)
    if (!slot) return
    
    // 添加收获粒子效果
    for (let i = 0; i < 12; i++) {
      const particle = this.add.circle(
        slot.x + (Math.random() - 0.5) * 60,
        slot.y + (Math.random() - 0.5) * 60,
        4,
        0xFFD700
      )
      
      this.tweens.add({
        targets: particle,
        y: particle.y - 50,
        x: particle.x + (Math.random() - 0.5) * 100,
        alpha: 0,
        duration: 1500,
        ease: 'Cubic.easeOut',
        onComplete: () => particle.destroy()
      })
    }
  }

  private showSlotInfo(slotId: string): void {
    const slotData = this.plantingManager.getSlot(slotId)
    if (!slotData) return
    
    const product = getFuturesProductById(slotData.cropId!)
    const minutes = Math.floor(slotData.remainingTime / 60000)
    const seconds = Math.floor((slotData.remainingTime % 60000) / 1000)
    
    this.showNotification(
      `${product?.name}\n品质: ${this.getRarityName(slotData.quality)}\n剩余: ${minutes}:${seconds.toString().padStart(2, '0')}`,
      0x2196F3
    )
  }

  private showNotification(message: string, color: number): void {
    const notification = this.add.text(400, 100, message, {
      fontSize: '16px',
      color: '#FFFFFF',
      fontFamily: 'Arial',
      backgroundColor: `#${color.toString(16).padStart(6, '0')}`,
      padding: { x: 16, y: 12 },
      align: 'center'
    }).setOrigin(0.5)
    
    // 动画效果
    notification.setAlpha(0)
    this.tweens.add({
      targets: notification,
      alpha: 1,
      y: 80,
      duration: 300,
      ease: 'Back.easeOut'
    })
    
    this.time.delayedCall(2000, () => {
      this.tweens.add({
        targets: notification,
        alpha: 0,
        y: 60,
        duration: 300,
        onComplete: () => notification.destroy()
      })
    })
  }

  private getRarityColor(rarity: ItemRarity): number {
    const colors = {
      [ItemRarity.GRAY]: 0x9E9E9E,
      [ItemRarity.GREEN]: 0x4CAF50,
      [ItemRarity.BLUE]: 0x2196F3,
      [ItemRarity.ORANGE]: 0xFF9800,
      [ItemRarity.GOLD]: 0xFFD700,
      [ItemRarity.GOLD_RED]: 0xFF4444
    }
    return colors[rarity]
  }

  private getRarityName(rarity: ItemRarity): string {
    const names = {
      [ItemRarity.GRAY]: '普通',
      [ItemRarity.GREEN]: '优质',
      [ItemRarity.BLUE]: '稀有',
      [ItemRarity.ORANGE]: '史诗',
      [ItemRarity.GOLD]: '传说',
      [ItemRarity.GOLD_RED]: '神话'
    }
    return names[rarity]
  }

  private setupControls(): void {
    // 空格键快速种植
    this.input.keyboard?.on('keydown-SPACE', () => {
      this.quickPlant()
    })
    
    // H键收获全部
    this.input.keyboard?.on('keydown-H', () => {
      this.harvestAll()
    })
    
    // ESC键返回
    this.input.keyboard?.on('keydown-ESC', () => {
      console.log('🔙 返回主菜单')
    })
  }

  update() {
    // Phaser更新循环
  }
} 