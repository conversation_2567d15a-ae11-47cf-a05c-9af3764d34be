import Phaser from 'phaser'
import { GameStateManager } from '../../managers/GameStateManager'
import { HarvestManager } from '../managers/HarvestManager'
import { PlantingManager, PlantingMode } from '../managers/PlantingManager'
import { CropSprite } from '../objects/CropSprite'
import { CropType, CropStage, CropQuality } from '../../types/crop'

export class HarvestTestScene extends Phaser.Scene {
  private gameStateManager!: GameStateManager
  private harvestManager!: HarvestManager
  private plantingManager!: PlantingManager
  private cropSprites: Map<string, CropSprite> = new Map()
  
  private gridSize = { width: 8, height: 6 }
  private tileSize: number = 80
  private startX: number = 150
  private startY: number = 100
  
  // UI元素
  private controlPanel!: Phaser.GameObjects.Container
  private infoText!: Phaser.GameObjects.Text
  private statsText!: Phaser.GameObjects.Text

  constructor() {
    super({ key: 'HarvestTestScene' })
  }

  create() {
    // 初始化GameStateManager
    this.gameStateManager = new GameStateManager()
    
    // 创建背景
    this.createBackground()
    
    // 创建农场网格
    this.createFarmGrid()
    
    // 初始化管理器
    this.initializeManagers()
    
    // 创建控制面板
    this.createControlPanel()
    
    // 创建信息显示
    this.createInfoDisplay()
    
    // 种植一些测试作物
    this.seedTestCrops()
    
    // 刷新显示
    this.refreshFarmDisplay()
    
    console.log('🧪 收获测试场景已加载')
  }

  private createBackground() {
    // 简单的渐变背景
    const bg = this.add.graphics()
    bg.fillGradientStyle(0x87CEEB, 0x87CEEB, 0x90EE90, 0x90EE90, 1)
    bg.fillRect(0, 0, 800, 600)
    
    // 标题
    this.add.text(400, 30, '🧪 收获系统测试', {
      fontSize: '24px',
      color: '#2D5530',
      fontFamily: 'Arial',
      fontStyle: 'bold'
    }).setOrigin(0.5)
  }

  private createFarmGrid() {
    const farmGrid = this.add.group()
    
    for (let row = 0; row < this.gridSize.height; row++) {
      for (let col = 0; col < this.gridSize.width; col++) {
        const x = this.startX + col * this.tileSize
        const y = this.startY + row * this.tileSize
        
        const tile = this.add.rectangle(x + this.tileSize/2, y + this.tileSize/2, 
          this.tileSize - 2, this.tileSize - 2, 0x8B4513, 0.8)
        tile.setStrokeStyle(1, 0x654321)
        tile.setInteractive({ useHandCursor: true })
        
        // 点击处理
        tile.on('pointerdown', () => this.onTileClick(row, col))
        
        farmGrid.add(tile)
      }
    }
  }

  private initializeManagers() {
    // 初始化收获管理器
    this.harvestManager = new HarvestManager(this, this.gameStateManager)
    
    // 初始化种植管理器
    this.plantingManager = new PlantingManager(this, this.gameStateManager)
    
    console.log('🔧 管理器初始化完成')
  }

  private createControlPanel() {
    this.controlPanel = this.add.container(50, 400)
    
    // 背景
    const panelBg = this.add.rectangle(0, 0, 200, 160, 0x2C5530, 0.9)
    panelBg.setStrokeStyle(2, 0x90EE90)
    this.controlPanel.add(panelBg)
    
    // 标题
    const title = this.add.text(0, -70, '🎮 控制面板', {
      fontSize: '14px',
      color: '#FFD700',
      fontFamily: 'Arial',
      fontStyle: 'bold'
    }).setOrigin(0.5)
    this.controlPanel.add(title)
    
    // 按钮配置
    const buttons = [
      { text: '🌱 快速种植', action: () => this.quickPlant() },
      { text: '⏰ 快速成熟', action: () => this.quickMature() },
      { text: '🌾 批量收获', action: () => this.batchHarvest() },
      { text: '🗑️ 清空农场', action: () => this.clearFarm() }
    ]
    
    buttons.forEach((config, index) => {
      const y = -40 + index * 30
      
      const button = this.add.rectangle(0, y, 180, 25, 0x90EE90, 0.8)
      button.setStrokeStyle(1, 0x228B22)
      button.setInteractive({ useHandCursor: true })
      this.controlPanel.add(button)
      
      const buttonText = this.add.text(0, y, config.text, {
        fontSize: '12px',
        color: '#1A4A1A',
        fontFamily: 'Arial'
      }).setOrigin(0.5)
      this.controlPanel.add(buttonText)
      
      // 按钮交互
      button.on('pointerover', () => {
        button.setFillStyle(0xA0FFA0, 1)
      })
      
      button.on('pointerout', () => {
        button.setFillStyle(0x90EE90, 0.8)
      })
      
      button.on('pointerdown', config.action)
    })
  }

  private createInfoDisplay() {
    // 农场状态信息
    this.infoText = this.add.text(400, 480, '', {
      fontSize: '12px',
      color: '#4A4A4A',
      fontFamily: 'Arial',
      align: 'center'
    }).setOrigin(0.5)
    
    // 游戏统计信息
    this.statsText = this.add.text(600, 150, '', {
      fontSize: '11px',
      color: '#2D5530',
      fontFamily: 'Arial',
      backgroundColor: '#FFFFFF',
      padding: { x: 10, y: 10 }
    }).setOrigin(0, 0)
    
    this.updateInfoDisplay()
  }

  private seedTestCrops() {
    const testPositions = [
      { x: 0, y: 0, type: CropType.KNOWLEDGE_FLOWER, stage: CropStage.SEED },
      { x: 1, y: 0, type: CropType.STRENGTH_TREE, stage: CropStage.SPROUT },
      { x: 2, y: 0, type: CropType.TIME_VEGGIE, stage: CropStage.GROWING },
      { x: 3, y: 0, type: CropType.MEDITATION_LOTUS, stage: CropStage.MATURE },
      { x: 0, y: 1, type: CropType.KNOWLEDGE_FLOWER, stage: CropStage.READY_TO_HARVEST },
      { x: 1, y: 1, type: CropType.STRENGTH_TREE, stage: CropStage.READY_TO_HARVEST },
    ]
    
    testPositions.forEach(pos => {
      this.gameStateManager.plantCrop(pos.x, pos.y, pos.type)
      
      // 手动设置阶段
      const gameState = this.gameStateManager.getGameState()
      const crop = gameState.farmGrid.plots[pos.y][pos.x]
      if (crop) {
        crop.stage = pos.stage
        crop.quality = this.getRandomQuality()
        
        // 为成熟作物设置专注时间贡献
        if (pos.stage === CropStage.READY_TO_HARVEST) {
          crop.focusTimeContributed = Math.random() * 3000000 // 0-50分钟
        }
      }
    })
    
    console.log('🌱 测试作物已种植')
  }

  private getRandomQuality(): CropQuality {
    const qualities = [
      CropQuality.COMMON,
      CropQuality.UNCOMMON,
      CropQuality.RARE,
      CropQuality.EPIC,
      CropQuality.LEGENDARY
    ]
    return Phaser.Utils.Array.GetRandom(qualities)
  }

  private quickPlant() {
    const gameState = this.gameStateManager.getGameState()
    let planted = 0
    
    for (let y = 0; y < this.gridSize.height && planted < 10; y++) {
      for (let x = 0; x < this.gridSize.width && planted < 10; x++) {
        if (!gameState.farmGrid.plots[y][x]) {
          const cropType = Phaser.Utils.Array.GetRandom([
            CropType.KNOWLEDGE_FLOWER,
            CropType.STRENGTH_TREE,
            CropType.TIME_VEGGIE,
            CropType.MEDITATION_LOTUS
          ])
          
          this.gameStateManager.plantCrop(x, y, cropType)
          planted++
        }
      }
    }
    
    this.refreshFarmDisplay()
    this.updateInfoDisplay()
    console.log(`🌱 快速种植了 ${planted} 个作物`)
  }

  private quickMature() {
    const gameState = this.gameStateManager.getGameState()
    let matured = 0
    
    gameState.crops.forEach(crop => {
      if (crop.stage !== CropStage.READY_TO_HARVEST) {
        crop.stage = CropStage.READY_TO_HARVEST
        crop.quality = this.getRandomQuality()
        crop.focusTimeContributed = Math.random() * 3000000 // 随机专注时间
        matured++
      }
    })
    
    this.refreshFarmDisplay()
    this.updateInfoDisplay()
    console.log(`⏰ ${matured} 个作物已成熟`)
  }

  private async batchHarvest() {
    const result = await this.harvestManager.harvestAllMature()
    
    this.refreshFarmDisplay()
    this.updateInfoDisplay()
    
    console.log(`🌾 批量收获完成: ${result.totalHarvested}个作物`)
    console.log(`📊 总经验: ${result.totalExperience}`)
    console.log(`💎 总资源:`, result.totalResources)
  }

  private clearFarm() {
    const gameState = this.gameStateManager.getGameState()
    gameState.crops.clear()
    
    // 重置网格
    for (let y = 0; y < this.gridSize.height; y++) {
      for (let x = 0; x < this.gridSize.width; x++) {
        gameState.farmGrid.plots[y][x] = null
      }
    }
    
    this.refreshFarmDisplay()
    this.updateInfoDisplay()
    console.log('🗑️ 农场已清空')
  }

  private async onTileClick(row: number, col: number) {
    const gameState = this.gameStateManager.getGameState()
    const crop = gameState.farmGrid.plots[row]?.[col]
    
    if (!crop) {
      // 空地，尝试种植
      const cropType = Phaser.Utils.Array.GetRandom([
        CropType.KNOWLEDGE_FLOWER,
        CropType.STRENGTH_TREE,
        CropType.TIME_VEGGIE,
        CropType.MEDITATION_LOTUS
      ])
      
      this.gameStateManager.plantCrop(col, row, cropType)
      this.refreshFarmDisplay()
      console.log(`🌱 在 (${col}, ${row}) 种植了 ${this.getCropName(cropType)}`)
    } else if (crop.stage === CropStage.READY_TO_HARVEST) {
      // 成熟作物，收获
      const result = await this.harvestManager.harvestCrop(col, row)
      
      if (result.success) {
        console.log(`🌾 收获成功: ${result.message}`)
        if (result.rewards) {
          console.log(`  经验: +${result.rewards.experience}`)
          console.log(`  资源:`, result.rewards.resources)
          if (result.rewards.bonus) {
            console.log(`  奖励: ${result.rewards.bonus.message}`)
          }
        }
      } else {
        console.log(`❌ 收获失败: ${result.message}`)
      }
      
      this.refreshFarmDisplay()
    } else {
      // 生长中的作物，快速成熟
      crop.stage = CropStage.READY_TO_HARVEST
      crop.quality = this.getRandomQuality()
      crop.focusTimeContributed = Math.random() * 3000000
      
      this.refreshFarmDisplay()
      console.log(`⏰ 作物在 (${col}, ${row}) 已成熟`)
    }
    
    this.updateInfoDisplay()
  }

  private refreshFarmDisplay() {
    // 清除现有作物精灵
    this.cropSprites.forEach(sprite => sprite.destroy())
    this.cropSprites.clear()
    
    const gameState = this.gameStateManager.getGameState()
    
    // 为每个作物创建精灵
    gameState.crops.forEach((crop, cropId) => {
      const { gridX, gridY } = crop.position
      
      if (gridX >= 0 && gridX < this.gridSize.width && 
          gridY >= 0 && gridY < this.gridSize.height) {
        
        const worldX = this.startX + gridX * this.tileSize + this.tileSize / 2
        const worldY = this.startY + gridY * this.tileSize + this.tileSize / 2
        
        const cropSprite = new CropSprite(this, worldX, worldY, crop)
        this.cropSprites.set(cropId, cropSprite)
        
        // 添加质量指示器
        if (crop.stage === CropStage.READY_TO_HARVEST) {
          const qualityColor = this.getQualityColor(crop.quality)
          const qualityIndicator = this.add.circle(worldX + 25, worldY - 25, 8, qualityColor, 0.8)
          qualityIndicator.setStrokeStyle(2, 0xFFFFFF)
        }
      }
    })
  }

  private updateInfoDisplay() {
    const gameState = this.gameStateManager.getGameState()
    const stats = this.gameStateManager.getGameStats()
    
    // 计算作物阶段统计
    const stageStats = {
      seed: 0,
      sprout: 0,
      growing: 0,
      mature: 0,
      ready: 0
    }
    
    gameState.crops.forEach(crop => {
      switch (crop.stage) {
        case CropStage.SEED:
          stageStats.seed++
          break
        case CropStage.SPROUT:
          stageStats.sprout++
          break
        case CropStage.GROWING:
          stageStats.growing++
          break
        case CropStage.MATURE:
          stageStats.mature++
          break
        case CropStage.READY_TO_HARVEST:
          stageStats.ready++
          break
      }
    })
    
    // 更新农场状态信息
    this.infoText.setText([
      `农场状态: 总作物 ${gameState.crops.size}`,
      `种子: ${stageStats.seed} | 幼苗: ${stageStats.sprout} | 生长: ${stageStats.growing} | 成熟: ${stageStats.mature} | 可收获: ${stageStats.ready}`,
      '💡 点击空地种植 | 点击生长中作物加速成熟 | 点击成熟作物收获'
    ].join('\n'))
    
    // 更新游戏统计信息
    this.statsText.setText([
      '📊 游戏统计',
      '',
      `等级: ${gameState.level}`,
      `经验: ${gameState.experience} / ${stats.nextLevelExp}`,
      `总收获: ${gameState.totalCropsHarvested}`,
      '',
      '💎 资源:',
      `知识: ${gameState.resources.knowledge}`,
      `力量: ${gameState.resources.strength}`,
      `时间: ${gameState.resources.time}`,
      `冥想: ${gameState.resources.meditation}`
    ].join('\n'))
  }

  private getCropName(cropType: CropType): string {
    const names = {
      [CropType.KNOWLEDGE_FLOWER]: '知识花',
      [CropType.STRENGTH_TREE]: '力量树',
      [CropType.TIME_VEGGIE]: '时间蔬菜',
      [CropType.MEDITATION_LOTUS]: '冥想莲花'
    }
    return names[cropType] || '未知作物'
  }

  private getQualityColor(quality: CropQuality): number {
    const colors = {
      [CropQuality.COMMON]: 0x808080,
      [CropQuality.UNCOMMON]: 0x32CD32,
      [CropQuality.RARE]: 0x4169E1,
      [CropQuality.EPIC]: 0x9370DB,
      [CropQuality.LEGENDARY]: 0xFFD700
    }
    return colors[quality] || 0x808080
  }

  update() {
    // 更新作物精灵状态
    this.cropSprites.forEach(cropSprite => {
      cropSprite.update()
    })
    
    // 定期更新信息显示
    if (this.time.now % 1000 < 16) { // 每秒更新一次
      this.updateInfoDisplay()
    }
  }

  shutdown() {
    if (this.harvestManager) {
      this.harvestManager.destroy()
    }
  }
} 