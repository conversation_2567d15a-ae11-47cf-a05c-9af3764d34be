import Phaser from 'phaser'
import { ItemIntegrationManager, IntegratedItem } from '../../managers/ItemIntegrationManager'
import { UnifiedInventoryPanel } from '../../components/UnifiedInventoryPanel'
import { LootboxType, ItemRarity, ItemCategory, ItemType } from '../../types/lootbox'
import { AgriculturalItem, GrowthStage } from '../../types/agriculture'

export class UnifiedAgriculturalScene extends Phaser.Scene {
  private itemManager!: ItemIntegrationManager
  private farmSlots: Map<string, Phaser.GameObjects.Container> = new Map()
  private gridSize = { width: 6, height: 4 }
  private slotSize = 80
  private startX = 100
  private startY = 150
  
  // UI 容器
  private uiContainer!: Phaser.GameObjects.Container
  private inventoryContainer!: Phaser.GameObjects.Container
  private lootboxContainer!: Phaser.GameObjects.Container
  private synthesisContainer!: Phaser.GameObjects.Container
  
  // 状态
  private selectedItems: string[] = []
  private currentMode: 'farm' | 'inventory' | 'lootbox' | 'synthesis' = 'farm'

  constructor() {
    super({ key: 'UnifiedAgriculturalScene' })
  }

  preload() {
    // 预加载占位符资源
    this.load.image('slot_empty', 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==')
  }

  create() {
    console.log('🌾 统一农产品场景启动')
    
    // 初始化管理器
    this.initializeManagers()
    
    // 创建UI界面
    this.createUI()
    
    // 创建农场网格
    this.createFarmGrid()
    
    // 添加一些测试数据
    this.addTestItems()
    
    // 设置控制
    this.setupControls()
    
    console.log('✅ 农产品系统初始化完成')
  }

  private initializeManagers(): void {
    this.itemManager = new ItemIntegrationManager()
    
    // 监听物品相关事件
    this.itemManager.onItemAdded((item) => {
      console.log('📦 新物品添加:', item.name)
      this.updateUI()
    })
    
    this.itemManager.onItemPlanted((data) => {
      console.log('🌱 物品种植:', data.item.name, '在位置:', data.slotId)
      this.updateFarmDisplay()
    })
    
    this.itemManager.onItemHarvested((item) => {
      console.log('🌾 物品收获:', item.name)
      this.updateFarmDisplay()
      this.updateUI()
    })
    
    this.itemManager.onItemsSynthesized((data) => {
      console.log('⚗️ 物品合成完成:', data.result.name)
      this.updateUI()
    })
  }

  private createUI(): void {
    // 创建背景
    this.add.rectangle(400, 300, 800, 600, 0x87CEEB, 0.3)
    
    // 标题
    this.add.text(400, 30, '🌾 农产品物品道具数值策划系统', {
      fontSize: '24px',
      color: '#2C5530',
      fontFamily: 'Arial Black',
      fontStyle: 'bold'
    }).setOrigin(0.5)
    
    // 创建主要UI容器
    this.uiContainer = this.add.container(0, 0)
    
    // 模式切换按钮
    this.createModeButtons()
    
    // 创建各个功能面板
    this.createInventoryPanel()
    this.createLootboxPanel()
    this.createSynthesisPanel()
    this.createStatsPanel()
  }

  private createModeButtons(): void {
    const modes = [
      { key: 'farm', label: '🚜 农场', x: 100 },
      { key: 'inventory', label: '🎒 背包', x: 200 },
      { key: 'lootbox', label: '📦 盲盒', x: 300 },
      { key: 'synthesis', label: '⚗️ 合成', x: 400 }
    ]
    
    modes.forEach(mode => {
      const button = this.add.container(mode.x, 70)
      
      const bg = this.add.rectangle(0, 0, 90, 30, 0x4CAF50, 0.8)
      bg.setStrokeStyle(2, 0x2E7D32)
      
      const text = this.add.text(0, 0, mode.label, {
        fontSize: '12px',
        color: '#FFFFFF',
        fontFamily: 'Arial'
      }).setOrigin(0.5)
      
      button.add([bg, text])
      button.setInteractive(new Phaser.Geom.Rectangle(-45, -15, 90, 30), Phaser.Geom.Rectangle.Contains)
      
      button.on('pointerdown', () => {
        this.switchMode(mode.key as any)
      })
      
      button.on('pointerover', () => {
        bg.setFillStyle(0x66BB6A, 0.9)
      })
      
      button.on('pointerout', () => {
        const isActive = this.currentMode === mode.key
        bg.setFillStyle(isActive ? 0x2E7D32 : 0x4CAF50, 0.8)
      })
    })
  }

  private createFarmGrid(): void {
    // 创建农田网格
    for (let y = 0; y < this.gridSize.height; y++) {
      for (let x = 0; x < this.gridSize.width; x++) {
        const slotId = `slot_${x}_${y}`
        const worldX = this.startX + x * (this.slotSize + 10)
        const worldY = this.startY + y * (this.slotSize + 10)
        
        const slot = this.add.container(worldX, worldY)
        
        // 土地背景
        const bg = this.add.rectangle(0, 0, this.slotSize, this.slotSize, 0x8B4513, 0.6)
        bg.setStrokeStyle(2, 0x654321)
        
        // 状态指示器
        const statusIcon = this.add.text(0, 0, '🌱', {
          fontSize: '32px'
        }).setOrigin(0.5).setVisible(false)
        
        slot.add([bg, statusIcon])
        slot.setData('slotId', slotId)
        slot.setData('isEmpty', true)
        slot.setData('statusIcon', statusIcon)
        
        // 添加点击交互
        slot.setInteractive(new Phaser.Geom.Rectangle(-this.slotSize/2, -this.slotSize/2, this.slotSize, this.slotSize), Phaser.Geom.Rectangle.Contains)
        
        slot.on('pointerdown', () => {
          this.handleSlotClick(slotId)
        })
        
        slot.on('pointerover', () => {
          bg.setStrokeStyle(3, 0xFFD700)
        })
        
        slot.on('pointerout', () => {
          bg.setStrokeStyle(2, 0x654321)
        })
        
        this.farmSlots.set(slotId, slot)
      }
    }
  }

  private createInventoryPanel(): void {
    this.inventoryContainer = this.add.container(550, 150)
    
    // 背景
    const bg = this.add.rectangle(0, 0, 220, 350, 0x2C5530, 0.9)
    bg.setStrokeStyle(2, 0x4CAF50)
    
    // 标题
    const title = this.add.text(0, -160, '🎒 物品背包', {
      fontSize: '16px',
      color: '#FFFFFF',
      fontFamily: 'Arial',
      fontStyle: 'bold'
    }).setOrigin(0.5)
    
    this.inventoryContainer.add([bg, title])
    this.inventoryContainer.setVisible(false)
  }

  private createLootboxPanel(): void {
    this.lootboxContainer = this.add.container(550, 150)
    
    // 背景
    const bg = this.add.rectangle(0, 0, 220, 350, 0x9C27B0, 0.9)
    bg.setStrokeStyle(2, 0xE91E63)
    
    // 标题
    const title = this.add.text(0, -160, '📦 盲盒抽奖', {
      fontSize: '16px',
      color: '#FFFFFF',
      fontFamily: 'Arial',
      fontStyle: 'bold'
    }).setOrigin(0.5)
    
    // 盲盒类型按钮
    const lootboxTypes = [
      { type: LootboxType.BASIC_FARM, label: '基础农场盒', cost: '50🪙', y: -100 },
      { type: LootboxType.PREMIUM_FARM, label: '高级农场盒', cost: '200🪙', y: -50 },
      { type: LootboxType.LEGENDARY_FARM, label: '传说农场盒', cost: '500🪙', y: 0 },
      { type: LootboxType.FUTURES_MYSTERY, label: '期货神秘盒', cost: '300🪙', y: 50 }
    ]
    
    lootboxTypes.forEach(lootbox => {
      const button = this.add.container(0, lootbox.y)
      
      const buttonBg = this.add.rectangle(0, 0, 180, 35, 0xFF9800, 0.8)
      buttonBg.setStrokeStyle(1, 0xF57C00)
      
      const buttonText = this.add.text(0, -8, lootbox.label, {
        fontSize: '12px',
        color: '#FFFFFF',
        fontFamily: 'Arial',
        fontStyle: 'bold'
      }).setOrigin(0.5)
      
      const costText = this.add.text(0, 8, lootbox.cost, {
        fontSize: '10px',
        color: '#FFE0B2',
        fontFamily: 'Arial'
      }).setOrigin(0.5)
      
      button.add([buttonBg, buttonText, costText])
      button.setInteractive(new Phaser.Geom.Rectangle(-90, -17.5, 180, 35), Phaser.Geom.Rectangle.Contains)
      
      button.on('pointerdown', () => {
        this.openLootbox(lootbox.type)
      })
      
      this.lootboxContainer.add(button)
    })
    
    this.lootboxContainer.add([bg, title])
    this.lootboxContainer.setVisible(false)
  }

  private createSynthesisPanel(): void {
    this.synthesisContainer = this.add.container(550, 150)
    
    // 背景
    const bg = this.add.rectangle(0, 0, 220, 350, 0x673AB7, 0.9)
    bg.setStrokeStyle(2, 0x9C27B0)
    
    // 标题
    const title = this.add.text(0, -160, '⚗️ 道具合成', {
      fontSize: '16px',
      color: '#FFFFFF',
      fontFamily: 'Arial',
      fontStyle: 'bold'
    }).setOrigin(0.5)
    
    // 合成说明
    const instructions = this.add.text(0, -120, '选择2个同品质同品种道具\n进行合成升级', {
      fontSize: '12px',
      color: '#E1BEE7',
      fontFamily: 'Arial',
      align: 'center'
    }).setOrigin(0.5)
    
    // 合成按钮
    const synthesisButton = this.add.container(0, 100)
    const buttonBg = this.add.rectangle(0, 0, 150, 40, 0x4CAF50, 0.8)
    buttonBg.setStrokeStyle(2, 0x2E7D32)
    
    const buttonText = this.add.text(0, 0, '开始合成', {
      fontSize: '14px',
      color: '#FFFFFF',
      fontFamily: 'Arial',
      fontStyle: 'bold'
    }).setOrigin(0.5)
    
    synthesisButton.add([buttonBg, buttonText])
    synthesisButton.setInteractive(new Phaser.Geom.Rectangle(-75, -20, 150, 40), Phaser.Geom.Rectangle.Contains)
    
    synthesisButton.on('pointerdown', () => {
      this.performSynthesis()
    })
    
    this.synthesisContainer.add([bg, title, instructions, synthesisButton])
    this.synthesisContainer.setVisible(false)
  }

  private createStatsPanel(): void {
    // 统计信息面板（固定显示）
    const statsContainer = this.add.container(650, 450)
    
    const bg = this.add.rectangle(0, 0, 140, 120, 0x607D8B, 0.9)
    bg.setStrokeStyle(2, 0x455A64)
    
    const title = this.add.text(0, -45, '📊 统计', {
      fontSize: '14px',
      color: '#FFFFFF',
      fontFamily: 'Arial',
      fontStyle: 'bold'
    }).setOrigin(0.5)
    
    const statsText = this.add.text(0, -10, '总物品: 0\n种植中: 0\n可收获: 0', {
      fontSize: '12px',
      color: '#CFD8DC',
      fontFamily: 'Arial',
      align: 'center'
    }).setOrigin(0.5)
    
    statsContainer.add([bg, title, statsText])
    statsContainer.setData('statsText', statsText)
  }

  private switchMode(mode: 'farm' | 'inventory' | 'lootbox' | 'synthesis'): void {
    this.currentMode = mode
    
    // 隐藏所有面板
    this.inventoryContainer.setVisible(false)
    this.lootboxContainer.setVisible(false)
    this.synthesisContainer.setVisible(false)
    
    // 显示对应面板
    switch (mode) {
      case 'inventory':
        this.inventoryContainer.setVisible(true)
        this.updateInventoryDisplay()
        break
      case 'lootbox':
        this.lootboxContainer.setVisible(true)
        break
      case 'synthesis':
        this.synthesisContainer.setVisible(true)
        break
    }
    
    this.updateModeButtons()
  }

  private updateModeButtons(): void {
    // 更新模式按钮样式
    // 这里可以添加按钮状态更新逻辑
  }

  private handleSlotClick(slotId: string): void {
    if (this.currentMode !== 'farm') return
    
    const slot = this.farmSlots.get(slotId)
    if (!slot) return
    
    const isEmpty = slot.getData('isEmpty')
    
    if (isEmpty) {
      // 空地，尝试种植
      this.plantOnSlot(slotId)
    } else {
      // 有作物，尝试收获
      this.harvestFromSlot(slotId)
    }
  }

  private async plantOnSlot(slotId: string): Promise<void> {
    // 获取可种植的种子
    const seeds = this.itemManager.getPlantableSeeds()
    
    if (seeds.length === 0) {
      this.showNotification('没有可种植的种子！', 0xFF5722)
      return
    }
    
    // 使用第一个种子进行种植
    const seed = seeds[0]
    const result = await this.itemManager.plantItem(seed.id, slotId)
    
    if (result.success) {
      this.showNotification(`成功种植 ${seed.name}！`, 0x4CAF50)
    } else {
      this.showNotification(`种植失败: ${result.message}`, 0xFF5722)
    }
  }

  private async harvestFromSlot(slotId: string): Promise<void> {
    // 这里需要实现收获逻辑
    this.showNotification('收获功能待实现', 0xFFC107)
  }

  private async openLootbox(lootboxType: LootboxType): Promise<void> {
    const result = await this.itemManager.openLootbox(lootboxType)
    
    if (result.success) {
      const itemNames = result.items.map(item => item.name).join(', ')
      this.showNotification(`获得: ${itemNames}`, 0x4CAF50)
    } else {
      this.showNotification(`开盒失败: ${result.error}`, 0xFF5722)
    }
  }

  private async performSynthesis(): Promise<void> {
    if (this.selectedItems.length !== 2) {
      this.showNotification('请选择2个物品进行合成', 0xFFC107)
      return
    }
    
    // 使用增强的合成系统
    const result = await this.itemManager.synthesizeItems(this.selectedItems)
    
    if (result.success) {
      this.showNotification(`合成成功！获得 ${result.resultItem?.name}`, 0x4CAF50)
    } else {
      this.showNotification(`合成失败: ${result.error}`, 0xFF5722)
    }
    
    this.selectedItems = []
  }



  private addTestItems(): void {
    // 添加测试物品
    console.log('🧪 添加测试数据...')
    
    // 模拟获得一些盲盒物品
    this.time.delayedCall(1000, () => {
      this.openLootbox(LootboxType.BASIC_FARM)
    })
    
    this.time.delayedCall(2000, () => {
      this.openLootbox(LootboxType.PREMIUM_FARM)
    })
  }

  private updateFarmDisplay(): void {
    // 更新农场显示
    this.farmSlots.forEach((slot, slotId) => {
      const statusIcon = slot.getData('statusIcon')
      // 这里可以根据实际种植状态更新显示
      // 暂时设置为空
      statusIcon.setVisible(false)
      slot.setData('isEmpty', true)
    })
  }

  private updateInventoryDisplay(): void {
    // 更新背包显示
    const items = this.itemManager.getAllItems()
    console.log('📦 当前物品数量:', items.length)
    
    // 这里可以添加详细的背包UI更新逻辑
  }

  private updateUI(): void {
    // 更新统计信息
    const items = this.itemManager.getAllItems()
    const totalItems = items.reduce((sum, item) => sum + item.quantity, 0)
    
    // 寻找统计文本并更新
    const statsContainer = this.children.list.find(child => 
      child instanceof Phaser.GameObjects.Container && 
      child.getData('statsText')
    ) as Phaser.GameObjects.Container
    
    if (statsContainer) {
      const statsText = statsContainer.getData('statsText') as Phaser.GameObjects.Text
      statsText.setText(`总物品: ${totalItems}\n种植中: 0\n可收获: 0`)
    }
  }

  private setupControls(): void {
    // 添加键盘快捷键
    this.input.keyboard?.on('keydown-ONE', () => this.switchMode('farm'))
    this.input.keyboard?.on('keydown-TWO', () => this.switchMode('inventory'))
    this.input.keyboard?.on('keydown-THREE', () => this.switchMode('lootbox'))
    this.input.keyboard?.on('keydown-FOUR', () => this.switchMode('synthesis'))
    
    // ESC键返回主菜单
    this.input.keyboard?.on('keydown-ESC', () => {
      console.log('🔙 返回主菜单')
      this.scene.start('MainScene')
    })
  }

  private showNotification(message: string, color: number = 0x4CAF50): void {
    // 创建通知文本
    const notification = this.add.text(400, 500, message, {
      fontSize: '16px',
      color: '#FFFFFF',
      fontFamily: 'Arial',
      backgroundColor: `#${color.toString(16).padStart(6, '0')}`,
      padding: { x: 10, y: 5 }
    }).setOrigin(0.5)
    
    // 淡入淡出动画
    notification.setAlpha(0)
    this.tweens.add({
      targets: notification,
      alpha: 1,
      duration: 300,
      yoyo: true,
      hold: 2000,
      onComplete: () => {
        notification.destroy()
      }
    })
  }

  update() {
    // 游戏主循环更新
  }
} 