import Phaser from 'phaser'
import { GameState } from '../../contexts/GameContext'
import { GameStateManager } from '../../managers/GameStateManager'
import { PlantingManager, PlantingMode } from '../managers/PlantingManager'
import { CropSprite } from '../objects/CropSprite'
import { CropType, CropStage, CropInstance } from '../../types/crop'

export class EnhancedFarmScene extends Phaser.Scene {
  private farmGrid: Phaser.GameObjects.Group | null = null
  private cropSprites: Map<string, CropSprite> = new Map()
  private gridSize = { width: 8, height: 6 }
  private tileSize = 80
  private startX = 200
  private startY = 100
  private particles: Phaser.GameObjects.Particles.ParticleEmitter | null = null
  private clouds: Phaser.GameObjects.Group | null = null
  
  // 管理器
  private gameStateManager!: GameStateManager
  private plantingManager!: PlantingManager
  
  // UI元素
  private plantButton!: Phaser.GameObjects.Container
  private farmInfoPanel!: Phaser.GameObjects.Container
  private statusText!: Phaser.GameObjects.Text

  constructor() {
    super({ key: 'EnhancedFarmScene' })
  }

  init(data: { gameStateManager: GameStateManager }) {
    this.gameStateManager = data.gameStateManager
  }

  preload() {
    this.createEnhancedTiles()
    this.createParticleTextures()
    this.createUITextures()
  }

  create() {
    // 创建背景和环境
    this.createEnhancedBackground()
    this.createAnimatedClouds()
    this.createParticleSystem()
    
    // 创建农场网格
    this.createFarmGrid()
    
    // 初始化种植管理器
    this.initializePlantingManager()
    
    // 创建UI
    this.createUI()
    this.createFarmInfoPanel()
    
    // 监听游戏状态变化
    this.setupGameStateListeners()
    
    // 初始加载农场状态
    this.refreshFarmDisplay()
    
    // 显示欢迎信息
    this.showWelcomeMessage()
    
    // 环境动画
    this.createEnvironmentAnimations()
  }

  private createEnhancedTiles() {
    // 土地瓦片
    const soilGraphics = this.add.graphics()
    soilGraphics.fillGradientStyle(0x8B4513, 0x8B4513, 0x654321, 0x654321, 1)
    soilGraphics.fillRect(0, 0, this.tileSize, this.tileSize)
    
    // 土地纹理
    soilGraphics.lineStyle(1, 0x5D2E0A, 0.3)
    for (let i = 0; i < 5; i++) {
      soilGraphics.lineBetween(
        Math.random() * this.tileSize, 
        Math.random() * this.tileSize,
        Math.random() * this.tileSize, 
        Math.random() * this.tileSize
      )
    }
    
    soilGraphics.lineStyle(2, 0x2F1A0A, 0.5)
    soilGraphics.strokeRect(1, 1, this.tileSize - 2, this.tileSize - 2)
    soilGraphics.generateTexture('soil-tile', this.tileSize, this.tileSize)
    soilGraphics.destroy()

    // 种子
    const seedGraphics = this.add.graphics()
    seedGraphics.fillStyle(0xFFD700)
    seedGraphics.fillCircle(this.tileSize/2, this.tileSize/2, 8)
    seedGraphics.fillStyle(0xFFFFAA, 0.6)
    seedGraphics.fillCircle(this.tileSize/2 - 2, this.tileSize/2 - 2, 3)
    seedGraphics.generateTexture('seed', this.tileSize, this.tileSize)
    seedGraphics.destroy()

    // 幼苗
    const sproutGraphics = this.add.graphics()
    sproutGraphics.fillStyle(0x228B22)
    sproutGraphics.fillRect(this.tileSize/2 - 2, this.tileSize/2 + 10, 4, 15)
    sproutGraphics.fillStyle(0x32CD32)
    sproutGraphics.fillEllipse(this.tileSize/2 - 8, this.tileSize/2, 8, 12)
    sproutGraphics.fillEllipse(this.tileSize/2 + 8, this.tileSize/2, 8, 12)
    sproutGraphics.generateTexture('sprout', this.tileSize, this.tileSize)
    sproutGraphics.destroy()

    // 知识花
    const flowerGraphics = this.add.graphics()
    // 花茎
    flowerGraphics.fillStyle(0x228B22)
    flowerGraphics.fillRect(this.tileSize/2 - 3, this.tileSize/2 + 5, 6, 20)
    // 花瓣
    flowerGraphics.fillStyle(0xFF69B4)
    for (let i = 0; i < 8; i++) {
      const angle = (i / 8) * Math.PI * 2
      const x = this.tileSize/2 + Math.cos(angle) * 12
      const y = this.tileSize/2 + Math.sin(angle) * 12
      flowerGraphics.fillCircle(x, y, 6)
    }
    // 花心
    flowerGraphics.fillStyle(0xFFD700)
    flowerGraphics.fillCircle(this.tileSize/2, this.tileSize/2, 8)
    flowerGraphics.generateTexture('knowledge-flower', this.tileSize, this.tileSize)
    flowerGraphics.destroy()
  }

  private createParticleTextures() {
    // 闪光粒子 - 用多边形创建星形
    const sparkleGraphics = this.add.graphics()
    sparkleGraphics.fillStyle(0xFFFFFF)
    
    // 手动绘制星形
    const centerX = 8
    const centerY = 8
    const outerRadius = 6
    const innerRadius = 3
    const points = 5
    
    sparkleGraphics.beginPath()
    for (let i = 0; i < points * 2; i++) {
      const angle = (i * Math.PI) / points
      const radius = i % 2 === 0 ? outerRadius : innerRadius
      const x = centerX + Math.cos(angle) * radius
      const y = centerY + Math.sin(angle) * radius
      
      if (i === 0) {
        sparkleGraphics.moveTo(x, y)
      } else {
        sparkleGraphics.lineTo(x, y)
      }
    }
    sparkleGraphics.closePath()
    sparkleGraphics.fillPath()
    
    sparkleGraphics.generateTexture('sparkle', 16, 16)
    sparkleGraphics.destroy()

    // 花粉粒子
    const pollenGraphics = this.add.graphics()
    pollenGraphics.fillStyle(0xFFD700, 0.8)
    pollenGraphics.fillCircle(4, 4, 3)
    pollenGraphics.generateTexture('pollen', 8, 8)
    pollenGraphics.destroy()
  }

  private createUITextures() {
    // 种植按钮图标
    const plantIconGraphics = this.add.graphics()
    plantIconGraphics.fillStyle(0x90EE90)
    plantIconGraphics.fillCircle(15, 15, 12)
    plantIconGraphics.fillStyle(0x228B22)
    plantIconGraphics.fillRect(13, 8, 4, 14)
    plantIconGraphics.fillEllipse(10, 10, 6, 8)
    plantIconGraphics.fillEllipse(20, 10, 6, 8)
    plantIconGraphics.generateTexture('plant-icon', 30, 30)
    plantIconGraphics.destroy()
  }

  private createEnhancedBackground() {
    // 天空渐变
    const skyGraphics = this.add.graphics()
    skyGraphics.fillGradientStyle(0x87CEEB, 0x87CEEB, 0xE0F6FF, 0xE0F6FF, 1)
    skyGraphics.fillRect(0, 0, 800, 600)
    skyGraphics.generateTexture('sky', 800, 600)
    skyGraphics.destroy()
    
    this.add.image(400, 300, 'sky')
  }

  private createAnimatedClouds() {
    this.clouds = this.add.group()
    
    for (let i = 0; i < 3; i++) {
      const cloud = this.add.ellipse(
        100 + i * 250, 
        80 + Math.random() * 40, 
        60 + Math.random() * 40, 
        30 + Math.random() * 20, 
        0xFFFFFF, 
        0.8
      )
      
      this.clouds.add(cloud)
      
      this.tweens.add({
        targets: cloud,
        x: cloud.x + 50,
        duration: 15000 + Math.random() * 10000,
        repeat: -1,
        yoyo: true,
        ease: 'Sine.easeInOut'
      })
    }
  }

  private createParticleSystem() {
    this.particles = this.add.particles(400, 300, 'sparkle', {
      speed: { min: 20, max: 40 },
      scale: { start: 0.2, end: 0 },
      alpha: { start: 0.8, end: 0 },
      lifespan: 3000,
      frequency: 2000,
      quantity: 1
    })
  }

  private createFarmGrid() {
    this.farmGrid = this.add.group()

    for (let row = 0; row < this.gridSize.height; row++) {
      for (let col = 0; col < this.gridSize.width; col++) {
        const x = this.startX + col * this.tileSize
        const y = this.startY + row * this.tileSize

        const soilTile = this.add.image(x, y, 'soil-tile')
          .setOrigin(0, 0)
          .setInteractive({ useHandCursor: true })
          .on('pointerdown', () => this.onTileClick(row, col))
          .on('pointerover', () => this.onTileHover(soilTile, true))
          .on('pointerout', () => this.onTileHover(soilTile, false))

        this.farmGrid.add(soilTile)
      }
    }
  }

  private initializePlantingManager() {
    this.plantingManager = new PlantingManager(this, this.gameStateManager)
    
    // 设置事件监听
    this.plantingManager.onPlantSuccess((result) => {
      console.log('种植成功:', result.message)
      this.refreshFarmDisplay()
      this.updateFarmInfoPanel()
    })
    
    this.plantingManager.onPlantFailure((result) => {
      console.log('种植失败:', result.message)
    })
    
    this.plantingManager.onModeChange((mode) => {
      this.updatePlantButtonState(mode)
    })
  }

  private setupGameStateListeners() {
    this.gameStateManager.on('crop_planted', () => {
      this.refreshFarmDisplay()
      this.updateFarmInfoPanel()
    })
    
    this.gameStateManager.on('crop_harvested', () => {
      this.refreshFarmDisplay()
      this.updateFarmInfoPanel()
    })
  }

  private createUI() {
    // 农场标题
    const titleText = this.add.text(400, 30, '🌱 自律农场 - 种植系统', {
      fontSize: '24px',
      color: '#2C5530',
      fontFamily: 'Arial',
      fontStyle: 'bold'
    }).setOrigin(0.5)
    titleText.setShadow(2, 2, '#000000', 2, false, true)

    // 种植按钮
    this.createPlantButton()
    
    // 状态文本
    this.statusText = this.add.text(400, 550, '点击"开始种植"选择种子', {
      fontSize: '14px',
      color: '#4A4A4A',
      fontFamily: 'Arial'
    }).setOrigin(0.5)
  }

  private createPlantButton() {
    this.plantButton = this.add.container(100, 500)
    
    const buttonBg = this.add.rectangle(0, 0, 120, 50, 0x90EE90, 0.9)
    buttonBg.setStrokeStyle(3, 0x228B22)
    this.plantButton.add(buttonBg)
    
    const buttonIcon = this.add.image(-20, 0, 'plant-icon').setScale(0.8)
    this.plantButton.add(buttonIcon)
    
    const buttonText = this.add.text(15, 0, '开始种植', {
      fontSize: '14px',
      color: '#1A4A1A',
      fontFamily: 'Arial',
      fontStyle: 'bold'
    }).setOrigin(0.5)
    this.plantButton.add(buttonText)
    
    // 添加交互
    buttonBg.setInteractive({ useHandCursor: true })
    
    buttonBg.on('pointerover', () => {
      buttonBg.setFillStyle(0xA0FFA0, 1)
      this.tweens.add({
        targets: this.plantButton,
        scaleX: 1.05,
        scaleY: 1.05,
        duration: 150
      })
    })
    
    buttonBg.on('pointerout', () => {
      buttonBg.setFillStyle(0x90EE90, 0.9)
      this.tweens.add({
        targets: this.plantButton,
        scaleX: 1,
        scaleY: 1,
        duration: 150
      })
    })
    
    buttonBg.on('pointerdown', () => {
      this.togglePlantingMode()
    })
  }

  private createFarmInfoPanel() {
    this.farmInfoPanel = this.add.container(650, 150)
    
    const panelBg = this.add.rectangle(0, 0, 140, 200, 0x2C5530, 0.9)
    panelBg.setStrokeStyle(2, 0x90EE90)
    this.farmInfoPanel.add(panelBg)
    
    const titleText = this.add.text(0, -85, '农场信息', {
      fontSize: '16px',
      color: '#FFFFFF',
      fontFamily: 'Arial',
      fontStyle: 'bold'
    }).setOrigin(0.5)
    this.farmInfoPanel.add(titleText)
    
    this.updateFarmInfoPanel()
  }

  private updateFarmInfoPanel() {
    // 清除旧信息（保留背景和标题）
    const children = this.farmInfoPanel.list.slice(2)
    children.forEach(child => {
      this.farmInfoPanel.remove(child)
      child.destroy()
    })
    
    const gameState = this.gameStateManager.getGameState()
    const stats = this.gameStateManager.getGameStats()
    
    const infoTexts = [
      `等级: ${gameState.level}`,
      `经验: ${gameState.experience}`,
      `作物数: ${stats.cropsCount}`,
      `收获数: ${stats.totalCropsHarvested}`,
      `知识: ${gameState.resources.knowledge}`,
      `力量: ${gameState.resources.strength}`
    ]
    
    infoTexts.forEach((text, index) => {
      const textObj = this.add.text(0, -50 + index * 20, text, {
        fontSize: '12px',
        color: '#CCCCCC',
        fontFamily: 'Arial'
      }).setOrigin(0.5)
      this.farmInfoPanel.add(textObj)
    })
  }

  private togglePlantingMode() {
    if (this.plantingManager.isInPlantingMode()) {
      this.plantingManager.exitPlantingMode()
    } else {
      this.plantingManager.startPlanting()
    }
  }

  private updatePlantButtonState(mode: PlantingMode) {
    const buttonBg = this.plantButton.getAt(0) as Phaser.GameObjects.Rectangle
    const buttonText = this.plantButton.getAt(2) as Phaser.GameObjects.Text
    
    if (mode === PlantingMode.NONE) {
      buttonBg.setFillStyle(0x90EE90, 0.9)
      buttonText.setText('开始种植')
      this.statusText.setText('点击"开始种植"选择种子')
    } else if (mode === PlantingMode.SELECTING_SEED) {
      buttonBg.setFillStyle(0xFFD700, 0.9)
      buttonText.setText('选择中...')
      this.statusText.setText('选择要种植的种子类型')
    } else if (mode === PlantingMode.PLACING_CROP) {
      buttonBg.setFillStyle(0xFF6B6B, 0.9)
      buttonText.setText('取消种植')
      this.statusText.setText('点击绿色区域进行种植')
    }
  }

  private refreshFarmDisplay() {
    // 清除现有作物精灵
    this.cropSprites.forEach(sprite => sprite.destroy())
    this.cropSprites.clear()
    
    const gameState = this.gameStateManager.getGameState()
    
    // 为每个作物创建或更新精灵
    gameState.crops.forEach((crop, cropId) => {
      const { gridX, gridY } = crop.position
      
      // 确保作物在网格范围内
      if (gridX >= 0 && gridX < this.gridSize.width && 
          gridY >= 0 && gridY < this.gridSize.height) {
        
        const worldX = this.startX + gridX * this.tileSize + this.tileSize / 2
        const worldY = this.startY + gridY * this.tileSize + this.tileSize / 2
        
        const cropSprite = new CropSprite(this, worldX, worldY, crop)
        this.cropSprites.set(cropId, cropSprite)
        
        // 添加点击收获功能
        cropSprite.setInteractive({ useHandCursor: true })
        cropSprite.on('pointerdown', () => {
          if (crop.stage === CropStage.READY_TO_HARVEST) {
            this.harvestCrop(gridX, gridY)
          }
        })
      }
    })
  }

  private harvestCrop(gridX: number, gridY: number) {
    const result = this.gameStateManager.harvestCrop(gridX, gridY)
    
    if (result.success) {
      // 播放收获动画
      this.playHarvestAnimation(gridX, gridY)
      console.log('收获成功！', result.rewards)
    } else {
      console.log('收获失败')
    }
  }

  private playHarvestAnimation(gridX: number, gridY: number) {
    const worldX = this.startX + gridX * this.tileSize + this.tileSize / 2
    const worldY = this.startY + gridY * this.tileSize + this.tileSize / 2
    
    // 收获粒子效果
    const harvestParticles = this.add.particles(worldX, worldY, 'sparkle', {
      speed: { min: 50, max: 100 },
      scale: { start: 0.5, end: 0 },
      alpha: { start: 1, end: 0 },
      lifespan: 1000,
      quantity: 15
    })
    
    // 奖励文本
    const rewardText = this.add.text(worldX, worldY - 30, '+10 经验', {
      fontSize: '14px',
      color: '#FFD700',
      fontFamily: 'Arial',
      fontStyle: 'bold'
    }).setOrigin(0.5)
    
    this.tweens.add({
      targets: rewardText,
      y: rewardText.y - 40,
      alpha: 0,
      duration: 1500,
      ease: 'Power2.easeOut'
    })
    
    this.time.delayedCall(1500, () => {
      harvestParticles.destroy()
      rewardText.destroy()
    })
  }

  private onTileClick(row: number, col: number) {
    if (this.plantingManager.isInPlantingMode()) {
      this.plantingManager.handleGridClick(col, row)
    } else {
      // 创建点击效果
      const x = this.startX + col * this.tileSize + this.tileSize / 2
      const y = this.startY + row * this.tileSize + this.tileSize / 2
      
      const clickEffect = this.add.circle(x, y, 15, 0xFFD700, 0.8)
      
      this.tweens.add({
        targets: clickEffect,
        scaleX: 2,
        scaleY: 2,
        alpha: 0,
        duration: 400,
        onComplete: () => clickEffect.destroy()
      })
    }
  }

  private onTileHover(tile: Phaser.GameObjects.Image, isOver: boolean) {
    if (isOver) {
      tile.setTint(0xFFFFAA)
      this.tweens.add({
        targets: tile,
        scaleX: 1.05,
        scaleY: 1.05,
        duration: 200
      })
    } else {
      tile.clearTint()
      this.tweens.add({
        targets: tile,
        scaleX: 1,
        scaleY: 1,
        duration: 200
      })
    }
  }

  private showWelcomeMessage() {
    const messageBg = this.add.rectangle(400, 300, 450, 180, 0x000000, 0.8)
    
    const welcomeText = this.add.text(400, 300, '🌱 欢迎来到自律农场！\n\n✨ 种植系统已就绪\n🎯 点击"开始种植"开始种植\n🌾 成熟后点击作物收获', {
      fontSize: '16px',
      color: '#FFFFFF',
      align: 'center',
      fontFamily: 'Arial'
    }).setOrigin(0.5)

    const border = this.add.rectangle(400, 300, 460, 190, 0x90EE90, 0)
    border.setStrokeStyle(3, 0x90EE90)
    
    this.tweens.add({
      targets: border,
      alpha: 0.8,
      duration: 1000,
      yoyo: true,
      repeat: 2
    })

    messageBg.setScale(0)
    welcomeText.setScale(0)
    
    this.tweens.add({
      targets: [messageBg, welcomeText],
      scaleX: 1,
      scaleY: 1,
      duration: 500,
      ease: 'Back.easeOut'
    })

    this.time.delayedCall(4000, () => {
      this.tweens.add({
        targets: [messageBg, welcomeText, border],
        alpha: 0,
        scaleX: 0.8,
        scaleY: 0.8,
        duration: 500,
        onComplete: () => {
          messageBg.destroy()
          welcomeText.destroy()
          border.destroy()
        }
      })
    })
  }

  private createEnvironmentAnimations() {
    this.time.addEvent({
      delay: 20000,
      callback: () => {
        const overlay = this.add.rectangle(400, 300, 800, 600, 0xFFFFAA, 0.1)
        
        this.tweens.add({
          targets: overlay,
          alpha: 0,
          duration: 3000,
          onComplete: () => overlay.destroy()
        })
      },
      loop: true
    })
  }

  update() {
    // 更新作物精灵状态
    this.cropSprites.forEach(cropSprite => {
      cropSprite.update()
    })
  }
} 