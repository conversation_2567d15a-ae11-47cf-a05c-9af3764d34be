import { Scene } from 'phaser'
import { CropType, CropStage } from '../../types/crop'
import { GameStateManager } from '../../managers/GameStateManager'
import { EventManager, GameEventType } from '../../managers/EventManager'
import { StorageManager } from '../../managers/StorageManager'

/**
 * 状态管理测试场景
 * 用于测试GameStateManager、EventManager和StorageManager的功能
 */
export class StateTestScene extends Scene {
  private gameStateManager!: GameStateManager
  private eventManager!: EventManager
  private storageManager!: StorageManager
  
  // UI 元素
  private infoText!: Phaser.GameObjects.Text
  private statusText!: Phaser.GameObjects.Text
  private controlButtons: Phaser.GameObjects.Container[] = []
  
  constructor() {
    super({ key: 'StateTestScene' })
  }
  
  create(): void {
    console.log('🧪 启动状态管理测试场景')
    
    // 初始化管理器
    this.initializeManagers()
    
    // 创建UI
    this.createUI()
    
    // 设置事件监听
    this.setupEventListeners()
    
    // 设置键盘控制
    this.setupKeyboardControls()
    
    console.log('✅ 状态管理测试场景初始化完成')
  }
  
  /**
   * 初始化管理器
   */
  private initializeManagers(): void {
    this.gameStateManager = new GameStateManager()
    this.eventManager = new EventManager(true) // 启用调试模式
    this.storageManager = new StorageManager()
    
    // 连接事件系统
    this.gameStateManager.on('crop_planted', (data) => {
      this.eventManager.publishCropPlanted(data.crop, data.position)
    })
    
    this.gameStateManager.on('crop_harvested', (data) => {
      this.eventManager.publishCropHarvested(data.crop, data.rewards, data.position)
    })
    
    this.gameStateManager.on('level_up', (data) => {
      this.eventManager.publishLevelUp(data.oldLevel, data.newLevel, data.remainingExp)
    })
  }
  
  /**
   * 创建UI界面
   */
  private createUI(): void {
    const { width } = this.scale
    
    // 标题
    this.add.text(width / 2, 30, '状态管理系统测试', {
      fontSize: '28px',
      color: '#ffffff',
      fontStyle: 'bold'
    }).setOrigin(0.5)
    
    // 状态信息
    this.infoText = this.add.text(20, 80, '', {
      fontSize: '16px',
      color: '#ffffff',
      backgroundColor: '#000000',
      padding: { x: 10, y: 10 }
    })
    
    // 实时状态
    this.statusText = this.add.text(width - 20, 80, '', {
      fontSize: '14px',
      color: '#ffffff',
      backgroundColor: '#001122',
      padding: { x: 10, y: 10 }
    }).setOrigin(1, 0)
    
    // 控制按钮
    this.createControlButtons()
    
    // 更新信息显示
    this.updateInfoDisplay()
  }
  
  /**
   * 创建控制按钮
   */
  private createControlButtons(): void {
    const { width, height } = this.scale
    const buttonWidth = 200
    const buttonHeight = 40
    const spacing = 10
    const cols = 4
    const startX = width / 2 - (cols * (buttonWidth + spacing)) / 2 + buttonWidth / 2
    const startY = height - 200
    
    const buttons = [
      { text: '种植测试作物', action: () => this.testPlantCrops() },
      { text: '收获所有作物', action: () => this.testHarvestCrops() },
      { text: '添加经验值', action: () => this.testAddExperience() },
      { text: '保存游戏状态', action: () => this.testSaveGame() },
      
      { text: '加载游戏状态', action: () => this.testLoadGame() },
      { text: '测试事件系统', action: () => this.testEventSystem() },
      { text: '测试通知系统', action: () => this.testNotifications() },
      { text: '重置游戏', action: () => this.testResetGame() },
      
      { text: '显示统计信息', action: () => this.showStats() },
      { text: '存储使用情况', action: () => this.showStorageUsage() },
      { text: '清除所有数据', action: () => this.clearAllData() },
      { text: '返回主菜单', action: () => this.returnToMenu() }
    ]
    
    buttons.forEach((buttonData, index) => {
      const col = index % cols
      const row = Math.floor(index / cols)
      const x = startX + col * (buttonWidth + spacing)
      const y = startY + row * (buttonHeight + spacing)
      
      const button = this.createButton(x, y, buttonWidth, buttonHeight, buttonData.text, buttonData.action)
      this.controlButtons.push(button)
    })
  }
  
  /**
   * 创建按钮
   */
  private createButton(
    x: number, 
    y: number, 
    width: number, 
    height: number, 
    text: string, 
    callback: () => void
  ): Phaser.GameObjects.Container {
    const container = this.add.container(x, y)
    
    // 按钮背景
    const bg = this.add.rectangle(0, 0, width, height, 0x4CAF50)
    bg.setStrokeStyle(2, 0x45A049)
    bg.setInteractive()
    
    // 按钮文本
    const label = this.add.text(0, 0, text, {
      fontSize: '14px',
      color: '#ffffff',
      fontStyle: 'bold'
    }).setOrigin(0.5)
    
    container.add([bg, label])
    
    // 鼠标事件
    bg.on('pointerover', () => {
      bg.setFillStyle(0x66BB6A)
      this.input.setDefaultCursor('pointer')
    })
    
    bg.on('pointerout', () => {
      bg.setFillStyle(0x4CAF50)
      this.input.setDefaultCursor('default')
    })
    
    bg.on('pointerup', () => {
      bg.setFillStyle(0x66BB6A)
      callback()
    })
    
    return container
  }
  
  /**
   * 设置事件监听
   */
  private setupEventListeners(): void {
    // 监听事件管理器的事件
    this.eventManager.subscribeToEvent(GameEventType.CROP_PLANTED, (event) => {
      console.log('🌱 作物种植事件:', event.data)
      this.updateInfoDisplay()
    })
    
    this.eventManager.subscribeToEvent(GameEventType.CROP_HARVESTED, (event) => {
      console.log('🎯 作物收获事件:', event.data)
      this.updateInfoDisplay()
    })
    
    this.eventManager.subscribeToEvent(GameEventType.LEVEL_UP, (event) => {
      console.log('🎉 升级事件:', event.data)
      this.updateInfoDisplay()
    })
  }
  
  /**
   * 设置键盘控制
   */
  private setupKeyboardControls(): void {
    this.input.keyboard?.on('keydown-ONE', () => this.testPlantCrops())
    this.input.keyboard?.on('keydown-TWO', () => this.testHarvestCrops())
    this.input.keyboard?.on('keydown-THREE', () => this.testSaveGame())
    this.input.keyboard?.on('keydown-FOUR', () => this.testLoadGame())
    this.input.keyboard?.on('keydown-ESC', () => this.returnToMenu())
  }
  
  /**
   * 测试种植作物
   */
  private testPlantCrops(): void {
    console.log('🧪 测试种植作物')
    
    const crops = [
      { x: 0, y: 0, type: CropType.KNOWLEDGE_FLOWER },
      { x: 1, y: 0, type: CropType.STRENGTH_TREE },
      { x: 2, y: 0, type: CropType.TIME_VEGGIE },
      { x: 3, y: 0, type: CropType.MEDITATION_LOTUS }
    ]
    
    let planted = 0
    crops.forEach(crop => {
      const success = this.gameStateManager.plantCrop(crop.x, crop.y, crop.type)
      if (success) planted++
    })
    
    this.eventManager.showNotification({
      title: '种植测试完成',
      message: `成功种植 ${planted} 株作物`,
      type: 'success'
    })
    
    this.updateInfoDisplay()
  }
  
  /**
   * 测试收获作物
   */
  private testHarvestCrops(): void {
    console.log('🧪 测试收获作物')
    
    const gameState = this.gameStateManager.getGameState()
    let harvested = 0
    
    // 遍历farmGrid查找可收获的作物
    for (let y = 0; y < gameState.farmGrid.height; y++) {
      for (let x = 0; x < gameState.farmGrid.width; x++) {
        const crop = gameState.farmGrid.plots[y][x]
        if (crop && crop.stage === CropStage.READY_TO_HARVEST) {
          const result = this.gameStateManager.harvestCrop(x, y)
          if (result.success) harvested++
        }
      }
    }
    
    this.eventManager.showNotification({
      title: '收获测试完成',
      message: `成功收获 ${harvested} 株作物`,
      type: 'success'
    })
    
    this.updateInfoDisplay()
  }
  
  /**
   * 测试添加经验值
   */
  private testAddExperience(): void {
    console.log('🧪 测试添加经验值')
    
    const gameState = this.gameStateManager.getGameState()
    
    // 直接修改experience值来触发升级检查
    // 这是一个简化的测试方法
    
    this.eventManager.showNotification({
      title: '经验值测试',
      message: `当前等级: ${gameState.level}, 经验: ${gameState.experience}`,
      type: 'info'
    })
    
    this.updateInfoDisplay()
  }
  
  /**
   * 测试保存游戏
   */
  private async testSaveGame(): Promise<void> {
    console.log('🧪 测试保存游戏')
    
    const success = await this.gameStateManager.saveGameState()
    
    this.eventManager.showNotification({
      title: '保存测试',
      message: success ? '游戏保存成功' : '游戏保存失败',
      type: success ? 'success' : 'error'
    })
  }
  
  /**
   * 测试加载游戏
   */
  private async testLoadGame(): Promise<void> {
    console.log('🧪 测试加载游戏')
    
    const success = await this.gameStateManager.loadGameState()
    
    this.eventManager.showNotification({
      title: '加载测试',
      message: success ? '游戏加载成功' : '游戏加载失败',
      type: success ? 'success' : 'error'
    })
    
    if (success) {
      this.updateInfoDisplay()
    }
  }
  
  /**
   * 测试事件系统
   */
  private testEventSystem(): void {
    console.log('🧪 测试事件系统')
    
    // 发布测试事件
    this.eventManager.publishEvent(GameEventType.GAME_SAVED, {
      message: '这是一个测试事件',
      timestamp: Date.now()
    })
    
    this.eventManager.showNotification({
      title: '事件系统测试',
      message: '已发布测试事件，请查看控制台',
      type: 'info'
    })
  }
  
  /**
   * 测试通知系统
   */
  private testNotifications(): void {
    console.log('🧪 测试通知系统')
    
    const notifications = [
      { title: '信息通知', message: '这是一个信息通知', type: 'info' as const },
      { title: '成功通知', message: '操作成功完成', type: 'success' as const },
      { title: '警告通知', message: '这是一个警告', type: 'warning' as const },
      { title: '错误通知', message: '发生了错误', type: 'error' as const }
    ]
    
    notifications.forEach((notification, index) => {
      setTimeout(() => {
        this.eventManager.showNotification(notification)
      }, index * 1000)
    })
  }
  
  /**
   * 测试重置游戏
   */
  private testResetGame(): void {
    console.log('🧪 测试重置游戏')
    
    this.gameStateManager.resetGameState()
    
    this.eventManager.showNotification({
      title: '重置完成',
      message: '游戏状态已重置',
      type: 'success'
    })
    
    this.updateInfoDisplay()
  }
  
  /**
   * 显示统计信息
   */
  private showStats(): void {
    console.log('📊 显示统计信息')
    
    const stats = this.gameStateManager.getGameStats()
    
    console.table(stats)
    
    this.eventManager.showNotification({
      title: '统计信息',
      message: `等级: ${stats.level}, 作物: ${stats.cropsCount}, 总资源: ${stats.resourcesTotal}`,
      type: 'info'
    })
  }
  
  /**
   * 显示存储使用情况
   */
  private showStorageUsage(): void {
    console.log('💾 显示存储使用情况')
    
    try {
      const usage = this.storageManager.getStorageUsage()
      console.log('存储使用情况:', usage)
      
      this.eventManager.showNotification({
        title: '存储使用情况',
        message: `已使用 ${Math.round(usage.percentage)}% 的本地存储`,
        type: 'info'
      })
    } catch (error) {
      console.error('获取存储使用情况失败:', error)
    }
  }
  
  /**
   * 清除所有数据
   */
  private clearAllData(): void {
    console.log('🗑️ 清除所有数据')
    
    try {
      this.storageManager.clearAllData()
      this.gameStateManager.resetGameState()
      
      this.eventManager.showNotification({
        title: '数据清除完成',
        message: '所有数据已清除',
        type: 'success'
      })
      
      this.updateInfoDisplay()
    } catch (error) {
      console.error('清除数据失败:', error)
      this.eventManager.showNotification({
        title: '清除失败',
        message: '数据清除过程中发生错误',
        type: 'error'
      })
    }
  }
  
  /**
   * 返回主菜单
   */
  private returnToMenu(): void {
    console.log('🔙 返回主菜单')
    
    // 清理资源
    this.gameStateManager.destroy()
    this.eventManager.destroy()
    this.storageManager.destroy()
    
    // 切换到主菜单场景
    this.scene.start('MainMenu')
  }
  
  /**
   * 更新信息显示
   */
  private updateInfoDisplay(): void {
    const gameState = this.gameStateManager.getGameState()
    const stats = this.gameStateManager.getGameStats()
    
    const info = [
      `玩家: ${gameState.playerName || '未设置'}`,
      `等级: ${gameState.level} (经验: ${gameState.experience}/${stats.nextLevelExp})`,
      `总专注时间: ${Math.round(gameState.totalFocusTime)} 分钟`,
      `收获总数: ${gameState.totalCropsHarvested}`,
      ``,
      `资源:`,
      `  知识: ${gameState.resources.knowledge}`,
      `  力量: ${gameState.resources.strength}`,
      `  时间: ${gameState.resources.time}`,
      `  冥想: ${gameState.resources.meditation}`,
      ``,
      `作物数量: ${stats.cropsCount}`,
      `农场规格: ${gameState.gridSize.width}x${gameState.gridSize.height}`
    ].join('\n')
    
    this.infoText.setText(info)
  }
  
  /**
   * 更新循环
   */
  update(): void {
    // 更新实时状态显示
    const now = Date.now()
    const fps = Math.round(this.game.loop.actualFps)
    const objectCount = this.children.list.length
    
    this.statusText.setText([
      `FPS: ${fps}`,
      `对象数: ${objectCount}`,
      `时间: ${new Date(now).toLocaleTimeString()}`
    ].join('\n'))
  }
  
  /**
   * 场景销毁
   */
  destroy(): void {
    // 清理管理器
    this.gameStateManager?.destroy()
    this.eventManager?.destroy()
    this.storageManager?.destroy()
    
    console.log('🧹 StateTestScene 已销毁')
  }
} 