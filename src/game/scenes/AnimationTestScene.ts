import Phaser from 'phaser'
import { CropSprite } from '../objects/CropSprite'
import { AnimationManager, AnimationType, AnimationPresets } from '../managers/AnimationManager'
import { 
  CropInstance, 
  CropStage, 
  CropType, 
  CropQuality,
  CROP_CONFIGS 
} from '../../types/crop'

/**
 * 动画测试场景
 * 用于测试和演示所有作物生长动画效果
 */
export class AnimationTestScene extends Phaser.Scene {
  private animationManager!: AnimationManager
  private testCrops: CropSprite[] = []
  private currentTestIndex: number = 0
  private instructionText!: Phaser.GameObjects.Text
  private debugInfo!: Phaser.GameObjects.Text
  
  constructor() {
    super({ key: 'AnimationTestScene' })
  }
  
  create(): void {
    console.log('🎬 动画测试场景启动')
    
    // 初始化动画管理器
    this.animationManager = new AnimationManager(this)
    
    // 创建背景
    this.createBackground()
    
    // 创建测试作物
    this.createTestCrops()
    
    // 创建UI
    this.createUI()
    
    // 设置输入处理
    this.setupInput()
    
    // 开始自动演示
    this.startAutoDemo()
  }
  
  /**
   * 创建背景
   */
  private createBackground(): void {
    // 渐变背景
    const graphics = this.add.graphics()
    graphics.fillGradientStyle(0x87CEEB, 0x87CEEB, 0x90EE90, 0x90EE90, 1)
    graphics.fillRect(0, 0, this.scale.width, this.scale.height)
    
    // 网格线
    graphics.lineStyle(1, 0xFFFFFF, 0.1)
    const gridSize = 50
    
    for (let x = 0; x < this.scale.width; x += gridSize) {
      graphics.moveTo(x, 0)
      graphics.lineTo(x, this.scale.height)
    }
    
    for (let y = 0; y < this.scale.height; y += gridSize) {
      graphics.moveTo(0, y)
      graphics.lineTo(this.scale.width, y)
    }
    
    graphics.strokePath()
  }
  
  /**
   * 创建测试作物
   */
  private createTestCrops(): void {
    const cropTypes = [
      CropType.KNOWLEDGE_FLOWER,
      CropType.STRENGTH_TREE,
      CropType.TIME_VEGGIE,
      CropType.MEDITATION_LOTUS
    ]
    
    const stages = [
      CropStage.SEED,
      CropStage.SPROUT,
      CropStage.GROWING,
      CropStage.MATURE,
      CropStage.READY_TO_HARVEST
    ]
    
    const qualities = [
      CropQuality.COMMON,
      CropQuality.UNCOMMON,
      CropQuality.RARE,
      CropQuality.EPIC,
      CropQuality.LEGENDARY
    ]
    
    const startX = 150
    const startY = 150
    const spacing = 120
    
    cropTypes.forEach((type, typeIndex) => {
      stages.forEach((stage, stageIndex) => {
        const x = startX + stageIndex * spacing
        const y = startY + typeIndex * spacing
        
                 // 创建测试作物实例
         const now = Date.now()
         const cropData: CropInstance = {
           id: `test-${typeIndex}-${stageIndex}`,
           type,
           stage,
           quality: qualities[stageIndex] || CropQuality.COMMON,
           plantedAt: now,
           harvestable: stage === CropStage.READY_TO_HARVEST,
           position: { x: stageIndex, y: typeIndex, gridX: stageIndex, gridY: typeIndex },
           stageStartTime: now,
           totalGrowthTime: 0,
           focusTimeContributed: 0,
           averageFocusScore: 1.0,
           qualityBonusesApplied: [],
           lastUpdateTime: now,
           isPaused: false
         }
        
        // 创建作物精灵
        const sprite = new CropSprite(this, x, y, cropData)
        this.testCrops.push(sprite)
        
        // 设置事件监听
        sprite.on('harvest', this.onCropHarvest, this)
        sprite.on('inspect', this.onCropInspect, this)
      })
    })
    
    // 添加标签
    this.addLabels(startX, startY, spacing)
  }
  
  /**
   * 添加标签
   */
  private addLabels(startX: number, startY: number, spacing: number): void {
    // 阶段标签
    const stageLabels = ['种子', '发芽', '生长', '成熟', '收获']
    stageLabels.forEach((label, index) => {
      this.add.text(startX + index * spacing, startY - 50, label, {
        fontSize: '14px',
        color: '#333333',
        fontStyle: 'bold'
      }).setOrigin(0.5)
    })
    
    // 作物类型标签
    const typeLabels = ['知识花', '力量树', '时间菜', '冥想莲']
    typeLabels.forEach((label, index) => {
      this.add.text(startX - 80, startY + index * spacing, label, {
        fontSize: '14px',
        color: '#333333',
        fontStyle: 'bold'
      }).setOrigin(0.5, 0.5)
    })
  }
  
  /**
   * 创建UI
   */
  private createUI(): void {
    // 标题
    this.add.text(this.scale.width / 2, 30, '🎬 作物生长动画测试场景', {
      fontSize: '24px',
      color: '#333333',
      fontStyle: 'bold'
    }).setOrigin(0.5)
    
    // 说明文字
    this.instructionText = this.add.text(this.scale.width / 2, 70, '', {
      fontSize: '16px',
      color: '#666666',
      align: 'center'
    }).setOrigin(0.5)
    
    // 调试信息
    this.debugInfo = this.add.text(20, this.scale.height - 150, '', {
      fontSize: '12px',
      color: '#333333',
      backgroundColor: 'rgba(255, 255, 255, 0.8)',
      padding: { x: 10, y: 5 }
    })
    
    // 控制按钮
    this.createControlButtons()
  }
  
  /**
   * 创建控制按钮
   */
  private createControlButtons(): void {
    const buttonY = this.scale.height - 60
    const buttonSpacing = 140
    let buttonX = 50
    
    // 播放种植动画
    this.createButton(buttonX, buttonY, '种植动画', () => {
      this.playPlantingDemo()
    })
    buttonX += buttonSpacing
    
    // 播放生长动画
    this.createButton(buttonX, buttonY, '生长动画', () => {
      this.playGrowthDemo()
    })
    buttonX += buttonSpacing
    
    // 播放品质升级
    this.createButton(buttonX, buttonY, '品质升级', () => {
      this.playQualityDemo()
    })
    buttonX += buttonSpacing
    
    // 播放收获动画
    this.createButton(buttonX, buttonY, '收获动画', () => {
      this.playHarvestDemo()
    })
    buttonX += buttonSpacing
    
    // 播放生长加速
    this.createButton(buttonX, buttonY, '生长加速', () => {
      this.playBoostDemo()
    })
    buttonX += buttonSpacing
    
    // 重置场景
    this.createButton(buttonX, buttonY, '重置', () => {
      this.resetScene()
    })
  }
  
  /**
   * 创建按钮
   */
  private createButton(x: number, y: number, text: string, callback: () => void): void {
    const button = this.add.graphics()
    button.fillStyle(0x4CAF50)
    button.fillRoundedRect(0, 0, 120, 35, 8)
    button.setPosition(x, y)
    button.setInteractive(new Phaser.Geom.Rectangle(0, 0, 120, 35), Phaser.Geom.Rectangle.Contains)
    
    const buttonText = this.add.text(x + 60, y + 17, text, {
      fontSize: '14px',
      color: '#FFFFFF',
      fontStyle: 'bold'
    }).setOrigin(0.5)
    
    button.on('pointerover', () => {
      button.clear()
      button.fillStyle(0x45A049)
      button.fillRoundedRect(0, 0, 120, 35, 8)
    })
    
    button.on('pointerout', () => {
      button.clear()
      button.fillStyle(0x4CAF50)
      button.fillRoundedRect(0, 0, 120, 35, 8)
    })
    
    button.on('pointerdown', callback)
  }
  
  /**
   * 设置输入处理
   */
  private setupInput(): void {
    // 键盘快捷键
    this.input.keyboard?.on('keydown-SPACE', () => {
      this.playRandomAnimation()
    })
    
    this.input.keyboard?.on('keydown-R', () => {
      this.resetScene()
    })
    
    this.input.keyboard?.on('keydown-ESC', () => {
      this.scene.start('MainGameScene')
    })
  }
  
  /**
   * 开始自动演示
   */
  private startAutoDemo(): void {
    this.instructionText.setText(
      '🎮 操作说明：\n' +
      '• 点击作物查看详情或收获\n' +
      '• 空格键：随机播放动画\n' +
      '• R键：重置场景\n' +
      '• ESC键：返回主游戏\n' +
      '• 点击下方按钮测试特定动画'
    )
    
    // 延迟开始自动演示
    this.time.delayedCall(2000, () => {
      this.playSequentialDemo()
    })
  }
  
  /**
   * 顺序演示所有动画
   */
  private async playSequentialDemo(): Promise<void> {
    console.log('🎭 开始顺序动画演示')
    
    // 1. 种植动画演示
    await this.playPlantingDemo()
    await this.wait(1000)
    
    // 2. 生长动画演示  
    await this.playGrowthDemo()
    await this.wait(1000)
    
    // 3. 品质升级演示
    await this.playQualityDemo()
    await this.wait(1000)
    
    // 4. 收获动画演示
    await this.playHarvestDemo()
    await this.wait(1000)
    
    // 5. 生长加速演示
    await this.playBoostDemo()
    
    console.log('✅ 动画演示完成')
  }
  
  /**
   * 播放种植动画演示
   */
  private async playPlantingDemo(): Promise<void> {
    console.log('🌱 演示种植动画')
    this.instructionText.setText('🌱 正在演示种植动画...')
    
    // 选择种子阶段的作物
    const seedCrops = this.testCrops.filter(crop => 
      crop.cropData.stage === CropStage.SEED
    )
    
    for (const crop of seedCrops) {
      await this.animationManager.playPlantingSequence(crop)
      await this.wait(200)
    }
  }
  
  /**
   * 播放生长动画演示
   */
  private async playGrowthDemo(): Promise<void> {
    console.log('🌿 演示生长动画')
    this.instructionText.setText('🌿 正在演示生长阶段转换动画...')
    
    // 随机选择一些作物进行生长动画
    const crops = this.getRandomCrops(6)
    
    for (const crop of crops) {
      const currentStage = crop.cropData.stage
      const nextStage = this.getNextStage(currentStage)
      
      if (nextStage) {
        await this.animationManager.playStageTransitionSequence(
          crop,
          currentStage,
          nextStage
        )
        crop.cropData.stage = nextStage
        crop.updateCropData(crop.cropData)
        await this.wait(300)
      }
    }
  }
  
  /**
   * 播放品质升级演示
   */
  private async playQualityDemo(): Promise<void> {
    console.log('⭐ 演示品质升级动画')
    this.instructionText.setText('⭐ 正在演示品质升级动画...')
    
    const crops = this.getRandomCrops(4)
    
    for (const crop of crops) {
      const currentQuality = crop.cropData.quality
      const nextQuality = this.getNextQuality(currentQuality)
      
      if (nextQuality) {
        crop.cropData.quality = nextQuality
        crop.updateCropData(crop.cropData)
        await this.animationManager.playQualityUpgradeSequence(crop)
        await this.wait(400)
      }
    }
  }
  
  /**
   * 播放收获动画演示
   */
  private async playHarvestDemo(): Promise<void> {
    console.log('🎯 演示收获动画')
    this.instructionText.setText('🎯 正在演示收获就绪动画...')
    
    // 选择成熟的作物
    const matureCrops = this.testCrops.filter(crop => 
      crop.cropData.stage === CropStage.MATURE ||
      crop.cropData.stage === CropStage.READY_TO_HARVEST
    )
    
    for (const crop of matureCrops.slice(0, 3)) {
      crop.cropData.stage = CropStage.READY_TO_HARVEST
      crop.cropData.harvestable = true
      crop.updateCropData(crop.cropData)
      await this.animationManager.playAnimation(crop, AnimationType.HARVEST_READY)
      await this.wait(300)
    }
  }
  
  /**
   * 播放生长加速演示
   */
  private async playBoostDemo(): Promise<void> {
    console.log('⚡ 演示生长加速动画')
    this.instructionText.setText('⚡ 正在演示生长加速效果...')
    
    const crops = this.getRandomCrops(8)
    
    for (const crop of crops) {
      this.animationManager.playGrowthBoost(crop)
      await this.wait(100)
    }
    
    await this.wait(1000)
  }
  
  /**
   * 播放随机动画
   */
  private playRandomAnimation(): void {
    const crop = this.getRandomCrops(1)[0]
    if (!crop) return
    
    const animations = [
      AnimationType.STAGE_TRANSITION,
      AnimationType.QUALITY_UPGRADE,
      AnimationType.GROWTH_BOOST,
      AnimationType.HARVEST_READY
    ]
    
    const randomAnimation = animations[Math.floor(Math.random() * animations.length)]
    this.animationManager.playAnimation(crop, randomAnimation)
    
    console.log(`🎲 随机播放 ${randomAnimation} 动画`)
  }
  
  /**
   * 重置场景
   */
  private resetScene(): void {
    console.log('🔄 重置动画测试场景')
    
    // 停止所有动画
    this.animationManager.destroy()
    
    // 重新启动场景
    this.scene.restart()
  }
  
  /**
   * 作物收获事件
   */
  private onCropHarvest(cropData: CropInstance): void {
    console.log(`🎯 收获作物: ${cropData.id}`)
    this.instructionText.setText(`🎯 收获了 ${this.getCropTypeName(cropData.type)}！`)
  }
  
  /**
   * 作物检查事件
   */
  private onCropInspect(cropData: CropInstance): void {
    console.log(`🔍 检查作物: ${cropData.id}`)
    const info = `🔍 ${this.getCropTypeName(cropData.type)} - ${this.getStageName(cropData.stage)} - ${this.getQualityName(cropData.quality)}`
    this.instructionText.setText(info)
  }
  
  /**
   * 获取随机作物
   */
  private getRandomCrops(count: number): CropSprite[] {
    const shuffled = [...this.testCrops].sort(() => 0.5 - Math.random())
    return shuffled.slice(0, count)
  }
  
  /**
   * 获取下一个生长阶段
   */
  private getNextStage(currentStage: CropStage): CropStage | null {
    const stages = [
      CropStage.SEED,
      CropStage.SPROUT,
      CropStage.GROWING,
      CropStage.MATURE,
      CropStage.READY_TO_HARVEST
    ]
    
    const currentIndex = stages.indexOf(currentStage)
    return currentIndex < stages.length - 1 ? stages[currentIndex + 1] : null
  }
  
  /**
   * 获取下一个品质等级
   */
  private getNextQuality(currentQuality: CropQuality): CropQuality | null {
    const qualities = [
      CropQuality.COMMON,
      CropQuality.UNCOMMON,
      CropQuality.RARE,
      CropQuality.EPIC,
      CropQuality.LEGENDARY
    ]
    
    const currentIndex = qualities.indexOf(currentQuality)
    return currentIndex < qualities.length - 1 ? qualities[currentIndex + 1] : null
  }
  
  /**
   * 获取作物类型名称
   */
  private getCropTypeName(type: CropType): string {
    const names = {
      [CropType.KNOWLEDGE_FLOWER]: '知识花',
      [CropType.STRENGTH_TREE]: '力量树',
      [CropType.TIME_VEGGIE]: '时间菜',
      [CropType.MEDITATION_LOTUS]: '冥想莲'
    }
    return names[type] || '未知作物'
  }
  
  /**
   * 获取阶段名称
   */
  private getStageName(stage: CropStage): string {
    const names = {
      [CropStage.SEED]: '种子',
      [CropStage.SPROUT]: '发芽',
      [CropStage.GROWING]: '生长',
      [CropStage.MATURE]: '成熟',
      [CropStage.READY_TO_HARVEST]: '收获',
      [CropStage.HARVESTED]: '已收获'
    }
    return names[stage] || '未知阶段'
  }
  
  /**
   * 获取品质名称
   */
  private getQualityName(quality: CropQuality): string {
    const names = {
      [CropQuality.COMMON]: '普通',
      [CropQuality.UNCOMMON]: '优良',
      [CropQuality.RARE]: '稀有',
      [CropQuality.EPIC]: '史诗',
      [CropQuality.LEGENDARY]: '传说'
    }
    return names[quality] || '未知品质'
  }
  
  /**
   * 等待指定时间
   */
  private wait(ms: number): Promise<void> {
    return new Promise(resolve => {
      this.time.delayedCall(ms, resolve)
    })
  }
  
  /**
   * 更新调试信息
   */
  update(): void {
    if (this.debugInfo) {
      const debugData = this.animationManager.getDebugInfo()
      const fps = this.game.loop.actualFps.toFixed(1)
      
      this.debugInfo.setText(
        `🔧 调试信息:\n` +
        `FPS: ${fps}\n` +
        `活跃动画: ${debugData.activeTweensCount}\n` +
        `粒子池: ${debugData.particlePoolSize}\n` +
        `内存使用:\n` +
        `  动画: ~${debugData.memoryUsage.tweenMemory}KB\n` +
        `  粒子: ~${debugData.memoryUsage.particleMemory}KB`
      )
    }
  }
  
  /**
   * 场景销毁
   */
  destroy(): void {
    // 清理动画管理器
    if (this.animationManager) {
      this.animationManager.destroy()
    }
    
    // 清理作物精灵
    this.testCrops.forEach(crop => crop.destroy())
    this.testCrops.length = 0
    
    super.destroy()
  }
} 