import Phaser from 'phaser'
import { CropType, CropInstance } from '../../types/crop'
import { GameStateManager } from '../../managers/GameStateManager'
import { PlantingUI } from '../objects/PlantingUI'

export enum PlantingMode {
  NONE = 'none',
  SELECTING_SEED = 'selecting_seed',
  PLACING_CROP = 'placing_crop'
}

export interface PlantingResult {
  success: boolean
  message: string
  crop?: CropInstance
  position?: { gridX: number; gridY: number }
}

export class PlantingManager {
  private scene: Phaser.Scene
  private gameStateManager: GameStateManager
  private plantingUI: PlantingUI
  private currentMode: PlantingMode = PlantingMode.NONE
  private selectedCropType: CropType | null = null
  private gridHighlights: Phaser.GameObjects.Rectangle[] = []
  
  // 配置
  private readonly gridSize = { width: 8, height: 6 }
  private readonly tileSize = 80
  private readonly gridStart = { x: 200, y: 100 }
  
  // 事件回调
  private onPlantSuccessCallback?: (result: PlantingResult) => void
  private onPlantFailureCallback?: (result: PlantingResult) => void
  private onModeChangeCallback?: (mode: PlantingMode) => void

  constructor(scene: Phaser.Scene, gameStateManager: GameStateManager) {
    this.scene = scene
    this.gameStateManager = gameStateManager
    
    // 创建种植UI
    this.plantingUI = new PlantingUI(scene, {
      x: 600,
      y: 300,
      visible: false,
      availableCrops: this.getAvailableCrops()
    })
    
    // 设置UI事件监听
    this.setupUIEventListeners()
  }

  private setupUIEventListeners(): void {
    this.plantingUI.onSeedSelected((cropType: CropType) => {
      this.onSeedSelected(cropType)
    })
    
    this.plantingUI.onClosed(() => {
      this.exitPlantingMode()
    })
  }

  /**
   * 开始种植模式
   */
  public startPlanting(): void {
    if (this.currentMode !== PlantingMode.NONE) {
      console.warn('已在种植模式中')
      return
    }
    
    const availableCrops = this.getAvailableCrops()
    
    if (availableCrops.length === 0) {
      this.showMessage('暂无可种植的作物', 'warning')
      return
    }
    
    this.setMode(PlantingMode.SELECTING_SEED)
    this.plantingUI.showUI(availableCrops)
  }

  /**
   * 退出种植模式
   */
  public exitPlantingMode(): void {
    this.clearGridHighlights()
    this.plantingUI.hideUI()
    this.selectedCropType = null
    this.setMode(PlantingMode.NONE)
  }

  /**
   * 处理网格点击
   */
  public handleGridClick(gridX: number, gridY: number): PlantingResult {
    if (this.currentMode !== PlantingMode.PLACING_CROP || !this.selectedCropType) {
      return {
        success: false,
        message: '请先选择种子'
      }
    }
    
    // 验证位置
    const validation = this.validatePlantPosition(gridX, gridY)
    if (!validation.valid) {
      const result: PlantingResult = {
        success: false,
        message: validation.message
      }
      
      if (this.onPlantFailureCallback) {
        this.onPlantFailureCallback(result)
      }
      
      return result
    }
    
    // 执行种植
    const plantSuccess = this.gameStateManager.plantCrop(gridX, gridY, this.selectedCropType)
    
    if (plantSuccess) {
      // 获取新种植的作物
      const gameState = this.gameStateManager.getGameState()
      const crop = gameState.farmGrid.plots[gridY][gridX]
      
      const result: PlantingResult = {
        success: true,
        message: `成功种植${this.getCropName(this.selectedCropType)}！`,
        crop: crop || undefined,
        position: { gridX, gridY }
      }
      
      // 显示种植动画
      this.playPlantingAnimation(gridX, gridY)
      
      // 退出种植模式
      this.exitPlantingMode()
      
      if (this.onPlantSuccessCallback) {
        this.onPlantSuccessCallback(result)
      }
      
      return result
    } else {
      const result: PlantingResult = {
        success: false,
        message: '种植失败，位置可能被占用'
      }
      
      if (this.onPlantFailureCallback) {
        this.onPlantFailureCallback(result)
      }
      
      return result
    }
  }

  /**
   * 种子选择回调
   */
  private onSeedSelected(cropType: CropType): void {
    this.selectedCropType = cropType
    this.setMode(PlantingMode.PLACING_CROP)
    this.plantingUI.hideUI()
    this.showPlantableAreas()
    this.showMessage(`选择位置来种植${this.getCropName(cropType)}`, 'info')
  }

  /**
   * 显示可种植区域
   */
  private showPlantableAreas(): void {
    this.clearGridHighlights()
    
    const gameState = this.gameStateManager.getGameState()
    
    for (let y = 0; y < this.gridSize.height; y++) {
      for (let x = 0; x < this.gridSize.width; x++) {
        const isOccupied = gameState.farmGrid.plots[y][x] !== null
        
        const posX = this.gridStart.x + x * this.tileSize + this.tileSize / 2
        const posY = this.gridStart.y + y * this.tileSize + this.tileSize / 2
        
        let highlight: Phaser.GameObjects.Rectangle
        
        if (isOccupied) {
          // 被占用的位置显示红色
          highlight = this.scene.add.rectangle(posX, posY, this.tileSize - 4, this.tileSize - 4, 0xFF4444, 0.3)
          highlight.setStrokeStyle(2, 0xFF6666, 0.8)
        } else {
          // 可种植的位置显示绿色
          highlight = this.scene.add.rectangle(posX, posY, this.tileSize - 4, this.tileSize - 4, 0x44FF44, 0.3)
          highlight.setStrokeStyle(2, 0x66FF66, 0.8)
          
          // 添加脉冲动画
          this.scene.tweens.add({
            targets: highlight,
            alpha: 0.1,
            duration: 1000,
            yoyo: true,
            repeat: -1,
            ease: 'Sine.easeInOut'
          })
        }
        
        this.gridHighlights.push(highlight)
      }
    }
  }

  /**
   * 清除网格高亮
   */
  private clearGridHighlights(): void {
    this.gridHighlights.forEach(highlight => {
      this.scene.tweens.killTweensOf(highlight)
      highlight.destroy()
    })
    this.gridHighlights = []
  }

  /**
   * 验证种植位置
   */
  private validatePlantPosition(gridX: number, gridY: number): { valid: boolean; message: string } {
    // 检查边界
    if (gridX < 0 || gridX >= this.gridSize.width || gridY < 0 || gridY >= this.gridSize.height) {
      return { valid: false, message: '位置超出农场边界' }
    }
    
    // 检查是否被占用
    const gameState = this.gameStateManager.getGameState()
    if (gameState.farmGrid.plots[gridY][gridX] !== null) {
      return { valid: false, message: '该位置已被占用' }
    }
    
    return { valid: true, message: '位置有效' }
  }

  /**
   * 播放种植动画
   */
  private playPlantingAnimation(gridX: number, gridY: number): void {
    const posX = this.gridStart.x + gridX * this.tileSize + this.tileSize / 2
    const posY = this.gridStart.y + gridY * this.tileSize + this.tileSize / 2
    
    // 创建种植效果粒子
    const particles = this.scene.add.particles(posX, posY, 'sparkle', {
      speed: { min: 30, max: 60 },
      scale: { start: 0.4, end: 0 },
      alpha: { start: 1, end: 0 },
      lifespan: 1000,
      quantity: 8
    })
    
    // 创建光环效果
    const ring = this.scene.add.circle(posX, posY, 40, 0x90EE90, 0)
    ring.setStrokeStyle(3, 0x90EE90, 0.8)
    
    this.scene.tweens.add({
      targets: ring,
      scaleX: 2,
      scaleY: 2,
      alpha: 0,
      duration: 800,
      ease: 'Power2.easeOut'
    })
    
    // 清理效果
    this.scene.time.delayedCall(1200, () => {
      particles.destroy()
      ring.destroy()
    })
  }

  /**
   * 显示消息
   */
  private showMessage(text: string, type: 'info' | 'warning' | 'error' = 'info'): void {
    const colors = {
      info: 0x4A90E2,
      warning: 0xF5A623,
      error: 0xD0021B
    }
    
    const message = this.scene.add.container(400, 50)
    
    const bg = this.scene.add.rectangle(0, 0, text.length * 8 + 40, 40, colors[type], 0.9)
    bg.setStrokeStyle(2, 0xFFFFFF, 0.8)
    message.add(bg)
    
    const textObj = this.scene.add.text(0, 0, text, {
      fontSize: '14px',
      color: '#FFFFFF',
      fontFamily: 'Arial'
    }).setOrigin(0.5)
    message.add(textObj)
    
    // 显示动画
    message.setScale(0.8)
    message.setAlpha(0)
    
    this.scene.tweens.add({
      targets: message,
      scaleX: 1,
      scaleY: 1,
      alpha: 1,
      duration: 200,
      ease: 'Back.easeOut'
    })
    
    // 自动隐藏
    this.scene.time.delayedCall(3000, () => {
      this.scene.tweens.add({
        targets: message,
        alpha: 0,
        y: message.y - 20,
        duration: 300,
        onComplete: () => message.destroy()
      })
    })
  }

  /**
   * 获取可用作物类型
   */
  private getAvailableCrops(): CropType[] {
    // 这里可以根据玩家等级、解锁状态等条件筛选
    const gameState = this.gameStateManager.getGameState()
    
    const baseCrops = [CropType.KNOWLEDGE_FLOWER]
    
    // 根据等级解锁更多作物
    if (gameState.level >= 3) {
      baseCrops.push(CropType.STRENGTH_TREE)
    }
    
    if (gameState.level >= 5) {
      baseCrops.push(CropType.TIME_VEGGIE)
    }
    
    if (gameState.level >= 8) {
      baseCrops.push(CropType.MEDITATION_LOTUS)
    }
    
    return baseCrops
  }

  /**
   * 获取作物名称
   */
  private getCropName(cropType: CropType): string {
    const names = {
      [CropType.KNOWLEDGE_FLOWER]: '知识花',
      [CropType.STRENGTH_TREE]: '力量树',
      [CropType.TIME_VEGGIE]: '时间蔬菜',
      [CropType.MEDITATION_LOTUS]: '冥想莲花'
    }
    return names[cropType] || '未知作物'
  }

  /**
   * 设置种植模式
   */
  private setMode(mode: PlantingMode): void {
    this.currentMode = mode
    if (this.onModeChangeCallback) {
      this.onModeChangeCallback(mode)
    }
  }

  /**
   * 获取当前模式
   */
  public getCurrentMode(): PlantingMode {
    return this.currentMode
  }

  /**
   * 检查是否在种植模式
   */
  public isInPlantingMode(): boolean {
    return this.currentMode !== PlantingMode.NONE
  }

  /**
   * 事件监听器设置
   */
  public onPlantSuccess(callback: (result: PlantingResult) => void): void {
    this.onPlantSuccessCallback = callback
  }

  public onPlantFailure(callback: (result: PlantingResult) => void): void {
    this.onPlantFailureCallback = callback
  }

  public onModeChange(callback: (mode: PlantingMode) => void): void {
    this.onModeChangeCallback = callback
  }

  /**
   * 销毁管理器
   */
  public destroy(): void {
    this.clearGridHighlights()
    this.plantingUI.destroy()
  }
} 