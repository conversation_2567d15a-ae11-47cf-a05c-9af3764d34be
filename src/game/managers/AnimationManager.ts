import Phaser from 'phaser'
import { CropSprite } from '../objects/CropSprite'
import { CropStage, CropType, CropQuality } from '../../types/crop'

// 动画类型
export enum AnimationType {
  STAGE_TRANSITION = 'stage_transition',
  QUALITY_UPGRADE = 'quality_upgrade', 
  HARVEST_READY = 'harvest_ready',
  PLANTING = 'planting',
  GROWTH_BOOST = 'growth_boost',
  IDLE_SWAY = 'idle_sway'
}

// 动画配置
export interface AnimationConfig {
  duration: number
  ease?: string
  scale?: { start: number; end: number }
  rotation?: { start: number; end: number }
  alpha?: { start: number; end: number }
  particles?: {
    count: number
    color: number
    spread: number
  }
}

// 预设动画配置
export const ANIMATION_PRESETS: Record<AnimationType, AnimationConfig> = {
  [AnimationType.STAGE_TRANSITION]: {
    duration: 800,
    ease: 'Back.easeOut',
    scale: { start: 1, end: 1.15 },
    particles: { count: 8, color: 0x90EE90, spread: 25 }
  },
  
  [AnimationType.QUALITY_UPGRADE]: {
    duration: 600,
    ease: 'Bounce.easeOut',
    scale: { start: 1, end: 1.2 },
    alpha: { start: 1, end: 0.3 },
    particles: { count: 12, color: 0x9370DB, spread: 30 }
  },
  
  [AnimationType.HARVEST_READY]: {
    duration: 1500,
    ease: 'Sine.easeInOut',
    alpha: { start: 1, end: 0.7 },
    particles: { count: 6, color: 0xFFD700, spread: 20 }
  },
  
  [AnimationType.PLANTING]: {
    duration: 1000,
    ease: 'Elastic.easeOut',
    scale: { start: 0, end: 1 },
    rotation: { start: -0.2, end: 0 }
  },
  
  [AnimationType.GROWTH_BOOST]: {
    duration: 400,
    ease: 'Quad.easeOut',
    scale: { start: 1, end: 1.1 },
    particles: { count: 15, color: 0x00FF00, spread: 35 }
  },
  
  [AnimationType.IDLE_SWAY]: {
    duration: 3000,
    ease: 'Sine.easeInOut',
    rotation: { start: -0.03, end: 0.03 }
  }
}

// 动画管理器
export class AnimationManager {
  private scene: Phaser.Scene
  private activeTweens: Map<string, Phaser.Tweens.Tween[]> = new Map()
  private particlePool: Phaser.GameObjects.Graphics[] = []
  
  constructor(scene: Phaser.Scene) {
    this.scene = scene
  }
  
  /**
   * 播放作物动画
   */
  playAnimation(
    sprite: CropSprite,
    type: AnimationType,
    customConfig?: Partial<AnimationConfig>
  ): Promise<void> {
    return new Promise((resolve) => {
      const config = { ...ANIMATION_PRESETS[type], ...customConfig }
      const spriteId = this.getSpriteId(sprite)
      
      // 停止现有动画
      this.stopAnimation(spriteId)
      
      const tweens: Phaser.Tweens.Tween[] = []
      
      // 主动画
      const mainTween = this.createMainTween(sprite, config, () => {
        this.cleanupTweens(spriteId)
        resolve()
      })
      
      if (mainTween) {
        tweens.push(mainTween)
      }
      
      // 粒子效果
      if (config.particles) {
        this.createParticleEffect(sprite, config.particles)
      }
      
      // 循环动画特殊处理
      if (type === AnimationType.IDLE_SWAY || type === AnimationType.HARVEST_READY) {
        this.setupLoopingAnimation(sprite, type, config, tweens)
      }
      
      this.activeTweens.set(spriteId, tweens)
    })
  }
  
  /**
   * 创建主要补间动画
   */
  private createMainTween(
    sprite: CropSprite,
    config: AnimationConfig,
    onComplete: () => void
  ): Phaser.Tweens.Tween | null {
    const targets: any = {}
    
    // 缩放动画
    if (config.scale) {
      targets.scaleX = config.scale.end
      targets.scaleY = config.scale.end
    }
    
    // 旋转动画
    if (config.rotation) {
      targets.rotation = config.rotation.end
    }
    
    // 透明度动画
    if (config.alpha) {
      targets.alpha = config.alpha.end
    }
    
    // 如果没有动画属性，直接完成
    if (Object.keys(targets).length === 0) {
      onComplete()
      return null
    }
    
    return this.scene.tweens.add({
      targets: sprite,
      ...targets,
      duration: config.duration,
      ease: config.ease || 'Linear',
      yoyo: config.alpha ? true : false, // 透明度动画需要往返
      onComplete
    })
  }
  
  /**
   * 设置循环动画
   */
  private setupLoopingAnimation(
    sprite: CropSprite,
    type: AnimationType,
    config: AnimationConfig,
    tweens: Phaser.Tweens.Tween[]
  ): void {
    if (type === AnimationType.IDLE_SWAY && config.rotation) {
      const swayTween = this.scene.tweens.add({
        targets: sprite,
        rotation: config.rotation.end,
        duration: config.duration,
        ease: config.ease || 'Sine.easeInOut',
        yoyo: true,
        repeat: -1
      })
      tweens.push(swayTween)
    }
    
    if (type === AnimationType.HARVEST_READY && config.alpha) {
      const glowTween = this.scene.tweens.add({
        targets: sprite,
        alpha: config.alpha.end,
        duration: config.duration,
        ease: config.ease || 'Sine.easeInOut',
        yoyo: true,
        repeat: -1
      })
      tweens.push(glowTween)
    }
  }
  
  /**
   * 创建粒子效果
   */
  private createParticleEffect(
    sprite: CropSprite,
    particleConfig: { count: number; color: number; spread: number }
  ): void {
    const { count, color, spread } = particleConfig
    
    for (let i = 0; i < count; i++) {
      setTimeout(() => {
        this.createSingleParticle(sprite, color, spread)
      }, i * 50) // 错开粒子生成时间
    }
  }
  
  /**
   * 创建单个粒子
   */
  private createSingleParticle(
    sprite: CropSprite,
    color: number,
    spread: number
  ): void {
    // 从对象池获取或创建新粒子
    let particle = this.particlePool.pop()
    
    if (!particle) {
      particle = this.scene.add.graphics()
    }
    
    // 设置粒子属性
    particle.clear()
    particle.fillStyle(color, 0.8)
    particle.fillCircle(0, 0, 2 + Math.random() * 2)
    particle.setPosition(sprite.x, sprite.y)
    particle.setAlpha(1)
    particle.setScale(1)
    
    // 随机方向和距离
    const angle = Math.random() * Math.PI * 2
    const distance = spread + Math.random() * spread
    const targetX = sprite.x + Math.cos(angle) * distance
    const targetY = sprite.y + Math.sin(angle) * distance
    
    // 粒子动画
    this.scene.tweens.add({
      targets: particle,
      x: targetX,
      y: targetY,
      alpha: 0,
      scale: 0,
      duration: 1000 + Math.random() * 500,
      ease: 'Quad.easeOut',
      onComplete: () => {
        // 回收到对象池
        particle!.setVisible(false)
        this.particlePool.push(particle!)
      }
    })
  }
  
  /**
   * 停止指定精灵的动画
   */
  stopAnimation(spriteId: string): void {
    const tweens = this.activeTweens.get(spriteId)
    if (tweens) {
      tweens.forEach(tween => {
        if (tween && !tween.isDestroyed()) {
          tween.destroy()
        }
      })
      this.activeTweens.delete(spriteId)
    }
  }
  
  /**
   * 清理补间动画
   */
  private cleanupTweens(spriteId: string): void {
    const tweens = this.activeTweens.get(spriteId)
    if (tweens) {
      // 只清理已完成的补间动画
      const activeTweens = tweens.filter(tween => !tween.isDestroyed())
      if (activeTweens.length > 0) {
        this.activeTweens.set(spriteId, activeTweens)
      } else {
        this.activeTweens.delete(spriteId)
      }
    }
  }
  
  /**
   * 获取精灵唯一标识
   */
  private getSpriteId(sprite: CropSprite): string {
    return sprite.cropData.id
  }
  
  /**
   * 播放种植动画序列
   */
  async playPlantingSequence(sprite: CropSprite): Promise<void> {
    // 1. 种子出现动画
    await this.playAnimation(sprite, AnimationType.PLANTING)
    
    // 2. 短暂延迟
    await this.delay(300)
    
    // 3. 开始摇摆动画
    this.playAnimation(sprite, AnimationType.IDLE_SWAY)
  }
  
  /**
   * 播放生长阶段转换序列
   */
  async playStageTransitionSequence(
    sprite: CropSprite,
    fromStage: CropStage,
    toStage: CropStage
  ): Promise<void> {
    // 1. 停止摇摆动画
    this.stopAnimation(this.getSpriteId(sprite))
    
    // 2. 播放转换动画
    await this.playAnimation(sprite, AnimationType.STAGE_TRANSITION)
    
    // 3. 如果进入收获阶段，播放收获就绪动画
    if (toStage === CropStage.READY_TO_HARVEST) {
      this.playAnimation(sprite, AnimationType.HARVEST_READY)
    } else {
      // 否则恢复摇摆动画
      this.playAnimation(sprite, AnimationType.IDLE_SWAY)
    }
  }
  
  /**
   * 播放品质升级序列
   */
  async playQualityUpgradeSequence(sprite: CropSprite): Promise<void> {
    await this.playAnimation(sprite, AnimationType.QUALITY_UPGRADE)
  }
  
  /**
   * 播放生长加速效果
   */
  playGrowthBoost(sprite: CropSprite): void {
    this.playAnimation(sprite, AnimationType.GROWTH_BOOST)
  }
  
  /**
   * 暂停所有动画
   */
  pauseAllAnimations(): void {
    this.activeTweens.forEach((tweens) => {
      tweens.forEach(tween => {
        if (tween && !tween.isDestroyed()) {
          tween.pause()
        }
      })
    })
  }
  
  /**
   * 恢复所有动画
   */
  resumeAllAnimations(): void {
    this.activeTweens.forEach((tweens) => {
      tweens.forEach(tween => {
        if (tween && !tween.isDestroyed()) {
          tween.resume()
        }
      })
    })
  }
  
  /**
   * 清理所有动画和资源
   */
  destroy(): void {
    // 停止所有动画
    this.activeTweens.forEach((tweens, spriteId) => {
      this.stopAnimation(spriteId)
    })
    
    // 清理粒子池
    this.particlePool.forEach(particle => {
      if (particle && particle.active) {
        particle.destroy()
      }
    })
    
    this.activeTweens.clear()
    this.particlePool.length = 0
  }
  
  /**
   * 获取调试信息
   */
  getDebugInfo(): {
    activeTweensCount: number
    particlePoolSize: number
    memoryUsage: {
      tweenMemory: number
      particleMemory: number
    }
  } {
    let totalTweens = 0
    this.activeTweens.forEach(tweens => {
      totalTweens += tweens.length
    })
    
    return {
      activeTweensCount: totalTweens,
      particlePoolSize: this.particlePool.length,
      memoryUsage: {
        tweenMemory: totalTweens * 100, // 估算值
        particleMemory: this.particlePool.length * 50 // 估算值
      }
    }
  }
  
  /**
   * 工具函数：延迟
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => {
      this.scene.time.delayedCall(ms, resolve)
    })
  }
}

// 动画预设工厂
export class AnimationPresets {
  /**
   * 创建知识花专用动画配置
   */
  static createKnowledgeFlowerConfig(): Partial<AnimationConfig> {
    return {
      particles: { count: 6, color: 0x87CEEB, spread: 20 },
      ease: 'Elastic.easeOut'
    }
  }
  
  /**
   * 创建力量树专用动画配置
   */
  static createStrengthTreeConfig(): Partial<AnimationConfig> {
    return {
      particles: { count: 10, color: 0x228B22, spread: 30 },
      duration: 1000,
      ease: 'Back.easeOut'
    }
  }
  
  /**
   * 创建时间菜专用动画配置
   */
  static createTimeVeggieConfig(): Partial<AnimationConfig> {
    return {
      particles: { count: 8, color: 0x32CD32, spread: 15 },
      duration: 600,
      ease: 'Quad.easeOut'
    }
  }
  
  /**
   * 创建冥想莲专用动画配置
   */
  static createMeditationLotusConfig(): Partial<AnimationConfig> {
    return {
      particles: { count: 12, color: 0xFF69B4, spread: 35 },
      duration: 1200,
      ease: 'Sine.easeInOut'
    }
  }
  
  /**
   * 根据作物类型获取动画配置
   */
  static getConfigForCropType(cropType: CropType): Partial<AnimationConfig> {
    switch (cropType) {
      case CropType.KNOWLEDGE_FLOWER:
        return this.createKnowledgeFlowerConfig()
      case CropType.STRENGTH_TREE:
        return this.createStrengthTreeConfig()
      case CropType.TIME_VEGGIE:
        return this.createTimeVeggieConfig()
      case CropType.MEDITATION_LOTUS:
        return this.createMeditationLotusConfig()
      default:
        return {}
    }
  }
} 