import Phaser from 'phaser'
import { GameStateManager } from '../../managers/GameStateManager'
import { CropInstance, CropType, CropStage, CropQuality } from '../../types/crop'

export interface HarvestResult {
  success: boolean
  crop?: CropInstance
  rewards?: {
    experience: number
    resources: Record<string, number>
    bonus?: {
      type: string
      value: number
      message: string
    }
  }
  message: string
}

export interface HarvestAnimation {
  particles?: Phaser.GameObjects.Particles.ParticleEmitter
  texts?: Phaser.GameObjects.Text[]
  effects?: Phaser.GameObjects.GameObject[]
}

export class HarvestManager {
  private scene: Phaser.Scene
  private gameStateManager: GameStateManager
  private activeAnimations: Map<string, HarvestAnimation> = new Map()
  
  // 奖励配置
  private readonly rewardConfig = {
    [CropType.KNOWLEDGE_FLOWER]: {
      baseExperience: 15,
      baseResources: { knowledge: 8 },
      bonusMultiplier: 1.2
    },
    [CropType.STRENGTH_TREE]: {
      baseExperience: 25,
      baseResources: { strength: 12 },
      bonusMultiplier: 1.5
    },
    [CropType.TIME_VEGGIE]: {
      baseExperience: 20,
      baseResources: { time: 10 },
      bonusMultiplier: 1.3
    },
    [CropType.MEDITATION_LOTUS]: {
      baseExperience: 30,
      baseResources: { meditation: 15 },
      bonusMultiplier: 1.8
    }
  }
  
  // 品质奖励修正
  private readonly qualityBonus = {
    [CropQuality.COMMON]: 1.0,
    [CropQuality.UNCOMMON]: 1.3,
    [CropQuality.RARE]: 1.6,
    [CropQuality.EPIC]: 2.0,
    [CropQuality.LEGENDARY]: 3.0
  }

  constructor(scene: Phaser.Scene, gameStateManager: GameStateManager) {
    this.scene = scene
    this.gameStateManager = gameStateManager
  }

  /**
   * 执行收获操作
   */
  public async harvestCrop(gridX: number, gridY: number): Promise<HarvestResult> {
    const gameState = this.gameStateManager.getGameState()
    const crop = gameState.farmGrid.plots[gridY]?.[gridX]
    
    if (!crop) {
      return {
        success: false,
        message: '该位置没有作物'
      }
    }
    
    if (crop.stage !== CropStage.READY_TO_HARVEST) {
      return {
        success: false,
        message: '作物尚未成熟，无法收获'
      }
    }
    
    // 计算奖励
    const rewards = this.calculateRewards(crop)
    
    // 执行实际收获
    const result = this.gameStateManager.harvestCrop(gridX, gridY)
    
    if (result.success) {
      // 播放收获动画
      await this.playHarvestAnimation(gridX, gridY, crop, rewards)
      
      // 检查成就和升级
      this.checkAchievements()
      this.checkLevelUp()
      
      return {
        success: true,
        crop,
        rewards,
        message: this.getHarvestMessage(crop, rewards)
      }
    }
    
    return {
      success: false,
      message: '收获失败，请重试'
    }
  }

  /**
   * 批量收获成熟作物
   */
  public async harvestAllMature(): Promise<{
    totalHarvested: number
    totalExperience: number
    totalResources: Record<string, number>
    harvestResults: HarvestResult[]
  }> {
    const gameState = this.gameStateManager.getGameState()
    const harvestResults: HarvestResult[] = []
    let totalExperience = 0
    const totalResources: Record<string, number> = {}
    
    for (let y = 0; y < gameState.gridSize.height; y++) {
      for (let x = 0; x < gameState.gridSize.width; x++) {
        const crop = gameState.farmGrid.plots[y][x]
        
        if (crop && crop.stage === CropStage.READY_TO_HARVEST) {
          const result = await this.harvestCrop(x, y)
          harvestResults.push(result)
          
          if (result.success && result.rewards) {
            totalExperience += result.rewards.experience
            
            Object.entries(result.rewards.resources).forEach(([resource, amount]) => {
              totalResources[resource] = (totalResources[resource] || 0) + amount
            })
          }
          
          // 添加延迟以避免动画重叠
          await this.delay(300)
        }
      }
    }
    
    // 显示批量收获总结
    if (harvestResults.length > 0) {
      this.showBatchHarvestSummary(totalExperience, totalResources, harvestResults.length)
    }
    
    return {
      totalHarvested: harvestResults.filter(r => r.success).length,
      totalExperience,
      totalResources,
      harvestResults
    }
  }

  /**
   * 计算收获奖励
   */
  private calculateRewards(crop: CropInstance): {
    experience: number
    resources: Record<string, number>
    bonus?: { type: string; value: number; message: string }
  } {
    const config = this.rewardConfig[crop.type]
    const qualityMultiplier = this.qualityBonus[crop.quality]
    
    let experience = Math.floor(config.baseExperience * qualityMultiplier)
    const resources: Record<string, number> = {}
    
    // 基础资源奖励
    Object.entries(config.baseResources).forEach(([resource, amount]) => {
      resources[resource] = Math.floor(amount * qualityMultiplier)
    })
    
    // 计算奖励加成
    let bonus: { type: string; value: number; message: string } | undefined
    
    // 专注时间奖励
    if (crop.focusTimeContributed > 1800000) { // 30分钟
      const focusBonus = Math.floor(experience * 0.5)
      experience += focusBonus
      bonus = {
        type: 'focus',
        value: focusBonus,
        message: '专注时间奖励！'
      }
    }
    
    // 完美品质奖励
    if (crop.quality === CropQuality.LEGENDARY) {
      const perfectBonus = Math.floor(experience * 0.8)
      experience += perfectBonus
      bonus = {
        type: 'perfect',
        value: perfectBonus,
        message: '完美品质奖励！'
      }
    }
    
    // 连续收获奖励
    const gameState = this.gameStateManager.getGameState()
    if (gameState.totalCropsHarvested > 0 && gameState.totalCropsHarvested % 10 === 9) {
      const streakBonus = Math.floor(experience * 0.3)
      experience += streakBonus
      bonus = {
        type: 'streak',
        value: streakBonus,
        message: '连续收获奖励！'
      }
    }
    
    return { experience, resources, bonus }
  }

  /**
   * 播放收获动画
   */
  private async playHarvestAnimation(
    gridX: number, 
    gridY: number, 
    crop: CropInstance, 
    rewards: any
  ): Promise<void> {
    const worldX = 200 + gridX * 80 + 40
    const worldY = 100 + gridY * 80 + 40
    const animationId = `harvest_${gridX}_${gridY}_${Date.now()}`
    
    const animation: HarvestAnimation = {
      particles: undefined,
      texts: [],
      effects: []
    }
    
    // 1. 收获粒子爆炸
    const harvestParticles = this.scene.add.particles(worldX, worldY, 'sparkle', {
      speed: { min: 60, max: 120 },
      scale: { start: 0.6, end: 0 },
      alpha: { start: 1, end: 0 },
      lifespan: 1200,
      quantity: 20,
      tint: this.getCropColor(crop.type)
    })
    animation.particles = harvestParticles
    
    // 2. 收获光环效果
    const harvestRing = this.scene.add.circle(worldX, worldY, 50, this.getCropColor(crop.type), 0)
    harvestRing.setStrokeStyle(4, this.getCropColor(crop.type), 0.8)
    animation.effects!.push(harvestRing)
    
    this.scene.tweens.add({
      targets: harvestRing,
      scaleX: 2.5,
      scaleY: 2.5,
      alpha: 0,
      duration: 1000,
      ease: 'Power2.easeOut'
    })
    
    // 3. 经验值文本
    const expText = this.scene.add.text(worldX, worldY - 40, `+${rewards.experience} 经验`, {
      fontSize: '16px',
      color: '#FFD700',
      fontFamily: 'Arial',
      fontStyle: 'bold'
    }).setOrigin(0.5)
    animation.texts!.push(expText)
    
    this.scene.tweens.add({
      targets: expText,
      y: expText.y - 50,
      alpha: 0,
      duration: 2000,
      ease: 'Power2.easeOut'
    })
    
    // 4. 资源奖励文本
    let textOffset = 0
    Object.entries(rewards.resources).forEach(([resource, amount]) => {
      const resourceText = this.scene.add.text(
        worldX + textOffset, 
        worldY - 20, 
        `+${amount} ${this.getResourceName(resource)}`, {
        fontSize: '12px',
        color: '#90EE90',
        fontFamily: 'Arial'
      }).setOrigin(0.5)
      animation.texts!.push(resourceText)
      
      this.scene.tweens.add({
        targets: resourceText,
        y: resourceText.y - 30,
        alpha: 0,
        duration: 1800,
        delay: 300,
        ease: 'Power2.easeOut'
      })
      
      textOffset += 40
    })
    
    // 5. 奖励加成显示
    if (rewards.bonus) {
      const bonusText = this.scene.add.text(worldX, worldY + 20, rewards.bonus.message, {
        fontSize: '14px',
        color: '#FF69B4',
        fontFamily: 'Arial',
        fontStyle: 'bold'
      }).setOrigin(0.5)
      animation.texts!.push(bonusText)
      
      this.scene.tweens.add({
        targets: bonusText,
        y: bonusText.y - 40,
        alpha: 0,
        duration: 2500,
        delay: 500,
        ease: 'Power2.easeOut'
      })
    }
    
    // 6. 品质指示器
    if (crop.quality !== CropQuality.COMMON) {
      const qualityText = this.scene.add.text(worldX, worldY + 40, this.getQualityName(crop.quality), {
        fontSize: '10px',
        color: this.getQualityColor(crop.quality),
        fontFamily: 'Arial',
        fontStyle: 'italic'
      }).setOrigin(0.5)
      animation.texts!.push(qualityText)
      
      this.scene.tweens.add({
        targets: qualityText,
        alpha: 0,
        duration: 2000,
        delay: 800
      })
    }
    
    this.activeAnimations.set(animationId, animation)
    
    // 清理动画
    this.scene.time.delayedCall(3000, () => {
      this.cleanupAnimation(animationId)
    })
    
    return this.delay(800) // 等待主要动画完成
  }

  /**
   * 显示批量收获总结
   */
  private showBatchHarvestSummary(
    totalExperience: number, 
    totalResources: Record<string, number>, 
    count: number
  ): void {
    const centerX = 400
    const centerY = 300
    
    const summaryBg = this.scene.add.rectangle(centerX, centerY, 300, 200, 0x2C5530, 0.95)
    summaryBg.setStrokeStyle(3, 0x90EE90)
    
    const titleText = this.scene.add.text(centerX, centerY - 70, '🌾 批量收获完成！', {
      fontSize: '18px',
      color: '#FFD700',
      fontFamily: 'Arial',
      fontStyle: 'bold'
    }).setOrigin(0.5)
    
    const summaryLines = [
      `收获作物: ${count} 个`,
      `总经验: +${totalExperience}`,
      ...Object.entries(totalResources).map(([resource, amount]) => 
        `${this.getResourceName(resource)}: +${amount}`)
    ]
    
    summaryLines.forEach((line, index) => {
      this.scene.add.text(centerX, centerY - 30 + index * 25, line, {
        fontSize: '14px',
        color: '#FFFFFF',
        fontFamily: 'Arial'
      }).setOrigin(0.5)
    })
    
    // 显示动画
    summaryBg.setScale(0)
    titleText.setScale(0)
    
    this.scene.tweens.add({
      targets: [summaryBg, titleText],
      scaleX: 1,
      scaleY: 1,
      duration: 400,
      ease: 'Back.easeOut'
    })
    
    // 自动隐藏
    this.scene.time.delayedCall(4000, () => {
      this.scene.tweens.add({
        targets: [summaryBg, titleText],
        alpha: 0,
        duration: 500,
        onComplete: () => {
          summaryBg.destroy()
          titleText.destroy()
        }
      })
    })
  }

  /**
   * 检查成就
   */
  private checkAchievements(): void {
    const gameState = this.gameStateManager.getGameState()
    
    // 首次收获成就
    if (gameState.totalCropsHarvested === 1) {
      this.showAchievement('初次收获', '收获了第一个作物！')
    }
    
    // 收获里程碑
    const milestones = [10, 50, 100, 500]
    if (milestones.includes(gameState.totalCropsHarvested)) {
      this.showAchievement(
        `收获专家${gameState.totalCropsHarvested}`, 
        `累计收获${gameState.totalCropsHarvested}个作物！`
      )
    }
  }

  /**
   * 检查等级提升
   */
  private checkLevelUp(): void {
    const gameState = this.gameStateManager.getGameState()
    const stats = this.gameStateManager.getGameStats()
    
    if (gameState.experience >= stats.nextLevelExp) {
      this.showLevelUp(gameState.level + 1)
    }
  }

  /**
   * 显示成就
   */
  private showAchievement(title: string, description: string): void {
    const achievementBg = this.scene.add.rectangle(400, 100, 350, 80, 0x4169E1, 0.9)
    achievementBg.setStrokeStyle(2, 0x6495ED)
    
    const achievementText = this.scene.add.text(400, 85, `🏆 ${title}`, {
      fontSize: '16px',
      color: '#FFD700',
      fontFamily: 'Arial',
      fontStyle: 'bold'
    }).setOrigin(0.5)
    
    const descText = this.scene.add.text(400, 110, description, {
      fontSize: '12px',
      color: '#FFFFFF',
      fontFamily: 'Arial'
    }).setOrigin(0.5)
    
    // 滑入动画
    achievementBg.y = -50
    achievementText.y = -65
    descText.y = -40
    
    this.scene.tweens.add({
      targets: [achievementBg, achievementText, descText],
      y: '+=150',
      duration: 500,
      ease: 'Back.easeOut'
    })
    
    // 自动隐藏
    this.scene.time.delayedCall(3000, () => {
      this.scene.tweens.add({
        targets: [achievementBg, achievementText, descText],
        y: '-=150',
        duration: 400,
        onComplete: () => {
          achievementBg.destroy()
          achievementText.destroy()
          descText.destroy()
        }
      })
    })
  }

  /**
   * 显示等级提升
   */
  private showLevelUp(newLevel: number): void {
    // 创建等级提升特效
    console.log(`恭喜！等级提升到 ${newLevel}！`)
  }

  /**
   * 获取收获消息
   */
  private getHarvestMessage(crop: CropInstance, rewards: any): string {
    const cropName = this.getCropName(crop.type)
    const qualityName = this.getQualityName(crop.quality)
    
    if (rewards.bonus) {
      return `收获了${qualityName}的${cropName}！获得额外奖励！`
    }
    
    return `成功收获了${qualityName}的${cropName}！`
  }

  // 辅助方法
  private getCropName(cropType: CropType): string {
    const names = {
      [CropType.KNOWLEDGE_FLOWER]: '知识花',
      [CropType.STRENGTH_TREE]: '力量树',
      [CropType.TIME_VEGGIE]: '时间蔬菜',
      [CropType.MEDITATION_LOTUS]: '冥想莲花'
    }
    return names[cropType] || '未知作物'
  }

  private getCropColor(cropType: CropType): number {
    const colors = {
      [CropType.KNOWLEDGE_FLOWER]: 0xFF69B4,
      [CropType.STRENGTH_TREE]: 0x8B4513,
      [CropType.TIME_VEGGIE]: 0x90EE90,
      [CropType.MEDITATION_LOTUS]: 0x9370DB
    }
    return colors[cropType] || 0xFFFFFF
  }

  private getResourceName(resource: string): string {
    const names: Record<string, string> = {
      knowledge: '知识',
      strength: '力量',
      time: '时间',
      meditation: '冥想'
    }
    return names[resource] || resource
  }

  private getQualityName(quality: CropQuality): string {
    const names = {
      [CropQuality.COMMON]: '普通',
      [CropQuality.UNCOMMON]: '优秀',
      [CropQuality.RARE]: '稀有',
      [CropQuality.EPIC]: '史诗',
      [CropQuality.LEGENDARY]: '传说'
    }
    return names[quality] || '未知'
  }

  private getQualityColor(quality: CropQuality): string {
    const colors = {
      [CropQuality.COMMON]: '#FFFFFF',
      [CropQuality.UNCOMMON]: '#32CD32',
      [CropQuality.RARE]: '#4169E1',
      [CropQuality.EPIC]: '#9370DB',
      [CropQuality.LEGENDARY]: '#FFD700'
    }
    return colors[quality] || '#FFFFFF'
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => {
      this.scene.time.delayedCall(ms, resolve)
    })
  }

  private cleanupAnimation(animationId: string): void {
    const animation = this.activeAnimations.get(animationId)
    if (animation) {
      animation.particles?.destroy()
      animation.texts?.forEach(text => text.destroy())
      animation.effects?.forEach(effect => effect.destroy())
      this.activeAnimations.delete(animationId)
    }
  }

  /**
   * 销毁管理器
   */
  public destroy(): void {
    this.activeAnimations.forEach((animation, id) => {
      this.cleanupAnimation(id)
    })
    this.activeAnimations.clear()
  }
} 