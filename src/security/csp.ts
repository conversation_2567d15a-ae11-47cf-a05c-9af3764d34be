/**
 * 内容安全策略 (CSP) 配置
 * 用于增强应用的安全性，防止XSS攻击和代码注入
 */

export interface CSPDirectives {
  'default-src'?: string[];
  'script-src'?: string[];
  'style-src'?: string[];
  'img-src'?: string[];
  'font-src'?: string[];
  'connect-src'?: string[];
  'media-src'?: string[];
  'object-src'?: string[];
  'child-src'?: string[];
  'frame-src'?: string[];
  'worker-src'?: string[];
  'manifest-src'?: string[];
  'base-uri'?: string[];
  'form-action'?: string[];
  'frame-ancestors'?: string[];
}

// 开发环境CSP配置（相对宽松）
export const developmentCSP: CSPDirectives = {
  'default-src': ["'self'", "'unsafe-inline'", "'unsafe-eval'"],
  'script-src': [
    "'self'",
    "'unsafe-inline'",
    "'unsafe-eval'",
    'http://localhost:*',
    'ws://localhost:*',
    'blob:',
    'data:'
  ],
  'style-src': [
    "'self'",
    "'unsafe-inline'",
    'https://fonts.googleapis.com'
  ],
  'img-src': [
    "'self'",
    'data:',
    'blob:',
    'https:',
    'http://localhost:*'
  ],
  'font-src': [
    "'self'",
    'data:',
    'https://fonts.gstatic.com'
  ],
  'connect-src': [
    "'self'",
    'http://localhost:*',
    'ws://localhost:*',
    'wss://localhost:*',
    'https://api.openweathermap.org',
    'https://api.weatherapi.com'
  ],
  'media-src': [
    "'self'",
    'blob:',
    'data:',
    'mediastream:'
  ],
  'worker-src': [
    "'self'",
    'blob:'
  ],
  'child-src': [
    "'self'"
  ],
  'frame-src': [
    "'self'"
  ]
};

// 生产环境CSP配置（更严格）
export const productionCSP: CSPDirectives = {
  'default-src': ["'self'"],
  'script-src': [
    "'self'",
    "'sha256-xyz123...'", // 替换为实际的脚本哈希
    'blob:'
  ],
  'style-src': [
    "'self'",
    "'sha256-abc456...'", // 替换为实际的样式哈希
    'https://fonts.googleapis.com'
  ],
  'img-src': [
    "'self'",
    'data:',
    'blob:',
    'https:'
  ],
  'font-src': [
    "'self'",
    'data:',
    'https://fonts.gstatic.com'
  ],
  'connect-src': [
    "'self'",
    'https://api.openweathermap.org',
    'https://api.weatherapi.com',
    'https://cdn.jsdelivr.net'
  ],
  'media-src': [
    "'self'",
    'blob:',
    'mediastream:'
  ],
  'worker-src': [
    "'self'",
    'blob:'
  ],
  'child-src': ["'none'"],
  'frame-src': ["'none'"],
  'object-src': ["'none'"],
  'base-uri': ["'self'"],
  'form-action': ["'self'"],
  'frame-ancestors': ["'none'"]
};

// 将CSP指令转换为字符串格式
export function formatCSP(directives: CSPDirectives): string {
  return Object.entries(directives)
    .map(([directive, sources]) => `${directive} ${sources.join(' ')}`)
    .join('; ');
}

// 获取当前环境的CSP配置
export function getCurrentCSP(): string {
  const isProduction = process.env.NODE_ENV === 'production';
  const directives = isProduction ? productionCSP : developmentCSP;
  return formatCSP(directives);
}

// 应用CSP到页面
export function applyCSP(): void {
  if (typeof document !== 'undefined') {
    // 创建或更新CSP meta标签
    let cspMeta = document.querySelector('meta[http-equiv="Content-Security-Policy"]') as HTMLMetaElement;
    
    if (!cspMeta) {
      cspMeta = document.createElement('meta');
      cspMeta.httpEquiv = 'Content-Security-Policy';
      document.head.appendChild(cspMeta);
    }
    
    cspMeta.content = getCurrentCSP();
    console.log('CSP已应用:', cspMeta.content);
  }
}

// CSP违反报告处理
export function setupCSPReporting(): void {
  if (typeof document !== 'undefined') {
    document.addEventListener('securitypolicyviolation', (event) => {
      console.warn('CSP违反报告:', {
        blockedURI: event.blockedURI,
        directive: event.violatedDirective,
        originalPolicy: event.originalPolicy,
        sourceFile: event.sourceFile,
        lineNumber: event.lineNumber,
        columnNumber: event.columnNumber
      });
      
      // 在生产环境中，可以将违反报告发送到服务器
      if (process.env.NODE_ENV === 'production') {
        // TODO: 发送CSP违反报告到监控服务
      }
    });
  }
}

// 安全标头配置（用于Electron main进程）
export const securityHeaders = {
  // 防止点击劫持
  'X-Frame-Options': 'DENY',
  
  // 防止MIME类型嗅探
  'X-Content-Type-Options': 'nosniff',
  
  // 启用XSS保护
  'X-XSS-Protection': '1; mode=block',
  
  // 引用策略
  'Referrer-Policy': 'strict-origin-when-cross-origin',
  
  // 权限策略
  'Permissions-Policy': 'camera=(self), microphone=(self), geolocation=(), payment=()',
  
  // 严格传输安全（如果使用HTTPS）
  'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
  
  // CSP
  'Content-Security-Policy': getCurrentCSP()
};

// 应用安全标头到Electron窗口
export function applySecurityHeaders(webContents: any): void {
  // 设置请求拦截器添加安全标头
  webContents.session.webRequest.onHeadersReceived((details: any, callback: any) => {
    const responseHeaders = {
      ...details.responseHeaders,
      ...securityHeaders
    };
    
    callback({ responseHeaders });
  });
  
  // 禁用Node.js集成（如果不需要）
  // webContents.setWebSecurity(true);
}

// 验证资源完整性
export function verifyResourceIntegrity(url: string, expectedHash: string): Promise<boolean> {
  return new Promise((resolve) => {
    fetch(url)
      .then(response => response.arrayBuffer())
      .then(buffer => {
        // 计算资源哈希
        const hashBuffer = crypto.subtle.digest('SHA-256', buffer);
        return hashBuffer;
      })
      .then(hashBuffer => {
        const hashArray = Array.from(new Uint8Array(hashBuffer));
        const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
        const hashBase64 = btoa(String.fromCharCode.apply(null, hashArray));
        
        // 验证哈希
        const isValid = hashHex === expectedHash || hashBase64 === expectedHash;
        resolve(isValid);
      })
      .catch(() => {
        resolve(false);
      });
  });
}

// 初始化安全配置
export function initializeSecurity(): void {
  // 应用CSP
  applyCSP();
  
  // 设置CSP违反报告
  setupCSPReporting();
  
  // 禁用开发工具（在生产环境）
  if (process.env.NODE_ENV === 'production' && typeof window !== 'undefined') {
    // 禁用右键菜单
    document.addEventListener('contextmenu', (e) => {
      e.preventDefault();
    });
    
    // 禁用F12和Ctrl+Shift+I
    document.addEventListener('keydown', (e) => {
      if (
        e.key === 'F12' ||
        (e.ctrlKey && e.shiftKey && e.key === 'I') ||
        (e.ctrlKey && e.shiftKey && e.key === 'C') ||
        (e.ctrlKey && e.shiftKey && e.key === 'J')
      ) {
        e.preventDefault();
      }
    });
  }
  
  console.log('安全配置已初始化');
} 