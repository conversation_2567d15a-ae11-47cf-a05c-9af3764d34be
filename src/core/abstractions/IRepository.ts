/**
 * 仓库模式抽象接口
 * 定义数据访问层的统一接口
 */

import { IItem, ItemId } from './IItem'
import { ItemCategory, Quality } from '../../types/enhanced-items'

// 基础仓库接口
export interface IRepository<TEntity, TId> {
  findById(id: TId): Promise<TEntity | null>
  save(entity: TEntity): Promise<void>
  delete(id: TId): Promise<void>
  findAll(): Promise<TEntity[]>
  exists(id: TId): Promise<boolean>
}

// 查询规格接口
export interface ISpecification<T> {
  isSatisfiedBy(item: T): boolean
  and(other: ISpecification<T>): ISpecification<T>
  or(other: ISpecification<T>): ISpecification<T>
  not(): ISpecification<T>
}

// 道具查询过滤器
export interface ItemFilter {
  category?: ItemCategory
  quality?: Quality[]
  priceRange?: { min: number; max: number }
  tradeable?: boolean
  stackable?: boolean
  searchText?: string
  location?: string
}

// 分页参数
export interface PaginationParams {
  page: number
  pageSize: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

// 分页结果
export interface PaginatedResult<T> {
  items: T[]
  totalCount: number
  totalPages: number
  currentPage: number
  pageSize: number
}

// 道具仓库接口
export interface IItemRepository extends IRepository<IItem, ItemId> {
  // 高级查询方法
  findByCategory(category: ItemCategory): Promise<IItem[]>
  findByQuality(quality: Quality): Promise<IItem[]>
  findByFilter(filter: ItemFilter): Promise<IItem[]>
  findByFilterPaginated(
    filter: ItemFilter, 
    pagination: PaginationParams
  ): Promise<PaginatedResult<IItem>>
  
  // 批量操作
  saveMany(items: IItem[]): Promise<void>
  deleteMany(ids: ItemId[]): Promise<void>
  
  // 聚合查询
  countByCategory(category: ItemCategory): Promise<number>
  getTotalValue(filter?: ItemFilter): Promise<number>
  getQualityDistribution(): Promise<Record<Quality, number>>
  
  // 搜索功能
  search(query: string, limit?: number): Promise<IItem[]>
  findSimilar(item: IItem, limit?: number): Promise<IItem[]>
}

// 用户库存仓库接口
export interface IInventoryRepository {
  getUserItems(userId: string): Promise<IItem[]>
  addItemToUser(userId: string, item: IItem): Promise<void>
  removeItemFromUser(userId: string, itemId: ItemId): Promise<void>
  updateItemQuantity(userId: string, itemId: ItemId, quantity: number): Promise<void>
  getEquippedItems(userId: string): Promise<IItem[]>
  
  // 库存查询
  findUserItemsByCategory(userId: string, category: ItemCategory): Promise<IItem[]>
  getUserInventoryValue(userId: string): Promise<number>
  getUserItemCount(userId: string): Promise<number>
} 