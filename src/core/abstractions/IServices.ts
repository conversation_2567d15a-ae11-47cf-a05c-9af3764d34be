/**
 * 服务层抽象接口
 * 定义业务逻辑服务的统一接口
 */

import { IItem, ItemId, ItemConfig, ItemTemplate } from './IItem'
import { ItemFilter, PaginationParams, PaginatedResult } from './IRepository'
import { Quality, ItemCategory } from '../../types/enhanced-items'

// 操作结果接口
export interface OperationResult<T = void> {
  success: boolean
  data?: T
  error?: string
  warnings?: string[]
}

// 道具工厂接口
export interface IItemFactory {
  createItem(template: ItemTemplate, quality: Quality): IItem
  createFromConfig(config: ItemConfig): IItem
  createRandomItem(category: ItemCategory, qualityWeights?: Record<Quality, number>): IItem
  getAvailableTemplates(category?: ItemCategory): ItemTemplate[]
  validateTemplate(template: ItemTemplate): boolean
}

// 道具服务接口
export interface IItemService {
  // 基础CRUD操作
  getItem(id: ItemId): Promise<OperationResult<IItem>>
  createItem(config: ItemConfig): Promise<OperationResult<IItem>>
  updateItem(id: ItemId, updates: Partial<ItemConfig>): Promise<OperationResult<IItem>>
  deleteItem(id: ItemId): Promise<OperationResult>
  
  // 查询操作
  findItems(filter: ItemFilter): Promise<OperationResult<IItem[]>>
  findItemsPaginated(
    filter: ItemFilter, 
    pagination: PaginationParams
  ): Promise<OperationResult<PaginatedResult<IItem>>>
  searchItems(query: string, limit?: number): Promise<OperationResult<IItem[]>>
  
  // 业务操作
  stackItems(sourceItem: IItem, targetItem: IItem): Promise<OperationResult<IItem>>
  splitItem(item: IItem, quantity: number): Promise<OperationResult<IItem[]>>
  upgradeItem(item: IItem): Promise<OperationResult<IItem>>
  
  // 验证操作
  validateItem(item: IItem): Promise<OperationResult<boolean>>
  canStack(item1: IItem, item2: IItem): boolean
  canTrade(item: IItem): boolean
}

// 合成配方接口
export interface SynthesisRecipe {
  id: string
  name: string
  description: string
  category: ItemCategory
  requiredItems: {
    template: ItemTemplate
    quality: Quality
    quantity: number
  }[]
  resultTemplate: ItemTemplate
  resultQuality: Quality
  baseSuccessRate: number
  cost?: number
  requiredLevel?: number
  unlockConditions?: string[]
}

// 合成结果接口
export interface SynthesisResult {
  success: boolean
  resultItem?: IItem
  consumedItems: IItem[]
  refundedItems?: IItem[]
  experience?: number
  cost?: number
}

// 合成服务接口
export interface ISynthesisService {
  // 合成操作
  synthesize(
    recipe: SynthesisRecipe, 
    materials: IItem[], 
    focusTime?: number
  ): Promise<OperationResult<SynthesisResult>>
  
  // 配方管理
  getRecipe(id: string): Promise<OperationResult<SynthesisRecipe>>
  getRecipesByCategory(category: ItemCategory): Promise<OperationResult<SynthesisRecipe[]>>
  getAllRecipes(): Promise<OperationResult<SynthesisRecipe[]>>
  
  // 验证操作
  validateMaterials(recipe: SynthesisRecipe, materials: IItem[]): boolean
  calculateSuccessRate(
    recipe: SynthesisRecipe, 
    materials: IItem[], 
    focusTime?: number
  ): number
  previewSynthesis(recipe: SynthesisRecipe, materials: IItem[]): SynthesisResult
  
  // 自动合成
  findSuitableRecipe(materials: IItem[]): Promise<OperationResult<SynthesisRecipe[]>>
  autoSynthesize(materials: IItem[]): Promise<OperationResult<SynthesisResult>>
}

// 盲盒配置接口
export interface LootboxConfig {
  id: string
  name: string
  description: string
  category: ItemCategory | 'mixed'
  icon: string
  price: {
    currency: string
    amount: number
  }
  dropRates: Record<Quality, number>
  itemPool: string[]
  guaranteedRarity?: Quality
  specialFeatures?: {
    guaranteedCount?: number
    bonusChance?: number
    pityTimer?: number
    limitedTime?: boolean
  }
}

// 盲盒结果接口
export interface LootboxResult {
  success: boolean
  items: IItem[]
  totalValue: number
  cost: {
    currency: string
    amount: number
  }
  bonusItems?: IItem[]
  pityProgress?: number
}

// 盲盒预览接口
export interface LootboxPreview {
  config: LootboxConfig
  possibleItems: string[]
  averageValue: number
  qualityDistribution: Record<Quality, number>
}

// 盲盒服务接口
export interface ILootboxService {
  // 盲盒操作
  openLootbox(
    type: string, 
    currency: Record<string, number>
  ): Promise<OperationResult<LootboxResult>>
  
  // 配置管理
  getAvailableLootboxes(): Promise<OperationResult<LootboxConfig[]>>
  getLootboxConfig(type: string): Promise<OperationResult<LootboxConfig>>
  previewLootbox(type: string): Promise<OperationResult<LootboxPreview>>
  
  // 批量操作
  openMultipleLootboxes(
    type: string, 
    count: number, 
    currency: Record<string, number>
  ): Promise<OperationResult<LootboxResult[]>>
  
  // 统计分析
  getLootboxStats(type: string): Promise<OperationResult<any>>
  getUserLootboxHistory(userId: string): Promise<OperationResult<LootboxResult[]>>
}

// 装备服务接口
export interface IEquipmentService {
  // 装备操作
  equipItem(userId: string, item: IItem): Promise<OperationResult>
  unequipItem(userId: string, itemId: ItemId): Promise<OperationResult>
  getEquippedItems(userId: string): Promise<OperationResult<IItem[]>>
  
  // 装备效果
  calculateEquipmentEffects(items: IItem[]): Record<string, number>
  getEquipmentBonus(userId: string): Promise<OperationResult<Record<string, number>>>
  
  // 装备验证
  canEquip(userId: string, item: IItem): Promise<OperationResult<boolean>>
  getEquipmentSlots(userId: string): Promise<OperationResult<Record<string, IItem | null>>>
} 