/**
 * 事件系统抽象接口
 * 定义领域事件和事件总线的统一接口
 */

import { IItem, ItemId } from './IItem'
import { Quality, ItemCategory } from '../../types/enhanced-items'
import { SynthesisResult, LootboxResult } from './IServices'

// 基础领域事件接口
export interface IDomainEvent {
  readonly eventId: string
  readonly eventType: string
  readonly timestamp: Date
  readonly aggregateId: string
  readonly eventData: Record<string, any>
  readonly userId?: string
  readonly correlationId?: string
}

// 事件处理器接口
export interface IEventHandler<TEvent extends IDomainEvent> {
  handle(event: TEvent): Promise<void>
  canHandle(eventType: string): boolean
}

// 事件总线接口
export interface IEventBus {
  publish(event: IDomainEvent): Promise<void>
  publishMany(events: IDomainEvent[]): Promise<void>
  subscribe<T extends IDomainEvent>(
    eventType: string,
    handler: IEventHandler<T>
  ): string
  unsubscribe(eventType: string, subscriptionId: string): void
  getSubscriptions(eventType?: string): string[]
}

// 事件存储接口
export interface IEventStore {
  append(event: IDomainEvent): Promise<void>
  getEvents(aggregateId: string): Promise<IDomainEvent[]>
  getEventsByType(eventType: string): Promise<IDomainEvent[]>
  getEventsAfter(timestamp: Date): Promise<IDomainEvent[]>
}

// ============= 道具相关事件 =============

// 道具创建事件
export interface ItemCreatedEvent extends IDomainEvent {
  eventType: 'ItemCreated'
  eventData: {
    itemId: string
    itemType: ItemCategory
    quality: Quality
    template: string
    creator?: string
    source: 'creation' | 'synthesis' | 'lootbox' | 'reward'
  }
}

// 道具更新事件
export interface ItemUpdatedEvent extends IDomainEvent {
  eventType: 'ItemUpdated'
  eventData: {
    itemId: string
    changes: Record<string, { old: any; new: any }>
    updatedBy?: string
  }
}

// 道具删除事件
export interface ItemDeletedEvent extends IDomainEvent {
  eventType: 'ItemDeleted'
  eventData: {
    itemId: string
    itemType: ItemCategory
    quality: Quality
    deletedBy?: string
    reason: 'user_action' | 'synthesis' | 'expired' | 'admin'
  }
}

// 道具转移事件
export interface ItemTransferredEvent extends IDomainEvent {
  eventType: 'ItemTransferred'
  eventData: {
    itemId: string
    fromLocation: string
    toLocation: string
    fromUser?: string
    toUser?: string
    quantity: number
  }
}

// ============= 合成相关事件 =============

// 合成开始事件
export interface SynthesisStartedEvent extends IDomainEvent {
  eventType: 'SynthesisStarted'
  eventData: {
    recipeId: string
    materials: {
      itemId: string
      quality: Quality
      quantity: number
    }[]
    expectedSuccessRate: number
    focusTime?: number
  }
}

// 合成完成事件
export interface SynthesisCompletedEvent extends IDomainEvent {
  eventType: 'SynthesisCompleted'
  eventData: {
    recipeId: string
    result: SynthesisResult
    materials: {
      itemId: string
      quality: Quality
      quantity: number
    }[]
    actualSuccessRate: number
  }
}

// 合成失败事件
export interface SynthesisFailedEvent extends IDomainEvent {
  eventType: 'SynthesisFailed'
  eventData: {
    recipeId: string
    materials: {
      itemId: string
      quality: Quality
      quantity: number
    }[]
    reason: string
    refundedItems?: string[]
  }
}

// ============= 盲盒相关事件 =============

// 盲盒开启事件
export interface LootboxOpenedEvent extends IDomainEvent {
  eventType: 'LootboxOpened'
  eventData: {
    lootboxType: string
    result: LootboxResult
    cost: {
      currency: string
      amount: number
    }
    pityProgress?: number
  }
}

// 盲盒购买事件
export interface LootboxPurchasedEvent extends IDomainEvent {
  eventType: 'LootboxPurchased'
  eventData: {
    lootboxType: string
    quantity: number
    totalCost: {
      currency: string
      amount: number
    }
  }
}

// ============= 装备相关事件 =============

// 装备装备事件
export interface ItemEquippedEvent extends IDomainEvent {
  eventType: 'ItemEquipped'
  eventData: {
    itemId: string
    slot: string
    previousItem?: string
    effects: Record<string, number>
  }
}

// 装备卸下事件
export interface ItemUnequippedEvent extends IDomainEvent {
  eventType: 'ItemUnequipped'
  eventData: {
    itemId: string
    slot: string
    effects: Record<string, number>
  }
}

// ============= 经济相关事件 =============

// 货币变化事件
export interface CurrencyChangedEvent extends IDomainEvent {
  eventType: 'CurrencyChanged'
  eventData: {
    currencyType: string
    oldAmount: number
    newAmount: number
    change: number
    reason: string
    relatedItemId?: string
  }
}

// 交易事件
export interface ItemTradedEvent extends IDomainEvent {
  eventType: 'ItemTraded'
  eventData: {
    itemId: string
    fromUser: string
    toUser: string
    price: number
    currency: string
    marketId?: string
  }
}

// ============= 统计相关事件 =============

// 用户行为事件
export interface UserActionEvent extends IDomainEvent {
  eventType: 'UserAction'
  eventData: {
    action: string
    category: string
    details: Record<string, any>
    sessionId?: string
  }
}

// 成就解锁事件
export interface AchievementUnlockedEvent extends IDomainEvent {
  eventType: 'AchievementUnlocked'
  eventData: {
    achievementId: string
    achievementName: string
    category: string
    rewards?: {
      itemId?: string
      currency?: { type: string; amount: number }[]
    }
  }
}

// ============= 事件类型联合 =============

export type ItemEvent = 
  | ItemCreatedEvent
  | ItemUpdatedEvent
  | ItemDeletedEvent
  | ItemTransferredEvent

export type SynthesisEvent = 
  | SynthesisStartedEvent
  | SynthesisCompletedEvent
  | SynthesisFailedEvent

export type LootboxEvent = 
  | LootboxOpenedEvent
  | LootboxPurchasedEvent

export type EquipmentEvent = 
  | ItemEquippedEvent
  | ItemUnequippedEvent

export type EconomyEvent = 
  | CurrencyChangedEvent
  | ItemTradedEvent

export type SystemEvent = 
  | UserActionEvent
  | AchievementUnlockedEvent

export type AllEvents = 
  | ItemEvent
  | SynthesisEvent
  | LootboxEvent
  | EquipmentEvent
  | EconomyEvent
  | SystemEvent

// 事件工厂接口
export interface IEventFactory {
  createItemCreatedEvent(
    itemId: string,
    item: IItem,
    source: string,
    userId?: string
  ): ItemCreatedEvent

  createSynthesisCompletedEvent(
    recipeId: string,
    result: SynthesisResult,
    materials: any[],
    userId?: string
  ): SynthesisCompletedEvent

  createLootboxOpenedEvent(
    lootboxType: string,
    result: LootboxResult,
    userId?: string
  ): LootboxOpenedEvent

  // ... 其他事件创建方法
} 