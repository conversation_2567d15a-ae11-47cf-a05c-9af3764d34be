/**
 * 道具系统核心抽象接口
 * 定义道具的基本结构和行为
 */

import { Quality, ItemCategory } from '../../types/enhanced-items'

// 值对象：道具ID
export class ItemId {
  constructor(public readonly value: string) {
    if (!value || value.trim().length === 0) {
      throw new Error('ItemId cannot be empty')
    }
  }

  equals(other: ItemId): boolean {
    return this.value === other.value
  }

  toString(): string {
    return this.value
  }
}

// 值对象：道具元数据
export interface ItemMetadata {
  readonly name: string
  readonly description: string
  readonly icon: string
  readonly category: ItemCategory
  readonly createdAt: Date
  readonly updatedAt: Date
}

// 值对象：道具属性
export interface ItemProperties {
  readonly quality: Quality
  readonly baseValue: number
  readonly stackable: boolean
  readonly tradeable: boolean
  readonly attributes: Record<string, any>
}

// 值对象：道具状态
export interface ItemState {
  readonly isEquipped: boolean
  readonly durability?: number
  readonly quantity: number
  readonly location: ItemLocation
}

// 枚举：道具位置
export enum ItemLocation {
  INVENTORY = 'inventory',
  EQUIPPED = 'equipped',
  STORAGE = 'storage',
  MARKET = 'market',
  SYNTHESIS = 'synthesis'
}

// 核心道具接口
export interface IItem {
  readonly id: ItemId
  readonly metadata: ItemMetadata
  readonly properties: ItemProperties
  readonly state: ItemState

  // 行为方法
  updateState(newState: Partial<ItemState>): IItem
  updateProperties(newProperties: Partial<ItemProperties>): IItem
  canStackWith(other: IItem): boolean
  getDisplayName(): string
  getQualityBonus(): number
  serialize(): Record<string, any>
}

// 道具模板接口（用于创建道具）
export interface ItemTemplate {
  readonly templateId: string
  readonly category: ItemCategory
  readonly baseMetadata: Omit<ItemMetadata, 'createdAt' | 'updatedAt'>
  readonly baseProperties: Omit<ItemProperties, 'quality'>
  readonly defaultState: Omit<ItemState, 'quantity'>
}

// 道具配置接口
export interface ItemConfig {
  readonly template: ItemTemplate
  readonly quality: Quality
  readonly quantity: number
  readonly customAttributes?: Record<string, any>
} 