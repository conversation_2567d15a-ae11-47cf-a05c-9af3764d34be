/**
 * 配置管理系统
 * 提供统一的配置管理接口
 */

// 配置管理器接口
export interface IConfigManager {
  get<T>(key: string): T
  set<T>(key: string, value: T): void
  getSection<T>(section: string): T
  reload(): Promise<void>
  subscribe(key: string, callback: (value: any) => void): () => void
}

// 配置变更事件
export interface ConfigChangeEvent {
  key: string
  oldValue: any
  newValue: any
  timestamp: Date
}

// 配置提供者接口
export interface IConfigProvider {
  load(): Promise<Record<string, any>>
  save(config: Record<string, any>): Promise<void>
}

// 配置验证器接口
export interface IConfigValidator {
  validate(key: string, value: any): boolean
  getValidationError(key: string, value: any): string | null
}

// 配置管理器实现
export class ConfigManager implements IConfigManager {
  private config: Record<string, any> = {}
  private providers: IConfigProvider[] = []
  private validators: Map<string, IConfigValidator> = new Map()
  private subscribers: Map<string, ((value: any) => void)[]> = new Map()

  constructor(providers: IConfigProvider[] = []) {
    this.providers = providers
  }

  /**
   * 获取配置值
   */
  get<T>(key: string): T {
    const keys = key.split('.')
    let current = this.config

    for (const k of keys) {
      if (current === null || current === undefined) {
        throw new Error(`Configuration key '${key}' not found`)
      }
      current = current[k]
    }

    return current as T
  }

  /**
   * 设置配置值
   */
  set<T>(key: string, value: T): void {
    const validator = this.validators.get(key)
    if (validator && !validator.validate(key, value)) {
      const error = validator.getValidationError(key, value)
      throw new Error(`Invalid configuration value for '${key}': ${error}`)
    }

    const oldValue = this.tryGet(key)
    this.setNestedValue(key, value)

    // 通知订阅者
    this.notifySubscribers(key, { key, oldValue, newValue: value, timestamp: new Date() })
  }

  /**
   * 获取配置段
   */
  getSection<T>(section: string): T {
    return this.get<T>(section)
  }

  /**
   * 重新加载配置
   */
  async reload(): Promise<void> {
    const newConfig: Record<string, any> = {}

    // 从所有提供者加载配置
    for (const provider of this.providers) {
      try {
        const providerConfig = await provider.load()
        Object.assign(newConfig, providerConfig)
      } catch (error) {
        console.warn('Failed to load config from provider:', error)
      }
    }

    // 更新配置并通知变更
    const oldConfig = { ...this.config }
    this.config = newConfig

    // 通知所有可能受影响的订阅者
    this.notifyAllSubscribers(oldConfig, newConfig)
  }

  /**
   * 订阅配置变更
   */
  subscribe(key: string, callback: (value: any) => void): () => void {
    const callbacks = this.subscribers.get(key) || []
    callbacks.push(callback)
    this.subscribers.set(key, callbacks)

    // 返回取消订阅函数
    return () => {
      const currentCallbacks = this.subscribers.get(key) || []
      const index = currentCallbacks.indexOf(callback)
      if (index > -1) {
        currentCallbacks.splice(index, 1)
        if (currentCallbacks.length === 0) {
          this.subscribers.delete(key)
        } else {
          this.subscribers.set(key, currentCallbacks)
        }
      }
    }
  }

  /**
   * 添加配置提供者
   */
  addProvider(provider: IConfigProvider): void {
    this.providers.push(provider)
  }

  /**
   * 添加配置验证器
   */
  addValidator(key: string, validator: IConfigValidator): void {
    this.validators.set(key, validator)
  }

  /**
   * 获取所有配置键
   */
  getAllKeys(): string[] {
    return this.getAllKeysRecursive(this.config, '')
  }

  /**
   * 检查配置键是否存在
   */
  has(key: string): boolean {
    try {
      this.get(key)
      return true
    } catch {
      return false
    }
  }

  /**
   * 获取配置统计信息
   */
  getStats(): {
    totalKeys: number
    providers: number
    validators: number
    subscribers: number
  } {
    return {
      totalKeys: this.getAllKeys().length,
      providers: this.providers.length,
      validators: this.validators.size,
      subscribers: this.subscribers.size
    }
  }

  /**
   * 尝试获取配置值（不抛出异常）
   */
  private tryGet(key: string): any {
    try {
      return this.get(key)
    } catch {
      return undefined
    }
  }

  /**
   * 设置嵌套值
   */
  private setNestedValue(key: string, value: any): void {
    const keys = key.split('.')
    let current = this.config

    for (let i = 0; i < keys.length - 1; i++) {
      const k = keys[i]
      if (!(k in current) || typeof current[k] !== 'object') {
        current[k] = {}
      }
      current = current[k]
    }

    current[keys[keys.length - 1]] = value
  }

  /**
   * 递归获取所有配置键
   */
  private getAllKeysRecursive(obj: any, prefix: string): string[] {
    const keys: string[] = []

    for (const [key, value] of Object.entries(obj)) {
      const fullKey = prefix ? `${prefix}.${key}` : key

      if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
        keys.push(...this.getAllKeysRecursive(value, fullKey))
      } else {
        keys.push(fullKey)
      }
    }

    return keys
  }

  /**
   * 通知订阅者
   */
  private notifySubscribers(key: string, event: ConfigChangeEvent): void {
    const callbacks = this.subscribers.get(key) || []
    for (const callback of callbacks) {
      try {
        callback(event.newValue)
      } catch (error) {
        console.error(`Error in config subscriber for '${key}':`, error)
      }
    }
  }

  /**
   * 通知所有订阅者配置变更
   */
  private notifyAllSubscribers(oldConfig: Record<string, any>, newConfig: Record<string, any>): void {
    const allKeys = new Set([
      ...this.getAllKeysRecursive(oldConfig, ''),
      ...this.getAllKeysRecursive(newConfig, '')
    ])

    for (const key of allKeys) {
      const oldValue = this.getValueByPath(oldConfig, key)
      const newValue = this.getValueByPath(newConfig, key)

      if (oldValue !== newValue) {
        this.notifySubscribers(key, {
          key,
          oldValue,
          newValue,
          timestamp: new Date()
        })
      }
    }
  }

  /**
   * 通过路径获取值
   */
  private getValueByPath(obj: any, path: string): any {
    const keys = path.split('.')
    let current = obj

    for (const key of keys) {
      if (current === null || current === undefined) {
        return undefined
      }
      current = current[key]
    }

    return current
  }
}

// JSON 文件配置提供者
export class JsonConfigProvider implements IConfigProvider {
  constructor(private filePath: string) {}

  async load(): Promise<Record<string, any>> {
    try {
      // 在实际项目中，这里应该使用fs模块读取文件
      // 这里提供一个简化的实现
      const response = await fetch(this.filePath)
      return await response.json()
    } catch (error) {
      console.warn(`Failed to load JSON config from ${this.filePath}:`, error)
      return {}
    }
  }

  async save(config: Record<string, any>): Promise<void> {
    // 在实际项目中，这里应该使用fs模块写入文件
    console.log('Save config to', this.filePath, config)
  }
}

// 环境变量配置提供者
export class EnvironmentConfigProvider implements IConfigProvider {
  constructor(private prefix: string = '') {}

  async load(): Promise<Record<string, any>> {
    const config: Record<string, any> = {}

    // 在浏览器环境中，process.env 可能不可用
    if (typeof process !== 'undefined' && process.env) {
      for (const [key, value] of Object.entries(process.env)) {
        if (!this.prefix || key.startsWith(this.prefix)) {
          const configKey = this.prefix ? key.substring(this.prefix.length) : key
          config[configKey.toLowerCase()] = this.parseValue(value)
        }
      }
    }

    return config
  }

  async save(): Promise<void> {
    throw new Error('Cannot save to environment variables')
  }

  private parseValue(value: string | undefined): any {
    if (value === undefined) return undefined
    if (value === 'true') return true
    if (value === 'false') return false
    if (/^\d+$/.test(value)) return parseInt(value, 10)
    if (/^\d+\.\d+$/.test(value)) return parseFloat(value)
    return value
  }
}

// 简单的配置验证器
export class SimpleConfigValidator implements IConfigValidator {
  constructor(
    private validationRules: Record<string, (value: any) => boolean>,
    private errorMessages: Record<string, string> = {}
  ) {}

  validate(key: string, value: any): boolean {
    const rule = this.validationRules[key]
    return rule ? rule(value) : true
  }

  getValidationError(key: string, value: any): string | null {
    if (this.validate(key, value)) {
      return null
    }
    return this.errorMessages[key] || `Invalid value for ${key}`
  }
}

// 道具系统配置定义
export interface ItemSystemConfig {
  synthesis: {
    baseSuccessRate: number
    focusTimeMultiplier: number
    maxFocusBonus: number
  }
  lootbox: {
    guaranteedPityTimer: number
    bonusDropChance: number
    maxDailyOpens: number
  }
  inventory: {
    maxSlots: number
    autoStack: boolean
    defaultLocation: string
  }
  equipment: {
    maxEquippedItems: number
    durabilityEnabled: boolean
    repairCostMultiplier: number
  }
}

// 默认配置
export const DEFAULT_ITEM_SYSTEM_CONFIG: ItemSystemConfig = {
  synthesis: {
    baseSuccessRate: 0.8,
    focusTimeMultiplier: 0.2,
    maxFocusBonus: 0.5
  },
  lootbox: {
    guaranteedPityTimer: 10,
    bonusDropChance: 0.1,
    maxDailyOpens: 50
  },
  inventory: {
    maxSlots: 100,
    autoStack: true,
    defaultLocation: 'inventory'
  },
  equipment: {
    maxEquippedItems: 4,
    durabilityEnabled: true,
    repairCostMultiplier: 0.1
  }
}

// 全局配置管理器实例
export const configManager = new ConfigManager([
  new EnvironmentConfigProvider('ITEM_SYSTEM_')
])

// 初始化默认配置
Object.entries(DEFAULT_ITEM_SYSTEM_CONFIG).forEach(([section, config]) => {
  Object.entries(config).forEach(([key, value]) => {
    const fullKey = `${section}.${key}`
    if (!configManager.has(fullKey)) {
      configManager.set(fullKey, value)
    }
  })
}) 