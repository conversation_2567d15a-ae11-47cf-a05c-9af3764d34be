/**
 * 依赖注入容器
 * 管理服务的注册、解析和生命周期
 */

// 服务生命周期枚举
export enum ServiceLifetime {
  TRANSIENT = 'transient',  // 每次创建新实例
  SINGLETON = 'singleton',  // 单例模式
  SCOPED = 'scoped'        // 作用域内单例
}

// 服务描述符
export interface ServiceDescriptor {
  token: string
  implementation?: any
  factory?: (container: DIContainer) => any
  lifetime: ServiceLifetime
  dependencies?: string[]
}

// 依赖注入错误
export class DIError extends Error {
  constructor(message: string, public readonly token?: string) {
    super(message)
    this.name = 'DIError'
  }
}

// 循环依赖检测器
class CircularDependencyDetector {
  private resolutionStack: string[] = []

  startResolving(token: string): void {
    if (this.resolutionStack.includes(token)) {
      const cycle = [...this.resolutionStack, token].join(' -> ')
      throw new DIError(`Circular dependency detected: ${cycle}`, token)
    }
    this.resolutionStack.push(token)
  }

  finishResolving(): void {
    this.resolutionStack.pop()
  }
}

// 作用域管理器
export class Scope {
  private instances = new Map<string, any>()

  get<T>(token: string): T | undefined {
    return this.instances.get(token)
  }

  set<T>(token: string, instance: T): void {
    this.instances.set(token, instance)
  }

  dispose(): void {
    // 调用所有实例的dispose方法（如果存在）
    for (const instance of this.instances.values()) {
      if (instance && typeof instance.dispose === 'function') {
        try {
          instance.dispose()
        } catch (error) {
          console.warn('Error disposing instance:', error)
        }
      }
    }
    this.instances.clear()
  }
}

// 依赖注入容器主类
export class DIContainer {
  private services = new Map<string, ServiceDescriptor>()
  private singletonInstances = new Map<string, any>()
  private currentScope: Scope | null = null
  private circularDetector = new CircularDependencyDetector()

  /**
   * 注册瞬态服务
   */
  registerTransient<T>(token: string, implementation: new (...args: any[]) => T): void {
    this.services.set(token, {
      token,
      implementation,
      lifetime: ServiceLifetime.TRANSIENT
    })
  }

  /**
   * 注册单例服务
   */
  registerSingleton<T>(token: string, implementation: new (...args: any[]) => T): void {
    this.services.set(token, {
      token,
      implementation,
      lifetime: ServiceLifetime.SINGLETON
    })
  }

  /**
   * 注册作用域服务
   */
  registerScoped<T>(token: string, implementation: new (...args: any[]) => T): void {
    this.services.set(token, {
      token,
      implementation,
      lifetime: ServiceLifetime.SCOPED
    })
  }

  /**
   * 注册工厂方法
   */
  registerFactory<T>(
    token: string,
    factory: (container: DIContainer) => T,
    lifetime: ServiceLifetime = ServiceLifetime.TRANSIENT
  ): void {
    this.services.set(token, {
      token,
      factory,
      lifetime
    })
  }

  /**
   * 注册实例
   */
  registerInstance<T>(token: string, instance: T): void {
    this.singletonInstances.set(token, instance)
    this.services.set(token, {
      token,
      lifetime: ServiceLifetime.SINGLETON
    })
  }

  /**
   * 解析服务
   */
  resolve<T>(token: string): T {
    const descriptor = this.services.get(token)
    if (!descriptor) {
      throw new DIError(`Service not registered: ${token}`, token)
    }

    try {
      this.circularDetector.startResolving(token)
      return this.createInstance<T>(descriptor)
    } finally {
      this.circularDetector.finishResolving()
    }
  }

  /**
   * 尝试解析服务（不抛出异常）
   */
  tryResolve<T>(token: string): T | null {
    try {
      return this.resolve<T>(token)
    } catch {
      return null
    }
  }

  /**
   * 检查服务是否已注册
   */
  isRegistered(token: string): boolean {
    return this.services.has(token)
  }

  /**
   * 创建作用域
   */
  createScope(): Scope {
    return new Scope()
  }

  /**
   * 在作用域内执行
   */
  async executeInScope<T>(scope: Scope, action: () => Promise<T>): Promise<T> {
    const previousScope = this.currentScope
    this.currentScope = scope
    try {
      return await action()
    } finally {
      this.currentScope = previousScope
    }
  }

  /**
   * 获取所有已注册的服务标识
   */
  getRegisteredServices(): string[] {
    return Array.from(this.services.keys())
  }

  /**
   * 清理容器
   */
  dispose(): void {
    // 清理单例实例
    for (const instance of this.singletonInstances.values()) {
      if (instance && typeof instance.dispose === 'function') {
        try {
          instance.dispose()
        } catch (error) {
          console.warn('Error disposing singleton instance:', error)
        }
      }
    }

    // 清理当前作用域
    if (this.currentScope) {
      this.currentScope.dispose()
    }

    this.services.clear()
    this.singletonInstances.clear()
    this.currentScope = null
  }

  /**
   * 创建实例
   */
  private createInstance<T>(descriptor: ServiceDescriptor): T {
    switch (descriptor.lifetime) {
      case ServiceLifetime.SINGLETON:
        return this.createSingletonInstance<T>(descriptor)
      case ServiceLifetime.SCOPED:
        return this.createScopedInstance<T>(descriptor)
      case ServiceLifetime.TRANSIENT:
      default:
        return this.createTransientInstance<T>(descriptor)
    }
  }

  /**
   * 创建单例实例
   */
  private createSingletonInstance<T>(descriptor: ServiceDescriptor): T {
    let instance = this.singletonInstances.get(descriptor.token)
    if (!instance) {
      instance = this.instantiate<T>(descriptor)
      this.singletonInstances.set(descriptor.token, instance)
    }
    return instance
  }

  /**
   * 创建作用域实例
   */
  private createScopedInstance<T>(descriptor: ServiceDescriptor): T {
    if (!this.currentScope) {
      throw new DIError(
        `Cannot resolve scoped service '${descriptor.token}' outside of a scope`,
        descriptor.token
      )
    }

    let instance = this.currentScope.get<T>(descriptor.token)
    if (!instance) {
      instance = this.instantiate<T>(descriptor)
      this.currentScope.set(descriptor.token, instance)
    }
    return instance
  }

  /**
   * 创建瞬态实例
   */
  private createTransientInstance<T>(descriptor: ServiceDescriptor): T {
    return this.instantiate<T>(descriptor)
  }

  /**
   * 实例化对象
   */
  private instantiate<T>(descriptor: ServiceDescriptor): T {
    if (descriptor.factory) {
      return descriptor.factory(this)
    }

    if (descriptor.implementation) {
      const dependencies = this.resolveDependencies(descriptor.implementation)
      return new descriptor.implementation(...dependencies)
    }

    throw new DIError(
      `Cannot instantiate service '${descriptor.token}': no implementation or factory provided`,
      descriptor.token
    )
  }

  /**
   * 解析构造函数依赖
   */
  private resolveDependencies(constructor: any): any[] {
    // 这里可以使用reflect-metadata来获取依赖信息
    // 简化实现：从构造函数参数名或注解中获取依赖
    const dependencies: any[] = []
    
    // 如果构造函数有静态属性定义依赖
    if (constructor.dependencies) {
      for (const dependency of constructor.dependencies) {
        dependencies.push(this.resolve(dependency))
      }
    }

    return dependencies
  }
}

// 装饰器工具（可选）
export function Injectable(token?: string) {
  return function <T extends new (...args: any[]) => any>(constructor: T) {
    // 添加元数据以便容器识别
    (constructor as any).injectable = true
    if (token) {
      (constructor as any).token = token
    }
    return constructor
  }
}

export function Inject(token: string) {
  return function (target: any, propertyKey: string, parameterIndex: number) {
    // 简化的注入信息记录
    if (!target.injectTokens) {
      target.injectTokens = []
    }
    target.injectTokens[parameterIndex] = token
  }
}

// 全局容器实例
export const container = new DIContainer() 