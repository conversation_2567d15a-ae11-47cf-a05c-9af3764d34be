/**
 * 事件总线实现
 * 提供发布订阅模式的事件系统
 */

import { 
  IDomainEvent, 
  IEventHandler, 
  IEventBus, 
  IEventStore 
} from '../abstractions/IEvents'

// 订阅信息
interface Subscription {
  id: string
  eventType: string
  handler: IEventHandler<any>
  created: Date
}

// 事件总线错误
export class EventBusError extends Error {
  constructor(message: string, public readonly eventType?: string) {
    super(message)
    this.name = 'EventBusError'
  }
}

// 事件总线实现
export class EventBus implements IEventBus {
  private subscriptions = new Map<string, Subscription[]>()
  private eventStore: IEventStore | null = null
  private isProcessing = false
  private eventQueue: IDomainEvent[] = []

  constructor(eventStore?: IEventStore) {
    this.eventStore = eventStore || null
  }

  /**
   * 发布单个事件
   */
  async publish(event: IDomainEvent): Promise<void> {
    try {
      // 存储事件（如果配置了事件存储）
      if (this.eventStore) {
        await this.eventStore.append(event)
      }

      // 如果正在处理事件，将新事件加入队列
      if (this.isProcessing) {
        this.eventQueue.push(event)
        return
      }

      await this.processEvent(event)
    } catch (error) {
      console.error(`Error publishing event ${event.eventType}:`, error)
      throw new EventBusError(
        `Failed to publish event: ${error instanceof Error ? error.message : 'Unknown error'}`,
        event.eventType
      )
    }
  }

  /**
   * 发布多个事件
   */
  async publishMany(events: IDomainEvent[]): Promise<void> {
    const errors: Error[] = []

    for (const event of events) {
      try {
        await this.publish(event)
      } catch (error) {
        errors.push(error as Error)
      }
    }

    if (errors.length > 0) {
      throw new EventBusError(
        `Failed to publish ${errors.length} out of ${events.length} events`
      )
    }
  }

  /**
   * 订阅事件
   */
  subscribe<T extends IDomainEvent>(
    eventType: string,
    handler: IEventHandler<T>
  ): string {
    const subscription: Subscription = {
      id: this.generateSubscriptionId(),
      eventType,
      handler,
      created: new Date()
    }

    const eventSubscriptions = this.subscriptions.get(eventType) || []
    eventSubscriptions.push(subscription)
    this.subscriptions.set(eventType, eventSubscriptions)

    return subscription.id
  }

  /**
   * 取消订阅
   */
  unsubscribe(eventType: string, subscriptionId: string): void {
    const eventSubscriptions = this.subscriptions.get(eventType)
    if (!eventSubscriptions) {
      return
    }

    const filteredSubscriptions = eventSubscriptions.filter(
      sub => sub.id !== subscriptionId
    )

    if (filteredSubscriptions.length === 0) {
      this.subscriptions.delete(eventType)
    } else {
      this.subscriptions.set(eventType, filteredSubscriptions)
    }
  }

  /**
   * 获取订阅信息
   */
  getSubscriptions(eventType?: string): string[] {
    if (eventType) {
      const eventSubscriptions = this.subscriptions.get(eventType) || []
      return eventSubscriptions.map(sub => sub.id)
    }

    const allSubscriptionIds: string[] = []
    for (const subscriptions of this.subscriptions.values()) {
      allSubscriptionIds.push(...subscriptions.map(sub => sub.id))
    }
    return allSubscriptionIds
  }

  /**
   * 清理所有订阅
   */
  clear(): void {
    this.subscriptions.clear()
    this.eventQueue = []
  }

  /**
   * 获取统计信息
   */
  getStats(): {
    totalSubscriptions: number
    eventTypes: string[]
    queuedEvents: number
  } {
    const totalSubscriptions = Array.from(this.subscriptions.values())
      .reduce((total, subs) => total + subs.length, 0)

    return {
      totalSubscriptions,
      eventTypes: Array.from(this.subscriptions.keys()),
      queuedEvents: this.eventQueue.length
    }
  }

  /**
   * 处理单个事件
   */
  private async processEvent(event: IDomainEvent): Promise<void> {
    this.isProcessing = true

    try {
      const eventSubscriptions = this.subscriptions.get(event.eventType) || []
      const promises: Promise<void>[] = []

      for (const subscription of eventSubscriptions) {
        if (subscription.handler.canHandle(event.eventType)) {
          promises.push(this.handleEvent(subscription.handler, event))
        }
      }

      // 并行处理所有处理器
      await Promise.allSettled(promises)

      // 处理队列中的事件
      while (this.eventQueue.length > 0) {
        const queuedEvent = this.eventQueue.shift()!
        await this.processEvent(queuedEvent)
      }
    } finally {
      this.isProcessing = false
    }
  }

  /**
   * 调用事件处理器
   */
  private async handleEvent(
    handler: IEventHandler<any>, 
    event: IDomainEvent
  ): Promise<void> {
    try {
      await handler.handle(event)
    } catch (error) {
      console.error(
        `Error in event handler for ${event.eventType}:`, 
        error
      )
      // 不重新抛出错误，以避免影响其他处理器
    }
  }

  /**
   * 生成订阅ID
   */
  private generateSubscriptionId(): string {
    return `sub_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }
}

// 内存事件存储实现
export class MemoryEventStore implements IEventStore {
  private events: IDomainEvent[] = []
  private aggregateEvents = new Map<string, IDomainEvent[]>()

  async append(event: IDomainEvent): Promise<void> {
    this.events.push(event)

    // 按聚合根ID索引
    const aggregateEvents = this.aggregateEvents.get(event.aggregateId) || []
    aggregateEvents.push(event)
    this.aggregateEvents.set(event.aggregateId, aggregateEvents)
  }

  async getEvents(aggregateId: string): Promise<IDomainEvent[]> {
    return this.aggregateEvents.get(aggregateId) || []
  }

  async getEventsByType(eventType: string): Promise<IDomainEvent[]> {
    return this.events.filter(event => event.eventType === eventType)
  }

  async getEventsAfter(timestamp: Date): Promise<IDomainEvent[]> {
    return this.events.filter(event => event.timestamp > timestamp)
  }

  // 获取所有事件
  async getAllEvents(): Promise<IDomainEvent[]> {
    return [...this.events]
  }

  // 清理事件
  clear(): void {
    this.events = []
    this.aggregateEvents.clear()
  }

  // 获取统计信息
  getStats(): {
    totalEvents: number
    aggregateCount: number
    eventTypes: string[]
  } {
    const eventTypes = [...new Set(this.events.map(e => e.eventType))]
    
    return {
      totalEvents: this.events.length,
      aggregateCount: this.aggregateEvents.size,
      eventTypes
    }
  }
}

// 基础事件处理器抽象类
export abstract class BaseEventHandler<TEvent extends IDomainEvent> 
  implements IEventHandler<TEvent> {
  
  protected abstract readonly supportedEventTypes: string[]

  abstract handle(event: TEvent): Promise<void>

  canHandle(eventType: string): boolean {
    return this.supportedEventTypes.includes(eventType)
  }
}

// 全局事件总线实例
export const eventBus = new EventBus(new MemoryEventStore()) 