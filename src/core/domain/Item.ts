/**
 * 道具实体实现
 * 实现IItem接口的具体类
 */

import { 
  IItem, 
  ItemId, 
  ItemMetadata, 
  ItemProperties, 
  ItemState, 
  ItemLocation 
} from '../abstractions/IItem'
import { Quality, ItemCategory } from '../../types/enhanced-items'
import { QUALITY_CONFIGS } from '../../types/enhanced-items'

// 道具实体类
export class Item implements IItem {
  readonly id: ItemId
  readonly metadata: ItemMetadata
  readonly properties: ItemProperties
  readonly state: ItemState

  constructor(
    id: ItemId,
    metadata: ItemMetadata,
    properties: ItemProperties,
    state: ItemState
  ) {
    this.id = id
    this.metadata = metadata
    this.properties = properties
    this.state = state
  }

  /**
   * 更新道具状态
   */
  updateState(newState: Partial<ItemState>): IItem {
    const updatedState: ItemState = {
      ...this.state,
      ...newState
    }

    return new Item(
      this.id,
      this.metadata,
      this.properties,
      updatedState
    )
  }

  /**
   * 更新道具属性
   */
  updateProperties(newProperties: Partial<ItemProperties>): IItem {
    const updatedProperties: ItemProperties = {
      ...this.properties,
      ...newProperties
    }

    return new Item(
      this.id,
      this.metadata,
      updatedProperties,
      this.state
    )
  }

  /**
   * 检查是否可以与其他道具堆叠
   */
  canStackWith(other: IItem): boolean {
    if (!this.properties.stackable || !other.properties.stackable) {
      return false
    }

    // 基本属性必须匹配
    return (
      this.metadata.category === other.metadata.category &&
      this.properties.quality === other.properties.quality &&
      this.metadata.name === other.metadata.name &&
      this.state.location === other.state.location &&
      !this.state.isEquipped &&
      !other.state.isEquipped
    )
  }

  /**
   * 获取显示名称
   */
  getDisplayName(): string {
    const qualityConfig = QUALITY_CONFIGS[this.properties.quality]
    return `${qualityConfig.name}${this.metadata.name}`
  }

  /**
   * 获取品质加成
   */
  getQualityBonus(): number {
    const qualityConfig = QUALITY_CONFIGS[this.properties.quality]
    return qualityConfig.attributeBonus
  }

  /**
   * 序列化为普通对象
   */
  serialize(): Record<string, any> {
    return {
      id: this.id.value,
      metadata: {
        ...this.metadata,
        createdAt: this.metadata.createdAt.toISOString(),
        updatedAt: this.metadata.updatedAt.toISOString()
      },
      properties: this.properties,
      state: this.state
    }
  }

  /**
   * 从序列化数据创建道具实例
   */
  static deserialize(data: Record<string, any>): Item {
    return new Item(
      new ItemId(data.id),
      {
        ...data.metadata,
        createdAt: new Date(data.metadata.createdAt),
        updatedAt: new Date(data.metadata.updatedAt)
      },
      data.properties,
      data.state
    )
  }

  /**
   * 创建道具的副本
   */
  clone(): Item {
    return new Item(
      this.id,
      { ...this.metadata },
      { ...this.properties },
      { ...this.state }
    )
  }

  /**
   * 检查道具是否有效
   */
  isValid(): boolean {
    try {
      // 检查必要字段
      if (!this.id || !this.metadata.name || !this.metadata.description) {
        return false
      }

      // 检查值的有效性
      if (this.properties.baseValue < 0 || this.state.quantity < 0) {
        return false
      }

      // 检查品质是否有效
      if (!Object.values(Quality).includes(this.properties.quality)) {
        return false
      }

      // 检查分类是否有效
      if (!Object.values(ItemCategory).includes(this.metadata.category)) {
        return false
      }

      return true
    } catch {
      return false
    }
  }

  /**
   * 获取道具的总价值
   */
  getTotalValue(): number {
    const qualityMultiplier = this.getQualityBonus() / 100 + 1
    return Math.floor(this.properties.baseValue * qualityMultiplier * this.state.quantity)
  }

  /**
   * 检查是否可以交易
   */
  canTrade(): boolean {
    return this.properties.tradeable && !this.state.isEquipped
  }

  /**
   * 检查是否可以装备
   */
  canEquip(): boolean {
    return this.metadata.category === ItemCategory.EQUIPMENT && !this.state.isEquipped
  }

  /**
   * 获取道具的完整信息
   */
  getFullInfo(): string {
    const qualityConfig = QUALITY_CONFIGS[this.properties.quality]
    const info = [
      `名称: ${this.getDisplayName()}`,
      `描述: ${this.metadata.description}`,
      `品质: ${qualityConfig.name}`,
      `数量: ${this.state.quantity}`,
      `价值: ${this.getTotalValue()}`,
      `可交易: ${this.properties.tradeable ? '是' : '否'}`,
      `可堆叠: ${this.properties.stackable ? '是' : '否'}`
    ]

    if (this.state.durability !== undefined) {
      info.push(`耐久度: ${this.state.durability}`)
    }

    if (this.state.isEquipped) {
      info.push(`状态: 已装备`)
    }

    return info.join('\n')
  }
} 