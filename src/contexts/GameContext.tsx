import React, { createContext, useContext, useReducer, useCallback, ReactNode } from 'react'
import { PostureAnalysis } from '../types/pose'

// 游戏状态类型
export interface GameState {
  // 专注状态
  currentFocusScore: number
  averageFocusScore: number
  focusSession: {
    startTime: number | null
    duration: number
    isActive: boolean
  }
  
  // 农场状态
  farmStats: {
    knowledgeFlowers: number
    strengthTrees: number
    timeVeggies: number
    meditationLotus: number
    totalGrowthPoints: number
  }
  
  // 游戏设置
  settings: {
    focusThreshold: number      // 专注度阈值（触发奖励）
    growthRate: number          // 植物生长速度倍数
    autoGrowth: boolean         // 是否启用自动生长
  }
  
  // 实时状态
  isPostureGood: boolean
  lastPostureUpdate: number
  currentStreak: number         // 连续专注时长（秒）
  dailyStats: {
    totalFocusTime: number
    goodPosturePercentage: number
    plantsGrown: number
  }
}

// 动作类型
export type GameAction = 
  | { type: 'UPDATE_POSTURE'; payload: PostureAnalysis }
  | { type: 'START_FOCUS_SESSION' }
  | { type: 'END_FOCUS_SESSION' }
  | { type: 'GROW_PLANT'; payload: { type: string; count?: number } }
  | { type: 'UPDATE_SETTINGS'; payload: Partial<GameState['settings']> }
  | { type: 'RESET_DAILY_STATS' }
  | { type: 'ADD_GROWTH_POINTS'; payload: number }

// 初始状态
const initialState: GameState = {
  currentFocusScore: 0,
  averageFocusScore: 0,
  focusSession: {
    startTime: null,
    duration: 0,
    isActive: false
  },
  farmStats: {
    knowledgeFlowers: 0,
    strengthTrees: 0,
    timeVeggies: 0,
    meditationLotus: 0,
    totalGrowthPoints: 0
  },
  settings: {
    focusThreshold: 75,    // 75分以上触发奖励
    growthRate: 1.0,       // 正常生长速度
    autoGrowth: true       // 启用自动生长
  },
  isPostureGood: false,
  lastPostureUpdate: Date.now(),
  currentStreak: 0,
  dailyStats: {
    totalFocusTime: 0,
    goodPosturePercentage: 0,
    plantsGrown: 0
  }
}

// 状态更新函数
function gameReducer(state: GameState, action: GameAction): GameState {
  switch (action.type) {
    case 'UPDATE_POSTURE': {
      const { focusScore, isFocused } = action.payload
      const now = Date.now()
      const timeDelta = now - state.lastPostureUpdate
      
      // 更新连续专注时长
      let newStreak = state.currentStreak
      if (isFocused && focusScore >= state.settings.focusThreshold) {
        newStreak += timeDelta / 1000  // 转换为秒
      } else {
        newStreak = 0  // 重置连续时长
      }
      
      return {
        ...state,
        currentFocusScore: focusScore,
        isPostureGood: isFocused,
        lastPostureUpdate: now,
        currentStreak: newStreak,
        averageFocusScore: (state.averageFocusScore * 0.9 + focusScore * 0.1), // 滑动平均
      }
    }
    
    case 'START_FOCUS_SESSION': {
      return {
        ...state,
        focusSession: {
          startTime: Date.now(),
          duration: 0,
          isActive: true
        },
        currentStreak: 0
      }
    }
    
    case 'END_FOCUS_SESSION': {
      const sessionDuration = state.focusSession.startTime 
        ? Date.now() - state.focusSession.startTime 
        : 0
      
      return {
        ...state,
        focusSession: {
          startTime: null,
          duration: sessionDuration,
          isActive: false
        },
        dailyStats: {
          ...state.dailyStats,
          totalFocusTime: state.dailyStats.totalFocusTime + sessionDuration
        }
      }
    }
    
    case 'GROW_PLANT': {
      const { type, count = 1 } = action.payload
      const newFarmStats = { ...state.farmStats }
      
      switch (type) {
        case 'knowledge':
          newFarmStats.knowledgeFlowers += count
          break
        case 'strength':
          newFarmStats.strengthTrees += count
          break
        case 'time':
          newFarmStats.timeVeggies += count
          break
        case 'meditation':
          newFarmStats.meditationLotus += count
          break
      }
      
      return {
        ...state,
        farmStats: newFarmStats,
        dailyStats: {
          ...state.dailyStats,
          plantsGrown: state.dailyStats.plantsGrown + count
        }
      }
    }
    
    case 'ADD_GROWTH_POINTS': {
      return {
        ...state,
        farmStats: {
          ...state.farmStats,
          totalGrowthPoints: state.farmStats.totalGrowthPoints + action.payload
        }
      }
    }
    
    case 'UPDATE_SETTINGS': {
      return {
        ...state,
        settings: {
          ...state.settings,
          ...action.payload
        }
      }
    }
    
    case 'RESET_DAILY_STATS': {
      return {
        ...state,
        dailyStats: {
          totalFocusTime: 0,
          goodPosturePercentage: 0,
          plantsGrown: 0
        }
      }
    }
    
    default:
      return state
  }
}

// 上下文类型
interface GameContextType {
  state: GameState
  dispatch: React.Dispatch<GameAction>
  
  // 便捷方法
  updatePosture: (analysis: PostureAnalysis) => void
  startFocusSession: () => void
  endFocusSession: () => void
  growPlant: (type: string, count?: number) => void
  updateSettings: (settings: Partial<GameState['settings']>) => void
  
  // 计算属性
  getFocusTimeFormatted: () => string
  getCurrentStreakFormatted: () => string
  shouldTriggerGrowth: () => boolean
}

// 创建上下文
const GameContext = createContext<GameContextType | undefined>(undefined)

// Provider组件
export const GameProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(gameReducer, initialState)
  
  const updatePosture = useCallback((analysis: PostureAnalysis) => {
    dispatch({ type: 'UPDATE_POSTURE', payload: analysis })
  }, [])
  
  const startFocusSession = useCallback(() => {
    dispatch({ type: 'START_FOCUS_SESSION' })
  }, [])
  
  const endFocusSession = useCallback(() => {
    dispatch({ type: 'END_FOCUS_SESSION' })
  }, [])
  
  const growPlant = useCallback((type: string, count = 1) => {
    dispatch({ type: 'GROW_PLANT', payload: { type, count } })
  }, [])
  
  const updateSettings = useCallback((settings: Partial<GameState['settings']>) => {
    dispatch({ type: 'UPDATE_SETTINGS', payload: settings })
  }, [])
  
  const getFocusTimeFormatted = useCallback((): string => {
    const minutes = Math.floor(state.dailyStats.totalFocusTime / 60000)
    const seconds = Math.floor((state.dailyStats.totalFocusTime % 60000) / 1000)
    return `${minutes}:${seconds.toString().padStart(2, '0')}`
  }, [state.dailyStats.totalFocusTime])
  
  const getCurrentStreakFormatted = useCallback((): string => {
    const minutes = Math.floor(state.currentStreak / 60)
    const seconds = Math.floor(state.currentStreak % 60)
    return `${minutes}:${seconds.toString().padStart(2, '0')}`
  }, [state.currentStreak])
  
  const shouldTriggerGrowth = useCallback((): boolean => {
    return state.isPostureGood && 
           state.currentFocusScore >= state.settings.focusThreshold &&
           state.currentStreak >= 30 // 连续专注30秒后触发生长
  }, [state.isPostureGood, state.currentFocusScore, state.settings.focusThreshold, state.currentStreak])
  
  const contextValue: GameContextType = {
    state,
    dispatch,
    updatePosture,
    startFocusSession,
    endFocusSession,
    growPlant,
    updateSettings,
    getFocusTimeFormatted,
    getCurrentStreakFormatted,
    shouldTriggerGrowth
  }
  
  return (
    <GameContext.Provider value={contextValue}>
      {children}
    </GameContext.Provider>
  )
}

// Hook for using the context
export const useGame = (): GameContextType => {
  const context = useContext(GameContext)
  if (context === undefined) {
    throw new Error('useGame must be used within a GameProvider')
  }
  return context
} 