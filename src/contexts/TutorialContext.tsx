import React, { createContext, useContext, useReducer, useCallback, useEffect, ReactNode } from 'react'
import { TutorialState, TutorialAction, TutorialStep } from '../types/tutorial'
import { TutorialProgressService } from '../services/TutorialProgressService'

// 初始状态
const initialState: TutorialState = {
  isActive: false,
  currentStepIndex: 0,
  steps: [],
  completedSteps: [],
  isFirstTimeUser: true,
  tutorialProgress: {
    cameraSetup: false,
    firstPlanting: false,
    basicOperations: false,
    completed: false
  }
}

// Reducer函数
function tutorialReducer(state: TutorialState, action: TutorialAction): TutorialState {
  switch (action.type) {
    case 'START_TUTORIAL':
      return {
        ...state,
        isActive: true,
        currentStepIndex: 0,
        steps: action.payload.steps
      }
    
    case 'NEXT_STEP':
      const nextIndex = Math.min(state.currentStepIndex + 1, state.steps.length - 1)
      return {
        ...state,
        currentStepIndex: nextIndex
      }
    
    case 'PREV_STEP':
      const prevIndex = Math.max(state.currentStepIndex - 1, 0)
      return {
        ...state,
        currentStepIndex: prevIndex
      }
    
    case 'SKIP_STEP':
      const skipIndex = Math.min(state.currentStepIndex + 1, state.steps.length - 1)
      return {
        ...state,
        currentStepIndex: skipIndex
      }
    
    case 'SET_STEP':
      const setIndex = Math.max(0, Math.min(action.payload, state.steps.length - 1))
      return {
        ...state,
        currentStepIndex: setIndex
      }
    
    case 'COMPLETE_STEP':
      return {
        ...state,
        completedSteps: [...state.completedSteps, action.payload]
      }
    
    case 'UPDATE_PROGRESS':
      return {
        ...state,
        tutorialProgress: {
          ...state.tutorialProgress,
          ...action.payload
        }
      }
    
    case 'FINISH_TUTORIAL':
      return {
        ...state,
        isActive: false,
        currentStepIndex: 0,
        isFirstTimeUser: false,
        tutorialProgress: {
          ...state.tutorialProgress,
          completed: true
        }
      }
    
    case 'RESET_TUTORIAL':
      return {
        ...initialState,
        isFirstTimeUser: true
      }
    
    default:
      return state
  }
}

// Context类型
interface TutorialContextType {
  state: TutorialState
  dispatch: React.Dispatch<TutorialAction>
  
  // 便捷方法
  startTutorial: (steps: TutorialStep[]) => void
  nextStep: () => void
  prevStep: () => void
  skipStep: () => void
  finishTutorial: () => void
  setStep: (index: number) => void
  completeStep: (stepId: string) => void
  updateProgress: (progress: Partial<TutorialState['tutorialProgress']>) => void
  resetTutorial: () => void
  
  // 计算属性
  getCurrentStep: () => TutorialStep | null
  isLastStep: () => boolean
  isFirstStep: () => boolean
  getProgress: () => number
  
  // 进度服务
  progressService: TutorialProgressService
}

// 创建Context
const TutorialContext = createContext<TutorialContextType | undefined>(undefined)

// 本地存储键
const TUTORIAL_STORAGE_KEY = 'selfgame_tutorial_progress'

// Provider组件
export const TutorialProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(tutorialReducer, initialState)
  const [progressService] = React.useState(() => new TutorialProgressService())

  // 从本地存储加载进度
  useEffect(() => {
    try {
      const savedProgress = localStorage.getItem(TUTORIAL_STORAGE_KEY)
      if (savedProgress) {
        const progress = JSON.parse(savedProgress)
        dispatch({ type: 'UPDATE_PROGRESS', payload: progress })
        
        // 如果教程已完成，设置为非首次用户
        if (progress.completed) {
          dispatch({ type: 'FINISH_TUTORIAL' })
        }
      }
    } catch (error) {
      console.warn('Failed to load tutorial progress:', error)
    }
  }, [])

  // 保存进度到本地存储
  useEffect(() => {
    try {
      localStorage.setItem(TUTORIAL_STORAGE_KEY, JSON.stringify(state.tutorialProgress))
    } catch (error) {
      console.warn('Failed to save tutorial progress:', error)
    }
  }, [state.tutorialProgress])

  // 便捷方法
  const startTutorial = useCallback((steps: TutorialStep[]) => {
    dispatch({ type: 'START_TUTORIAL', payload: { steps } })
    // 开始会话追踪
    progressService.startSession()
  }, [progressService])

  const nextStep = useCallback(() => {
    dispatch({ type: 'NEXT_STEP' })
  }, [])

  const prevStep = useCallback(() => {
    dispatch({ type: 'PREV_STEP' })
  }, [])

  const skipStep = useCallback(() => {
    dispatch({ type: 'SKIP_STEP' })
    // 记录跳过操作
    progressService.recordSkip()
  }, [progressService])

  const finishTutorial = useCallback(() => {
    dispatch({ type: 'FINISH_TUTORIAL' })
    // 结束会话追踪
    progressService.endSession()
  }, [progressService])

  const setStep = useCallback((index: number) => {
    dispatch({ type: 'SET_STEP', payload: index })
  }, [])

  const completeStep = useCallback((stepId: string) => {
    dispatch({ type: 'COMPLETE_STEP', payload: stepId })
  }, [])

  const updateProgress = useCallback((progress: Partial<TutorialState['tutorialProgress']>) => {
    dispatch({ type: 'UPDATE_PROGRESS', payload: progress })
  }, [])

  const resetTutorial = useCallback(() => {
    dispatch({ type: 'RESET_TUTORIAL' })
  }, [])

  // 计算属性
  const getCurrentStep = useCallback((): TutorialStep | null => {
    return state.steps[state.currentStepIndex] || null
  }, [state.steps, state.currentStepIndex])

  const isLastStep = useCallback((): boolean => {
    return state.currentStepIndex >= state.steps.length - 1
  }, [state.currentStepIndex, state.steps.length])

  const isFirstStep = useCallback((): boolean => {
    return state.currentStepIndex === 0
  }, [state.currentStepIndex])

  const getProgress = useCallback((): number => {
    if (state.steps.length === 0) return 0
    return ((state.currentStepIndex + 1) / state.steps.length) * 100
  }, [state.currentStepIndex, state.steps.length])

  const contextValue: TutorialContextType = {
    state,
    dispatch,
    startTutorial,
    nextStep,
    prevStep,
    skipStep,
    finishTutorial,
    setStep,
    completeStep,
    updateProgress,
    resetTutorial,
    getCurrentStep,
    isLastStep,
    isFirstStep,
    getProgress,
    progressService
  }

  return (
    <TutorialContext.Provider value={contextValue}>
      {children}
    </TutorialContext.Provider>
  )
}

// Hook
export const useTutorial = (): TutorialContextType => {
  const context = useContext(TutorialContext)
  if (context === undefined) {
    throw new Error('useTutorial must be used within a TutorialProvider')
  }
  return context
} 