import { EnhancedFarmManager } from '../managers/EnhancedFarmManager'
import { FarmSystemIntegrator } from '../adapters/FarmSystemIntegrator'
import { CropType, CropStage } from '../types/crop'
import { SelfDisciplineType } from '../data/cropSpecifications'

/**
 * 农场系统集成测试
 * 验证新旧农场管理系统的集成效果
 */
export class FarmIntegrationTest {
  private gameStateManager: any // 简化测试，使用mock对象
  private enhancedFarmManager: EnhancedFarmManager
  private integrator: FarmSystemIntegrator
  private testResults: Map<string, boolean> = new Map()
  
  constructor() {
    console.log('🧪 初始化农场系统集成测试...')
    
    // 创建mock GameStateManager
    this.gameStateManager = {
      getGameState: () => ({
        farmGrid: {
          width: 8,
          height: 8,
          plots: Array(8).fill(null).map(() => Array(8).fill(null))
        },
        crops: new Map(),
        level: 1,
        experience: 0,
        resources: {},
        totalCropsHarvested: 0
      }),
      plantCrop: () => true,
      harvestCrop: () => ({ success: true, crop: null }),
      updateFocusState: () => {},
      getGameStats: () => ({}),
      on: () => {}
    }
    
    // 初始化管理器
    this.enhancedFarmManager = new EnhancedFarmManager(8, 8)
    this.integrator = new FarmSystemIntegrator(
      this.gameStateManager, 
      this.enhancedFarmManager
    )
    
    this.setupEventListeners()
  }
  
  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    this.integrator.on('crop_planted_enhanced', (data) => {
      console.log('✅ 增强作物种植事件:', data.crop.type)
    })
    
    this.integrator.on('traditional_crop_planted', (data) => {
      console.log('✅ 传统作物种植事件:', data.crop.type)
    })
    
    this.integrator.on('crop_harvested_enhanced', (data) => {
      console.log('✅ 增强作物收获事件:', data.rewards.totalExperience, '经验')
    })
    
    this.integrator.on('behavior_detected', (data) => {
      console.log('✅ 行为检测事件:', data)
    })
    
    this.integrator.on('enhanced_mode_changed', (data) => {
      console.log('✅ 增强模式切换:', data.enabled ? '开启' : '关闭')
    })
    
    this.integrator.on('game_state_update_needed', (data) => {
      console.log('✅ 游戏状态更新需求:', data.type)
    })
  }
  
  /**
   * 运行所有测试
   */
  async runAllTests(): Promise<Map<string, boolean>> {
    console.log('\n🚀 开始运行农场系统集成测试...\n')
    
    try {
      // 启动集成器
      this.integrator.start()
      
      // 基础功能测试
      await this.testTraditionalCropPlanting()
      await this.testNewCropTypePlanting()
      await this.testCropRouting()
      
      // 增强功能测试
      await this.testBehaviorSessionManagement()
      await this.testEnhancedModeToggle()
      await this.testStateSync()
      
      // 集成功能测试
      await this.testMixedCropManagement()
      await this.testIntegratorState()
      
      // 清理
      this.integrator.stop()
      
    } catch (error) {
      console.error('❌ 测试过程中出现错误:', error)
      this.testResults.set('error_handling', false)
    }
    
    this.printTestResults()
    return this.testResults
  }
  
  /**
   * 测试传统作物种植
   */
  private async testTraditionalCropPlanting(): Promise<void> {
    console.log('📝 测试传统作物种植...')
    
    try {
      // 种植知识花（传统作物）
      const success = await this.integrator.plantCrop(2, 2, CropType.KNOWLEDGE_FLOWER)
      
      if (success) {
        const crop = this.integrator.getCropAt(2, 2)
        const isCorrectType = crop?.type === CropType.KNOWLEDGE_FLOWER
        const isCorrectStage = crop?.stage === CropStage.SEED
        
        this.testResults.set('traditional_crop_planting', isCorrectType && isCorrectStage)
        console.log(`  ✅ 传统作物种植: ${isCorrectType && isCorrectStage ? '成功' : '失败'}`)
      } else {
        this.testResults.set('traditional_crop_planting', false)
        console.log('  ❌ 传统作物种植失败')
      }
         } catch (error) {
       console.log('  ❌ 传统作物种植异常:', (error as Error).message)
       this.testResults.set('traditional_crop_planting', false)
     }
  }
  
  /**
   * 测试新作物类型种植
   */
  private async testNewCropTypePlanting(): Promise<void> {
    console.log('📝 测试新作物类型种植...')
    
    try {
      // 首先解锁新作物类型
      this.integrator.unlockCropType(CropType.FOCUS_FLOWER)
      
      // 种植专注花（新作物）
      const success = await this.integrator.plantCrop(3, 3, CropType.FOCUS_FLOWER)
      
      if (success) {
        const crop = this.integrator.getCropAt(3, 3)
        const isCorrectType = crop?.type === CropType.FOCUS_FLOWER
        const isCorrectStage = crop?.stage === CropStage.SEED
        
        this.testResults.set('new_crop_planting', isCorrectType && isCorrectStage)
        console.log(`  ✅ 新作物种植: ${isCorrectType && isCorrectStage ? '成功' : '失败'}`)
      } else {
        this.testResults.set('new_crop_planting', false)
        console.log('  ❌ 新作物种植失败')
      }
         } catch (error) {
       console.log('  ❌ 新作物种植异常:', (error as Error).message)
       this.testResults.set('new_crop_planting', false)
     }
   }
   
   /**
    * 测试作物路由逻辑
    */
   private async testCropRouting(): Promise<void> {
     console.log('📝 测试作物路由逻辑...')
     
     try {
       const unlockedTypes = this.integrator.getUnlockedCropTypes()
       const hasTraditional = unlockedTypes.includes(CropType.KNOWLEDGE_FLOWER)
       const hasNew = unlockedTypes.includes(CropType.FOCUS_FLOWER)
       
       this.testResults.set('crop_routing', hasTraditional && hasNew)
       console.log(`  ✅ 作物路由: ${hasTraditional && hasNew ? '成功' : '失败'}`)
       console.log(`    - 传统作物: ${hasTraditional}`)
       console.log(`    - 新作物: ${hasNew}`)
     } catch (error) {
       console.log('  ❌ 作物路由测试异常:', (error as Error).message)
       this.testResults.set('crop_routing', false)
     }
   }
   
   /**
    * 测试行为会话管理
    */
   private async testBehaviorSessionManagement(): Promise<void> {
     console.log('📝 测试行为会话管理...')
     
     try {
       // 开始深度专注行为会话
       const sessionId = this.integrator.startBehaviorSession(SelfDisciplineType.DEEP_FOCUS)
       
       // 模拟专注数据
       const focusData = [
         { score: 85, duration: 60000, timestamp: Date.now() },
         { score: 92, duration: 60000, timestamp: Date.now() + 60000 },
         { score: 78, duration: 60000, timestamp: Date.now() + 120000 }
       ]
       
       // 结束会话
       this.integrator.endBehaviorSession(sessionId, focusData)
       
       this.testResults.set('behavior_session', true)
       console.log('  ✅ 行为会话管理: 成功')
     } catch (error) {
       console.log('  ❌ 行为会话管理异常:', (error as Error).message)
       this.testResults.set('behavior_session', false)
     }
   }
   
   /**
    * 测试增强模式切换
    */
   private async testEnhancedModeToggle(): Promise<void> {
     console.log('📝 测试增强模式切换...')
     
     try {
       // 关闭增强模式
       this.integrator.setEnhancedMode(false)
       
       // 重新开启增强模式
       this.integrator.setEnhancedMode(true)
       
       const state = this.integrator.getIntegratorState()
       this.testResults.set('enhanced_mode_toggle', state.isEnhancedModeEnabled)
       console.log(`  ✅ 增强模式切换: ${state.isEnhancedModeEnabled ? '成功' : '失败'}`)
     } catch (error) {
       console.log('  ❌ 增强模式切换异常:', (error as Error).message)
       this.testResults.set('enhanced_mode_toggle', false)
     }
   }
   
   /**
    * 测试状态同步
    */
   private async testStateSync(): Promise<void> {
     console.log('📝 测试状态同步...')
     
     try {
       // 等待一个同步周期
       await new Promise(resolve => setTimeout(resolve, 6000))
       
       const state = this.integrator.getIntegratorState()
       const isSyncing = state.isSyncing
       const hasLastSyncTime = state.lastSyncTime > 0
       
       this.testResults.set('state_sync', isSyncing && hasLastSyncTime)
       console.log(`  ✅ 状态同步: ${isSyncing && hasLastSyncTime ? '成功' : '失败'}`)
     } catch (error) {
       console.log('  ❌ 状态同步测试异常:', (error as Error).message)
       this.testResults.set('state_sync', false)
     }
   }
   
   /**
    * 测试混合作物管理
    */
   private async testMixedCropManagement(): Promise<void> {
     console.log('📝 测试混合作物管理...')
     
     try {
       // 更新作物生长
       this.integrator.updateCropGrowth(30000, 85) // 30秒，85分专注度
       
       // 获取农场网格
       const farmGrid = this.integrator.getFarmGrid()
       
       // 检查网格是否正确
       const hasCorrectSize = farmGrid.width === 8 && farmGrid.height === 8
       const hasPlots = Array.isArray(farmGrid.plots)
       
       this.testResults.set('mixed_crop_management', hasCorrectSize && hasPlots)
       console.log(`  ✅ 混合作物管理: ${hasCorrectSize && hasPlots ? '成功' : '失败'}`)
     } catch (error) {
       console.log('  ❌ 混合作物管理异常:', (error as Error).message)
       this.testResults.set('mixed_crop_management', false)
     }
   }
   
   /**
    * 测试集成器状态
    */
   private async testIntegratorState(): Promise<void> {
     console.log('📝 测试集成器状态...')
     
     try {
       const state = this.integrator.getIntegratorState()
       const farmStats = this.integrator.getFarmStats()
       
       const hasValidState = typeof state.isEnhancedModeEnabled === 'boolean'
       const hasValidStats = farmStats !== null && typeof farmStats === 'object'
       
       this.testResults.set('integrator_state', hasValidState && hasValidStats)
       console.log(`  ✅ 集成器状态: ${hasValidState && hasValidStats ? '成功' : '失败'}`)
       
       // 输出状态信息
       console.log('    状态信息:', {
         增强模式: state.isEnhancedModeEnabled,
         正在同步: state.isSyncing,
         解锁作物数量: state.unlockedCropTypes.length
       })
     } catch (error) {
       console.log('  ❌ 集成器状态测试异常:', (error as Error).message)
       this.testResults.set('integrator_state', false)
     }
  }
  
  /**
   * 打印测试结果
   */
  private printTestResults(): void {
    console.log('\n📊 测试结果汇总:')
    console.log('='.repeat(50))
    
    let totalTests = 0
    let passedTests = 0
    
    this.testResults.forEach((passed, testName) => {
      totalTests++
      if (passed) passedTests++
      
      const status = passed ? '✅ 通过' : '❌ 失败'
      const formattedName = testName.replace(/_/g, ' ').toUpperCase()
      console.log(`${status} ${formattedName}`)
    })
    
    console.log('='.repeat(50))
    console.log(`总计: ${passedTests}/${totalTests} 测试通过`)
    console.log(`成功率: ${((passedTests / totalTests) * 100).toFixed(1)}%`)
    
    if (passedTests === totalTests) {
      console.log('\n🎉 所有测试通过！农场系统集成成功！')
    } else {
      console.log('\n⚠️  部分测试失败，需要进一步调试')
    }
  }
}

// 导出测试运行函数
export async function runFarmIntegrationTests(): Promise<Map<string, boolean>> {
  const test = new FarmIntegrationTest()
  return await test.runAllTests()
}

// 可以在浏览器控制台中运行: runFarmIntegrationTests() 