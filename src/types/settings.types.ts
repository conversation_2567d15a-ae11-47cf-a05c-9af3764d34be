// 设置系统类型定义
export interface UserSettings {
  general: GeneralSettings
  camera: CameraSettings
  audio: AudioSettings
  focus: FocusSettings
  privacy: PrivacySettings
  notifications: NotificationSettings
  appearance: AppearanceSettings
  data: DataSettings
}

// 通用设置
export interface GeneralSettings {
  language: string
  timezone: string
  autoStart: boolean
  minimizeToTray: boolean
  confirmBeforeExit: boolean
  autoSaveInterval: number // 秒
  performanceMode: PerformanceMode
}

export enum PerformanceMode {
  HIGH = 'high',
  BALANCED = 'balanced',
  POWER_SAVER = 'power_saver'
}

// 摄像头设置
export interface CameraSettings {
  enabled: boolean
  deviceId: string
  resolution: CameraResolution
  fps: number
  brightness: number
  contrast: number
  saturation: number
  flipHorizontal: boolean
  flipVertical: boolean
  exposureMode: ExposureMode
  focusMode: FocusMode
  nightVision: boolean
  motionDetection: boolean
}

export interface CameraResolution {
  width: number
  height: number
  label: string
}

export enum ExposureMode {
  AUTO = 'auto',
  MANUAL = 'manual'
}

export enum FocusMode {
  AUTO = 'auto',
  MANUAL = 'manual',
  CONTINUOUS = 'continuous'
}

// 音频设置
export interface AudioSettings {
  enabled: boolean
  volume: VolumeSettings
  effects: AudioEffects
  notifications: AudioNotifications
  devices: AudioDevices
  quality: AudioQuality
}

export interface VolumeSettings {
  master: number
  effects: number
  background: number
  voice: number
  notifications: number
  muted: boolean
}

export interface AudioEffects {
  enabled: boolean
  reverb: number
  bass: number
  treble: number
  spatialAudio: boolean
  noiseReduction: boolean
}

export interface AudioNotifications {
  playOnFocusLoss: boolean
  playOnAchievement: boolean
  playOnReminder: boolean
  playOnError: boolean
  customSounds: { [key: string]: string }
}

export interface AudioDevices {
  outputDeviceId: string
  inputDeviceId: string
  outputDevices: MediaDeviceInfo[]
  inputDevices: MediaDeviceInfo[]
}

export enum AudioQuality {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  LOSSLESS = 'lossless'
}

// 专注设置
export interface FocusSettings {
  enabled: boolean
  sensitivity: SensitivitySettings
  targets: FocusTargets
  detection: DetectionSettings
  breaks: BreakSettings
  rewards: RewardSettings
}

export interface SensitivitySettings {
  movement: number // 0-100
  eyeTracking: number // 0-100
  postureDetection: number // 0-100
  faceDetection: number // 0-100
  handGestures: number // 0-100
}

export interface FocusTargets {
  dailyGoals: DailyGoals
  customGoals: CustomGoal[]
  activeGoalId: string | null
}

export interface DailyGoals {
  focusTime: number // 分钟
  sessionCount: number
  breakFrequency: number // 分钟
  productivityScore: number // 0-100
}

export interface CustomGoal {
  id: string
  name: string
  description: string
  duration: number // 分钟
  priority: Priority
  category: GoalCategory
  reminders: boolean
  rewards: boolean
  createdAt: Date
  updatedAt: Date
}

export enum Priority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

export enum GoalCategory {
  WORK = 'work',
  STUDY = 'study',
  EXERCISE = 'exercise',
  MEDITATION = 'meditation',
  READING = 'reading',
  CREATIVE = 'creative',
  OTHER = 'other'
}

export interface DetectionSettings {
  enableFaceDetection: boolean
  enablePostureDetection: boolean
  enableEyeTracking: boolean
  enableHandGestures: boolean
  confidenceThreshold: number // 0-100
  detectionInterval: number // 毫秒
  smoothingFactor: number // 0-1
}

export interface BreakSettings {
  enabled: boolean
  frequency: number // 分钟
  duration: number // 分钟
  type: BreakType
  reminders: boolean
  forced: boolean
  activities: BreakActivity[]
}

export enum BreakType {
  MICRO = 'micro', // 1-2分钟
  SHORT = 'short', // 5-10分钟
  LONG = 'long', // 15-30分钟
  CUSTOM = 'custom'
}

export interface BreakActivity {
  id: string
  name: string
  description: string
  duration: number
  category: string
  instructions: string[]
  enabled: boolean
}

export interface RewardSettings {
  enabled: boolean
  pointsPerMinute: number
  bonusMultiplier: number
  achievementRewards: boolean
  levelUpRewards: boolean
  streakRewards: boolean
  customRewards: CustomReward[]
}

export interface CustomReward {
  id: string
  name: string
  description: string
  cost: number // 积分
  category: string
  unlocked: boolean
  claimed: boolean
}

// 隐私设置
export interface PrivacySettings {
  dataCollection: DataCollectionSettings
  sharing: DataSharingSettings
  retention: DataRetentionSettings
  consent: ConsentSettings
  anonymization: boolean
  encryption: EncryptionSettings
}

export interface DataCollectionSettings {
  behaviorAnalytics: boolean
  performanceMetrics: boolean
  errorReporting: boolean
  usageStatistics: boolean
  cameraData: boolean
  audioData: boolean
  locationData: boolean
}

export interface DataSharingSettings {
  analytics: boolean
  research: boolean
  marketing: boolean
  thirdParty: boolean
  aggregatedData: boolean
  anonymizedData: boolean
}

export interface DataRetentionSettings {
  behaviorData: number // 天
  performanceData: number // 天
  errorLogs: number // 天
  userGeneratedContent: number // 天
  autoDelete: boolean
}

export interface ConsentSettings {
  analytics: boolean
  marketing: boolean
  research: boolean
  thirdParty: boolean
  lastUpdated: Date
  version: string
}

export interface EncryptionSettings {
  enabled: boolean
  level: EncryptionLevel
  keyRotation: boolean
  localEncryption: boolean
}

export enum EncryptionLevel {
  BASIC = 'basic',
  STANDARD = 'standard',
  ADVANCED = 'advanced'
}

// 通知设置
export interface NotificationSettings {
  enabled: boolean
  types: NotificationTypes
  delivery: DeliverySettings
  schedule: ScheduleSettings
  priority: PrioritySettings
}

export interface NotificationTypes {
  achievements: boolean
  reminders: boolean
  breaks: boolean
  goals: boolean
  system: boolean
  social: boolean
  marketing: boolean
}

export interface DeliverySettings {
  push: boolean
  email: boolean
  sms: boolean
  desktop: boolean
  sound: boolean
  vibration: boolean
}

export interface ScheduleSettings {
  quietHoursEnabled: boolean
  quietStart: string // HH:MM
  quietEnd: string // HH:MM
  weekendsOnly: boolean
  workdaysOnly: boolean
  customSchedule: TimeSlot[]
}

export interface TimeSlot {
  id: string
  name: string
  start: string // HH:MM
  end: string // HH:MM
  days: number[] // 0-6 (周日-周六)
  enabled: boolean
}

export interface PrioritySettings {
  high: boolean
  medium: boolean
  low: boolean
  filterByPriority: boolean
}

// 外观设置
export interface AppearanceSettings {
  theme: ThemeSettings
  layout: LayoutSettings
  accessibility: AccessibilitySettings
  customization: CustomizationSettings
}

export interface ThemeSettings {
  mode: ThemeMode
  primary: string
  secondary: string
  accent: string
  background: string
  surface: string
  darkMode: boolean
  autoTheme: boolean
  customThemes: CustomTheme[]
}

export enum ThemeMode {
  LIGHT = 'light',
  DARK = 'dark',
  AUTO = 'auto',
  CUSTOM = 'custom'
}

export interface CustomTheme {
  id: string
  name: string
  colors: { [key: string]: string }
  fonts: { [key: string]: string }
  spacing: { [key: string]: number }
  borderRadius: { [key: string]: number }
}

export interface LayoutSettings {
  density: LayoutDensity
  sidebar: SidebarSettings
  toolbar: ToolbarSettings
  statusBar: boolean
  fullscreen: boolean
  compactMode: boolean
}

export enum LayoutDensity {
  COMFORTABLE = 'comfortable',
  COMPACT = 'compact',
  CONDENSED = 'condensed'
}

export interface SidebarSettings {
  visible: boolean
  collapsed: boolean
  position: SidebarPosition
  width: number
}

export enum SidebarPosition {
  LEFT = 'left',
  RIGHT = 'right'
}

export interface ToolbarSettings {
  visible: boolean
  position: ToolbarPosition
  items: string[]
  customizable: boolean
}

export enum ToolbarPosition {
  TOP = 'top',
  BOTTOM = 'bottom'
}

export interface AccessibilitySettings {
  enabled: boolean
  highContrast: boolean
  largeText: boolean
  screenReader: boolean
  keyboardNavigation: boolean
  reducedMotion: boolean
  colorBlindSupport: boolean
  fontSize: number
  lineHeight: number
}

export interface CustomizationSettings {
  wallpaper: string
  animations: boolean
  transitions: boolean
  effects: boolean
  shortcuts: { [key: string]: string }
  widgets: WidgetSettings[]
}

export interface WidgetSettings {
  id: string
  name: string
  enabled: boolean
  position: { x: number; y: number }
  size: { width: number; height: number }
  config: { [key: string]: any }
}

// 数据设置
export interface DataSettings {
  storage: StorageSettings
  backup: BackupSettings
  sync: SyncSettings
  export: ExportSettings
  import: ImportSettings
  cache: CacheSettings
}

export interface StorageSettings {
  location: string
  maxSize: number // MB
  compression: boolean
  autoCleanup: boolean
  cleanupThreshold: number // MB
}

export interface BackupSettings {
  enabled: boolean
  frequency: BackupFrequency
  retention: number // 天
  location: BackupLocation
  encryption: boolean
  compression: boolean
  incremental: boolean
}

export enum BackupFrequency {
  HOURLY = 'hourly',
  DAILY = 'daily',
  WEEKLY = 'weekly',
  MONTHLY = 'monthly'
}

export enum BackupLocation {
  LOCAL = 'local',
  CLOUD = 'cloud',
  BOTH = 'both'
}

export interface SyncSettings {
  enabled: boolean
  frequency: SyncFrequency
  conflicts: ConflictResolution
  bandwidth: BandwidthSettings
  devices: DeviceSettings[]
}

export enum SyncFrequency {
  REALTIME = 'realtime',
  EVERY_MINUTE = 'every_minute',
  EVERY_HOUR = 'every_hour',
  MANUAL = 'manual'
}

export enum ConflictResolution {
  SERVER_WINS = 'server_wins',
  CLIENT_WINS = 'client_wins',
  MERGE = 'merge',
  ASK_USER = 'ask_user'
}

export interface BandwidthSettings {
  maxUpload: number // KB/s
  maxDownload: number // KB/s
  limitEnabled: boolean
}

export interface DeviceSettings {
  id: string
  name: string
  type: DeviceType
  lastSync: Date
  enabled: boolean
}

export enum DeviceType {
  DESKTOP = 'desktop',
  MOBILE = 'mobile',
  TABLET = 'tablet',
  WEB = 'web'
}

export interface ExportSettings {
  format: ExportFormat
  includePersonalData: boolean
  includeAnalytics: boolean
  includeSettings: boolean
  compression: boolean
  encryption: boolean
}

export enum ExportFormat {
  JSON = 'json',
  CSV = 'csv',
  XML = 'xml',
  PDF = 'pdf'
}

export interface ImportSettings {
  validateData: boolean
  mergeStrategy: MergeStrategy
  backupBeforeImport: boolean
  skipErrors: boolean
}

export enum MergeStrategy {
  REPLACE = 'replace',
  MERGE = 'merge',
  SKIP = 'skip'
}

export interface CacheSettings {
  enabled: boolean
  maxSize: number // MB
  maxAge: number // 小时
  cleanupFrequency: number // 小时
  preload: boolean
}

// 设置操作相关类型
export interface SettingsAction {
  type: SettingsActionType
  payload?: any
  category?: SettingsCategory
  key?: string
}

export enum SettingsActionType {
  LOAD = 'LOAD',
  SAVE = 'SAVE',
  UPDATE = 'UPDATE',
  RESET = 'RESET',
  VALIDATE = 'VALIDATE',
  EXPORT = 'EXPORT',
  IMPORT = 'IMPORT'
}

export enum SettingsCategory {
  GENERAL = 'general',
  CAMERA = 'camera',
  AUDIO = 'audio',
  FOCUS = 'focus',
  PRIVACY = 'privacy',
  NOTIFICATIONS = 'notifications',
  APPEARANCE = 'appearance',
  DATA = 'data'
}

// 设置验证
export interface SettingsValidation {
  isValid: boolean
  errors: ValidationError[]
  warnings: ValidationWarning[]
}

export interface ValidationError {
  category: SettingsCategory
  key: string
  message: string
  severity: ValidationSeverity
}

export interface ValidationWarning {
  category: SettingsCategory
  key: string
  message: string
  suggestion?: string
}

export enum ValidationSeverity {
  ERROR = 'error',
  WARNING = 'warning',
  INFO = 'info'
}

// 设置事件
export interface SettingsEvent {
  type: SettingsEventType
  category: SettingsCategory
  key: string
  oldValue: any
  newValue: any
  timestamp: Date
}

export enum SettingsEventType {
  CHANGED = 'changed',
  LOADED = 'loaded',
  SAVED = 'saved',
  RESET = 'reset',
  VALIDATED = 'validated'
} 