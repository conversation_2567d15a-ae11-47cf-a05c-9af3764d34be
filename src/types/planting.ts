import { ItemRarity } from './lootbox'

// 种植状态枚举
export enum PlantingStage {
  EMPTY = 'empty',           // 空地
  PLANTED = 'planted',       // 已种植
  GROWING = 'growing',       // 生长中
  MATURE = 'mature',         // 成熟
  READY_HARVEST = 'ready'    // 可收获
}

// 道具类型
export enum ItemTool {
  SEED = 'seed',             // 种子
  FERTILIZER = 'fertilizer', // 肥料  
  WATER = 'water',           // 浇水
  PESTICIDE = 'pesticide',   // 农药
  ACCELERATOR = 'accelerator', // 生长加速剂
  ENHANCER = 'enhancer'      // 品质提升剂
}

// 种植道具
export interface PlantingItem {
  id: string
  name: string
  icon: string
  type: ItemTool
  rarity: ItemRarity
  description: string
  effects: {
    yieldBoost?: number     // 产量提升%
    qualityBoost?: number   // 品质提升概率%
    speedBoost?: number     // 生长速度提升%
    rarityBoost?: number    // 稀有度提升概率%
  }
  price: number
  unlockLevel?: number
}

// 种植地块数据
export interface PlantingSlot {
  id: string
  cropId?: string            // 作物ID
  stage: PlantingStage       // 当前阶段
  quality: ItemRarity        // 预定品质
  plantedAt: number         // 种植时间戳
  growthTime: number        // 生长总时间(毫秒)
  remainingTime: number     // 剩余时间
  appliedItems: PlantingItem[]  // 已使用的道具
  yieldMultiplier: number   // 产量倍数
  qualityBonus: number      // 品质加成
  specialEffects: string[]  // 特殊效果
}

// 收集册条目
export interface CollectionEntry {
  cropId: string
  quality: ItemRarity
  firstObtained: number     // 首次获得时间戳
  totalHarvested: number    // 总收获次数
  bestYield: number         // 最佳产量记录
  isNew: boolean           // 是否为新获得
  isRare: boolean          // 是否为稀有获得
}

// 成就类型
export interface Achievement {
  id: string
  name: string
  description: string
  icon: string
  requirements: {
    type: 'harvest_count' | 'collection_rate' | 'rare_harvest' | 'level_reach'
    target: number
    cropId?: string
    quality?: ItemRarity
  }
  rewards: {
    experience: number
    coins: number
    items?: { id: string, quantity: number }[]
  }
  isCompleted: boolean
  progress: number
}

// 农场数据 - 修正：achievements为Achievement[]而不是string[]
export interface FarmData {
  level: number
  experience: number
  coins: number
  slots: PlantingSlot[]
  inventory: { [itemId: string]: number }
  collection: { [key: string]: CollectionEntry }
  achievements: Achievement[]
  dailyStreak: number
  lastLoginDate: string
}

// 种植结果
export interface HarvestResult {
  cropId: string
  quality: ItemRarity
  quantity: number
  experience: number
  coins: number
  isNewCollection: boolean
  isRareGet: boolean
  specialEffects: string[]
}

// 作物生长配置
export interface CropGrowthConfig {
  cropId: string
  baseGrowthTime: number    // 基础生长时间(分钟)
  rarityWeights: { [key in ItemRarity]: number }  // 稀有度权重
  visualEffects: {
    growthParticles: string
    matureGlow: string
    harvestEffect: string
  }
  environmentPreferences: {
    season?: string[]
    weather?: string[]
    soilType?: string
  }
}

// 特殊事件
export interface SpecialEvent {
  id: string
  name: string
  description: string
  type: 'weather' | 'festival' | 'market' | 'mystery'
  startTime: number
  endTime: number
  effects: {
    globalYieldBoost?: number
    rarityBoost?: number
    experienceBoost?: number
    discountRate?: number
  }
  isActive: boolean
} 