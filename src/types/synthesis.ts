import { ItemRarity, LootboxItem, ItemCategory, ItemType } from './lootbox'
import { CurrencyType } from './currency'

// 合成类型
export enum SynthesisType {
  UPGRADE = 'upgrade',        // 升级合成（同类物品升级）
  COMBINE = 'combine',        // 组合合成（不同物品组合）
  TRANSFORM = 'transform',    // 转换合成（农产品->工业品）
  SPECIAL = 'special'         // 特殊合成（限时配方）
}

// 合成配方
export interface SynthesisRecipe {
  id: string
  name: string
  description: string
  type: SynthesisType
  category: ItemCategory
  icon: string
  
  // 合成材料
  materials: {
    itemId: string
    quantity: number
    rarity?: ItemRarity  // 可选：要求特定品质
  }[]
  
  // 额外消耗
  cost?: {
    currency: CurrencyType
    amount: number
  }
  
  // 产出结果
  result: {
    itemId: string
    quantity: number
    rarity: ItemRarity
    guaranteedRarity?: ItemRarity  // 保底品质
  }
  
  // 成功率配置
  successRate: {
    base: number                    // 基础成功率
    rarityBonus?: Record<ItemRarity, number>  // 材料品质加成
    bonusConditions?: {
      timeBonus?: number            // 时间段加成
      streakBonus?: number          // 连续成功加成
      equipmentBonus?: number       // 设备加成
    }
  }
  
  // 解锁条件
  unlockConditions: {
    level?: number                  // 等级要求
    achievements?: string[]         // 成就要求
    items?: string[]               // 拥有物品要求
    farmBuildings?: string[]       // 建筑要求
  }
  
  // 特殊属性
  isLimited?: boolean              // 是否限时配方
  dailyLimit?: number              // 每日合成次数限制
  globalLimit?: number             // 全局合成次数限制
  
  metadata?: {
    discoveredBy?: string          // 发现者
    discoveryDate?: number         // 发现时间
    rarity?: 'common' | 'rare' | 'epic' | 'legendary'  // 配方稀有度
  }
}

// 合成进度
export interface SynthesisProgress {
  recipeId: string
  attempts: number
  successes: number
  failures: number
  streak: number
  lastAttempt: number
  totalMaterialsUsed: Record<string, number>
  totalCostSpent: Record<CurrencyType, number>
}

// 合成结果
export interface SynthesisResult {
  recipeId: string
  success: boolean
  materialsUsed: {
    itemId: string
    quantity: number
    rarity: ItemRarity
  }[]
  costSpent?: {
    currency: CurrencyType
    amount: number
  }
  result?: {
    item: LootboxItem
    quantity: number
    actualRarity: ItemRarity
    bonusAttributes?: Record<string, number>
  }
  bonusRewards?: {
    experience: number
    currency?: Record<CurrencyType, number>
    items?: LootboxItem[]
  }
  timestamp: number
  metadata?: {
    successRate: number
    bonusApplied: string[]
    criticalSuccess?: boolean
  }
}

// 产业链节点
export interface IndustryNode {
  id: string
  name: string
  description: string
  category: ItemCategory
  level: number
  
  // 建筑要求
  requiredBuildings: {
    buildingId: string
    level: number
  }[]
  
  // 可生产的物品
  products: {
    itemId: string
    productionTime: number      // 生产时间（秒）
    inputMaterials: {
      itemId: string
      quantity: number
    }[]
    outputQuantity: number
    qualityRange: [ItemRarity, ItemRarity]  // 品质范围
  }[]
  
  // 解锁条件
  unlockConditions: {
    level: number
    previousNodes?: string[]
    items?: string[]
    achievements?: string[]
  }
}

// 产业链配置
export interface IndustryChain {
  id: string
  name: string
  description: string
  category: ItemCategory
  icon: string
  
  // 产业链节点
  nodes: IndustryNode[]
  
  // 最终目标
  finalGoals: {
    buildingId: string
    description: string
    rewards: {
      currency: Record<CurrencyType, number>
      items: LootboxItem[]
      achievements: string[]
      unlocks: string[]  // 解锁新功能
    }
  }[]
}

// 预定义合成配方数据
export const SYNTHESIS_RECIPES: Record<string, SynthesisRecipe> = {
  // 农产品升级配方
  'wheat_upgrade': {
    id: 'wheat_upgrade',
    name: '小麦品质提升',
    description: '将普通小麦合成为高品质小麦',
    type: SynthesisType.UPGRADE,
    category: ItemCategory.AGRICULTURAL,
    icon: '🌾',
    materials: [
      { itemId: 'wheat_gray', quantity: 3 },
      { itemId: 'fertilizer', quantity: 1 }
    ],
    result: {
      itemId: 'wheat_green',
      quantity: 1,
      rarity: ItemRarity.GREEN
    },
    successRate: {
      base: 0.8,
      rarityBonus: {
        [ItemRarity.GRAY]: 0,
        [ItemRarity.GREEN]: 0.1,
        [ItemRarity.BLUE]: 0.2,
        [ItemRarity.ORANGE]: 0.3,
        [ItemRarity.GOLD]: 0.4,
        [ItemRarity.GOLD_RED]: 0.5
      }
    },
    unlockConditions: {
      level: 5
    }
  },
  
  // 农产品转工业品
  'wheat_to_flour': {
    id: 'wheat_to_flour',
    name: '小麦制粉',
    description: '将小麦加工成面粉',
    type: SynthesisType.TRANSFORM,
    category: ItemCategory.INDUSTRIAL,
    icon: '🏭',
    materials: [
      { itemId: 'wheat', quantity: 5 }
    ],
    cost: {
      currency: CurrencyType.FOCUS_COIN,
      amount: 50
    },
    result: {
      itemId: 'flour',
      quantity: 3,
      rarity: ItemRarity.GREEN
    },
    successRate: {
      base: 0.9
    },
    unlockConditions: {
      level: 10,
      farmBuildings: ['mill']
    }
  },
  
  // 工业品组合
  'machinery_assembly': {
    id: 'machinery_assembly',
    name: '机械组装',
    description: '组装各种零件制造机械',
    type: SynthesisType.COMBINE,
    category: ItemCategory.INDUSTRIAL,
    icon: '⚙️',
    materials: [
      { itemId: 'metal_component', quantity: 2 },
      { itemId: 'electronic_chip', quantity: 1 },
      { itemId: 'energy_core', quantity: 1 }
    ],
    cost: {
      currency: CurrencyType.DISCIPLINE_TOKEN,
      amount: 10
    },
    result: {
      itemId: 'production_machine',
      quantity: 1,
      rarity: ItemRarity.BLUE,
      guaranteedRarity: ItemRarity.GREEN
    },
    successRate: {
      base: 0.7,
      bonusConditions: {
        equipmentBonus: 0.2
      }
    },
    unlockConditions: {
      level: 20,
      farmBuildings: ['assembly_line'],
      achievements: ['first_machine_builder']
    },
    dailyLimit: 3
  },
  
  // 特殊限时配方
  'golden_harvest_special': {
    id: 'golden_harvest_special',
    name: '金色丰收仪式',
    description: '传说中的特殊合成，只在特定时期可用',
    type: SynthesisType.SPECIAL,
    category: ItemCategory.AGRICULTURAL,
    icon: '🏅',
    materials: [
      { itemId: 'gold_wheat', quantity: 1, rarity: ItemRarity.GOLD },
      { itemId: 'gold_corn', quantity: 1, rarity: ItemRarity.GOLD },
      { itemId: 'blessed_water', quantity: 1 },
      { itemId: 'harvest_blessing', quantity: 1 }
    ],
    cost: {
      currency: CurrencyType.GOLDEN_HARVEST,
      amount: 1
    },
    result: {
      itemId: 'legendary_harvest_crown',
      quantity: 1,
      rarity: ItemRarity.GOLD_RED
    },
    successRate: {
      base: 0.5,
      rarityBonus: {
        [ItemRarity.GRAY]: 0,
        [ItemRarity.GREEN]: 0.05,
        [ItemRarity.BLUE]: 0.1,
        [ItemRarity.ORANGE]: 0.15,
        [ItemRarity.GOLD]: 0.2,
        [ItemRarity.GOLD_RED]: 0.3
      }
    },
    unlockConditions: {
      level: 50,
      achievements: ['harvest_master', 'golden_farmer'],
      items: ['harvest_altar']
    },
    isLimited: true,
    globalLimit: 100,
    metadata: {
      rarity: 'legendary'
    }
  }
}

// 产业链配置
export const INDUSTRY_CHAINS: Record<string, IndustryChain> = {
  'agricultural_chain': {
    id: 'agricultural_chain',
    name: '农业产业链',
    description: '从种地到农产品加工的完整产业链',
    category: ItemCategory.AGRICULTURAL,
    icon: '🚜',
    nodes: [
      {
        id: 'seed_cultivation',
        name: '种子培育',
        description: '培育高品质种子',
        category: ItemCategory.AGRICULTURAL,
        level: 1,
        requiredBuildings: [
          { buildingId: 'greenhouse', level: 1 }
        ],
        products: [
          {
            itemId: 'premium_seeds',
            productionTime: 3600,
            inputMaterials: [
              { itemId: 'basic_seeds', quantity: 3 },
              { itemId: 'fertilizer', quantity: 1 }
            ],
            outputQuantity: 1,
            qualityRange: [ItemRarity.GREEN, ItemRarity.BLUE]
          }
        ],
        unlockConditions: {
          level: 10
        }
      },
      {
        id: 'crop_processing',
        name: '作物加工',
        description: '将作物加工成食品',
        category: ItemCategory.AGRICULTURAL,
        level: 2,
        requiredBuildings: [
          { buildingId: 'processing_plant', level: 1 }
        ],
        products: [
          {
            itemId: 'processed_food',
            productionTime: 7200,
            inputMaterials: [
              { itemId: 'crops', quantity: 5 },
              { itemId: 'packaging', quantity: 2 }
            ],
            outputQuantity: 3,
            qualityRange: [ItemRarity.GREEN, ItemRarity.ORANGE]
          }
        ],
        unlockConditions: {
          level: 20,
          previousNodes: ['seed_cultivation']
        }
      }
    ],
    finalGoals: [
      {
        buildingId: 'mega_farm_complex',
        description: '建造超大型农业综合体',
        rewards: {
          currency: {
            [CurrencyType.GOLDEN_HARVEST]: 10,
            [CurrencyType.FUTURES_CRYSTAL]: 100
          },
          items: [],
          achievements: ['agricultural_tycoon'],
          unlocks: ['futures_trading', 'global_market_access']
        }
      }
    ]
  },
  
  'industrial_chain': {
    id: 'industrial_chain',
    name: '工业产业链',
    description: '从小作坊到现代化工厂的工业发展',
    category: ItemCategory.INDUSTRIAL,
    icon: '🏭',
    nodes: [
      {
        id: 'workshop_production',
        name: '作坊生产',
        description: '小规模手工制作',
        category: ItemCategory.INDUSTRIAL,
        level: 1,
        requiredBuildings: [
          { buildingId: 'workshop', level: 1 }
        ],
        products: [
          {
            itemId: 'handmade_tools',
            productionTime: 1800,
            inputMaterials: [
              { itemId: 'raw_materials', quantity: 2 }
            ],
            outputQuantity: 1,
            qualityRange: [ItemRarity.GRAY, ItemRarity.GREEN]
          }
        ],
        unlockConditions: {
          level: 5
        }
      },
      {
        id: 'factory_production',
        name: '工厂生产',
        description: '现代化批量生产',
        category: ItemCategory.INDUSTRIAL,
        level: 3,
        requiredBuildings: [
          { buildingId: 'factory', level: 2 }
        ],
        products: [
          {
            itemId: 'manufactured_goods',
            productionTime: 10800,
            inputMaterials: [
              { itemId: 'components', quantity: 3 },
              { itemId: 'energy', quantity: 2 }
            ],
            outputQuantity: 5,
            qualityRange: [ItemRarity.BLUE, ItemRarity.GOLD]
          }
        ],
        unlockConditions: {
          level: 40,
          previousNodes: ['workshop_production'],
          achievements: ['industrial_pioneer']
        }
      }
    ],
    finalGoals: [
      {
        buildingId: 'industrial_empire',
        description: '建立工业帝国',
        rewards: {
          currency: {
            [CurrencyType.GOLDEN_HARVEST]: 25,
            [CurrencyType.FUTURES_CRYSTAL]: 250
          },
          items: [],
          achievements: ['industrial_emperor'],
          unlocks: ['automated_production', 'ai_factory_management']
        }
      }
    ]
  }
} 