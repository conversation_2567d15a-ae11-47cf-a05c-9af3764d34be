import { ItemRarity } from './lootbox'

// 专注代币系统
export interface FocusToken {
  id: string
  amount: number
  earnedToday: number
  totalEarned: number
  lastEarnTime: number
  dailyLimit: number
}

// 农产品道具
export interface AgriculturalItem {
  id: string
  name: string
  nameEn: string
  description: string
  rarity: ItemRarity
  category: ItemCategory
  variety: string  // 对应 CropVariety 或 LivestockVariety
  level: number
  quality: number
  
  // 生产属性
  production: {
    minDaily: number
    maxDaily: number
    currentDaily?: number
    lastHarvestTime?: number
    nextHarvestTime?: number
  }
  
  // 经济属性
  value: {
    basePrice: number
    currentPrice: number
    marketDemand: number
    priceHistory: PricePoint[]
  }
  
  // 生长属性
  growth: {
    plantedTime?: number
    growthTime: number  // 小时
    currentStage: GrowthStage
    isReady: boolean
    needsWater?: boolean
    needsFertilizer?: boolean
  }
  
  // 特殊属性
  special?: {
    weatherBonus?: number
    seasonBonus?: number
    skillBonus?: number
    isHybrid?: boolean
    parentItems?: string[]  // 合成来源
  }
  
  // 游戏属性
  sprite: string
  animation?: string
  sound?: string
  location?: {
    farmSlotId?: string
    x?: number
    y?: number
  }
}

export enum ItemCategory {
  CROP = 'crop',
  LIVESTOCK = 'livestock',
  SEED = 'seed',
  PRODUCT = 'product',
  TOOL = 'tool',
  MATERIAL = 'material'
}

export enum GrowthStage {
  SEED = 'seed',
  SPROUT = 'sprout',
  GROWING = 'growing',
  FLOWERING = 'flowering',
  MATURE = 'mature',
  READY = 'ready'
}

export interface PricePoint {
  timestamp: number
  price: number
  volume: number
}

// 农田系统
export interface FarmSlot {
  id: string
  x: number
  y: number
  isUnlocked: boolean
  isOccupied: boolean
  currentItem?: AgriculturalItem
  soilQuality: number
  moistureLevel: number
  fertilizerLevel: number
  lastWatered?: number
  lastFertilized?: number
}

export interface Farm {
  id: string
  name: string
  level: number
  experience: number
  slots: FarmSlot[]
  maxSlots: number
  unlockCost: number
  
  // 农场属性
  properties: {
    wateringEfficiency: number
    fertilizerEfficiency: number
    harvestBonus: number
    growthSpeedMultiplier: number
  }
  
  // 农场建筑/装饰
  buildings: FarmBuilding[]
  decorations: FarmDecoration[]
}

export interface FarmBuilding {
  id: string
  type: BuildingType
  name: string
  x: number
  y: number
  level: number
  effects: BuildingEffect[]
  sprite: string
}

export enum BuildingType {
  WAREHOUSE = 'warehouse',
  GREENHOUSE = 'greenhouse',
  WELL = 'well',
  FERTILIZER_PLANT = 'fertilizer_plant',
  MARKET_STALL = 'market_stall',
  SYNTHESIS_LAB = 'synthesis_lab'
}

export interface BuildingEffect {
  type: EffectType
  value: number
  radius?: number
  targetCategory?: ItemCategory
}

export enum EffectType {
  GROWTH_SPEED = 'growth_speed',
  PRODUCTION_BONUS = 'production_bonus',
  QUALITY_BONUS = 'quality_bonus',
  PRICE_BONUS = 'price_bonus',
  STORAGE_CAPACITY = 'storage_capacity'
}

export interface FarmDecoration {
  id: string
  name: string
  x: number
  y: number
  sprite: string
  beautyValue: number
}

// 盲盒系统
export interface LootBox {
  id: string
  name: string
  description: string
  price: number
  currency: 'focus_token' | 'gold' | 'diamond'
  rarity: ItemRarity
  contents: LootBoxContent[]
  animation: string
  openSound: string
  rarityGlow: string
}

export interface LootBoxContent {
  itemId: string
  itemType: 'agricultural_item' | 'building' | 'decoration' | 'token' | 'material'
  probability: number
  minQuantity: number
  maxQuantity: number
  specialEffects?: string[]
}

// 合成系统
export interface SynthesisRecipe {
  id: string
  name: string
  description: string
  inputItems: SynthesisInput[]
  outputItem: SynthesisOutput
  successRate: number
  cost: {
    gold?: number
    focusToken?: number
  }
  requiredLevel: number
  requiredBuilding?: BuildingType
  craftTime: number  // 秒
}

export interface SynthesisInput {
  itemId: string
  quantity: number
  rarity?: ItemRarity
  mustBeSameVariety: boolean
}

export interface SynthesisOutput {
  itemId: string
  quantity: number
  rarity: ItemRarity
  variety: string
  qualityRange: {
    min: number
    max: number
  }
}

// 合成会话
export interface SynthesisSession {
  id: string
  recipeId: string
  inputItems: AgriculturalItem[]
  startTime: number
  endTime: number
  status: SynthesisStatus
  hasProtection: boolean
  protectionItems: string[]
  result?: SynthesisResult
}

export enum SynthesisStatus {
  PREPARING = 'preparing',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}

export interface SynthesisResult {
  success: boolean
  outputItems: AgriculturalItem[]
  failureReason?: string
  compensationItems?: AgriculturalItem[]
  experienceGained: number
}

// 市场系统
export interface MarketListing {
  id: string
  sellerId: string
  item: AgriculturalItem
  quantity: number
  pricePerUnit: number
  totalPrice: number
  listedTime: number
  expiresTime: number
  status: ListingStatus
}

export enum ListingStatus {
  ACTIVE = 'active',
  SOLD = 'sold',
  EXPIRED = 'expired',
  CANCELLED = 'cancelled'
}

export interface MarketOrder {
  id: string
  buyerId: string
  listingId: string
  quantity: number
  totalPrice: number
  orderTime: number
  status: OrderStatus
}

export enum OrderStatus {
  PENDING = 'pending',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}

// 玩家库存
export interface PlayerInventory {
  focusTokens: FocusToken
  items: AgriculturalItem[]
  capacity: number
  categories: {
    [key in ItemCategory]: AgriculturalItem[]
  }
}

// 游戏状态
export interface GameState {
  player: {
    id: string
    name: string
    level: number
    experience: number
    inventory: PlayerInventory
    farm: Farm
    achievements: Achievement[]
    statistics: PlayerStatistics
  }
  market: {
    listings: MarketListing[]
    priceHistory: Record<string, PricePoint[]>
    demandFactors: Record<string, number>
  }
  synthesis: {
    availableRecipes: SynthesisRecipe[]
    activeSessions: SynthesisSession[]
    completedSessions: SynthesisSession[]
  }
  time: {
    gameTime: number
    realTime: number
    season: Season
    weather: Weather
    dayOfWeek: number
  }
}

export interface Achievement {
  id: string
  name: string
  description: string
  category: string
  progress: number
  target: number
  completed: boolean
  completedTime?: number
  rewards: AchievementReward[]
}

export interface AchievementReward {
  type: 'focus_token' | 'item' | 'building' | 'experience'
  amount: number
  itemId?: string
}

export interface PlayerStatistics {
  totalItemsHarvested: number
  totalItemsSynthesized: number
  totalFocusTokensEarned: number
  totalMarketTransactions: number
  totalPlayTime: number
  favoriteItem?: string
  highestQualityItem?: AgriculturalItem
  farmValueHistory: number[]
}

export enum Season {
  SPRING = 'spring',
  SUMMER = 'summer',
  AUTUMN = 'autumn',
  WINTER = 'winter'
}

export enum Weather {
  SUNNY = 'sunny',
  CLOUDY = 'cloudy',
  RAINY = 'rainy',
  STORMY = 'stormy',
  SNOWY = 'snowy'
}

// Phaser游戏对象接口
export interface GameObjectData {
  scene: string
  x: number
  y: number
  texture: string
  frame?: string | number
  data?: any
}

// 游戏配置
export interface GameConfig {
  focusToken: {
    dailyLimit: number
    earnRate: number
    bonusMultiplier: number
  }
  farm: {
    initialSlots: number
    maxSlots: number
    slotUnlockCost: number[]
  }
  synthesis: {
    baseSuccessRate: Record<ItemRarity, number>
    protectionEffects: Record<string, number>
    experienceRewards: Record<ItemRarity, number>
  }
  market: {
    listingFee: number
    transactionFee: number
    maxListingDuration: number
    priceFluctuationRange: number
  }
} 