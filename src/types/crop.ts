// 作物生长阶段枚举
export enum CropStage {
  SEED = 'seed',
  SPROUT = 'sprout', 
  GROWING = 'growing',
  MATURE = 'mature',
  READY_TO_HARVEST = 'ready_to_harvest',
  HARVESTED = 'harvested'
}

// 作物类型枚举
export enum CropType {
  KNOWLEDGE_FLOWER = 'knowledge_flower',
  STRENGTH_TREE = 'strength_tree',
  TIME_VEGGIE = 'time_veggie',
  MEDITATION_LOTUS = 'meditation_lotus',
  FOCUS_FLOWER = 'focus_flower',
  READING_VINE = 'reading_vine',
  SOCIAL_FRUIT = 'social_fruit'
}

// 作物品质等级
export enum CropQuality {
  COMMON = 'common',
  UNCOMMON = 'uncommon',
  RARE = 'rare',
  EPIC = 'epic',
  LEGENDARY = 'legendary'
}

// 单个生长阶段的配置
export interface StageConfig {
  stage: CropStage
  duration: number // 持续时间（毫秒）
  minFocusScore: number // 最低专注分数要求
  visualScale: number // 视觉缩放比例
  spriteFrame: string // 精灵图帧名称
  description: string // 阶段描述
}

// 作物基础配置
export interface CropConfig {
  type: CropType
  name: string
  description: string
  icon: string
  baseGrowthTime: number // 基础生长时间（毫秒）
  focusMultiplier: number // 专注度对生长速度的影响系数
  stages: StageConfig[] // 各个生长阶段配置
  rewards: {
    baseExp: number
    growthPoints: number
    specialItems?: string[]
  }
  requirements: {
    minLevel?: number
    prerequisites?: CropType[]
  }
}

// 作物实例状态
export interface CropInstance {
  id: string // 唯一标识符
  type: CropType
  stage: CropStage
  quality: CropQuality
  plantedAt: number // 种植时间戳
  stageStartTime: number // 当前阶段开始时间
  totalGrowthTime: number // 总生长时间
  focusTimeContributed: number // 专注时间贡献
  averageFocusScore: number // 平均专注分数
  position: {
    x: number
    y: number
    gridX: number
    gridY: number
  }
  isGrowing: boolean // 是否正在生长
  isPaused: boolean // 是否暂停生长
  harvestable: boolean // 是否可收获
  metadata: {
    sessionsContributed: number // 贡献的专注会话数
    bestFocusStreak: number // 最佳专注连续时间
    growthBoosts: number // 生长加速次数
  }
}

// 农场网格状态
export interface FarmGrid {
  width: number
  height: number
  plots: (CropInstance | null)[][] // 二维网格，null表示空地
}

// 作物生长事件
export interface CropGrowthEvent {
  cropId: string
  type: 'stage_change' | 'growth_boost' | 'pause' | 'resume' | 'harvest'
  timestamp: number
  data: {
    fromStage?: CropStage
    toStage?: CropStage
    focusScore?: number
    reason?: string
  }
}

// 作物统计信息
export interface CropStats {
  totalPlanted: number
  totalHarvested: number
  totalGrowthTime: number
  averageQuality: CropQuality
  bestCrop: {
    type: CropType
    quality: CropQuality
    growthTime: number
  }
  typeStats: {
    [key in CropType]: {
      planted: number
      harvested: number
      averageGrowthTime: number
    }
  }
}

// 生长计算函数类型
export type GrowthCalculator = (
  crop: CropInstance,
  focusScore: number,
  deltaTime: number
) => {
  progressDelta: number
  shouldAdvanceStage: boolean
  qualityBonus: number
}

// 作物管理器接口
export interface CropManager {
  // 作物操作
  plantCrop(type: CropType, gridX: number, gridY: number): Promise<CropInstance>
  harvestCrop(cropId: string): Promise<{
    rewards: any
    quality: CropQuality
  }>
  removeCrop(cropId: string): Promise<void>
  
  // 生长管理
  updateGrowth(deltaTime: number, focusScore: number): void
  pauseGrowth(cropId?: string): void
  resumeGrowth(cropId?: string): void
  boostGrowth(cropId: string, multiplier: number): void
  
  // 查询
  getCrop(cropId: string): CropInstance | null
  getCropsAtStage(stage: CropStage): CropInstance[]
  getHarvestableCrops(): CropInstance[]
  getFarmGrid(): FarmGrid
  
  // 统计
  getStats(): CropStats
  getCropProgress(cropId: string): number // 0-1
  getEstimatedTimeToHarvest(cropId: string): number
  
  // 事件
  onGrowthEvent(callback: (event: CropGrowthEvent) => void): void
  offGrowthEvent(callback: (event: CropGrowthEvent) => void): void
}

// 预定义作物配置
export const CROP_CONFIGS: Record<CropType, CropConfig> = {
  [CropType.KNOWLEDGE_FLOWER]: {
    type: CropType.KNOWLEDGE_FLOWER,
    name: '知识花',
    description: '通过专注学习培育的智慧之花，象征知识的积累',
    icon: '🌸',
    baseGrowthTime: 30 * 60 * 1000, // 30分钟
    focusMultiplier: 1.5,
    stages: [
      {
        stage: CropStage.SEED,
        duration: 2 * 60 * 1000, // 2分钟
        minFocusScore: 50,
        visualScale: 0.3,
        spriteFrame: 'knowledge_seed',
        description: '知识的种子正在孕育'
      },
      {
        stage: CropStage.SPROUT,
        duration: 5 * 60 * 1000, // 5分钟
        minFocusScore: 60,
        visualScale: 0.5,
        spriteFrame: 'knowledge_sprout',
        description: '幼苗破土而出，渴望学习'
      },
      {
        stage: CropStage.GROWING,
        duration: 15 * 60 * 1000, // 15分钟
        minFocusScore: 70,
        visualScale: 0.8,
        spriteFrame: 'knowledge_growing',
        description: '专注地吸收知识养分'
      },
      {
        stage: CropStage.MATURE,
        duration: 8 * 60 * 1000, // 8分钟
        minFocusScore: 80,
        visualScale: 1.0,
        spriteFrame: 'knowledge_mature',
        description: '知识之花即将绽放'
      },
      {
        stage: CropStage.READY_TO_HARVEST,
        duration: Infinity,
        minFocusScore: 0,
        visualScale: 1.2,
        spriteFrame: 'knowledge_bloom',
        description: '智慧之花完全盛开，可以收获了！'
      }
    ],
    rewards: {
      baseExp: 100,
      growthPoints: 50,
      specialItems: ['wisdom_essence', 'focus_boost']
    },
    requirements: {
      minLevel: 1
    }
  },
  
  [CropType.STRENGTH_TREE]: {
    type: CropType.STRENGTH_TREE,
    name: '力量树',
    description: '通过运动和锻炼培育的强壮之树，象征身体的力量',
    icon: '🌳',
    baseGrowthTime: 45 * 60 * 1000, // 45分钟
    focusMultiplier: 1.2,
    stages: [
      {
        stage: CropStage.SEED,
        duration: 3 * 60 * 1000,
        minFocusScore: 40,
        visualScale: 0.2,
        spriteFrame: 'strength_seed',
        description: '力量的种子蓄势待发'
      },
      {
        stage: CropStage.SPROUT,
        duration: 8 * 60 * 1000,
        minFocusScore: 50,
        visualScale: 0.4,
        spriteFrame: 'strength_sprout',
        description: '强壮的嫩芽正在成长'
      },
      {
        stage: CropStage.GROWING,
        duration: 20 * 60 * 1000,
        minFocusScore: 60,
        visualScale: 0.7,
        spriteFrame: 'strength_growing',
        description: '茁壮成长，越来越强壮'
      },
      {
        stage: CropStage.MATURE,
        duration: 14 * 60 * 1000,
        minFocusScore: 70,
        visualScale: 1.0,
        spriteFrame: 'strength_mature',
        description: '力量之树挺拔伟岸'
      },
      {
        stage: CropStage.READY_TO_HARVEST,
        duration: Infinity,
        minFocusScore: 0,
        visualScale: 1.3,
        spriteFrame: 'strength_mighty',
        description: '强壮的力量之树可以收获了！'
      }
    ],
    rewards: {
      baseExp: 120,
      growthPoints: 70,
      specialItems: ['power_essence', 'endurance_boost']
    },
    requirements: {
      minLevel: 3
    }
  },
  
  [CropType.TIME_VEGGIE]: {
    type: CropType.TIME_VEGGIE,
    name: '时间菜',
    description: '通过合理时间管理培育的蔬菜，象征效率与规律',
    icon: '🥬',
    baseGrowthTime: 20 * 60 * 1000, // 20分钟
    focusMultiplier: 2.0,
    stages: [
      {
        stage: CropStage.SEED,
        duration: 1 * 60 * 1000,
        minFocusScore: 60,
        visualScale: 0.3,
        spriteFrame: 'time_seed',
        description: '时间的种子分秒必争'
      },
      {
        stage: CropStage.SPROUT,
        duration: 3 * 60 * 1000,
        minFocusScore: 70,
        visualScale: 0.5,
        spriteFrame: 'time_sprout',
        description: '效率的嫩芽快速成长'
      },
      {
        stage: CropStage.GROWING,
        duration: 10 * 60 * 1000,
        minFocusScore: 80,
        visualScale: 0.8,
        spriteFrame: 'time_growing',
        description: '时间管理显现成效'
      },
      {
        stage: CropStage.MATURE,
        duration: 6 * 60 * 1000,
        minFocusScore: 85,
        visualScale: 1.0,
        spriteFrame: 'time_mature',
        description: '时间蔬菜即将成熟'
      },
      {
        stage: CropStage.READY_TO_HARVEST,
        duration: Infinity,
        minFocusScore: 0,
        visualScale: 1.1,
        spriteFrame: 'time_ripe',
        description: '高效的时间菜可以收获了！'
      }
    ],
    rewards: {
      baseExp: 80,
      growthPoints: 40,
      specialItems: ['efficiency_essence', 'time_boost']
    },
    requirements: {
      minLevel: 2
    }
  },
  
  [CropType.MEDITATION_LOTUS]: {
    type: CropType.MEDITATION_LOTUS,
    name: '冥想莲',
    description: '通过冥想和内省培育的圣洁莲花，象征内心的平静',
    icon: '🪷',
    baseGrowthTime: 60 * 60 * 1000, // 60分钟
    focusMultiplier: 1.8,
    stages: [
      {
        stage: CropStage.SEED,
        duration: 5 * 60 * 1000,
        minFocusScore: 70,
        visualScale: 0.2,
        spriteFrame: 'lotus_seed',
        description: '平静的莲子在水中沉思'
      },
      {
        stage: CropStage.SPROUT,
        duration: 10 * 60 * 1000,
        minFocusScore: 75,
        visualScale: 0.4,
        spriteFrame: 'lotus_sprout',
        description: '莲芽缓缓浮出水面'
      },
      {
        stage: CropStage.GROWING,
        duration: 25 * 60 * 1000,
        minFocusScore: 80,
        visualScale: 0.7,
        spriteFrame: 'lotus_growing',
        description: '在宁静中慢慢绽放'
      },
      {
        stage: CropStage.MATURE,
        duration: 20 * 60 * 1000,
        minFocusScore: 90,
        visualScale: 1.0,
        spriteFrame: 'lotus_mature',
        description: '冥想莲即将达到完美境界'
      },
      {
        stage: CropStage.READY_TO_HARVEST,
        duration: Infinity,
        minFocusScore: 0,
        visualScale: 1.4,
        spriteFrame: 'lotus_enlightened',
        description: '圣洁的冥想莲已达到开悟状态！'
      }
    ],
    rewards: {
      baseExp: 150,
      growthPoints: 100,
      specialItems: ['serenity_essence', 'meditation_boost', 'enlightenment_crystal']
    },
    requirements: {
      minLevel: 5,
      prerequisites: [CropType.KNOWLEDGE_FLOWER]
    }
  },
  
  [CropType.FOCUS_FLOWER]: {
    type: CropType.FOCUS_FLOWER,
    name: '专注花',
    description: '通过深度专注培育的花朵，象征心无旁骛的专注力',
    icon: '🌺',
    baseGrowthTime: 25 * 60 * 1000, // 25分钟
    focusMultiplier: 2.5,
    stages: [
      {
        stage: CropStage.SEED,
        duration: 1.5 * 60 * 1000,
        minFocusScore: 75,
        visualScale: 0.25,
        spriteFrame: 'focus_seed',
        description: '专注的种子需要高度集中'
      },
      {
        stage: CropStage.SPROUT,
        duration: 4 * 60 * 1000,
        minFocusScore: 80,
        visualScale: 0.45,
        spriteFrame: 'focus_sprout',
        description: '专注力开始凝聚成形'
      },
      {
        stage: CropStage.GROWING,
        duration: 12 * 60 * 1000,
        minFocusScore: 85,
        visualScale: 0.75,
        spriteFrame: 'focus_growing',
        description: '专注之花在纯净的心境中成长'
      },
      {
        stage: CropStage.MATURE,
        duration: 7.5 * 60 * 1000,
        minFocusScore: 90,
        visualScale: 1.0,
        spriteFrame: 'focus_mature',
        description: '专注力达到高度凝聚状态'
      },
      {
        stage: CropStage.READY_TO_HARVEST,
        duration: Infinity,
        minFocusScore: 0,
        visualScale: 1.25,
        spriteFrame: 'focus_perfect',
        description: '完美的专注之花绽放出纯净光芒！'
      }
    ],
    rewards: {
      baseExp: 110,
      growthPoints: 80,
      specialItems: ['concentration_essence', 'focus_crystal', 'clarity_boost']
    },
    requirements: {
      minLevel: 4,
      prerequisites: [CropType.KNOWLEDGE_FLOWER, CropType.TIME_VEGGIE]
    }
  },
  
  [CropType.READING_VINE]: {
    type: CropType.READING_VINE,
    name: '读书藤',
    description: '通过阅读习惯培育的知识藤蔓，象征智慧的延伸与传播',
    icon: '📚',
    baseGrowthTime: 35 * 60 * 1000, // 35分钟
    focusMultiplier: 1.7,
    stages: [
      {
        stage: CropStage.SEED,
        duration: 2.5 * 60 * 1000,
        minFocusScore: 55,
        visualScale: 0.2,
        spriteFrame: 'reading_seed',
        description: '知识的种子等待启蒙'
      },
      {
        stage: CropStage.SPROUT,
        duration: 6 * 60 * 1000,
        minFocusScore: 65,
        visualScale: 0.4,
        spriteFrame: 'reading_sprout',
        description: '求知的嫩芽探索世界'
      },
      {
        stage: CropStage.GROWING,
        duration: 18 * 60 * 1000,
        minFocusScore: 75,
        visualScale: 0.8,
        spriteFrame: 'reading_growing',
        description: '知识藤蔓不断延伸扩展'
      },
      {
        stage: CropStage.MATURE,
        duration: 8.5 * 60 * 1000,
        minFocusScore: 80,
        visualScale: 1.0,
        spriteFrame: 'reading_mature',
        description: '智慧的藤蔓枝繁叶茂'
      },
      {
        stage: CropStage.READY_TO_HARVEST,
        duration: Infinity,
        minFocusScore: 0,
        visualScale: 1.3,
        spriteFrame: 'reading_flourish',
        description: '知识藤蔓结出智慧果实！'
      }
    ],
    rewards: {
      baseExp: 130,
      growthPoints: 90,
      specialItems: ['knowledge_vine', 'wisdom_scroll', 'learning_boost']
    },
    requirements: {
      minLevel: 3,
      prerequisites: [CropType.KNOWLEDGE_FLOWER]
    }
  },
  
  [CropType.SOCIAL_FRUIT]: {
    type: CropType.SOCIAL_FRUIT,
    name: '社交果',
    description: '通过社交互动培育的友谊之果，象征人际关系的和谐',
    icon: '🍎',
    baseGrowthTime: 40 * 60 * 1000, // 40分钟
    focusMultiplier: 1.3,
    stages: [
      {
        stage: CropStage.SEED,
        duration: 3 * 60 * 1000,
        minFocusScore: 45,
        visualScale: 0.3,
        spriteFrame: 'social_seed',
        description: '友谊的种子等待连接'
      },
      {
        stage: CropStage.SPROUT,
        duration: 8 * 60 * 1000,
        minFocusScore: 55,
        visualScale: 0.5,
        spriteFrame: 'social_sprout',
        description: '社交的嫩芽寻求共鸣'
      },
      {
        stage: CropStage.GROWING,
        duration: 20 * 60 * 1000,
        minFocusScore: 65,
        visualScale: 0.8,
        spriteFrame: 'social_growing',
        description: '人际关系网络逐渐扩展'
      },
      {
        stage: CropStage.MATURE,
        duration: 9 * 60 * 1000,
        minFocusScore: 70,
        visualScale: 1.0,
        spriteFrame: 'social_mature',
        description: '社交果树即将结出友谊果实'
      },
      {
        stage: CropStage.READY_TO_HARVEST,
        duration: Infinity,
        minFocusScore: 0,
        visualScale: 1.2,
        spriteFrame: 'social_harvest',
        description: '友谊之果甜美可人，可以分享了！'
      }
    ],
    rewards: {
      baseExp: 100,
      growthPoints: 60,
      specialItems: ['friendship_essence', 'harmony_crystal', 'social_boost']
    },
    requirements: {
      minLevel: 2,
      prerequisites: [CropType.STRENGTH_TREE]
    }
  }
}

// 生长进度计算函数
export const calculateGrowthProgress = (
  crop: CropInstance,
  config: CropConfig
): number => {
  const currentStage = config.stages.find(s => s.stage === crop.stage)
  if (!currentStage) return 0
  
  const stageElapsed = Date.now() - crop.stageStartTime
  const stageProgress = Math.min(stageElapsed / currentStage.duration, 1)
  
  // 计算总体进度
  const stageIndex = config.stages.findIndex(s => s.stage === crop.stage)
  const completedStages = stageIndex
  const totalStages = config.stages.length - 1 // 不计算HARVESTED阶段
  
  return (completedStages + stageProgress) / totalStages
}

// 品质计算函数
export const calculateQuality = (
  crop: CropInstance,
  averageFocusScore: number
): CropQuality => {
  if (averageFocusScore >= 95) return CropQuality.LEGENDARY
  if (averageFocusScore >= 90) return CropQuality.EPIC
  if (averageFocusScore >= 80) return CropQuality.RARE
  if (averageFocusScore >= 70) return CropQuality.UNCOMMON
  return CropQuality.COMMON
} 