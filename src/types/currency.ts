// 代币类型枚举
export enum CurrencyType {
  // 基础代币
  FOCUS_COIN = 'focus_coin',        // 专注币 - 通过专注任务获得
  DISCIPLINE_TOKEN = 'discipline_token', // 自律代币 - 高质量表现奖励
  
  // 特殊代币
  FUTURES_CRYSTAL = 'futures_crystal', // 期货水晶 - 稀有代币
  GOLDEN_HARVEST = 'golden_harvest',   // 金色收获 - 顶级代币
}

// 货币配置
export interface CurrencyConfig {
  id: CurrencyType
  name: string
  description: string
  icon: string
  color: string
  maxAmount: number
  transferable: boolean
  convertible: boolean
  conversionRates?: Record<CurrencyType, number>
}

// 用户货币状态
export interface UserCurrency {
  [CurrencyType.FOCUS_COIN]: number
  [CurrencyType.DISCIPLINE_TOKEN]: number
  [CurrencyType.FUTURES_CRYSTAL]: number
  [CurrencyType.GOLDEN_HARVEST]: number
}

// 货币奖励配置
export interface CurrencyReward {
  type: CurrencyType
  amount: number
  multiplier?: number
  bonusConditions?: {
    streakBonus?: number
    qualityBonus?: number
    timeBonus?: number
  }
}

// 代币获取来源
export enum CurrencySource {
  FOCUS_TASK = 'focus_task',
  CROP_HARVEST = 'crop_harvest',
  ACHIEVEMENT = 'achievement',
  DAILY_BONUS = 'daily_bonus',
  STREAK_BONUS = 'streak_bonus',
  SYNTHESIS = 'synthesis',
  TRADING = 'trading',
  EVENT = 'event'
}

// 代币交易记录
export interface CurrencyTransaction {
  id: string
  type: 'earn' | 'spend' | 'convert' | 'transfer'
  currency: CurrencyType
  amount: number
  source: CurrencySource
  timestamp: number
  metadata?: {
    sourceId?: string
    targetId?: string
    multiplier?: number
    description?: string
  }
}

// 货币系统配置
export const CURRENCY_CONFIGS: Record<CurrencyType, CurrencyConfig> = {
  [CurrencyType.FOCUS_COIN]: {
    id: CurrencyType.FOCUS_COIN,
    name: '专注币',
    description: '通过完成专注任务获得的基础代币',
    icon: '🪙',
    color: '#FFD700',
    maxAmount: 999999,
    transferable: true,
    convertible: true,
    conversionRates: {
      [CurrencyType.FOCUS_COIN]: 1,
      [CurrencyType.DISCIPLINE_TOKEN]: 10, // 10专注币 = 1自律代币
      [CurrencyType.FUTURES_CRYSTAL]: 100,
      [CurrencyType.GOLDEN_HARVEST]: 1000
    }
  },
  [CurrencyType.DISCIPLINE_TOKEN]: {
    id: CurrencyType.DISCIPLINE_TOKEN,
    name: '自律代币',
    description: '高质量自律表现的奖励代币',
    icon: '🏆',
    color: '#C0C0C0',
    maxAmount: 99999,
    transferable: true,
    convertible: true,
    conversionRates: {
      [CurrencyType.FOCUS_COIN]: 0.1,
      [CurrencyType.DISCIPLINE_TOKEN]: 1,
      [CurrencyType.FUTURES_CRYSTAL]: 10,
      [CurrencyType.GOLDEN_HARVEST]: 100
    }
  },
  [CurrencyType.FUTURES_CRYSTAL]: {
    id: CurrencyType.FUTURES_CRYSTAL,
    name: '期货水晶',
    description: '期货交易成功获得的稀有水晶',
    icon: '💎',
    color: '#4169E1',
    maxAmount: 9999,
    transferable: false,
    convertible: true,
    conversionRates: {
      [CurrencyType.FOCUS_COIN]: 0.01,
      [CurrencyType.DISCIPLINE_TOKEN]: 0.1,
      [CurrencyType.FUTURES_CRYSTAL]: 1,
      [CurrencyType.GOLDEN_HARVEST]: 10
    }
  },
  [CurrencyType.GOLDEN_HARVEST]: {
    id: CurrencyType.GOLDEN_HARVEST,
    name: '金色收获',
    description: '最珍贵的收获代币，用于顶级交易',
    icon: '🏅',
    color: '#FFB300',
    maxAmount: 999,
    transferable: false,
    convertible: false
  }
} 