import { ItemRarity, ItemCategory } from './lootbox'

// 装饰道具类型枚举
export enum DecorationType {
  // 围栏类
  FENCE = 'fence',
  HEDGE = 'hedge',
  WALL = 'wall',
  
  // 植物装饰
  ORNAMENTAL_PLANT = 'ornamental_plant',
  FLOWER_BED = 'flower_bed',
  TREE = 'tree',
  BUSH = 'bush',
  
  // 功能装饰
  LAMP = 'lamp',
  FOUNTAIN = 'fountain',
  STATUE = 'statue',
  BENCH = 'bench',
  
  // 路径装饰
  STONE_PATH = 'stone_path',
  WOODEN_PATH = 'wooden_path',
  BRICK_PATH = 'brick_path',
  
  // 季节装饰
  SEASONAL = 'seasonal',
  HOLIDAY = 'holiday',
  
  // 文化主题
  CHINESE_STYLE = 'chinese_style',
  WESTERN_STYLE = 'western_style',
  MODERN_STYLE = 'modern_style',
  RUSTIC_STYLE = 'rustic_style'
}

// 装饰道具尺寸
export interface DecorationSize {
  width: number
  height: number
  depth?: number  // 3D深度（如果支持）
}

// 装饰道具效果
export interface DecorationEffect {
  type: DecorationEffectType
  value: number
  radius?: number         // 影响范围（格子数）
  range?: number          // 新增：影响范围别名，向后兼容
  description: string
}

export enum DecorationEffectType {
  BEAUTY_BOOST = 'beauty_boost',           // 美观度提升
  GROWTH_SPEED = 'growth_speed',           // 作物生长速度
  HARVEST_BONUS = 'harvest_bonus',         // 收获奖励
  MOOD_BOOST = 'mood_boost',               // 心情提升
  FOCUS_TOKEN_BONUS = 'focus_token_bonus', // 专注代币奖励
  WEATHER_PROTECTION = 'weather_protection', // 天气保护
  PEST_RESISTANCE = 'pest_resistance',     // 害虫抗性
  SYNTHESIS_BONUS = 'synthesis_bonus',     // 合成成功率提升
  
  // 新增效果类型
  PLANT_GROWTH_SPEED = 'plant_growth_speed', // 植物生长速度
  FOCUS_BONUS = 'focus_bonus',               // 专注效果提升
  BEAUTY_MULTIPLIER = 'beauty_multiplier',   // 美观度加成
  HARVEST_YIELD = 'harvest_yield',           // 收获产量
  AUTOMATION_EFFICIENCY = 'automation_efficiency', // 自动化效率
  CROP_QUALITY = 'crop_quality',             // 作物品质
  SEASONAL_BONUS = 'seasonal_bonus',         // 季节收益加成
  EXOTIC_CROP_YIELD = 'exotic_crop_yield',   // 异国作物产量
  BIODIVERSITY_BONUS = 'biodiversity_bonus', // 生物多样性奖励
  PRESERVATION_BONUS = 'preservation_bonus', // 作物保存时间
  WINTER_CROP_BONUS = 'winter_crop_bonus',   // 冬季作物产量
  DROUGHT_RESISTANCE = 'drought_resistance', // 抗旱能力
  RARE_RESOURCE_CHANCE = 'rare_resource_chance' // 稀有资源获得几率
}

// 装饰道具主接口
export interface DecorationItem {
  id: string
  name: string
  nameEn: string
  description: string
  type: DecorationType
  rarity: ItemRarity
  category: ItemCategory
  
  // 基础属性
  size: DecorationSize
  beautyValue: number
  level: number
  maxLevel: number
  
  // 外观属性
  visual: {
    sprite: string
    spriteSheet?: string
    animation?: string[]
    variants?: string[]      // 颜色/样式变体
    seasonalSprites?: Record<string, string>  // 季节性外观
  }
  
  // 功能效果
  effects?: DecorationEffect[]
  
  // 放置规则
  placement: {
    canOverlap: boolean
    needsOpenSpace: boolean
    canPlaceOnWater: boolean
    canPlaceOnPath: boolean
    restrictedZones?: string[]  // 不能放置的区域
    requiredAdjacent?: DecorationType[]  // 需要相邻的装饰类型
  }
  
  // 经济属性
  economy: {
    basePrice: number
    sellPrice: number
    maintenanceCost?: number  // 维护费用
    unlockLevel: number       // 解锁等级
    unlockConditions?: UnlockCondition[]
  }
  
  // 互动属性
  interaction?: {
    canInteract: boolean
    interactionType: InteractionType
    rewards?: InteractionReward[]
    cooldown?: number         // 交互冷却时间（秒）
  }
  
  // 时间属性
  temporal?: {
    isTemporary: boolean
    duration?: number         // 持续时间（小时）
    renewalCost?: number
    seasonalAvailability?: string[]
  }
  
  // 音效
  audio?: {
    placementSound?: string
    ambientSound?: string
    interactionSound?: string
  }
}

// 解锁条件
export interface UnlockCondition {
  type: UnlockConditionType
  value: number | string
  description: string
}

export enum UnlockConditionType {
  FARM_LEVEL = 'farm_level',
  ACHIEVEMENT = 'achievement',
  ITEM_COUNT = 'item_count',
  FOCUS_TOKENS = 'focus_tokens',
  SEASON = 'season',
  SPECIAL_EVENT = 'special_event'
}

// 交互类型
export enum InteractionType {
  CLICK = 'click',
  HARVEST = 'harvest',
  WATER = 'water',
  ADMIRE = 'admire',
  COLLECT = 'collect',
  UPGRADE = 'upgrade'
}

// 交互奖励
export interface InteractionReward {
  type: 'focus_token' | 'item' | 'experience' | 'beauty'
  amount: number
  itemId?: string
  probability: number
}

// 装饰品在农场中的放置实例
export interface PlacedDecoration {
  instanceId: string        // 唯一实例ID
  decorationId: string      // 装饰道具ID
  x: number
  y: number
  rotation: number          // 旋转角度（0-360）
  variant?: string          // 选择的变体
  level: number
  placedTime: number
  lastInteractionTime?: number
  
  // 状态信息
  condition: number         // 状态/耐久度 (0-100)
  needsMaintenance: boolean
  maintenanceReminderTime?: number
}

// 农场主题配置
export interface FarmTheme {
  id: string
  name: string
  nameEn: string
  description: string
  rarity?: ItemRarity          // 可选，向后兼容
  
  // 主题属性
  category: ThemeStyle         // 新名称，替代style
  style?: ThemeStyle          // 向后兼容
  season?: string             // 特定季节主题
  culture?: string            // 文化主题
  price: number               // 主题价格
  isDefault?: boolean         // 是否为默认主题
  
  // 视觉配置
  visual: {
    backgroundTexture: string
    groundTexture: string
    skyTexture?: string
    skybox?: string           // 新增天空盒
    waterTexture?: string     // 新增水纹理
    fogColor?: string
    lightingPreset: string
    musicTrack?: string
    
    // 新增丰富的视觉元素
    treeModels?: string[]     // 3D模型
    particleEffects?: string[] // 粒子效果
    weather?: string[]        // 天气效果
    colorPalette?: {          // 颜色主题
      primary: string
      secondary: string
      accent: string
      background: string
    }
  }
  
  // 音频配置（新增）
  audio?: {
    ambientMusic?: string
    soundEffects?: Record<string, string>
  }
  
  // 装饰偏好
  decorationPreferences: {
    recommendedTypes?: DecorationType[]    // 向后兼容
    bonusTypes?: DecorationType[]          // 向后兼容
    restrictedTypes?: DecorationType[]     // 向后兼容
    
    // 新增配置
    favoredTypes?: DecorationType[]        // 推荐类型
    discouragedTypes?: DecorationType[]    // 不推荐类型
    seasonalVariations?: {                 // 季节变化
      spring?: { tint: string, intensity: number }
      summer?: { tint: string, intensity: number }
      autumn?: { tint: string, intensity: number }
      winter?: { tint: string, intensity: number }
    }
  }
  
  // 主题效果
  themeEffects?: DecorationEffect[]
  
  // 解锁条件
  unlockConditions: UnlockCondition[]
  
  // 新增属性
  achievements?: string[]     // 相关成就
  tags?: string[]            // 主题标签
}

export enum ThemeStyle {
  NATURAL = 'natural',
  MODERN = 'modern',
  TRADITIONAL = 'traditional',
  FANTASY = 'fantasy',
  MINIMALIST = 'minimalist',
  LUXURIOUS = 'luxurious',
  RUSTIC = 'rustic',
  FUTURISTIC = 'futuristic',
  
  // 新增主题风格
  EASTERN = 'eastern',       // 东方风格
  TROPICAL = 'tropical',     // 热带风格
  SEASONAL = 'seasonal',     // 季节主题
  EXOTIC = 'exotic'         // 异域风情
}

// 装饰商店配置
export interface DecorationShop {
  id: string
  name: string
  
  // 商店分类
  categories: DecorationShopCategory[]
  
  // 轮换商品
  rotation: {
    dailyItems: string[]      // 每日商品ID
    weeklyItems: string[]     // 每周商品ID
    lastRotationTime: number
  }
  
  // 特价活动
  sales?: DecorationSale[]
}

export interface DecorationShopCategory {
  id: string
  name: string
  icon: string
  decorationTypes: DecorationType[]
  sortOrder: number
  isNew: boolean
  isPopular: boolean
}

export interface DecorationSale {
  id: string
  name: string
  description: string
  discount: number          // 折扣百分比
  targetItems: string[]     // 目标商品ID
  startTime: number
  endTime: number
  isActive: boolean
}

// 装饰管理器状态
export interface DecorationManager {
  // 已拥有的装饰道具
  ownedDecorations: Record<string, number>  // decorationId -> quantity
  
  // 已放置的装饰
  placedDecorations: PlacedDecoration[]
  
  // 当前主题
  currentTheme: string
  unlockedThemes: string[]
  
  // 美观度统计
  beautyStats: {
    totalBeauty: number
    beautyByType: Record<DecorationType, number>
    beautyHistory: BeautyHistoryPoint[]
  }
  
  // 商店状态
  shopState: {
    lastVisitTime: number
    favoriteCategory?: string
    recentPurchases: string[]
    wishlist: string[]
  }
}

export interface BeautyHistoryPoint {
  timestamp: number
  totalBeauty: number
  majorChanges: string[]    // 记录重大变更
}

// 装饰预设配置
export interface DecorationPreset {
  id: string
  name: string
  description: string
  author: string            // 创建者
  theme: string             // 对应主题ID
  
  // 预设配置
  decorations: PresetDecoration[]
  estimatedCost: number
  requiredLevel: number
  beautyRating: number
  
  // 元数据
  tags: string[]
  popularity: number
  createdTime: number
  isOfficial: boolean
  isShareable: boolean
}

export interface PresetDecoration {
  decorationId: string
  x: number
  y: number
  rotation: number
  variant?: string
  level: number
}

// 成就相关
export interface DecorationAchievement {
  id: string
  name: string
  description: string
  
  // 成就条件
  condition: {
    type: DecorationAchievementType
    target: number
    decorationType?: DecorationType
    themeId?: string
  }
  
  // 奖励
  rewards: {
    focusTokens?: number
    decorationItems?: string[]
    themes?: string[]
    titles?: string[]
  }
}

export enum DecorationAchievementType {
  TOTAL_DECORATIONS = 'total_decorations',
  BEAUTY_LEVEL = 'beauty_level',
  THEME_COLLECTOR = 'theme_collector',
  DECORATION_TYPE_MASTER = 'decoration_type_master',
  DAILY_DECORATOR = 'daily_decorator',
  PRESET_CREATOR = 'preset_creator'
} 