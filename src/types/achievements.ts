// 成就类型枚举
export enum AchievementType {
  DAILY = 'daily',           // 每日成就
  WEEKLY = 'weekly',         // 每周成就
  MILESTONE = 'milestone',   // 里程碑成就
  STREAK = 'streak',         // 连续成就
  SPECIAL = 'special'        // 特殊成就
}

// 成就类别枚举
export enum AchievementCategory {
  FOCUS = 'focus',           // 专注相关
  POSTURE = 'posture',       // 姿态相关
  TIME = 'time',             // 时间相关
  CONSISTENCY = 'consistency', // 一致性相关
  IMPROVEMENT = 'improvement'  // 改进相关
}

// 经验值来源
export enum ExperienceSource {
  DAILY_TASK = 'daily_task',
  WEEKLY_TASK = 'weekly_task',
  ACHIEVEMENT = 'achievement',
  MILESTONE = 'milestone',
  BONUS = 'bonus',
  PERFECT_SESSION = 'perfect_session',
  IMPROVEMENT = 'improvement'
}

// 成就定义接口
export interface Achievement {
  id: string;
  name: string;
  description: string;
  type: AchievementType;
  category: AchievementCategory;
  icon: string;
  experienceReward: number;
  requirement: {
    type: string;
    value: number;
    description: string;
  };
  isHidden?: boolean;        // 隐藏成就
  prerequisite?: string[];   // 前置成就
}

// 用户成就进度
export interface UserAchievementProgress {
  achievementId: string;
  progress: number;          // 当前进度
  maxProgress: number;       // 最大进度
  isCompleted: boolean;
  completedAt?: Date;
  lastUpdated: Date;
}

// 等级定义
export interface Level {
  level: number;
  requiredExperience: number;
  name: string;
  description: string;
  rewards?: {
    type: string;
    value: any;
  }[];
}

// 经验值记录
export interface ExperienceRecord {
  id: string;
  amount: number;
  source: ExperienceSource;
  description: string;
  achievementId?: string;
  earnedAt: Date;
  sessionId?: string;
}

// 用户等级和经验值状态
export interface UserExperience {
  currentLevel: number;
  currentExperience: number;
  totalExperience: number;
  experienceToNextLevel: number;
  lastLevelUp?: Date;
}

// 奖励类型
export enum RewardType {
  EXPERIENCE = 'experience',
  BADGE = 'badge',
  TITLE = 'title',
  THEME = 'theme',
  FEATURE = 'feature'
}

// 奖励定义
export interface Reward {
  id: string;
  name: string;
  description: string;
  type: RewardType;
  icon?: string;
  value: any;
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
}

// 用户奖励状态
export interface UserReward {
  rewardId: string;
  earnedAt: Date;
  isActive?: boolean;
  achievementId?: string;
}

// 成就统计
export interface AchievementStats {
  totalAchievements: number;
  completedAchievements: number;
  completionRate: number;
  lastAchievement?: {
    achievement: Achievement;
    completedAt: Date;
  };
  categoryCounts: Record<AchievementCategory, {
    total: number;
    completed: number;
  }>;
}

// 日常任务
export interface DailyTask {
  id: string;
  name: string;
  description: string;
  experienceReward: number;
  requirement: {
    type: string;
    value: number;
  };
  resetTime: string;        // 重置时间 (HH:MM)
}

// 用户日常任务进度
export interface UserDailyTaskProgress {
  taskId: string;
  progress: number;
  maxProgress: number;
  isCompleted: boolean;
  completedAt?: Date;
  resetAt: Date;
  lastUpdated: Date;
}

// 周常任务
export interface WeeklyTask {
  id: string;
  name: string;
  description: string;
  experienceReward: number;
  requirement: {
    type: string;
    value: number;
  };
  resetDay: number;         // 0=周日, 1=周一...
}

// 用户周常任务进度
export interface UserWeeklyTaskProgress {
  taskId: string;
  progress: number;
  maxProgress: number;
  isCompleted: boolean;
  completedAt?: Date;
  resetAt: Date;
  lastUpdated: Date;
}

// 成就系统配置
export interface AchievementSystemConfig {
  experienceMultiplier: number;
  levelUpBonus: number;
  dailyTaskResetTime: string;
  weeklyTaskResetDay: number;
  maxDailyTasks: number;
  maxWeeklyTasks: number;
}

// 成就事件
export interface AchievementEvent {
  type: string;
  data: any;
  timestamp: Date;
  sessionId?: string;
}

// 成就通知
export interface AchievementNotification {
  type: 'achievement' | 'level_up' | 'task_complete' | 'milestone';
  title: string;
  message: string;
  icon?: string;
  achievement?: Achievement;
  level?: Level;
  experience?: number;
  createdAt: Date;
  isRead: boolean;
}

// 用户等级信息（扩展Level接口）
export interface UserLevel extends Level {
  experienceRequired: number;
  icon: string;
}

// 等级晋升历史记录
export interface LevelUpHistory {
  previousLevel: number;
  newLevel: number;
  experienceGained: number;
  totalExperience: number;
  timestamp: Date;
  rewards?: Reward[];
} 