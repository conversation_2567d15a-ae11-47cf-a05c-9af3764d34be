// Electron API类型声明
declare global {
  interface Window {
    electronAPI?: ElectronAPI;
  }
}

interface ElectronAPI {
  // 监控相关方法
  startMonitoring(): Promise<{ success: boolean; message?: string }>;
  stopMonitoring(): Promise<{ success: boolean; message?: string }>;
  getMonitoringStatus(): Promise<{
    isMonitoring: boolean;
    currentApp: {
      name: string;
      processId: number;
      windowTitle: string;
      executablePath: string;
      isActive: boolean;
      timestamp: number;
    } | null;
    whitelistApps: string[];
    violationStartTime: number | null;
    violationThreshold: number;
  }>;
  
  // 白名单管理
  getWhitelist(): Promise<{ success: boolean; data: string[]; message?: string }>;
  setWhitelist(apps: string[]): Promise<{ success: boolean; message?: string }>;
  
  // 应用信息
  getInstalledApps(): Promise<string[]>;
  getAppHistory(hours: number): Promise<Array<{
    name: string;
    processId: number;
    windowTitle: string;
    executablePath: string;
    isActive: boolean;
    timestamp: number;
  }>>;
  
  // 事件监听
  onMonitoringUpdate(callback: (data: {
    activeApp: string;
    isWhitelisted: boolean;
    appInfo: {
      name: string;
      processId: number;
      windowTitle: string;
      executablePath: string;
      isActive: boolean;
      timestamp: number;
    };
  }) => void): void;
  
  onViolationDetected(callback: (data: {
    activeApp: string;
    violationTime: number;
  }) => void): void;
  
  // 清理监听器
  removeAllListeners(event: string): void;
}

export {}; 