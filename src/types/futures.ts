import { CurrencyType } from './currency'
import { ItemRarity, LootboxItem } from './lootbox'

// 期货商品类型（中国期货市场农产品）
export enum FuturesType {
  // 中国农产品期货
  WHEAT = 'wheat',           // 强筋小麦 WH
  CORN = 'corn',             // 玉米 C
  RICE = 'rice',             // 早籼稻 RI
  SOYBEAN = 'soybean',       // 大豆 A
  SOYBEAN_MEAL = 'soybean_meal', // 豆粕 M
  SOYBEAN_OIL = 'soybean_oil',   // 豆油 Y
  COTTON = 'cotton',         // 棉花 CF
  WHITE_SUGAR = 'white_sugar', // 白糖 SR
  RAPESEED_OIL = 'rapeseed_oil', // 菜籽油 OI
  RAPESEED_MEAL = 'rapeseed_meal', // 菜籽粕 RM
  PALM_OIL = 'palm_oil',     // 棕榈油 P
  EGG = 'egg',               // 鸡蛋 JD
  
  // 虚拟期货（游戏专属）
  FOCUS_INDEX = 'focus_index',      // 专注力指数
  DISCIPLINE_INDEX = 'discipline_index'  // 自律指数
}

// 期货合约
export interface FuturesContract {
  id: string
  symbol: string
  name: string
  type: FuturesType
  category: 'agricultural' | 'industrial' | 'virtual'
  
  // 合约规格
  contractSize: number          // 合约单位
  tickSize: number             // 最小变动单位
  deliveryMonth: string        // 交割月份
  
  // 价格信息
  currentPrice: number
  openPrice: number
  highPrice: number
  lowPrice: number
  previousClose: number
  
  // 交易信息
  volume: number               // 成交量
  openInterest: number         // 持仓量
  lastTradeTime: number
  
  // 保证金要求
  initialMargin: number        // 初始保证金
  maintenanceMargin: number    // 维持保证金
  
  // 风险参数
  dailyPriceLimit: number      // 涨跌停板
  volatility: number           // 波动率
  
  metadata: {
    description: string
    underlyingAsset: string
    exchange: string
    tradingHours: {
      start: string
      end: string
    }
    isActive: boolean
  }
}

// 期货订单类型
export enum OrderType {
  MARKET = 'market',           // 市价单
  LIMIT = 'limit',             // 限价单
  STOP = 'stop',               // 止损单
  STOP_LIMIT = 'stop_limit'    // 止损限价单
}

// 期货订单方向
export enum OrderSide {
  BUY = 'buy',                 // 买入开仓
  SELL = 'sell'                // 卖出开仓
}

// 期货订单
export interface FuturesOrder {
  id: string
  userId: string
  contractId: string
  type: OrderType
  side: OrderSide
  quantity: number
  price?: number               // 限价单价格
  stopPrice?: number           // 止损价格
  
  status: 'pending' | 'filled' | 'cancelled' | 'rejected'
  
  // 执行信息
  filledQuantity: number
  averagePrice: number
  
  // 时间戳
  createdAt: number
  updatedAt: number
  filledAt?: number
  
  // 手续费和保证金
  commission: number
  marginRequired: number
  
  metadata?: {
    source: 'manual' | 'auto' | 'strategy'
    strategyId?: string
    notes?: string
  }
}

// 期货仓位
export interface FuturesPosition {
  id: string
  userId: string
  contractId: string
  side: OrderSide
  quantity: number
  averagePrice: number
  
  // 盈亏信息
  unrealizedPnL: number        // 未实现盈亏
  realizedPnL: number          // 已实现盈亏
  
  // 保证金信息
  initialMargin: number
  maintenanceMargin: number
  availableMargin: number
  
  // 风险指标
  marginRatio: number          // 保证金比例
  riskLevel: 'low' | 'medium' | 'high' | 'danger'
  
  // 时间信息
  openedAt: number
  lastUpdated: number
  
  metadata?: {
    maxPnL: number             // 最大盈利
    maxDrawdown: number        // 最大回撤
    holdingDays: number        // 持仓天数
  }
}

// 期货交易结果
export interface TradingResult {
  orderId: string
  success: boolean
  
  // 交易详情
  executedPrice: number
  executedQuantity: number
  commission: number
  
  // 盈亏信息
  pnl: number
  pnlPercentage: number
  
  // 奖励信息
  rewards?: {
    experience: number
    currency: Record<CurrencyType, number>
    items?: LootboxItem[]
    achievements?: string[]
  }
  
  // 风险评估
  riskScore: number
  riskLevel: 'conservative' | 'moderate' | 'aggressive' | 'extreme'
  
  timestamp: number
  
  metadata?: {
    marketCondition: string
    volatilityLevel: number
    tradingVolume: number
    priceMovement: number
  }
}

// 期货市场数据
export interface MarketData {
  timestamp: number
  contractId: string
  
  // OHLC数据
  open: number
  high: number
  low: number
  close: number
  volume: number
  
  // 技术指标
  indicators?: {
    sma20?: number             // 20日移动平均
    sma50?: number             // 50日移动平均
    rsi?: number               // 相对强弱指数
    macd?: number              // MACD
    bollingerUpper?: number    // 布林带上轨
    bollingerLower?: number    // 布林带下轨
  }
  
  // 市场情绪
  sentiment?: {
    bullish: number            // 看涨比例
    bearish: number            // 看跌比例
    neutral: number            // 中性比例
  }
}

// 交易策略
export interface TradingStrategy {
  id: string
  name: string
  description: string
  type: 'trend_following' | 'mean_reversion' | 'arbitrage' | 'custom'
  
  // 策略参数
  parameters: {
    [key: string]: number | string | boolean
  }
  
  // 适用合约
  applicableContracts: FuturesType[]
  
  // 风险管理
  riskManagement: {
    maxPositionSize: number    // 最大仓位
    stopLossPercent: number    // 止损百分比
    takeProfitPercent: number  // 止盈百分比
    maxDailyLoss: number       // 最大日损失
  }
  
  // 回测结果
  backtestResults?: {
    totalReturn: number
    sharpeRatio: number
    maxDrawdown: number
    winRate: number
    profitFactor: number
  }
  
  // 状态
  isActive: boolean
  autoExecute: boolean
  
  metadata?: {
    createdBy: string
    createdAt: number
    lastUpdated: number
    tags: string[]
  }
}

// 期货交易成就
export interface TradingAchievement {
  id: string
  name: string
  description: string
  icon: string
  category: 'profit' | 'volume' | 'streak' | 'risk' | 'strategy'
  
  // 解锁条件
  requirements: {
    totalProfit?: number
    totalVolume?: number
    winStreak?: number
    totalTrades?: number
    maxDrawdown?: number
    riskScore?: number
    strategyUse?: string[]
  }
  
  // 奖励
  rewards: {
    experience: number
    currency: Record<CurrencyType, number>
    items?: LootboxItem[]
    unlocks?: string[]         // 解锁新功能
  }
  
  rarity: ItemRarity
  isHidden: boolean            // 隐藏成就
}

// 期货交易事件
export interface TradingEvent {
  id: string
  name: string
  description: string
  type: 'market_crash' | 'bull_run' | 'volatility_spike' | 'news_impact' | 'seasonal'
  
  // 事件影响
  effects: {
    contractIds: string[]      // 受影响的合约
    priceImpact: number        // 价格影响百分比
    volatilityMultiplier: number  // 波动率倍数
    volumeBonus: number        // 成交量加成
  }
  
  // 时间信息
  startTime: number
  endTime: number
  duration: number
  
  // 特殊奖励
  eventRewards?: {
    bonusMultiplier: number    // 奖励倍数
    specialItems?: LootboxItem[]
    limitedAchievements?: string[]
  }
  
  metadata?: {
    probability: number        // 发生概率
    severity: 'minor' | 'moderate' | 'major' | 'extreme'
    newsHeadline?: string
    backgroundStory?: string
  }
}

// 期货交易配置（中国农产品期货）
export const FUTURES_CONFIGS: Record<FuturesType, Partial<FuturesContract>> = {
  [FuturesType.WHEAT]: {
    symbol: 'WH',
    name: '强筋小麦',
    category: 'agricultural',
    contractSize: 20,
    tickSize: 1,
    initialMargin: 1500,
    maintenanceMargin: 1200,
    dailyPriceLimit: 0.05,
    volatility: 0.25
  },
  [FuturesType.CORN]: {
    symbol: 'C',
    name: '玉米',
    category: 'agricultural',
    contractSize: 10,
    tickSize: 1,
    initialMargin: 1200,
    maintenanceMargin: 960,
    dailyPriceLimit: 0.04,
    volatility: 0.22
  },
  [FuturesType.RICE]: {
    symbol: 'RI',
    name: '早籼稻',
    category: 'agricultural',
    contractSize: 20,
    tickSize: 1,
    initialMargin: 1000,
    maintenanceMargin: 800,
    dailyPriceLimit: 0.04,
    volatility: 0.20
  },
  [FuturesType.SOYBEAN]: {
    symbol: 'A',
    name: '大豆',
    category: 'agricultural',
    contractSize: 10,
    tickSize: 1,
    initialMargin: 2000,
    maintenanceMargin: 1600,
    dailyPriceLimit: 0.04,
    volatility: 0.28
  },
  [FuturesType.SOYBEAN_MEAL]: {
    symbol: 'M',
    name: '豆粕',
    category: 'agricultural',
    contractSize: 10,
    tickSize: 1,
    initialMargin: 1800,
    maintenanceMargin: 1440,
    dailyPriceLimit: 0.04,
    volatility: 0.30
  },
  [FuturesType.SOYBEAN_OIL]: {
    symbol: 'Y',
    name: '豆油',
    category: 'agricultural',
    contractSize: 10,
    tickSize: 2,
    initialMargin: 2200,
    maintenanceMargin: 1760,
    dailyPriceLimit: 0.04,
    volatility: 0.32
  },
  [FuturesType.COTTON]: {
    symbol: 'CF',
    name: '棉花',
    category: 'agricultural',
    contractSize: 5,
    tickSize: 5,
    initialMargin: 2500,
    maintenanceMargin: 2000,
    dailyPriceLimit: 0.04,
    volatility: 0.35
  },
  [FuturesType.WHITE_SUGAR]: {
    symbol: 'SR',
    name: '白糖',
    category: 'agricultural',
    contractSize: 10,
    tickSize: 1,
    initialMargin: 1600,
    maintenanceMargin: 1280,
    dailyPriceLimit: 0.04,
    volatility: 0.26
  },
  [FuturesType.RAPESEED_OIL]: {
    symbol: 'OI',
    name: '菜籽油',
    category: 'agricultural',
    contractSize: 10,
    tickSize: 2,
    initialMargin: 2000,
    maintenanceMargin: 1600,
    dailyPriceLimit: 0.04,
    volatility: 0.30
  },
  [FuturesType.RAPESEED_MEAL]: {
    symbol: 'RM',
    name: '菜籽粕',
    category: 'agricultural',
    contractSize: 10,
    tickSize: 1,
    initialMargin: 1400,
    maintenanceMargin: 1120,
    dailyPriceLimit: 0.04,
    volatility: 0.28
  },
  [FuturesType.PALM_OIL]: {
    symbol: 'P',
    name: '棕榈油',
    category: 'agricultural',
    contractSize: 10,
    tickSize: 2,
    initialMargin: 1800,
    maintenanceMargin: 1440,
    dailyPriceLimit: 0.04,
    volatility: 0.33
  },
  [FuturesType.EGG]: {
    symbol: 'JD',
    name: '鸡蛋',
    category: 'agricultural',
    contractSize: 5,
    tickSize: 1,
    initialMargin: 2200,
    maintenanceMargin: 1760,
    dailyPriceLimit: 0.05,
    volatility: 0.40
  },
  [FuturesType.FOCUS_INDEX]: {
    symbol: 'FI',
    name: '专注力指数',
    category: 'virtual',
    contractSize: 1,
    tickSize: 0.001,
    initialMargin: 100,
    maintenanceMargin: 75,
    dailyPriceLimit: 0.2,
    volatility: 0.4
  },
  [FuturesType.DISCIPLINE_INDEX]: {
    symbol: 'DI',
    name: '自律指数',
    category: 'virtual',
    contractSize: 1,
    tickSize: 0.001,
    initialMargin: 150,
    maintenanceMargin: 112.5,
    dailyPriceLimit: 0.18,
    volatility: 0.38
  }
}

// 交易奖励配置
export const TRADING_REWARDS = {
  // 基础交易奖励
  baseRewards: {
    experience: 10,
    currency: {
      [CurrencyType.FOCUS_COIN]: 5,
      [CurrencyType.DISCIPLINE_TOKEN]: 1
    }
  },
  
  // 盈利奖励倍数
  profitMultipliers: {
    small: 1.2,    // 小额盈利
    medium: 1.5,   // 中等盈利
    large: 2.0,    // 大额盈利
    huge: 3.0      // 巨额盈利
  },
  
  // 连胜奖励
  streakBonus: {
    3: 0.1,
    5: 0.2,
    10: 0.5,
    20: 1.0,
    50: 2.0
  },
  
  // 风险调整奖励
  riskAdjustedBonus: {
    conservative: 1.0,
    moderate: 1.1,
    aggressive: 1.3,
    extreme: 1.5
  }
} 