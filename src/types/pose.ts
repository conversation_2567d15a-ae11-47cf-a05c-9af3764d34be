// MediaPipe姿态检测类型定义

export interface PoseLandmark {
  x: number;  // 标准化坐标 (0-1)
  y: number;  // 标准化坐标 (0-1)
  z: number;  // 深度信息
  visibility?: number;  // 可见性 (0-1)，可选
}

// 使用MediaPipe原生的Results类型
export interface PoseResults {
  poseLandmarks?: any[];  // MediaPipe的NormalizedLandmarkList
  poseWorldLandmarks?: any[];
  multiFaceGeometry?: any;
}

export interface PostureAnalysis {
  isSeated: boolean;
  isFocused: boolean;
  focusScore: number;  // 0-100
  headPosition: 'center' | 'left' | 'right' | 'forward' | 'back';
  shoulderBalance: number;  // -1 到 1，0为平衡
  bodyLean: number;  // -1 到 1，0为直立
  warnings: string[];
}

// 增强版姿态分析配置
export interface EnhancedPoseConfig {
  // 基础阈值
  minVisibility: number
  focusThreshold: number
  
  // 时间序列配置  
  historySize: number
  smoothingFactor: number
  stabilityThreshold: number
  
  // 环境适应配置
  lightAdaptationEnabled: boolean
  angleAdaptationEnabled: boolean
  
  // 个人化配置
  personalizedLearningEnabled: boolean
  adaptationSpeed: number
  baselineCalibrationPeriod: number
}

// 历史数据点
export interface PostureDataPoint {
  timestamp: number
  focusScore: number
  headPosition: string
  shoulderBalance: number
  bodyLean: number
  confidence: number
  environmentalFactors: {
    lighting: {
      level: number
      quality: 'poor' | 'fair' | 'good' | 'excellent'
      uniformity: number
      shadows: boolean
    }
    camera: {
      angle: number
      distance: number
      stability: number
      resolution: 'low' | 'medium' | 'high'
    }
    background: {
      complexity: number
      contrast: number
      motion: boolean
    }
    pose: {
      visibility: number
      occlusion: number
      clarity: number
    }
  }
}

// 个人化基线数据
export interface PersonalBaseline {
  avgHeadOffset: { x: number; y: number; z: number }
  avgShoulderBalance: number
  avgBodyLean: number
  preferredPosture: {
    headPosition: string
    shoulderTolerance: number
    bodyLeanTolerance: number
  }
  calibrationCount: number
  lastCalibration: number
}

// 增强版分析结果
export interface EnhancedPostureAnalysis extends PostureAnalysis {
  confidence: number
  stability: number
  trend: 'improving' | 'stable' | 'declining'
  personalizedScore: number
  environmentalFactors: {
    lighting: {
      level: number
      quality: 'poor' | 'fair' | 'good' | 'excellent'
      uniformity: number
      shadows: boolean
    }
    camera: {
      angle: number
      distance: number
      stability: number
      resolution: 'low' | 'medium' | 'high'
    }
    background: {
      complexity: number
      contrast: number
      motion: boolean
    }
    pose: {
      visibility: number
      occlusion: number
      clarity: number
    }
  }
  recommendations: string[]
  headOffset?: { x: number; y: number; z: number }
}

// 姿态检测错误类型
export interface PoseDetectionError {
  message: string;
  code?: string;
  name?: string;
}

export interface PoseDetectionState {
  isActive: boolean;
  isLoading: boolean;
  error: PoseDetectionError | null;
  currentPose: PoseResults | null;
  postureAnalysis: PostureAnalysis | null;
  statistics: {
    totalDetections: number;
    goodPostureCount: number;
    averageFocusScore: number;
    sessionDuration: number;  // 毫秒
  };
}

// MediaPipe姿态关键点索引
export enum PoseLandmarkIndex {
  NOSE = 0,
  LEFT_EYE_INNER = 1,
  LEFT_EYE = 2,
  LEFT_EYE_OUTER = 3,
  RIGHT_EYE_INNER = 4,
  RIGHT_EYE = 5,
  RIGHT_EYE_OUTER = 6,
  LEFT_EAR = 7,
  RIGHT_EAR = 8,
  MOUTH_LEFT = 9,
  MOUTH_RIGHT = 10,
  LEFT_SHOULDER = 11,
  RIGHT_SHOULDER = 12,
  LEFT_ELBOW = 13,
  RIGHT_ELBOW = 14,
  LEFT_WRIST = 15,
  RIGHT_WRIST = 16,
  LEFT_PINKY = 17,
  RIGHT_PINKY = 18,
  LEFT_INDEX = 19,
  RIGHT_INDEX = 20,
  LEFT_THUMB = 21,
  RIGHT_THUMB = 22,
  LEFT_HIP = 23,
  RIGHT_HIP = 24,
  LEFT_KNEE = 25,
  RIGHT_KNEE = 26,
  LEFT_ANKLE = 27,
  RIGHT_ANKLE = 28,
  LEFT_HEEL = 29,
  RIGHT_HEEL = 30,
  LEFT_FOOT_INDEX = 31,
  RIGHT_FOOT_INDEX = 32
}

export interface PoseDetectionConfig {
  modelComplexity: 0 | 1 | 2;  // 模型复杂度
  smoothLandmarks: boolean;     // 平滑关键点
  enableSegmentation: boolean;  // 启用分割
  smoothSegmentation: boolean;  // 平滑分割
  minDetectionConfidence: number;  // 最小检测置信度
  minTrackingConfidence: number;   // 最小跟踪置信度
} 