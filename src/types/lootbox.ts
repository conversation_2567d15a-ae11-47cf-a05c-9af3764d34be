import { CurrencyType } from './currency'

// 导入新的道具品种
import { AgriculturalVariety, IndustrialVariety, EquipmentType } from './enhanced-items'

// 物品品质等级
export enum ItemRarity {
  GRAY = 'gray',         // 灰色 - 普通
  GREEN = 'green',       // 绿色 - 优秀
  BLUE = 'blue',         // 蓝色 - 稀有
  ORANGE = 'orange',     // 橙色 - 史诗
  GOLD = 'gold',         // 金色 - 传说
  GOLD_RED = 'gold_red'  // 金红色 - 神话
}

// 物品类别
export enum ItemCategory {
  // 农产品线
  AGRICULTURAL = 'agricultural',
  // 工业品线
  INDUSTRIAL = 'industrial',
  CROP = 'crop', // 添加CROP类别
  LIVESTOCK = 'livestock' // 添加LIVESTOCK类别
}

// 物品类型
export enum ItemType {
  // 农产品类型
  SEED = 'seed',           // 种子
  CROP = 'crop',           // 作物
  LIVESTOCK = 'livestock', // 牲畜
  FARM_TOOL = 'farm_tool', // 农具
  FARM_BUILDING = 'farm_building', // 农业建筑
  
  // 工业品类型
  RAW_MATERIAL = 'raw_material',   // 原材料
  COMPONENT = 'component',         // 组件
  MACHINERY = 'machinery',         // 机械
  PRODUCT = 'product',             // 产品
  FACTORY_BUILDING = 'factory_building', // 工厂建筑
  
  // 通用类型
  CURRENCY = 'currency',           // 货币
  BOOST = 'boost',                 // 增益道具
  DECORATION = 'decoration'        // 装饰品
}

// 盲盒物品
export interface LootboxItem {
  id: string
  name: string
  description: string
  category: ItemCategory
  type: ItemType
  rarity: ItemRarity
  icon: string
  value: number
  stackable: boolean
  tradeable: boolean
  synthesizable: boolean
  metadata: {
    // 农产品属性
    yieldMultiplier?: number    // 产量倍数
    growthSpeed?: number        // 成长速度
    qualityBonus?: number       // 品质加成
    
    // 工业品属性
    productionSpeed?: number    // 生产速度
    efficiency?: number         // 效率加成
    capacity?: number           // 容量
    
    // 合成配方ID
    synthesisRecipes?: string[]
    
    // 特殊效果
    effects?: string[]
    duration?: number           // 持续时间（毫秒）
    
    // 期货相关
    futuresPrice?: number       // 期货价格
    priceVolatility?: number    // 价格波动率
    marketDemand?: number       // 市场需求
  }
}

// 盲盒类型
export enum LootboxType {
  // 农产品系列
  BASIC_FARM = 'basic_farm',         // 基础农场盒
  PREMIUM_FARM = 'premium_farm',     // 高级农场盒
  LEGENDARY_FARM = 'legendary_farm', // 传说农场盒
  
  // 工业品系列
  BASIC_INDUSTRIAL = 'basic_industrial',         // 基础工业盒
  PREMIUM_INDUSTRIAL = 'premium_industrial',     // 高级工业盒
  LEGENDARY_INDUSTRIAL = 'legendary_industrial', // 传说工业盒
  
  // 混合系列
  FUTURES_MYSTERY = 'futures_mystery',           // 期货神秘盒
  GOLDEN_TREASURE = 'golden_treasure',           // 金色宝藏盒
  SYNTHESIS_BOX = 'synthesis_box'                // 合成专用盒
}

// 盲盒配置
export interface LootboxConfig {
  id: LootboxType
  name: string
  description: string
  category: ItemCategory | 'mixed'
  icon: string
  price: {
    currency: CurrencyType
    amount: number
  }
  guaranteedRarity?: ItemRarity  // 保底品质
  dropRates: {
    [ItemRarity.GRAY]: number
    [ItemRarity.GREEN]: number
    [ItemRarity.BLUE]: number
    [ItemRarity.ORANGE]: number
    [ItemRarity.GOLD]: number
    [ItemRarity.GOLD_RED]: number
  }
  itemPool: (AgriculturalVariety | IndustrialVariety | EquipmentType)[]  // 可能开出的物品品种列表
  specialFeatures?: {
    guaranteedCount?: number      // 保底数量
    pityTimer?: number           // 同情计时器
    bonusChance?: number         // 额外奖励几率
    limitedTime?: boolean        // 限时盒子
  }
}

// 开盒结果
export interface LootboxResult {
  lootboxType: LootboxType
  items: {
    item: LootboxItem
    quantity: number
    isGuaranteed: boolean
    isBonus: boolean
  }[]
  totalValue: number
  rarityBreakdown: Record<ItemRarity, number>
  openedAt: number
  cost: {
    currency: CurrencyType
    amount: number
  }
}

// 开盒历史
export interface LootboxHistory {
  userId: string
  totalOpened: number
  totalSpent: Record<CurrencyType, number>
  totalValue: number
  rarityStats: Record<ItemRarity, number>
  recentOpens: LootboxResult[]
  pityCounters: Record<LootboxType, number>
}

// 品质配色方案
export const RARITY_COLORS: Record<ItemRarity, string> = {
  [ItemRarity.GRAY]: '#9E9E9E',      // 灰色
  [ItemRarity.GREEN]: '#4CAF50',     // 绿色
  [ItemRarity.BLUE]: '#2196F3',      // 蓝色
  [ItemRarity.ORANGE]: '#FF9800',    // 橙色
  [ItemRarity.GOLD]: '#FFD700',      // 金色
  [ItemRarity.GOLD_RED]: '#FF6B6B'   // 金红色
}

// 品质权重（用于抽取概率计算）
export const RARITY_WEIGHTS: Record<ItemRarity, number> = {
  [ItemRarity.GRAY]: 1000,      // 60%
  [ItemRarity.GREEN]: 500,      // 30%
  [ItemRarity.BLUE]: 120,       // 7.2%
  [ItemRarity.ORANGE]: 25,      // 1.5%
  [ItemRarity.GOLD]: 8,         // 0.8%
  [ItemRarity.GOLD_RED]: 2      // 0.2%
} 