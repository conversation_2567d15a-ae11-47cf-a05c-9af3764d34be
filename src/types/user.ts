// 用户数据存储系统类型定义

// 用户档案接口
export interface UserProfile {
  id: string
  username: string
  email?: string
  createdAt: number
  lastLoginAt: number
  preferences: UserPreferences
  statistics: UserStatistics
}

// 用户偏好设置
export interface UserPreferences {
  language: string
  theme: 'light' | 'dark' | 'auto'
  notifications: {
    gameProgress: boolean
    achievements: boolean
    dailyReminders: boolean
  }
  accessibility: {
    highContrast: boolean
    fontSize: 'small' | 'medium' | 'large'
    reducedMotion: boolean
  }
  gameplay: {
    autoSave: boolean
    autoSaveInterval: number // 分钟
    showTutorials: boolean
    soundEnabled: boolean
    musicEnabled: boolean
    effectsVolume: number // 0-100
    musicVolume: number // 0-100
  }
}

// 用户统计数据
export interface UserStatistics {
  totalPlayTime: number // 毫秒
  sessionsPlayed: number
  achievementsUnlocked: number
  totalCropsPlanted: number
  totalCropsHarvested: number
  totalExperienceGained: number
  maxLevel: number
  focusStats: FocusStatistics
  lastUpdated: number
}

// 专注度统计
export interface FocusStatistics {
  totalFocusTime: number // 毫秒
  averageFocusScore: number
  bestFocusStreak: number // 毫秒
  totalFocusBreaks: number
  focusSessionsCompleted: number
  lastFocusSession: number
}

// 游戏进度数据
export interface GameProgress {
  userId: string
  gameId: string
  level: number
  experience: number
  gameTime: number // 毫秒
  farmGrid: Record<string, string> // 位置ID -> 作物ID
  crops: Record<string, CropProgressData>
  inventory: InventoryData
  achievements: Achievement[]
  currentMission?: Mission
  lastSaved: number
  version: string // 游戏版本
}

// 作物进度数据
export interface CropProgressData {
  id: string
  type: string
  stage: string
  plantedAt: number
  lastUpdateTime: number
  position: { x: number; y: number }
  quality: number
  focusTimeReceived: number
  growthMultiplier: number
}

// 库存数据
export interface InventoryData {
  seeds: Record<string, number> // 种子类型 -> 数量
  harvested: Record<string, number> // 收获物类型 -> 数量
  tools: Record<string, ToolData>
  currency: number
}

// 工具数据
export interface ToolData {
  id: string
  type: string
  level: number
  durability: number
  lastUsed: number
}

// 成就数据
export interface Achievement {
  id: string
  name: string
  description: string
  unlockedAt: number
  progress: number
  maxProgress: number
  category: 'farming' | 'focus' | 'progress' | 'social'
}

// 任务数据
export interface Mission {
  id: string
  title: string
  description: string
  objectives: MissionObjective[]
  reward: MissionReward
  startedAt: number
  deadline?: number
  status: 'active' | 'completed' | 'failed'
}

// 任务目标
export interface MissionObjective {
  id: string
  description: string
  type: 'plant' | 'harvest' | 'focus' | 'level'
  target: number
  current: number
  completed: boolean
}

// 任务奖励
export interface MissionReward {
  experience: number
  currency: number
  items?: Record<string, number>
  achievements?: string[]
}

// 行为统计记录
export interface BehaviorRecord {
  id: string
  userId: string
  timestamp: number
  event: BehaviorEvent
  metadata: Record<string, any>
}

// 行为事件类型
export interface BehaviorEvent {
  type: 'game_start' | 'game_end' | 'crop_plant' | 'crop_harvest' | 'focus_session' | 'level_up' | 'achievement_unlock'
  category: 'gameplay' | 'focus' | 'progress' | 'system'
  value?: number
  details?: string
}

// 存储配置
export interface StorageConfig {
  encryptionEnabled: boolean
  compressionEnabled: boolean
  backupEnabled: boolean
  backupInterval: number // 小时
  maxBackupFiles: number
  dataRetentionDays: number
}

// 数据备份信息
export interface BackupInfo {
  id: string
  timestamp: number
  size: number // 字节
  checksum: string
  version: string
  description?: string
}

// 数据完整性检查结果
export interface DataIntegrityCheck {
  timestamp: number
  isValid: boolean
  errors: string[]
  warnings: string[]
  checkedTables: string[]
}

// 数据库实体关系
export interface UserDataRelations {
  profile: UserProfile
  progress: GameProgress[]
  behaviors: BehaviorRecord[]
  backups: BackupInfo[]
} 