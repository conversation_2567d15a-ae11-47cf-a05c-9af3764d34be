import { WeatherType, WeatherIntensity } from './weather';

// 粒子效果配置
export interface ParticleEffectConfig {
  particleCount: number;
  speed: { min: number; max: number };
  scale: { min: number; max: number };
  alpha: { min: number; max: number };
  lifespan: number;
  emissionRate: number;
  gravity?: { x: number; y: number };
  direction?: { min: number; max: number };
  frequency?: number;
  texture?: string;
  tint?: number;
  blend?: Phaser.BlendModes;
}

// 动画效果配置
export interface AnimationConfig {
  duration: number;
  ease?: string;
  repeat?: number;
  yoyo?: boolean;
  delay?: number;
  onComplete?: () => void;
}

// 背景效果配置
export interface BackgroundEffectConfig {
  skyGradient: {
    topColor: number;
    bottomColor: number;
  };
  fogOverlay?: {
    alpha: number;
    color: number;
  };
  lightingLevel: number; // 0-1
  ambientTint?: number;
}

// 环境音效配置
export interface EnvironmentSoundConfig {
  volume: number;
  loop: boolean;
  fade?: {
    duration: number;
    from: number;
    to: number;
  };
}

// 天气视觉效果配置
export interface WeatherVisualConfig {
  weatherType: WeatherType;
  intensity: WeatherIntensity;
  particles?: ParticleEffectConfig[];
  background: BackgroundEffectConfig;
  animations?: {
    clouds?: AnimationConfig;
    wind?: AnimationConfig;
    lightning?: AnimationConfig;
  };
  sounds?: Record<string, EnvironmentSoundConfig>;
  transitionDuration: number; // 切换到此天气的过渡时间
}

// 天气过渡效果
export interface WeatherTransitionConfig {
  fromWeather: WeatherType;
  toWeather: WeatherType;
  duration: number;
  ease: string;
  particleTransition?: {
    crossFadeDuration: number;
    overlayDuration: number;
  };
  backgroundTransition?: {
    colorBlendDuration: number;
    lightingBlendDuration: number;
  };
}

// 视觉效果质量设置
export enum VisualQuality {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  ULTRA = 'ultra'
}

// 视觉效果性能配置
export interface VisualPerformanceConfig {
  quality: VisualQuality;
  maxParticles: number;
  enableAdvancedEffects: boolean;
  enablePostProcessing: boolean;
  particlePoolSize: number;
  updateFrequency: number; // 更新频率 (ms)
}

// 天气视觉效果事件
export interface WeatherVisualEvent {
  type: 'weather_visual_start' | 'weather_visual_complete' | 'weather_transition_start' | 'weather_transition_complete';
  weatherType: WeatherType;
  intensity: WeatherIntensity;
  timestamp: Date;
  duration?: number;
}

// 闪电效果配置
export interface LightningConfig extends AnimationConfig {
  branches: number;
  thickness: number;
  color: number;
  flickerCount: number;
  illuminationRadius: number;
  soundDelay: number; // 雷声延迟
}

// 风效果配置
export interface WindEffectConfig {
  strength: number; // 0-1
  direction: number; // 角度
  gustFrequency: number; // 阵风频率
  swayTargets: string[]; // 受影响的对象选择器
  leafParticles?: ParticleEffectConfig;
}

// 雾效果配置
export interface FogEffectConfig {
  density: number; // 0-1
  color: number;
  height: number; // 雾的高度范围
  movement: {
    speed: number;
    direction: number;
  };
  visibility: number; // 能见度 0-1
}

// 季节视觉调整
export interface SeasonalVisualAdjust {
  spring: {
    colorTint: number;
    brightness: number;
    particleBonus: number;
  };
  summer: {
    colorTint: number;
    brightness: number;
    particleBonus: number;
  };
  autumn: {
    colorTint: number;
    brightness: number;
    particleBonus: number;
  };
  winter: {
    colorTint: number;
    brightness: number;
    particleBonus: number;
  };
}

// 天气视觉管理器状态
export interface WeatherVisualManagerState {
  currentWeather: WeatherType;
  currentIntensity: WeatherIntensity;
  isTransitioning: boolean;
  transitionProgress: number;
  activeEffects: Map<string, Phaser.GameObjects.GameObject>;
  particleEmitters: Map<string, Phaser.GameObjects.Particles.ParticleEmitter>;
  performanceConfig: VisualPerformanceConfig;
  seasonalAdjustment: SeasonalVisualAdjust;
}

// 预设天气视觉配置
export const WEATHER_VISUAL_PRESETS: Record<WeatherType, WeatherVisualConfig> = {
  [WeatherType.SUNNY]: {
    weatherType: WeatherType.SUNNY,
    intensity: WeatherIntensity.LIGHT,
    particles: [
      {
        particleCount: 50,
        speed: { min: 10, max: 30 },
        scale: { min: 0.1, max: 0.3 },
        alpha: { min: 0.3, max: 0.7 },
        lifespan: 3000,
        emissionRate: 10,
        texture: 'light_particle',
        tint: 0xFFD700,
        blend: Phaser.BlendModes.ADD
      }
    ],
    background: {
      skyGradient: {
        topColor: 0x87CEEB,
        bottomColor: 0xE0F6FF
      },
      lightingLevel: 1.0,
      ambientTint: 0xFFFFE0
    },
    animations: {
      clouds: {
        duration: 20000,
        ease: 'Linear',
        repeat: -1
      }
    },
    transitionDuration: 2000
  },
  [WeatherType.PARTLY_CLOUDY]: {
    weatherType: WeatherType.PARTLY_CLOUDY,
    intensity: WeatherIntensity.LIGHT,
    background: {
      skyGradient: {
        topColor: 0x87CEEB,
        bottomColor: 0xF0F8FF
      },
      lightingLevel: 0.8,
      ambientTint: 0xF5F5DC
    },
    animations: {
      clouds: {
        duration: 15000,
        ease: 'Linear',
        repeat: -1
      }
    },
    transitionDuration: 1500
  },
  [WeatherType.CLOUDY]: {
    weatherType: WeatherType.CLOUDY,
    intensity: WeatherIntensity.MODERATE,
    background: {
      skyGradient: {
        topColor: 0x708090,
        bottomColor: 0xD3D3D3
      },
      lightingLevel: 0.6,
      ambientTint: 0xD3D3D3
    },
    animations: {
      clouds: {
        duration: 12000,
        ease: 'Linear',
        repeat: -1
      }
    },
    transitionDuration: 2000
  },
  [WeatherType.RAINY]: {
    weatherType: WeatherType.RAINY,
    intensity: WeatherIntensity.MODERATE,
    particles: [
      {
        particleCount: 200,
        speed: { min: 100, max: 150 },
        scale: { min: 0.5, max: 1.0 },
        alpha: { min: 0.6, max: 0.9 },
        lifespan: 1000,
        emissionRate: 150,
        gravity: { x: -20, y: 200 },
        texture: 'rain_drop',
        tint: 0x6495ED
      }
    ],
    background: {
      skyGradient: {
        topColor: 0x2F4F4F,
        bottomColor: 0x696969
      },
      lightingLevel: 0.4,
      ambientTint: 0x708090
    },
    transitionDuration: 2500
  },
  [WeatherType.HEAVY_RAIN]: {
    weatherType: WeatherType.HEAVY_RAIN,
    intensity: WeatherIntensity.HEAVY,
    particles: [
      {
        particleCount: 400,
        speed: { min: 150, max: 250 },
        scale: { min: 0.7, max: 1.2 },
        alpha: { min: 0.7, max: 1.0 },
        lifespan: 800,
        emissionRate: 300,
        gravity: { x: -40, y: 300 },
        texture: 'rain_drop',
        tint: 0x4682B4
      }
    ],
    background: {
      skyGradient: {
        topColor: 0x191970,
        bottomColor: 0x2F4F4F
      },
      fogOverlay: {
        alpha: 0.3,
        color: 0x778899
      },
      lightingLevel: 0.2,
      ambientTint: 0x483D8B
    },
    transitionDuration: 3000
  },
  [WeatherType.THUNDERSTORM]: {
    weatherType: WeatherType.THUNDERSTORM,
    intensity: WeatherIntensity.EXTREME,
    particles: [
      {
        particleCount: 500,
        speed: { min: 200, max: 300 },
        scale: { min: 0.8, max: 1.5 },
        alpha: { min: 0.8, max: 1.0 },
        lifespan: 600,
        emissionRate: 400,
        gravity: { x: -60, y: 400 },
        texture: 'rain_drop',
        tint: 0x4169E1
      }
    ],
    background: {
      skyGradient: {
        topColor: 0x0F0F23,
        bottomColor: 0x1C1C3A
      },
      fogOverlay: {
        alpha: 0.4,
        color: 0x2F2F2F
      },
      lightingLevel: 0.1,
      ambientTint: 0x191970
    },
    animations: {
      lightning: {
        duration: 100,
        ease: 'Power2.easeOut',
        repeat: 3,
        delay: 2000
      }
    },
    transitionDuration: 3500
  },
  [WeatherType.SNOWY]: {
    weatherType: WeatherType.SNOWY,
    intensity: WeatherIntensity.MODERATE,
    particles: [
      {
        particleCount: 150,
        speed: { min: 30, max: 80 },
        scale: { min: 0.3, max: 0.8 },
        alpha: { min: 0.7, max: 1.0 },
        lifespan: 4000,
        emissionRate: 50,
        gravity: { x: -5, y: 50 },
        texture: 'snowflake',
        tint: 0xFFFAFA
      }
    ],
    background: {
      skyGradient: {
        topColor: 0xB0C4DE,
        bottomColor: 0xF0F8FF
      },
      lightingLevel: 0.7,
      ambientTint: 0xF0F8FF
    },
    transitionDuration: 2500
  },
  [WeatherType.FOGGY]: {
    weatherType: WeatherType.FOGGY,
    intensity: WeatherIntensity.MODERATE,
    background: {
      skyGradient: {
        topColor: 0x708090,
        bottomColor: 0xC0C0C0
      },
      fogOverlay: {
        alpha: 0.6,
        color: 0xD3D3D3
      },
      lightingLevel: 0.3,
      ambientTint: 0xD3D3D3
    },
    transitionDuration: 4000
  },
  [WeatherType.WINDY]: {
    weatherType: WeatherType.WINDY,
    intensity: WeatherIntensity.MODERATE,
    particles: [
      {
        particleCount: 100,
        speed: { min: 80, max: 120 },
        scale: { min: 0.2, max: 0.5 },
        alpha: { min: 0.3, max: 0.6 },
        lifespan: 2000,
        emissionRate: 30,
        direction: { min: -30, max: 30 },
        texture: 'leaf_particle',
        tint: 0x90EE90
      }
    ],
    background: {
      skyGradient: {
        topColor: 0x87CEEB,
        bottomColor: 0xE0F6FF
      },
      lightingLevel: 0.9,
      ambientTint: 0xF0FFFF
    },
    animations: {
      wind: {
        duration: 500,
        ease: 'Sine.easeInOut',
        repeat: -1,
        yoyo: true
      }
    },
    transitionDuration: 2000
  }
}; 