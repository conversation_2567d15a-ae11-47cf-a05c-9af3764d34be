import { ItemRarity, ItemCategory, ItemType } from './lootbox';

// 背包物品信息
export interface InventoryItem {
  id: string;
  itemId: string; // 对应 LootboxItem 的 id
  name: string;
  rarity: ItemRarity;
  category: ItemCategory;
  type: ItemType;
  description: string;
  icon: string;
  quantity: number; // 数量
  obtainedAt: number; // 获得时间戳
}

// 背包状态
export interface InventoryState {
  items: InventoryItem[];
  maxSlots: number; // 最大背包槽位
  usedSlots: number; // 已使用槽位
}

// 合成配方
export interface SynthesisRecipe {
  id: string;
  name: string;
  description: string;
  requiredItems: {
    rarity: ItemRarity;
    quantity: number;
    category?: ItemCategory; // 可选，指定类别
  }[];
  resultRarity: ItemRarity;
  successRate: number; // 成功率 0-1
}

// 合成结果
export interface SynthesisResult {
  success: boolean;
  resultItem?: InventoryItem;
  consumedItems: InventoryItem[];
  message: string;
}

// 品质等级映射
export const RARITY_LEVELS: Record<ItemRarity, number> = {
  [ItemRarity.GRAY]: 1,
  [ItemRarity.GREEN]: 2,
  [ItemRarity.BLUE]: 3,
  [ItemRarity.ORANGE]: 4,
  [ItemRarity.GOLD]: 5,
  [ItemRarity.GOLD_RED]: 6
};

// 品质名称映射
export const RARITY_NAMES: Record<ItemRarity, string> = {
  [ItemRarity.GRAY]: '灰色',
  [ItemRarity.GREEN]: '绿色',
  [ItemRarity.BLUE]: '蓝色',
  [ItemRarity.ORANGE]: '橙色',
  [ItemRarity.GOLD]: '金色',
  [ItemRarity.GOLD_RED]: '金红色'
};

// RARITY_COLORS 现在从 lootbox.ts 导入，避免重复定义 