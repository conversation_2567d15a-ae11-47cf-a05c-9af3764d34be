// 天气类型枚举
export enum WeatherType {
  SUNNY = 'sunny',
  PARTLY_CLOUDY = 'partly_cloudy',
  CLOUDY = 'cloudy',
  RAINY = 'rainy',
  HEAVY_RAIN = 'heavy_rain',
  THUNDERSTORM = 'thunderstorm',
  SNOWY = 'snowy',
  FOGGY = 'foggy',
  WINDY = 'windy'
}

// 天气强度等级
export enum WeatherIntensity {
  LIGHT = 'light',
  MODERATE = 'moderate',
  HEAVY = 'heavy',
  EXTREME = 'extreme'
}

// 天气变化趋势
export enum WeatherTrend {
  IMPROVING = 'improving',
  STABLE = 'stable',
  DETERIORATING = 'deteriorating'
}

// 时间段枚举
export enum TimeOfDay {
  DAWN = 'dawn',
  MORNING = 'morning',
  NOON = 'noon',
  AFTERNOON = 'afternoon',
  EVENING = 'evening',
  NIGHT = 'night',
  MIDNIGHT = 'midnight'
}

// 天气状态接口
export interface WeatherState {
  type: WeatherType;
  intensity: WeatherIntensity;
  temperature: number; // 摄氏度
  humidity: number; // 0-100%
  windSpeed: number; // km/h
  visibility: number; // km
  pressure: number; // hPa
  timeOfDay: TimeOfDay;
  timestamp: Date;
}

// 天气效果接口
export interface WeatherEffect {
  // 对专注训练的影响
  focusMultiplier: number; // 专注度影响倍数 (0.5-2.0)
  comfortLevel: number; // 舒适度 (0-10)
  moodBonus: number; // 心情加成 (-5 到 +5)
  difficultyModifier: number; // 难度修正 (-2 到 +2)
  
  // 视觉效果配置
  backgroundFilter: string; // CSS滤镜
  particleEffect?: ParticleConfig;
  lightingEffect?: LightingConfig;
  
  // 音效配置
  ambientSound?: string;
  soundVolume: number; // 0-1
  
  // 特殊效果
  specialEffects?: SpecialEffect[];
}

// 粒子效果配置
export interface ParticleConfig {
  type: 'rain' | 'snow' | 'leaves' | 'dust';
  count: number;
  speed: number;
  size: number;
  opacity: number;
  direction: number; // 角度
  color?: string;
}

// 光照效果配置
export interface LightingConfig {
  brightness: number; // 0-1
  contrast: number; // 0-2
  saturation: number; // 0-2
  hue: number; // 0-360
  warmth: number; // 色温调整 -1到1
}

// 特殊效果
export interface SpecialEffect {
  type: 'lightning' | 'rainbow' | 'aurora' | 'fog_overlay';
  probability: number; // 0-1，出现概率
  duration: number; // 持续时间（秒）
  intensity: number; // 强度 0-1
}

// 天气预报项
export interface WeatherForecast {
  time: Date;
  weatherState: WeatherState;
  confidence: number; // 预测可信度 0-1
  description: string;
}

// 天气变化事件
export interface WeatherChangeEvent {
  id: string;
  previousWeather: WeatherState;
  newWeather: WeatherState;
  changeReason: WeatherChangeReason;
  duration: number; // 变化持续时间（分钟）
  timestamp: Date;
}

// 天气变化原因
export enum WeatherChangeReason {
  NATURAL_CYCLE = 'natural_cycle',
  USER_FOCUS_LEVEL = 'user_focus_level',
  TRAINING_PROGRESS = 'training_progress',
  ACHIEVEMENT_UNLOCK = 'achievement_unlock',
  TIME_BASED = 'time_based',
  RANDOM_EVENT = 'random_event',
  SEASONAL_CHANGE = 'seasonal_change',
  USER_PREFERENCE = 'user_preference'
}

// 天气偏好设置
export interface WeatherPreferences {
  favoriteWeather: WeatherType[];
  avoidWeather: WeatherType[];
  autoAdjustBasedOnFocus: boolean;
  enableDynamicWeather: boolean;
  weatherChangeFrequency: 'low' | 'medium' | 'high';
  enableSoundEffects: boolean;
  soundVolume: number;
  enableVisualEffects: boolean;
  visualEffectIntensity: 'subtle' | 'normal' | 'dramatic';
}

// 天气统计数据
export interface WeatherStats {
  totalTimeInWeather: Record<WeatherType, number>; // 分钟
  favoriteWeatherByFocus: WeatherType;
  mostProductiveWeather: WeatherType;
  weatherChangesTriggered: number;
  averageFocusInWeather: Record<WeatherType, number>;
  weatherStreaks: Record<WeatherType, number>; // 连续天数
}

// 季节枚举
export enum Season {
  SPRING = 'spring',
  SUMMER = 'summer',
  AUTUMN = 'autumn',
  WINTER = 'winter'
}

// 季节配置
export interface SeasonConfig {
  season: Season;
  temperatureRange: [number, number]; // [最低, 最高]
  commonWeatherTypes: WeatherType[];
  weatherProbabilities: Record<WeatherType, number>;
  dayLength: number; // 小时
  specialEvents?: SeasonalEvent[];
}

// 季节性事件
export interface SeasonalEvent {
  name: string;
  description: string;
  startDate: string; // MM-DD格式
  endDate: string;
  weatherOverride?: WeatherType;
  specialEffects?: SpecialEffect[];
  achievementBonus?: number;
}

// 天气系统配置
export interface WeatherSystemConfig {
  enableRealTimeWeather: boolean;
  enableSeasonalChanges: boolean;
  weatherUpdateInterval: number; // 分钟
  transitionDuration: number; // 秒
  maxWeatherIntensity: WeatherIntensity;
  enableWeatherNotifications: boolean;
  autoSaveWeatherData: boolean;
  weatherHistoryRetentionDays: number;
}

// 天气管理器状态
export interface WeatherManagerState {
  currentWeather: WeatherState;
  currentSeason: Season;
  nextWeatherChange?: Date;
  isTransitioning: boolean;
  transitionProgress: number; // 0-1
  weatherHistory: WeatherState[];
  forecast: WeatherForecast[];
  preferences: WeatherPreferences;
  stats: WeatherStats;
  config: WeatherSystemConfig;
}

// 天气条件匹配器
export interface WeatherCondition {
  weatherTypes?: WeatherType[];
  intensityRange?: [WeatherIntensity, WeatherIntensity];
  temperatureRange?: [number, number];
  timeOfDay?: TimeOfDay[];
  season?: Season[];
  customMatcher?: (weather: WeatherState) => boolean;
}

// 天气触发器
export interface WeatherTrigger {
  id: string;
  name: string;
  description: string;
  condition: WeatherCondition;
  action: WeatherTriggerAction;
  priority: number;
  isActive: boolean;
  cooldownMinutes?: number;
  lastTriggered?: Date;
}

// 天气触发器动作
export interface WeatherTriggerAction {
  type: 'change_weather' | 'play_sound' | 'show_notification' | 'unlock_achievement' | 'adjust_difficulty';
  payload: any;
  delay?: number; // 秒
}

// 天气事件监听器类型
export type WeatherEventListener = (event: WeatherChangeEvent) => void;

// 导出所有天气相关的常量
export const WEATHER_CONSTANTS = {
  // 默认天气持续时间（分钟）
  DEFAULT_WEATHER_DURATION: {
    [WeatherType.SUNNY]: 120,
    [WeatherType.PARTLY_CLOUDY]: 90,
    [WeatherType.CLOUDY]: 60,
    [WeatherType.RAINY]: 45,
    [WeatherType.HEAVY_RAIN]: 30,
    [WeatherType.THUNDERSTORM]: 20,
    [WeatherType.SNOWY]: 60,
    [WeatherType.FOGGY]: 40,
    [WeatherType.WINDY]: 50
  },
  
  // 天气变化概率
  WEATHER_TRANSITION_PROBABILITIES: {
    [WeatherType.SUNNY]: {
      [WeatherType.PARTLY_CLOUDY]: 0.3,
      [WeatherType.CLOUDY]: 0.1,
      [WeatherType.SUNNY]: 0.6
    },
    [WeatherType.PARTLY_CLOUDY]: {
      [WeatherType.SUNNY]: 0.4,
      [WeatherType.CLOUDY]: 0.4,
      [WeatherType.RAINY]: 0.1,
      [WeatherType.PARTLY_CLOUDY]: 0.1
    },
    // ... 可以继续扩展其他天气类型的转换概率
  },
  
  // 温度范围
  TEMPERATURE_RANGES: {
    [Season.SPRING]: [-5, 25],
    [Season.SUMMER]: [15, 35],
    [Season.AUTUMN]: [0, 20],
    [Season.WINTER]: [-15, 10]
  },
  
  // 更新间隔
  UPDATE_INTERVALS: {
    WEATHER_CHECK: 30000, // 30秒
    FORECAST_UPDATE: 300000, // 5分钟
    STATS_UPDATE: 60000, // 1分钟
    ANIMATION_FRAME: 16 // ~60fps
  }
} as const; 