// 期货游戏道具系统类型定义
export enum Quality {
  COMMON = 'common',      // 普通
  GOOD = 'good',          // 优质 
  RARE = 'rare',          // 稀有
  EPIC = 'epic',          // 史诗
  LEGENDARY = 'legendary' // 传说
}

export enum ItemCategory {
  AGRICULTURAL = 'agricultural', // 农业产品
  INDUSTRIAL = 'industrial',     // 工业产品
  EQUIPMENT = 'equipment'        // 装备类道具
}

// 基础道具接口
export interface BaseItem {
  id: string
  name: string
  description: string
  category: ItemCategory
  quality: Quality
  icon: string
  baseValue: number
  stackable: boolean
  tradeable: boolean
  obtainedAt?: number
}

// 农业产品道具
export interface AgriculturalItem extends BaseItem {
  category: ItemCategory.AGRICULTURAL
  variety: AgriculturalVariety
  production: {
    minDaily: number  // 最低日产量
    maxDaily: number  // 最高日产量
    currentRate: number // 当前产率倍数
  }
  futuresCode: string // 期货代码
  seasonalBonus?: number // 季节加成
}

// 工业产品道具
export interface IndustrialItem extends BaseItem {
  category: ItemCategory.INDUSTRIAL
  variety: IndustrialVariety
  properties: {
    durability: number    // 耐久度
    efficiency: number    // 效率
    capacity: number      // 容量
  }
  futuresCode: string
  industrialType: 'metal' | 'energy' | 'chemical'
}

// 装备类道具
export interface EquipmentItem extends BaseItem {
  category: ItemCategory.EQUIPMENT
  equipmentType: EquipmentType
  attributes: {
    focusBonus: number      // 专注力加成百分比
    productionBonus: number // 生产力加成百分比
    qualityBonus: number    // 品质加成百分比
    duration: number        // 持续时间(小时)
  }
  slot: EquipmentSlot
  isEquipped?: boolean
}

// 农业产品品种枚举
export enum AgriculturalVariety {
  // 谷物类
  CORN = 'corn',           // 玉米
  WHEAT = 'wheat',         // 小麦
  SOYBEAN = 'soybean',     // 大豆
  
  // 油脂类
  SOYBEAN_OIL = 'soybean_oil',     // 豆油
  PALM_OIL = 'palm_oil',           // 棕榈油
  RAPESEED_OIL = 'rapeseed_oil',   // 菜籽油
  SOYBEAN_MEAL = 'soybean_meal',   // 豆粕
  
  // 软商品
  COTTON = 'cotton',       // 棉花
  WHITE_SUGAR = 'white_sugar', // 白糖
  APPLE = 'apple',         // 苹果
  RED_DATES = 'red_dates', // 红枣
  
  // 畜牧产品
  LIVE_HOG = 'live_hog'    // 生猪
}

// 工业产品品种枚举
export enum IndustrialVariety {
  // 有色金属
  COPPER = 'copper',       // 铜
  ALUMINUM = 'aluminum',   // 铝
  LEAD = 'lead',           // 铅
  ZINC = 'zinc',           // 锌
  NICKEL = 'nickel',       // 镍
  TIN = 'tin',             // 锡
  
  // 贵金属
  GOLD = 'gold',           // 黄金
  SILVER = 'silver',       // 白银
  
  // 黑色金属
  REBAR = 'rebar',         // 螺纹钢
  HOT_ROLLED_COIL = 'hot_rolled_coil', // 热轧卷板
  
  // 建材
  GLASS = 'glass',         // 玻璃
  
  // 能源化工
  THERMAL_COAL = 'thermal_coal', // 动力煤
  COKE = 'coke',           // 焦炭
  COKING_COAL = 'coking_coal',   // 焦煤
  CRUDE_OIL = 'crude_oil', // 原油
  ASPHALT = 'asphalt',     // 沥青
  LPG = 'lpg'              // 液化石油气
}

// 装备类型
export enum EquipmentType {
  FOCUS_GLASSES = 'focus_glasses',     // 聚焦眼镜
  FOCUS_HEADPHONES = 'focus_headphones', // 专注耳机
  ENERGY_BRACELET = 'energy_bracelet', // 能量手环
  DISCIPLINE_CLOCK = 'discipline_clock' // 自律时钟
}

// 装备槽位
export enum EquipmentSlot {
  HEAD = 'head',     // 头部 (眼镜、耳机)
  WRIST = 'wrist',   // 手腕 (手环)
  DESK = 'desk'      // 桌面 (时钟)
}

// 品质配置
export interface QualityConfig {
  quality: Quality
  name: string
  color: string
  borderColor: string
  glowColor: string
  productionRange: [number, number] // 产量范围
  attributeBonus: number // 装备属性加成百分比
  synthesisSuccessRate: number // 合成基础成功率
  rarityWeight: number // 抽取权重
}

// 品质配置表
export const QUALITY_CONFIGS: Record<Quality, QualityConfig> = {
  [Quality.COMMON]: {
    quality: Quality.COMMON,
    name: '普通',
    color: '#9E9E9E',
    borderColor: '#757575',
    glowColor: '#BDBDBD',
    productionRange: [100, 120],
    attributeBonus: 3,
    synthesisSuccessRate: 80,
    rarityWeight: 1000
  },
  [Quality.GOOD]: {
    quality: Quality.GOOD,
    name: '优质',
    color: '#4CAF50',
    borderColor: '#388E3C',
    glowColor: '#66BB6A',
    productionRange: [130, 160],
    attributeBonus: 6,
    synthesisSuccessRate: 60,
    rarityWeight: 500
  },
  [Quality.RARE]: {
    quality: Quality.RARE,
    name: '稀有',
    color: '#2196F3',
    borderColor: '#1976D2',
    glowColor: '#42A5F5',
    productionRange: [170, 220],
    attributeBonus: 9,
    synthesisSuccessRate: 40,
    rarityWeight: 150
  },
  [Quality.EPIC]: {
    quality: Quality.EPIC,
    name: '史诗',
    color: '#9C27B0',
    borderColor: '#7B1FA2',
    glowColor: '#AB47BC',
    productionRange: [230, 300],
    attributeBonus: 12,
    synthesisSuccessRate: 20,
    rarityWeight: 30
  },
  [Quality.LEGENDARY]: {
    quality: Quality.LEGENDARY,
    name: '传说',
    color: '#FF9800',
    borderColor: '#F57C00',
    glowColor: '#FFB74D',
    productionRange: [320, 400],
    attributeBonus: 15,
    synthesisSuccessRate: 10,
    rarityWeight: 5
  }
}

// 统一的道具类型
export type GameItem = AgriculturalItem | IndustrialItem | EquipmentItem 