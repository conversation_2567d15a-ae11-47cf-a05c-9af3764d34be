// 游戏数据模型 - 支持完整的可玩性功能
import { ItemRarity } from './lootbox'

// ===== 基础枚举和常量 =====
export enum TerrainType {
  PLAINS = 'plains',
  HILLS = 'hills',
  WETLANDS = 'wetlands',
  FERTILE_VALLEY = 'fertile_valley',
  MOUNTAINSIDE = 'mountainside'
}

export enum Season {
  SPRING = 'spring',
  SUMMER = 'summer',
  AUTUMN = 'autumn',
  WINTER = 'winter'
}

export enum WeatherType {
  SUNNY = 'sunny',
  RAINY = 'rainy',
  CLOUDY = 'cloudy',
  STORMY = 'stormy',
  DROUGHT = 'drought',
  SNOW = 'snow'
}

export enum SkillBranch {
  PLANTING = 'planting',
  BREEDING = 'breeding',
  COMMERCE = 'commerce',
  RESEARCH = 'research'
}

export enum QuestType {
  MAIN_STORY = 'main_story',
  SIDE_QUEST = 'side_quest',
  DAILY_QUEST = 'daily_quest',
  URGENT_EVENT = 'urgent_event',
  ACHIEVEMENT = 'achievement'
}

export enum QuestStatus {
  AVAILABLE = 'available',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  FAILED = 'failed',
  LOCKED = 'locked'
}

// ===== 玩家档案数据模型 =====
export interface PlayerProfile {
  id: string
  username: string
  level: number
  experience: number
  experienceToNextLevel: number
  
  // 技能系统
  skillPoints: number
  skills: {
    [SkillBranch.PLANTING]: SkillData
    [SkillBranch.BREEDING]: SkillData
    [SkillBranch.COMMERCE]: SkillData
    [SkillBranch.RESEARCH]: SkillData
  }
  
  // 货币和资源
  coins: number
  reputation: {
    [exchange: string]: number // 各交易所声望
  }
  
  // 统计数据
  statistics: PlayerStatistics
  
  // 偏好设置
  preferences: PlayerPreferences
  
  // 创建和更新时间
  createdAt: Date
  lastActiveAt: Date
}

export interface SkillData {
  level: number
  experience: number
  specialization?: string // 专业化方向
  unlockedAbilities: string[]
  passiveEffects: SkillEffect[]
}

export interface SkillEffect {
  type: string
  value: number
  description: string
}

export interface PlayerStatistics {
  totalPlayTime: number
  totalCropsPlanted: number
  totalCropsHarvested: number
  totalTradesCompleted: number
  bestHarvestValue: number
  longestPlayStreak: number
  achievementsUnlocked: number
  
  // 按作物类型统计
  cropStats: {
    [cropId: string]: {
      planted: number
      harvested: number
      totalValue: number
      bestQuality: ItemRarity
    }
  }
}

export interface PlayerPreferences {
  autoSave: boolean
  soundEffects: boolean
  backgroundMusic: boolean
  notifications: boolean
  language: string
  theme: string
}

// ===== 扩展农场数据模型 =====
export interface ExtendedFarmData {
  id: string
  ownerId: string
  name: string
  level: number
  size: FarmSize
  
  // 地形和土地
  terrain: TerrainType
  plots: FarmPlot[]
  buildings: FarmBuilding[]
  
  // 环境系统
  currentSeason: Season
  currentWeather: WeatherType
  soilHealth: number
  waterLevel: number
  
  // 存储和库存
  storage: FarmStorage
  equipment: Equipment[]
  
  // 经济数据
  totalValue: number
  income: number
  expenses: number
  
  // 美化和装饰
  decorations: Decoration[]
  theme: string
  
  createdAt: Date
  lastUpdated: Date
}

export interface FarmSize {
  width: number
  height: number
  totalPlots: number
  usedPlots: number
}

export interface FarmPlot {
  id: string
  x: number
  y: number
  terrain: TerrainType
  soilFertility: number
  currentCrop?: CropInstance
  lastCropType?: string
  rotationHistory: string[]
  diseases: Disease[]
  pests: Pest[]
}

export interface CropInstance {
  id: string
  cropType: string
  plantedAt: Date
  growthStage: number
  quality: ItemRarity
  health: number
  estimatedYield: number
  appliedItems: string[] // 使用的道具
}

export interface Disease {
  type: string
  severity: number
  startedAt: Date
  treated: boolean
}

export interface Pest {
  type: string
  population: number
  damage: number
  controlled: boolean
}

export interface FarmBuilding {
  id: string
  type: string
  x: number
  y: number
  level: number
  capacity: number
  efficiency: number
}

export interface FarmStorage {
  capacity: number
  items: StorageItem[]
}

export interface StorageItem {
  itemId: string
  quantity: number
  quality: ItemRarity
  storedAt: Date
  expiresAt?: Date
}

export interface Equipment {
  id: string
  type: string
  durability: number
  efficiency: number
  effects: EquipmentEffect[]
}

export interface EquipmentEffect {
  type: string
  value: number
  target: string
}

export interface Decoration {
  id: string
  type: string
  x: number
  y: number
  purchasedAt: Date
}

// ===== 市场数据模型 =====
export interface MarketData {
  currentPrices: {
    [itemId: string]: MarketPrice
  }
  priceHistory: {
    [itemId: string]: PriceHistoryEntry[]
  }
  supplyDemand: {
    [itemId: string]: SupplyDemandData
  }
  marketEvents: MarketEvent[]
  playerTransactions: Transaction[]
}

export interface MarketPrice {
  current: number
  change24h: number
  changePercent: number
  volume: number
  lastUpdated: Date
}

export interface PriceHistoryEntry {
  timestamp: Date
  price: number
  volume: number
  event?: string
}

export interface SupplyDemandData {
  supply: number
  demand: number
  trend: 'up' | 'down' | 'stable'
  forecast: PriceForecast[]
}

export interface PriceForecast {
  timestamp: Date
  predictedPrice: number
  confidence: number
}

export interface MarketEvent {
  id: string
  type: string
  title: string
  description: string
  impact: {
    [itemId: string]: number // 价格影响百分比
  }
  startDate: Date
  endDate: Date
  severity: 'low' | 'medium' | 'high' | 'extreme'
}

export interface Transaction {
  id: string
  playerId: string
  type: 'buy' | 'sell'
  itemId: string
  quantity: number
  pricePerUnit: number
  totalValue: number
  timestamp: Date
  marketConditions: string
}

// ===== 社交数据模型 =====
export interface SocialData {
  playerId: string
  friends: Friend[]
  cooperatives: CooperativeMembership[]
  messages: Message[]
  leaderboards: LeaderboardEntry[]
  achievements: Achievement[]
}

export interface Friend {
  playerId: string
  username: string
  level: number
  status: 'online' | 'offline' | 'away'
  farmName: string
  addedAt: Date
  lastInteraction: Date
  mutualHelp: number
}

export interface CooperativeMembership {
  cooperativeId: string
  cooperativeName: string
  role: 'member' | 'officer' | 'leader'
  joinedAt: Date
  contribution: number
  permissions: string[]
}

export interface Cooperative {
  id: string
  name: string
  description: string
  memberCount: number
  level: number
  treasury: number
  projects: CooperativeProject[]
  createdAt: Date
}

export interface CooperativeProject {
  id: string
  title: string
  description: string
  goal: number
  progress: number
  rewards: ProjectReward[]
  deadline: Date
  participants: string[]
}

export interface ProjectReward {
  type: string
  value: number
  description: string
}

export interface Message {
  id: string
  senderId: string
  receiverId?: string
  cooperativeId?: string
  content: string
  type: 'direct' | 'cooperative' | 'system'
  timestamp: Date
  read: boolean
}

export interface LeaderboardEntry {
  rank: number
  playerId: string
  username: string
  score: number
  category: string
  period: 'daily' | 'weekly' | 'monthly' | 'all-time'
  lastUpdated: Date
}

export interface Achievement {
  id: string
  title: string
  description: string
  category: string
  rarity: ItemRarity
  progress: number
  maxProgress: number
  completed: boolean
  completedAt?: Date
  rewards: AchievementReward[]
}

export interface AchievementReward {
  type: 'coins' | 'experience' | 'item' | 'skill_points' | 'reputation'
  value: number
  itemId?: string
}

// ===== 任务数据模型 =====
export interface QuestData {
  availableQuests: Quest[]
  activeQuests: Quest[]
  completedQuests: Quest[]
  questProgress: {
    [questId: string]: QuestProgress
  }
  dailyQuestResets: Date
  mainStoryProgress: number
}

export interface Quest {
  id: string
  type: QuestType
  title: string
  description: string
  objectives: QuestObjective[]
  rewards: QuestReward[]
  requirements: QuestRequirement[]
  timeLimit?: number
  difficulty: 'easy' | 'medium' | 'hard' | 'extreme'
  status: QuestStatus
  unlocks?: string[] // 解锁的内容
  createdAt: Date
  availableUntil?: Date
}

export interface QuestObjective {
  id: string
  description: string
  type: string
  target: number
  current: number
  completed: boolean
  data?: any // 额外的目标数据
}

export interface QuestReward {
  type: 'coins' | 'experience' | 'item' | 'skill_points' | 'reputation' | 'unlock'
  value: number
  itemId?: string
  unlockId?: string
  description: string
}

export interface QuestRequirement {
  type: 'level' | 'quest_completed' | 'achievement' | 'item' | 'skill'
  value: number | string
  description: string
}

export interface QuestProgress {
  questId: string
  startedAt: Date
  objectives: {
    [objectiveId: string]: number
  }
  completed: boolean
  completedAt?: Date
}

// ===== 事件和通知系统 =====
export interface GameEvent {
  id: string
  type: string
  title: string
  description: string
  data: any
  timestamp: Date
  playerId?: string
  global: boolean
  processed: boolean
}

export interface Notification {
  id: string
  type: 'info' | 'success' | 'warning' | 'error'
  title: string
  message: string
  data?: any
  timestamp: Date
  read: boolean
  expiresAt?: Date
}

// ===== 游戏配置 =====
export interface GameConfig {
  version: string
  features: {
    [feature: string]: boolean
  }
  balancing: {
    experienceMultiplier: number
    coinMultiplier: number
    growthSpeedMultiplier: number
    qualityChanceMultiplier: number
  }
  limits: {
    maxFarmSize: number
    maxFriends: number
    maxStorageCapacity: number
    maxEquipment: number
  }
}

// ===== 保存数据格式 =====
export interface GameSaveData {
  version: string
  playerId: string
  playerProfile: PlayerProfile
  farmData: ExtendedFarmData
  marketData: MarketData
  socialData: SocialData
  questData: QuestData
  notifications: Notification[]
  gameEvents: GameEvent[]
  lastSaved: Date
  checksum: string
} 