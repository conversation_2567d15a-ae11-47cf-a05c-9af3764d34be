// 期货游戏道具系统类型定义
export enum Quality {
  COMMON = 'common',      // 普通
  GOOD = 'good',          // 优质 
  RARE = 'rare',          // 稀有
  EPIC = 'epic',          // 史诗
  LEGENDARY = 'legendary' // 传说
}

export enum ItemCategory {
  AGRICULTURAL = 'agricultural', // 农业产品
  INDUSTRIAL = 'industrial',     // 工业产品
  EQUIPMENT = 'equipment'        // 装备类道具
}

// 基础道具接口
export interface BaseItem {
  id: string
  name: string
  description: string
  category: ItemCategory
  quality: Quality
  icon: string
  baseValue: number
  stackable: boolean
  tradeable: boolean
  obtainedAt?: number
}

// 农业产品道具
export interface AgriculturalItem extends BaseItem {
  category: ItemCategory.AGRICULTURAL
  variety: AgriculturalVariety
  production: {
    minDaily: number  // 最低日产量
    maxDaily: number  // 最高日产量
    currentRate: number // 当前产率倍数
  }
  futuresCode: string // 期货代码
  seasonalBonus?: number // 季节加成
}

// 工业产品道具
export interface IndustrialItem extends BaseItem {
  category: ItemCategory.INDUSTRIAL
  variety: IndustrialVariety
  properties: {
    durability: number    // 耐久度
    efficiency: number    // 效率
    capacity: number      // 容量
  }
  futuresCode: string
  industrialType: 'metal' | 'energy' | 'chemical'
}

// 装备类道具
export interface EquipmentItem extends BaseItem {
  category: ItemCategory.EQUIPMENT
  equipmentType: EquipmentType
  attributes: {
    focusBonus: number      // 专注力加成百分比
    productionBonus: number // 生产力加成百分比
    qualityBonus: number    // 品质加成百分比
    duration: number        // 持续时间(小时)
  }
  slot: EquipmentSlot
  isEquipped?: boolean
}

// 农业产品品种枚举
export enum AgriculturalVariety {
  // 谷物类
  CORN = 'corn',           // 玉米
  WHEAT = 'wheat',         // 小麦
  SOYBEAN = 'soybean',     // 大豆
  
  // 油脂类
  SOYBEAN_OIL = 'soybean_oil',     // 豆油
  PALM_OIL = 'palm_oil',           // 棕榈油
  RAPESEED_OIL = 'rapeseed_oil',   // 菜籽油
  SOYBEAN_MEAL = 'soybean_meal',   // 豆粕
  
  // 软商品
  COTTON = 'cotton',       // 棉花
  WHITE_SUGAR = 'white_sugar', // 白糖
  APPLE = 'apple',         // 苹果
  RED_DATES = 'red_dates', // 红枣
  
  // 畜牧产品
  LIVE_HOG = 'live_hog'    // 生猪
}

// 工业产品品种枚举
export enum IndustrialVariety {
  // 有色金属
  COPPER = 'copper',       // 铜
  ALUMINUM = 'aluminum',   // 铝
  LEAD = 'lead',           // 铅
  ZINC = 'zinc',           // 锌
  NICKEL = 'nickel',       // 镍
  TIN = 'tin',             // 锡
  
  // 贵金属
  GOLD = 'gold',           // 黄金
  SILVER = 'silver',       // 白银
  
  // 黑色金属
  REBAR = 'rebar',         // 螺纹钢
  HOT_ROLLED_COIL = 'hot_rolled_coil', // 热轧卷板
  
  // 建材
  GLASS = 'glass',         // 玻璃
  
  // 能源化工
  THERMAL_COAL = 'thermal_coal', // 动力煤
  COKE = 'coke',           // 焦炭
  COKING_COAL = 'coking_coal',   // 焦煤
  CRUDE_OIL = 'crude_oil', // 原油
  ASPHALT = 'asphalt',     // 沥青
  LPG = 'lpg'              // 液化石油气
}

// 装备类型
export enum EquipmentType {
  FOCUS_GLASSES = 'focus_glasses',     // 聚焦眼镜
  FOCUS_HEADPHONES = 'focus_headphones', // 专注耳机
  ENERGY_BRACELET = 'energy_bracelet', // 能量手环
  DISCIPLINE_CLOCK = 'discipline_clock' // 自律时钟
}

// 装备槽位
export enum EquipmentSlot {
  HEAD = 'head',     // 头部 (眼镜、耳机)
  WRIST = 'wrist',   // 手腕 (手环)
  DESK = 'desk'      // 桌面 (时钟)
}

// 品质配置
export interface QualityConfig {
  quality: Quality
  name: string
  color: string
  borderColor: string
  glowColor: string
  productionRange: [number, number] // 产量范围
  attributeBonus: number // 装备属性加成百分比
  synthesisSuccessRate: number // 合成基础成功率
  rarityWeight: number // 抽取权重
}

// 品质配置表
export const QUALITY_CONFIGS: Record<Quality, QualityConfig> = {
  [Quality.COMMON]: {
    quality: Quality.COMMON,
    name: '普通',
    color: '#9E9E9E',
    borderColor: '#757575',
    glowColor: '#BDBDBD',
    productionRange: [100, 120],
    attributeBonus: 3,
    synthesisSuccessRate: 80,
    rarityWeight: 1000
  },
  [Quality.GOOD]: {
    quality: Quality.GOOD,
    name: '优质',
    color: '#4CAF50',
    borderColor: '#388E3C',
    glowColor: '#66BB6A',
    productionRange: [130, 160],
    attributeBonus: 6,
    synthesisSuccessRate: 60,
    rarityWeight: 500
  },
  [Quality.RARE]: {
    quality: Quality.RARE,
    name: '稀有',
    color: '#2196F3',
    borderColor: '#1976D2',
    glowColor: '#42A5F5',
    productionRange: [170, 220],
    attributeBonus: 9,
    synthesisSuccessRate: 40,
    rarityWeight: 150
  },
  [Quality.EPIC]: {
    quality: Quality.EPIC,
    name: '史诗',
    color: '#9C27B0',
    borderColor: '#7B1FA2',
    glowColor: '#AB47BC',
    productionRange: [230, 300],
    attributeBonus: 12,
    synthesisSuccessRate: 20,
    rarityWeight: 30
  },
  [Quality.LEGENDARY]: {
    quality: Quality.LEGENDARY,
    name: '传说',
    color: '#FF9800',
    borderColor: '#F57C00',
    glowColor: '#FFB74D',
    productionRange: [320, 400],
    attributeBonus: 15,
    synthesisSuccessRate: 10,
    rarityWeight: 5
  }
}

// 道具数据库
export interface ItemDatabase {
  agricultural: Record<AgriculturalVariety, Omit<AgriculturalItem, 'id' | 'quality' | 'obtainedAt'>>
  industrial: Record<IndustrialVariety, Omit<IndustrialItem, 'id' | 'quality' | 'obtainedAt'>>
  equipment: Record<EquipmentType, Omit<EquipmentItem, 'id' | 'quality' | 'obtainedAt'>>
}

// 预定义道具数据
export const ITEM_DATABASE: ItemDatabase = {
  agricultural: {
    [AgriculturalVariety.CORN]: {
      name: '玉米',
      description: '主要的饲料作物和工业原料',
      category: ItemCategory.AGRICULTURAL,
      variety: AgriculturalVariety.CORN,
      icon: '🌽',
      baseValue: 2800,
      stackable: true,
      tradeable: true,
      production: { minDaily: 100, maxDaily: 120, currentRate: 1.0 },
      futuresCode: 'C',
      seasonalBonus: 1.2
    },
    [AgriculturalVariety.WHEAT]: {
      name: '小麦',
      description: '重要的粮食作物',
      category: ItemCategory.AGRICULTURAL,
      variety: AgriculturalVariety.WHEAT,
      icon: '🌾',
      baseValue: 3200,
      stackable: true,
      tradeable: true,
      production: { minDaily: 100, maxDaily: 120, currentRate: 1.0 },
      futuresCode: 'WH',
      seasonalBonus: 1.1
    },
    [AgriculturalVariety.SOYBEAN]: {
      name: '大豆',
      description: '重要的油料和蛋白质作物',
      category: ItemCategory.AGRICULTURAL,
      variety: AgriculturalVariety.SOYBEAN,
      icon: '🫘',
      baseValue: 4200,
      stackable: true,
      tradeable: true,
      production: { minDaily: 100, maxDaily: 120, currentRate: 1.0 },
      futuresCode: 'A',
      seasonalBonus: 1.15
    },
    [AgriculturalVariety.COTTON]: {
      name: '棉花',
      description: '重要的纺织原料',
      category: ItemCategory.AGRICULTURAL,
      variety: AgriculturalVariety.COTTON,
      icon: '🌸',
      baseValue: 16000,
      stackable: true,
      tradeable: true,
      production: { minDaily: 100, maxDaily: 120, currentRate: 1.0 },
      futuresCode: 'CF'
    },
    [AgriculturalVariety.WHITE_SUGAR]: {
      name: '白糖',
      description: '重要的食品工业原料',
      category: ItemCategory.AGRICULTURAL,
      variety: AgriculturalVariety.WHITE_SUGAR,
      icon: '🍬',
      baseValue: 6500,
      stackable: true,
      tradeable: true,
      production: { minDaily: 100, maxDaily: 120, currentRate: 1.0 },
      futuresCode: 'SR'
    },
    [AgriculturalVariety.SOYBEAN_OIL]: {
      name: '豆油',
      description: '主要食用油品种',
      category: ItemCategory.AGRICULTURAL,
      variety: AgriculturalVariety.SOYBEAN_OIL,
      icon: '🛢️',
      baseValue: 8500,
      stackable: true,
      tradeable: true,
      production: { minDaily: 100, maxDaily: 120, currentRate: 1.0 },
      futuresCode: 'Y'
    },
    [AgriculturalVariety.PALM_OIL]: {
      name: '棕榈油',
      description: '重要的植物油品种',
      category: ItemCategory.AGRICULTURAL,
      variety: AgriculturalVariety.PALM_OIL,
      icon: '🌴',
      baseValue: 7800,
      stackable: true,
      tradeable: true,
      production: { minDaily: 100, maxDaily: 120, currentRate: 1.0 },
      futuresCode: 'P'
    },
    [AgriculturalVariety.RAPESEED_OIL]: {
      name: '菜籽油',
      description: '健康的食用油选择',
      category: ItemCategory.AGRICULTURAL,
      variety: AgriculturalVariety.RAPESEED_OIL,
      icon: '🌻',
      baseValue: 9200,
      stackable: true,
      tradeable: true,
      production: { minDaily: 100, maxDaily: 120, currentRate: 1.0 },
      futuresCode: 'OI'
    },
    [AgriculturalVariety.SOYBEAN_MEAL]: {
      name: '豆粕',
      description: '重要的饲料蛋白原料',
      category: ItemCategory.AGRICULTURAL,
      variety: AgriculturalVariety.SOYBEAN_MEAL,
      icon: '🥜',
      baseValue: 3800,
      stackable: true,
      tradeable: true,
      production: { minDaily: 100, maxDaily: 120, currentRate: 1.0 },
      futuresCode: 'M'
    },
    [AgriculturalVariety.APPLE]: {
      name: '苹果',
      description: '优质水果期货品种',
      category: ItemCategory.AGRICULTURAL,
      variety: AgriculturalVariety.APPLE,
      icon: '🍎',
      baseValue: 12000,
      stackable: true,
      tradeable: true,
      production: { minDaily: 100, maxDaily: 120, currentRate: 1.0 },
      futuresCode: 'AP',
      seasonalBonus: 1.3
    },
    [AgriculturalVariety.RED_DATES]: {
      name: '红枣',
      description: '传统养生食品',
      category: ItemCategory.AGRICULTURAL,
      variety: AgriculturalVariety.RED_DATES,
      icon: '🫒',
      baseValue: 15000,
      stackable: true,
      tradeable: true,
      production: { minDaily: 100, maxDaily: 120, currentRate: 1.0 },
      futuresCode: 'CJ'
    },
    [AgriculturalVariety.LIVE_HOG]: {
      name: '生猪',
      description: '重要的肉类期货品种',
      category: ItemCategory.AGRICULTURAL,
      variety: AgriculturalVariety.LIVE_HOG,
      icon: '🐷',
      baseValue: 18000,
      stackable: true,
      tradeable: true,
      production: { minDaily: 100, maxDaily: 120, currentRate: 1.0 },
      futuresCode: 'LH'
    }
  },
  industrial: {
    [IndustrialVariety.COPPER]: {
      name: '铜',
      description: '重要的工业金属',
      category: ItemCategory.INDUSTRIAL,
      variety: IndustrialVariety.COPPER,
      icon: '🔶',
      baseValue: 68000,
      stackable: true,
      tradeable: true,
      properties: { durability: 85, efficiency: 90, capacity: 100 },
      futuresCode: 'CU',
      industrialType: 'metal'
    },
    [IndustrialVariety.ALUMINUM]: {
      name: '铝',
      description: '轻金属工业原料',
      category: ItemCategory.INDUSTRIAL,
      variety: IndustrialVariety.ALUMINUM,
      icon: '⚪',
      baseValue: 19000,
      stackable: true,
      tradeable: true,
      properties: { durability: 70, efficiency: 85, capacity: 90 },
      futuresCode: 'AL',
      industrialType: 'metal'
    },
    [IndustrialVariety.ZINC]: {
      name: '锌',
      description: '重要的有色金属',
      category: ItemCategory.INDUSTRIAL,
      variety: IndustrialVariety.ZINC,
      icon: '🔘',
      baseValue: 24000,
      stackable: true,
      tradeable: true,
      properties: { durability: 75, efficiency: 80, capacity: 85 },
      futuresCode: 'ZN',
      industrialType: 'metal'
    },
    [IndustrialVariety.GOLD]: {
      name: '黄金',
      description: '贵金属投资品种',
      category: ItemCategory.INDUSTRIAL,
      variety: IndustrialVariety.GOLD,
      icon: '🏆',
      baseValue: 450000,
      stackable: true,
      tradeable: true,
      properties: { durability: 100, efficiency: 95, capacity: 100 },
      futuresCode: 'AU',
      industrialType: 'metal'
    },
    [IndustrialVariety.SILVER]: {
      name: '白银',
      description: '工业和投资兼具的贵金属',
      category: ItemCategory.INDUSTRIAL,
      variety: IndustrialVariety.SILVER,
      icon: '🥈',
      baseValue: 5200,
      stackable: true,
      tradeable: true,
      properties: { durability: 90, efficiency: 88, capacity: 95 },
      futuresCode: 'AG',
      industrialType: 'metal'
    },
    [IndustrialVariety.CRUDE_OIL]: {
      name: '原油',
      description: '重要的能源期货',
      category: ItemCategory.INDUSTRIAL,
      variety: IndustrialVariety.CRUDE_OIL,
      icon: '🛢️',
      baseValue: 520,
      stackable: true,
      tradeable: true,
      properties: { durability: 80, efficiency: 100, capacity: 100 },
      futuresCode: 'SC',
      industrialType: 'energy'
    },
    [IndustrialVariety.THERMAL_COAL]: {
      name: '动力煤',
      description: '重要的能源原料',
      category: ItemCategory.INDUSTRIAL,
      variety: IndustrialVariety.THERMAL_COAL,
      icon: '⚫',
      baseValue: 980,
      stackable: true,
      tradeable: true,
      properties: { durability: 70, efficiency: 85, capacity: 90 },
      futuresCode: 'ZC',
      industrialType: 'energy'
    },
    [IndustrialVariety.REBAR]: {
      name: '螺纹钢',
      description: '建筑用钢材',
      category: ItemCategory.INDUSTRIAL,
      variety: IndustrialVariety.REBAR,
      icon: '🔩',
      baseValue: 3800,
      stackable: true,
      tradeable: true,
      properties: { durability: 95, efficiency: 90, capacity: 85 },
      futuresCode: 'RB',
      industrialType: 'metal'
    },
    [IndustrialVariety.HOT_ROLLED_COIL]: {
      name: '热轧卷板',
      description: '钢铁工业产品',
      category: ItemCategory.INDUSTRIAL,
      variety: IndustrialVariety.HOT_ROLLED_COIL,
      icon: '📜',
      baseValue: 3600,
      stackable: true,
      tradeable: true,
      properties: { durability: 90, efficiency: 88, capacity: 80 },
      futuresCode: 'HC',
      industrialType: 'metal'
    },
    [IndustrialVariety.GLASS]: {
      name: '玻璃',
      description: '建材工业产品',
      category: ItemCategory.INDUSTRIAL,
      variety: IndustrialVariety.GLASS,
      icon: '🔆',
      baseValue: 1800,
      stackable: true,
      tradeable: true,
      properties: { durability: 60, efficiency: 75, capacity: 70 },
      futuresCode: 'FG',
      industrialType: 'chemical'
    },
    [IndustrialVariety.COKE]: {
      name: '焦炭',
      description: '钢铁冶炼原料',
      category: ItemCategory.INDUSTRIAL,
      variety: IndustrialVariety.COKE,
      icon: '⬛',
      baseValue: 2200,
      stackable: true,
      tradeable: true,
      properties: { durability: 80, efficiency: 90, capacity: 85 },
      futuresCode: 'J',
      industrialType: 'energy'
    },
    [IndustrialVariety.COKING_COAL]: {
      name: '焦煤',
      description: '炼焦专用煤',
      category: ItemCategory.INDUSTRIAL,
      variety: IndustrialVariety.COKING_COAL,
      icon: '◼️',
      baseValue: 1650,
      stackable: true,
      tradeable: true,
      properties: { durability: 75, efficiency: 85, capacity: 80 },
      futuresCode: 'JM',
      industrialType: 'energy'
    },
    [IndustrialVariety.ASPHALT]: {
      name: '沥青',
      description: '道路建设材料',
      category: ItemCategory.INDUSTRIAL,
      variety: IndustrialVariety.ASPHALT,
      icon: '🛣️',
      baseValue: 3400,
      stackable: true,
      tradeable: true,
      properties: { durability: 70, efficiency: 80, capacity: 75 },
      futuresCode: 'BU',
      industrialType: 'chemical'
    },
    [IndustrialVariety.LPG]: {
      name: '液化石油气',
      description: '清洁能源燃料',
      category: ItemCategory.INDUSTRIAL,
      variety: IndustrialVariety.LPG,
      icon: '💨',
      baseValue: 4200,
      stackable: true,
      tradeable: true,
      properties: { durability: 85, efficiency: 95, capacity: 90 },
      futuresCode: 'PG',
      industrialType: 'energy'
    },
    [IndustrialVariety.LEAD]: {
      name: '铅',
      description: '重金属工业原料',
      category: ItemCategory.INDUSTRIAL,
      variety: IndustrialVariety.LEAD,
      icon: '🔘',
      baseValue: 16000,
      stackable: true,
      tradeable: true,
      properties: { durability: 70, efficiency: 75, capacity: 80 },
      futuresCode: 'PB',
      industrialType: 'metal'
    },
    [IndustrialVariety.NICKEL]: {
      name: '镍',
      description: '重要的合金材料',
      category: ItemCategory.INDUSTRIAL,
      variety: IndustrialVariety.NICKEL,
      icon: '⚙️',
      baseValue: 140000,
      stackable: true,
      tradeable: true,
      properties: { durability: 95, efficiency: 90, capacity: 85 },
      futuresCode: 'NI',
      industrialType: 'metal'
    },
    [IndustrialVariety.TIN]: {
      name: '锡',
      description: '重要的有色金属',
      category: ItemCategory.INDUSTRIAL,
      variety: IndustrialVariety.TIN,
      icon: '🔩',
      baseValue: 220000,
      stackable: true,
      tradeable: true,
      properties: { durability: 85, efficiency: 88, capacity: 90 },
      futuresCode: 'SN',
      industrialType: 'metal'
    }
  },
  equipment: {
    [EquipmentType.FOCUS_GLASSES]: {
      name: '聚焦眼镜',
      description: '提升专注力的智能眼镜',
      category: ItemCategory.EQUIPMENT,
      equipmentType: EquipmentType.FOCUS_GLASSES,
      icon: '👓',
      baseValue: 2000,
      stackable: false,
      tradeable: true,
      attributes: { focusBonus: 0, productionBonus: 0, qualityBonus: 0, duration: 24 },
      slot: EquipmentSlot.HEAD
    },
    [EquipmentType.FOCUS_HEADPHONES]: {
      name: '专注耳机',
      description: '降噪专注的智能耳机',
      category: ItemCategory.EQUIPMENT,
      equipmentType: EquipmentType.FOCUS_HEADPHONES,
      icon: '🎧',
      baseValue: 1800,
      stackable: false,
      tradeable: true,
      attributes: { focusBonus: 0, productionBonus: 0, qualityBonus: 0, duration: 24 },
      slot: EquipmentSlot.HEAD
    },
    [EquipmentType.ENERGY_BRACELET]: {
      name: '能量手环',
      description: '监测活力的智能手环',
      category: ItemCategory.EQUIPMENT,
      equipmentType: EquipmentType.ENERGY_BRACELET,
      icon: '⌚',
      baseValue: 1500,
      stackable: false,
      tradeable: true,
      attributes: { focusBonus: 0, productionBonus: 0, qualityBonus: 0, duration: 24 },
      slot: EquipmentSlot.WRIST
    },
    [EquipmentType.DISCIPLINE_CLOCK]: {
      name: '自律时钟',
      description: '帮助管理时间的智能时钟',
      category: ItemCategory.EQUIPMENT,
      equipmentType: EquipmentType.DISCIPLINE_CLOCK,
      icon: '⏰',
      baseValue: 2200,
      stackable: false,
      tradeable: true,
      attributes: { focusBonus: 0, productionBonus: 0, qualityBonus: 0, duration: 24 },
      slot: EquipmentSlot.DESK
    }
  }
}

// 统一的道具类型
export type GameItem = AgriculturalItem | IndustrialItem | EquipmentItem

// 道具工厂函数
export function createItem(
  variety: AgriculturalVariety | IndustrialVariety | EquipmentType,
  quality: Quality,
  quantity: number = 1
): GameItem {
  const baseItem = getBaseItemData(variety)
  const qualityConfig = QUALITY_CONFIGS[quality]
  const id = `${variety}_${quality}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

  const item = {
    ...baseItem,
    id,
    quality,
    obtainedAt: Date.now()
  } as GameItem

  // 根据品质调整属性
  if (item.category === ItemCategory.AGRICULTURAL) {
    const agriItem = item as AgriculturalItem
    const [min, max] = qualityConfig.productionRange
    agriItem.production.minDaily = min
    agriItem.production.maxDaily = max
    agriItem.production.currentRate = 1.0 + (qualityConfig.attributeBonus / 100)
  } else if (item.category === ItemCategory.INDUSTRIAL) {
    const indItem = item as IndustrialItem
    const bonus = qualityConfig.attributeBonus / 100
    indItem.properties.durability = Math.round(indItem.properties.durability * (1 + bonus))
    indItem.properties.efficiency = Math.round(indItem.properties.efficiency * (1 + bonus))
    indItem.properties.capacity = Math.round(indItem.properties.capacity * (1 + bonus))
  } else if (item.category === ItemCategory.EQUIPMENT) {
    const equipItem = item as EquipmentItem
    equipItem.attributes.focusBonus = qualityConfig.attributeBonus
    equipItem.attributes.productionBonus = qualityConfig.attributeBonus
    equipItem.attributes.qualityBonus = qualityConfig.attributeBonus
  }

  return item
}

function getBaseItemData(variety: AgriculturalVariety | IndustrialVariety | EquipmentType) {
  if (Object.values(AgriculturalVariety).includes(variety as AgriculturalVariety)) {
    return ITEM_DATABASE.agricultural[variety as AgriculturalVariety]
  } else if (Object.values(IndustrialVariety).includes(variety as IndustrialVariety)) {
    return ITEM_DATABASE.industrial[variety as IndustrialVariety]
  } else if (Object.values(EquipmentType).includes(variety as EquipmentType)) {
    return ITEM_DATABASE.equipment[variety as EquipmentType]
  }
  throw new Error(`Unknown item variety: ${variety}`)
} 