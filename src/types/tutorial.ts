// 引导步骤类型
export interface TutorialStep {
  id: string
  title: string
  description: string
  targetElement?: string  // CSS选择器或元素ID
  position?: 'top' | 'bottom' | 'left' | 'right' | 'center'
  action?: 'click' | 'hover' | 'focus' | 'wait' | 'custom' | 'celebration'
  actionData?: any
  skipable?: boolean
  waitForElement?: boolean  // 是否等待目标元素出现
  onBeforeShow?: () => void | Promise<void>
  onAfterShow?: () => void | Promise<void>
  onBeforeNext?: () => boolean | Promise<boolean>
  onAfterNext?: () => void | Promise<void>
  customComponent?: React.ComponentType<TutorialStepProps>
}

// 引导步骤组件的Props
export interface TutorialStepProps {
  step: TutorialStep
  isActive: boolean
  onNext: () => void
  onPrev: () => void
  onSkip: () => void
  onFinish: () => void
  currentIndex: number
  totalSteps: number
}

// 引导状态
export interface TutorialState {
  isActive: boolean
  currentStepIndex: number
  steps: TutorialStep[]
  completedSteps: string[]  // 已完成的步骤ID
  isFirstTimeUser: boolean
  tutorialProgress: {
    cameraSetup: boolean
    firstPlanting: boolean
    basicOperations: boolean
    completed: boolean
  }
}

// 引导动作
export type TutorialAction = 
  | { type: 'START_TUTORIAL'; payload: { steps: TutorialStep[] } }
  | { type: 'NEXT_STEP' }
  | { type: 'PREV_STEP' }
  | { type: 'SKIP_STEP' }
  | { type: 'FINISH_TUTORIAL' }
  | { type: 'SET_STEP'; payload: number }
  | { type: 'COMPLETE_STEP'; payload: string }
  | { type: 'UPDATE_PROGRESS'; payload: Partial<TutorialState['tutorialProgress']> }
  | { type: 'RESET_TUTORIAL' }

// 引导高亮样式
export interface HighlightStyle {
  borderRadius?: string
  padding?: number
  animation?: string
  zIndex?: number
}

// 引导提示位置
export interface TooltipPosition {
  x: number
  y: number
  placement: 'top' | 'bottom' | 'left' | 'right'
} 