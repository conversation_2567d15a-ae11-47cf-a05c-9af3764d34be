import React, { useState } from 'react'
import './App.css'

const SimpleApp: React.FC = () => {
  const [currentPage, setCurrentPage] = useState<'home' | 'lootbox'>('home')

  if (currentPage === 'lootbox') {
    return (
      <div style={{
        padding: '20px',
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        color: 'white'
      }}>
        <button
          onClick={() => setCurrentPage('home')}
          style={{
            position: 'fixed',
            top: '20px',
            left: '20px',
            zIndex: 1000,
            padding: '10px 20px',
            backgroundColor: '#8b5cf6',
            color: 'white',
            border: 'none',
            borderRadius: '6px',
            cursor: 'pointer',
            fontSize: '1rem',
            fontWeight: 'bold',
            boxShadow: '0 2px 8px rgba(0,0,0,0.2)'
          }}
        >
          ← 返回主页
        </button>

        <div style={{
          maxWidth: '1200px',
          margin: '0 auto',
          paddingTop: '80px'
        }}>
          <div style={{
            textAlign: 'center',
            marginBottom: '40px',
            padding: '32px',
            background: 'rgba(255, 255, 255, 0.1)',
            borderRadius: '24px',
            backdropFilter: 'blur(10px)'
          }}>
            <h1 style={{
              fontSize: '2.5rem',
              fontWeight: '800',
              margin: '0 0 16px 0',
              textShadow: '0 2px 4px rgba(0, 0, 0, 0.3)'
            }}>
              🎰 期货盲盒精品商店
            </h1>
            <p style={{
              fontSize: '1.2rem',
              margin: '0',
              opacity: '0.9'
            }}>
              精选优质期货品种，开启您的投资传奇
            </p>
          </div>

          {/* 盲盒展示区域 */}
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
            gap: '24px',
            marginBottom: '40px'
          }}>
            {/* 农业期货盲盒 */}
            <div style={{
              background: 'rgba(255, 255, 255, 0.95)',
              borderRadius: '20px',
              padding: '24px',
              color: '#1e293b',
              boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
              transition: 'transform 0.3s ease',
              cursor: 'pointer'
            }}
            onMouseOver={(e) => {
              e.currentTarget.style.transform = 'translateY(-8px)'
            }}
            onMouseOut={(e) => {
              e.currentTarget.style.transform = 'translateY(0)'
            }}>
              <div style={{
                fontSize: '3rem',
                textAlign: 'center',
                marginBottom: '16px'
              }}>🌾</div>
              <h3 style={{
                fontSize: '1.4rem',
                fontWeight: '700',
                margin: '0 0 12px 0',
                textAlign: 'center',
                color: '#22c55e'
              }}>农业期货宝盒</h3>
              <p style={{
                fontSize: '0.95rem',
                color: '#64748b',
                lineHeight: '1.5',
                margin: '0 0 20px 0',
                textAlign: 'center'
              }}>
                包含玉米、小麦、大豆等优质农产品期货
              </p>
              <div style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                gap: '8px',
                padding: '12px 20px',
                background: 'linear-gradient(135deg, #f0fdf4, #dcfce7)',
                border: '2px solid #22c55e',
                borderRadius: '16px',
                fontWeight: '700'
              }}>
                <span style={{ fontSize: '1.2rem' }}>💰</span>
                <span style={{ fontSize: '1.3rem', color: '#059669' }}>1000</span>
                <span style={{ fontSize: '0.9rem', color: '#64748b' }}>专注币</span>
              </div>
            </div>

            {/* 工业金属盲盒 */}
            <div style={{
              background: 'rgba(255, 255, 255, 0.95)',
              borderRadius: '20px',
              padding: '24px',
              color: '#1e293b',
              boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
              transition: 'transform 0.3s ease',
              cursor: 'pointer'
            }}
            onMouseOver={(e) => {
              e.currentTarget.style.transform = 'translateY(-8px)'
            }}
            onMouseOut={(e) => {
              e.currentTarget.style.transform = 'translateY(0)'
            }}>
              <div style={{
                fontSize: '3rem',
                textAlign: 'center',
                marginBottom: '16px'
              }}>🏭</div>
              <h3 style={{
                fontSize: '1.4rem',
                fontWeight: '700',
                margin: '0 0 12px 0',
                textAlign: 'center',
                color: '#3b82f6'
              }}>工业金属宝盒</h3>
              <p style={{
                fontSize: '0.95rem',
                color: '#64748b',
                lineHeight: '1.5',
                margin: '0 0 20px 0',
                textAlign: 'center'
              }}>
                包含铜、铝、锌等工业金属期货
              </p>
              <div style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                gap: '8px',
                padding: '12px 20px',
                background: 'linear-gradient(135deg, #eff6ff, #dbeafe)',
                border: '2px solid #3b82f6',
                borderRadius: '16px',
                fontWeight: '700'
              }}>
                <span style={{ fontSize: '1.2rem' }}>💰</span>
                <span style={{ fontSize: '1.3rem', color: '#059669' }}>1500</span>
                <span style={{ fontSize: '0.9rem', color: '#64748b' }}>专注币</span>
              </div>
            </div>

            {/* 能源化工盲盒 */}
            <div style={{
              background: 'rgba(255, 255, 255, 0.95)',
              borderRadius: '20px',
              padding: '24px',
              color: '#1e293b',
              boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
              transition: 'transform 0.3s ease',
              cursor: 'pointer'
            }}
            onMouseOver={(e) => {
              e.currentTarget.style.transform = 'translateY(-8px)'
            }}
            onMouseOut={(e) => {
              e.currentTarget.style.transform = 'translateY(0)'
            }}>
              <div style={{
                fontSize: '3rem',
                textAlign: 'center',
                marginBottom: '16px'
              }}>⚡</div>
              <h3 style={{
                fontSize: '1.4rem',
                fontWeight: '700',
                margin: '0 0 12px 0',
                textAlign: 'center',
                color: '#f59e0b'
              }}>能源化工宝盒</h3>
              <p style={{
                fontSize: '0.95rem',
                color: '#64748b',
                lineHeight: '1.5',
                margin: '0 0 20px 0',
                textAlign: 'center'
              }}>
                包含原油、天然气等能源期货
              </p>
              <div style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                gap: '8px',
                padding: '12px 20px',
                background: 'linear-gradient(135deg, #fffbeb, #fef3c7)',
                border: '2px solid #f59e0b',
                borderRadius: '16px',
                fontWeight: '700'
              }}>
                <span style={{ fontSize: '1.2rem' }}>💰</span>
                <span style={{ fontSize: '1.3rem', color: '#059669' }}>2000</span>
                <span style={{ fontSize: '0.9rem', color: '#64748b' }}>专注币</span>
              </div>
            </div>
          </div>

          <div style={{
            textAlign: 'center',
            padding: '24px',
            background: 'rgba(255, 255, 255, 0.1)',
            borderRadius: '16px',
            backdropFilter: 'blur(10px)'
          }}>
            <p style={{
              fontSize: '1.1rem',
              margin: '0',
              opacity: '0.9'
            }}>
              💡 这是期货盲盒系统的美观设计演示
            </p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div style={{
      padding: '50px',
      textAlign: 'center',
      background: 'linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%)',
      minHeight: '100vh',
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'center',
      alignItems: 'center'
    }}>
      <h1 style={{
        color: '#1976d2',
        marginBottom: '30px',
        fontSize: '2.5rem',
        fontWeight: '800'
      }}>
        🎁 自律农场 - 期货游戏系统
      </h1>
      
      <div style={{
        fontSize: '4rem',
        marginBottom: '30px'
      }}>
        📦🌾💰
      </div>
      
      <p style={{
        color: '#666',
        fontSize: '1.2rem',
        marginBottom: '40px',
        maxWidth: '600px',
        lineHeight: '1.6'
      }}>
        欢迎来到自律农场的期货游戏系统！通过专注学习和良好习惯来获得游戏货币，购买期货盲盒，体验投资乐趣。
      </p>
      
      <button
        onClick={() => setCurrentPage('lootbox')}
        style={{
          padding: '20px 40px',
          fontSize: '1.2rem',
          fontWeight: 'bold',
          backgroundColor: '#8b5cf6',
          color: 'white',
          border: 'none',
          borderRadius: '16px',
          cursor: 'pointer',
          boxShadow: '0 8px 24px rgba(139, 92, 246, 0.3)',
          transition: 'all 0.3s ease'
        }}
        onMouseOver={(e) => {
          e.currentTarget.style.transform = 'translateY(-4px)'
          e.currentTarget.style.boxShadow = '0 12px 32px rgba(139, 92, 246, 0.4)'
        }}
        onMouseOut={(e) => {
          e.currentTarget.style.transform = 'translateY(0)'
          e.currentTarget.style.boxShadow = '0 8px 24px rgba(139, 92, 246, 0.3)'
        }}
      >
        🎨 体验期货盲盒设计
      </button>

      <div style={{
        marginTop: '50px',
        padding: '20px',
        backgroundColor: 'rgba(255, 255, 255, 0.8)',
        borderRadius: '12px',
        maxWidth: '500px'
      }}>
        <h3 style={{ color: '#1976d2', marginBottom: '16px' }}>✨ 设计特点</h3>
        <ul style={{
          textAlign: 'left',
          color: '#666',
          lineHeight: '1.8'
        }}>
          <li>🎨 现代化的卡片设计</li>
          <li>✨ 流畅的悬停动画效果</li>
          <li>🎯 清晰的分类和价格展示</li>
          <li>📱 完美的响应式布局</li>
        </ul>
      </div>
    </div>
  )
}

export default SimpleApp
