import { GameStateManager, GameState, FocusState } from '../managers/GameStateManager'
import { EnhancedFarmManager, EnhancedFarmEvent, SessionConfig } from '../managers/EnhancedFarmManager'
import { 
  CropInstance, 
  CropType, 
  CropStage, 
  CropQuality,
  FarmGrid,
  CropGrowthEvent
} from '../types/crop'
import { SelfDisciplineType } from '../data/cropSpecifications'
import { HarvestResult } from '../systems/CropRewardSystem'

// 简单的EventEmitter实现（浏览器兼容）
class SimpleEventEmitter {
  private listeners: Map<string, Array<(...args: any[]) => void>> = new Map()
  
  on(event: string, listener: (...args: any[]) => void): this {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, [])
    }
    this.listeners.get(event)!.push(listener)
    return this
  }
  
  emit(event: string, ...args: any[]): boolean {
    const listeners = this.listeners.get(event)
    if (listeners && listeners.length > 0) {
      listeners.forEach(listener => {
        try {
          listener(...args)
        } catch (error) {
          console.error(`Event listener error for ${event}:`, error)
        }
      })
      return true
    }
    return false
  }
  
  removeAllListeners(): this {
    this.listeners.clear()
    return this
  }
}

/**
 * 农场系统集成器
 * 作为EnhancedFarmManager和现有GameStateManager之间的适配器
 * 确保新功能与现有系统的兼容性
 */
export class FarmSystemIntegrator extends SimpleEventEmitter {
  private gameStateManager: GameStateManager
  private enhancedFarmManager: EnhancedFarmManager
  private isEnhancedModeEnabled: boolean = true
  private syncInterval: number | null = null
  private lastSyncTime: number = 0
  
  constructor(
    gameStateManager: GameStateManager, 
    enhancedFarmManager?: EnhancedFarmManager
  ) {
    super()
    
    this.gameStateManager = gameStateManager
    this.enhancedFarmManager = enhancedFarmManager || new EnhancedFarmManager(8, 8)
    
    this.setupEventBridging()
    this.startStateSynchronization()
  }
  
  // ================== 适配器方法 ==================
  
  /**
   * 种植作物 - 智能路由到适当的管理器
   */
  async plantCrop(gridX: number, gridY: number, cropType: CropType): Promise<boolean> {
    try {
      if (this.isEnhancedModeEnabled && this.isNewCropType(cropType)) {
        // 使用增强农场管理器处理新作物类型
        const crop = await this.enhancedFarmManager.plantCrop(cropType, gridX, gridY)
        
        // 同步到游戏状态管理器
        await this.syncCropToGameState(crop)
        
        this.emit('crop_planted_enhanced', { crop, position: { gridX, gridY } })
        return true
      } else {
        // 使用传统游戏状态管理器处理经典作物
        const success = this.gameStateManager.plantCrop(gridX, gridY, cropType)
        
        if (success) {
          // 同步到增强农场管理器
          await this.syncCropFromGameState(gridX, gridY)
        }
        
        return success
      }
    } catch (error) {
      console.error('种植作物失败:', error)
      this.emit('plant_error', { error, position: { gridX, gridY }, cropType })
      return false
    }
  }
  
  /**
   * 收获作物 - 智能路由处理
   */
  async harvestCrop(gridX: number, gridY: number): Promise<{
    success: boolean
    rewards?: HarvestResult | any
    crop?: CropInstance
  }> {
    try {
      const gameState = this.gameStateManager.getGameState()
      const crop = gameState.farmGrid.plots[gridY]?.[gridX]
      
      if (!crop) {
        return { success: false }
      }
      
      if (this.isEnhancedModeEnabled && this.isNewCropType(crop.type)) {
        // 使用增强收获系统
        const result = await this.enhancedFarmManager.harvestCrop(crop.id)
        
        // 同步到游戏状态
        this.updateGameStateAfterHarvest(gridX, gridY, result.rewards)
        
        this.emit('crop_harvested_enhanced', { crop, rewards: result.rewards, position: { gridX, gridY } })
        
        return {
          success: true,
          rewards: result.rewards,
          crop
        }
      } else {
        // 使用传统收获系统
        const result = this.gameStateManager.harvestCrop(gridX, gridY)
        
        if (result.success && result.crop) {
          // 清理增强农场管理器中的对应作物
          await this.enhancedFarmManager.removeCrop(result.crop.id)
        }
        
        return result
      }
    } catch (error) {
      console.error('收获作物失败:', error)
      this.emit('harvest_error', { error, position: { gridX, gridY } })
      return { success: false }
    }
  }
  
  /**
   * 获取农场网格 - 统一视图
   */
  getFarmGrid(): FarmGrid {
    return this.gameStateManager.getGameState().farmGrid
  }
  
  /**
   * 获取指定位置的作物
   */
  getCropAt(gridX: number, gridY: number): CropInstance | undefined {
    const gameState = this.gameStateManager.getGameState()
    return gameState.farmGrid.plots[gridY]?.[gridX] || undefined
  }
  
  /**
   * 更新作物生长 - 智能分发到相应管理器
   */
  updateCropGrowth(deltaTime: number, focusScore: number): void {
    if (this.isEnhancedModeEnabled) {
      this.enhancedFarmManager.updateGrowth(deltaTime, focusScore)
    }
    
    // 传统作物继续通过GameStateManager的专注系统更新
    const focusState: FocusState = {
      focusScore,
      isFocused: focusScore > 60,
      sessionDuration: deltaTime,
      consecutiveFocusTime: focusScore > 60 ? deltaTime : 0,
      lastUpdateTime: Date.now()
    }
    
    this.gameStateManager.updateFocusState(focusState)
  }
  
  // ================== 增强功能接口 ==================
  
  /**
   * 开始行为会话
   */
  startBehaviorSession(behaviorType: SelfDisciplineType): string {
    if (!this.isEnhancedModeEnabled) {
      throw new Error('增强模式未启用')
    }
    
    return this.enhancedFarmManager.startBehaviorSession(behaviorType)
  }
  
  /**
   * 结束行为会话
   */
  endBehaviorSession(sessionId: string, focusData: { score: number; duration: number; timestamp: number }[]): void {
    if (!this.isEnhancedModeEnabled) {
      throw new Error('增强模式未启用')
    }
    
    this.enhancedFarmManager.endBehaviorSession(sessionId, focusData)
  }
  
  /**
   * 获取解锁的作物类型
   */
  getUnlockedCropTypes(): CropType[] {
    if (this.isEnhancedModeEnabled) {
      return this.enhancedFarmManager.getUnlockedCropTypes()
    }
    
    // 传统模式只返回基础作物类型
    return [CropType.KNOWLEDGE_FLOWER, CropType.STRENGTH_TREE, CropType.TIME_VEGGIE, CropType.MEDITATION_LOTUS]
  }
  
  /**
   * 解锁新作物类型
   */
  unlockCropType(type: CropType): void {
    if (this.isEnhancedModeEnabled) {
      this.enhancedFarmManager.unlockCropType(type)
      this.emit('crop_type_unlocked', { cropType: type })
    }
  }
  
  /**
   * 获取农场统计信息
   */
  getFarmStats(): any {
    if (this.isEnhancedModeEnabled) {
      return {
        enhanced: this.enhancedFarmManager.getStats(),
        traditional: this.gameStateManager.getGameStats()
      }
    }
    
    return { traditional: this.gameStateManager.getGameStats() }
  }
  
  // ================== 配置管理 ==================
  
  /**
   * 启用/禁用增强模式
   */
  setEnhancedMode(enabled: boolean): void {
    this.isEnhancedModeEnabled = enabled
    
    if (enabled) {
      this.enhancedFarmManager.start()
    } else {
      this.enhancedFarmManager.stop()
    }
    
    this.emit('enhanced_mode_changed', { enabled })
  }
  
  /**
   * 配置会话参数
   */
  setSessionConfig(config: Partial<SessionConfig>): void {
    if (this.isEnhancedModeEnabled) {
      this.enhancedFarmManager.setSessionConfig(config)
    }
  }
  
  // ================== 内部方法 ==================
  
  /**
   * 检查是否为新作物类型
   */
  private isNewCropType(cropType: CropType): boolean {
    const newCropTypes = [
      CropType.FOCUS_FLOWER,
      CropType.READING_VINE,
      CropType.SOCIAL_FRUIT
    ]
    return newCropTypes.includes(cropType)
  }
  
  /**
   * 设置事件桥接
   */
  private setupEventBridging(): void {
    // 暂时简化事件桥接，避免类型不匹配问题
    // 在后续版本中可以实现更完整的事件系统
    
    // 监听游戏状态管理器事件
    this.gameStateManager.on('crop_planted', (data: any) => {
      this.emit('traditional_crop_planted', data)
    })
    
    // 注意：由于EnhancedFarmManager使用Node的EventEmitter，
    // 而我们的适配器使用浏览器兼容的SimpleEventEmitter，
    // 暂时在直接调用时手动触发事件
  }
  
  /**
   * 开始状态同步
   */
  private startStateSynchronization(): void {
    this.syncInterval = setInterval(() => {
      this.performPeriodicSync()
    }, 5000) // 每5秒同步一次
  }
  
  /**
   * 执行周期性同步
   */
  private performPeriodicSync(): void {
    try {
      const now = Date.now()
      if (now - this.lastSyncTime < 4000) {
        return // 避免过于频繁的同步
      }
      
      this.syncStatesFromEnhanced()
      this.lastSyncTime = now
    } catch (error) {
      console.error('状态同步失败:', error)
    }
  }
  
  /**
   * 将增强农场状态同步到游戏状态
   */
  private syncStatesFromEnhanced(): void {
    if (!this.isEnhancedModeEnabled) return
    
    const farmState = this.enhancedFarmManager.getFarmState()
    const gameState = this.gameStateManager.getGameState()
    
    // 同步用户等级和经验
    if (farmState.userLevel !== gameState.level) {
      // 注意：这里需要确保GameStateManager有更新等级的方法
      // 当前的GameStateManager可能没有这个方法，可能需要扩展
      this.emit('level_sync_needed', { 
        currentLevel: gameState.level, 
        newLevel: farmState.userLevel 
      })
    }
    
    // 同步库存到资源系统
    farmState.inventory.forEach((amount, itemName) => {
      if (itemName.includes('精华') || itemName.includes('水晶')) {
        // 可以将特殊物品映射到游戏资源
        this.emit('inventory_sync', { itemName, amount })
      }
    })
  }
  
  /**
   * 同步作物到游戏状态
   */
  private async syncCropToGameState(crop: CropInstance): Promise<void> {
    const gameState = this.gameStateManager.getGameState()
    
    // 确保农场网格中有位置
    const { gridX, gridY } = crop.position
    if (gameState.farmGrid.plots[gridY] && gameState.farmGrid.plots[gridY][gridX] === null) {
      gameState.farmGrid.plots[gridY][gridX] = crop
      gameState.crops.set(crop.id, crop)
    }
  }
  
  /**
   * 从游戏状态同步作物
   */
  private async syncCropFromGameState(gridX: number, gridY: number): Promise<void> {
    const gameState = this.gameStateManager.getGameState()
    const crop = gameState.farmGrid.plots[gridY]?.[gridX]
    
    if (crop && !this.enhancedFarmManager.getCrop(crop.id)) {
      // 将传统作物添加到增强管理器中
      try {
        await this.enhancedFarmManager.plantCrop(crop.type, gridX, gridY)
      } catch (error) {
        console.warn('同步作物到增强管理器失败:', error)
      }
    }
  }
  
  /**
   * 收获后更新游戏状态
   */
  private updateGameStateAfterHarvest(gridX: number, gridY: number, rewards: HarvestResult): void {
    try {
      // 注意：getGameState()返回只读对象，需要通过适当的方法更新状态
      // 这里我们通过事件通知需要更新状态
      this.emit('game_state_update_needed', {
        type: 'harvest',
        position: { gridX, gridY },
        experience: rewards.totalExperience,
        growthPoints: rewards.totalGrowthPoints,
        items: rewards.specialItems
      })
      
      // 模拟更新游戏状态（在实际实现中应该通过GameStateManager的方法）
      console.log('收获完成，需要更新游戏状态:', {
        experience: rewards.totalExperience,
        growthPoints: rewards.totalGrowthPoints,
        specialItems: rewards.specialItems.length,
        achievements: rewards.achievements.length
      })
    } catch (error) {
      console.error('更新游戏状态失败:', error)
    }
  }
  
  // ================== 生命周期管理 ==================
  
  /**
   * 启动集成器
   */
  start(): void {
    if (this.isEnhancedModeEnabled) {
      this.enhancedFarmManager.start()
    }
    this.emit('integrator_started')
  }
  
  /**
   * 停止集成器
   */
  stop(): void {
    if (this.syncInterval) {
      clearInterval(this.syncInterval)
      this.syncInterval = null
    }
    
    this.enhancedFarmManager.stop()
    this.emit('integrator_stopped')
  }
  
  /**
   * 销毁集成器
   */
  destroy(): void {
    this.stop()
    this.removeAllListeners()
  }
  
  /**
   * 获取集成器状态
   */
  getIntegratorState(): {
    isEnhancedModeEnabled: boolean
    isSyncing: boolean
    lastSyncTime: number
    unlockedCropTypes: CropType[]
    farmStats: any
  } {
    return {
      isEnhancedModeEnabled: this.isEnhancedModeEnabled,
      isSyncing: this.syncInterval !== null,
      lastSyncTime: this.lastSyncTime,
      unlockedCropTypes: this.getUnlockedCropTypes(),
      farmStats: this.getFarmStats()
    }
  }
} 