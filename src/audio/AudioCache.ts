/**
 * 音频缓存管理器
 * 负责音频资源的缓存、预加载和内存优化
 */

export interface CachedAudioData {
  buffer: AudioBuffer
  url: string
  size: number
  lastAccessed: number
  accessCount: number
  preloaded: boolean
}

export interface AudioCacheStats {
  totalCached: number
  totalSize: number
  memoryUsage: number
  hitRate: number
  totalRequests: number
  cacheHits: number
  cacheMisses: number
}

export interface AudioCacheOptions {
  maxCacheSize: number          // 最大缓存大小(MB)
  maxCacheCount: number         // 最大缓存文件数
  preloadTimeout: number        // 预加载超时时间(ms)
  cleanupInterval: number       // 清理间隔(ms)
  enableAutoCleanup: boolean    // 启用自动清理
}

const DEFAULT_CACHE_OPTIONS: AudioCacheOptions = {
  maxCacheSize: 50,           // 50MB
  maxCacheCount: 20,          // 20个文件
  preloadTimeout: 5000,       // 5秒
  cleanupInterval: 60000,     // 60秒
  enableAutoCleanup: true
}

export class AudioCache {
  private cache: Map<string, CachedAudioData> = new Map()
  private loading: Map<string, Promise<AudioBuffer>> = new Map()
  private options: AudioCacheOptions
  private stats: AudioCacheStats
  private cleanupTimer?: number
  private audioContext: AudioContext

  constructor(audioContext: AudioContext, options: Partial<AudioCacheOptions> = {}) {
    this.audioContext = audioContext
    this.options = { ...DEFAULT_CACHE_OPTIONS, ...options }
    this.stats = {
      totalCached: 0,
      totalSize: 0,
      memoryUsage: 0,
      hitRate: 0,
      totalRequests: 0,
      cacheHits: 0,
      cacheMisses: 0
    }

    if (this.options.enableAutoCleanup) {
      this.startAutoCleanup()
    }
  }

  /**
   * 获取或加载音频
   */
  async getAudio(url: string, preload: boolean = false): Promise<AudioBuffer | null> {
    this.stats.totalRequests++

    // 检查缓存
    const cached = this.cache.get(url)
    if (cached) {
      cached.lastAccessed = Date.now()
      cached.accessCount++
      this.stats.cacheHits++
      this.updateHitRate()
      return cached.buffer
    }

    this.stats.cacheMisses++

    // 检查是否正在加载
    const loading = this.loading.get(url)
    if (loading) {
      try {
        return await loading
      } catch (error) {
        console.warn(`音频加载失败: ${url}`, error)
        return null
      }
    }

    // 开始加载
    const loadPromise = this.loadAudio(url, preload)
    this.loading.set(url, loadPromise)

    try {
      const buffer = await loadPromise
      return buffer
    } catch (error) {
      console.warn(`音频加载失败: ${url}`, error)
      return null
    } finally {
      this.loading.delete(url)
    }
  }

  /**
   * 预加载音频列表
   */
  async preloadAudios(urls: string[]): Promise<{
    loaded: string[]
    failed: string[]
  }> {
    const results = {
      loaded: [] as string[],
      failed: [] as string[]
    }

    console.log(`开始预加载 ${urls.length} 个音频文件...`)

    const loadPromises = urls.map(async (url) => {
      try {
        const buffer = await this.getAudio(url, true)
        if (buffer) {
          results.loaded.push(url)
        } else {
          results.failed.push(url)
        }
      } catch (error) {
        console.warn(`预加载失败: ${url}`, error)
        results.failed.push(url)
      }
    })

    await Promise.allSettled(loadPromises)

    console.log(`预加载完成: ${results.loaded.length} 成功, ${results.failed.length} 失败`)
    return results
  }

  /**
   * 加载音频文件
   */
  private async loadAudio(url: string, preload: boolean): Promise<AudioBuffer> {
    try {
      console.log(`${preload ? '预加载' : '加载'}音频: ${url}`)

      const response = await fetch(url)
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const arrayBuffer = await response.arrayBuffer()
      const audioBuffer = await this.audioContext.decodeAudioData(arrayBuffer)

      // 添加到缓存
      this.addToCache(url, audioBuffer, arrayBuffer.byteLength, preload)

      return audioBuffer
    } catch (error) {
      console.error(`音频加载失败: ${url}`, error)
      throw error
    }
  }

  /**
   * 添加到缓存
   */
  private addToCache(url: string, buffer: AudioBuffer, size: number, preloaded: boolean): void {
    // 检查缓存容量
    this.ensureCacheCapacity(size)

    const cacheData: CachedAudioData = {
      buffer,
      url,
      size,
      lastAccessed: Date.now(),
      accessCount: 1,
      preloaded
    }

    this.cache.set(url, cacheData)
    this.updateStats()

    console.log(`音频已缓存: ${url} (${(size / 1024 / 1024).toFixed(2)}MB)`)
  }

  /**
   * 确保缓存容量充足
   */
  private ensureCacheCapacity(newItemSize: number): void {
    const maxSizeBytes = this.options.maxCacheSize * 1024 * 1024
    let currentSize = this.getCurrentCacheSize()

    // 按访问时间排序，清理最久未访问的
    const sortedEntries = Array.from(this.cache.entries())
      .sort(([, a], [, b]) => a.lastAccessed - b.lastAccessed)

    while (
      (currentSize + newItemSize > maxSizeBytes || this.cache.size >= this.options.maxCacheCount) &&
      sortedEntries.length > 0
    ) {
      const [url, data] = sortedEntries.shift()!
      this.cache.delete(url)
      currentSize -= data.size
      console.log(`清理缓存: ${url} (释放 ${(data.size / 1024 / 1024).toFixed(2)}MB)`)
    }
  }

  /**
   * 获取当前缓存大小
   */
  private getCurrentCacheSize(): number {
    let size = 0
    for (const data of this.cache.values()) {
      size += data.size
    }
    return size
  }

  /**
   * 更新统计信息
   */
  private updateStats(): void {
    this.stats.totalCached = this.cache.size
    this.stats.totalSize = this.getCurrentCacheSize()
    this.stats.memoryUsage = (this.stats.totalSize / 1024 / 1024) // MB
    this.updateHitRate()
  }

  /**
   * 更新命中率
   */
  private updateHitRate(): void {
    if (this.stats.totalRequests > 0) {
      this.stats.hitRate = (this.stats.cacheHits / this.stats.totalRequests) * 100
    }
  }

  /**
   * 获取统计信息
   */
  getStats(): AudioCacheStats {
    this.updateStats()
    return { ...this.stats }
  }

  /**
   * 获取缓存详情
   */
  getCacheDetails(): Array<{
    url: string
    size: string
    lastAccessed: string
    accessCount: number
    preloaded: boolean
  }> {
    return Array.from(this.cache.entries()).map(([url, data]) => ({
      url: url.split('/').pop() || url,
      size: `${(data.size / 1024 / 1024).toFixed(2)}MB`,
      lastAccessed: new Date(data.lastAccessed).toLocaleTimeString(),
      accessCount: data.accessCount,
      preloaded: data.preloaded
    }))
  }

  /**
   * 手动清理缓存
   */
  cleanup(): void {
    const now = Date.now()
    const maxAge = 5 * 60 * 1000 // 5分钟
    let cleanedCount = 0

    for (const [url, data] of this.cache.entries()) {
      if (now - data.lastAccessed > maxAge && data.accessCount === 1 && !data.preloaded) {
        this.cache.delete(url)
        cleanedCount++
      }
    }

    if (cleanedCount > 0) {
      console.log(`清理了 ${cleanedCount} 个过期缓存项`)
      this.updateStats()
    }
  }

  /**
   * 开始自动清理
   */
  private startAutoCleanup(): void {
    this.cleanupTimer = window.setInterval(() => {
      this.cleanup()
    }, this.options.cleanupInterval)
  }

  /**
   * 停止自动清理
   */
  stopAutoCleanup(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer)
      this.cleanupTimer = undefined
    }
  }

  /**
   * 清空缓存
   */
  clear(): void {
    this.cache.clear()
    this.loading.clear()
    this.stats = {
      totalCached: 0,
      totalSize: 0,
      memoryUsage: 0,
      hitRate: 0,
      totalRequests: 0,
      cacheHits: 0,
      cacheMisses: 0
    }
    console.log('音频缓存已清空')
  }

  /**
   * 销毁缓存管理器
   */
  destroy(): void {
    this.stopAutoCleanup()
    this.clear()
  }
} 