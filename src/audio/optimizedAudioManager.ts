/**
 * 优化的音频管理器扩展
 * 集成了缓存和性能监控功能的音频管理增强版本
 */

import { AudioManager } from './AudioManager'
import { AudioCache, AudioCacheStats } from './AudioCache'
import { AudioPerformanceMonitor, AudioPerformanceMetrics } from './AudioPerformanceMonitor'
import { BACKGROUND_MUSIC, SOUND_EFFECTS } from './audioConfig'

export interface OptimizedAudioSettings {
  enableCache: boolean
  enablePerformanceMonitoring: boolean
  preloadEssentialAudios: boolean
  cacheOptions: {
    maxSize: number
    maxCount: number
    autoCleanup: boolean
  }
}

const DEFAULT_OPTIMIZED_SETTINGS: OptimizedAudioSettings = {
  enableCache: true,
  enablePerformanceMonitoring: true,
  preloadEssentialAudios: true,
  cacheOptions: {
    maxSize: 50, // 50MB
    maxCount: 20,
    autoCleanup: true
  }
}

/**
 * 优化的音频管理器
 * 扩展基础AudioManager，添加缓存和性能监控功能
 */
export class OptimizedAudioManager {
  private audioManager: AudioManager
  private audioCache: AudioCache | null = null
  private performanceMonitor: AudioPerformanceMonitor | null = null
  private settings: OptimizedAudioSettings
  private isOptimizationEnabled: boolean = false

  constructor(settings: Partial<OptimizedAudioSettings> = {}) {
    this.audioManager = AudioManager.getInstance()
    this.settings = { ...DEFAULT_OPTIMIZED_SETTINGS, ...settings }
  }

  /**
   * 初始化优化功能
   */
  async initializeOptimizations(): Promise<boolean> {
    try {
      // 确保基础音频管理器已初始化
      if (!this.audioManager.getStatus().isInitialized) {
        const success = await this.audioManager.initialize()
        if (!success) {
          console.error('基础音频管理器初始化失败')
          return false
        }
      }

      // 获取AudioContext
      const audioContext = (this.audioManager as any).audioContext
      if (!audioContext) {
        console.error('AudioContext不可用')
        return false
      }

      // 初始化缓存
      if (this.settings.enableCache) {
        this.audioCache = new AudioCache(audioContext, {
          maxCacheSize: this.settings.cacheOptions.maxSize,
          maxCacheCount: this.settings.cacheOptions.maxCount,
          enableAutoCleanup: this.settings.cacheOptions.autoCleanup
        })
        console.log('音频缓存系统已启用')
      }

      // 初始化性能监控
      if (this.settings.enablePerformanceMonitoring) {
        this.performanceMonitor = new AudioPerformanceMonitor(audioContext)
        console.log('音频性能监控已启用')
      }

      // 预加载基础音频
      if (this.settings.preloadEssentialAudios && this.audioCache) {
        await this.preloadEssentialAudios()
      }

      this.isOptimizationEnabled = true
      console.log('音频优化功能初始化完成')
      return true
    } catch (error) {
      console.error('音频优化功能初始化失败:', error)
      return false
    }
  }

  /**
   * 预加载基础音频文件
   */
  private async preloadEssentialAudios(): Promise<void> {
    if (!this.audioCache) return

    const essentialAudios: string[] = []
    
    // 添加所有背景音乐
    Object.keys(BACKGROUND_MUSIC).forEach(key => {
      essentialAudios.push(key)
    })
    
    // 添加常用音效
    const commonEffects = ['plant', 'water', 'harvest', 'levelup']
    commonEffects.forEach(effect => {
      if ((SOUND_EFFECTS as any)[effect]) {
        essentialAudios.push(effect)
      }
    })

    console.log('开始预加载基础音频文件...')
    const results = await this.audioCache.preloadAudios(essentialAudios)
    console.log(`预加载完成: ${results.loaded.length} 成功, ${results.failed.length} 失败`)
  }

  /**
   * 优化的音效播放
   */
  async playEffect(effectId: string, volume: number = 1.0): Promise<boolean> {
    const startTime = performance.now()
    let success = false
    let errorMessage: string | undefined

    try {
      // 尝试从缓存获取音频
      if (this.audioCache) {
        const buffer = await this.audioCache.getAudio(effectId)
        this.performanceMonitor?.recordCacheEvent(effectId, !!buffer)
        
        if (buffer) {
          // 使用缓存的音频播放
          success = await this.playAudioBuffer(buffer, volume, 'effect')
        } else {
          // 回退到标准播放
          success = await this.audioManager.playEffect(effectId, volume)
        }
      } else {
        // 标准播放
        success = await this.audioManager.playEffect(effectId, volume)
      }
    } catch (error) {
      success = false
      errorMessage = error instanceof Error ? error.message : '未知错误'
      console.error(`优化音效播放失败 ${effectId}:`, error)
    }

    // 记录性能数据
    this.performanceMonitor?.recordPlayEvent(effectId, startTime, success, errorMessage)
    
    return success
  }

  /**
   * 优化的音乐播放
   */
  async playMusic(musicId: string, loop: boolean = true): Promise<boolean> {
    const startTime = performance.now()
    let success = false
    let errorMessage: string | undefined

    try {
      // 尝试从缓存获取音频
      if (this.audioCache) {
        const buffer = await this.audioCache.getAudio(musicId, true) // 预加载标记
        this.performanceMonitor?.recordCacheEvent(musicId, !!buffer)
        
        if (buffer) {
          // 使用缓存的音频播放
          success = await this.playMusicBuffer(buffer, loop)
        } else {
          // 回退到标准播放
          success = await this.audioManager.playMusic(musicId, loop)
        }
      } else {
        // 标准播放
        success = await this.audioManager.playMusic(musicId, loop)
      }
    } catch (error) {
      success = false
      errorMessage = error instanceof Error ? error.message : '未知错误'
      console.error(`优化音乐播放失败 ${musicId}:`, error)
    }

    // 记录性能数据
    this.performanceMonitor?.recordPlayEvent(musicId, startTime, success, errorMessage)
    
    return success
  }

  /**
   * 播放音频缓冲区（音效）
   */
  private async playAudioBuffer(buffer: AudioBuffer, volume: number, type: 'effect' | 'music'): Promise<boolean> {
    const audioContext = (this.audioManager as any).audioContext
    if (!audioContext) return false

    try {
      const source = audioContext.createBufferSource()
      const gainNode = audioContext.createGain()
      
      source.buffer = buffer
      source.connect(gainNode)
      
      // 连接到相应的输出节点
      const outputNode = type === 'effect' 
        ? (this.audioManager as any).effectsGainNode 
        : (this.audioManager as any).musicGainNode
      
      if (outputNode) {
        gainNode.connect(outputNode)
        
        // 设置音量
        const settings = this.audioManager.getSettings()
        const finalVolume = type === 'effect'
          ? volume * settings.effectsVolume * settings.masterVolume
          : volume * settings.musicVolume * settings.masterVolume
        
        gainNode.gain.setValueAtTime(finalVolume, audioContext.currentTime)
        
        // 播放
        source.start(0)
        
        // 记录实例数
        this.updateInstanceCount()
        
        // 清理
        source.onended = () => {
          this.updateInstanceCount()
        }
        
        return true
      }
    } catch (error) {
      console.error('播放音频缓冲区失败:', error)
    }
    
    return false
  }

  /**
   * 播放音乐缓冲区
   */
  private async playMusicBuffer(buffer: AudioBuffer, loop: boolean): Promise<boolean> {
    // 先停止当前音乐
    this.audioManager.stopMusic()
    
    const audioContext = (this.audioManager as any).audioContext
    const musicGainNode = (this.audioManager as any).musicGainNode
    
    if (!audioContext || !musicGainNode) return false

    try {
      const source = audioContext.createBufferSource()
      const gainNode = audioContext.createGain()
      
      source.buffer = buffer
      source.loop = loop
      source.connect(gainNode)
      gainNode.connect(musicGainNode)
      
      // 设置音量
      const settings = this.audioManager.getSettings()
      const finalVolume = settings.musicVolume * settings.masterVolume
      gainNode.gain.setValueAtTime(finalVolume, audioContext.currentTime)
      
      // 播放
      source.start(0)
      
      // 更新当前音乐引用
      ;(this.audioManager as any).currentMusic = {
        buffer,
        source,
        gainNode,
        isPlaying: true,
        startTime: audioContext.currentTime,
        isPaused: false
      }
      
      return true
    } catch (error) {
      console.error('播放音乐缓冲区失败:', error)
      return false
    }
  }

  /**
   * 更新实例计数
   */
  private updateInstanceCount(): void {
    if (!this.performanceMonitor) return
    
    const audioContext = (this.audioManager as any).audioContext
    if (!audioContext) return
    
    // 简单估算活跃实例数
    const instances = (this.audioManager as any).audioInstances
    const activeCount = instances ? instances.size : 0
    
    this.performanceMonitor.recordInstanceCount(activeCount)
  }

  /**
   * 获取性能报告
   */
  getPerformanceReport(): {
    metrics: AudioPerformanceMetrics
    warnings: string[]
    recommendations: string[]
  } | null {
    if (!this.performanceMonitor) return null
    
    const report = this.performanceMonitor.getPerformanceReport()
    
    // 添加缓存统计到指标中
    if (this.audioCache) {
      const cacheStats = this.audioCache.getStats()
      report.metrics.cacheHitRate = cacheStats.hitRate
      report.metrics.cacheSize = cacheStats.memoryUsage
      report.metrics.preloadedCount = cacheStats.totalCached
    }
    
    return report
  }

  /**
   * 获取缓存详情
   */
  getCacheDetails(): Array<{
    url: string
    size: string
    lastAccessed: string
    accessCount: number
    preloaded: boolean
  }> {
    return this.audioCache?.getCacheDetails() || []
  }

  /**
   * 获取缓存统计
   */
  getCacheStats(): AudioCacheStats | null {
    return this.audioCache?.getStats() || null
  }

  /**
   * 手动清理缓存
   */
  cleanupCache(): void {
    this.audioCache?.cleanup()
  }

  /**
   * 清空缓存
   */
  clearCache(): void {
    this.audioCache?.clear()
  }

  /**
   * 重置性能统计
   */
  resetPerformanceStats(): void {
    this.performanceMonitor?.reset()
  }

  /**
   * 导出性能数据
   */
  exportPerformanceData(): any {
    return this.performanceMonitor?.exportData() || null
  }

  /**
   * 获取优化状态
   */
  getOptimizationStatus(): {
    enabled: boolean
    cache: boolean
    monitoring: boolean
    settings: OptimizedAudioSettings
  } {
    return {
      enabled: this.isOptimizationEnabled,
      cache: !!this.audioCache,
      monitoring: !!this.performanceMonitor,
      settings: this.settings
    }
  }

  /**
   * 代理其他AudioManager方法
   */
  stopMusic(): void {
    this.audioManager.stopMusic()
  }

  toggleMusic(): boolean {
    return this.audioManager.toggleMusic()
  }

  setVolume(type: 'master' | 'music' | 'effects', value: number): void {
    this.audioManager.setVolume(type, value)
  }

  setEnabled(type: 'music' | 'effects', enabled: boolean): void {
    this.audioManager.setEnabled(type, enabled)
  }

  getSettings() {
    return this.audioManager.getSettings()
  }

  applySettings(settings: any): void {
    this.audioManager.applySettings(settings)
  }

  getStatus() {
    return this.audioManager.getStatus()
  }

  async playGameEventSound(event: any, volume: number = 1.0): Promise<boolean> {
    return this.playEffect(event, volume)
  }

  /**
   * 销毁优化功能
   */
  destroy(): void {
    this.audioCache?.destroy()
    this.performanceMonitor?.reset()
    this.audioCache = null
    this.performanceMonitor = null
    this.isOptimizationEnabled = false
  }
} 