import { 
  AudioTrack, 
  AudioSettings, 
  DEFAULT_AUDIO_SETTINGS,
  BACKGROUND_MUSIC,
  SOUND_EFFECTS,
  GAME_EVENT_SOUNDS,
  GameEvent 
} from './audioConfig'
import { AudioPlaceholderGenerator, AUDIO_PLACEHOLDERS } from './audioPlaceholders'
import { AudioCache } from './AudioCache'
import { AudioPerformanceMonitor } from './AudioPerformanceMonitor'
import { AudioCache } from './AudioCache'
import { AudioPerformanceMonitor } from './AudioPerformanceMonitor'

/**
 * 音频实例接口
 */
interface AudioInstance {
  buffer: AudioBuffer
  source?: AudioBufferSourceNode
  gainNode?: GainNode
  track: AudioTrack
  isPlaying: boolean
  startTime: number
  isPaused: boolean
  pausedAt?: number
}

/**
 * 音频管理器
 * 负责管理游戏中的所有音频播放，包括背景音乐和音效
 */
export class AudioManager {
  private static instance: AudioManager
  private audioContext: AudioContext | null = null
  private masterGainNode: GainNode | null = null
  private musicGainNode: GainNode | null = null
  private effectsGainNode: GainNode | null = null
  
  // 音频实例缓存
  private audioInstances: Map<string, AudioInstance> = new Map()
  private currentMusic: AudioInstance | null = null
  
  // 配置和状态
  private settings: AudioSettings = { ...DEFAULT_AUDIO_SETTINGS }
  private isInitialized: boolean = false
  private placeholderGenerator: AudioPlaceholderGenerator
  
  // 音频预加载缓存 (废弃，使用新的缓存系统)
  private audioBuffers: Map<string, AudioBuffer> = new Map()
  private loadingPromises: Map<string, Promise<AudioBuffer>> = new Map()

  // 新的缓存和性能监控系统
  private audioCache: AudioCache | null = null
  private performanceMonitor: AudioPerformanceMonitor | null = null

  private constructor() {
    this.placeholderGenerator = new AudioPlaceholderGenerator()
  }

  /**
   * 获取单例实例
   */
  static getInstance(): AudioManager {
    if (!AudioManager.instance) {
      AudioManager.instance = new AudioManager()
    }
    return AudioManager.instance
  }

  /**
   * 初始化音频系统
   */
  async initialize(): Promise<boolean> {
    try {
      // 初始化 AudioContext
      if (!this.audioContext) {
        this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)()
      }

      // 处理 AudioContext 被暂停的情况
      if (this.audioContext.state === 'suspended') {
        await this.audioContext.resume()
      }

      // 初始化缓存和性能监控
      this.audioCache = new AudioCache(this.audioContext, {
        maxCacheSize: 50,
        maxCacheCount: 20,
        enableAutoCleanup: true
      })
      
      this.performanceMonitor = new AudioPerformanceMonitor(this.audioContext)

      // 创建主增益节点
      this.masterGainNode = this.audioContext.createGain()
      this.musicGainNode = this.audioContext.createGain()
      this.effectsGainNode = this.audioContext.createGain()

      // 连接音频图
      this.musicGainNode.connect(this.masterGainNode)
      this.effectsGainNode.connect(this.masterGainNode)
      this.masterGainNode.connect(this.audioContext.destination)

      // 设置初始音量
      this.updateVolumes()

      // 预加载音频占位符和常用音频
      await this.preloadPlaceholders()
      await this.preloadEssentialAudios()

      this.isInitialized = true
      console.log('音频系统初始化成功')
      return true
    } catch (error) {
      console.error('音频系统初始化失败:', error)
      return false
    }
  }

  /**
   * 预加载基础音频文件
   */
  private async preloadEssentialAudios(): Promise<void> {
    if (!this.audioCache) return

    const essentialAudios: string[] = []
    
    // 添加所有背景音乐
    Object.keys(BACKGROUND_MUSIC).forEach(key => {
      essentialAudios.push(key)
    })
    
    // 添加常用音效
    const commonEffects = ['plant', 'water', 'harvest', 'levelup']
    commonEffects.forEach(effect => {
      if (SOUND_EFFECTS[effect]) {
        essentialAudios.push(effect)
      }
    })

    console.log('开始预加载基础音频文件...')
    const results = await this.audioCache.preloadAudios(essentialAudios)
    console.log(`预加载完成: ${results.loaded.length} 成功, ${results.failed.length} 失败`)
  }

  /**
   * 预加载音频占位符
   */
  private async preloadPlaceholders(): Promise<void> {
    const loadPromises: Promise<void>[] = []

    for (const [id, config] of Object.entries(AUDIO_PLACEHOLDERS)) {
      const loadPromise = this.generatePlaceholderAudio(id, config)
        .then(buffer => {
          if (buffer) {
            this.audioBuffers.set(id, buffer)
          }
        })
        .catch(error => {
          console.warn(`生成占位符音频失败 ${id}:`, error)
        })
      
      loadPromises.push(loadPromise)
    }

    await Promise.all(loadPromises)
    console.log(`已预加载 ${this.audioBuffers.size} 个音频占位符`)
  }

  /**
   * 生成占位符音频
   */
  private async generatePlaceholderAudio(id: string, config: any): Promise<AudioBuffer | null> {
    try {
      switch (config.generator) {
        case 'tone':
          return await this.placeholderGenerator.generateTone(
            config.frequency,
            config.duration,
            config.type || 'sine'
          )
        case 'noise':
          return await this.placeholderGenerator.generateNoise(
            config.duration,
            config.filterFreq || 1000
          )
        case 'chord':
          return await this.placeholderGenerator.generateChord(
            config.frequencies,
            config.duration
          )
        default:
          console.warn(`未知的音频生成器类型: ${config.generator}`)
          return null
      }
    } catch (error) {
      console.error(`生成占位符音频失败 ${id}:`, error)
      return null
    }
  }

  /**
   * 播放音效
   */
  async playEffect(effectId: string, volume: number = 1.0): Promise<boolean> {
    if (!this.isInitialized || !this.settings.effectsEnabled) {
      return false
    }

    try {
      const buffer = await this.getAudioBuffer(effectId)
      if (!buffer || !this.audioContext || !this.effectsGainNode) {
        return false
      }

      // 创建音频源
      const source = this.audioContext.createBufferSource()
      const gainNode = this.audioContext.createGain()

      source.buffer = buffer
      source.connect(gainNode)
      gainNode.connect(this.effectsGainNode)

      // 设置音量
      const finalVolume = volume * this.settings.effectsVolume * this.settings.masterVolume
      gainNode.gain.setValueAtTime(finalVolume, this.audioContext.currentTime)

      // 播放音效
      source.start(0)

      // 创建音频实例记录
      const track = this.findTrackById(effectId)
      if (track) {
        const instance: AudioInstance = {
          buffer,
          source,
          gainNode,
          track,
          isPlaying: true,
          startTime: this.audioContext.currentTime,
          isPaused: false
        }

        this.audioInstances.set(`effect_${Date.now()}_${effectId}`, instance)

        // 播放结束后清理
        source.onended = () => {
          this.cleanupAudioInstance(instance)
        }
      }

      return true
    } catch (error) {
      console.error(`播放音效失败 ${effectId}:`, error)
      return false
    }
  }

  /**
   * 播放背景音乐
   */
  async playMusic(musicId: string, loop: boolean = true): Promise<boolean> {
    if (!this.isInitialized || !this.settings.musicEnabled) {
      return false
    }

    // 停止当前音乐
    if (this.currentMusic) {
      this.stopMusic()
    }

    try {
      const buffer = await this.getAudioBuffer(musicId)
      if (!buffer || !this.audioContext || !this.musicGainNode) {
        return false
      }

      // 创建音频源
      const source = this.audioContext.createBufferSource()
      const gainNode = this.audioContext.createGain()

      source.buffer = buffer
      source.loop = loop
      source.connect(gainNode)
      gainNode.connect(this.musicGainNode)

      // 设置音量
      const finalVolume = this.settings.musicVolume * this.settings.masterVolume
      gainNode.gain.setValueAtTime(finalVolume, this.audioContext.currentTime)

      // 播放音乐
      source.start(0)

      // 创建音频实例记录
      const track = this.findTrackById(musicId)
      if (track) {
        this.currentMusic = {
          buffer,
          source,
          gainNode,
          track,
          isPlaying: true,
          startTime: this.audioContext.currentTime,
          isPaused: false
        }

        // 更新当前音乐设置
        this.settings.currentMusic = musicId

        // 播放结束处理（仅对非循环音乐）
        if (!loop) {
          source.onended = () => {
            this.currentMusic = null
          }
        }
      }

      return true
    } catch (error) {
      console.error(`播放背景音乐失败 ${musicId}:`, error)
      return false
    }
  }

  /**
   * 停止背景音乐
   */
  stopMusic(): void {
    if (this.currentMusic && this.currentMusic.source) {
      try {
        this.currentMusic.source.stop()
      } catch (error) {
        // 忽略已经停止的音频源错误
      }
      this.currentMusic = null
    }
  }

  /**
   * 暂停/恢复背景音乐
   */
  toggleMusic(): boolean {
    if (!this.currentMusic) {
      return false
    }

    if (this.currentMusic.isPaused) {
      // 恢复播放
      this.resumeMusic()
      return true
    } else {
      // 暂停播放
      this.pauseMusic()
      return false
    }
  }

  /**
   * 暂停背景音乐
   */
  private pauseMusic(): void {
    if (this.currentMusic && this.currentMusic.source && this.audioContext) {
      this.currentMusic.isPaused = true
      this.currentMusic.pausedAt = this.audioContext.currentTime
      
      // 逐渐降低音量
      if (this.currentMusic.gainNode) {
        this.currentMusic.gainNode.gain.exponentialRampToValueAtTime(
          0.001, 
          this.audioContext.currentTime + 0.1
        )
      }
    }
  }

  /**
   * 恢复背景音乐
   */
  private resumeMusic(): void {
    if (this.currentMusic && this.currentMusic.gainNode && this.audioContext) {
      this.currentMusic.isPaused = false
      this.currentMusic.pausedAt = undefined
      
      // 恢复音量
      const targetVolume = this.settings.musicVolume * this.settings.masterVolume
      this.currentMusic.gainNode.gain.exponentialRampToValueAtTime(
        Math.max(targetVolume, 0.001),
        this.audioContext.currentTime + 0.1
      )
    }
  }

  /**
   * 根据游戏事件播放音效
   */
  async playGameEventSound(event: GameEvent, volume: number = 1.0): Promise<boolean> {
    const soundId = GAME_EVENT_SOUNDS[event]
    if (soundId) {
      return await this.playEffect(soundId, volume)
    }
    return false
  }

  /**
   * 获取音频缓冲区
   */
  private async getAudioBuffer(audioId: string): Promise<AudioBuffer | null> {
    // 先检查缓存
    if (this.audioBuffers.has(audioId)) {
      return this.audioBuffers.get(audioId)!
    }

    // 检查是否正在加载
    if (this.loadingPromises.has(audioId)) {
      return await this.loadingPromises.get(audioId)!
    }

    // 尝试加载音频文件
    const loadPromise = this.loadAudioFile(audioId)
    this.loadingPromises.set(audioId, loadPromise)

    try {
      const buffer = await loadPromise
      this.audioBuffers.set(audioId, buffer)
      return buffer
    } catch (error) {
      console.warn(`加载音频文件失败，使用占位符: ${audioId}`, error)
      // 回退到占位符
      return this.audioBuffers.get(audioId) || null
    } finally {
      this.loadingPromises.delete(audioId)
    }
  }

  /**
   * 加载音频文件
   */
  private async loadAudioFile(audioId: string): Promise<AudioBuffer> {
    if (!this.audioContext) {
      throw new Error('AudioContext 未初始化')
    }

    const track = this.findTrackById(audioId)
    if (!track) {
      throw new Error(`找不到音频配置: ${audioId}`)
    }

    const response = await fetch(track.file)
    if (!response.ok) {
      throw new Error(`加载音频文件失败: ${response.status}`)
    }

    const arrayBuffer = await response.arrayBuffer()
    const audioBuffer = await this.audioContext.decodeAudioData(arrayBuffer)
    return audioBuffer
  }

  /**
   * 查找音频轨道配置
   */
  private findTrackById(audioId: string): AudioTrack | null {
    const allTracks = [...BACKGROUND_MUSIC, ...SOUND_EFFECTS]
    return allTracks.find(track => track.id === audioId) || null
  }

  /**
   * 更新音量设置
   */
  private updateVolumes(): void {
    if (!this.masterGainNode || !this.musicGainNode || !this.effectsGainNode) {
      return
    }

    // 更新主音量
    this.masterGainNode.gain.setValueAtTime(
      this.settings.masterVolume,
      this.audioContext?.currentTime || 0
    )

    // 更新音乐音量
    this.musicGainNode.gain.setValueAtTime(
      this.settings.musicVolume,
      this.audioContext?.currentTime || 0
    )

    // 更新音效音量
    this.effectsGainNode.gain.setValueAtTime(
      this.settings.effectsVolume,
      this.audioContext?.currentTime || 0
    )
  }

  /**
   * 设置音量
   */
  setVolume(type: 'master' | 'music' | 'effects', value: number): void {
    value = Math.max(0, Math.min(1, value)) // 限制在 0-1 范围内

    switch (type) {
      case 'master':
        this.settings.masterVolume = value
        break
      case 'music':
        this.settings.musicVolume = value
        break
      case 'effects':
        this.settings.effectsVolume = value
        break
    }

    this.updateVolumes()
  }

  /**
   * 启用/禁用音频
   */
  setEnabled(type: 'music' | 'effects', enabled: boolean): void {
    switch (type) {
      case 'music':
        this.settings.musicEnabled = enabled
        if (!enabled && this.currentMusic) {
          this.stopMusic()
        }
        break
      case 'effects':
        this.settings.effectsEnabled = enabled
        break
    }
  }

  /**
   * 获取当前设置
   */
  getSettings(): Readonly<AudioSettings> {
    return { ...this.settings }
  }

  /**
   * 应用设置
   */
  applySettings(newSettings: Partial<AudioSettings>): void {
    this.settings = { ...this.settings, ...newSettings }
    this.updateVolumes()

    // 如果禁用了音乐，停止当前播放
    if (!this.settings.musicEnabled && this.currentMusic) {
      this.stopMusic()
    }
  }

  /**
   * 清理音频实例
   */
  private cleanupAudioInstance(instance: AudioInstance): void {
    instance.isPlaying = false
    if (instance.source) {
      try {
        instance.source.disconnect()
      } catch (error) {
        // 忽略断开连接错误
      }
    }
    if (instance.gainNode) {
      try {
        instance.gainNode.disconnect()
      } catch (error) {
        // 忽略断开连接错误
      }
    }
  }

  /**
   * 销毁音频管理器
   */
  destroy(): void {
    // 停止所有音频
    this.stopMusic()
    
    // 清理音频实例
    this.audioInstances.forEach(instance => {
      this.cleanupAudioInstance(instance)
    })
    this.audioInstances.clear()

    // 关闭 AudioContext
    if (this.audioContext && this.audioContext.state !== 'closed') {
      this.audioContext.close()
    }

    this.audioContext = null
    this.masterGainNode = null
    this.musicGainNode = null
    this.effectsGainNode = null
    this.isInitialized = false

    console.log('音频系统已销毁')
  }

  /**
   * 获取音频系统状态
   */
  getStatus(): {
    isInitialized: boolean
    isPlaying: boolean
    currentMusic?: string
    audioContextState?: string
    loadedAudios: number
  } {
    return {
      isInitialized: this.isInitialized,
      isPlaying: this.currentMusic?.isPlaying || false,
      currentMusic: this.currentMusic?.track.id,
      audioContextState: this.audioContext?.state,
      loadedAudios: this.audioBuffers.size
    }
  }
} 