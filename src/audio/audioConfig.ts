// 音频资源配置
export interface AudioTrack {
  id: string
  name: string
  file: string
  volume: number
  loop: boolean
  category: 'music' | 'effect'
  description: string
}

// 背景音乐配置
export const BACKGROUND_MUSIC: AudioTrack[] = [
  {
    id: 'farm_ambient',
    name: '农场环境音',
    file: '/audio/music/farm_ambient.mp3',
    volume: 0.3,
    loop: true,
    category: 'music',
    description: '温和的农场背景音乐，适合专注时播放'
  },
  {
    id: 'peaceful_meadow',
    name: '宁静草地',
    file: '/audio/music/peaceful_meadow.mp3',
    volume: 0.25,
    loop: true,
    category: 'music',
    description: '平静舒缓的旋律，帮助用户集中注意力'
  },
  {
    id: 'morning_garden',
    name: '清晨花园',
    file: '/audio/music/morning_garden.mp3',
    volume: 0.28,
    loop: true,
    category: 'music',
    description: '清新的早晨氛围音乐，激发活力'
  }
]

// 音效配置
export const SOUND_EFFECTS: AudioTrack[] = [
  {
    id: 'plant_seed',
    name: '种植种子',
    file: '/audio/effects/plant_seed.wav',
    volume: 0.6,
    loop: false,
    category: 'effect',
    description: '种植时的轻柔土壤声音'
  },
  {
    id: 'water_pour',
    name: '浇水',
    file: '/audio/effects/water_pour.wav',
    volume: 0.5,
    loop: false,
    category: 'effect',
    description: '浇水时的流水声'
  },
  {
    id: 'crop_harvest',
    name: '收获作物',
    file: '/audio/effects/crop_harvest.wav',
    volume: 0.7,
    loop: false,
    category: 'effect',
    description: '收获时的轻快采摘声'
  },
  {
    id: 'growth_complete',
    name: '成长完成',
    file: '/audio/effects/growth_complete.wav',
    volume: 0.4,
    loop: false,
    category: 'effect',
    description: '作物成熟时的铃声提示'
  },
  {
    id: 'focus_boost',
    name: '专注加成',
    file: '/audio/effects/focus_boost.wav',
    volume: 0.3,
    loop: false,
    category: 'effect',
    description: '专注度提升时的正向反馈音效'
  },
  {
    id: 'ui_click',
    name: 'UI点击',
    file: '/audio/effects/ui_click.wav',
    volume: 0.4,
    loop: false,
    category: 'effect',
    description: '界面按钮点击音效'
  },
  {
    id: 'achievement',
    name: '成就解锁',
    file: '/audio/effects/achievement.wav',
    volume: 0.6,
    loop: false,
    category: 'effect',
    description: '获得成就时的庆祝音效'
  }
]

// 天气音效配置
export const WEATHER_SOUND_EFFECTS: AudioTrack[] = [
  // 晴天音效
  {
    id: 'weather_sunny_birds',
    name: '晴天鸟鸣',
    file: '/audio/weather/sunny_birds.mp3',
    volume: 0.3,
    loop: true,
    category: 'effect',
    description: '晴朗天气的鸟叫声，营造温暖舒适的氛围'
  },
  {
    id: 'weather_sunny_breeze',
    name: '晴天微风',
    file: '/audio/weather/sunny_breeze.mp3',
    volume: 0.2,
    loop: true,
    category: 'effect',
    description: '温和的微风声，增添自然宁静感'
  },
  
  // 部分多云音效
  {
    id: 'weather_partly_cloudy_wind',
    name: '多云微风',
    file: '/audio/weather/partly_cloudy_wind.mp3',
    volume: 0.25,
    loop: true,
    category: 'effect',
    description: '多云天气的轻柔风声'
  },
  {
    id: 'weather_partly_cloudy_birds_distant',
    name: '远方鸟叫',
    file: '/audio/weather/partly_cloudy_birds_distant.mp3',
    volume: 0.2,
    loop: true,
    category: 'effect',
    description: '远处偶尔传来的鸟叫声'
  },
  
  // 多云音效
  {
    id: 'weather_cloudy_wind',
    name: '多云风声',
    file: '/audio/weather/cloudy_wind.mp3',
    volume: 0.3,
    loop: true,
    category: 'effect',
    description: '阴天的持续风声'
  },
  {
    id: 'weather_cloudy_atmosphere',
    name: '多云氛围',
    file: '/audio/weather/cloudy_atmosphere.mp3',
    volume: 0.25,
    loop: true,
    category: 'effect',
    description: '阴云密布的环境氛围音'
  },
  
  // 雨天音效
  {
    id: 'weather_rain_light',
    name: '小雨',
    file: '/audio/weather/rain_light.mp3',
    volume: 0.4,
    loop: true,
    category: 'effect',
    description: '轻柔的雨滴声，营造宁静的雨天氛围'
  },
  {
    id: 'weather_rain_drops',
    name: '雨滴声',
    file: '/audio/weather/rain_drops.mp3',
    volume: 0.35,
    loop: true,
    category: 'effect',
    description: '雨滴落在叶子上的清脆声音'
  },
  
  // 大雨音效
  {
    id: 'weather_heavy_rain',
    name: '大雨',
    file: '/audio/weather/heavy_rain.mp3',
    volume: 0.5,
    loop: true,
    category: 'effect',
    description: '密集的大雨声，营造强烈的降雨氛围'
  },
  {
    id: 'weather_heavy_rain_wind',
    name: '大雨伴风',
    file: '/audio/weather/heavy_rain_wind.mp3',
    volume: 0.4,
    loop: true,
    category: 'effect',
    description: '大雨中的强风声'
  },
  
  // 雷暴音效
  {
    id: 'weather_thunderstorm_rain',
    name: '暴雨',
    file: '/audio/weather/thunderstorm_rain.mp3',
    volume: 0.6,
    loop: true,
    category: 'effect',
    description: '雷暴中的猛烈降雨声'
  },
  {
    id: 'weather_thunder_distant',
    name: '远雷',
    file: '/audio/weather/thunder_distant.mp3',
    volume: 0.4,
    loop: false,
    category: 'effect',
    description: '远处传来的低沉雷声'
  },
  {
    id: 'weather_thunder_close',
    name: '近雷',
    file: '/audio/weather/thunder_close.mp3',
    volume: 0.5,
    loop: false,
    category: 'effect',
    description: '近距离的强烈雷声'
  },
  {
    id: 'weather_storm_wind',
    name: '风暴风声',
    file: '/audio/weather/storm_wind.mp3',
    volume: 0.45,
    loop: true,
    category: 'effect',
    description: '风暴中的狂风声'
  },
  
  // 雪天音效
  {
    id: 'weather_snow_wind',
    name: '雪天风声',
    file: '/audio/weather/snow_wind.mp3',
    volume: 0.3,
    loop: true,
    category: 'effect',
    description: '雪天的轻柔风声'
  },
  {
    id: 'weather_snow_falling',
    name: '雪花飘落',
    file: '/audio/weather/snow_falling.mp3',
    volume: 0.2,
    loop: true,
    category: 'effect',
    description: '雪花轻柔飘落的微妙声音'
  },
  
  // 雾天音效
  {
    id: 'weather_fog_atmosphere',
    name: '雾天氛围',
    file: '/audio/weather/fog_atmosphere.mp3',
    volume: 0.25,
    loop: true,
    category: 'effect',
    description: '雾气弥漫的神秘氛围音'
  },
  {
    id: 'weather_fog_echo',
    name: '雾天回声',
    file: '/audio/weather/fog_echo.mp3',
    volume: 0.2,
    loop: true,
    category: 'effect',
    description: '雾天中的轻微回声效果'
  },
  
  // 风天音效
  {
    id: 'weather_wind_strong',
    name: '强风',
    file: '/audio/weather/wind_strong.mp3',
    volume: 0.45,
    loop: true,
    category: 'effect',
    description: '强劲的风声，营造动感的大风天氛围'
  },
  {
    id: 'weather_wind_leaves',
    name: '风吹叶子',
    file: '/audio/weather/wind_leaves.mp3',
    volume: 0.35,
    loop: true,
    category: 'effect',
    description: '风吹动树叶的沙沙声'
  },
  {
    id: 'weather_wind_gusts',
    name: '阵风',
    file: '/audio/weather/wind_gusts.mp3',
    volume: 0.4,
    loop: false,
    category: 'effect',
    description: '间歇性的强烈阵风声'
  }
]

// 音频类型枚举
export enum AudioType {
  BACKGROUND_MUSIC = 'background_music',
  SOUND_EFFECT = 'sound_effect'
}

// 音频设置
export interface AudioSettings {
  masterVolume: number
  musicVolume: number
  effectsVolume: number
  musicEnabled: boolean
  effectsEnabled: boolean
  currentMusic?: string
}

// 默认音频设置
export const DEFAULT_AUDIO_SETTINGS: AudioSettings = {
  masterVolume: 0.7,
  musicVolume: 0.5,
  effectsVolume: 0.8,
  musicEnabled: true,
  effectsEnabled: true,
  currentMusic: 'farm_ambient'
}

// 游戏事件音效映射
export const GAME_EVENT_SOUNDS = {
  CROP_PLANTED: 'plant_seed',
  CROP_WATERED: 'water_pour',
  CROP_HARVESTED: 'crop_harvest',
  CROP_GROWN: 'growth_complete',
  FOCUS_IMPROVED: 'focus_boost',
  UI_INTERACTION: 'ui_click',
  ACHIEVEMENT_UNLOCKED: 'achievement'
} as const

export type GameEvent = keyof typeof GAME_EVENT_SOUNDS 