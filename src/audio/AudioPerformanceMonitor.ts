/**
 * 音频性能监控器
 * 监控音频播放性能、延迟和资源使用情况
 */

export interface AudioPerformanceMetrics {
  // 延迟指标
  averageLatency: number        // 平均播放延迟(ms)
  minLatency: number           // 最小延迟(ms)
  maxLatency: number           // 最大延迟(ms)
  
  // 播放统计
  totalPlays: number           // 总播放次数
  successfulPlays: number      // 成功播放次数
  failedPlays: number          // 失败播放次数
  successRate: number          // 成功率(%)
  
  // 资源指标
  activeInstances: number      // 活跃音频实例数
  peakInstances: number        // 峰值实例数
  memoryUsage: number          // 内存使用量(MB)
  
  // 系统指标
  audioContextState: string    // 音频上下文状态
  sampleRate: number          // 采样率
  baseLatency: number         // 基础延迟
  outputLatency: number       // 输出延迟
  
  // 缓存指标
  cacheHitRate: number        // 缓存命中率(%)
  cacheSize: number           // 缓存大小(MB)
  preloadedCount: number      // 预加载文件数
}

export interface PerformanceEvent {
  timestamp: number
  type: 'play' | 'stop' | 'error' | 'load' | 'cache_hit' | 'cache_miss'
  audioId: string
  latency?: number
  success?: boolean
  errorMessage?: string
  memoryUsage?: number
}

export class AudioPerformanceMonitor {
  private events: PerformanceEvent[] = []
  private maxEvents: number = 1000
  private audioContext: AudioContext | null = null
  private startTime: number = performance.now()
  private latencyMeasurements: number[] = []
  private instanceCounts: number[] = []
  
  // 性能阈值
  private readonly WARNING_LATENCY = 100  // 100ms
  private readonly ERROR_LATENCY = 200    // 200ms
  private readonly MAX_INSTANCES = 10     // 最大同时播放数

  constructor(audioContext?: AudioContext | null) {
    this.audioContext = audioContext || null
  }

  /**
   * 设置音频上下文
   */
  setAudioContext(audioContext: AudioContext): void {
    this.audioContext = audioContext
  }

  /**
   * 记录播放事件
   */
  recordPlayEvent(audioId: string, startTime: number, success: boolean, errorMessage?: string): void {
    const latency = performance.now() - startTime
    
    this.addEvent({
      timestamp: Date.now(),
      type: success ? 'play' : 'error',
      audioId,
      latency,
      success,
      errorMessage
    })

    if (success) {
      this.latencyMeasurements.push(latency)
      
      // 限制延迟测量记录数量
      if (this.latencyMeasurements.length > 100) {
        this.latencyMeasurements = this.latencyMeasurements.slice(-50)
      }

      // 延迟警告
      if (latency > this.WARNING_LATENCY) {
        const level = latency > this.ERROR_LATENCY ? 'error' : 'warn'
        console[level](`音频播放延迟过高: ${audioId} - ${latency.toFixed(2)}ms`)
      }
    }
  }

  /**
   * 记录停止事件
   */
  recordStopEvent(audioId: string): void {
    this.addEvent({
      timestamp: Date.now(),
      type: 'stop',
      audioId
    })
  }

  /**
   * 记录加载事件
   */
  recordLoadEvent(audioId: string, loadTime: number, success: boolean): void {
    this.addEvent({
      timestamp: Date.now(),
      type: 'load',
      audioId,
      latency: loadTime,
      success
    })
  }

  /**
   * 记录缓存事件
   */
  recordCacheEvent(audioId: string, hit: boolean): void {
    this.addEvent({
      timestamp: Date.now(),
      type: hit ? 'cache_hit' : 'cache_miss',
      audioId
    })
  }

  /**
   * 记录实例数量
   */
  recordInstanceCount(count: number): void {
    this.instanceCounts.push(count)
    
    // 限制记录数量
    if (this.instanceCounts.length > 100) {
      this.instanceCounts = this.instanceCounts.slice(-50)
    }

    // 实例数警告
    if (count > this.MAX_INSTANCES) {
      console.warn(`音频实例数过多: ${count}，可能影响性能`)
    }
  }

  /**
   * 记录内存使用
   */
  recordMemoryUsage(usage: number): void {
    this.addEvent({
      timestamp: Date.now(),
      type: 'play', // 使用play类型记录内存
      audioId: 'memory',
      memoryUsage: usage
    })
  }

  /**
   * 添加事件
   */
  private addEvent(event: PerformanceEvent): void {
    this.events.push(event)
    
    // 限制事件数量
    if (this.events.length > this.maxEvents) {
      this.events = this.events.slice(-Math.floor(this.maxEvents * 0.8))
    }
  }

  /**
   * 获取性能指标
   */
  getMetrics(): AudioPerformanceMetrics {
    const now = Date.now()
    const recentEvents = this.events.filter(e => now - e.timestamp < 60000) // 最近1分钟

    // 延迟指标
    const latencies = this.latencyMeasurements
    const avgLatency = latencies.length > 0 
      ? latencies.reduce((sum, lat) => sum + lat, 0) / latencies.length 
      : 0
    const minLatency = latencies.length > 0 ? Math.min(...latencies) : 0
    const maxLatency = latencies.length > 0 ? Math.max(...latencies) : 0

    // 播放统计
    const playEvents = recentEvents.filter(e => e.type === 'play' || e.type === 'error')
    const successfulPlays = playEvents.filter(e => e.success === true).length
    const failedPlays = playEvents.filter(e => e.success === false).length
    const totalPlays = successfulPlays + failedPlays
    const successRate = totalPlays > 0 ? (successfulPlays / totalPlays) * 100 : 100

    // 实例指标
    const activeInstances = this.instanceCounts.length > 0 
      ? this.instanceCounts[this.instanceCounts.length - 1] 
      : 0
    const peakInstances = this.instanceCounts.length > 0 
      ? Math.max(...this.instanceCounts) 
      : 0

    // 内存指标
    const memoryEvents = this.events.filter(e => e.memoryUsage !== undefined)
    const memoryUsage = memoryEvents.length > 0 
      ? memoryEvents[memoryEvents.length - 1].memoryUsage! 
      : 0

    // 音频上下文指标
    const audioContextState = this.audioContext?.state || 'unknown'
    const sampleRate = this.audioContext?.sampleRate || 44100
    const baseLatency = this.audioContext?.baseLatency ? this.audioContext.baseLatency * 1000 : 0
    const outputLatency = this.audioContext?.outputLatency ? this.audioContext.outputLatency * 1000 : 0

    // 缓存指标
    const cacheHits = recentEvents.filter(e => e.type === 'cache_hit').length
    const cacheMisses = recentEvents.filter(e => e.type === 'cache_miss').length
    const totalCacheRequests = cacheHits + cacheMisses
    const cacheHitRate = totalCacheRequests > 0 ? (cacheHits / totalCacheRequests) * 100 : 0

    return {
      averageLatency: avgLatency,
      minLatency,
      maxLatency,
      totalPlays,
      successfulPlays,
      failedPlays,
      successRate,
      activeInstances,
      peakInstances,
      memoryUsage,
      audioContextState,
      sampleRate,
      baseLatency,
      outputLatency,
      cacheHitRate,
      cacheSize: memoryUsage, // 假设缓存大小等于内存使用
      preloadedCount: 0 // 需要从外部传入
    }
  }

  /**
   * 获取性能报告
   */
  getPerformanceReport(): {
    metrics: AudioPerformanceMetrics
    warnings: string[]
    recommendations: string[]
  } {
    const metrics = this.getMetrics()
    const warnings: string[] = []
    const recommendations: string[] = []

    // 延迟警告
    if (metrics.averageLatency > this.WARNING_LATENCY) {
      warnings.push(`平均播放延迟过高: ${metrics.averageLatency.toFixed(2)}ms`)
      if (metrics.averageLatency > this.ERROR_LATENCY) {
        recommendations.push('考虑启用音频预加载或优化音频文件大小')
      }
    }

    // 成功率警告
    if (metrics.successRate < 95) {
      warnings.push(`音频播放成功率较低: ${metrics.successRate.toFixed(1)}%`)
      recommendations.push('检查音频文件路径和网络连接状况')
    }

    // 实例数警告
    if (metrics.peakInstances > this.MAX_INSTANCES) {
      warnings.push(`同时播放音频数过多: ${metrics.peakInstances}`)
      recommendations.push('限制同时播放的音频数量，实现音频池管理')
    }

    // 内存使用警告
    if (metrics.memoryUsage > 100) { // 100MB
      warnings.push(`音频内存使用过高: ${metrics.memoryUsage.toFixed(2)}MB`)
      recommendations.push('启用音频缓存清理或减少预加载的音频数量')
    }

    // 缓存命中率
    if (metrics.cacheHitRate < 80 && metrics.totalPlays > 10) {
      warnings.push(`缓存命中率较低: ${metrics.cacheHitRate.toFixed(1)}%`)
      recommendations.push('增加音频预加载或调整缓存策略')
    }

    // 音频上下文状态
    if (metrics.audioContextState === 'suspended') {
      warnings.push('音频上下文处于暂停状态')
      recommendations.push('等待用户交互后恢复音频上下文')
    }

    return {
      metrics,
      warnings,
      recommendations
    }
  }

  /**
   * 获取最近事件
   */
  getRecentEvents(count: number = 20): PerformanceEvent[] {
    return this.events.slice(-count).reverse()
  }

  /**
   * 清理旧数据
   */
  cleanup(): void {
    const now = Date.now()
    const maxAge = 10 * 60 * 1000 // 10分钟
    
    this.events = this.events.filter(e => now - e.timestamp < maxAge)
    
    // 保留最近的延迟测量
    if (this.latencyMeasurements.length > 50) {
      this.latencyMeasurements = this.latencyMeasurements.slice(-25)
    }
    
    // 保留最近的实例计数
    if (this.instanceCounts.length > 50) {
      this.instanceCounts = this.instanceCounts.slice(-25)
    }
  }

  /**
   * 重置统计
   */
  reset(): void {
    this.events = []
    this.latencyMeasurements = []
    this.instanceCounts = []
    this.startTime = performance.now()
  }

  /**
   * 导出性能数据
   */
  exportData(): {
    startTime: number
    uptime: number
    events: PerformanceEvent[]
    latencies: number[]
    instances: number[]
    metrics: AudioPerformanceMetrics
  } {
    return {
      startTime: this.startTime,
      uptime: performance.now() - this.startTime,
      events: [...this.events],
      latencies: [...this.latencyMeasurements],
      instances: [...this.instanceCounts],
      metrics: this.getMetrics()
    }
  }
} 