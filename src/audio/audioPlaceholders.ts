// 音频占位符生成器
// 在开发阶段使用，生成简单的音频文件作为占位符

/**
 * 生成简单的音频缓冲区用作占位符
 * 使用Web Audio API生成基本的音调和音效
 */
export class AudioPlaceholderGenerator {
  private audioContext: AudioContext | null = null

  constructor() {
    if (typeof window !== 'undefined' && 'AudioContext' in window) {
      this.audioContext = new AudioContext()
    }
  }

  /**
   * 生成简单的音调
   * @param frequency 频率 (Hz)
   * @param duration 持续时间 (秒)
   * @param type 波形类型
   */
  async generateTone(
    frequency: number, 
    duration: number, 
    type: OscillatorType = 'sine'
  ): Promise<AudioBuffer | null> {
    if (!this.audioContext) return null

    const sampleRate = this.audioContext.sampleRate
    const length = sampleRate * duration
    const buffer = this.audioContext.createBuffer(1, length, sampleRate)
    const data = buffer.getChannelData(0)

    for (let i = 0; i < length; i++) {
      const time = i / sampleRate
      switch (type) {
        case 'sine':
          data[i] = Math.sin(2 * Math.PI * frequency * time) * 0.3
          break
        case 'square':
          data[i] = Math.sign(Math.sin(2 * Math.PI * frequency * time)) * 0.2
          break
        case 'triangle':
          data[i] = (2 / Math.PI) * Math.asin(Math.sin(2 * Math.PI * frequency * time)) * 0.3
          break
        case 'sawtooth':
          data[i] = (2 * ((frequency * time) % 1) - 1) * 0.2
          break
      }
      
      // 淡入淡出效果
      const fadeLength = Math.min(0.1 * sampleRate, length / 4)
      if (i < fadeLength) {
        data[i] *= i / fadeLength
      } else if (i > length - fadeLength) {
        data[i] *= (length - i) / fadeLength
      }
    }

    return buffer
  }

  /**
   * 生成噪音音效
   * @param duration 持续时间
   * @param filterFreq 过滤器频率
   */
  async generateNoise(duration: number, filterFreq: number = 1000): Promise<AudioBuffer | null> {
    if (!this.audioContext) return null

    const sampleRate = this.audioContext.sampleRate
    const length = sampleRate * duration
    const buffer = this.audioContext.createBuffer(1, length, sampleRate)
    const data = buffer.getChannelData(0)

    for (let i = 0; i < length; i++) {
      data[i] = (Math.random() * 2 - 1) * 0.1
      
      // 简单的低通滤波效果
      if (i > 0) {
        const alpha = filterFreq / (filterFreq + sampleRate)
        data[i] = alpha * data[i] + (1 - alpha) * data[i - 1]
      }
    }

    return buffer
  }

  /**
   * 生成复合音效（多个频率组合）
   */
  async generateChord(frequencies: number[], duration: number): Promise<AudioBuffer | null> {
    if (!this.audioContext) return null

    const sampleRate = this.audioContext.sampleRate
    const length = sampleRate * duration
    const buffer = this.audioContext.createBuffer(1, length, sampleRate)
    const data = buffer.getChannelData(0)

    for (let i = 0; i < length; i++) {
      const time = i / sampleRate
      let sample = 0
      
      frequencies.forEach((freq, index) => {
        const amplitude = 0.2 / frequencies.length
        sample += Math.sin(2 * Math.PI * freq * time) * amplitude
      })
      
      data[i] = sample
      
      // 淡入淡出
      const fadeLength = Math.min(0.05 * sampleRate, length / 6)
      if (i < fadeLength) {
        data[i] *= i / fadeLength
      } else if (i > length - fadeLength) {
        data[i] *= (length - i) / fadeLength
      }
    }

    return buffer
  }
}

/**
 * 预设的音频占位符配置
 */
export const AUDIO_PLACEHOLDERS = {
  // 背景音乐占位符 - 持续的和谐音调
  farm_ambient: {
    generator: 'chord',
    frequencies: [220, 293.66, 369.99], // A3, D4, F#4 - 舒缓的和弦
    duration: 10, // 10秒循环
    description: '农场环境音占位符'
  },
  
  peaceful_meadow: {
    generator: 'chord', 
    frequencies: [196, 261.63, 329.63], // G3, C4, E4 - 平静的大三和弦
    duration: 12,
    description: '宁静草地占位符'
  },
  
  morning_garden: {
    generator: 'chord',
    frequencies: [174.61, 220, 277.18], // F3, A3, C#4 - 清新的和弦
    duration: 8,
    description: '清晨花园占位符'
  },
  
  // 音效占位符 - 短促的音调或噪音
  plant_seed: {
    generator: 'tone',
    frequency: 440,
    duration: 0.3,
    type: 'sine',
    description: '种植音效占位符'
  },
  
  water_pour: {
    generator: 'noise',
    duration: 1.5,
    filterFreq: 800,
    description: '浇水音效占位符'
  },
  
  crop_harvest: {
    generator: 'tone',
    frequency: 523.25, // C5
    duration: 0.5,
    type: 'triangle',
    description: '收获音效占位符'
  },
  
  growth_complete: {
    generator: 'chord',
    frequencies: [523.25, 659.25, 783.99], // C5, E5, G5 - 明亮的和弦
    duration: 1.0,
    description: '成长完成音效占位符'
  },
  
  focus_boost: {
    generator: 'tone',
    frequency: 880, // A5
    duration: 0.4,
    type: 'sine',
    description: '专注提升音效占位符'
  },
  
  ui_click: {
    generator: 'tone',
    frequency: 1200,
    duration: 0.1,
    type: 'square',
    description: 'UI点击音效占位符'
  },
  
  achievement: {
    generator: 'chord',
    frequencies: [523.25, 659.25, 783.99, 1046.5], // C5, E5, G5, C6
    duration: 1.5,
    description: '成就音效占位符'
  }
} as const

/**
 * 音频占位符说明文档
 */
export const AUDIO_PLACEHOLDER_README = `
# 音频占位符说明

## 概述
这些占位符音频使用Web Audio API生成，用于开发阶段的测试和原型制作。

## 背景音乐占位符
- farm_ambient: 舒缓的三和弦循环 (A-D-F#)
- peaceful_meadow: 平静的大三和弦 (G-C-E) 
- morning_garden: 清新的和弦 (F-A-C#)

## 音效占位符
- plant_seed: 440Hz正弦波，模拟种植声音
- water_pour: 过滤白噪音，模拟流水声
- crop_harvest: 523Hz三角波，模拟收获声音
- growth_complete: 明亮和弦 (C-E-G)，庆祝成长
- focus_boost: 880Hz正弦波，积极反馈
- ui_click: 1200Hz方波，界面交互
- achievement: 四声部和弦，庆祝成就

## 替换说明
在生产环境中，这些占位符应该被替换为：
1. 专业录制的音乐文件 (.mp3, .ogg)
2. 高质量的音效样本 (.wav, .ogg)
3. 符合农场主题的真实音频内容
4. 经过版权清理的音频资源

## 使用建议
- 背景音乐应该循环播放
- 音效应该响应用户操作
- 音量应该可调节
- 考虑用户的专注需求，避免过于突兀的音效
` 