import { platformDetector, OperatingSystem, DeviceType } from './PlatformDetector';
import { responsiveAdapter, ScreenSize } from './ResponsiveAdapter';

// 优化类型定义
export enum OptimizationType {
  PERFORMANCE = 'performance',
  MEMORY = 'memory',
  BATTERY = 'battery',
  RENDERING = 'rendering',
  NETWORK = 'network',
  ACCESSIBILITY = 'accessibility'
}

export enum OptimizationLevel {
  NONE = 'none',
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  AGGRESSIVE = 'aggressive'
}

export interface OptimizationRule {
  id: string;
  type: OptimizationType;
  level: OptimizationLevel;
  condition: (platform: any, viewport: any) => boolean;
  action: () => void;
  description: string;
  impact: string;
  reversible: boolean;
}

export interface PlatformOptimizationConfig {
  enableAnimations: boolean;
  enableShadows: boolean;
  enableBlur: boolean;
  enableTransitions: boolean;
  maxImageQuality: number; // 0-100
  maxVideoQuality: string; // 'low', 'medium', 'high'
  enableLazyLoading: boolean;
  enableServiceWorker: boolean;
  enablePreloading: boolean;
  enableCompression: boolean;
  maxConcurrentRequests: number;
  cacheDuration: number; // milliseconds
  memoryLimit: number; // MB
  enableVirtualization: boolean;
  enableCodeSplitting: boolean;
}

export interface PerformanceMetrics {
  fps: number;
  memoryUsage: number; // MB
  networkLatency: number; // ms
  renderTime: number; // ms
  loadTime: number; // ms
  interactionLatency: number; // ms
}

export interface OptimizationReport {
  appliedOptimizations: OptimizationRule[];
  performanceGain: number; // percentage
  memoryReduction: number; // MB
  batteryImpact: number; // percentage
  userExperienceScore: number; // 0-100
  recommendations: string[];
  warnings: string[];
}

// 平台特定的默认配置
const PLATFORM_OPTIMIZATION_CONFIGS: Record<string, Partial<PlatformOptimizationConfig>> = {
  // 移动设备优化
  [`${DeviceType.MOBILE}-${OperatingSystem.IOS}`]: {
    enableAnimations: false,
    enableShadows: false,
    enableBlur: false,
    enableTransitions: true,
    maxImageQuality: 75,
    maxVideoQuality: 'medium',
    enableLazyLoading: true,
    enableServiceWorker: true,
    enablePreloading: false,
    maxConcurrentRequests: 4,
    cacheDuration: 24 * 60 * 60 * 1000, // 24小时
    memoryLimit: 512,
    enableVirtualization: true,
    enableCodeSplitting: true
  },
  [`${DeviceType.MOBILE}-${OperatingSystem.ANDROID}`]: {
    enableAnimations: false,
    enableShadows: false,
    enableBlur: false,
    enableTransitions: true,
    maxImageQuality: 70,
    maxVideoQuality: 'medium',
    enableLazyLoading: true,
    enableServiceWorker: true,
    enablePreloading: false,
    maxConcurrentRequests: 3,
    cacheDuration: 24 * 60 * 60 * 1000,
    memoryLimit: 256,
    enableVirtualization: true,
    enableCodeSplitting: true
  },
  // 平板设备优化
  [`${DeviceType.TABLET}-${OperatingSystem.IOS}`]: {
    enableAnimations: true,
    enableShadows: true,
    enableBlur: true,
    enableTransitions: true,
    maxImageQuality: 85,
    maxVideoQuality: 'high',
    enableLazyLoading: true,
    enableServiceWorker: true,
    enablePreloading: true,
    maxConcurrentRequests: 6,
    cacheDuration: 48 * 60 * 60 * 1000,
    memoryLimit: 1024,
    enableVirtualization: false,
    enableCodeSplitting: true
  },
  // 桌面设备优化
  [`${DeviceType.DESKTOP}-${OperatingSystem.WINDOWS}`]: {
    enableAnimations: true,
    enableShadows: true,
    enableBlur: true,
    enableTransitions: true,
    maxImageQuality: 95,
    maxVideoQuality: 'high',
    enableLazyLoading: false,
    enableServiceWorker: true,
    enablePreloading: true,
    maxConcurrentRequests: 8,
    cacheDuration: 7 * 24 * 60 * 60 * 1000,
    memoryLimit: 2048,
    enableVirtualization: false,
    enableCodeSplitting: false
  }
};

// 默认配置
const DEFAULT_CONFIG: PlatformOptimizationConfig = {
  enableAnimations: true,
  enableShadows: true,
  enableBlur: true,
  enableTransitions: true,
  maxImageQuality: 90,
  maxVideoQuality: 'high',
  enableLazyLoading: true,
  enableServiceWorker: true,
  enablePreloading: true,
  enableCompression: true,
  maxConcurrentRequests: 6,
  cacheDuration: 24 * 60 * 60 * 1000,
  memoryLimit: 1024,
  enableVirtualization: false,
  enableCodeSplitting: true
};

export class PlatformOptimizer {
  private static instance: PlatformOptimizer;
  private config: PlatformOptimizationConfig;
  private appliedOptimizations: OptimizationRule[] = [];
  private performanceMetrics: PerformanceMetrics | null = null;
  private isInitialized = false;
  private performanceObserver: PerformanceObserver | null = null;
  private memoryMonitor: any = null;
  private optimizationRules: OptimizationRule[] = [];

  private constructor() {
    this.config = this.generatePlatformConfig();
    this.setupOptimizationRules();
  }

  public static getInstance(): PlatformOptimizer {
    if (!PlatformOptimizer.instance) {
      PlatformOptimizer.instance = new PlatformOptimizer();
    }
    return PlatformOptimizer.instance;
  }

  /**
   * 初始化平台优化器
   */
  public async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    try {
      await this.detectCapabilities();
      this.setupPerformanceMonitoring();
      this.applyOptimizations();
      this.isInitialized = true;
    } catch (error) {
      console.error('平台优化器初始化失败:', error);
      throw error;
    }
  }

  /**
   * 生成平台特定配置
   */
  private generatePlatformConfig(): PlatformOptimizationConfig {
    const platform = platformDetector.getPlatformInfo();
    const configKey = `${platform.device}-${platform.os}`;
    const platformConfig = PLATFORM_OPTIMIZATION_CONFIGS[configKey];

    return {
      ...DEFAULT_CONFIG,
      ...platformConfig
    };
  }

  /**
   * 设置优化规则
   */
  private setupOptimizationRules(): void {
    this.optimizationRules = [
      // 移动设备性能优化
      {
        id: 'mobile-disable-animations',
        type: OptimizationType.PERFORMANCE,
        level: OptimizationLevel.HIGH,
        condition: (platform, viewport) => 
          platform.device === DeviceType.MOBILE || viewport?.screenSize === ScreenSize.MOBILE,
        action: () => this.disableAnimations(),
        description: '在移动设备上禁用动画以提升性能',
        impact: '提升帧率10-20%，减少CPU使用',
        reversible: true
      },
      
      // 低内存优化
      {
        id: 'low-memory-optimization',
        type: OptimizationType.MEMORY,
        level: OptimizationLevel.HIGH,
        condition: () => this.getAvailableMemory() < 1024,
        action: () => this.enableMemoryOptimization(),
        description: '启用内存优化，减少内存占用',
        impact: '减少内存使用30-50%',
        reversible: true
      },

      // 慢网络优化
      {
        id: 'slow-network-optimization',
        type: OptimizationType.NETWORK,
        level: OptimizationLevel.MEDIUM,
        condition: () => this.getNetworkSpeed() === 'slow',
        action: () => this.enableNetworkOptimization(),
        description: '启用网络优化，减少数据传输',
        impact: '减少网络请求40-60%',
        reversible: true
      },

      // 电池优化
      {
        id: 'battery-optimization',
        type: OptimizationType.BATTERY,
        level: OptimizationLevel.MEDIUM,
        condition: () => this.getBatteryLevel() < 20,
        action: () => this.enableBatteryOptimization(),
        description: '启用电池优化，延长电池寿命',
        impact: '减少电池消耗20-30%',
        reversible: true
      },

      // 高分辨率显示优化
      {
        id: 'high-dpi-optimization',
        type: OptimizationType.RENDERING,
        level: OptimizationLevel.LOW,
        condition: (platform, viewport) => viewport?.pixelRatio > 2,
        action: () => this.enableHighDPIOptimization(),
        description: '优化高分辨率显示',
        impact: '改善显示质量，轻微增加GPU使用',
        reversible: true
      }
    ];
  }

  /**
   * 检测设备能力
   */
  private async detectCapabilities(): Promise<void> {
    if (typeof window === 'undefined') {
      return;
    }

    try {
      // 检测GPU能力
      const canvas = document.createElement('canvas');
      const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
      const gpuInfo = gl ? (gl as WebGLRenderingContext).getParameter((gl as WebGLRenderingContext).RENDERER) : 'Unknown';

      // 检测CPU核心数
      const cpuCores = navigator.hardwareConcurrency || 1;

      // 检测内存（如果支持）
      const memory = (navigator as any).deviceMemory || 'Unknown';

      // 检测网络连接
      const connection = (navigator as any).connection || (navigator as any).mozConnection || (navigator as any).webkitConnection;
      const networkType = connection?.effectiveType || 'Unknown';

      console.log('设备能力检测:', {
        gpu: gpuInfo,
        cpuCores,
        memory,
        networkType
      });

    } catch (error) {
      console.warn('设备能力检测失败:', error);
    }
  }

  /**
   * 设置性能监控
   */
  private setupPerformanceMonitoring(): void {
    if (typeof window === 'undefined' || !window.PerformanceObserver) {
      return;
    }

    try {
      // 监控帧率
      this.performanceObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        this.processPerformanceEntries(entries);
      });

      this.performanceObserver.observe({ 
        entryTypes: ['paint', 'navigation', 'resource', 'measure'] 
      });

      // 监控内存使用（如果支持）
      if ('memory' in performance) {
        this.startMemoryMonitoring();
      }

    } catch (error) {
      console.warn('性能监控设置失败:', error);
    }
  }

  /**
   * 处理性能指标
   */
  private processPerformanceEntries(entries: PerformanceEntry[]): void {
    entries.forEach(entry => {
      switch (entry.entryType) {
        case 'paint':
          if (entry.name === 'first-contentful-paint') {
            console.log('首次内容绘制时间:', entry.startTime);
          }
          break;
        case 'navigation':
          const navEntry = entry as PerformanceNavigationTiming;
          console.log('页面加载时间:', navEntry.loadEventEnd - navEntry.fetchStart);
          break;
      }
    });
  }

  /**
   * 开始内存监控
   */
  private startMemoryMonitoring(): void {
    this.memoryMonitor = setInterval(() => {
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        const memoryUsage = {
          used: Math.round(memory.usedJSHeapSize / 1048576), // MB
          total: Math.round(memory.totalJSHeapSize / 1048576), // MB
          limit: Math.round(memory.jsHeapSizeLimit / 1048576) // MB
        };

        // 如果内存使用超过阈值，触发优化
        if (memoryUsage.used > this.config.memoryLimit) {
          this.triggerMemoryOptimization();
        }
      }
    }, 10000); // 每10秒检查一次
  }

  /**
   * 应用优化
   */
  private applyOptimizations(): void {
    const platform = platformDetector.getPlatformInfo();
    const viewport = responsiveAdapter.getViewportInfo();

    this.optimizationRules.forEach(rule => {
      if (rule.condition(platform, viewport)) {
        try {
          rule.action();
          this.appliedOptimizations.push(rule);
          console.log(`应用优化: ${rule.description}`);
        } catch (error) {
          console.error(`优化应用失败: ${rule.id}`, error);
        }
      }
    });

    this.applyPlatformSpecificOptimizations();
  }

  /**
   * 应用平台特定优化
   */
  private applyPlatformSpecificOptimizations(): void {
    if (typeof document === 'undefined') {
      return;
    }

    const root = document.documentElement;

    // 设置CSS变量
    root.style.setProperty('--enable-animations', this.config.enableAnimations ? '1' : '0');
    root.style.setProperty('--enable-shadows', this.config.enableShadows ? '1' : '0');
    root.style.setProperty('--enable-blur', this.config.enableBlur ? '1' : '0');
    root.style.setProperty('--enable-transitions', this.config.enableTransitions ? '1' : '0');
    root.style.setProperty('--max-image-quality', `${this.config.maxImageQuality}%`);
    root.style.setProperty('--max-concurrent-requests', `${this.config.maxConcurrentRequests}`);

    // 添加优化类
    if (!this.config.enableAnimations) {
      root.classList.add('no-animations');
    }
    if (!this.config.enableShadows) {
      root.classList.add('no-shadows');
    }
    if (!this.config.enableBlur) {
      root.classList.add('no-blur');
    }
    if (this.config.enableVirtualization) {
      root.classList.add('enable-virtualization');
    }
  }

  /**
   * 禁用动画
   */
  private disableAnimations(): void {
    this.config.enableAnimations = false;
    this.config.enableTransitions = false;
    
    if (typeof document !== 'undefined') {
      const style = document.createElement('style');
      style.textContent = `
        *, *::before, *::after {
          animation-duration: 0.01ms !important;
          animation-iteration-count: 1 !important;
          transition-duration: 0.01ms !important;
          scroll-behavior: auto !important;
        }
      `;
      document.head.appendChild(style);
    }
  }

  /**
   * 启用内存优化
   */
  private enableMemoryOptimization(): void {
    this.config.enableVirtualization = true;
    this.config.enableLazyLoading = true;
    this.config.maxImageQuality = Math.min(this.config.maxImageQuality, 60);
    this.config.maxConcurrentRequests = Math.min(this.config.maxConcurrentRequests, 3);
  }

  /**
   * 启用网络优化
   */
  private enableNetworkOptimization(): void {
    this.config.enableCompression = true;
    this.config.enablePreloading = false;
    this.config.maxImageQuality = Math.min(this.config.maxImageQuality, 70);
    this.config.maxVideoQuality = 'low';
    this.config.maxConcurrentRequests = 2;
  }

  /**
   * 启用电池优化
   */
  private enableBatteryOptimization(): void {
    this.config.enableAnimations = false;
    this.config.enableShadows = false;
    this.config.enableBlur = false;
    this.config.maxImageQuality = Math.min(this.config.maxImageQuality, 50);
    this.config.maxVideoQuality = 'low';
  }

  /**
   * 启用高DPI优化
   */
  private enableHighDPIOptimization(): void {
    this.config.maxImageQuality = Math.min(this.config.maxImageQuality + 10, 100);
  }

  /**
   * 触发内存优化
   */
  private triggerMemoryOptimization(): void {
    // 清理未使用的缓存
    if (typeof window !== 'undefined' && window.caches) {
      window.caches.keys().then(names => {
        names.forEach(name => {
          if (name.includes('old') || name.includes('temp')) {
            window.caches.delete(name);
          }
        });
      });
    }

    // 垃圾回收提示（如果支持）
    if (typeof window !== 'undefined' && (window as any).gc) {
      (window as any).gc();
    }
  }

  /**
   * 获取可用内存
   */
  private getAvailableMemory(): number {
    if (typeof navigator !== 'undefined' && (navigator as any).deviceMemory) {
      return (navigator as any).deviceMemory * 1024; // GB转MB
    }
    return 2048; // 默认2GB
  }

  /**
   * 获取网络速度
   */
  private getNetworkSpeed(): string {
    if (typeof navigator !== 'undefined') {
      const connection = (navigator as any).connection || (navigator as any).mozConnection;
      if (connection) {
        const type = connection.effectiveType;
        if (type === 'slow-2g' || type === '2g') return 'slow';
        if (type === '3g') return 'medium';
        return 'fast';
      }
    }
    return 'unknown';
  }

  /**
   * 获取电池电量
   */
  private getBatteryLevel(): number {
    // 电池API已被废弃，返回默认值
    return 50;
  }

  /**
   * 获取当前配置
   */
  public getConfig(): PlatformOptimizationConfig {
    return { ...this.config };
  }

  /**
   * 更新配置
   */
  public updateConfig(updates: Partial<PlatformOptimizationConfig>): void {
    this.config = { ...this.config, ...updates };
    this.applyPlatformSpecificOptimizations();
  }

  /**
   * 获取性能指标
   */
  public getPerformanceMetrics(): PerformanceMetrics | null {
    return this.performanceMetrics;
  }

  /**
   * 获取优化报告
   */
  public getOptimizationReport(): OptimizationReport {
    return {
      appliedOptimizations: [...this.appliedOptimizations],
      performanceGain: this.calculatePerformanceGain(),
      memoryReduction: this.calculateMemoryReduction(),
      batteryImpact: this.calculateBatteryImpact(),
      userExperienceScore: this.calculateUXScore(),
      recommendations: this.generateRecommendations(),
      warnings: this.generateWarnings()
    };
  }

  /**
   * 计算性能提升
   */
  private calculatePerformanceGain(): number {
    // 基于应用的优化计算性能提升百分比
    let gain = 0;
    this.appliedOptimizations.forEach(opt => {
      switch (opt.type) {
        case OptimizationType.PERFORMANCE:
          gain += opt.level === OptimizationLevel.HIGH ? 15 : 
                 opt.level === OptimizationLevel.MEDIUM ? 8 : 3;
          break;
        case OptimizationType.MEMORY:
          gain += opt.level === OptimizationLevel.HIGH ? 10 : 5;
          break;
      }
    });
    return Math.min(gain, 100);
  }

  /**
   * 计算内存减少
   */
  private calculateMemoryReduction(): number {
    let reduction = 0;
    this.appliedOptimizations.forEach(opt => {
      if (opt.type === OptimizationType.MEMORY) {
        reduction += opt.level === OptimizationLevel.HIGH ? 128 : 64;
      }
    });
    return reduction;
  }

  /**
   * 计算电池影响
   */
  private calculateBatteryImpact(): number {
    let impact = 0;
    this.appliedOptimizations.forEach(opt => {
      if (opt.type === OptimizationType.BATTERY) {
        impact += opt.level === OptimizationLevel.HIGH ? 25 : 15;
      }
    });
    return Math.min(impact, 100);
  }

  /**
   * 计算用户体验分数
   */
  private calculateUXScore(): number {
    const platform = platformDetector.getPlatformInfo();
    let score = 70; // 基础分数

    // 根据平台调整
    if (platform.device === DeviceType.MOBILE) {
      score += this.config.enableLazyLoading ? 10 : -5;
      score += !this.config.enableAnimations ? 5 : -10;
    } else {
      score += this.config.enableAnimations ? 10 : -5;
      score += this.config.enableShadows ? 5 : 0;
    }

    return Math.max(0, Math.min(100, score));
  }

  /**
   * 生成建议
   */
  private generateRecommendations(): string[] {
    const recommendations: string[] = [];
    const platform = platformDetector.getPlatformInfo();

    if (platform.device === DeviceType.MOBILE) {
      recommendations.push('考虑为移动设备启用更激进的优化');
      if (this.config.enableAnimations) {
        recommendations.push('建议在移动设备上禁用动画以提升性能');
      }
    }

    if (this.getAvailableMemory() < 1024) {
      recommendations.push('启用内存优化以改善性能');
    }

    if (this.getNetworkSpeed() === 'slow') {
      recommendations.push('启用网络优化以减少数据使用');
    }

    return recommendations;
  }

  /**
   * 生成警告
   */
  private generateWarnings(): string[] {
    const warnings: string[] = [];

    if (!this.config.enableAnimations && !this.config.enableTransitions) {
      warnings.push('完全禁用动画可能影响用户体验');
    }

    if (this.config.maxImageQuality < 50) {
      warnings.push('图片质量过低可能影响视觉效果');
    }

    return warnings;
  }

  /**
   * 获取诊断信息
   */
  public getDiagnosticInfo(): string {
    const platform = platformDetector.getPlatformInfo();
    
    return JSON.stringify({
      isInitialized: this.isInitialized,
      platform,
      config: this.config,
      appliedOptimizations: this.appliedOptimizations.map(opt => ({
        id: opt.id,
        type: opt.type,
        level: opt.level,
        description: opt.description
      })),
      performanceMetrics: this.performanceMetrics,
      optimizationReport: this.getOptimizationReport(),
      timestamp: new Date().toISOString()
    }, null, 2);
  }

  /**
   * 释放资源
   */
  public dispose(): void {
    if (this.performanceObserver) {
      this.performanceObserver.disconnect();
      this.performanceObserver = null;
    }

    if (this.memoryMonitor) {
      clearInterval(this.memoryMonitor);
      this.memoryMonitor = null;
    }

    this.appliedOptimizations = [];
  }
}

// 导出单例实例
export const platformOptimizer = PlatformOptimizer.getInstance();

// 便捷函数
export const initializePlatformOptimizer = () => platformOptimizer.initialize();
export const getPlatformConfig = () => platformOptimizer.getConfig();
export const updatePlatformConfig = (config: Partial<PlatformOptimizationConfig>) => platformOptimizer.updateConfig(config);
export const getOptimizationReport = () => platformOptimizer.getOptimizationReport();
export const getPerformanceMetrics = () => platformOptimizer.getPerformanceMetrics();
