/**
 * 高级多帧数据分析器
 * 处理多帧姿态数据，分析时间序列变化趋势，提升判断准确性
 */

import { 
  PoseLandmark, 
  PostureDataPoint, 
  EnhancedPostureAnalysis 
} from '../types/pose'

/**
 * 多帧分析配置
 */
export interface MultiFrameConfig {
  frameWindowSize: number
  frameStride: number
  minFramesForAnalysis: number
  continuityThreshold: number
  motionSmoothnessFactor: number
  stabilityWindow: number
  stabilityThreshold: number
  trendAnalysisEnabled: boolean
  trendSensitivity: number
  frameDropDetection: boolean
  inconsistencyDetection: boolean
  predictionEnabled: boolean
  predictionHorizon: number
}

/**
 * 帧间变化数据
 */
export interface FrameChangeData {
  frameIndex: number
  timestamp: number
  positionChanges: {
    head: { x: number; y: number; z: number }
    shoulders: { left: number; right: number }
    bodyCenter: { x: number; y: number }
  }
  velocities: {
    headVelocity: number
    shoulderVelocity: number
    bodyVelocity: number
  }
  accelerations: {
    headAcceleration: number
    shoulderAcceleration: number
    bodyAcceleration: number
  }
}

/**
 * 多帧分析结果
 */
export interface MultiFrameAnalysisResult {
  frameCount: number
  timeSpan: number
  averageFPS: number
  posturalStability: {
    overall: number
    head: number
    shoulders: number
    body: number
  }
  movementContinuity: {
    overall: number
    smoothness: number
    predictability: number
  }
  trends: {
    focusScoreTrend: 'improving' | 'stable' | 'declining'
    postureQualityTrend: 'improving' | 'stable' | 'declining'
    movementTrend: 'increasing' | 'stable' | 'decreasing'
  }
  multiFrameFocusScore: number
  confidenceLevel: number
  anomalies: {
    frameDrops: number[]
    suddenMovements: number[]
    inconsistencies: number[]
  }
  predictions?: {
    nextFrameFocusScore: number
    stabilityForecast: number
    recommendedActions: string[]
  }
  recommendations: string[]
}

/**
 * 高级多帧数据分析器
 */
export class AdvancedMultiFrameAnalyzer {
  private config: MultiFrameConfig
  private frameBuffer: EnhancedPostureAnalysis[] = []
  private changeHistory: FrameChangeData[] = []
  private previousFrame: EnhancedPostureAnalysis | null = null
  
  constructor(config: Partial<MultiFrameConfig> = {}) {
    this.config = {
      frameWindowSize: 30,
      frameStride: 1,
      minFramesForAnalysis: 10,
      continuityThreshold: 0.7,
      motionSmoothnessFactor: 0.8,
      stabilityWindow: 15,
      stabilityThreshold: 0.85,
      trendAnalysisEnabled: true,
      trendSensitivity: 0.3,
      frameDropDetection: true,
      inconsistencyDetection: true,
      predictionEnabled: true,
      predictionHorizon: 5,
      ...config
    }
  }
  
  /**
   * 添加新帧数据
   */
  public addFrame(frameAnalysis: EnhancedPostureAnalysis): void {
    // 计算帧间变化
    if (this.previousFrame) {
      const changeData = this.calculateFrameChanges(this.previousFrame, frameAnalysis)
      this.changeHistory.push(changeData)
      
      // 保持变化历史在合理范围内
      if (this.changeHistory.length > this.config.frameWindowSize * 2) {
        this.changeHistory.shift()
      }
    }
    
    // 添加到帧缓冲区
    this.frameBuffer.push(frameAnalysis)
    
    if (this.frameBuffer.length > this.config.frameWindowSize) {
      this.frameBuffer.shift()
    }
    
    this.previousFrame = frameAnalysis
  }
  
  /**
   * 计算帧间变化
   */
  private calculateFrameChanges(
    prevFrame: EnhancedPostureAnalysis, 
    currentFrame: EnhancedPostureAnalysis
  ): FrameChangeData {
    const timeDelta = 33 // 假设30fps，约33ms per frame
    
    // 位置变化
    const headPosChange = {
      x: (currentFrame.headOffset?.x || 0) - (prevFrame.headOffset?.x || 0),
      y: (currentFrame.headOffset?.y || 0) - (prevFrame.headOffset?.y || 0),
      z: (currentFrame.headOffset?.z || 0) - (prevFrame.headOffset?.z || 0)
    }
    
    const shoulderChange = {
      left: Math.abs(currentFrame.shoulderBalance - prevFrame.shoulderBalance),
      right: Math.abs(currentFrame.shoulderBalance - prevFrame.shoulderBalance)
    }
    
    const bodyChange = {
      x: Math.abs(currentFrame.bodyLean - prevFrame.bodyLean),
      y: 0
    }
    
    // 速度计算（单位/毫秒）
    const headVelocity = Math.sqrt(
      headPosChange.x * headPosChange.x + 
      headPosChange.y * headPosChange.y + 
      headPosChange.z * headPosChange.z
    ) / timeDelta
    
    const shoulderVelocity = shoulderChange.left / timeDelta
    const bodyVelocity = bodyChange.x / timeDelta
    
    // 加速度计算
    const prevChangeData = this.changeHistory[this.changeHistory.length - 1]
    const headAcceleration = prevChangeData ? 
      (headVelocity - prevChangeData.velocities.headVelocity) / timeDelta : 0
    const shoulderAcceleration = prevChangeData ?
      (shoulderVelocity - prevChangeData.velocities.shoulderVelocity) / timeDelta : 0
    const bodyAcceleration = prevChangeData ?
      (bodyVelocity - prevChangeData.velocities.bodyVelocity) / timeDelta : 0
    
    return {
      frameIndex: this.frameBuffer.length,
      timestamp: Date.now(),
      positionChanges: {
        head: headPosChange,
        shoulders: shoulderChange,
        bodyCenter: bodyChange
      },
      velocities: {
        headVelocity,
        shoulderVelocity,
        bodyVelocity
      },
      accelerations: {
        headAcceleration,
        shoulderAcceleration,
        bodyAcceleration
      }
    }
  }
  
  /**
   * 计算姿态稳定性
   */
  private calculatePosturalStability(): {
    overall: number
    head: number
    shoulders: number
    body: number
  } {
    if (this.changeHistory.length < 5) {
      return { overall: 0, head: 0, shoulders: 0, body: 0 }
    }
    
    const recentChanges = this.changeHistory.slice(-this.config.stabilityWindow)
    
    // 头部稳定性
    const headChanges = recentChanges.map(change => 
      Math.sqrt(
        change.positionChanges.head.x ** 2 + 
        change.positionChanges.head.y ** 2 + 
        change.positionChanges.head.z ** 2
      )
    )
    const headStability = this.calculateStabilityScore(headChanges)
    
    // 肩膀稳定性
    const shoulderChanges = recentChanges.map(change => change.positionChanges.shoulders.left)
    const shoulderStability = this.calculateStabilityScore(shoulderChanges)
    
    // 身体稳定性
    const bodyChanges = recentChanges.map(change => change.positionChanges.bodyCenter.x)
    const bodyStability = this.calculateStabilityScore(bodyChanges)
    
    // 综合稳定性
    const overall = (headStability * 0.5 + shoulderStability * 0.3 + bodyStability * 0.2)
    
    return {
      overall,
      head: headStability,
      shoulders: shoulderStability,
      body: bodyStability
    }
  }
  
  /**
   * 计算稳定性分数
   */
  private calculateStabilityScore(changes: number[]): number {
    if (changes.length === 0) return 0
    
    const mean = changes.reduce((sum, change) => sum + change, 0) / changes.length
    const variance = changes.reduce((sum, change) => sum + (change - mean) ** 2, 0) / changes.length
    const stdDev = Math.sqrt(variance)
    
    // 标准差越小，稳定性越高
    return Math.max(0, 1 - stdDev / 0.1) // 归一化到0-1
  }
  
  /**
   * 计算运动连续性
   */
  private calculateMovementContinuity(): {
    overall: number
    smoothness: number
    predictability: number
  } {
    if (this.changeHistory.length < 10) {
      return { overall: 0, smoothness: 0, predictability: 0 }
    }
    
    const recentChanges = this.changeHistory.slice(-15)
    
    // 平滑度：检查加速度变化
    const accelerationChanges = recentChanges.map(change => change.accelerations.headAcceleration)
    const smoothness = this.calculateSmoothness(accelerationChanges)
    
    // 可预测性：检查速度模式
    const velocityChanges = recentChanges.map(change => change.velocities.headVelocity)
    const predictability = this.calculatePredictability(velocityChanges)
    
    const overall = (smoothness * 0.6 + predictability * 0.4)
    
    return {
      overall,
      smoothness,
      predictability
    }
  }
  
  /**
   * 计算平滑度
   */
  private calculateSmoothness(accelerations: number[]): number {
    if (accelerations.length < 3) return 0
    
    // 计算加速度的二阶差分
    const secondDerivatives = []
    for (let i = 2; i < accelerations.length; i++) {
      const secondDerivative = accelerations[i] - 2 * accelerations[i-1] + accelerations[i-2]
      secondDerivatives.push(Math.abs(secondDerivative))
    }
    
    const avgSecondDerivative = secondDerivatives.reduce((sum, val) => sum + val, 0) / secondDerivatives.length
    return Math.max(0, 1 - avgSecondDerivative / 0.005) // 归一化
  }
  
  /**
   * 计算可预测性（自相关）
   */
  private calculatePredictability(velocities: number[]): number {
    if (velocities.length < 5) return 0
    
    const lag = 1
    const n = velocities.length - lag
    const mean = velocities.reduce((sum, v) => sum + v, 0) / velocities.length
    
    let numerator = 0
    let denominator = 0
    
    for (let i = 0; i < n; i++) {
      numerator += (velocities[i] - mean) * (velocities[i + lag] - mean)
      denominator += (velocities[i] - mean) ** 2
    }
    
    const autocorrelation = denominator > 0 ? numerator / denominator : 0
    return Math.max(0, autocorrelation)
  }
  
  /**
   * 分析趋势
   */
  private analyzeTrends(): {
    focusScoreTrend: 'improving' | 'stable' | 'declining'
    postureQualityTrend: 'improving' | 'stable' | 'declining'
    movementTrend: 'increasing' | 'stable' | 'decreasing'
  } {
    if (!this.config.trendAnalysisEnabled || this.frameBuffer.length < 15) {
      return {
        focusScoreTrend: 'stable',
        postureQualityTrend: 'stable',
        movementTrend: 'stable'
      }
    }
    
    const recentFrames = this.frameBuffer.slice(-15)
    
    // 专注分数趋势
    const focusScores = recentFrames.map(frame => frame.focusScore)
    const focusScoreTrend = this.calculateTrend(focusScores)
    
    // 姿态质量趋势
    const stabilityScores = recentFrames.map(frame => frame.stability)
    const postureQualityTrend = this.calculateTrend(stabilityScores)
    
    // 运动趋势
    const movementScores = this.changeHistory.slice(-10).map(change => 
      change.velocities.headVelocity + change.velocities.shoulderVelocity + change.velocities.bodyVelocity
    )
    const movementTrend = this.calculateMovementTrend(movementScores)
    
    return {
      focusScoreTrend,
      postureQualityTrend,
      movementTrend
    }
  }
  
  /**
   * 计算趋势
   */
  private calculateTrend(values: number[]): 'improving' | 'stable' | 'declining' {
    if (values.length < 5) return 'stable'
    
    const firstHalf = values.slice(0, Math.floor(values.length / 2))
    const secondHalf = values.slice(Math.floor(values.length / 2))
    
    const firstAvg = firstHalf.reduce((sum, v) => sum + v, 0) / firstHalf.length
    const secondAvg = secondHalf.reduce((sum, v) => sum + v, 0) / secondHalf.length
    
    const diff = secondAvg - firstAvg
    
    if (diff > this.config.trendSensitivity) return 'improving'
    if (diff < -this.config.trendSensitivity) return 'declining'
    return 'stable'
  }
  
  /**
   * 计算运动趋势
   */
  private calculateMovementTrend(values: number[]): 'increasing' | 'stable' | 'decreasing' {
    if (values.length < 5) return 'stable'
    
    const firstHalf = values.slice(0, Math.floor(values.length / 2))
    const secondHalf = values.slice(Math.floor(values.length / 2))
    
    const firstAvg = firstHalf.reduce((sum, v) => sum + v, 0) / firstHalf.length
    const secondAvg = secondHalf.reduce((sum, v) => sum + v, 0) / secondHalf.length
    
    const diff = secondAvg - firstAvg
    
    if (diff > this.config.trendSensitivity * 0.5) return 'increasing'
    if (diff < -this.config.trendSensitivity * 0.5) return 'decreasing'
    return 'stable'
  }
  
  /**
   * 计算多帧专注分数
   */
  private calculateMultiFrameFocusScore(): { score: number; confidence: number } {
    if (this.frameBuffer.length === 0) {
      return { score: 0, confidence: 0 }
    }
    
    const recentFrames = this.frameBuffer.slice(-this.config.frameWindowSize)
    
    // 基础分数：加权平均（近期帧权重更高）
    let weightedSum = 0
    let totalWeight = 0
    
    recentFrames.forEach((frame, index) => {
      const weight = Math.exp(index / recentFrames.length) // 指数权重
      weightedSum += frame.focusScore * weight
      totalWeight += weight
    })
    
    const baseScore = totalWeight > 0 ? weightedSum / totalWeight : 0
    
    // 稳定性调整
    const stability = this.calculatePosturalStability().overall
    const stabilityBonus = stability * 5 // 最多5分奖励
    
    // 连续性调整
    const continuity = this.calculateMovementContinuity().overall
    const continuityBonus = continuity * 3 // 最多3分奖励
    
    // 趋势调整
    const trends = this.analyzeTrends()
    let trendAdjustment = 0
    if (trends.focusScoreTrend === 'improving') trendAdjustment = 2
    else if (trends.focusScoreTrend === 'declining') trendAdjustment = -2
    
    // 最终分数
    const finalScore = Math.max(0, Math.min(100, 
      baseScore + stabilityBonus + continuityBonus + trendAdjustment
    ))
    
    // 置信度计算
    const avgConfidence = recentFrames.reduce((sum, frame) => sum + frame.confidence, 0) / recentFrames.length
    const dataQuality = recentFrames.length / this.config.frameWindowSize
    const confidence = avgConfidence * dataQuality * stability
    
    return { score: finalScore, confidence }
  }
  
  /**
   * 检测异常
   */
  private detectAnomalies(): {
    frameDrops: number[]
    suddenMovements: number[]
    inconsistencies: number[]
  } {
    const frameDrops: number[] = []
    const suddenMovements: number[] = []
    const inconsistencies: number[] = []
    
    if (this.config.inconsistencyDetection && this.changeHistory.length > 5) {
      // 检测突然的大幅运动
      const recentChanges = this.changeHistory.slice(-10)
      const avgVelocity = recentChanges.reduce((sum, change) => 
        sum + change.velocities.headVelocity, 0) / recentChanges.length
      
      recentChanges.forEach((change, index) => {
        if (change.velocities.headVelocity > avgVelocity * 3) {
          suddenMovements.push(change.frameIndex)
        }
        
        // 检测不一致性（加速度突变）
        if (Math.abs(change.accelerations.headAcceleration) > 0.01) {
          inconsistencies.push(change.frameIndex)
        }
      })
    }
    
    return { frameDrops, suddenMovements, inconsistencies }
  }

  public analyzeMultiFrame(): MultiFrameAnalysisResult | null {
    if (this.frameBuffer.length < this.config.minFramesForAnalysis) {
      return null
    }
    
    const frameCount = this.frameBuffer.length
    const timeSpan = frameCount * 33 // 假设30fps
    const averageFPS = 30
    
    // 计算各项指标
    const posturalStability = this.calculatePosturalStability()
    const movementContinuity = this.calculateMovementContinuity()
    const trends = this.analyzeTrends()
    const multiFrameResult = this.calculateMultiFrameFocusScore()
    const anomalies = this.detectAnomalies()
    
    // 生成预测
    const predictions = this.config.predictionEnabled ? this.generatePredictions() : undefined
    
    // 生成建议
    const recommendations = this.generateMultiFrameRecommendations({
      posturalStability,
      movementContinuity,
      trends,
      anomalies
    })
    
    return {
      frameCount,
      timeSpan,
      averageFPS,
      posturalStability,
      movementContinuity,
      trends,
      multiFrameFocusScore: multiFrameResult.score,
      confidenceLevel: multiFrameResult.confidence,
      anomalies,
      predictions,
      recommendations
    }
  }
  
  /**
   * 生成预测
   */
  private generatePredictions(): {
    nextFrameFocusScore: number
    stabilityForecast: number
    recommendedActions: string[]
  } {
    if (this.frameBuffer.length < 10) {
      return {
        nextFrameFocusScore: 0,
        stabilityForecast: 0,
        recommendedActions: []
      }
    }
    
    const recentFrames = this.frameBuffer.slice(-10)
    const recentScores = recentFrames.map(frame => frame.focusScore)
    
    // 简单的线性预测
    const trend = this.calculateTrend(recentScores)
    const currentScore = recentScores[recentScores.length - 1]
    const avgChange = this.calculateAverageChange(recentScores)
    
    let nextFrameFocusScore = currentScore
    if (trend === 'improving') {
      nextFrameFocusScore = Math.min(100, currentScore + Math.abs(avgChange))
    } else if (trend === 'declining') {
      nextFrameFocusScore = Math.max(0, currentScore - Math.abs(avgChange))
    }
    
    // 稳定性预测
    const currentStability = this.calculatePosturalStability().overall
    const stabilityTrend = this.analyzeTrends().postureQualityTrend
    let stabilityForecast = currentStability
    
    if (stabilityTrend === 'improving') {
      stabilityForecast = Math.min(1, currentStability + 0.1)
    } else if (stabilityTrend === 'declining') {
      stabilityForecast = Math.max(0, currentStability - 0.1)
    }
    
    // 推荐行动
    const recommendedActions = this.generatePredictiveRecommendations(
      nextFrameFocusScore, 
      stabilityForecast, 
      trend
    )
    
    return {
      nextFrameFocusScore,
      stabilityForecast,
      recommendedActions
    }
  }
  
  /**
   * 计算平均变化
   */
  private calculateAverageChange(values: number[]): number {
    if (values.length < 2) return 0
    
    let totalChange = 0
    for (let i = 1; i < values.length; i++) {
      totalChange += values[i] - values[i-1]
    }
    
    return totalChange / (values.length - 1)
  }
  
  /**
   * 生成预测性建议
   */
  private generatePredictiveRecommendations(
    predictedScore: number, 
    predictedStability: number, 
    trend: 'improving' | 'stable' | 'declining'
  ): string[] {
    const recommendations: string[] = []
    
    if (predictedScore < 60) {
      recommendations.push('预测显示专注度可能下降，建议主动调整姿态')
    }
    
    if (predictedStability < 0.6) {
      recommendations.push('预测显示姿态稳定性可能降低，建议减少不必要的移动')
    }
    
    if (trend === 'declining') {
      recommendations.push('检测到下降趋势，建议短暂休息或调整环境')
    } else if (trend === 'improving') {
      recommendations.push('专注度呈上升趋势，继续保持当前状态')
    }
    
    return recommendations
  }
  
  /**
   * 生成多帧建议
   */
  private generateMultiFrameRecommendations(context: {
    posturalStability: any
    movementContinuity: any
    trends: any
    anomalies: any
  }): string[] {
    const recommendations: string[] = []
    
    // 基于稳定性的建议
    if (context.posturalStability.overall < 0.7) {
      recommendations.push('姿态稳定性较低，建议减少频繁的位置调整')
      
      if (context.posturalStability.head < 0.6) {
        recommendations.push('头部移动较频繁，尝试保持头部相对固定')
      }
      
      if (context.posturalStability.shoulders < 0.6) {
        recommendations.push('肩膀位置不够稳定，注意保持肩膀平衡')
      }
    }
    
    // 基于连续性的建议
    if (context.movementContinuity.overall < 0.6) {
      recommendations.push('动作连续性有待改善，避免突然的大幅度移动')
      
      if (context.movementContinuity.smoothness < 0.5) {
        recommendations.push('动作平滑度不足，尝试更缓慢、平稳的调整')
      }
    }
    
    // 基于趋势的建议
    if (context.trends.focusScoreTrend === 'declining') {
      recommendations.push('专注度呈下降趋势，建议检查环境因素或考虑短暂休息')
    }
    
    if (context.trends.movementTrend === 'increasing') {
      recommendations.push('检测到运动增加，如果感到疲劳建议适当休息')
    }
    
    // 基于异常的建议
    if (context.anomalies.suddenMovements.length > 2) {
      recommendations.push('检测到多次突然移动，建议保持更稳定的坐姿')
    }
    
    return recommendations
  }
  
  public reset(): void {
    this.frameBuffer = []
    this.changeHistory = []
    this.previousFrame = null
  }
  
  /**
   * 获取详细统计信息
   */
  public getDetailedStats(): any {
    if (this.frameBuffer.length === 0) return null
    
    const stability = this.calculatePosturalStability()
    const continuity = this.calculateMovementContinuity()
    const trends = this.analyzeTrends()
    const anomalies = this.detectAnomalies()
    
    return {
      frameStatistics: {
        totalFrames: this.frameBuffer.length,
        averageFocusScore: this.frameBuffer.reduce((sum, frame) => sum + frame.focusScore, 0) / this.frameBuffer.length,
        averageConfidence: this.frameBuffer.reduce((sum, frame) => sum + frame.confidence, 0) / this.frameBuffer.length
      },
      stability,
      continuity,
      trends,
      anomalies
    }
  }
}

export function createMultiFrameAnalyzer(config?: Partial<MultiFrameConfig>): AdvancedMultiFrameAnalyzer {
  return new AdvancedMultiFrameAnalyzer(config)
} 