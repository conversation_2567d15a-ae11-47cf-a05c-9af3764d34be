import { 
  CropInstance, 
  CropConfig, 
  CropStage, 
  CropQuality,
  CropGrowthEvent,
  CROP_CONFIGS,
  calculateGrowthProgress,
  calculateQuality 
} from '../types/crop'

// 时间计算配置
export const GROWTH_CONFIG = {
  // 最小更新间隔（毫秒）
  MIN_UPDATE_INTERVAL: 1000,
  
  // 专注度对生长速度的影响范围
  FOCUS_SPEED_RANGE: {
    MIN_MULTIPLIER: 0.1, // 专注度0时的生长倍数
    MAX_MULTIPLIER: 2.5, // 专注度100时的生长倍数
  },
  
  // 品质计算相关
  QUALITY_THRESHOLDS: {
    LEGENDARY: 95,
    EPIC: 90,
    RARE: 80,
    UNCOMMON: 70,
    COMMON: 0
  },
  
  // 暂停惩罚
  PAUSE_PENALTY: {
    GRACE_PERIOD: 30 * 1000, // 30秒宽限期
    PENALTY_RATE: 0.1, // 每分钟损失10%进度
  }
} as const

// 专注度历史记录
export interface FocusHistory {
  timestamp: number
  score: number
  duration: number
}

// 生长计算器类
export class GrowthCalculator {
  private focusHistory: Map<string, FocusHistory[]> = new Map()
  private lastUpdateTime: number = Date.now()
  
  /**
   * 计算作物生长进度
   */
  calculateProgress(
    crop: CropInstance,
    currentFocusScore: number,
    deltaTime: number
  ): {
    progressDelta: number
    shouldAdvanceStage: boolean
    qualityBonus: number
    newStage?: CropStage
  } {
    const config = CROP_CONFIGS[crop.type]
    if (!config) {
      throw new Error(`Unknown crop type: ${crop.type}`)
    }
    
    // 如果作物暂停或已收获，不计算进度
    if (crop.isPaused || crop.stage === CropStage.HARVESTED) {
      return { progressDelta: 0, shouldAdvanceStage: false, qualityBonus: 0 }
    }
    
    // 获取当前阶段配置
    const currentStageConfig = config.stages.find(s => s.stage === crop.stage)
    if (!currentStageConfig) {
      return { progressDelta: 0, shouldAdvanceStage: false, qualityBonus: 0 }
    }
    
    // 计算专注度对生长速度的影响
    const focusMultiplier = this.calculateFocusMultiplier(
      currentFocusScore,
      currentStageConfig.minFocusScore,
      config.focusMultiplier
    )
    
    // 计算基础进度增量
    const baseProgress = deltaTime / currentStageConfig.duration
    const adjustedProgress = baseProgress * focusMultiplier
    
    // 更新专注度历史
    this.updateFocusHistory(crop.id, currentFocusScore, deltaTime)
    
    // 计算品质奖励
    const averageFocus = this.getAverageFocusScore(crop.id)
    const qualityBonus = this.calculateQualityBonus(averageFocus)
    
    // 检查是否应该进入下一阶段
    const currentStageElapsed = Date.now() - crop.stageStartTime
    const shouldAdvanceStage = currentStageElapsed >= currentStageConfig.duration
    
    let newStage: CropStage | undefined
    if (shouldAdvanceStage) {
      newStage = this.getNextStage(crop.stage, config)
    }
    
    return {
      progressDelta: adjustedProgress,
      shouldAdvanceStage,
      qualityBonus,
      newStage
    }
  }
  
  /**
   * 计算专注度对生长速度的影响倍数
   */
  private calculateFocusMultiplier(
    currentScore: number,
    minRequiredScore: number,
    cropFocusMultiplier: number
  ): number {
    // 如果专注度低于最低要求，大幅降低生长速度
    if (currentScore < minRequiredScore) {
      const penalty = (minRequiredScore - currentScore) / minRequiredScore
      return Math.max(0.1, 1 - penalty * 0.8)
    }
    
    // 专注度满足要求时，根据分数计算加成
    const { MIN_MULTIPLIER, MAX_MULTIPLIER } = GROWTH_CONFIG.FOCUS_SPEED_RANGE
    const normalizedScore = Math.min(currentScore / 100, 1)
    const baseMultiplier = MIN_MULTIPLIER + (MAX_MULTIPLIER - MIN_MULTIPLIER) * normalizedScore
    
    return baseMultiplier * cropFocusMultiplier
  }
  
  /**
   * 更新专注度历史记录
   */
  private updateFocusHistory(cropId: string, score: number, duration: number): void {
    if (!this.focusHistory.has(cropId)) {
      this.focusHistory.set(cropId, [])
    }
    
    const history = this.focusHistory.get(cropId)!
    const now = Date.now()
    
    // 添加新记录
    history.push({
      timestamp: now,
      score,
      duration
    })
    
    // 清理过旧的记录（保留最近1小时）
    const oneHourAgo = now - 60 * 60 * 1000
    const validHistory = history.filter(h => h.timestamp > oneHourAgo)
    this.focusHistory.set(cropId, validHistory)
  }
  
  /**
   * 获取平均专注度分数
   */
  getAverageFocusScore(cropId: string): number {
    const history = this.focusHistory.get(cropId) || []
    if (history.length === 0) return 50 // 默认值
    
    // 按持续时间加权计算平均值
    let totalScore = 0
    let totalDuration = 0
    
    for (const record of history) {
      totalScore += record.score * record.duration
      totalDuration += record.duration
    }
    
    return totalDuration > 0 ? totalScore / totalDuration : 50
  }
  
  /**
   * 计算品质奖励
   */
  private calculateQualityBonus(averageFocusScore: number): number {
    const { QUALITY_THRESHOLDS } = GROWTH_CONFIG
    
    if (averageFocusScore >= QUALITY_THRESHOLDS.LEGENDARY) return 0.5
    if (averageFocusScore >= QUALITY_THRESHOLDS.EPIC) return 0.3
    if (averageFocusScore >= QUALITY_THRESHOLDS.RARE) return 0.2
    if (averageFocusScore >= QUALITY_THRESHOLDS.UNCOMMON) return 0.1
    return 0
  }
  
  /**
   * 获取下一个生长阶段
   */
  private getNextStage(currentStage: CropStage, config: CropConfig): CropStage | undefined {
    const stages = config.stages.map(s => s.stage)
    const currentIndex = stages.indexOf(currentStage)
    
    if (currentIndex === -1 || currentIndex >= stages.length - 1) {
      return undefined
    }
    
    return stages[currentIndex + 1]
  }
  
  /**
   * 计算估计收获时间
   */
  estimateTimeToHarvest(
    crop: CropInstance,
    currentFocusScore: number = 75
  ): number {
    const config = CROP_CONFIGS[crop.type]
    if (!config) return 0
    
    const currentStageIndex = config.stages.findIndex(s => s.stage === crop.stage)
    if (currentStageIndex === -1) return 0
    
    let totalTimeRemaining = 0
    
    // 计算当前阶段剩余时间
    const currentStage = config.stages[currentStageIndex]
    const currentElapsed = Date.now() - crop.stageStartTime
    const currentRemaining = Math.max(0, currentStage.duration - currentElapsed)
    
    const focusMultiplier = this.calculateFocusMultiplier(
      currentFocusScore,
      currentStage.minFocusScore,
      config.focusMultiplier
    )
    
    totalTimeRemaining += currentRemaining / focusMultiplier
    
    // 计算后续阶段时间
    for (let i = currentStageIndex + 1; i < config.stages.length - 1; i++) {
      const stage = config.stages[i]
      if (stage.stage === CropStage.READY_TO_HARVEST) break
      
      const stageMultiplier = this.calculateFocusMultiplier(
        currentFocusScore,
        stage.minFocusScore,
        config.focusMultiplier
      )
      
      totalTimeRemaining += stage.duration / stageMultiplier
    }
    
    return totalTimeRemaining
  }
  
  /**
   * 计算暂停惩罚
   */
  calculatePausePenalty(crop: CropInstance, pauseDuration: number): number {
    const { GRACE_PERIOD, PENALTY_RATE } = GROWTH_CONFIG.PAUSE_PENALTY
    
    if (pauseDuration <= GRACE_PERIOD) {
      return 0 // 宽限期内无惩罚
    }
    
    const penaltyDuration = pauseDuration - GRACE_PERIOD
    const penaltyMinutes = penaltyDuration / (60 * 1000)
    
    return Math.min(0.5, penaltyMinutes * PENALTY_RATE) // 最多损失50%进度
  }
  
  /**
   * 应用生长加速
   */
  applyGrowthBoost(
    crop: CropInstance,
    multiplier: number,
    duration: number
  ): CropInstance {
    const boostedCrop = { ...crop }
    
    // 记录加速信息
    boostedCrop.metadata = {
      ...boostedCrop.metadata,
      growthBoosts: boostedCrop.metadata.growthBoosts + 1
    }
    
    // 可以在这里添加临时状态或标记
    // 实际的加速效果会在 calculateProgress 中体现
    
    return boostedCrop
  }
  
  /**
   * 重置专注度历史
   */
  clearFocusHistory(cropId?: string): void {
    if (cropId) {
      this.focusHistory.delete(cropId)
    } else {
      this.focusHistory.clear()
    }
  }
  
  /**
   * 获取生长统计信息
   */
  getGrowthStats(crop: CropInstance): {
    averageFocusScore: number
    totalFocusTime: number
    estimatedQuality: CropQuality
    growthEfficiency: number
  } {
    const averageFocusScore = this.getAverageFocusScore(crop.id)
    const history = this.focusHistory.get(crop.id) || []
    const totalFocusTime = history.reduce((sum, h) => sum + h.duration, 0)
    const estimatedQuality = calculateQuality(crop, averageFocusScore)
    
    // 计算生长效率（实际时间vs理论最优时间）
    const config = CROP_CONFIGS[crop.type]
    const actualTime = Date.now() - crop.plantedAt
    const theoreticalOptimalTime = config.baseGrowthTime * 0.7 // 假设最优条件下能节省30%时间
    const growthEfficiency = Math.min(1, theoreticalOptimalTime / actualTime)
    
    return {
      averageFocusScore,
      totalFocusTime,
      estimatedQuality,
      growthEfficiency
    }
  }
}

// 全局生长计算器实例
export const growthCalculator = new GrowthCalculator()

// 时间格式化工具函数
export const formatTimeRemaining = (milliseconds: number): string => {
  if (milliseconds <= 0) return '已完成'
  
  const seconds = Math.floor(milliseconds / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)
  
  if (hours > 0) {
    return `${hours}小时${minutes % 60}分钟`
  } else if (minutes > 0) {
    return `${minutes}分钟${seconds % 60}秒`
  } else {
    return `${seconds}秒`
  }
}

// 生长速度描述
export const getGrowthSpeedDescription = (multiplier: number): string => {
  if (multiplier >= 2.0) return '飞速生长'
  if (multiplier >= 1.5) return '快速生长'
  if (multiplier >= 1.0) return '正常生长'
  if (multiplier >= 0.5) return '缓慢生长'
  return '几乎停滞'
}

// 专注度要求描述
export const getFocusRequirementDescription = (
  currentScore: number,
  requiredScore: number
): string => {
  if (currentScore >= requiredScore) {
    return '专注度充足'
  } else {
    const deficit = requiredScore - currentScore
    if (deficit > 30) return '需要大幅提高专注度'
    if (deficit > 15) return '需要提高专注度'
    return '稍微提高专注度即可'
  }
} 