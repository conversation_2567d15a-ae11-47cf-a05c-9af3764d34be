import { LootboxType, LootboxResult, ItemRarity } from '../types/lootbox'
import { CurrencyType } from '../types/currency'
import { LOOTBOX_CONFIGS } from '../data/lootboxConfigs'
import { ItemFactory } from './ItemFactory'
import { GameItem, Quality, AgriculturalVariety, IndustrialVariety, EquipmentType } from '../types/enhanced-items'

// 品质映射表 - 将盲盒系统的品质映射到新道具系统的品质
const RARITY_TO_QUALITY_MAP: Record<ItemRarity, Quality> = {
  [ItemRarity.GRAY]: Quality.COMMON,
  [ItemRarity.GREEN]: Quality.GOOD,
  [ItemRarity.BLUE]: Quality.RARE,
  [ItemRarity.ORANGE]: Quality.EPIC,
  [ItemRarity.GOLD]: Quality.LEGENDARY,
  [ItemRarity.GOLD_RED]: Quality.LEGENDARY
}

// 盲盒生成器类
export class LootboxGenerator {
  // 根据概率生成品质
  private static generateRarity(lootboxType: LootboxType): ItemRarity {
    const config = LOOTBOX_CONFIGS[lootboxType]
    const random = Math.random()
    let cumulativeRate = 0
    
    // 按品质顺序检查概率
    for (const [rarity, rate] of Object.entries(config.dropRates)) {
      cumulativeRate += rate
      if (random <= cumulativeRate) {
        return rarity as ItemRarity
      }
    }
    
    // 默认返回最低品质
    return ItemRarity.GRAY
  }

  // 从物品池中选择随机物品
  private static selectRandomItem(itemPool: (AgriculturalVariety | IndustrialVariety | EquipmentType)[]): AgriculturalVariety | IndustrialVariety | EquipmentType | null {
    if (itemPool.length === 0) return null
    
    const randomIndex = Math.floor(Math.random() * itemPool.length)
    return itemPool[randomIndex]
  }
  
  // 生成单个物品
  private static generateSingleItem(lootboxType: LootboxType, guaranteedRarity?: ItemRarity, isBonus = false): {
    item: GameItem
    quantity: number
    isGuaranteed: boolean
    isBonus: boolean
  } | null {
    const config = LOOTBOX_CONFIGS[lootboxType]
    
    // 确定品质
    const rarity = guaranteedRarity || this.generateRarity(lootboxType)
    const quality = RARITY_TO_QUALITY_MAP[rarity]
    
    // 选择物品品种
    const variety = this.selectRandomItem(config.itemPool)
    if (!variety) return null
    
    // 使用ItemFactory创建物品
    const item = ItemFactory.createItem(variety, quality)
    
    // 计算数量（可堆叠物品可能有多个）
    let quantity = 1
    if (item.stackable) {
      switch (rarity) {
        case ItemRarity.GRAY:
          quantity = Math.floor(Math.random() * 3) + 1  // 1-3
          break
        case ItemRarity.GREEN:
          quantity = Math.floor(Math.random() * 2) + 1  // 1-2
          break
        default:
          quantity = 1
      }
    }
    
    return {
      item,
      quantity,
      isGuaranteed: !!guaranteedRarity,
      isBonus
    }
  }
  
  // 生成盲盒结果
  public static generateLootbox(lootboxType: LootboxType, userCurrency?: Record<CurrencyType, number>): LootboxResult {
    const config = LOOTBOX_CONFIGS[lootboxType]
    const openedAt = Date.now()
    
    // 检查用户是否有足够的货币
    if (userCurrency) {
      const requiredAmount = config.price.amount
      const userAmount = userCurrency[config.price.currency] || 0
      if (userAmount < requiredAmount) {
        throw new Error(`货币不足！需要 ${requiredAmount} ${config.price.currency}，但只有 ${userAmount}`)
      }
    }
    
    const items: {
      item: GameItem
      quantity: number
      isGuaranteed: boolean
      isBonus: boolean
    }[] = []
    
    const rarityBreakdown: Record<ItemRarity, number> = {
      [ItemRarity.GRAY]: 0,
      [ItemRarity.GREEN]: 0,
      [ItemRarity.BLUE]: 0,
      [ItemRarity.ORANGE]: 0,
      [ItemRarity.GOLD]: 0,
      [ItemRarity.GOLD_RED]: 0
    }
    
    // 生成保底物品
    const guaranteedCount = config.specialFeatures?.guaranteedCount || 1
    for (let i = 0; i < guaranteedCount; i++) {
      let guaranteedRarity: ItemRarity | undefined
      
      // 第一个物品使用保底品质
      if (i === 0 && config.guaranteedRarity) {
        guaranteedRarity = config.guaranteedRarity
      }
      
      const generatedItem = this.generateSingleItem(lootboxType, guaranteedRarity)
      if (generatedItem) {
        items.push(generatedItem)
        // 根据物品品质更新统计
        const itemRarity = this.qualityToRarity(generatedItem.item.quality)
        rarityBreakdown[itemRarity] += generatedItem.quantity
      }
    }
    
    // 生成额外奖励物品
    if (config.specialFeatures?.bonusChance) {
      const bonusRoll = Math.random()
      if (bonusRoll <= config.specialFeatures.bonusChance) {
        const bonusItem = this.generateSingleItem(lootboxType, undefined, true)
        if (bonusItem) {
          items.push(bonusItem)
          const itemRarity = this.qualityToRarity(bonusItem.item.quality)
          rarityBreakdown[itemRarity] += bonusItem.quantity
        }
      }
    }
    
    // 计算总价值
    const totalValue = items.reduce((sum, item) => {
      return sum + (item.item.baseValue * item.quantity)
    }, 0)
    
    // 构建结果对象，转换为期望的格式
    return {
      lootboxType,
      items: items.map(item => ({
        item: this.convertGameItemToLootboxItem(item.item),
        quantity: item.quantity,
        isGuaranteed: item.isGuaranteed,
        isBonus: item.isBonus
      })),
      totalValue,
      rarityBreakdown,
      openedAt,
      cost: config.price
    }
  }

  // 将游戏道具转换为盲盒道具格式（为了兼容性）
  private static convertGameItemToLootboxItem(gameItem: GameItem): any {
    return {
      id: gameItem.id,
      name: gameItem.name,
      description: gameItem.description,
      category: gameItem.category,
      rarity: this.qualityToRarity(gameItem.quality),
      icon: gameItem.icon,
      value: gameItem.baseValue,
      stackable: gameItem.stackable,
      tradeable: gameItem.tradeable,
      metadata: this.extractMetadata(gameItem)
    }
  }

  // 品质转品稀有度
  private static qualityToRarity(quality: Quality): ItemRarity {
    const reverseMap: Record<Quality, ItemRarity> = {
      [Quality.COMMON]: ItemRarity.GRAY,
      [Quality.GOOD]: ItemRarity.GREEN,
      [Quality.RARE]: ItemRarity.BLUE,
      [Quality.EPIC]: ItemRarity.ORANGE,
      [Quality.LEGENDARY]: ItemRarity.GOLD
    }
    return reverseMap[quality] || ItemRarity.GRAY
  }

  // 提取道具元数据
  private static extractMetadata(gameItem: GameItem): any {
    const metadata: any = {}
    
    if (gameItem.category === 'agricultural') {
      const agriItem = gameItem as any
      metadata.yieldMultiplier = agriItem.production?.currentRate || 1.0
      metadata.futuresPrice = agriItem.baseValue
      metadata.futuresCode = agriItem.futuresCode
    } else if (gameItem.category === 'industrial') {
      const indItem = gameItem as any
      metadata.efficiency = indItem.properties?.efficiency || 100
      metadata.capacity = indItem.properties?.capacity || 100
      metadata.futuresPrice = indItem.baseValue
      metadata.futuresCode = indItem.futuresCode
    } else if (gameItem.category === 'equipment') {
      const equipItem = gameItem as any
      metadata.focusBonus = equipItem.attributes?.focusBonus || 0
      metadata.productionBonus = equipItem.attributes?.productionBonus || 0
      metadata.duration = equipItem.attributes?.duration || 24
    }
    
    return metadata
  }
  
  // 生成多个盲盒结果
  public static generateMultipleLootboxes(
    lootboxType: LootboxType, 
    count: number, 
    userCurrency: Record<CurrencyType, number>
  ): LootboxResult[] {
    const config = LOOTBOX_CONFIGS[lootboxType]
    const results: LootboxResult[] = []
    
    // 检查用户是否有足够的货币开启所有盲盒
    const totalCost = config.price.amount * count
    const userAmount = userCurrency[config.price.currency] || 0
    
    if (userAmount < totalCost) {
      throw new Error(`货币不足！需要 ${totalCost} ${config.price.currency}，但只有 ${userAmount}`)
    }
    
    // 模拟用户货币，每次开盒后减少
    const simulatedCurrency = { ...userCurrency }
    
    // 生成指定数量的盲盒结果
    for (let i = 0; i < count; i++) {
      try {
        const result = this.generateLootbox(lootboxType, simulatedCurrency)
        results.push(result)
        
        // 从模拟货币中扣除本次开盒费用
        simulatedCurrency[config.price.currency] -= config.price.amount
      } catch (error) {
        // 如果某次开盒失败，记录错误但继续尝试
        console.warn(`第 ${i + 1} 次开盒失败:`, error)
        break
      }
    }
    
    return results
  }

  // 获取盲盒预览信息
  public static getLootboxPreview(lootboxType: LootboxType): {
    config: typeof LOOTBOX_CONFIGS[LootboxType]
    possibleItems: string[]
    expectedValue: number
  } {
    const config = LOOTBOX_CONFIGS[lootboxType]
    const possibleItems = config.itemPool.map(variety => {
      // 为每个品种创建一个示例道具来获取名称
      const sampleItem = ItemFactory.createItem(variety, Quality.COMMON)
      return sampleItem.name
    })
    
    // 计算期望价值（简化计算）
    const expectedValue = Object.entries(config.dropRates).reduce((sum, [rarity, rate]) => {
      const quality = RARITY_TO_QUALITY_MAP[rarity as ItemRarity]
      // 估算该品质的平均价值
      const avgValue = config.itemPool.reduce((itemSum, variety) => {
        const sampleItem = ItemFactory.createItem(variety, quality)
        return itemSum + sampleItem.baseValue
      }, 0) / config.itemPool.length
      
      return sum + (avgValue * rate)
    }, 0)
    
    return {
      config,
      possibleItems,
      expectedValue: Math.round(expectedValue)
    }
  }
}

// 品质显示文本
export const RARITY_NAMES: Record<ItemRarity, string> = {
  [ItemRarity.GRAY]: '普通',
  [ItemRarity.GREEN]: '优秀', 
  [ItemRarity.BLUE]: '稀有',
  [ItemRarity.ORANGE]: '史诗',
  [ItemRarity.GOLD]: '传说',
  [ItemRarity.GOLD_RED]: '神话'
}

// 货币显示文本
export const CURRENCY_NAMES: Record<CurrencyType, string> = {
  [CurrencyType.FOCUS_COIN]: '专注币',
  [CurrencyType.DISCIPLINE_TOKEN]: '自律代币',
  [CurrencyType.FUTURES_CRYSTAL]: '期货水晶',
  [CurrencyType.GOLDEN_HARVEST]: '金色收获'
} 