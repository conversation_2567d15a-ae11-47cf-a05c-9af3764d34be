import { 
  PoseLandmark, 
  PostureAnalysis, 
  PoseLandmarkIndex,
  PostureDataPoint,
  PersonalBaseline,
  EnhancedPostureAnalysis
} from '../types/pose'
import { AdvancedTimeSeriesAnalyzer, TimeSeriesConfig } from './timeSeriesAnalysis'
import { 
  AdvancedEnvironmentalAnalyzer, 
  EnvironmentalConditions,
  EnvironmentalAdaptationConfig 
} from './environmentalAdaptation'
import { 
  AdvancedAdaptiveLearningAnalyzer,
  createAdaptiveLearningAnalyzer,
  UserFeedback,
  UserBehaviorConfig
} from './adaptiveLearning'
import {
  AdvancedMultiFrameAnalyzer,
  createMultiFrameAnalyzer,
  MultiFrameConfig,
  MultiFrameAnalysisResult
} from './multiFrameAnalysis'

export interface EnhancedPoseConfig {
  minVisibility: number
  headPositionSensitivity: number
  shoulderBalanceSensitivity: number
  bodyLeanSensitivity: number
  faceDirectionThreshold: number
  stabilityWeight: number
  temporalSmoothing: boolean
  environmentalFactorsEnabled: boolean
  personalizedLearningEnabled: boolean
  adaptationSpeed: number
  baselineCalibrationPeriod: number
  focusThreshold: number
  historySize: number
  smoothingFactor: number
  stabilityThreshold: number
  lightAdaptationEnabled: boolean
  angleAdaptationEnabled: boolean
}

/**
 * 增强版姿态分析器类
 */
export class EnhancedPoseAnalyzer {
  private config: EnhancedPoseConfig
  private historyData: PostureDataPoint[] = []
  private personalBaseline: PersonalBaseline | null = null
  private environmentalData: any = {}
  
  // 时间序列分析器
  private timeSeriesAnalyzer: AdvancedTimeSeriesAnalyzer
  
  // 环境适应分析器
  private environmentalAnalyzer: AdvancedEnvironmentalAnalyzer
  
  // 自适应学习分析器
  private adaptiveLearning: AdvancedAdaptiveLearningAnalyzer
  
  // 多帧分析器
  private multiFrameAnalyzer: AdvancedMultiFrameAnalyzer
  
  // 前一次分析结果
  private previousAnalysis: EnhancedPostureAnalysis | null = null
  
  constructor(
    config: Partial<EnhancedPoseConfig> = {},
    adaptiveLearningConfig: Partial<UserBehaviorConfig> = {},
    multiFrameConfig: Partial<MultiFrameConfig> = {}
  ) {
    this.config = {
      minVisibility: 0.6,
      focusThreshold: 70,
      historySize: 30,
      smoothingFactor: 0.3,
      stabilityThreshold: 0.1,
      lightAdaptationEnabled: true,
      angleAdaptationEnabled: true,
      personalizedLearningEnabled: true,
      adaptationSpeed: 0.05,
      baselineCalibrationPeriod: 300,
      ...config
    }

    // 初始化时间序列分析器
    const timeSeriesConfig: Partial<TimeSeriesConfig> = {
      kalmanProcessNoise: 0.1,
      kalmanMeasurementNoise: 0.5,
      smoothingWindow: 8,
      adaptiveSmoothingEnabled: true,
      outlierDetectionEnabled: true,
      outlierThreshold: 2.0,
      trendAnalysisWindow: 15,
      seasonalityPeriod: 30
    }
    
    this.timeSeriesAnalyzer = new AdvancedTimeSeriesAnalyzer(timeSeriesConfig)

    // 初始化环境适应分析器
    const environmentalConfig: Partial<EnvironmentalAdaptationConfig> = {
      lightingAdaptation: {
        enabled: this.config.lightAdaptationEnabled,
        minLightLevel: 0.4,
        adaptationSpeed: 0.1,
        shadowDetectionEnabled: true
      },
      cameraAdaptation: {
        enabled: this.config.angleAdaptationEnabled,
        angleToleranceRange: 20,
        distanceOptimalRange: [0.6, 1.8],
        stabilityThreshold: 0.05
      },
      backgroundAdaptation: {
        enabled: true,
        complexityThreshold: 0.6,
        contrastThreshold: 0.5,
        motionDetectionEnabled: false
      },
      autoCalibration: {
        enabled: true,
        calibrationPeriod: 30000,
        minSamplesForCalibration: 15
      }
    }
    
    this.environmentalAnalyzer = new AdvancedEnvironmentalAnalyzer(environmentalConfig)

    // 初始化自适应学习分析器
    this.adaptiveLearning = createAdaptiveLearningAnalyzer(adaptiveLearningConfig)

    // 初始化多帧分析器
    this.multiFrameAnalyzer = createMultiFrameAnalyzer(multiFrameConfig)
  }

  /**
   * 计算增强的角度
   */
  private calculateEnhancedAngle(
    point1: PoseLandmark, 
    vertex: PoseLandmark, 
    point2: PoseLandmark
  ): { angle: number; confidence: number } {
    const vector1 = {
      x: point1.x - vertex.x,
      y: point1.y - vertex.y
    }
    const vector2 = {
      x: point2.x - vertex.x,
      y: point2.y - vertex.y
    }
    
    const dot = vector1.x * vector2.x + vector1.y * vector2.y
    const mag1 = Math.sqrt(vector1.x * vector1.x + vector1.y * vector1.y)
    const mag2 = Math.sqrt(vector2.x * vector2.x + vector2.y * vector2.y)
    
    // 计算置信度（基于向量长度和可见性）
    const confidence = Math.min(
      (point1.visibility ?? 1) * (vertex.visibility ?? 1) * (point2.visibility ?? 1),
      Math.min(mag1, mag2) * 2 // 向量长度影响置信度
    )
    
    const angle = Math.acos(Math.max(-1, Math.min(1, dot / (mag1 * mag2))))
    
    return { angle, confidence }
  }

  /**
   * 检测环境因素（使用高级环境分析器）
   */
  private detectEnvironmentalFactors(landmarks: PoseLandmark[]): EnvironmentalConditions {
    return this.environmentalAnalyzer.analyzeEnvironmentalConditions(landmarks)
  }

  /**
   * 智能坐姿检测
   */
  private detectSeatedPosture(landmarks: PoseLandmark[]): { isSeated: boolean; confidence: number } {
    if (!landmarks || landmarks.length < 33) return { isSeated: false, confidence: 0 }
    
    const leftHip = landmarks[PoseLandmarkIndex.LEFT_HIP]
    const rightHip = landmarks[PoseLandmarkIndex.RIGHT_HIP]
    const leftKnee = landmarks[PoseLandmarkIndex.LEFT_KNEE]
    const rightKnee = landmarks[PoseLandmarkIndex.RIGHT_KNEE]
    
    // 检查关键点可见性
    const keyPointsVisible = [leftHip, rightHip, leftKnee, rightKnee].every(
      point => (point.visibility ?? 0) >= this.config.minVisibility
    )
    
    if (!keyPointsVisible) {
      return { isSeated: false, confidence: 0 }
    }
    
    // 计算髋膝角度
    const leftAngleData = this.calculateEnhancedAngle(
      { ...leftHip, y: leftHip.y - 0.1 },
      leftHip,
      leftKnee
    )
    
    const rightAngleData = this.calculateEnhancedAngle(
      { ...rightHip, y: rightHip.y - 0.1 },
      rightHip,
      rightKnee
    )
    
    const avgAngle = (leftAngleData.angle + rightAngleData.angle) / 2
    const avgConfidence = (leftAngleData.confidence + rightAngleData.confidence) / 2
    const angleInDegrees = avgAngle * 180 / Math.PI
    
    // 动态调整角度范围（基于环境因素）
    let minAngle = 50
    let maxAngle = 130
    
    const environmentalFactors = this.detectEnvironmentalFactors(landmarks)
    if (environmentalFactors.camera.angle > 10) {
      // 根据摄像头角度调整检测范围
      const angleAdjustment = (environmentalFactors.camera.angle / 90) * 20
      minAngle -= angleAdjustment
      maxAngle += angleAdjustment
    }
    
    const isSeated = angleInDegrees > minAngle && angleInDegrees < maxAngle
    const confidence = avgConfidence * (isSeated ? 1 : 0.5)
    
    return { isSeated, confidence }
  }

  /**
   * 分析头部位置（增强版）
   */
  private analyzeHeadPositionEnhanced(landmarks: PoseLandmark[]): {
    position: 'center' | 'left' | 'right' | 'forward' | 'back';
    offset: { x: number; y: number; z: number };
    confidence: number;
  } {
    const nose = landmarks[PoseLandmarkIndex.NOSE]
    const leftShoulder = landmarks[PoseLandmarkIndex.LEFT_SHOULDER]
    const rightShoulder = landmarks[PoseLandmarkIndex.RIGHT_SHOULDER]
    
    const minVisibility = this.config.minVisibility
    const keyPointsVisible = [nose, leftShoulder, rightShoulder].every(
      point => (point.visibility ?? 0) >= minVisibility
    )
    
    if (!keyPointsVisible) {
      return { position: 'center', offset: { x: 0, y: 0, z: 0 }, confidence: 0 }
    }
    
    // 计算肩膀中心点
    const shoulderCenter = {
      x: (leftShoulder.x + rightShoulder.x) / 2,
      y: (leftShoulder.y + rightShoulder.y) / 2,
      z: (leftShoulder.z + rightShoulder.z) / 2
    }
    
    const headOffset = {
      x: nose.x - shoulderCenter.x,
      y: nose.y - shoulderCenter.y,
      z: nose.z - shoulderCenter.z
    }
    
    // 个人化基线调整
    let adjustedOffset = headOffset
    if (this.personalBaseline && this.config.personalizedLearningEnabled) {
      adjustedOffset = {
        x: headOffset.x - this.personalBaseline.avgHeadOffset.x,
        y: headOffset.y - this.personalBaseline.avgHeadOffset.y,
        z: headOffset.z - this.personalBaseline.avgHeadOffset.z
      }
    }
    
    // 动态阈值调整
    let thresholdX = 0.05
    let thresholdZ = 0.05
    
    const environmentalFactors = this.detectEnvironmentalFactors(landmarks)
    if (environmentalFactors.lighting.level < 0.7) {
      thresholdX *= 1.2 // 光线不好时放宽阈值
      thresholdZ *= 1.2
    }
    
    // 计算置信度
    const confidence = Math.min(
      nose.visibility ?? 1,
      leftShoulder.visibility ?? 1,
      rightShoulder.visibility ?? 1
    )
    
    // 判断头部位置
    let position: 'center' | 'left' | 'right' | 'forward' | 'back' = 'center'
    if (Math.abs(adjustedOffset.x) > thresholdX) {
      position = adjustedOffset.x > 0 ? 'right' : 'left'
    } else if (Math.abs(adjustedOffset.z) > thresholdZ) {
      position = adjustedOffset.z > 0 ? 'back' : 'forward'
    }
    
    return { position, offset: adjustedOffset, confidence }
  }

  /**
   * 计算智能专注分数
   */
  private calculateIntelligentFocusScore(
    headData: any,
    shoulderBalance: number,
    bodyLean: number,
    seatedData: any,
    environmentalFactors: any
  ): { score: number; confidence: number; factors: any } {
    // 获取自适应学习的个人化阈值
    const personalizedThresholds = this.adaptiveLearning.getPersonalizedThresholds()
    const environmentalPreferences = this.adaptiveLearning.getEnvironmentalPreferences()
    
    let score = 100
    let confidenceScore = 1.0
    const factors: any = {}

    // 头部位置评分（权重：40%）
    if (headData.position === 'center') {
      factors.headPositionScore = 100
    } else if (headData.position === 'forward' || headData.position === 'back') {
      factors.headPositionScore = 70
    } else {
      factors.headPositionScore = 50
    }
    
    // 使用个人化的头部位置偏好
    if (this.adaptiveLearning.getBehaviorPattern().posturePreferences.preferredHeadPosition === headData.position) {
      factors.headPositionScore = Math.min(100, factors.headPositionScore * 1.2) // 20%加成
    }

    // 肩膀平衡评分（权重：25%）
    const shoulderBalanceTolerance = this.adaptiveLearning.getBehaviorPattern().posturePreferences.tolerableShoulderImbalance
    factors.shoulderBalanceScore = Math.max(0, 100 - Math.abs(shoulderBalance) * 100 / shoulderBalanceTolerance)

    // 身体倾斜评分（权重：25%）
    const bodyLeanTolerance = this.adaptiveLearning.getBehaviorPattern().posturePreferences.tolerableBodyLean
    factors.bodyLeanScore = Math.max(0, 100 - Math.abs(bodyLean) * 100 / bodyLeanTolerance)

    // 坐姿评分（权重：10%）
    factors.seatedScore = seatedData.isSeated ? 100 : 20

    // 综合评分
    score = (
      factors.headPositionScore * 0.4 +
      factors.shoulderBalanceScore * 0.25 +
      factors.bodyLeanScore * 0.25 +
      factors.seatedScore * 0.1
    )

    // 环境因素调整
    let environmentalAdjustment = 1.0
    
    // 使用个人化的环境偏好
    const lightingDiff = Math.abs(environmentalFactors.lighting.level - environmentalPreferences.optimalLighting)
    const angleDiff = Math.abs(environmentalFactors.camera.angle - environmentalPreferences.preferredCameraAngle)
    const complexityDiff = Math.abs(environmentalFactors.background.complexity - environmentalPreferences.tolerableBackgroundComplexity)
    
    // 基于个人化偏好调整环境影响
    if (lightingDiff < 0.2) environmentalAdjustment *= 1.1 // 光线接近个人偏好
    else if (lightingDiff > 0.4) environmentalAdjustment *= 0.9 // 光线偏离个人偏好
    
    if (angleDiff < 10) environmentalAdjustment *= 1.05 // 角度接近个人偏好
    else if (angleDiff > 25) environmentalAdjustment *= 0.95 // 角度偏离个人偏好

    score *= environmentalAdjustment

    // 置信度计算
    confidenceScore = Math.min(1.0, (
      headData.confidence * 0.4 +
      seatedData.confidence * 0.3 +
      (environmentalFactors.pose.visibility / 1.0) * 0.3
    ))

    // 使用个人化的置信度阈值
    const minConfidence = personalizedThresholds.confidenceThreshold
    if (confidenceScore < minConfidence) {
      score *= (confidenceScore / minConfidence) // 按比例降低分数
    }

    return { 
      score: Math.max(0, Math.min(100, score)), 
      confidence: confidenceScore, 
      factors 
    }
  }

  /**
   * 应用高级时间序列平滑
   */
  private applyTemporalSmoothing(currentScore: number, timestamp: number): { 
    smoothed: number, 
    isOutlier: boolean, 
    stats: any 
  } {
    // 使用高级时间序列分析器处理
    const result = this.timeSeriesAnalyzer.process(currentScore, timestamp)
    
    return {
      smoothed: result.smoothed,
      isOutlier: result.isOutlier,
      stats: result.stats
    }
  }

  /**
   * 计算稳定性指标
   */
  private calculateStability(): number {
    if (this.historyData.length < 5) return 0
    
    const recentScores = this.historyData.slice(-10).map(data => data.focusScore)
    const mean = recentScores.reduce((sum, score) => sum + score, 0) / recentScores.length
    const variance = recentScores.reduce((sum, score) => sum + Math.pow(score - mean, 2), 0) / recentScores.length
    const stdDev = Math.sqrt(variance)
    
    // 返回稳定性分数（标准差越小越稳定）
    return Math.max(0, 1 - (stdDev / 50))
  }

  /**
   * 分析趋势
   */
  private analyzeTrend(): 'improving' | 'stable' | 'declining' {
    if (this.historyData.length < 10) return 'stable'
    
    const recentData = this.historyData.slice(-10)
    const oldData = this.historyData.slice(-20, -10)
    
    if (oldData.length === 0) return 'stable'
    
    const recentAvg = recentData.reduce((sum, data) => sum + data.focusScore, 0) / recentData.length
    const oldAvg = oldData.reduce((sum, data) => sum + data.focusScore, 0) / oldData.length
    
    const diff = recentAvg - oldAvg
    
    if (diff > 5) return 'improving'
    if (diff < -5) return 'declining'
    return 'stable'
  }

  /**
   * 将时间序列分析器的趋势类型映射到我们的类型
   */
  private mapTrendFromStats(trend: 'increasing' | 'decreasing' | 'stable'): 'improving' | 'stable' | 'declining' {
    switch (trend) {
      case 'increasing': return 'improving'
      case 'decreasing': return 'declining'
      case 'stable': return 'stable'
      default: return 'stable'
    }
  }

  /**
   * 更新个人基线
   */
  private updatePersonalBaseline(analysis: any): void {
    if (!this.config.personalizedLearningEnabled) return
    
    const now = Date.now()
    
    if (!this.personalBaseline) {
      this.personalBaseline = {
        avgHeadOffset: { x: 0, y: 0, z: 0 },
        avgShoulderBalance: 0,
        avgBodyLean: 0,
        preferredPosture: {
          headPosition: 'center',
          shoulderTolerance: 0.1,
          bodyLeanTolerance: 0.1
        },
        calibrationCount: 0,
        lastCalibration: now
      }
    }
    
    // 仅在高质量数据时更新基线
    if (analysis.confidence > 0.8 && analysis.focusScore > 60) {
      const adaptationRate = this.config.adaptationSpeed
      const baseline = this.personalBaseline
      
      // 更新平均值
      baseline.avgHeadOffset.x = baseline.avgHeadOffset.x * (1 - adaptationRate) + analysis.headOffset.x * adaptationRate
      baseline.avgHeadOffset.y = baseline.avgHeadOffset.y * (1 - adaptationRate) + analysis.headOffset.y * adaptationRate
      baseline.avgHeadOffset.z = baseline.avgHeadOffset.z * (1 - adaptationRate) + analysis.headOffset.z * adaptationRate
      
      baseline.avgShoulderBalance = baseline.avgShoulderBalance * (1 - adaptationRate) + analysis.shoulderBalance * adaptationRate
      baseline.avgBodyLean = baseline.avgBodyLean * (1 - adaptationRate) + analysis.bodyLean * adaptationRate
      
      baseline.calibrationCount++
      baseline.lastCalibration = now
    }
  }

  /**
   * 生成智能建议
   */
  private generateIntelligentRecommendations(context: any): string[] {
    const recommendations: string[] = []
    const { 
      trend, 
      stability, 
      environmentalFactors, 
      headOffset, 
      shoulderBalance, 
      bodyLean,
      personalizedThresholds,
      expectedScore 
    } = context
    
    // 基于趋势的建议
    if (trend === 'declining') {
      recommendations.push('检测到专注度下降趋势，建议短暂休息或调整坐姿')
    } else if (trend === 'improving') {
      recommendations.push('专注度正在改善，保持当前状态')
    }
    
    // 基于稳定性的建议
    if (personalizedThresholds && stability < personalizedThresholds.stabilityThreshold) {
      recommendations.push('姿态稳定性较低，建议保持更稳定的坐姿')
    }
    
    // 基于个人化期望的建议
    if (expectedScore) {
      const currentTime = new Date().getHours()
      const prediction = this.adaptiveLearning.predictUserPerformance(Date.now())
      
      if (prediction.expectedScore > 75 && context.focusScore < prediction.expectedScore * 0.8) {
        recommendations.push(`根据您在${currentTime}点的历史表现，您通常能达到更高的专注度`)
      }
      
      // 添加用户行为预测建议
      prediction.recommendations.forEach(rec => {
        if (!recommendations.includes(rec)) {
          recommendations.push(rec)
        }
      })
    }
    
    // 个人化环境建议
    const envPrefs = this.adaptiveLearning.getEnvironmentalPreferences()
    
    if (environmentalFactors && envPrefs) {
      if (Math.abs(environmentalFactors.lighting.level - envPrefs.optimalLighting) > 0.3) {
        if (environmentalFactors.lighting.level < envPrefs.optimalLighting) {
          recommendations.push('根据您的偏好，增加环境光线可能有助于提高专注度')
        } else {
          recommendations.push('根据您的偏好，适当降低环境光线可能有助于提高专注度')
        }
      }
      
      if (Math.abs(environmentalFactors.camera.angle - envPrefs.preferredCameraAngle) > 15) {
        recommendations.push('调整摄像头角度到您的偏好位置可能会改善检测效果')
      }
    }
    
    // 基于学习进度的建议
    const learningState = this.adaptiveLearning.getLearningState()
    if (learningState.learningProgress < 0.3) {
      recommendations.push('系统仍在学习您的习惯，持续使用将提供更准确的个人化建议')
    } else if (learningState.confidenceLevel > 0.8) {
      recommendations.push('基于您的使用习惯，系统已优化了检测标准')
    }
    
    // 传统姿态建议（仍然有效）
    if (headOffset && (Math.abs(headOffset.x) > 0.05 || Math.abs(headOffset.y) > 0.05)) {
      recommendations.push('调整头部位置到屏幕中央')
    }
    
    if (typeof shoulderBalance === 'number' && typeof bodyLean === 'number') {
      const behaviorPattern = this.adaptiveLearning.getBehaviorPattern()
      
      if (Math.abs(shoulderBalance) > behaviorPattern.posturePreferences.tolerableShoulderImbalance) {
        recommendations.push('注意保持肩膀平衡')
      }
      
      if (Math.abs(bodyLean) > behaviorPattern.posturePreferences.tolerableBodyLean) {
        recommendations.push('避免身体过度倾斜，保持直立坐姿')
      }
    }
    
    // 环境相关建议
    if (environmentalFactors) {
      if (environmentalFactors.lighting.quality === 'poor') {
        recommendations.push('改善照明条件以获得更好的检测效果')
      }
      
      if (environmentalFactors.background.motion) {
        recommendations.push('减少背景干扰有助于提高检测精度')
      }
    }
    
    return recommendations.slice(0, 5) // 限制建议数量
  }

  /**
   * 主要分析函数
   */
  public analyze(
    landmarks: PoseLandmark[], 
    timestamp: number = Date.now(),
    externalEnvironmentalFactors?: any
  ): EnhancedPostureAnalysis {
    if (!landmarks || landmarks.length < 33) {
      return {
        isSeated: false,
        isFocused: false,
        focusScore: 0,
        headPosition: 'center',
        shoulderBalance: 0,
        bodyLean: 0,
        warnings: ['无法检测到有效的姿态数据'],
        confidence: 0,
        stability: 0,
        trend: 'stable',
        personalizedScore: 0,
        environmentalFactors: {
          lighting: { level: 0, quality: 'poor', uniformity: 0, shadows: false },
          camera: { angle: 0, distance: 1, stability: 0, resolution: 'low' },
          background: { complexity: 1, contrast: 0, motion: false },
          pose: { visibility: 0, occlusion: 1, clarity: 0 }
        },
        recommendations: ['请确保摄像头能够清晰捕捉您的姿态']
      }
    }
    
    // 检测环境因素
    const environmentalFactors = this.detectEnvironmentalFactors(landmarks)
    
    // 增强版姿态检测
    const seatedData = this.detectSeatedPosture(landmarks)
    const headData = this.analyzeHeadPositionEnhanced(landmarks)
    
    // 计算肩膀平衡和身体倾斜（复用原有逻辑，但可能需要增强）
    const shoulderBalance = this.calculateShoulderBalance(landmarks)
    const bodyLean = this.calculateBodyLean(landmarks)
    
    // 计算智能专注分数（现在使用个人化阈值）
    const scoreData = this.calculateIntelligentFocusScore(
      headData,
      shoulderBalance,
      bodyLean,
      seatedData,
      environmentalFactors
    )
    
    // 应用高级时间序列平滑
    const smoothingResult = this.applyTemporalSmoothing(scoreData.score, timestamp)
    const smoothedScore = smoothingResult.smoothed
    
    // 计算稳定性和趋势（现在可以从时间序列分析器获取更准确的结果）
    const stability = Math.max(smoothingResult.stats.autocorrelation, this.calculateStability())
    const trend = this.mapTrendFromStats(smoothingResult.stats.trend)
    
    // 获取个人化的专注度阈值
    const personalizedThresholds = this.adaptiveLearning.getPersonalizedThresholds()
    const focusThreshold = personalizedThresholds.focusThreshold
    
    // 创建数据点
    const dataPoint: PostureDataPoint = {
      timestamp,
      focusScore: smoothedScore,
      headPosition: headData.position,
      shoulderBalance,
      bodyLean,
      confidence: scoreData.confidence,
      environmentalFactors
    }
    
    // 更新历史数据
    this.historyData.push(dataPoint)
    if (this.historyData.length > this.config.historySize) {
      this.historyData.shift()
    }
    
    // 计算个人化分数
    const expectedScore = this.adaptiveLearning.getExpectedFocusScoreForTime(timestamp)
    const personalizedScore = smoothedScore + (smoothedScore - expectedScore) * 0.2 // 基于期望值的调整
    
    // 构建分析结果
    const result: EnhancedPostureAnalysis = {
      isSeated: seatedData.isSeated,
      isFocused: smoothedScore > focusThreshold, // 使用个人化阈值
      focusScore: smoothedScore,
      headPosition: headData.position,
      shoulderBalance,
      bodyLean,
      warnings: this.generateWarnings(headData.position, shoulderBalance, bodyLean, seatedData.isSeated),
      confidence: scoreData.confidence,
      stability,
      trend,
      personalizedScore: Math.max(0, Math.min(100, personalizedScore)),
      environmentalFactors,
      recommendations: this.generateIntelligentRecommendations({
        trend,
        stability,
        environmentalFactors,
        headOffset: headData.offset,
        shoulderBalance,
        bodyLean,
        personalizedThresholds,
        expectedScore
      }),
      headOffset: headData.offset
    }
    
    // 将分析结果自动添加到多帧分析器中
    if (this.multiFrameAnalyzer) {
      this.multiFrameAnalyzer.addFrame(result)
    }

    this.previousAnalysis = result
    return result
  }

  /**
   * 计算肩膀平衡度（复用原有逻辑）
   */
  private calculateShoulderBalance(landmarks: PoseLandmark[]): number {
    const leftShoulder = landmarks[PoseLandmarkIndex.LEFT_SHOULDER]
    const rightShoulder = landmarks[PoseLandmarkIndex.RIGHT_SHOULDER]
    
    if ((leftShoulder.visibility ?? 0) < this.config.minVisibility || 
        (rightShoulder.visibility ?? 0) < this.config.minVisibility) {
      return 0
    }
    
    const heightDiff = rightShoulder.y - leftShoulder.y
    return Math.max(-1, Math.min(1, heightDiff * 10))
  }

  /**
   * 计算身体倾斜度（复用原有逻辑）
   */
  private calculateBodyLean(landmarks: PoseLandmark[]): number {
    const leftShoulder = landmarks[PoseLandmarkIndex.LEFT_SHOULDER]
    const rightShoulder = landmarks[PoseLandmarkIndex.RIGHT_SHOULDER]
    const leftHip = landmarks[PoseLandmarkIndex.LEFT_HIP]
    const rightHip = landmarks[PoseLandmarkIndex.RIGHT_HIP]
    
    const requiredPoints = [leftShoulder, rightShoulder, leftHip, rightHip]
    if (requiredPoints.some(point => (point.visibility ?? 0) < this.config.minVisibility)) {
      return 0
    }
    
    const shoulderCenter = (leftShoulder.x + rightShoulder.x) / 2
    const hipCenter = (leftHip.x + rightHip.x) / 2
    const lean = shoulderCenter - hipCenter
    
    return Math.max(-1, Math.min(1, lean * 5))
  }

  /**
   * 生成警告信息（复用并增强原有逻辑）
   */
  private generateWarnings(
    headPosition: string,
    shoulderBalance: number,
    bodyLean: number,
    isSeated: boolean
  ): string[] {
    const warnings: string[] = []
    
    if (!isSeated) {
      warnings.push('请保持坐姿进行学习')
    }
    
    if (headPosition === 'forward') {
      warnings.push('头部过于前倾，请调整坐姿')
    } else if (headPosition === 'left' || headPosition === 'right') {
      warnings.push('头部倾斜，请保持头部居中')
    }
    
    // 动态阈值
    const shoulderThreshold = this.personalBaseline ? 
      this.personalBaseline.preferredPosture.shoulderTolerance : 0.3
    const bodyLeanThreshold = this.personalBaseline ? 
      this.personalBaseline.preferredPosture.bodyLeanTolerance : 0.3
    
    if (Math.abs(shoulderBalance) > shoulderThreshold) {
      warnings.push('肩膀不平衡，请调整坐姿')
    }
    
    if (Math.abs(bodyLean) > bodyLeanThreshold) {
      warnings.push('身体倾斜过度，请坐直')
    }
    
    return warnings
  }

  /**
   * 获取个人基线数据
   */
  public getPersonalBaseline(): PersonalBaseline | null {
    return this.personalBaseline
  }

  /**
   * 重置个人基线
   */
  public resetPersonalBaseline(): void {
    this.personalBaseline = null
    this.historyData = []
  }

  /**
   * 获取历史数据
   */
  public getHistoryData(): PostureDataPoint[] {
    return [...this.historyData]
  }

  /**
   * 获取时间序列统计信息
   */
  public getTimeSeriesStats(): any {
    return this.timeSeriesAnalyzer.getStats()
  }

  /**
   * 预测未来专注度分数
   */
  public predictFocusScore(steps: number = 5): number[] {
    return this.timeSeriesAnalyzer.predict(steps)
  }

  /**
   * 检测当前是否有异常值
   */
  public hasRecentOutliers(): boolean {
    const history = this.timeSeriesAnalyzer.getHistory()
    if (history.raw.length < 5) return false
    
    // 检查最近5个数据点是否有异常
    const recentData = history.raw.slice(-5)
    const mean = recentData.reduce((sum, v) => sum + v, 0) / recentData.length
    const variance = recentData.reduce((sum, v) => sum + Math.pow(v - mean, 2), 0) / recentData.length
    const stdDev = Math.sqrt(variance)
    
    return recentData.some(value => Math.abs(value - mean) > 2 * stdDev)
  }

  /**
   * 重置时间序列分析器
   */
  public resetTimeSeriesAnalyzer(): void {
    this.timeSeriesAnalyzer.reset()
  }

  /**
   * 更新配置
   */
  public updateConfig(newConfig: Partial<EnhancedPoseConfig>): void {
    this.config = { ...this.config, ...newConfig }
  }

  /**
   * 更新时间序列分析器配置
   */
  public updateTimeSeriesConfig(newConfig: Partial<TimeSeriesConfig>): void {
    this.timeSeriesAnalyzer.updateConfig(newConfig)
  }

  /**
   * 记录用户会话数据供自适应学习使用
   */
  public recordSession(sessionDurationMinutes: number): void {
    if (this.historyData.length === 0) return
    
    // 转换历史数据为增强姿态分析格式
    const analysisData: EnhancedPostureAnalysis[] = this.historyData.map(dataPoint => ({
      isSeated: true, // 假设会话期间都是坐着的
      isFocused: dataPoint.focusScore > this.config.focusThreshold,
      focusScore: dataPoint.focusScore,
      headPosition: dataPoint.headPosition as any,
      shoulderBalance: dataPoint.shoulderBalance,
      bodyLean: dataPoint.bodyLean,
      warnings: [],
      confidence: dataPoint.confidence,
      stability: 0.5, // 默认值
      trend: 'stable' as any,
      personalizedScore: dataPoint.focusScore,
      environmentalFactors: dataPoint.environmentalFactors,
      recommendations: []
    }))
    
    // 记录到自适应学习分析器
    this.adaptiveLearning.recordSession(analysisData, sessionDurationMinutes)
  }
  
  /**
   * 添加用户显式反馈
   */
  public addUserFeedback(
    satisfied: boolean, 
    comfort: number, 
    sessionQuality: number,
    comments?: string
  ): void {
    const now = new Date()
    
    const feedback: UserFeedback = {
      timestamp: Date.now(),
      type: 'explicit',
      satisfied,
      comfort,
      sessionQuality,
      comments,
      sessionContext: {
        timeOfDay: now.getHours(),
        dayOfWeek: now.getDay(),
        sessionDuration: 0 // Will be updated if available
      }
    }
    
    this.adaptiveLearning.addUserFeedback(feedback)
  }
  
  /**
   * 获取用户行为预测
   */
  public predictUserPerformance(timestamp?: number): {
    expectedScore: number
    confidence: number
    recommendations: string[]
  } {
    return this.adaptiveLearning.predictUserPerformance(timestamp || Date.now())
  }
  
  /**
   * 获取当前学习状态
   */
  public getLearningState(): any {
    return this.adaptiveLearning.getLearningState()
  }
  
  /**
   * 获取用户行为模式
   */
  public getBehaviorPattern(): any {
    return this.adaptiveLearning.getBehaviorPattern()
  }
  
  /**
   * 重置学习数据
   */
  public resetLearningData(): void {
    this.adaptiveLearning.resetLearningData()
  }
  
  /**
   * 导出学习数据
   */
  public exportLearningData(): any {
    return this.adaptiveLearning.exportLearningData()
  }
  
  /**
   * 导入学习数据
   */
  public importLearningData(data: any): void {
    this.adaptiveLearning.importLearningData(data)
  }
  
  /**
   * 获取环境分析状态
   */
  public getEnvironmentalState(): any {
    return this.environmentalAnalyzer.getCurrentConditions()
  }

  /**
   * 获取多帧分析结果
   */
  public getMultiFrameAnalysis(): MultiFrameAnalysisResult | null {
    return this.multiFrameAnalyzer.analyzeMultiFrame()
  }
  
  /**
   * 添加帧数据到多帧分析器
   */
  public addFrameToMultiAnalysis(analysis: EnhancedPostureAnalysis): void {
    this.multiFrameAnalyzer.addFrame(analysis)
  }
  
  /**
   * 重置多帧分析器
   */
  public resetMultiFrameAnalysis(): void {
    this.multiFrameAnalyzer.reset()
  }
  
  /**
   * 获取多帧详细统计
   */
  public getMultiFrameStats(): any {
    return this.multiFrameAnalyzer.getDetailedStats()
  }
}

// 导出增强版分析函数（兼容现有接口）
export function analyzePostureEnhanced(
  landmarks: PoseLandmark[], 
  analyzer?: EnhancedPoseAnalyzer
): EnhancedPostureAnalysis {
  if (!analyzer) {
    analyzer = new EnhancedPoseAnalyzer()
  }
  
  return analyzer.analyze(landmarks)
}

// 创建默认分析器实例
export const defaultEnhancedAnalyzer = new EnhancedPoseAnalyzer() 