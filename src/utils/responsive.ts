// 响应式断点定义
export const BREAKPOINTS = {
  mobile: 375,
  tablet: 768,
  desktop: 1024,
  large: 1200,
  xlarge: 1400
} as const

// 媒体查询字符串
export const MEDIA_QUERIES = {
  mobile: `(max-width: ${BREAKPOINTS.mobile}px)`,
  tablet: `(max-width: ${BREAKPOINTS.tablet}px)`,
  desktop: `(max-width: ${BREAKPOINTS.desktop}px)`,
  large: `(max-width: ${BREAKPOINTS.large}px)`,
  xlarge: `(max-width: ${BREAKPOINTS.xlarge}px)`,
  
  // min-width 查询
  minMobile: `(min-width: ${BREAKPOINTS.mobile + 1}px)`,
  minTablet: `(min-width: ${BREAKPOINTS.tablet + 1}px)`,
  minDesktop: `(min-width: ${BREAKPOINTS.desktop + 1}px)`,
  minLarge: `(min-width: ${BREAKPOINTS.large + 1}px)`,
  
  // 范围查询
  tabletOnly: `(min-width: ${BREAKPOINTS.mobile + 1}px) and (max-width: ${BREAKPOINTS.tablet}px)`,
  desktopOnly: `(min-width: ${BREAKPOINTS.tablet + 1}px) and (max-width: ${BREAKPOINTS.desktop}px)`,
  
  // 设备特性
  touch: '(hover: none) and (pointer: coarse)',
  mouse: '(hover: hover) and (pointer: fine)',
  landscape: '(orientation: landscape)',
  portrait: '(orientation: portrait)',
  
  // 高分辨率
  retina: '(-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi)',
  
  // 暗色模式
  darkMode: '(prefers-color-scheme: dark)',
  lightMode: '(prefers-color-scheme: light)',
  
  // 减少动画
  reducedMotion: '(prefers-reduced-motion: reduce)'
} as const

// 设备类型检测
export function getDeviceType(): 'mobile' | 'tablet' | 'desktop' {
  if (typeof window === 'undefined') return 'desktop'
  
  const width = window.innerWidth
  if (width <= BREAKPOINTS.mobile) return 'mobile'
  if (width <= BREAKPOINTS.tablet) return 'tablet'
  return 'desktop'
}

// 检测是否为触摸设备
export function isTouchDevice(): boolean {
  if (typeof window === 'undefined') return false
  
  return 'ontouchstart' in window || 
         navigator.maxTouchPoints > 0 || 
         (navigator as any).msMaxTouchPoints > 0
}

// 检测设备方向
export function getOrientation(): 'portrait' | 'landscape' {
  if (typeof window === 'undefined') return 'landscape'
  
  return window.innerHeight > window.innerWidth ? 'portrait' : 'landscape'
}

// 获取视口尺寸
export function getViewportSize() {
  if (typeof window === 'undefined') {
    return { width: 1920, height: 1080 }
  }
  
  return {
    width: window.innerWidth,
    height: window.innerHeight
  }
}

// 媒体查询匹配检测
export function matchMedia(query: string): boolean {
  if (typeof window === 'undefined') return false
  
  return window.matchMedia(query).matches
}

// 创建响应式监听器
export function createMediaListener(
  query: string, 
  callback: (matches: boolean) => void
): () => void {
  if (typeof window === 'undefined') {
    return () => {}
  }
  
  const mediaQuery = window.matchMedia(query)
  const handler = (e: MediaQueryListEvent) => callback(e.matches)
  
  // 立即执行一次
  callback(mediaQuery.matches)
  
  // 添加监听器
  mediaQuery.addEventListener('change', handler)
  
  // 返回清理函数
  return () => mediaQuery.removeEventListener('change', handler)
}

// React Hook for media queries
import { useState, useEffect } from 'react'

export function useMediaQuery(query: string): boolean {
  const [matches, setMatches] = useState(() => matchMedia(query))
  
  useEffect(() => {
    return createMediaListener(query, setMatches)
  }, [query])
  
  return matches
}

// React Hook for device type
export function useDeviceType() {
  const [deviceType, setDeviceType] = useState(() => getDeviceType())
  
  useEffect(() => {
    const handleResize = () => setDeviceType(getDeviceType())
    
    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])
  
  return deviceType
}

// React Hook for orientation
export function useOrientation() {
  const [orientation, setOrientation] = useState(() => getOrientation())
  
  useEffect(() => {
    const handleResize = () => setOrientation(getOrientation())
    const handleOrientationChange = () => {
      // 延迟检测以确保尺寸已更新
      setTimeout(handleResize, 100)
    }
    
    window.addEventListener('resize', handleResize)
    window.addEventListener('orientationchange', handleOrientationChange)
    
    return () => {
      window.removeEventListener('resize', handleResize)
      window.removeEventListener('orientationchange', handleOrientationChange)
    }
  }, [])
  
  return orientation
}

// React Hook for viewport size
export function useViewportSize() {
  const [size, setSize] = useState(() => getViewportSize())
  
  useEffect(() => {
    const handleResize = () => setSize(getViewportSize())
    
    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])
  
  return size
}

// 检测是否为移动设备（基于用户代理）
export function isMobile(): boolean {
  if (typeof navigator === 'undefined') return false
  
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
    navigator.userAgent
  )
}

// 检测是否为iOS设备
export function isIOS(): boolean {
  if (typeof navigator === 'undefined') return false
  
  return /iPad|iPhone|iPod/.test(navigator.userAgent)
}

// 检测是否为Android设备
export function isAndroid(): boolean {
  if (typeof navigator === 'undefined') return false
  
  return /Android/.test(navigator.userAgent)
}

// 计算缩放比例
export function getScaleFactor(baseWidth: number = 800): number {
  const { width } = getViewportSize()
  const deviceType = getDeviceType()
  
  // 为不同设备类型设置不同的缩放策略
  switch (deviceType) {
    case 'mobile':
      return Math.min(width / baseWidth, 1)
    case 'tablet':
      return Math.min(width / baseWidth, 1.2)
    default:
      return Math.min(width / baseWidth, 1.5)
  }
}

// 响应式字体大小计算
export function getResponsiveFontSize(baseSize: number): number {
  const deviceType = getDeviceType()
  const scaleFactor = getScaleFactor()
  
  switch (deviceType) {
    case 'mobile':
      return Math.max(baseSize * scaleFactor * 0.9, 12)
    case 'tablet':
      return Math.max(baseSize * scaleFactor * 0.95, 14)
    default:
      return baseSize * scaleFactor
  }
}

// 响应式间距计算
export function getResponsiveSpacing(baseSpacing: number): number {
  const deviceType = getDeviceType()
  
  switch (deviceType) {
    case 'mobile':
      return baseSpacing * 0.7
    case 'tablet':
      return baseSpacing * 0.85
    default:
      return baseSpacing
  }
} 