import { PostureDataPoint } from '../types/pose'

/**
 * 时间序列分析配置
 */
export interface TimeSeriesConfig {
  // 滤波器配置
  kalmanProcessNoise: number    // 过程噪声
  kalmanMeasurementNoise: number // 测量噪声
  
  // 平滑算法配置
  smoothingWindow: number       // 滑动窗口大小
  adaptiveSmoothingEnabled: boolean // 自适应平滑
  
  // 异常检测
  outlierDetectionEnabled: boolean
  outlierThreshold: number      // 异常值阈值(标准差倍数)
  
  // 趋势分析
  trendAnalysisWindow: number   // 趋势分析窗口
  seasonalityPeriod: number     // 季节性周期
}

/**
 * 时间序列统计信息
 */
export interface TimeSeriesStats {
  mean: number
  variance: number
  stdDev: number
  min: number
  max: number
  trend: 'increasing' | 'decreasing' | 'stable'
  seasonality: number
  autocorrelation: number
}

/**
 * 卡尔曼滤波器状态
 */
interface KalmanState {
  x: number      // 状态估计
  P: number      // 估计误差协方差
  Q: number      // 过程噪声协方差
  R: number      // 测量噪声协方差
  K: number      // 卡尔曼增益
}

/**
 * 高级时间序列分析器
 */
export class AdvancedTimeSeriesAnalyzer {
  private config: TimeSeriesConfig
  private kalmanState: KalmanState
  private rawDataHistory: number[] = []
  private filteredDataHistory: number[] = []
  private timestamps: number[] = []

  constructor(config: Partial<TimeSeriesConfig> = {}) {
    this.config = {
      kalmanProcessNoise: 0.1,
      kalmanMeasurementNoise: 1.0,
      smoothingWindow: 10,
      adaptiveSmoothingEnabled: true,
      outlierDetectionEnabled: true,
      outlierThreshold: 2.5,
      trendAnalysisWindow: 20,
      seasonalityPeriod: 60, // 1分钟周期（假设1Hz采样）
      ...config
    }

    // 初始化卡尔曼滤波器
    this.kalmanState = {
      x: 0,           // 初始状态估计
      P: 1,           // 初始估计误差协方差
      Q: this.config.kalmanProcessNoise,
      R: this.config.kalmanMeasurementNoise,
      K: 0
    }
  }

  /**
   * 卡尔曼滤波处理
   */
  private kalmanFilter(measurement: number): number {
    const state = this.kalmanState

    // 预测步骤
    // x_k|k-1 = x_k-1|k-1 (假设常速模型)
    // P_k|k-1 = P_k-1|k-1 + Q
    state.P = state.P + state.Q

    // 更新步骤
    // K_k = P_k|k-1 / (P_k|k-1 + R)
    state.K = state.P / (state.P + state.R)
    
    // x_k|k = x_k|k-1 + K_k * (z_k - x_k|k-1)
    state.x = state.x + state.K * (measurement - state.x)
    
    // P_k|k = (1 - K_k) * P_k|k-1
    state.P = (1 - state.K) * state.P

    return state.x
  }

  /**
   * 异常值检测
   */
  private detectOutlier(value: number, recentData: number[]): boolean {
    if (!this.config.outlierDetectionEnabled || recentData.length < 5) {
      return false
    }

    const mean = recentData.reduce((sum, v) => sum + v, 0) / recentData.length
    const variance = recentData.reduce((sum, v) => sum + Math.pow(v - mean, 2), 0) / recentData.length
    const stdDev = Math.sqrt(variance)

    return Math.abs(value - mean) > (this.config.outlierThreshold * stdDev)
  }

  /**
   * 自适应平滑
   */
  private adaptiveSmoothing(currentValue: number, recentData: number[]): number {
    if (!this.config.adaptiveSmoothingEnabled || recentData.length < 3) {
      return currentValue
    }

    // 计算数据变化率
    const recentChanges = []
    for (let i = 1; i < recentData.length; i++) {
      recentChanges.push(Math.abs(recentData[i] - recentData[i-1]))
    }
    
    const avgChange = recentChanges.reduce((sum, change) => sum + change, 0) / recentChanges.length
    
    // 根据变化率调整平滑因子
    let smoothingFactor = 0.3 // 基础平滑因子
    if (avgChange > 10) {
      smoothingFactor = 0.1 // 变化大时，更多平滑
    } else if (avgChange < 3) {
      smoothingFactor = 0.7 // 变化小时，更快响应
    }

    const recentAvg = recentData.reduce((sum, v) => sum + v, 0) / recentData.length
    return currentValue * smoothingFactor + recentAvg * (1 - smoothingFactor)
  }

  /**
   * 移动平均滤波器
   */
  private movingAverage(data: number[], window: number): number {
    const relevantData = data.slice(-window)
    return relevantData.reduce((sum, value) => sum + value, 0) / relevantData.length
  }

  /**
   * 指数加权移动平均
   */
  private exponentialMovingAverage(currentValue: number, previousEMA: number, alpha: number): number {
    return alpha * currentValue + (1 - alpha) * previousEMA
  }

  /**
   * 趋势检测（线性回归）
   */
  private detectTrend(data: number[], timestamps: number[]): 'increasing' | 'decreasing' | 'stable' {
    if (data.length < this.config.trendAnalysisWindow) {
      return 'stable'
    }

    const recentData = data.slice(-this.config.trendAnalysisWindow)
    const recentTimestamps = timestamps.slice(-this.config.trendAnalysisWindow)
    
    // 简单线性回归
    const n = recentData.length
    const sumX = recentTimestamps.reduce((sum, t) => sum + t, 0)
    const sumY = recentData.reduce((sum, v) => sum + v, 0)
    const sumXY = recentTimestamps.reduce((sum, t, i) => sum + t * recentData[i], 0)
    const sumXX = recentTimestamps.reduce((sum, t) => sum + t * t, 0)

    const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX)
    
    // 根据斜率判断趋势
    if (slope > 0.5) return 'increasing'
    if (slope < -0.5) return 'decreasing'
    return 'stable'
  }

  /**
   * 计算自相关性
   */
  private calculateAutocorrelation(data: number[], lag: number = 1): number {
    if (data.length < lag + 10) return 0

    const n = data.length - lag
    const mean = data.reduce((sum, v) => sum + v, 0) / data.length
    
    let numerator = 0
    let denominator = 0
    
    for (let i = 0; i < n; i++) {
      numerator += (data[i] - mean) * (data[i + lag] - mean)
    }
    
    for (let i = 0; i < data.length; i++) {
      denominator += Math.pow(data[i] - mean, 2)
    }
    
    return denominator === 0 ? 0 : numerator / denominator
  }

  /**
   * 主要处理函数
   */
  public process(newValue: number, timestamp: number = Date.now()): {
    filtered: number
    smoothed: number
    isOutlier: boolean
    stats: TimeSeriesStats
  } {
    // 检测异常值
    const isOutlier = this.detectOutlier(newValue, this.rawDataHistory)
    
    // 如果是异常值，可以选择忽略或使用插值
    const processedValue = isOutlier ? 
      (this.rawDataHistory.length > 0 ? this.rawDataHistory[this.rawDataHistory.length - 1] : newValue) : 
      newValue

    // 更新历史数据
    this.rawDataHistory.push(processedValue)
    this.timestamps.push(timestamp)
    
    // 保持历史数据大小
    const maxHistorySize = Math.max(this.config.smoothingWindow * 3, this.config.trendAnalysisWindow * 2, 100)
    if (this.rawDataHistory.length > maxHistorySize) {
      this.rawDataHistory.shift()
      this.timestamps.shift()
    }

    // 卡尔曼滤波
    const kalmanFiltered = this.kalmanFilter(processedValue)
    
    // 自适应平滑
    const adaptiveSmoothed = this.config.adaptiveSmoothingEnabled ? 
      this.adaptiveSmoothing(kalmanFiltered, this.rawDataHistory) :
      this.movingAverage(this.rawDataHistory, this.config.smoothingWindow)

    // 更新滤波后的历史数据
    this.filteredDataHistory.push(adaptiveSmoothed)
    if (this.filteredDataHistory.length > maxHistorySize) {
      this.filteredDataHistory.shift()
    }

    // 计算统计信息
    const stats = this.calculateStats()

    return {
      filtered: kalmanFiltered,
      smoothed: adaptiveSmoothed,
      isOutlier,
      stats
    }
  }

  /**
   * 计算时间序列统计信息
   */
  private calculateStats(): TimeSeriesStats {
    const data = this.filteredDataHistory
    
    if (data.length === 0) {
      return {
        mean: 0,
        variance: 0,
        stdDev: 0,
        min: 0,
        max: 0,
        trend: 'stable',
        seasonality: 0,
        autocorrelation: 0
      }
    }

    const mean = data.reduce((sum, v) => sum + v, 0) / data.length
    const variance = data.reduce((sum, v) => sum + Math.pow(v - mean, 2), 0) / data.length
    const stdDev = Math.sqrt(variance)
    const min = Math.min(...data)
    const max = Math.max(...data)
    
    const trend = this.detectTrend(data, this.timestamps)
    const autocorrelation = this.calculateAutocorrelation(data)
    
    // 简单的季节性检测
    const seasonality = data.length >= this.config.seasonalityPeriod ? 
      this.calculateAutocorrelation(data, this.config.seasonalityPeriod) : 0

    return {
      mean,
      variance,
      stdDev,
      min,
      max,
      trend,
      seasonality,
      autocorrelation
    }
  }

  /**
   * 获取预测值
   */
  public predict(steps: number = 1): number[] {
    if (this.filteredDataHistory.length < 2) {
      return Array(steps).fill(this.kalmanState.x)
    }

    const predictions: number[] = []
    const stats = this.calculateStats()
    
    // 基于趋势的简单预测
    const recentData = this.filteredDataHistory.slice(-10)
    let currentValue = recentData[recentData.length - 1]
    
    // 计算平均变化率
    let avgChange = 0
    if (recentData.length >= 2) {
      const changes = []
      for (let i = 1; i < recentData.length; i++) {
        changes.push(recentData[i] - recentData[i-1])
      }
      avgChange = changes.reduce((sum, change) => sum + change, 0) / changes.length
    }

    for (let i = 0; i < steps; i++) {
      currentValue += avgChange
      predictions.push(Math.max(0, Math.min(100, currentValue))) // 限制在0-100范围内
    }

    return predictions
  }

  /**
   * 重置分析器
   */
  public reset(): void {
    this.rawDataHistory = []
    this.filteredDataHistory = []
    this.timestamps = []
    this.kalmanState = {
      x: 0,
      P: 1,
      Q: this.config.kalmanProcessNoise,
      R: this.config.kalmanMeasurementNoise,
      K: 0
    }
  }

  /**
   * 更新配置
   */
  public updateConfig(newConfig: Partial<TimeSeriesConfig>): void {
    this.config = { ...this.config, ...newConfig }
    
    // 更新卡尔曼滤波器参数
    this.kalmanState.Q = this.config.kalmanProcessNoise
    this.kalmanState.R = this.config.kalmanMeasurementNoise
  }

  /**
   * 获取历史数据
   */
  public getHistory(): { raw: number[], filtered: number[], timestamps: number[] } {
    return {
      raw: [...this.rawDataHistory],
      filtered: [...this.filteredDataHistory],
      timestamps: [...this.timestamps]
    }
  }
} 