import { platformDetector, OperatingSystem, Browser, DeviceType } from './PlatformDetector'

// 摄像头API类型定义
export enum CameraAdapterType {
  WEBRTC = 'webrtc',
  LEGACY = 'legacy',
  CORDOVA = 'cordova',
  ELECTRON = 'electron',
  UNKNOWN = 'unknown'
}

export enum CameraQuality {
  LOW = 'low',        // 480p
  MEDIUM = 'medium',  // 720p
  HIGH = 'high',      // 1080p
  ULTRA = 'ultra'     // 4K
}

export enum CameraFacingMode {
  USER = 'user',           // 前置摄像头
  ENVIRONMENT = 'environment', // 后置摄像头
  LEFT = 'left',           // 左摄像头
  RIGHT = 'right'          // 右摄像头
}

export interface CameraConstraints {
  width?: number | { min?: number; max?: number; ideal?: number }
  height?: number | { min?: number; max?: number; ideal?: number }
  frameRate?: number | { min?: number; max?: number; ideal?: number }
  facingMode?: CameraFacingMode | string
  deviceId?: string
  aspectRatio?: number
  resizeMode?: 'none' | 'crop-and-scale'
  focusMode?: 'continuous' | 'single-shot' | 'manual'
  exposureMode?: 'continuous' | 'single-shot' | 'manual'
  whiteBalanceMode?: 'continuous' | 'single-shot' | 'manual'
  zoom?: number
}

export interface CameraDevice {
  deviceId: string
  groupId: string
  kind: MediaDeviceKind
  label: string
  facing?: CameraFacingMode
  resolution?: {
    width: number
    height: number
  }
  capabilities?: MediaTrackCapabilities
}

export interface CameraCapabilities {
  devices: CameraDevice[]
  supportedConstraints: string[]
  supportedQualities: CameraQuality[]
  supportedFormats: string[]
  maxResolution: {
    width: number
    height: number
  }
  features: {
    zoom: boolean
    focus: boolean
    exposure: boolean
    whiteBalance: boolean
    torch: boolean
    faceDetection: boolean
    backgroundBlur: boolean
  }
}

export interface CameraStreamOptions {
  quality: CameraQuality
  facingMode: CameraFacingMode
  deviceId?: string
  constraints?: CameraConstraints
  autoSwitch?: boolean  // 自动切换到最佳设备
  fallback?: boolean    // 启用降级策略
}

export interface CameraStreamInfo {
  stream: MediaStream
  device: CameraDevice
  actualConstraints: MediaTrackConstraints
  settings: MediaTrackSettings
  capabilities: MediaTrackCapabilities
}

export interface CameraError {
  code: string
  message: string
  name: string
  constraint?: string
  deviceId?: string
  platform?: string
  suggestion?: string
}

// 错误代码常量
export const CAMERA_ERROR_CODES = {
  NOT_SUPPORTED: 'CAMERA_NOT_SUPPORTED',
  PERMISSION_DENIED: 'CAMERA_PERMISSION_DENIED',
  DEVICE_NOT_FOUND: 'CAMERA_DEVICE_NOT_FOUND',
  DEVICE_BUSY: 'CAMERA_DEVICE_BUSY',
  CONSTRAINT_NOT_SATISFIED: 'CAMERA_CONSTRAINT_NOT_SATISFIED',
  INITIALIZATION_FAILED: 'CAMERA_INITIALIZATION_FAILED',
  STREAM_FAILED: 'CAMERA_STREAM_FAILED',
  PLATFORM_NOT_SUPPORTED: 'CAMERA_PLATFORM_NOT_SUPPORTED',
  BROWSER_NOT_SUPPORTED: 'CAMERA_BROWSER_NOT_SUPPORTED',
  HTTPS_REQUIRED: 'CAMERA_HTTPS_REQUIRED',
  UNKNOWN_ERROR: 'CAMERA_UNKNOWN_ERROR'
} as const

// 质量预设配置
const QUALITY_PRESETS: Record<CameraQuality, CameraConstraints> = {
  [CameraQuality.LOW]: {
    width: { ideal: 640 },
    height: { ideal: 480 },
    frameRate: { ideal: 24 }
  },
  [CameraQuality.MEDIUM]: {
    width: { ideal: 1280 },
    height: { ideal: 720 },
    frameRate: { ideal: 30 }
  },
  [CameraQuality.HIGH]: {
    width: { ideal: 1920 },
    height: { ideal: 1080 },
    frameRate: { ideal: 30 }
  },
  [CameraQuality.ULTRA]: {
    width: { ideal: 3840 },
    height: { ideal: 2160 },
    frameRate: { ideal: 30 }
  }
}

export class CameraAdapterService {
  private static instance: CameraAdapterService
  private adapterType: CameraAdapterType = CameraAdapterType.UNKNOWN
  private currentStream: MediaStream | null = null
  private availableDevices: CameraDevice[] = []
  private capabilities: CameraCapabilities | null = null
  private isInitialized = false
  private listeners: {
    deviceChange: Array<(devices: CameraDevice[]) => void>
    streamChange: Array<(stream: MediaStream | null) => void>
    error: Array<(error: CameraError) => void>
  } = {
    deviceChange: [],
    streamChange: [],
    error: []
  }

  private constructor() {
    this.detectAdapterType()
    this.setupEventListeners()
  }

  public static getInstance(): CameraAdapterService {
    if (!CameraAdapterService.instance) {
      CameraAdapterService.instance = new CameraAdapterService()
    }
    return CameraAdapterService.instance
  }

  /**
   * 初始化摄像头适配器
   */
  public async initialize(): Promise<void> {
    if (this.isInitialized) {
      return
    }

    try {
      await this.checkSupport()
      await this.detectDevices()
      await this.generateCapabilities()
      this.isInitialized = true
    } catch (error) {
      const cameraError = this.createError(
        CAMERA_ERROR_CODES.INITIALIZATION_FAILED,
        '摄像头适配器初始化失败',
        error
      )
      this.notifyError(cameraError)
      throw cameraError
    }
  }

  /**
   * 检查摄像头支持
   */
  private async checkSupport(): Promise<void> {
    const platform = platformDetector.getPlatformInfo()

    // 检查HTTPS要求（生产环境）
    if (location.protocol !== 'https:' && location.hostname !== 'localhost') {
      throw this.createError(
        CAMERA_ERROR_CODES.HTTPS_REQUIRED,
        '摄像头访问需要HTTPS连接',
        null,
        '请使用HTTPS协议访问网站'
      )
    }

    // 检查浏览器支持
    if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
      throw this.createError(
        CAMERA_ERROR_CODES.BROWSER_NOT_SUPPORTED,
        '浏览器不支持摄像头API',
        null,
        '请升级到支持WebRTC的现代浏览器'
      )
    }

    // 平台特定检查
    if (platform.browser === Browser.IE) {
      throw this.createError(
        CAMERA_ERROR_CODES.BROWSER_NOT_SUPPORTED,
        'Internet Explorer不支持摄像头功能',
        null,
        '请使用Chrome、Firefox、Safari或Edge浏览器'
      )
    }

    // 检查WebRTC支持
    if (!platform.capabilities.webRTC) {
      throw this.createError(
        CAMERA_ERROR_CODES.NOT_SUPPORTED,
        '当前环境不支持WebRTC',
        null,
        '请检查浏览器设置或升级浏览器版本'
      )
    }
  }

  /**
   * 检测适配器类型
   */
  private detectAdapterType(): void {
    const platform = platformDetector.getPlatformInfo()

    // 检测Electron环境
    if ((window as any).require || (window as any).electron) {
      this.adapterType = CameraAdapterType.ELECTRON
      return
    }

    // 检测Cordova环境
    if ((window as any).cordova || (window as any).PhoneGap) {
      this.adapterType = CameraAdapterType.CORDOVA
      return
    }

    // 检测WebRTC支持
    if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
      this.adapterType = CameraAdapterType.WEBRTC
      return
    }

    // 降级到传统API
    if ((navigator as any).getUserMedia || 
        (navigator as any).webkitGetUserMedia || 
        (navigator as any).mozGetUserMedia) {
      this.adapterType = CameraAdapterType.LEGACY
      return
    }

    this.adapterType = CameraAdapterType.UNKNOWN
  }

  /**
   * 检测可用设备
   */
  private async detectDevices(): Promise<void> {
    try {
      if (!navigator.mediaDevices?.enumerateDevices) {
        this.availableDevices = []
        return
      }

      const devices = await navigator.mediaDevices.enumerateDevices()
      const videoDevices = devices.filter(device => device.kind === 'videoinput')

      this.availableDevices = await Promise.all(
        videoDevices.map(async (device) => {
          const cameraDevice: CameraDevice = {
            deviceId: device.deviceId,
            groupId: device.groupId,
            kind: device.kind,
            label: device.label || `摄像头 ${videoDevices.indexOf(device) + 1}`,
            facing: this.detectFacingMode(device.label)
          }

          // 尝试获取设备能力
          try {
            const stream = await navigator.mediaDevices.getUserMedia({
              video: { deviceId: device.deviceId }
            })
            const track = stream.getVideoTracks()[0]
            cameraDevice.capabilities = track.getCapabilities()
            
            // 获取分辨率信息
            const settings = track.getSettings()
            if (settings.width && settings.height) {
              cameraDevice.resolution = {
                width: settings.width,
                height: settings.height
              }
            }

            track.stop()
            stream.getTracks().forEach(track => track.stop())
          } catch (error) {
            // 无法获取设备能力，跳过
          }

          return cameraDevice
        })
      )

      this.notifyDeviceChange()
    } catch (error) {
      throw this.createError(
        CAMERA_ERROR_CODES.DEVICE_NOT_FOUND,
        '无法检测摄像头设备',
        error
      )
    }
  }

  /**
   * 检测摄像头朝向
   */
  private detectFacingMode(label: string): CameraFacingMode {
    const lowerLabel = label.toLowerCase()
    
    if (lowerLabel.includes('front') || lowerLabel.includes('user') || lowerLabel.includes('内置')) {
      return CameraFacingMode.USER
    }
    if (lowerLabel.includes('back') || lowerLabel.includes('rear') || lowerLabel.includes('environment')) {
      return CameraFacingMode.ENVIRONMENT
    }
    if (lowerLabel.includes('left')) {
      return CameraFacingMode.LEFT
    }
    if (lowerLabel.includes('right')) {
      return CameraFacingMode.RIGHT
    }

    // 默认根据设备类型推测
    const platform = platformDetector.getPlatformInfo()
    return platform.device === DeviceType.MOBILE ? CameraFacingMode.USER : CameraFacingMode.ENVIRONMENT
  }

  /**
   * 生成摄像头能力信息
   */
  private async generateCapabilities(): Promise<void> {
    const platform = platformDetector.getPlatformInfo()
    const config = platformDetector.getPlatformConfig()

    // 获取支持的约束
    const supportedConstraints = navigator.mediaDevices?.getSupportedConstraints() || {}

    // 计算最大分辨率
    let maxResolution = { width: 1920, height: 1080 }
    if (this.availableDevices.length > 0) {
      const resolutions = this.availableDevices
        .filter(device => device.resolution)
        .map(device => device.resolution!)
      
      if (resolutions.length > 0) {
        maxResolution = {
          width: Math.max(...resolutions.map(r => r.width)),
          height: Math.max(...resolutions.map(r => r.height))
        }
      }
    }

    // 检测功能支持
    const features = {
      zoom: supportedConstraints.zoom || false,
      focus: supportedConstraints.focusMode || false,
      exposure: supportedConstraints.exposureMode || false,
      whiteBalance: supportedConstraints.whiteBalanceMode || false,
      torch: supportedConstraints.torch || false,
      faceDetection: false, // 需要额外检测
      backgroundBlur: false // 需要额外检测
    }

    // 根据平台调整支持的质量
    let supportedQualities = [CameraQuality.LOW, CameraQuality.MEDIUM, CameraQuality.HIGH]
    if (platform.device === DeviceType.DESKTOP && maxResolution.width >= 3840) {
      supportedQualities.push(CameraQuality.ULTRA)
    }
    if (platform.device === DeviceType.MOBILE) {
      supportedQualities = [CameraQuality.LOW, CameraQuality.MEDIUM]
    }

    this.capabilities = {
      devices: this.availableDevices,
      supportedConstraints: Object.keys(supportedConstraints),
      supportedQualities,
      supportedFormats: config.camera.supportedFormats,
      maxResolution,
      features
    }
  }

  /**
   * 获取摄像头流
   */
  public async getStream(options: CameraStreamOptions): Promise<CameraStreamInfo> {
    if (!this.isInitialized) {
      await this.initialize()
    }

    try {
      // 构建约束条件
      const constraints = this.buildConstraints(options)
      
      // 尝试获取流
      let stream: MediaStream
      let device: CameraDevice | undefined

      try {
        stream = await this.requestStream(constraints)
        device = this.findDeviceByStream(stream)
      } catch (error) {
        if (options.fallback) {
          // 降级策略
          const fallbackConstraints = this.buildFallbackConstraints(options)
          stream = await this.requestStream(fallbackConstraints)
          device = this.findDeviceByStream(stream)
        } else {
          throw error
        }
      }

      if (!device) {
        device = this.availableDevices[0] || {
          deviceId: 'unknown',
          groupId: 'unknown',
          kind: 'videoinput',
          label: '未知摄像头'
        }
      }

      // 获取实际设置
      const track = stream.getVideoTracks()[0]
      const settings = track.getSettings()
      const capabilities = track.getCapabilities()

      // 停止当前流
      if (this.currentStream) {
        this.stopStream()
      }

      this.currentStream = stream
      this.notifyStreamChange(stream)

      return {
        stream,
        device,
        actualConstraints: constraints.video as MediaTrackConstraints,
        settings,
        capabilities
      }
    } catch (error) {
      const cameraError = this.handleStreamError(error, options)
      this.notifyError(cameraError)
      throw cameraError
    }
  }

  /**
   * 构建约束条件
   */
  private buildConstraints(options: CameraStreamOptions): MediaStreamConstraints {
    const platform = platformDetector.getPlatformInfo()
    const baseConstraints = QUALITY_PRESETS[options.quality]
    
    // 合并用户约束
    const videoConstraints: CameraConstraints = {
      ...baseConstraints,
      ...options.constraints,
      facingMode: options.facingMode
    }

    // 添加设备ID
    if (options.deviceId) {
      videoConstraints.deviceId = options.deviceId
    } else if (options.autoSwitch) {
      // 自动选择最佳设备
      const bestDevice = this.selectBestDevice(options.facingMode, options.quality)
      if (bestDevice) {
        videoConstraints.deviceId = bestDevice.deviceId
      }
    }

    // 平台特定优化
    if (platform.device === DeviceType.MOBILE) {
      // 移动设备优化
      videoConstraints.frameRate = { ideal: 24, max: 30 }
    }

    if (platform.browser === Browser.SAFARI) {
      // Safari特定优化
      delete videoConstraints.frameRate
    }

    return {
      video: videoConstraints,
      audio: false
    }
  }

  /**
   * 构建降级约束
   */
  private buildFallbackConstraints(options: CameraStreamOptions): MediaStreamConstraints {
    return {
      video: {
        width: { ideal: 640 },
        height: { ideal: 480 },
        frameRate: { ideal: 24 },
        facingMode: options.facingMode
      },
      audio: false
    }
  }

  /**
   * 选择最佳设备
   */
  private selectBestDevice(facingMode: CameraFacingMode, quality: CameraQuality): CameraDevice | null {
    if (this.availableDevices.length === 0) {
      return null
    }

    // 优先选择指定朝向的设备
    let candidates = this.availableDevices.filter(device => device.facing === facingMode)
    
    if (candidates.length === 0) {
      candidates = this.availableDevices
    }

    // 根据质量要求选择分辨率最合适的设备
    const targetRes = QUALITY_PRESETS[quality]
    const targetWidth = typeof targetRes.width === 'object' ? targetRes.width.ideal : targetRes.width
    const targetHeight = typeof targetRes.height === 'object' ? targetRes.height.ideal : targetRes.height

    if (targetWidth && targetHeight) {
      candidates.sort((a, b) => {
        const aRes = a.resolution || { width: 640, height: 480 }
        const bRes = b.resolution || { width: 640, height: 480 }
        
        const aDiff = Math.abs(aRes.width - targetWidth) + Math.abs(aRes.height - targetHeight)
        const bDiff = Math.abs(bRes.width - targetWidth) + Math.abs(bRes.height - targetHeight)
        
        return aDiff - bDiff
      })
    }

    return candidates[0]
  }

  /**
   * 请求摄像头流
   */
  private async requestStream(constraints: MediaStreamConstraints): Promise<MediaStream> {
    switch (this.adapterType) {
      case CameraAdapterType.WEBRTC:
        return await navigator.mediaDevices.getUserMedia(constraints)
      
      case CameraAdapterType.LEGACY:
        return await this.getLegacyStream(constraints)
      
      case CameraAdapterType.ELECTRON:
        return await this.getElectronStream(constraints)
      
      case CameraAdapterType.CORDOVA:
        return await this.getCordovaStream(constraints)
      
      default:
        throw this.createError(
          CAMERA_ERROR_CODES.NOT_SUPPORTED,
          '当前平台不支持摄像头访问'
        )
    }
  }

  /**
   * 传统API获取流
   */
  private async getLegacyStream(constraints: MediaStreamConstraints): Promise<MediaStream> {
    return new Promise((resolve, reject) => {
      const getUserMedia = (navigator as any).getUserMedia || 
                          (navigator as any).webkitGetUserMedia || 
                          (navigator as any).mozGetUserMedia

      if (!getUserMedia) {
        reject(this.createError(
          CAMERA_ERROR_CODES.NOT_SUPPORTED,
          '传统摄像头API不可用'
        ))
        return
      }

      getUserMedia.call(navigator, constraints, resolve, reject)
    })
  }

  /**
   * Electron环境获取流
   */
  private async getElectronStream(constraints: MediaStreamConstraints): Promise<MediaStream> {
    // Electron环境通常使用标准WebRTC API
    return await navigator.mediaDevices.getUserMedia(constraints)
  }

  /**
   * Cordova环境获取流
   */
  private async getCordovaStream(constraints: MediaStreamConstraints): Promise<MediaStream> {
    // Cordova环境需要特殊处理
    throw this.createError(
      CAMERA_ERROR_CODES.PLATFORM_NOT_SUPPORTED,
      'Cordova摄像头支持尚未实现'
    )
  }

  /**
   * 通过流查找设备
   */
  private findDeviceByStream(stream: MediaStream): CameraDevice | undefined {
    const track = stream.getVideoTracks()[0]
    if (!track) return undefined

    const settings = track.getSettings()
    if (!settings.deviceId) return undefined

    return this.availableDevices.find(device => device.deviceId === settings.deviceId)
  }

  /**
   * 处理流错误
   */
  private handleStreamError(error: any, options: CameraStreamOptions): CameraError {
    if (error.name === 'NotAllowedError') {
      return this.createError(
        CAMERA_ERROR_CODES.PERMISSION_DENIED,
        '用户拒绝了摄像头访问权限',
        error,
        '请在浏览器设置中允许摄像头访问'
      )
    }

    if (error.name === 'NotFoundError') {
      return this.createError(
        CAMERA_ERROR_CODES.DEVICE_NOT_FOUND,
        '未找到摄像头设备',
        error,
        '请检查摄像头是否已连接并正常工作'
      )
    }

    if (error.name === 'NotReadableError') {
      return this.createError(
        CAMERA_ERROR_CODES.DEVICE_BUSY,
        '摄像头设备被其他应用占用',
        error,
        '请关闭其他使用摄像头的应用程序'
      )
    }

    if (error.name === 'OverconstrainedError' || error.name === 'ConstraintNotSatisfiedError') {
      return this.createError(
        CAMERA_ERROR_CODES.CONSTRAINT_NOT_SATISFIED,
        '摄像头不支持请求的配置',
        error,
        '请尝试降低质量设置或使用其他摄像头'
      )
    }

    return this.createError(
      CAMERA_ERROR_CODES.STREAM_FAILED,
      '获取摄像头流失败',
      error
    )
  }

  /**
   * 停止当前流
   */
  public stopStream(): void {
    if (this.currentStream) {
      this.currentStream.getTracks().forEach(track => {
        track.stop()
      })
      this.currentStream = null
      this.notifyStreamChange(null)
    }
  }

  /**
   * 切换摄像头
   */
  public async switchCamera(deviceId?: string): Promise<CameraStreamInfo> {
    if (!this.currentStream) {
      throw this.createError(
        CAMERA_ERROR_CODES.STREAM_FAILED,
        '没有活动的摄像头流'
      )
    }

    // 确定目标设备
    let targetDevice: CameraDevice | undefined
    if (deviceId) {
      targetDevice = this.availableDevices.find(d => d.deviceId === deviceId)
    } else {
      // 切换到下一个设备
      const currentDevice = this.findDeviceByStream(this.currentStream)
      if (currentDevice) {
        const currentIndex = this.availableDevices.findIndex(d => d.deviceId === currentDevice.deviceId)
        const nextIndex = (currentIndex + 1) % this.availableDevices.length
        targetDevice = this.availableDevices[nextIndex]
      }
    }

    if (!targetDevice) {
      throw this.createError(
        CAMERA_ERROR_CODES.DEVICE_NOT_FOUND,
        '找不到目标摄像头设备'
      )
    }

    // 获取当前设置
    const currentTrack = this.currentStream.getVideoTracks()[0]
    const currentSettings = currentTrack.getSettings()

    // 创建新的流选项
    const options: CameraStreamOptions = {
      quality: this.getQualityFromSettings(currentSettings),
      facingMode: targetDevice.facing || CameraFacingMode.USER,
      deviceId: targetDevice.deviceId,
      autoSwitch: false,
      fallback: true
    }

    return await this.getStream(options)
  }

  /**
   * 从设置中获取质量等级
   */
  private getQualityFromSettings(settings: MediaTrackSettings): CameraQuality {
    const width = settings.width || 640
    const height = settings.height || 480

    if (width >= 1920 && height >= 1080) {
      return CameraQuality.HIGH
    }
    if (width >= 1280 && height >= 720) {
      return CameraQuality.MEDIUM
    }
    return CameraQuality.LOW
  }

  /**
   * 获取可用设备列表
   */
  public getAvailableDevices(): CameraDevice[] {
    return [...this.availableDevices]
  }

  /**
   * 获取摄像头能力
   */
  public getCapabilities(): CameraCapabilities | null {
    return this.capabilities
  }

  /**
   * 获取当前流
   */
  public getCurrentStream(): MediaStream | null {
    return this.currentStream
  }

  /**
   * 获取适配器类型
   */
  public getAdapterType(): CameraAdapterType {
    return this.adapterType
  }

  /**
   * 检查权限状态
   */
  public async checkPermission(): Promise<PermissionState> {
    if (!navigator.permissions) {
      return 'granted' // 假设已授权
    }

    try {
      const permission = await navigator.permissions.query({ name: 'camera' as PermissionName })
      return permission.state
    } catch (error) {
      return 'granted' // 降级处理
    }
  }

  /**
   * 请求权限
   */
  public async requestPermission(): Promise<boolean> {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ video: true })
      stream.getTracks().forEach(track => track.stop())
      return true
    } catch (error) {
      return false
    }
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 监听设备变化
    if (navigator.mediaDevices?.addEventListener) {
      navigator.mediaDevices.addEventListener('devicechange', () => {
        this.detectDevices().catch(error => {
          this.notifyError(this.createError(
            CAMERA_ERROR_CODES.DEVICE_NOT_FOUND,
            '设备检测失败',
            error
          ))
        })
      })
    }

    // 监听平台变化
    platformDetector.addListener(() => {
      this.detectAdapterType()
    })
  }

  /**
   * 添加事件监听器
   */
  public addEventListener<T extends keyof typeof this.listeners>(
    event: T,
    callback: typeof this.listeners[T][0]
  ): void {
    (this.listeners[event] as any[]).push(callback)
  }

  /**
   * 移除事件监听器
   */
  public removeEventListener<T extends keyof typeof this.listeners>(
    event: T,
    callback: typeof this.listeners[T][0]
  ): void {
    const listeners = this.listeners[event] as any[]
    const index = listeners.indexOf(callback)
    if (index > -1) {
      listeners.splice(index, 1)
    }
  }

  /**
   * 通知设备变化
   */
  private notifyDeviceChange(): void {
    this.listeners.deviceChange.forEach(callback => {
      try {
        callback(this.availableDevices)
      } catch (error) {
        console.error('Device change listener error:', error)
      }
    })
  }

  /**
   * 通知流变化
   */
  private notifyStreamChange(stream: MediaStream | null): void {
    this.listeners.streamChange.forEach(callback => {
      try {
        callback(stream)
      } catch (error) {
        console.error('Stream change listener error:', error)
      }
    })
  }

  /**
   * 通知错误
   */
  private notifyError(error: CameraError): void {
    this.listeners.error.forEach(callback => {
      try {
        callback(error)
      } catch (callbackError) {
        console.error('Error listener error:', callbackError)
      }
    })
  }

  /**
   * 创建错误对象
   */
  private createError(
    code: string,
    message: string,
    originalError?: any,
    suggestion?: string
  ): CameraError {
    const platform = platformDetector.getPlatformInfo()
    
    return {
      code,
      message,
      name: originalError?.name || 'CameraError',
      constraint: originalError?.constraint,
      platform: `${platform.os}-${platform.browser}`,
      suggestion
    }
  }

  /**
   * 获取诊断信息
   */
  public getDiagnosticInfo(): string {
    const platform = platformDetector.getPlatformInfo()
    
    return JSON.stringify({
      adapterType: this.adapterType,
      isInitialized: this.isInitialized,
      platform: platform,
      devices: this.availableDevices,
      capabilities: this.capabilities,
      currentStream: this.currentStream ? {
        id: this.currentStream.id,
        active: this.currentStream.active,
        tracks: this.currentStream.getTracks().map(track => ({
          kind: track.kind,
          label: track.label,
          enabled: track.enabled,
          settings: track.getSettings()
        }))
      } : null,
      timestamp: new Date().toISOString()
    }, null, 2)
  }

  /**
   * 释放资源
   */
  public dispose(): void {
    this.stopStream()
    this.listeners.deviceChange = []
    this.listeners.streamChange = []
    this.listeners.error = []
  }
}

// 导出单例实例
export const cameraAdapter = CameraAdapterService.getInstance()

// 便捷函数
export const initializeCamera = () => cameraAdapter.initialize()
export const getCameraStream = (options: CameraStreamOptions) => cameraAdapter.getStream(options)
export const stopCameraStream = () => cameraAdapter.stopStream()
export const switchCamera = (deviceId?: string) => cameraAdapter.switchCamera(deviceId)
export const getCameraDevices = () => cameraAdapter.getAvailableDevices()
export const getCameraCapabilities = () => cameraAdapter.getCapabilities()
export const checkCameraPermission = () => cameraAdapter.checkPermission()
export const requestCameraPermission = () => cameraAdapter.requestPermission() 