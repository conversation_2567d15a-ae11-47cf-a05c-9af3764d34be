import { PoseLandmark, PostureAnalysis, PoseLandmarkIndex, EnhancedPostureAnalysis } from '../types/pose'
import { defaultEnhancedAnalyzer, analyzePostureEnhanced } from './enhancedPoseAnalysis'

/**
 * 计算两点之间的角度（弧度）
 */
function calculateAngle(point1: PoseLandmark, vertex: PoseLandmark, point2: PoseLandmark): number {
  const vector1 = {
    x: point1.x - vertex.x,
    y: point1.y - vertex.y
  }
  const vector2 = {
    x: point2.x - vertex.x,
    y: point2.y - vertex.y
  }
  
  const dot = vector1.x * vector2.x + vector1.y * vector2.y
  const mag1 = Math.sqrt(vector1.x * vector1.x + vector1.y * vector1.y)
  const mag2 = Math.sqrt(vector2.x * vector2.x + vector2.y * vector2.y)
  
  return Math.acos(dot / (mag1 * mag2))
}

/**
 * 判断用户是否处于坐姿
 */
function isSeated(landmarks: PoseLandmark[]): boolean {
  if (!landmarks || landmarks.length < 33) return false
  
  const leftHip = landmarks[PoseLandmarkIndex.LEFT_HIP]
  const rightHip = landmarks[PoseLandmarkIndex.RIGHT_HIP]
  const leftKnee = landmarks[PoseLandmarkIndex.LEFT_KNEE]
  const rightKnee = landmarks[PoseLandmarkIndex.RIGHT_KNEE]
  
  // 检查关键点可见性
  const minVisibility = 0.5
  if ((leftHip.visibility ?? 0) < minVisibility || (rightHip.visibility ?? 0) < minVisibility ||
      (leftKnee.visibility ?? 0) < minVisibility || (rightKnee.visibility ?? 0) < minVisibility) {
    return false
  }
  
  // 计算髋部到膝盖的角度（坐着时应该接近90度）
  const leftHipKneeAngle = calculateAngle(
    { ...leftHip, y: leftHip.y - 0.1 },  // 假设的上身点
    leftHip,
    leftKnee
  )
  
  const rightHipKneeAngle = calculateAngle(
    { ...rightHip, y: rightHip.y - 0.1 },
    rightHip,
    rightKnee
  )
  
  // 坐姿的髋膝角度通常在60-120度之间
  const avgAngle = (leftHipKneeAngle + rightHipKneeAngle) / 2
  const angleInDegrees = avgAngle * 180 / Math.PI
  
  return angleInDegrees > 60 && angleInDegrees < 120
}

/**
 * 分析头部位置
 */
function analyzeHeadPosition(landmarks: PoseLandmark[]): 'center' | 'left' | 'right' | 'forward' | 'back' {
  const nose = landmarks[PoseLandmarkIndex.NOSE]
  const leftShoulder = landmarks[PoseLandmarkIndex.LEFT_SHOULDER]
  const rightShoulder = landmarks[PoseLandmarkIndex.RIGHT_SHOULDER]
  
  if ((nose.visibility ?? 0) < 0.5 || (leftShoulder.visibility ?? 0) < 0.5 || (rightShoulder.visibility ?? 0) < 0.5) {
    return 'center'
  }
  
  // 计算肩膀中心点
  const shoulderCenter = {
    x: (leftShoulder.x + rightShoulder.x) / 2,
    y: (leftShoulder.y + rightShoulder.y) / 2,
    z: (leftShoulder.z + rightShoulder.z) / 2
  }
  
  const headOffset = {
    x: nose.x - shoulderCenter.x,
    y: nose.y - shoulderCenter.y,
    z: nose.z - shoulderCenter.z
  }
  
  // 判断左右倾斜
  if (Math.abs(headOffset.x) > 0.05) {
    return headOffset.x > 0 ? 'right' : 'left'
  }
  
  // 判断前后倾斜
  if (Math.abs(headOffset.z) > 0.05) {
    return headOffset.z > 0 ? 'back' : 'forward'
  }
  
  return 'center'
}

/**
 * 计算肩膀平衡度
 */
function calculateShoulderBalance(landmarks: PoseLandmark[]): number {
  const leftShoulder = landmarks[PoseLandmarkIndex.LEFT_SHOULDER]
  const rightShoulder = landmarks[PoseLandmarkIndex.RIGHT_SHOULDER]
  
  if ((leftShoulder.visibility ?? 0) < 0.5 || (rightShoulder.visibility ?? 0) < 0.5) {
    return 0
  }
  
  // 计算肩膀高度差，正值表示右肩高，负值表示左肩高
  const heightDiff = rightShoulder.y - leftShoulder.y
  
  // 归一化到-1到1范围
  return Math.max(-1, Math.min(1, heightDiff * 10))
}

/**
 * 计算身体倾斜度
 */
function calculateBodyLean(landmarks: PoseLandmark[]): number {
  const leftShoulder = landmarks[PoseLandmarkIndex.LEFT_SHOULDER]
  const rightShoulder = landmarks[PoseLandmarkIndex.RIGHT_SHOULDER]
  const leftHip = landmarks[PoseLandmarkIndex.LEFT_HIP]
  const rightHip = landmarks[PoseLandmarkIndex.RIGHT_HIP]
  
  if ((leftShoulder.visibility ?? 0) < 0.5 || (rightShoulder.visibility ?? 0) < 0.5 ||
      (leftHip.visibility ?? 0) < 0.5 || (rightHip.visibility ?? 0) < 0.5) {
    return 0
  }
  
  // 计算肩膀和髋部的中心点
  const shoulderCenter = (leftShoulder.x + rightShoulder.x) / 2
  const hipCenter = (leftHip.x + rightHip.x) / 2
  
  // 计算身体倾斜角度
  const lean = shoulderCenter - hipCenter
  
  // 归一化到-1到1范围
  return Math.max(-1, Math.min(1, lean * 5))
}

/**
 * 计算专注分数
 */
function calculateFocusScore(
  headPosition: string,
  shoulderBalance: number,
  bodyLean: number,
  isSeated: boolean
): number {
  let score = 100
  
  // 坐姿基础分
  if (!isSeated) {
    score -= 30
  }
  
  // 头部位置扣分
  if (headPosition !== 'center') {
    score -= 20
  }
  
  // 肩膀不平衡扣分
  score -= Math.abs(shoulderBalance) * 15
  
  // 身体倾斜扣分
  score -= Math.abs(bodyLean) * 20
  
  return Math.max(0, Math.min(100, score))
}

/**
 * 生成姿态警告信息
 */
function generateWarnings(
  headPosition: string,
  shoulderBalance: number,
  bodyLean: number,
  isSeated: boolean
): string[] {
  const warnings: string[] = []
  
  if (!isSeated) {
    warnings.push('请保持坐姿进行学习')
  }
  
  if (headPosition === 'forward') {
    warnings.push('头部过于前倾，请调整坐姿')
  } else if (headPosition === 'left' || headPosition === 'right') {
    warnings.push('头部倾斜，请保持头部居中')
  }
  
  if (Math.abs(shoulderBalance) > 0.3) {
    warnings.push('肩膀不平衡，请调整坐姿')
  }
  
  if (Math.abs(bodyLean) > 0.3) {
    warnings.push('身体倾斜过度，请坐直')
  }
  
  return warnings
}

/**
 * 分析姿态数据（增强版）
 * 默认使用增强版算法，提供更准确和智能的分析
 */
export function analyzePose(landmarks: PoseLandmark[], useEnhanced: boolean = true): PostureAnalysis | EnhancedPostureAnalysis {
  if (useEnhanced) {
    return analyzePostureEnhanced(landmarks, defaultEnhancedAnalyzer)
  }
  
  // 回退到原有的简单算法
  return analyzePoseSimple(landmarks)
}

/**
 * 简单姿态分析（原有算法）
 * 保留作为回退选项
 */
export function analyzePoseSimple(landmarks: PoseLandmark[]): PostureAnalysis {
  if (!landmarks || landmarks.length < 33) {
    return {
      isSeated: false,
      isFocused: false,
      focusScore: 0,
      headPosition: 'center',
      shoulderBalance: 0,
      bodyLean: 0,
      warnings: ['无法检测到有效的姿态数据']
    }
  }
  
  const seated = isSeated(landmarks)
  const headPosition = analyzeHeadPosition(landmarks)
  const shoulderBalance = calculateShoulderBalance(landmarks)
  const bodyLean = calculateBodyLean(landmarks)
  const focusScore = calculateFocusScore(headPosition, shoulderBalance, bodyLean, seated)
  const warnings = generateWarnings(headPosition, shoulderBalance, bodyLean, seated)
  
  return {
    isSeated: seated,
    isFocused: focusScore > 70,
    focusScore,
    headPosition,
    shoulderBalance,
    bodyLean,
    warnings
  }
}

/**
 * 更新统计信息
 */
export function updateStatistics(
  currentStats: any,
  postureAnalysis: PostureAnalysis,
  sessionStartTime: number
): any {
  const now = Date.now()
  
  // 计算平均专注分数
  const averageFocusScore = 
    (currentStats.averageFocusScore * currentStats.totalDetections + postureAnalysis.focusScore) /
    (currentStats.totalDetections + 1)
  
  const newStats = {
    totalDetections: currentStats.totalDetections + 1,
    goodPostureCount: currentStats.goodPostureCount + (postureAnalysis.isFocused ? 1 : 0),
    sessionDuration: now - sessionStartTime,
    averageFocusScore
  }
  
  return newStats
} 