import { PoseLandmark, PoseLandmarkIndex } from '../types/pose'

/**
 * 环境条件类型
 */
export interface EnvironmentalConditions {
  lighting: {
    level: number          // 0-1，光线强度
    quality: 'poor' | 'fair' | 'good' | 'excellent'
    uniformity: number     // 光线均匀度
    shadows: boolean       // 是否有明显阴影
  }
  camera: {
    angle: number          // 摄像头角度偏差
    distance: number       // 距离评估
    stability: number      // 摄像头稳定性
    resolution: 'low' | 'medium' | 'high'
  }
  background: {
    complexity: number     // 背景复杂度
    contrast: number       // 与人体的对比度
    motion: boolean        // 背景是否有运动
  }
  pose: {
    visibility: number     // 整体可见性
    occlusion: number      // 遮挡程度
    clarity: number        // 姿态清晰度
  }
}

/**
 * 环境适应配置
 */
export interface EnvironmentalAdaptationConfig {
  // 光线适应
  lightingAdaptation: {
    enabled: boolean
    minLightLevel: number
    adaptationSpeed: number
    shadowDetectionEnabled: boolean
  }
  
  // 摄像头适应
  cameraAdaptation: {
    enabled: boolean
    angleToleranceRange: number
    distanceOptimalRange: [number, number]
    stabilityThreshold: number
  }
  
  // 背景适应
  backgroundAdaptation: {
    enabled: boolean
    complexityThreshold: number
    contrastThreshold: number
    motionDetectionEnabled: boolean
  }
  
  // 自动校准
  autoCalibration: {
    enabled: boolean
    calibrationPeriod: number
    minSamplesForCalibration: number
  }
}

/**
 * 环境补偿参数
 */
export interface EnvironmentalCompensation {
  visibilityThreshold: number
  angleThresholds: {
    head: { x: number; z: number }
    shoulder: number
    body: number
  }
  confidenceMultipliers: {
    lighting: number
    camera: number
    background: number
  }
  scoreAdjustments: {
    lightingBonus: number
    cameraAnglePenalty: number
    backgroundNoisePenalty: number
  }
}

/**
 * 高级环境适应分析器
 */
export class AdvancedEnvironmentalAnalyzer {
  private config: EnvironmentalAdaptationConfig
  private calibrationData: {
    lightingHistory: number[]
    cameraAngleHistory: number[]
    distanceHistory: number[]
    timestamps: number[]
  } = {
    lightingHistory: [],
    cameraAngleHistory: [],
    distanceHistory: [],
    timestamps: []
  }
  
  private baselineConditions: EnvironmentalConditions | null = null
  private adaptationFactors: EnvironmentalCompensation

  constructor(config: Partial<EnvironmentalAdaptationConfig> = {}) {
    this.config = {
      lightingAdaptation: {
        enabled: true,
        minLightLevel: 0.3,
        adaptationSpeed: 0.1,
        shadowDetectionEnabled: true
      },
      cameraAdaptation: {
        enabled: true,
        angleToleranceRange: 15, // 度
        distanceOptimalRange: [0.5, 2.0],
        stabilityThreshold: 0.05
      },
      backgroundAdaptation: {
        enabled: true,
        complexityThreshold: 0.7,
        contrastThreshold: 0.4,
        motionDetectionEnabled: true
      },
      autoCalibration: {
        enabled: true,
        calibrationPeriod: 30000, // 30秒
        minSamplesForCalibration: 20
      },
      ...config
    }

    // 初始化适应因子
    this.adaptationFactors = {
      visibilityThreshold: 0.6,
      angleThresholds: {
        head: { x: 0.05, z: 0.05 },
        shoulder: 0.3,
        body: 0.3
      },
      confidenceMultipliers: {
        lighting: 1.0,
        camera: 1.0,
        background: 1.0
      },
      scoreAdjustments: {
        lightingBonus: 0,
        cameraAnglePenalty: 0,
        backgroundNoisePenalty: 0
      }
    }
  }

  /**
   * 分析环境条件
   */
  public analyzeEnvironmentalConditions(landmarks: PoseLandmark[]): EnvironmentalConditions {
    const lighting = this.analyzeLightingConditions(landmarks)
    const camera = this.analyzeCameraConditions(landmarks)
    const background = this.analyzeBackgroundConditions(landmarks)
    const pose = this.analyzePoseQuality(landmarks)

    const conditions: EnvironmentalConditions = {
      lighting,
      camera,
      background,
      pose
    }

    // 更新校准数据
    this.updateCalibrationData(conditions)

    return conditions
  }

  /**
   * 分析光线条件
   */
  private analyzeLightingConditions(landmarks: PoseLandmark[]): EnvironmentalConditions['lighting'] {
    // 计算平均可见性作为光线强度指标
    const visibilities = landmarks
      .filter(point => point.visibility !== undefined)
      .map(point => point.visibility!)
    
    const avgVisibility = visibilities.length > 0 ? 
      visibilities.reduce((sum, v) => sum + v, 0) / visibilities.length : 0

    // 计算可见性方差作为光线均匀度指标
    const variance = visibilities.length > 0 ?
      visibilities.reduce((sum, v) => sum + Math.pow(v - avgVisibility, 2), 0) / visibilities.length : 0
    const uniformity = Math.max(0, 1 - Math.sqrt(variance))

    // 检测阴影（通过面部关键点可见性差异）
    const facePoints = [
      landmarks[PoseLandmarkIndex.NOSE],
      landmarks[PoseLandmarkIndex.LEFT_EYE],
      landmarks[PoseLandmarkIndex.RIGHT_EYE],
      landmarks[PoseLandmarkIndex.MOUTH_LEFT],
      landmarks[PoseLandmarkIndex.MOUTH_RIGHT]
    ].filter(point => point && point.visibility !== undefined)

    const faceVisibilityVariance = facePoints.length > 1 ?
      facePoints.reduce((sum, point) => {
        const diff = (point.visibility! - avgVisibility)
        return sum + diff * diff
      }, 0) / facePoints.length : 0

    const shadows = faceVisibilityVariance > 0.1

    // 确定光线质量等级
    let quality: 'poor' | 'fair' | 'good' | 'excellent'
    if (avgVisibility < 0.3) quality = 'poor'
    else if (avgVisibility < 0.6) quality = 'fair'
    else if (avgVisibility < 0.8) quality = 'good'
    else quality = 'excellent'

    return {
      level: avgVisibility,
      quality,
      uniformity,
      shadows
    }
  }

  /**
   * 分析摄像头条件
   */
  private analyzeCameraConditions(landmarks: PoseLandmark[]): EnvironmentalConditions['camera'] {
    const leftShoulder = landmarks[PoseLandmarkIndex.LEFT_SHOULDER]
    const rightShoulder = landmarks[PoseLandmarkIndex.RIGHT_SHOULDER]
    const nose = landmarks[PoseLandmarkIndex.NOSE]

    if (!leftShoulder || !rightShoulder || !nose) {
      return {
        angle: 0,
        distance: 1,
        stability: 0,
        resolution: 'low'
      }
    }

    // 计算摄像头角度（基于肩膀宽度和对称性）
    const shoulderWidth = Math.abs(leftShoulder.x - rightShoulder.x)
    const shoulderCenter = (leftShoulder.x + rightShoulder.x) / 2
    const shoulderSymmetry = Math.abs(shoulderCenter - 0.5) // 假设画面中心为0.5

    // 角度估算（简化模型）
    const angle = shoulderSymmetry * 90 // 转换为度数

    // 距离估算（基于肩膀宽度和Z深度）
    const avgZ = Math.abs((leftShoulder.z + rightShoulder.z) / 2)
    const distance = Math.max(0.1, Math.min(3.0, avgZ * 2 + shoulderWidth))

    // 稳定性（基于历史数据变化）
    let stability = 1.0
    if (this.calibrationData.cameraAngleHistory.length > 5) {
      const recentAngles = this.calibrationData.cameraAngleHistory.slice(-5)
      const angleVariance = recentAngles.reduce((sum, a) => {
        const diff = a - angle
        return sum + diff * diff
      }, 0) / recentAngles.length
      stability = Math.max(0, 1 - Math.sqrt(angleVariance) / 10)
    }

    // 分辨率估算（基于关键点精度）
    const avgVisibility = [leftShoulder, rightShoulder, nose]
      .reduce((sum, point) => sum + (point.visibility || 0), 0) / 3
    
    let resolution: 'low' | 'medium' | 'high'
    if (avgVisibility < 0.5) resolution = 'low'
    else if (avgVisibility < 0.8) resolution = 'medium'
    else resolution = 'high'

    return { angle, distance, stability, resolution }
  }

  /**
   * 分析背景条件
   */
  private analyzeBackgroundConditions(landmarks: PoseLandmark[]): EnvironmentalConditions['background'] {
    // 通过关键点检测质量推断背景复杂度
    const validPoints = landmarks.filter(point => (point.visibility || 0) > 0.3)
    const detectionRate = validPoints.length / landmarks.length

    // 背景复杂度（检测率越低，背景越复杂）
    const complexity = Math.max(0, 1 - detectionRate)

    // 对比度（通过可见性分布推断）
    const visibilities = landmarks.map(point => point.visibility || 0)
    const avgVisibility = visibilities.reduce((sum, v) => sum + v, 0) / visibilities.length
    const contrast = avgVisibility // 简化模型

    // 运动检测（基于连续帧的变化）
    const motion = false // 需要多帧数据，这里简化

    return { complexity, contrast, motion }
  }

  /**
   * 分析姿态质量
   */
  private analyzePoseQuality(landmarks: PoseLandmark[]): EnvironmentalConditions['pose'] {
    const validPoints = landmarks.filter(point => (point.visibility || 0) > 0.5)
    const visibility = validPoints.length / landmarks.length

    // 遮挡程度（关键点缺失率）
    const keyPoints = [
      landmarks[PoseLandmarkIndex.NOSE],
      landmarks[PoseLandmarkIndex.LEFT_SHOULDER],
      landmarks[PoseLandmarkIndex.RIGHT_SHOULDER],
      landmarks[PoseLandmarkIndex.LEFT_HIP],
      landmarks[PoseLandmarkIndex.RIGHT_HIP]
    ]
    const visibleKeyPoints = keyPoints.filter(point => point && (point.visibility || 0) > 0.5)
    const occlusion = 1 - (visibleKeyPoints.length / keyPoints.length)

    // 清晰度（平均可见性）
    const clarity = landmarks.reduce((sum, point) => sum + (point.visibility || 0), 0) / landmarks.length

    return { visibility, occlusion, clarity }
  }

  /**
   * 更新校准数据
   */
  private updateCalibrationData(conditions: EnvironmentalConditions): void {
    const now = Date.now()
    
    this.calibrationData.lightingHistory.push(conditions.lighting.level)
    this.calibrationData.cameraAngleHistory.push(conditions.camera.angle)
    this.calibrationData.distanceHistory.push(conditions.camera.distance)
    this.calibrationData.timestamps.push(now)

    // 保持历史数据大小
    const maxHistory = 100
    if (this.calibrationData.lightingHistory.length > maxHistory) {
      this.calibrationData.lightingHistory.shift()
      this.calibrationData.cameraAngleHistory.shift()
      this.calibrationData.distanceHistory.shift()
      this.calibrationData.timestamps.shift()
    }

    // 自动校准
    if (this.config.autoCalibration.enabled) {
      this.performAutoCalibration()
    }
  }

  /**
   * 执行自动校准
   */
  private performAutoCalibration(): void {
    const { calibrationPeriod, minSamplesForCalibration } = this.config.autoCalibration
    const now = Date.now()
    
    if (this.calibrationData.timestamps.length < minSamplesForCalibration) {
      return
    }

    const recentData = this.calibrationData.timestamps
      .map((timestamp, index) => ({ timestamp, index }))
      .filter(item => now - item.timestamp < calibrationPeriod)

    if (recentData.length < minSamplesForCalibration) {
      return
    }

    // 计算基线条件
    const recentLighting = recentData.map(item => 
      this.calibrationData.lightingHistory[item.index]
    )
    const recentAngles = recentData.map(item => 
      this.calibrationData.cameraAngleHistory[item.index]
    )
    const recentDistances = recentData.map(item => 
      this.calibrationData.distanceHistory[item.index]
    )

    const avgLighting = recentLighting.reduce((sum, v) => sum + v, 0) / recentLighting.length
    const avgAngle = recentAngles.reduce((sum, v) => sum + v, 0) / recentAngles.length
    const avgDistance = recentDistances.reduce((sum, v) => sum + v, 0) / recentDistances.length

    // 更新适应因子
    this.updateAdaptationFactors(avgLighting, avgAngle, avgDistance)
  }

  /**
   * 更新适应因子
   */
  private updateAdaptationFactors(avgLighting: number, avgAngle: number, avgDistance: number): void {
    // 光线适应
    if (this.config.lightingAdaptation.enabled) {
      if (avgLighting < this.config.lightingAdaptation.minLightLevel) {
        // 光线不足时放宽阈值
        this.adaptationFactors.visibilityThreshold *= 0.8
        this.adaptationFactors.confidenceMultipliers.lighting = 0.7
      } else {
        // 光线充足时提高要求
        this.adaptationFactors.confidenceMultipliers.lighting = 1.2
        this.adaptationFactors.scoreAdjustments.lightingBonus = 5
      }
    }

    // 摄像头角度适应
    if (this.config.cameraAdaptation.enabled) {
      if (avgAngle > this.config.cameraAdaptation.angleToleranceRange) {
        // 角度偏差大时调整阈值
        const angleFactor = 1 + (avgAngle / 90) * 0.5
        this.adaptationFactors.angleThresholds.head.x *= angleFactor
        this.adaptationFactors.angleThresholds.head.z *= angleFactor
        this.adaptationFactors.scoreAdjustments.cameraAnglePenalty = avgAngle * 0.2
      }
    }

    // 距离适应
    const [minDist, maxDist] = this.config.cameraAdaptation.distanceOptimalRange
    if (avgDistance < minDist || avgDistance > maxDist) {
      const distanceFactor = avgDistance < minDist ? 
        minDist / avgDistance : avgDistance / maxDist
      this.adaptationFactors.angleThresholds.shoulder *= distanceFactor
      this.adaptationFactors.angleThresholds.body *= distanceFactor
    }
  }

  /**
   * 获取环境补偿参数
   */
  public getEnvironmentalCompensation(): EnvironmentalCompensation {
    return { ...this.adaptationFactors }
  }

  /**
   * 应用环境补偿到分数
   */
  public applyEnvironmentalCompensation(
    baseScore: number, 
    conditions: EnvironmentalConditions
  ): { adjustedScore: number; adjustments: any } {
    let adjustedScore = baseScore
    const adjustments: any = {}

    // 光线补偿
    if (conditions.lighting.level < 0.5) {
      const lightingPenalty = (0.5 - conditions.lighting.level) * 10
      adjustedScore -= lightingPenalty
      adjustments.lightingPenalty = lightingPenalty
    } else if (conditions.lighting.level > 0.8) {
      adjustedScore += this.adaptationFactors.scoreAdjustments.lightingBonus
      adjustments.lightingBonus = this.adaptationFactors.scoreAdjustments.lightingBonus
    }

    // 摄像头角度补偿
    if (conditions.camera.angle > 15) {
      adjustedScore -= this.adaptationFactors.scoreAdjustments.cameraAnglePenalty
      adjustments.cameraAnglePenalty = this.adaptationFactors.scoreAdjustments.cameraAnglePenalty
    }

    // 背景复杂度补偿
    if (conditions.background.complexity > 0.7) {
      const backgroundPenalty = (conditions.background.complexity - 0.7) * 15
      adjustedScore -= backgroundPenalty
      adjustments.backgroundPenalty = backgroundPenalty
    }

    // 姿态质量补偿
    if (conditions.pose.occlusion > 0.3) {
      const occlusionPenalty = conditions.pose.occlusion * 20
      adjustedScore -= occlusionPenalty
      adjustments.occlusionPenalty = occlusionPenalty
    }

    return {
      adjustedScore: Math.max(0, Math.min(100, adjustedScore)),
      adjustments
    }
  }

  /**
   * 重置校准数据
   */
  public resetCalibration(): void {
    this.calibrationData = {
      lightingHistory: [],
      cameraAngleHistory: [],
      distanceHistory: [],
      timestamps: []
    }
    this.baselineConditions = null
  }

  /**
   * 获取环境质量报告
   */
  public getEnvironmentalQualityReport(conditions: EnvironmentalConditions): {
    overall: 'poor' | 'fair' | 'good' | 'excellent'
    issues: string[]
    recommendations: string[]
  } {
    const issues: string[] = []
    const recommendations: string[] = []

    // 检查光线
    if (conditions.lighting.level < 0.4) {
      issues.push('光线不足')
      recommendations.push('增加照明或调整位置到光线更好的地方')
    }
    if (conditions.lighting.shadows) {
      issues.push('存在明显阴影')
      recommendations.push('调整光源位置以减少阴影')
    }

    // 检查摄像头
    if (conditions.camera.angle > 20) {
      issues.push('摄像头角度偏差较大')
      recommendations.push('调整摄像头位置使其正对用户')
    }
    if (conditions.camera.distance < 0.5 || conditions.camera.distance > 2.0) {
      issues.push('距离摄像头过近或过远')
      recommendations.push('调整座位距离，保持适当的拍摄距离')
    }

    // 检查背景
    if (conditions.background.complexity > 0.7) {
      issues.push('背景过于复杂')
      recommendations.push('选择简洁的背景或使用虚拟背景')
    }

    // 检查姿态质量
    if (conditions.pose.occlusion > 0.4) {
      issues.push('身体部分被遮挡')
      recommendations.push('调整坐姿确保关键部位可见')
    }

    // 综合评估
    const scores = [
      conditions.lighting.level,
      1 - conditions.camera.angle / 45, // 角度转换为0-1分数
      Math.max(0, 1 - Math.abs(conditions.camera.distance - 1.0)), // 距离评分
      1 - conditions.background.complexity,
      1 - conditions.pose.occlusion
    ]
    const avgScore = scores.reduce((sum, score) => sum + score, 0) / scores.length

    let overall: 'poor' | 'fair' | 'good' | 'excellent'
    if (avgScore < 0.3) overall = 'poor'
    else if (avgScore < 0.6) overall = 'fair'
    else if (avgScore < 0.8) overall = 'good'
    else overall = 'excellent'

    return { overall, issues, recommendations }
  }
} 