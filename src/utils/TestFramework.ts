import { platformDetector, PlatformInfo } from './PlatformDetector';
import { responsiveAdapter, ViewportInfo } from './ResponsiveAdapter';

// 测试类型定义
export enum TestType {
  UNIT = 'unit',
  INTEGRATION = 'integration',
  PLATFORM = 'platform',
  PERFORMANCE = 'performance',
  COMPATIBILITY = 'compatibility',
  VISUAL = 'visual',
  FUNCTIONAL = 'functional'
}

export enum TestStatus {
  PENDING = 'pending',
  RUNNING = 'running',
  PASSED = 'passed',
  FAILED = 'failed',
  SKIPPED = 'skipped',
  ERROR = 'error'
}

export enum TestPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

export interface TestCase {
  id: string;
  name: string;
  description: string;
  type: TestType;
  priority: TestPriority;
  platforms: string[];
  status: TestStatus;
  duration: number;
  error?: string;
  result?: any;
  metadata?: Record<string, any>;
}

export interface TestSuite {
  id: string;
  name: string;
  description: string;
  testCases: TestCase[];
  status: TestStatus;
  totalTests: number;
  passedTests: number;
  failedTests: number;
  skippedTests: number;
  duration: number;
}

export interface TestResult {
  testCase: TestCase;
  platform: PlatformInfo;
  viewport?: ViewportInfo;
  timestamp: Date;
  success: boolean;
  error?: string;
  details?: any;
}

export interface TestReport {
  suites: TestSuite[];
  totalTests: number;
  passedTests: number;
  failedTests: number;
  skippedTests: number;
  duration: number;
  platform: PlatformInfo;
  viewport?: ViewportInfo;
  timestamp: Date;
  coverage: number;
}

export interface TestConfig {
  timeout: number;
  retries: number;
  enablePerformanceMonitoring: boolean;
  enableScreenshots: boolean;
  enableLogs: boolean;
  parallel: boolean;
  maxParallelTests: number;
  filterByPlatform: boolean;
  skipSlowTests: boolean;
}

const DEFAULT_TEST_CONFIG: TestConfig = {
  timeout: 30000,
  retries: 2,
  enablePerformanceMonitoring: true,
  enableScreenshots: false,
  enableLogs: true,
  parallel: false,
  maxParallelTests: 4,
  filterByPlatform: true,
  skipSlowTests: false
};

export class TestFramework {
  private static instance: TestFramework;
  private config: TestConfig;
  private testSuites: Map<string, TestSuite> = new Map();
  private testResults: TestResult[] = [];
  private isRunning = false;
  private currentTest: TestCase | null = null;
  private performanceObserver: PerformanceObserver | null = null;
  private startTime = 0;

  private constructor() {
    this.config = { ...DEFAULT_TEST_CONFIG };
    this.setupPerformanceMonitoring();
  }

  public static getInstance(): TestFramework {
    if (!TestFramework.instance) {
      TestFramework.instance = new TestFramework();
    }
    return TestFramework.instance;
  }

  public async initialize(): Promise<void> {
    try {
      await this.setupBuiltinTests();
      console.log('测试框架初始化成功');
    } catch (error) {
      console.error('测试框架初始化失败:', error);
      throw error;
    }
  }

  private async setupBuiltinTests(): Promise<void> {
    // 平台检测测试套件
    const platformSuite: TestSuite = {
      id: 'platform-detection',
      name: '平台检测测试',
      description: '测试平台检测功能的准确性',
      testCases: [
        {
          id: 'platform-info',
          name: '平台信息检测',
          description: '验证能够正确检测操作系统、设备类型和浏览器',
          type: TestType.PLATFORM,
          priority: TestPriority.CRITICAL,
          platforms: ['all'],
          status: TestStatus.PENDING,
          duration: 0
        },
        {
          id: 'hardware-capabilities',
          name: '硬件能力检测',
          description: '验证硬件能力检测的准确性',
          type: TestType.PLATFORM,
          priority: TestPriority.HIGH,
          platforms: ['all'],
          status: TestStatus.PENDING,
          duration: 0
        }
      ],
      status: TestStatus.PENDING,
      totalTests: 0,
      passedTests: 0,
      failedTests: 0,
      skippedTests: 0,
      duration: 0
    };

    // 响应式测试套件
    const responsiveSuite: TestSuite = {
      id: 'responsive-adaptation',
      name: '响应式适配测试',
      description: '测试响应式界面适配功能',
      testCases: [
        {
          id: 'viewport-detection',
          name: '视口检测',
          description: '验证视口信息检测的准确性',
          type: TestType.INTEGRATION,
          priority: TestPriority.HIGH,
          platforms: ['all'],
          status: TestStatus.PENDING,
          duration: 0
        }
      ],
      status: TestStatus.PENDING,
      totalTests: 0,
      passedTests: 0,
      failedTests: 0,
      skippedTests: 0,
      duration: 0
    };

    this.registerTestSuite(platformSuite);
    this.registerTestSuite(responsiveSuite);
  }

  public registerTestSuite(suite: TestSuite): void {
    suite.totalTests = suite.testCases.length;
    this.testSuites.set(suite.id, suite);
  }

  public async runAllTests(): Promise<TestReport> {
    if (this.isRunning) {
      throw new Error('测试已在运行中');
    }

    this.isRunning = true;
    this.startTime = performance.now();
    this.testResults = [];

    try {
      const platform = platformDetector.getPlatformInfo();
      const viewport = responsiveAdapter.getViewportInfo();

      for (const suite of this.testSuites.values()) {
        await this.runTestSuite(suite);
      }

      return this.generateTestReport(platform, viewport || undefined);
    } finally {
      this.isRunning = false;
    }
  }

  public async runTestSuite(suite: TestSuite): Promise<TestSuite> {
    suite.status = TestStatus.RUNNING;
    suite.passedTests = 0;
    suite.failedTests = 0;
    suite.skippedTests = 0;
    const suiteStartTime = performance.now();

    const platform = platformDetector.getPlatformInfo();

    for (const testCase of suite.testCases) {
      if (this.shouldSkipTest(testCase, platform)) {
        testCase.status = TestStatus.SKIPPED;
        suite.skippedTests++;
        continue;
      }

      const result = await this.runTestCase(testCase);
      
      if (result.success) {
        testCase.status = TestStatus.PASSED;
        suite.passedTests++;
      } else {
        testCase.status = TestStatus.FAILED;
        testCase.error = result.error;
        suite.failedTests++;
      }

      this.testResults.push(result);
    }

    suite.duration = performance.now() - suiteStartTime;
    suite.status = suite.failedTests > 0 ? TestStatus.FAILED : TestStatus.PASSED;

    return suite;
  }

  public async runTestCase(testCase: TestCase): Promise<TestResult> {
    this.currentTest = testCase;
    testCase.status = TestStatus.RUNNING;
    
    const platform = platformDetector.getPlatformInfo();
    const viewport = responsiveAdapter.getViewportInfo();
    const startTime = performance.now();

    try {
      const result = await Promise.race([
        this.executeTest(testCase),
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('测试超时')), this.config.timeout)
        )
      ]);

      testCase.duration = performance.now() - startTime;

      return {
        testCase,
        platform,
        viewport: viewport || undefined,
        timestamp: new Date(),
        success: true,
        details: result
      };

    } catch (error) {
      testCase.duration = performance.now() - startTime;

      return {
        testCase,
        platform,
        viewport: viewport || undefined,
        timestamp: new Date(),
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    } finally {
      this.currentTest = null;
    }
  }

  private async executeTest(testCase: TestCase): Promise<any> {
    switch (testCase.id) {
      case 'platform-info':
        return this.testPlatformInfo();
      case 'hardware-capabilities':
        return this.testHardwareCapabilities();
      case 'viewport-detection':
        return this.testViewportDetection();
      default:
        throw new Error(`未知测试用例: ${testCase.id}`);
    }
  }

  private async testPlatformInfo(): Promise<any> {
    const platform = platformDetector.getPlatformInfo();
    
    if (!platform.os || !platform.device || !platform.browser) {
      throw new Error('平台信息缺失');
    }

    if (platform.version && typeof platform.version !== 'string') {
      throw new Error('版本信息格式错误');
    }

    return {
      platform,
      valid: true
    };
  }

  private async testHardwareCapabilities(): Promise<any> {
    const platform = platformDetector.getPlatformInfo();
    
    // 验证平台信息中的硬件相关信息
    if (!platform) {
      throw new Error('平台信息获取失败');
    }

    // 检查是否有基本的硬件信息
    const hasHardwareInfo = navigator.hardwareConcurrency || 
                           (navigator as any).deviceMemory ||
                           screen.width > 0;

    if (!hasHardwareInfo) {
      throw new Error('硬件能力检测失败');
    }

    return {
      platform,
      hardwareConcurrency: navigator.hardwareConcurrency,
      deviceMemory: (navigator as any).deviceMemory,
      screenResolution: `${screen.width}x${screen.height}`,
      valid: true
    };
  }

  private async testViewportDetection(): Promise<any> {
    const viewport = responsiveAdapter.getViewportInfo();
    
    if (!viewport) {
      throw new Error('视口信息检测失败');
    }

    if (viewport.width <= 0 || viewport.height <= 0) {
      throw new Error('视口尺寸检测失败');
    }

    if (!viewport.screenSize || !viewport.orientation) {
      throw new Error('视口属性检测失败');
    }

    return {
      viewport,
      valid: true
    };
  }

  private shouldSkipTest(testCase: TestCase, platform: PlatformInfo): boolean {
    if (!this.config.filterByPlatform) {
      return false;
    }

    if (testCase.platforms.length > 0 && !testCase.platforms.includes('all')) {
      const platformMatches = testCase.platforms.some(p => 
        p === platform.device || 
        p === platform.os || 
        p === platform.browser
      );
      
      if (!platformMatches) {
        return true;
      }
    }

    if (this.config.skipSlowTests && testCase.type === TestType.PERFORMANCE) {
      return true;
    }

    return false;
  }

  private setupPerformanceMonitoring(): void {
    if (typeof window === 'undefined' || !window.PerformanceObserver) {
      return;
    }

    try {
      this.performanceObserver = new PerformanceObserver((list) => {
        if (this.currentTest) {
          const entries = list.getEntries();
          this.currentTest.metadata = {
            ...this.currentTest.metadata,
            performanceEntries: entries.length
          };
        }
      });

      this.performanceObserver.observe({ entryTypes: ['measure', 'navigation'] });
    } catch (error) {
      console.warn('性能监控设置失败:', error);
    }
  }

  private generateTestReport(platform: PlatformInfo, viewport?: ViewportInfo): TestReport {
    const suites = Array.from(this.testSuites.values());
    const totalTests = suites.reduce((sum, suite) => sum + suite.totalTests, 0);
    const passedTests = suites.reduce((sum, suite) => sum + suite.passedTests, 0);
    const failedTests = suites.reduce((sum, suite) => sum + suite.failedTests, 0);
    const skippedTests = suites.reduce((sum, suite) => sum + suite.skippedTests, 0);
    const duration = performance.now() - this.startTime;
    const coverage = totalTests > 0 ? (passedTests / totalTests) * 100 : 0;

    return {
      suites,
      totalTests,
      passedTests,
      failedTests,
      skippedTests,
      duration,
      platform,
      viewport,
      timestamp: new Date(),
      coverage
    };
  }

  public getConfig(): TestConfig {
    return { ...this.config };
  }

  public updateConfig(updates: Partial<TestConfig>): void {
    this.config = { ...this.config, ...updates };
  }

  public getTestSuite(id: string): TestSuite | undefined {
    return this.testSuites.get(id);
  }

  public getAllTestSuites(): TestSuite[] {
    return Array.from(this.testSuites.values());
  }

  public getTestResults(): TestResult[] {
    return [...this.testResults];
  }

  public clearResults(): void {
    this.testResults = [];
    for (const suite of this.testSuites.values()) {
      suite.status = TestStatus.PENDING;
      suite.passedTests = 0;
      suite.failedTests = 0;
      suite.skippedTests = 0;
      suite.duration = 0;
      
      for (const testCase of suite.testCases) {
        testCase.status = TestStatus.PENDING;
        testCase.duration = 0;
        testCase.error = undefined;
      }
    }
  }

  public getDiagnosticInfo(): string {
    return JSON.stringify({
      config: this.config,
      isRunning: this.isRunning,
      currentTest: this.currentTest,
      totalSuites: this.testSuites.size,
      totalResults: this.testResults.length,
      timestamp: new Date().toISOString()
    }, null, 2);
  }

  public dispose(): void {
    if (this.performanceObserver) {
      this.performanceObserver.disconnect();
      this.performanceObserver = null;
    }
    
    this.testSuites.clear();
    this.testResults = [];
    this.isRunning = false;
    this.currentTest = null;
  }
}

export const testFramework = TestFramework.getInstance();

export const initializeTestFramework = () => testFramework.initialize();
export const runAllTests = () => testFramework.runAllTests();
export const runTestSuite = (suiteId: string) => {
  const suite = testFramework.getTestSuite(suiteId);
  return suite ? testFramework.runTestSuite(suite) : Promise.reject(new Error('测试套件不存在'));
};
export const getTestResults = () => testFramework.getTestResults();
export const clearTestResults = () => testFramework.clearResults();
export const getTestConfig = () => testFramework.getConfig();
export const updateTestConfig = (config: Partial<TestConfig>) => testFramework.updateConfig(config);
