import { 
  GameItem, 
  Quality, 
  QUALITY_CONFIGS, 
  ItemCategory, 
  AgriculturalVariety, 
  IndustrialVariety, 
  EquipmentType,
  AgriculturalItem,
  IndustrialItem,
  EquipmentItem,
  EquipmentSlot
} from '../types/enhanced-items'

// 道具数据库
const ITEM_DATABASE = {
  agricultural: {
    [AgriculturalVariety.CORN]: {
      name: '玉米',
      description: '主要的饲料作物和工业原料',
      category: ItemCategory.AGRICULTURAL,
      variety: AgriculturalVariety.CORN,
      icon: '🌽',
      baseValue: 2800,
      stackable: true,
      tradeable: true,
      production: { minDaily: 100, maxDaily: 120, currentRate: 1.0 },
      futuresCode: 'C',
      seasonalBonus: 1.2
    },
    [AgriculturalVariety.WHEAT]: {
      name: '小麦',
      description: '重要的粮食作物',
      category: ItemCategory.AGRICULTURAL,
      variety: AgriculturalVariety.WHEAT,
      icon: '🌾',
      baseValue: 3200,
      stackable: true,
      tradeable: true,
      production: { minDaily: 100, maxDaily: 120, currentRate: 1.0 },
      futuresCode: 'WH',
      seasonalBonus: 1.1
    },
    [AgriculturalVariety.SOYBEAN]: {
      name: '大豆',
      description: '重要的油料和蛋白质作物',
      category: ItemCategory.AGRICULTURAL,
      variety: AgriculturalVariety.SOYBEAN,
      icon: '🫘',
      baseValue: 4200,
      stackable: true,
      tradeable: true,
      production: { minDaily: 100, maxDaily: 120, currentRate: 1.0 },
      futuresCode: 'A',
      seasonalBonus: 1.15
    },
    [AgriculturalVariety.COTTON]: {
      name: '棉花',
      description: '重要的纺织原料',
      category: ItemCategory.AGRICULTURAL,
      variety: AgriculturalVariety.COTTON,
      icon: '🌸',
      baseValue: 16000,
      stackable: true,
      tradeable: true,
      production: { minDaily: 100, maxDaily: 120, currentRate: 1.0 },
      futuresCode: 'CF'
    },
    [AgriculturalVariety.WHITE_SUGAR]: {
      name: '白糖',
      description: '重要的食品工业原料',
      category: ItemCategory.AGRICULTURAL,
      variety: AgriculturalVariety.WHITE_SUGAR,
      icon: '🍬',
      baseValue: 6500,
      stackable: true,
      tradeable: true,
      production: { minDaily: 100, maxDaily: 120, currentRate: 1.0 },
      futuresCode: 'SR'
    },
    [AgriculturalVariety.APPLE]: {
      name: '苹果',
      description: '优质水果期货品种',
      category: ItemCategory.AGRICULTURAL,
      variety: AgriculturalVariety.APPLE,
      icon: '🍎',
      baseValue: 12000,
      stackable: true,
      tradeable: true,
      production: { minDaily: 100, maxDaily: 120, currentRate: 1.0 },
      futuresCode: 'AP',
      seasonalBonus: 1.3
    },
    [AgriculturalVariety.LIVE_HOG]: {
      name: '生猪',
      description: '重要的肉类期货品种',
      category: ItemCategory.AGRICULTURAL,
      variety: AgriculturalVariety.LIVE_HOG,
      icon: '🐷',
      baseValue: 18000,
      stackable: true,
      tradeable: true,
      production: { minDaily: 100, maxDaily: 120, currentRate: 1.0 },
      futuresCode: 'LH'
    },
    // 油脂类
    [AgriculturalVariety.SOYBEAN_OIL]: {
      name: '豆油',
      description: '重要的植物油期货品种',
      category: ItemCategory.AGRICULTURAL,
      variety: AgriculturalVariety.SOYBEAN_OIL,
      icon: '🛢️',
      baseValue: 8500,
      stackable: true,
      tradeable: true,
      production: { minDaily: 100, maxDaily: 120, currentRate: 1.0 },
      futuresCode: 'Y'
    },
    [AgriculturalVariety.PALM_OIL]: {
      name: '棕榈油',
      description: '热带植物油期货品种',
      category: ItemCategory.AGRICULTURAL,
      variety: AgriculturalVariety.PALM_OIL,
      icon: '🌴',
      baseValue: 8200,
      stackable: true,
      tradeable: true,
      production: { minDaily: 100, maxDaily: 120, currentRate: 1.0 },
      futuresCode: 'P'
    },
    [AgriculturalVariety.RAPESEED_OIL]: {
      name: '菜籽油',
      description: '菜籽榨取的植物油',
      category: ItemCategory.AGRICULTURAL,
      variety: AgriculturalVariety.RAPESEED_OIL,
      icon: '🌻',
      baseValue: 9200,
      stackable: true,
      tradeable: true,
      production: { minDaily: 100, maxDaily: 120, currentRate: 1.0 },
      futuresCode: 'OI'
    },
    [AgriculturalVariety.SOYBEAN_MEAL]: {
      name: '豆粕',
      description: '大豆榨油后的副产品',
      category: ItemCategory.AGRICULTURAL,
      variety: AgriculturalVariety.SOYBEAN_MEAL,
      icon: '🧄',
      baseValue: 3800,
      stackable: true,
      tradeable: true,
      production: { minDaily: 100, maxDaily: 120, currentRate: 1.0 },
      futuresCode: 'M'
    },
    [AgriculturalVariety.RED_DATES]: {
      name: '红枣',
      description: '中国传统干果期货品种',
      category: ItemCategory.AGRICULTURAL,
      variety: AgriculturalVariety.RED_DATES,
      icon: '🫐',
      baseValue: 11000,
      stackable: true,
      tradeable: true,
      production: { minDaily: 100, maxDaily: 120, currentRate: 1.0 },
      futuresCode: 'CJ',
      seasonalBonus: 1.4
    }
  },
  industrial: {
    [IndustrialVariety.COPPER]: {
      name: '铜',
      description: '重要的工业金属',
      category: ItemCategory.INDUSTRIAL,
      variety: IndustrialVariety.COPPER,
      icon: '🔶',
      baseValue: 68000,
      stackable: true,
      tradeable: true,
      properties: { durability: 85, efficiency: 90, capacity: 100 },
      futuresCode: 'CU',
      industrialType: 'metal' as const
    },
    [IndustrialVariety.ALUMINUM]: {
      name: '铝',
      description: '轻金属工业原料',
      category: ItemCategory.INDUSTRIAL,
      variety: IndustrialVariety.ALUMINUM,
      icon: '⚪',
      baseValue: 19000,
      stackable: true,
      tradeable: true,
      properties: { durability: 70, efficiency: 85, capacity: 90 },
      futuresCode: 'AL',
      industrialType: 'metal' as const
    },
    [IndustrialVariety.GOLD]: {
      name: '黄金',
      description: '贵金属投资品种',
      category: ItemCategory.INDUSTRIAL,
      variety: IndustrialVariety.GOLD,
      icon: '🏆',
      baseValue: 450000,
      stackable: true,
      tradeable: true,
      properties: { durability: 100, efficiency: 95, capacity: 100 },
      futuresCode: 'AU',
      industrialType: 'metal' as const
    },
    [IndustrialVariety.SILVER]: {
      name: '白银',
      description: '工业和投资兼具的贵金属',
      category: ItemCategory.INDUSTRIAL,
      variety: IndustrialVariety.SILVER,
      icon: '🥈',
      baseValue: 5200,
      stackable: true,
      tradeable: true,
      properties: { durability: 90, efficiency: 88, capacity: 95 },
      futuresCode: 'AG',
      industrialType: 'metal' as const
    },
    [IndustrialVariety.CRUDE_OIL]: {
      name: '原油',
      description: '重要的能源期货',
      category: ItemCategory.INDUSTRIAL,
      variety: IndustrialVariety.CRUDE_OIL,
      icon: '🛢️',
      baseValue: 520,
      stackable: true,
      tradeable: true,
      properties: { durability: 80, efficiency: 100, capacity: 100 },
      futuresCode: 'SC',
      industrialType: 'energy' as const
    },
    // 其他有色金属
    [IndustrialVariety.LEAD]: {
      name: '铅',
      description: '重要的工业金属',
      category: ItemCategory.INDUSTRIAL,
      variety: IndustrialVariety.LEAD,
      icon: '🔘',
      baseValue: 15200,
      stackable: true,
      tradeable: true,
      properties: { durability: 75, efficiency: 80, capacity: 85 },
      futuresCode: 'PB',
      industrialType: 'metal' as const
    },
    [IndustrialVariety.ZINC]: {
      name: '锌',
      description: '防腐镀层金属',
      category: ItemCategory.INDUSTRIAL,
      variety: IndustrialVariety.ZINC,
      icon: '⚫',
      baseValue: 24000,
      stackable: true,
      tradeable: true,
      properties: { durability: 80, efficiency: 85, capacity: 90 },
      futuresCode: 'ZN',
      industrialType: 'metal' as const
    },
    [IndustrialVariety.NICKEL]: {
      name: '镍',
      description: '不锈钢重要原料',
      category: ItemCategory.INDUSTRIAL,
      variety: IndustrialVariety.NICKEL,
      icon: '🔸',
      baseValue: 110000,
      stackable: true,
      tradeable: true,
      properties: { durability: 90, efficiency: 92, capacity: 95 },
      futuresCode: 'NI',
      industrialType: 'metal' as const
    },
    [IndustrialVariety.TIN]: {
      name: '锡',
      description: '电子工业用金属',
      category: ItemCategory.INDUSTRIAL,
      variety: IndustrialVariety.TIN,
      icon: '🟡',
      baseValue: 210000,
      stackable: true,
      tradeable: true,
      properties: { durability: 85, efficiency: 88, capacity: 90 },
      futuresCode: 'SN',
      industrialType: 'metal' as const
    },
    // 黑色金属
    [IndustrialVariety.REBAR]: {
      name: '螺纹钢',
      description: '建筑钢材期货',
      category: ItemCategory.INDUSTRIAL,
      variety: IndustrialVariety.REBAR,
      icon: '🔩',
      baseValue: 4200,
      stackable: true,
      tradeable: true,
      properties: { durability: 100, efficiency: 95, capacity: 100 },
      futuresCode: 'RB',
      industrialType: 'metal' as const
    },
    [IndustrialVariety.HOT_ROLLED_COIL]: {
      name: '热轧卷板',
      description: '钢铁板材期货',
      category: ItemCategory.INDUSTRIAL,
      variety: IndustrialVariety.HOT_ROLLED_COIL,
      icon: '📜',
      baseValue: 4100,
      stackable: true,
      tradeable: true,
      properties: { durability: 95, efficiency: 100, capacity: 95 },
      futuresCode: 'HC',
      industrialType: 'metal' as const
    },
    // 建材
    [IndustrialVariety.GLASS]: {
      name: '玻璃',
      description: '建筑装饰材料',
      category: ItemCategory.INDUSTRIAL,
      variety: IndustrialVariety.GLASS,
      icon: '🔆',
      baseValue: 1800,
      stackable: true,
      tradeable: true,
      properties: { durability: 60, efficiency: 75, capacity: 80 },
      futuresCode: 'FG',
      industrialType: 'chemical' as const
    },
    // 能源化工
    [IndustrialVariety.THERMAL_COAL]: {
      name: '动力煤',
      description: '火力发电燃料',
      category: ItemCategory.INDUSTRIAL,
      variety: IndustrialVariety.THERMAL_COAL,
      icon: '⚫',
      baseValue: 820,
      stackable: true,
      tradeable: true,
      properties: { durability: 70, efficiency: 95, capacity: 100 },
      futuresCode: 'TC',
      industrialType: 'energy' as const
    },
    [IndustrialVariety.COKE]: {
      name: '焦炭',
      description: '钢铁冶炼燃料',
      category: ItemCategory.INDUSTRIAL,
      variety: IndustrialVariety.COKE,
      icon: '🟫',
      baseValue: 2200,
      stackable: true,
      tradeable: true,
      properties: { durability: 80, efficiency: 100, capacity: 95 },
      futuresCode: 'J',
      industrialType: 'energy' as const
    },
    [IndustrialVariety.COKING_COAL]: {
      name: '焦煤',
      description: '焦炭生产原料',
      category: ItemCategory.INDUSTRIAL,
      variety: IndustrialVariety.COKING_COAL,
      icon: '⬛',
      baseValue: 1680,
      stackable: true,
      tradeable: true,
      properties: { durability: 75, efficiency: 90, capacity: 90 },
      futuresCode: 'JM',
      industrialType: 'energy' as const
    },
    [IndustrialVariety.ASPHALT]: {
      name: '沥青',
      description: '道路建设材料',
      category: ItemCategory.INDUSTRIAL,
      variety: IndustrialVariety.ASPHALT,
      icon: '🛣️',
      baseValue: 3800,
      stackable: true,
      tradeable: true,
      properties: { durability: 85, efficiency: 90, capacity: 85 },
      futuresCode: 'BU',
      industrialType: 'chemical' as const
    },
    [IndustrialVariety.LPG]: {
      name: '液化石油气',
      description: '清洁能源期货',
      category: ItemCategory.INDUSTRIAL,
      variety: IndustrialVariety.LPG,
      icon: '🛢️',
      baseValue: 4200,
      stackable: true,
      tradeable: true,
      properties: { durability: 80, efficiency: 100, capacity: 100 },
      futuresCode: 'PG',
      industrialType: 'energy' as const
    }
  },
  equipment: {
    [EquipmentType.FOCUS_GLASSES]: {
      name: '聚焦眼镜',
      description: '提升专注力的智能眼镜',
      category: ItemCategory.EQUIPMENT,
      equipmentType: EquipmentType.FOCUS_GLASSES,
      icon: '👓',
      baseValue: 2000,
      stackable: false,
      tradeable: true,
      attributes: { focusBonus: 0, productionBonus: 0, qualityBonus: 0, duration: 24 },
      slot: EquipmentSlot.HEAD
    },
    [EquipmentType.FOCUS_HEADPHONES]: {
      name: '专注耳机',
      description: '降噪专注的智能耳机',
      category: ItemCategory.EQUIPMENT,
      equipmentType: EquipmentType.FOCUS_HEADPHONES,
      icon: '🎧',
      baseValue: 1800,
      stackable: false,
      tradeable: true,
      attributes: { focusBonus: 0, productionBonus: 0, qualityBonus: 0, duration: 24 },
      slot: EquipmentSlot.HEAD
    },
    [EquipmentType.ENERGY_BRACELET]: {
      name: '能量手环',
      description: '监测活力的智能手环',
      category: ItemCategory.EQUIPMENT,
      equipmentType: EquipmentType.ENERGY_BRACELET,
      icon: '⌚',
      baseValue: 1500,
      stackable: false,
      tradeable: true,
      attributes: { focusBonus: 0, productionBonus: 0, qualityBonus: 0, duration: 24 },
      slot: EquipmentSlot.WRIST
    },
    [EquipmentType.DISCIPLINE_CLOCK]: {
      name: '自律时钟',
      description: '帮助管理时间的智能时钟',
      category: ItemCategory.EQUIPMENT,
      equipmentType: EquipmentType.DISCIPLINE_CLOCK,
      icon: '⏰',
      baseValue: 2200,
      stackable: false,
      tradeable: true,
      attributes: { focusBonus: 0, productionBonus: 0, qualityBonus: 0, duration: 24 },
      slot: EquipmentSlot.DESK
    }
  }
}

// 道具工厂类
export class ItemFactory {
  /**
   * 创建道具实例
   */
  static createItem(
    variety: AgriculturalVariety | IndustrialVariety | EquipmentType,
    quality: Quality = Quality.COMMON,
    quantity: number = 1
  ): GameItem {
    const baseItem = this.getBaseItemData(variety)
    if (!baseItem) {
      throw new Error(`Unknown item variety: ${variety}`)
    }

    const qualityConfig = QUALITY_CONFIGS[quality]
    const id = `${variety}_${quality}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

    const item = {
      ...baseItem,
      id,
      quality,
      obtainedAt: Date.now()
    } as GameItem

    // 根据品质调整属性
    this.updateItemAttributes(item, quality)
    
    return item
  }

  /**
   * 批量创建道具
   */
  static createItems(
    variety: AgriculturalVariety | IndustrialVariety | EquipmentType,
    quality: Quality,
    count: number
  ): GameItem[] {
    const items: GameItem[] = []
    for (let i = 0; i < count; i++) {
      items.push(this.createItem(variety, quality))
    }
    return items
  }

  /**
   * 随机创建道具
   */
  static createRandomItem(category?: ItemCategory): GameItem {
    const randomQuality = this.getRandomQuality()
    
    if (category) {
      const variety = this.getRandomVariety(category)
      return this.createItem(variety, randomQuality)
    } else {
      const randomCategory = this.getRandomCategory()
      const variety = this.getRandomVariety(randomCategory)
      return this.createItem(variety, randomQuality)
    }
  }

  /**
   * 创建演示数据
   */
  static createDemoInventory(): GameItem[] {
    const items: GameItem[] = []
    
    // 农业产品
    items.push(this.createItem(AgriculturalVariety.CORN, Quality.COMMON))
    items.push(this.createItem(AgriculturalVariety.CORN, Quality.COMMON))
    items.push(this.createItem(AgriculturalVariety.WHEAT, Quality.GOOD))
    items.push(this.createItem(AgriculturalVariety.SOYBEAN, Quality.RARE))
    items.push(this.createItem(AgriculturalVariety.APPLE, Quality.EPIC))
    
    // 工业产品
    items.push(this.createItem(IndustrialVariety.COPPER, Quality.COMMON))
    items.push(this.createItem(IndustrialVariety.ALUMINUM, Quality.GOOD))
    items.push(this.createItem(IndustrialVariety.GOLD, Quality.LEGENDARY))
    
    // 装备
    items.push(this.createItem(EquipmentType.FOCUS_GLASSES, Quality.RARE))
    items.push(this.createItem(EquipmentType.ENERGY_BRACELET, Quality.GOOD))
    
    return items
  }

  /**
   * 获取基础道具数据
   */
  private static getBaseItemData(variety: AgriculturalVariety | IndustrialVariety | EquipmentType) {
    if (Object.values(AgriculturalVariety).includes(variety as AgriculturalVariety)) {
      return ITEM_DATABASE.agricultural[variety as AgriculturalVariety]
    } else if (Object.values(IndustrialVariety).includes(variety as IndustrialVariety)) {
      return ITEM_DATABASE.industrial[variety as IndustrialVariety]
    } else if (Object.values(EquipmentType).includes(variety as EquipmentType)) {
      return ITEM_DATABASE.equipment[variety as EquipmentType]
    }
    return null
  }

  /**
   * 更新道具属性根据品质
   */
  private static updateItemAttributes(item: GameItem, quality: Quality): void {
    const qualityConfig = QUALITY_CONFIGS[quality]

    if (item.category === ItemCategory.AGRICULTURAL) {
      const agriItem = item as AgriculturalItem
      const [min, max] = qualityConfig.productionRange
      agriItem.production.minDaily = min
      agriItem.production.maxDaily = max
      agriItem.production.currentRate = 1.0 + (qualityConfig.attributeBonus / 100)
    } else if (item.category === ItemCategory.INDUSTRIAL) {
      const indItem = item as IndustrialItem
      const bonus = qualityConfig.attributeBonus / 100
      const baseProps = indItem.properties
      indItem.properties = {
        durability: Math.round(baseProps.durability * (1 + bonus)),
        efficiency: Math.round(baseProps.efficiency * (1 + bonus)),
        capacity: Math.round(baseProps.capacity * (1 + bonus))
      }
    } else if (item.category === ItemCategory.EQUIPMENT) {
      const equipItem = item as EquipmentItem
      equipItem.attributes = {
        ...equipItem.attributes,
        focusBonus: qualityConfig.attributeBonus,
        productionBonus: qualityConfig.attributeBonus,
        qualityBonus: qualityConfig.attributeBonus
      }
    }
  }

  /**
   * 获取随机品质
   */
  private static getRandomQuality(): Quality {
    const weights = Object.entries(QUALITY_CONFIGS).map(([quality, config]) => ({
      quality: quality as Quality,
      weight: config.rarityWeight
    }))
    
    const totalWeight = weights.reduce((sum, item) => sum + item.weight, 0)
    let random = Math.random() * totalWeight
    
    for (const item of weights) {
      random -= item.weight
      if (random <= 0) {
        return item.quality
      }
    }
    
    return Quality.COMMON
  }

  /**
   * 获取随机分类
   */
  private static getRandomCategory(): ItemCategory {
    const categories = Object.values(ItemCategory)
    return categories[Math.floor(Math.random() * categories.length)]
  }

  /**
   * 获取随机品种
   */
  private static getRandomVariety(category: ItemCategory): AgriculturalVariety | IndustrialVariety | EquipmentType {
    switch (category) {
      case ItemCategory.AGRICULTURAL:
        const agriVarieties = Object.values(AgriculturalVariety)
        return agriVarieties[Math.floor(Math.random() * agriVarieties.length)]
      
      case ItemCategory.INDUSTRIAL:
        const indVarieties = Object.values(IndustrialVariety)
        return indVarieties[Math.floor(Math.random() * indVarieties.length)]
      
      case ItemCategory.EQUIPMENT:
        const equipTypes = Object.values(EquipmentType)
        return equipTypes[Math.floor(Math.random() * equipTypes.length)]
      
      default:
        throw new Error(`Unknown category: ${category}`)
    }
  }
}

// 导出便捷函数
export const createItem = ItemFactory.createItem.bind(ItemFactory)
export const createRandomItem = ItemFactory.createRandomItem.bind(ItemFactory)
export const createDemoInventory = ItemFactory.createDemoInventory.bind(ItemFactory) 