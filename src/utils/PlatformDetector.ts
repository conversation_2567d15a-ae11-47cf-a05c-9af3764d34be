// 平台类型定义
export enum OperatingSystem {
  WINDOWS = 'windows',
  MACOS = 'macos',
  LINUX = 'linux',
  IOS = 'ios',
  ANDROID = 'android',
  UNKNOWN = 'unknown'
}

export enum DeviceType {
  DESKTOP = 'desktop',
  TABLET = 'tablet',
  MOBILE = 'mobile',
  UNKNOWN = 'unknown'
}

export enum Browser {
  CHROME = 'chrome',
  FIREFOX = 'firefox',
  SAFARI = 'safari',
  EDGE = 'edge',
  IE = 'ie',
  OPERA = 'opera',
  UNKNOWN = 'unknown'
}

export enum Architecture {
  X86 = 'x86',
  X64 = 'x64',
  ARM = 'arm',
  ARM64 = 'arm64',
  UNKNOWN = 'unknown'
}

// 平台信息接口
export interface PlatformInfo {
  os: OperatingSystem
  device: DeviceType
  browser: Browser
  architecture: Architecture
  version: {
    os: string
    browser: string
  }
  capabilities: {
    touch: boolean
    webRTC: boolean
    webGL: boolean
    localStorage: boolean
    sessionStorage: boolean
    indexedDB: boolean
    webWorkers: boolean
    serviceWorkers: boolean
    notifications: boolean
    geolocation: boolean
    camera: boolean
    microphone: boolean
  }
  hardware: {
    cores: number
    memory: number // GB
    screen: {
      width: number
      height: number
      pixelRatio: number
      colorDepth: number
    }
  }
  network: {
    connection: string
    downlink?: number
    effectiveType?: string
  }
}

// 平台特定配置
export interface PlatformConfig {
  fileSystem: {
    separator: string
    homePath: string
    tempPath: string
  }
  camera: {
    preferredFormat: string
    supportedFormats: string[]
    defaultConstraints: MediaStreamConstraints
  }
  performance: {
    maxMemoryUsage: number // MB
    preferredFrameRate: number
    enableGPUAcceleration: boolean
  }
  ui: {
    scrollBehavior: 'smooth' | 'auto'
    animationDuration: number
    preferredFontSize: number
  }
}

export class PlatformDetector {
  private static instance: PlatformDetector
  private platformInfo: PlatformInfo | null = null
  private platformConfig: PlatformConfig | null = null
  private listeners: Array<(info: PlatformInfo) => void> = []

  private constructor() {
    this.detectPlatform()
    this.setupEventListeners()
  }

  public static getInstance(): PlatformDetector {
    if (!PlatformDetector.instance) {
      PlatformDetector.instance = new PlatformDetector()
    }
    return PlatformDetector.instance
  }

  /**
   * 获取平台信息
   */
  public getPlatformInfo(): PlatformInfo {
    if (!this.platformInfo) {
      this.detectPlatform()
    }
    return this.platformInfo!
  }

  /**
   * 获取平台配置
   */
  public getPlatformConfig(): PlatformConfig {
    if (!this.platformConfig) {
      this.generatePlatformConfig()
    }
    return this.platformConfig!
  }

  /**
   * 添加平台信息变化监听器
   */
  public addListener(callback: (info: PlatformInfo) => void): void {
    this.listeners.push(callback)
  }

  /**
   * 移除监听器
   */
  public removeListener(callback: (info: PlatformInfo) => void): void {
    const index = this.listeners.indexOf(callback)
    if (index > -1) {
      this.listeners.splice(index, 1)
    }
  }

  /**
   * 检测操作系统
   */
  private detectOperatingSystem(): OperatingSystem {
    const userAgent = navigator.userAgent.toLowerCase()
    const platform = navigator.platform?.toLowerCase() || ''

    // iOS检测
    if (/iphone|ipad|ipod/.test(userAgent) || (platform === 'macintel' && navigator.maxTouchPoints > 1)) {
      return OperatingSystem.IOS
    }

    // Android检测
    if (/android/.test(userAgent)) {
      return OperatingSystem.ANDROID
    }

    // Windows检测
    if (/win/.test(platform) || /windows/.test(userAgent)) {
      return OperatingSystem.WINDOWS
    }

    // macOS检测
    if (/mac/.test(platform) || /darwin/.test(userAgent)) {
      return OperatingSystem.MACOS
    }

    // Linux检测
    if (/linux/.test(platform) || /x11/.test(userAgent)) {
      return OperatingSystem.LINUX
    }

    return OperatingSystem.UNKNOWN
  }

  /**
   * 检测设备类型
   */
  private detectDeviceType(): DeviceType {
    const userAgent = navigator.userAgent.toLowerCase()
    
    // 移动设备检测
    if (/android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent)) {
      // 区分手机和平板
      if (/ipad|android(?!.*mobile)|tablet/i.test(userAgent)) {
        return DeviceType.TABLET
      }
      return DeviceType.MOBILE
    }

    // 基于屏幕尺寸的检测
    const width = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth
    if (width < 768) {
      return DeviceType.MOBILE
    } else if (width < 1024) {
      return DeviceType.TABLET
    }

    return DeviceType.DESKTOP
  }

  /**
   * 检测浏览器
   */
  private detectBrowser(): Browser {
    const userAgent = navigator.userAgent.toLowerCase()

    if (/edg\//.test(userAgent)) {
      return Browser.EDGE
    }
    if (/chrome\//.test(userAgent) && !/edg\//.test(userAgent)) {
      return Browser.CHROME
    }
    if (/firefox\//.test(userAgent)) {
      return Browser.FIREFOX
    }
    if (/safari\//.test(userAgent) && !/chrome\//.test(userAgent)) {
      return Browser.SAFARI
    }
    if (/trident\//.test(userAgent) || /msie/.test(userAgent)) {
      return Browser.IE
    }
    if (/opera\//.test(userAgent) || /opr\//.test(userAgent)) {
      return Browser.OPERA
    }

    return Browser.UNKNOWN
  }

  /**
   * 检测系统架构
   */
  private detectArchitecture(): Architecture {
    const userAgent = navigator.userAgent.toLowerCase()
    const platform = navigator.platform?.toLowerCase() || ''

    if (/arm64|aarch64/.test(userAgent) || /arm64/.test(platform)) {
      return Architecture.ARM64
    }
    if (/arm/.test(userAgent) || /arm/.test(platform)) {
      return Architecture.ARM
    }
    if (/x64|x86_64|amd64/.test(userAgent) || /win64|wow64/.test(userAgent)) {
      return Architecture.X64
    }
    if (/x86/.test(userAgent) || /i386|i686/.test(platform)) {
      return Architecture.X86
    }

    // 默认基于平台推测
    const os = this.detectOperatingSystem()
    if (os === OperatingSystem.IOS || os === OperatingSystem.ANDROID) {
      return Architecture.ARM64
    }

    return Architecture.UNKNOWN
  }

  /**
   * 检测版本信息
   */
  private detectVersions(): { os: string; browser: string } {
    const userAgent = navigator.userAgent

    // 操作系统版本
    let osVersion = 'Unknown'
    const os = this.detectOperatingSystem()
    
    switch (os) {
      case OperatingSystem.WINDOWS:
        const winMatch = userAgent.match(/Windows NT ([\d.]+)/)
        if (winMatch) osVersion = winMatch[1]
        break
      case OperatingSystem.MACOS:
        const macMatch = userAgent.match(/Mac OS X ([\d._]+)/)
        if (macMatch) osVersion = macMatch[1].replace(/_/g, '.')
        break
      case OperatingSystem.IOS:
        const iosMatch = userAgent.match(/OS ([\d._]+)/)
        if (iosMatch) osVersion = iosMatch[1].replace(/_/g, '.')
        break
      case OperatingSystem.ANDROID:
        const androidMatch = userAgent.match(/Android ([\d.]+)/)
        if (androidMatch) osVersion = androidMatch[1]
        break
    }

    // 浏览器版本
    let browserVersion = 'Unknown'
    const browser = this.detectBrowser()
    
    switch (browser) {
      case Browser.CHROME:
        const chromeMatch = userAgent.match(/Chrome\/([\d.]+)/)
        if (chromeMatch) browserVersion = chromeMatch[1]
        break
      case Browser.FIREFOX:
        const firefoxMatch = userAgent.match(/Firefox\/([\d.]+)/)
        if (firefoxMatch) browserVersion = firefoxMatch[1]
        break
      case Browser.SAFARI:
        const safariMatch = userAgent.match(/Version\/([\d.]+)/)
        if (safariMatch) browserVersion = safariMatch[1]
        break
      case Browser.EDGE:
        const edgeMatch = userAgent.match(/Edg\/([\d.]+)/)
        if (edgeMatch) browserVersion = edgeMatch[1]
        break
    }

    return { os: osVersion, browser: browserVersion }
  }

  /**
   * 检测浏览器功能支持
   */
  private detectCapabilities(): PlatformInfo['capabilities'] {
    return {
      touch: 'ontouchstart' in window || navigator.maxTouchPoints > 0,
      webRTC: !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia),
      webGL: !!window.WebGLRenderingContext,
      localStorage: !!window.localStorage,
      sessionStorage: !!window.sessionStorage,
      indexedDB: !!window.indexedDB,
      webWorkers: !!window.Worker,
      serviceWorkers: 'serviceWorker' in navigator,
      notifications: 'Notification' in window,
      geolocation: 'geolocation' in navigator,
      camera: !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia),
      microphone: !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia)
    }
  }

  /**
   * 检测硬件信息
   */
  private detectHardware(): PlatformInfo['hardware'] {
    const screen = window.screen
    
    return {
      cores: navigator.hardwareConcurrency || 1,
      memory: (navigator as any).deviceMemory || 0,
      screen: {
        width: screen.width,
        height: screen.height,
        pixelRatio: window.devicePixelRatio || 1,
        colorDepth: screen.colorDepth || 24
      }
    }
  }

  /**
   * 检测网络信息
   */
  private detectNetwork(): PlatformInfo['network'] {
    const connection = (navigator as any).connection || 
                      (navigator as any).mozConnection || 
                      (navigator as any).webkitConnection

    if (connection) {
      return {
        connection: connection.type || 'unknown',
        downlink: connection.downlink,
        effectiveType: connection.effectiveType
      }
    }

    return {
      connection: 'unknown'
    }
  }

  /**
   * 主检测方法
   */
  private detectPlatform(): void {
    const versions = this.detectVersions()
    
    this.platformInfo = {
      os: this.detectOperatingSystem(),
      device: this.detectDeviceType(),
      browser: this.detectBrowser(),
      architecture: this.detectArchitecture(),
      version: versions,
      capabilities: this.detectCapabilities(),
      hardware: this.detectHardware(),
      network: this.detectNetwork()
    }

    this.generatePlatformConfig()
    this.notifyListeners()
  }

  /**
   * 生成平台特定配置
   */
  private generatePlatformConfig(): void {
    const info = this.platformInfo!
    
    this.platformConfig = {
      fileSystem: this.getFileSystemConfig(info.os),
      camera: this.getCameraConfig(info),
      performance: this.getPerformanceConfig(info),
      ui: this.getUIConfig(info)
    }
  }

  /**
   * 获取文件系统配置
   */
  private getFileSystemConfig(os: OperatingSystem): PlatformConfig['fileSystem'] {
    switch (os) {
      case OperatingSystem.WINDOWS:
        return {
          separator: '\\',
          homePath: '%USERPROFILE%',
          tempPath: '%TEMP%'
        }
      case OperatingSystem.MACOS:
      case OperatingSystem.LINUX:
        return {
          separator: '/',
          homePath: '~',
          tempPath: '/tmp'
        }
      default:
        return {
          separator: '/',
          homePath: '.',
          tempPath: './temp'
        }
    }
  }

  /**
   * 获取摄像头配置
   */
  private getCameraConfig(info: PlatformInfo): PlatformConfig['camera'] {
    const baseConfig = {
      preferredFormat: 'video/mp4',
      supportedFormats: ['video/mp4', 'video/webm'],
      defaultConstraints: {
        video: {
          width: { ideal: 1280 },
          height: { ideal: 720 },
          frameRate: { ideal: 30 }
        },
        audio: false
      } as MediaStreamConstraints
    }

    // 移动设备优化
    if (info.device === DeviceType.MOBILE) {
      baseConfig.defaultConstraints.video = {
        width: { ideal: 640 },
        height: { ideal: 480 },
        frameRate: { ideal: 24 }
      }
    }

    // 浏览器特定优化
    if (info.browser === Browser.SAFARI) {
      baseConfig.supportedFormats = ['video/mp4']
    }

    return baseConfig
  }

  /**
   * 获取性能配置
   */
  private getPerformanceConfig(info: PlatformInfo): PlatformConfig['performance'] {
    let maxMemoryUsage = 512 // MB
    let preferredFrameRate = 60
    let enableGPUAcceleration = true

    // 基于设备类型调整
    switch (info.device) {
      case DeviceType.MOBILE:
        maxMemoryUsage = 256
        preferredFrameRate = 30
        break
      case DeviceType.TABLET:
        maxMemoryUsage = 384
        preferredFrameRate = 45
        break
    }

    // 基于硬件调整
    if (info.hardware.memory > 0) {
      maxMemoryUsage = Math.min(info.hardware.memory * 1024 * 0.2, maxMemoryUsage)
    }

    if (info.hardware.cores < 4) {
      preferredFrameRate = Math.min(preferredFrameRate, 30)
    }

    // 浏览器特定调整
    if (info.browser === Browser.IE) {
      enableGPUAcceleration = false
      preferredFrameRate = 30
    }

    return {
      maxMemoryUsage,
      preferredFrameRate,
      enableGPUAcceleration
    }
  }

  /**
   * 获取UI配置
   */
  private getUIConfig(info: PlatformInfo): PlatformConfig['ui'] {
    return {
      scrollBehavior: info.capabilities.touch ? 'auto' : 'smooth',
      animationDuration: info.device === DeviceType.MOBILE ? 200 : 300,
      preferredFontSize: info.device === DeviceType.MOBILE ? 14 : 16
    }
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 监听屏幕方向变化
    window.addEventListener('orientationchange', () => {
      setTimeout(() => this.detectPlatform(), 100)
    })

    // 监听窗口大小变化
    window.addEventListener('resize', () => {
      setTimeout(() => this.detectPlatform(), 100)
    })

    // 监听网络状态变化
    window.addEventListener('online', () => this.detectPlatform())
    window.addEventListener('offline', () => this.detectPlatform())
  }

  /**
   * 通知监听器
   */
  private notifyListeners(): void {
    if (this.platformInfo) {
      this.listeners.forEach(callback => {
        try {
          callback(this.platformInfo!)
        } catch (error) {
          console.error('Platform detection listener error:', error)
        }
      })
    }
  }

  /**
   * 工具方法：检查是否为特定平台
   */
  public isWindows(): boolean {
    return this.getPlatformInfo().os === OperatingSystem.WINDOWS
  }

  public isMacOS(): boolean {
    return this.getPlatformInfo().os === OperatingSystem.MACOS
  }

  public isLinux(): boolean {
    return this.getPlatformInfo().os === OperatingSystem.LINUX
  }

  public isMobile(): boolean {
    return this.getPlatformInfo().device === DeviceType.MOBILE
  }

  public isTablet(): boolean {
    return this.getPlatformInfo().device === DeviceType.TABLET
  }

  public isDesktop(): boolean {
    return this.getPlatformInfo().device === DeviceType.DESKTOP
  }

  public supportsTouchscreen(): boolean {
    return this.getPlatformInfo().capabilities.touch
  }

  public supportsWebRTC(): boolean {
    return this.getPlatformInfo().capabilities.webRTC
  }

  /**
   * 获取优化建议
   */
  public getOptimizationRecommendations(): string[] {
    const info = this.getPlatformInfo()
    const recommendations: string[] = []

    if (info.device === DeviceType.MOBILE) {
      recommendations.push('启用移动设备优化模式')
      recommendations.push('减少动画复杂度')
      recommendations.push('优化触摸交互')
    }

    if (info.hardware.memory > 0 && info.hardware.memory < 4) {
      recommendations.push('启用内存节省模式')
      recommendations.push('减少缓存大小')
    }

    if (info.hardware.cores < 4) {
      recommendations.push('减少并行处理任务')
      recommendations.push('优化渲染性能')
    }

    if (!info.capabilities.webGL) {
      recommendations.push('禁用GPU加速')
      recommendations.push('使用软件渲染')
    }

    if (info.browser === Browser.IE) {
      recommendations.push('提示用户升级浏览器')
      recommendations.push('启用兼容模式')
    }

    return recommendations
  }

  /**
   * 导出诊断信息
   */
  public exportDiagnosticInfo(): string {
    const info = this.getPlatformInfo()
    const config = this.getPlatformConfig()
    const recommendations = this.getOptimizationRecommendations()

    return JSON.stringify({
      platform: info,
      config,
      recommendations,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent
    }, null, 2)
  }
}

// 导出单例实例
export const platformDetector = PlatformDetector.getInstance()

// 便捷函数
export const getPlatformInfo = () => platformDetector.getPlatformInfo()
export const getPlatformConfig = () => platformDetector.getPlatformConfig()
export const isWindows = () => platformDetector.isWindows()
export const isMacOS = () => platformDetector.isMacOS()
export const isLinux = () => platformDetector.isLinux()
export const isMobile = () => platformDetector.isMobile()
export const isTablet = () => platformDetector.isTablet()
export const isDesktop = () => platformDetector.isDesktop()
export const supportsTouchscreen = () => platformDetector.supportsTouchscreen()
export const supportsWebRTC = () => platformDetector.supportsWebRTC() 