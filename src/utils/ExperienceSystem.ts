import {
  ExperienceSource,
  ExperienceRecord,
  UserExperience,
  Level,
  AchievementSystemConfig
} from '../types/achievements';

// 等级系统配置
const DEFAULT_LEVELS: Level[] = [
  { level: 1, requiredExperience: 0, name: '专注新手', description: '开始你的专注之旅' },
  { level: 2, requiredExperience: 100, name: '初级专注者', description: '掌握基本专注技巧' },
  { level: 3, requiredExperience: 250, name: '专注学徒', description: '专注能力稳步提升' },
  { level: 4, requiredExperience: 450, name: '专注技师', description: '展现出色的专注能力' },
  { level: 5, requiredExperience: 700, name: '专注专家', description: '专注技能日趋成熟' },
  { level: 6, requiredExperience: 1000, name: '专注大师', description: '拥有卓越的专注力' },
  { level: 7, requiredExperience: 1350, name: '专注宗师', description: '专注能力登峰造极' },
  { level: 8, requiredExperience: 1750, name: '专注传说', description: '成为专注领域的传奇' },
  { level: 9, requiredExperience: 2200, name: '专注大神', description: '达到专注的神级境界' },
  { level: 10, requiredExperience: 2700, name: '专注至尊', description: '专注力的终极体现' }
];

// 经验值奖励配置
const EXPERIENCE_REWARDS = {
  [ExperienceSource.DAILY_TASK]: 50,
  [ExperienceSource.WEEKLY_TASK]: 200,
  [ExperienceSource.ACHIEVEMENT]: 100,
  [ExperienceSource.MILESTONE]: 300,
  [ExperienceSource.BONUS]: 25,
  [ExperienceSource.PERFECT_SESSION]: 75,
  [ExperienceSource.IMPROVEMENT]: 40
};

export class ExperienceSystem {
  private config: AchievementSystemConfig;
  private levels: Level[];
  private experienceRecords: ExperienceRecord[] = [];

  constructor(config?: Partial<AchievementSystemConfig>) {
    this.config = {
      experienceMultiplier: 1.0,
      levelUpBonus: 50,
      dailyTaskResetTime: '00:00',
      weeklyTaskResetDay: 1, // 周一
      maxDailyTasks: 5,
      maxWeeklyTasks: 3,
      ...config
    };
    this.levels = DEFAULT_LEVELS;
  }

  /**
   * 计算基础经验值奖励
   */
  private calculateBaseExperience(source: ExperienceSource, multiplier: number = 1): number {
    const baseReward = EXPERIENCE_REWARDS[source] || 0;
    return Math.floor(baseReward * multiplier * this.config.experienceMultiplier);
  }

  /**
   * 添加经验值
   */
  addExperience(
    source: ExperienceSource,
    description: string,
    options: {
      multiplier?: number;
      customAmount?: number;
      achievementId?: string;
      sessionId?: string;
    } = {}
  ): ExperienceRecord {
    const {
      multiplier = 1,
      customAmount,
      achievementId,
      sessionId
    } = options;

    const amount = customAmount !== undefined 
      ? customAmount 
      : this.calculateBaseExperience(source, multiplier);

    const record: ExperienceRecord = {
      id: this.generateId(),
      amount,
      source,
      description,
      achievementId,
      sessionId,
      earnedAt: new Date()
    };

    this.experienceRecords.push(record);
    return record;
  }

  /**
   * 批量添加经验值
   */
  addMultipleExperience(experiences: Array<{
    source: ExperienceSource;
    description: string;
    multiplier?: number;
    customAmount?: number;
    achievementId?: string;
    sessionId?: string;
  }>): ExperienceRecord[] {
    return experiences.map(exp => this.addExperience(exp.source, exp.description, exp));
  }

  /**
   * 计算用户当前经验值状态
   */
  calculateUserExperience(): UserExperience {
    const totalExperience = this.experienceRecords.reduce((sum, record) => sum + record.amount, 0);
    const currentLevel = this.calculateLevel(totalExperience);
    const currentLevelRequirement = this.levels[currentLevel - 1]?.requiredExperience || 0;
    const nextLevelRequirement = this.levels[currentLevel]?.requiredExperience || totalExperience;
    const currentExperience = totalExperience - currentLevelRequirement;
    const experienceToNextLevel = nextLevelRequirement - totalExperience;

    // 查找最后一次升级时间
    const lastLevelUp = this.findLastLevelUpDate(totalExperience);

    return {
      currentLevel,
      currentExperience,
      totalExperience,
      experienceToNextLevel: Math.max(0, experienceToNextLevel),
      lastLevelUp
    };
  }

  /**
   * 根据总经验值计算等级
   */
  calculateLevel(totalExperience: number): number {
    for (let i = this.levels.length - 1; i >= 0; i--) {
      if (totalExperience >= this.levels[i].requiredExperience) {
        return this.levels[i].level;
      }
    }
    return 1;
  }

  /**
   * 获取等级信息
   */
  getLevelInfo(level: number): Level | undefined {
    return this.levels.find(l => l.level === level);
  }

  /**
   * 获取所有等级信息
   */
  getAllLevels(): Level[] {
    return [...this.levels];
  }

  /**
   * 检查是否达到新等级
   */
  checkLevelUp(previousExperience: number, newExperience: number): {
    leveledUp: boolean;
    oldLevel: number;
    newLevel: number;
    bonusExperience?: number;
  } {
    const oldLevel = this.calculateLevel(previousExperience);
    const newLevel = this.calculateLevel(newExperience);
    
    if (newLevel > oldLevel) {
      // 升级奖励经验值
      const bonusRecord = this.addExperience(
        ExperienceSource.BONUS,
        `升级到 ${this.getLevelInfo(newLevel)?.name} 的奖励`,
        { customAmount: this.config.levelUpBonus }
      );

      return {
        leveledUp: true,
        oldLevel,
        newLevel,
        bonusExperience: bonusRecord.amount
      };
    }

    return {
      leveledUp: false,
      oldLevel,
      newLevel
    };
  }

  /**
   * 获取经验值记录
   */
  getExperienceRecords(options: {
    source?: ExperienceSource;
    sessionId?: string;
    dateRange?: { start: Date; end: Date };
    limit?: number;
  } = {}): ExperienceRecord[] {
    let records = [...this.experienceRecords];

    // 按来源过滤
    if (options.source) {
      records = records.filter(record => record.source === options.source);
    }

    // 按会话过滤
    if (options.sessionId) {
      records = records.filter(record => record.sessionId === options.sessionId);
    }

    // 按日期范围过滤
    if (options.dateRange) {
      records = records.filter(record => 
        record.earnedAt >= options.dateRange!.start && 
        record.earnedAt <= options.dateRange!.end
      );
    }

    // 按时间倒序排列
    records.sort((a, b) => b.earnedAt.getTime() - a.earnedAt.getTime());

    // 限制数量
    if (options.limit) {
      records = records.slice(0, options.limit);
    }

    return records;
  }

  /**
   * 获取经验值统计
   */
  getExperienceStats(): {
    totalExperience: number;
    todayExperience: number;
    weekExperience: number;
    averageDailyExperience: number;
    experienceBySource: Record<ExperienceSource, number>;
    recordCount: number;
  } {
    const now = new Date();
    const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const weekStart = new Date(todayStart.getTime() - 7 * 24 * 60 * 60 * 1000);

    const totalExperience = this.experienceRecords.reduce((sum, record) => sum + record.amount, 0);
    const todayExperience = this.experienceRecords
      .filter(record => record.earnedAt >= todayStart)
      .reduce((sum, record) => sum + record.amount, 0);
    const weekExperience = this.experienceRecords
      .filter(record => record.earnedAt >= weekStart)
      .reduce((sum, record) => sum + record.amount, 0);

    // 计算平均每日经验值
    const firstRecord = this.experienceRecords
      .sort((a, b) => a.earnedAt.getTime() - b.earnedAt.getTime())[0];
    const daysSinceStart = firstRecord 
      ? Math.max(1, Math.ceil((now.getTime() - firstRecord.earnedAt.getTime()) / (24 * 60 * 60 * 1000)))
      : 1;
    const averageDailyExperience = totalExperience / daysSinceStart;

    // 按来源统计经验值
    const experienceBySource: Record<ExperienceSource, number> = {} as any;
    Object.values(ExperienceSource).forEach(source => {
      experienceBySource[source] = this.experienceRecords
        .filter(record => record.source === source)
        .reduce((sum, record) => sum + record.amount, 0);
    });

    return {
      totalExperience,
      todayExperience,
      weekExperience,
      averageDailyExperience,
      experienceBySource,
      recordCount: this.experienceRecords.length
    };
  }

  /**
   * 清除过期记录
   */
  cleanupOldRecords(daysToKeep: number = 30): number {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

    const initialCount = this.experienceRecords.length;
    this.experienceRecords = this.experienceRecords.filter(
      record => record.earnedAt >= cutoffDate
    );

    return initialCount - this.experienceRecords.length;
  }

  /**
   * 导入经验值记录
   */
  importRecords(records: ExperienceRecord[]): void {
    this.experienceRecords = [...this.experienceRecords, ...records];
    // 按时间排序
    this.experienceRecords.sort((a, b) => a.earnedAt.getTime() - b.earnedAt.getTime());
  }

  /**
   * 导出经验值记录
   */
  exportRecords(): ExperienceRecord[] {
    return [...this.experienceRecords];
  }

  /**
   * 查找最后一次升级日期
   */
  private findLastLevelUpDate(currentExperience: number): Date | undefined {
    const currentLevel = this.calculateLevel(currentExperience);
    if (currentLevel <= 1) return undefined;

    // 寻找达到当前等级的时间点
    const levelRequirement = this.levels[currentLevel - 1].requiredExperience;
    let accumulatedExp = 0;

    for (const record of this.experienceRecords.sort((a, b) => a.earnedAt.getTime() - b.earnedAt.getTime())) {
      accumulatedExp += record.amount;
      if (accumulatedExp >= levelRequirement) {
        return record.earnedAt;
      }
    }

    return undefined;
  }

  /**
   * 生成唯一ID
   */
  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<AchievementSystemConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * 获取当前配置
   */
  getConfig(): AchievementSystemConfig {
    return { ...this.config };
  }
} 