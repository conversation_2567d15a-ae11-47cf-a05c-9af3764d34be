import React from 'react'
import { memoryManager } from './MemoryManager'

/**
 * 事件系统优化器
 * 实现事件委托、防抖节流、事件池等优化技术
 */

export interface EventConfig {
  type: string
  handler: EventListener
  options?: AddEventListenerOptions
  throttle?: number
  debounce?: number
  preventDefault?: boolean
  stopPropagation?: boolean
}

export interface DelegatedEventConfig {
  selector: string
  events: Record<string, EventListener>
  container?: Element
}

export interface EventMetrics {
  eventType: string
  handlerCount: number
  avgExecutionTime: number
  totalCalls: number
  lastExecutionTime: number
  errors: number
}

export class EventSystemOptimizer {
  private static instance: EventSystemOptimizer
  private eventMetrics = new Map<string, EventMetrics>()
  private throttledHandlers = new Map<string, any>()
  private debouncedHandlers = new Map<string, any>()
  private delegatedEvents = new Map<string, DelegatedEventConfig>()
  private eventPool: Event[] = []
  private maxPoolSize = 100
  private performanceThreshold = 16 // 16ms threshold

  private constructor() {
    this.setupGlobalEventDelegation()
    this.startPerformanceMonitoring()
  }

  static getInstance(): EventSystemOptimizer {
    if (!EventSystemOptimizer.instance) {
      EventSystemOptimizer.instance = new EventSystemOptimizer()
    }
    return EventSystemOptimizer.instance
  }

  /**
   * 设置全局事件委托
   */
  private setupGlobalEventDelegation(): void {
    const commonEvents = ['click', 'mousedown', 'mouseup', 'touchstart', 'touchend']
    
    commonEvents.forEach(eventType => {
      document.addEventListener(eventType, (event) => {
        this.handleDelegatedEvent(event)
      }, { passive: true, capture: true })
    })
  }

  /**
   * 处理委托事件
   */
  private handleDelegatedEvent(event: Event): void {
    const target = event.target as Element
    if (!target) return

    // 查找匹配的委托配置
    for (const [id, config] of this.delegatedEvents) {
      const matchingElements = config.container 
        ? config.container.querySelectorAll(config.selector)
        : document.querySelectorAll(config.selector)

      for (const element of Array.from(matchingElements)) {
        if (element === target || element.contains(target)) {
          const handler = config.events[event.type]
          if (handler) {
            this.executeHandler(handler, event, `delegated-${id}-${event.type}`)
          }
        }
      }
    }
  }

  /**
   * 创建优化的事件监听器
   */
  createOptimizedEventListener(
    target: EventTarget,
    config: EventConfig
  ): string {
    const id = `event-${Date.now()}-${Math.random()}`
    let handler = config.handler

    // 应用节流
    if (config.throttle) {
      handler = this.createThrottledHandler(handler, config.throttle, id)
    }

    // 应用防抖
    if (config.debounce) {
      handler = this.createDebouncedHandler(handler, config.debounce, id)
    }

    // 包装处理函数以添加性能监控和错误处理
    const wrappedHandler = (event: Event) => {
      if (config.preventDefault) {
        event.preventDefault()
      }
      if (config.stopPropagation) {
        event.stopPropagation()
      }

      this.executeHandler(handler, event, id)
    }

    // 添加事件监听器
    target.addEventListener(config.type, wrappedHandler, config.options)

    // 注册到内存管理器
    const cleanup = () => {
      target.removeEventListener(config.type, wrappedHandler, config.options)
      this.cleanupHandler(id)
    }

    memoryManager.registerResource(id, 'listener', 0, cleanup, {
      target,
      type: config.type,
      hasThrottle: !!config.throttle,
      hasDebounce: !!config.debounce
    })

    // 初始化指标
    this.initEventMetrics(config.type)

    return id
  }

  /**
   * 创建事件委托
   */
  createEventDelegation(config: DelegatedEventConfig): string {
    const id = `delegation-${Date.now()}-${Math.random()}`
    
    this.delegatedEvents.set(id, config)

    // 注册到内存管理器
    const cleanup = () => {
      this.delegatedEvents.delete(id)
    }

    memoryManager.registerResource(id, 'listener', 0, cleanup, {
      type: 'delegated',
      selector: config.selector,
      eventCount: Object.keys(config.events).length
    })

    return id
  }

  /**
   * 创建节流处理函数
   */
  private createThrottledHandler(
    handler: EventListener,
    delay: number,
    id: string
  ): EventListener {
    let lastExecution = 0
    let timeoutId: number | null = null

    const throttledHandler = (event: Event) => {
      const now = Date.now()
      const timeSinceLastExecution = now - lastExecution

      if (timeSinceLastExecution >= delay) {
        lastExecution = now
        handler(event)
      } else {
        // 确保最后一次调用也会被执行
        if (timeoutId) {
          clearTimeout(timeoutId)
        }
        timeoutId = window.setTimeout(() => {
          lastExecution = Date.now()
          handler(event)
          timeoutId = null
        }, delay - timeSinceLastExecution)
      }
    }

    this.throttledHandlers.set(id, { handler: throttledHandler, timeoutId })
    return throttledHandler
  }

  /**
   * 创建防抖处理函数
   */
  private createDebouncedHandler(
    handler: EventListener,
    delay: number,
    id: string
  ): EventListener {
    let timeoutId: number | null = null

    const debouncedHandler = (event: Event) => {
      if (timeoutId) {
        clearTimeout(timeoutId)
      }

      timeoutId = window.setTimeout(() => {
        handler(event)
        timeoutId = null
      }, delay)
    }

    this.debouncedHandlers.set(id, { handler: debouncedHandler, timeoutId })
    return debouncedHandler
  }

  /**
   * 执行处理函数并记录性能指标
   */
  private executeHandler(handler: EventListener, event: Event, metricKey: string): void {
    const startTime = performance.now()
    
    try {
      handler(event)
    } catch (error) {
      console.error('事件处理器执行错误:', error)
      this.updateErrorMetrics(metricKey)
    } finally {
      const executionTime = performance.now() - startTime
      this.updatePerformanceMetrics(metricKey, executionTime)
    }
  }

  /**
   * 初始化事件指标
   */
  private initEventMetrics(eventType: string): void {
    if (!this.eventMetrics.has(eventType)) {
      this.eventMetrics.set(eventType, {
        eventType,
        handlerCount: 0,
        avgExecutionTime: 0,
        totalCalls: 0,
        lastExecutionTime: 0,
        errors: 0
      })
    }
    
    const metrics = this.eventMetrics.get(eventType)!
    metrics.handlerCount++
  }

  /**
   * 更新性能指标
   */
  private updatePerformanceMetrics(metricKey: string, executionTime: number): void {
    const eventType = metricKey.split('-')[0]
    const metrics = this.eventMetrics.get(eventType)
    
    if (metrics) {
      metrics.totalCalls++
      metrics.lastExecutionTime = executionTime
      metrics.avgExecutionTime = (metrics.avgExecutionTime * (metrics.totalCalls - 1) + executionTime) / metrics.totalCalls

      // 警告慢事件处理器
      if (executionTime > this.performanceThreshold) {
        console.warn(`⚠️ 慢事件处理器: ${eventType} 执行时间 ${executionTime.toFixed(2)}ms`)
      }
    }
  }

  /**
   * 更新错误指标
   */
  private updateErrorMetrics(metricKey: string): void {
    const eventType = metricKey.split('-')[0]
    const metrics = this.eventMetrics.get(eventType)
    
    if (metrics) {
      metrics.errors++
    }
  }

  /**
   * 清理处理函数
   */
  private cleanupHandler(id: string): void {
    // 清理节流处理器
    const throttledHandler = this.throttledHandlers.get(id)
    if (throttledHandler && throttledHandler.timeoutId) {
      clearTimeout(throttledHandler.timeoutId)
      this.throttledHandlers.delete(id)
    }

    // 清理防抖处理器
    const debouncedHandler = this.debouncedHandlers.get(id)
    if (debouncedHandler && debouncedHandler.timeoutId) {
      clearTimeout(debouncedHandler.timeoutId)
      this.debouncedHandlers.delete(id)
    }
  }

  /**
   * 开始性能监控
   */
  private startPerformanceMonitoring(): void {
    setInterval(() => {
      this.analyzeEventPerformance()
    }, 60000) // 每分钟分析一次
  }

  /**
   * 分析事件性能
   */
  private analyzeEventPerformance(): void {
    for (const [eventType, metrics] of this.eventMetrics) {
      if (metrics.avgExecutionTime > this.performanceThreshold) {
        console.warn(`📊 事件性能警告: ${eventType} 平均执行时间 ${metrics.avgExecutionTime.toFixed(2)}ms`)
      }

      if (metrics.errors > 0) {
        console.warn(`❌ 事件错误: ${eventType} 错误次数 ${metrics.errors}`)
      }

      // 重置计数器
      if (metrics.totalCalls > 1000) {
        metrics.totalCalls = Math.floor(metrics.totalCalls / 2)
        metrics.errors = Math.floor(metrics.errors / 2)
      }
    }
  }

  /**
   * 获取事件性能报告
   */
  getPerformanceReport(): Record<string, EventMetrics> {
    const report: Record<string, EventMetrics> = {}
    
    for (const [eventType, metrics] of this.eventMetrics) {
      report[eventType] = { ...metrics }
    }
    
    return report
  }

  /**
   * 优化建议
   */
  getOptimizationSuggestions(): string[] {
    const suggestions: string[] = []
    
    for (const [eventType, metrics] of this.eventMetrics) {
      if (metrics.handlerCount > 10) {
        suggestions.push(`考虑为 ${eventType} 事件使用事件委托，当前有 ${metrics.handlerCount} 个处理器`)
      }

      if (metrics.avgExecutionTime > this.performanceThreshold) {
        suggestions.push(`${eventType} 事件处理器执行时间过长 (${metrics.avgExecutionTime.toFixed(2)}ms)，建议优化`)
      }

      if (metrics.errors > metrics.totalCalls * 0.01) {
        suggestions.push(`${eventType} 事件错误率过高 (${(metrics.errors / metrics.totalCalls * 100).toFixed(1)}%)`)
      }
    }
    
    return suggestions
  }

  /**
   * 移除事件监听器
   */
  removeEventListener(id: string): void {
    memoryManager.unregisterResource(id)
  }

  /**
   * 清理所有事件
   */
  cleanup(): void {
    // 清理所有节流和防抖处理器
    for (const [id] of this.throttledHandlers) {
      this.cleanupHandler(id)
    }

    for (const [id] of this.debouncedHandlers) {
      this.cleanupHandler(id)
    }

    // 清理委托事件
    this.delegatedEvents.clear()

    // 清理指标
    this.eventMetrics.clear()

    console.log('✅ 事件系统清理完成')
  }
}

// 单例实例
export const eventOptimizer = EventSystemOptimizer.getInstance()

// React Hooks
export function useOptimizedEventListener(
  target: React.RefObject<Element> | Element | null,
  eventType: string,
  handler: EventListener,
  options?: {
    throttle?: number
    debounce?: number
    preventDefault?: boolean
    stopPropagation?: boolean
    passive?: boolean
  }
) {
  const handlerRef = React.useRef(handler)
  const idRef = React.useRef<string | null>(null)

  // 更新处理函数引用
  React.useEffect(() => {
    handlerRef.current = handler
  }, [handler])

  React.useEffect(() => {
    const element = target && 'current' in target ? target.current : target
    if (!element) return

    const config: EventConfig = {
      type: eventType,
      handler: (event) => handlerRef.current(event),
      options: { passive: options?.passive },
      throttle: options?.throttle,
      debounce: options?.debounce,
      preventDefault: options?.preventDefault,
      stopPropagation: options?.stopPropagation
    }

    idRef.current = eventOptimizer.createOptimizedEventListener(element, config)

    return () => {
      if (idRef.current) {
        eventOptimizer.removeEventListener(idRef.current)
        idRef.current = null
      }
    }
  }, [target, eventType, options?.throttle, options?.debounce, options?.preventDefault, options?.stopPropagation, options?.passive])
}

export function useEventDelegation(
  selector: string,
  events: Record<string, EventListener>,
  container?: React.RefObject<Element>
) {
  const eventsRef = React.useRef(events)
  const idRef = React.useRef<string | null>(null)

  // 更新事件处理函数引用
  React.useEffect(() => {
    eventsRef.current = events
  }, [events])

  React.useEffect(() => {
    const containerElement = container?.current || document.body

    const config: DelegatedEventConfig = {
      selector,
      events: Object.fromEntries(
        Object.entries(events).map(([type, handler]) => [
          type,
          (event: Event) => eventsRef.current[type]?.(event)
        ])
      ),
      container: containerElement
    }

    idRef.current = eventOptimizer.createEventDelegation(config)

    return () => {
      if (idRef.current) {
        eventOptimizer.removeEventListener(idRef.current)
        idRef.current = null
      }
    }
  }, [selector, container])
}

// 工具函数
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): T {
  let lastExecution = 0
  let timeoutId: number | null = null

  return ((...args: Parameters<T>) => {
    const now = Date.now()
    const timeSinceLastExecution = now - lastExecution

    if (timeSinceLastExecution >= delay) {
      lastExecution = now
      return func(...args)
    } else {
      if (timeoutId) {
        clearTimeout(timeoutId)
      }
      timeoutId = window.setTimeout(() => {
        lastExecution = Date.now()
        func(...args)
        timeoutId = null
      }, delay - timeSinceLastExecution)
    }
  }) as T
}

export function debounce<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): T {
  let timeoutId: number | null = null

  return ((...args: Parameters<T>) => {
    if (timeoutId) {
      clearTimeout(timeoutId)
    }

    timeoutId = window.setTimeout(() => {
      func(...args)
      timeoutId = null
    }, delay)
  }) as T
} 