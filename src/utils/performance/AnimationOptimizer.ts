import React from 'react'

/**
 * 动画优化器
 * 用于管理和优化CSS动画性能，减少GPU负载和电池消耗
 */

export interface AnimationConfig {
  name: string
  duration: number
  timing: string
  fillMode: string
  iterationCount: number | 'infinite'
  isEnabled: boolean
  priority: 'low' | 'medium' | 'high'
}

export interface PerformanceSettings {
  enableAnimations: boolean
  enableParticles: boolean
  enableShadows: boolean
  enableFilters: boolean
  maxAnimations: number
  reducedMotion: boolean
  batteryOptimized: boolean
  performanceLevel: 'high' | 'medium' | 'low' | 'potato'
}

export class AnimationOptimizer {
  private static instance: AnimationOptimizer
  private settings: PerformanceSettings
  private activeAnimations = new Set<string>()
  private animationQueue: string[] = []
  private rafId: number | null = null
  private isOptimizing = false

  private constructor() {
    this.settings = this.getDefaultSettings()
    this.detectPerformanceLevel()
    this.setupReducedMotionListener()
    this.setupBatteryListener()
  }

  static getInstance(): AnimationOptimizer {
    if (!AnimationOptimizer.instance) {
      AnimationOptimizer.instance = new AnimationOptimizer()
    }
    return AnimationOptimizer.instance
  }

  /**
   * 获取默认性能设置
   */
  private getDefaultSettings(): PerformanceSettings {
    return {
      enableAnimations: true,
      enableParticles: true,
      enableShadows: true,
      enableFilters: true,
      maxAnimations: 10,
      reducedMotion: window.matchMedia('(prefers-reduced-motion: reduce)').matches,
      batteryOptimized: false,
      performanceLevel: 'high'
    }
  }

  /**
   * 检测设备性能水平
   */
  private detectPerformanceLevel(): void {
    const canvas = document.createElement('canvas')
    const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl') as WebGLRenderingContext | null
    
    // 检测GPU性能
    let performanceLevel: PerformanceSettings['performanceLevel'] = 'medium'
    
    if (gl) {
      const debugInfo = gl.getExtension('WEBGL_debug_renderer_info')
      if (debugInfo) {
        const renderer = gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL) as string
        // 基于GPU型号判断性能级别
        if (renderer.includes('Intel') && renderer.includes('UHD')) {
          performanceLevel = 'low'
        } else if (renderer.includes('GTX') || renderer.includes('RTX') || renderer.includes('Radeon')) {
          performanceLevel = 'high'
        }
      }
    }

    // 检测CPU核心数
    const cores = navigator.hardwareConcurrency || 2
    if (cores < 4) {
      performanceLevel = performanceLevel === 'high' ? 'medium' : 'low'
    }

    // 检测内存
    const memory = (navigator as any).deviceMemory
    if (memory && memory < 4) {
      performanceLevel = 'low'
    }

    this.settings.performanceLevel = performanceLevel
    this.applyPerformanceLevelSettings()
  }

  /**
   * 应用性能级别设置
   */
  private applyPerformanceLevelSettings(): void {
    switch (this.settings.performanceLevel) {
      case 'potato':
        this.settings.enableAnimations = false
        this.settings.enableParticles = false
        this.settings.enableShadows = false
        this.settings.enableFilters = false
        this.settings.maxAnimations = 0
        break
      
      case 'low':
        this.settings.enableAnimations = true
        this.settings.enableParticles = false
        this.settings.enableShadows = false
        this.settings.enableFilters = false
        this.settings.maxAnimations = 3
        break
      
      case 'medium':
        this.settings.enableAnimations = true
        this.settings.enableParticles = true
        this.settings.enableShadows = false
        this.settings.enableFilters = true
        this.settings.maxAnimations = 6
        break
      
      case 'high':
        this.settings.enableAnimations = true
        this.settings.enableParticles = true
        this.settings.enableShadows = true
        this.settings.enableFilters = true
        this.settings.maxAnimations = 15
        break
    }
  }

  /**
   * 监听减少动画偏好设置
   */
  private setupReducedMotionListener(): void {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)')
    mediaQuery.addListener(() => {
      this.settings.reducedMotion = mediaQuery.matches
      this.updateAnimationState()
    })
  }

  /**
   * 监听电池状态
   */
  private setupBatteryListener(): void {
    if ('getBattery' in navigator) {
      (navigator as any).getBattery().then((battery: any) => {
        const updateBatteryOptimization = () => {
          const isLowBattery = battery.level < 0.2 || !battery.charging
          this.settings.batteryOptimized = isLowBattery
          
          if (isLowBattery) {
            this.settings.maxAnimations = Math.max(1, this.settings.maxAnimations / 2)
            this.settings.enableParticles = false
          }
          
          this.updateAnimationState()
        }

        battery.addEventListener('levelchange', updateBatteryOptimization)
        battery.addEventListener('chargingchange', updateBatteryOptimization)
        updateBatteryOptimization()
      })
    }
  }

  /**
   * 更新动画状态
   */
  private updateAnimationState(): void {
    const shouldDisableAnimations = this.settings.reducedMotion || 
                                   this.settings.batteryOptimized ||
                                   !this.settings.enableAnimations

    document.documentElement.style.setProperty(
      '--animations-enabled', 
      shouldDisableAnimations ? '0' : '1'
    )

    // 应用CSS变量
    document.documentElement.style.setProperty(
      '--enable-particles', 
      this.settings.enableParticles ? '1' : '0'
    )
    
    document.documentElement.style.setProperty(
      '--enable-shadows', 
      this.settings.enableShadows ? '1' : '0'
    )
    
    document.documentElement.style.setProperty(
      '--enable-filters', 
      this.settings.enableFilters ? '1' : '0'
    )

    // 调整动画时长
    const durationMultiplier = this.settings.batteryOptimized ? 2 : 1
    document.documentElement.style.setProperty(
      '--animation-duration-multiplier', 
      durationMultiplier.toString()
    )
  }

  /**
   * 优化动画帧率
   */
  optimizeFrameRate(): void {
    if (this.isOptimizing) return
    
    this.isOptimizing = true
    let lastTime = 0
    const targetFPS = this.settings.performanceLevel === 'low' ? 30 : 60
    const frameInterval = 1000 / targetFPS

    const optimize = (currentTime: number) => {
      if (currentTime - lastTime >= frameInterval) {
        // 检查当前活动动画数量
        this.checkAnimationCount()
        lastTime = currentTime
      }
      
      this.rafId = requestAnimationFrame(optimize)
    }

    this.rafId = requestAnimationFrame(optimize)
  }

  /**
   * 检查动画数量并进行限制
   */
  private checkAnimationCount(): void {
    const animatingElements = document.querySelectorAll('[data-animating="true"]')
    
    if (animatingElements.length > this.settings.maxAnimations) {
      // 暂停低优先级动画
      const lowPriorityElements = document.querySelectorAll('[data-animation-priority="low"]')
      for (let i = 0; i < lowPriorityElements.length && animatingElements.length > this.settings.maxAnimations; i++) {
        this.pauseAnimation(lowPriorityElements[i] as HTMLElement)
      }
    }
  }

  /**
   * 暂停动画
   */
  pauseAnimation(element: HTMLElement): void {
    element.style.animationPlayState = 'paused'
    element.removeAttribute('data-animating')
  }

  /**
   * 恢复动画
   */
  resumeAnimation(element: HTMLElement): void {
    if (this.activeAnimations.size < this.settings.maxAnimations) {
      element.style.animationPlayState = 'running'
      element.setAttribute('data-animating', 'true')
    }
  }

  /**
   * 注册动画元素
   */
  registerAnimation(element: HTMLElement, priority: 'low' | 'medium' | 'high' = 'medium'): void {
    element.setAttribute('data-animation-priority', priority)
    
    if (this.shouldAllowAnimation()) {
      element.setAttribute('data-animating', 'true')
      this.activeAnimations.add(element.id || Math.random().toString())
    } else {
      this.pauseAnimation(element)
    }
  }

  /**
   * 移除动画注册
   */
  unregisterAnimation(element: HTMLElement): void {
    element.removeAttribute('data-animating')
    element.removeAttribute('data-animation-priority')
    this.activeAnimations.delete(element.id || '')
  }

  /**
   * 检查是否应该允许动画
   */
  private shouldAllowAnimation(): boolean {
    return this.settings.enableAnimations && 
           !this.settings.reducedMotion && 
           this.activeAnimations.size < this.settings.maxAnimations
  }

  /**
   * 创建优化的CSS类
   */
  generateOptimizedCSS(): string {
    const level = this.settings.performanceLevel
    
    return `
      /* 性能优化的动画规则 */
      :root {
        --perf-level: "${level}";
        --animations-enabled: ${this.settings.enableAnimations ? '1' : '0'};
        --enable-particles: ${this.settings.enableParticles ? '1' : '0'};
        --enable-shadows: ${this.settings.enableShadows ? '1' : '0'};
        --enable-filters: ${this.settings.enableFilters ? '1' : '0'};
        --animation-duration-multiplier: ${this.settings.batteryOptimized ? '2' : '1'};
        --max-animations: ${this.settings.maxAnimations};
      }

      /* GPU优化动画基类 */
      .optimized-animation {
        will-change: transform, opacity;
        transform: translateZ(0);
        backface-visibility: hidden;
        perspective: 1000px;
      }

      /* 条件动画 */
      ${level === 'potato' ? `
        .crop-sprite { animation: none !important; }
        .particle { display: none !important; }
      ` : ''}

      ${level === 'low' ? `
        .crop-sprite { 
          animation-duration: calc(var(--growth-duration) * 2) !important;
          filter: none !important;
          box-shadow: none !important;
        }
        .particle { display: none !important; }
      ` : ''}

      ${level === 'medium' ? `
        .crop-sprite { 
          filter: none !important; 
        }
      ` : ''}

      /* 减少动画偏好 */
      @media (prefers-reduced-motion: reduce) {
        .crop-sprite,
        .particle {
          animation-duration: 0.01ms !important;
          animation-iteration-count: 1 !important;
        }
      }

      /* 电池优化 */
      [data-battery-optimized="true"] .crop-sprite {
        animation-duration: calc(var(--growth-duration) * var(--animation-duration-multiplier)) !important;
      }

      /* 动画暂停状态 */
      [data-animating="false"] {
        animation-play-state: paused !important;
      }
    `
  }

  /**
   * 应用CSS优化
   */
  applyCSSOptimizations(): void {
    let styleElement = document.getElementById('animation-optimizer-styles')
    
    if (!styleElement) {
      styleElement = document.createElement('style')
      styleElement.id = 'animation-optimizer-styles'
      document.head.appendChild(styleElement)
    }

    styleElement.textContent = this.generateOptimizedCSS()
    this.updateAnimationState()
  }

  /**
   * 获取性能设置
   */
  getSettings(): PerformanceSettings {
    return { ...this.settings }
  }

  /**
   * 更新设置
   */
  updateSettings(newSettings: Partial<PerformanceSettings>): void {
    this.settings = { ...this.settings, ...newSettings }
    this.applyPerformanceLevelSettings()
    this.applyCSSOptimizations()
  }

  /**
   * 清理资源
   */
  destroy(): void {
    if (this.rafId) {
      cancelAnimationFrame(this.rafId)
      this.rafId = null
    }
    this.isOptimizing = false
    this.activeAnimations.clear()
  }
}

// 单例实例
export const animationOptimizer = AnimationOptimizer.getInstance()

// React Hook
export function useAnimationOptimization(elementRef: React.RefObject<HTMLElement>, priority: 'low' | 'medium' | 'high' = 'medium') {
  React.useEffect(() => {
    const element = elementRef.current
    if (!element) return

    animationOptimizer.registerAnimation(element, priority)
    
    return () => {
      animationOptimizer.unregisterAnimation(element)
    }
  }, [elementRef, priority])

  return animationOptimizer.getSettings()
} 