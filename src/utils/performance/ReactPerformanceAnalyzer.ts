import React from 'react'

/**
 * React组件性能分析器
 * 用于检测和优化React组件的性能问题
 */

export interface PerformanceMetrics {
  renderTime: number
  rerenderCount: number
  componentName: string
  propsChanges: number
  stateChanges: number
  memoryUsage: number
  lastRenderTimestamp: number
}

export interface ComponentAnalysis {
  component: string
  issues: PerformanceIssue[]
  suggestions: string[]
  optimizationLevel: 'good' | 'moderate' | 'needs-attention' | 'critical'
  metrics: PerformanceMetrics
}

export interface PerformanceIssue {
  type: 'frequent-rerender' | 'large-component' | 'expensive-operation' | 'memory-leak' | 'inefficient-hooks'
  severity: 'low' | 'medium' | 'high' | 'critical'
  description: string
  solution: string
}

export interface PerformanceReport {
  timestamp: Date
  components: ComponentAnalysis[]
  overallScore: number
  criticalIssues: number
  recommendations: string[]
}

class ReactPerformanceAnalyzer {
  private componentMetrics = new Map<string, PerformanceMetrics>()
  private renderStartTimes = new Map<string, number>()
  private observers = new Map<string, PerformanceObserver>()
  private isEnabled = false

  /**
   * 启用性能监控
   */
  enable(): void {
    if (typeof window === 'undefined' || this.isEnabled) return

    this.isEnabled = true
    this.startMemoryMonitoring()
    this.startRenderTracking()
    console.log('🚀 React性能分析器已启用')
  }

  /**
   * 禁用性能监控
   */
  disable(): void {
    this.isEnabled = false
    this.observers.forEach(observer => observer.disconnect())
    this.observers.clear()
    console.log('⏹️ React性能分析器已禁用')
  }

  /**
   * 分析组件性能
   */
  analyzeComponent(componentName: string): ComponentAnalysis | null {
    const metrics = this.componentMetrics.get(componentName)
    if (!metrics) return null

    const issues: PerformanceIssue[] = []
    const suggestions: string[] = []

    // 检测频繁重新渲染
    if (metrics.rerenderCount > 20) {
      issues.push({
        type: 'frequent-rerender',
        severity: metrics.rerenderCount > 50 ? 'critical' : 'high',
        description: `组件${componentName}重新渲染${metrics.rerenderCount}次，可能影响性能`,
        solution: '使用React.memo、useMemo或useCallback优化组件'
      })
      suggestions.push('考虑使用React.memo包装组件以防止不必要的重新渲染')
    }

    // 检测渲染时间过长
    if (metrics.renderTime > 16) {
      issues.push({
        type: 'expensive-operation',
        severity: metrics.renderTime > 50 ? 'critical' : 'medium',
        description: `组件${componentName}渲染时间为${metrics.renderTime.toFixed(2)}ms，超过16ms阈值`,
        solution: '将复杂计算移到useMemo中，或拆分为更小的组件'
      })
      suggestions.push('使用useMemo缓存复杂计算结果')
    }

    // 确定优化级别
    const criticalIssues = issues.filter(i => i.severity === 'critical').length
    const highIssues = issues.filter(i => i.severity === 'high').length
    const mediumIssues = issues.filter(i => i.severity === 'medium').length

    let optimizationLevel: ComponentAnalysis['optimizationLevel']
    if (criticalIssues > 0) {
      optimizationLevel = 'critical'
    } else if (highIssues > 0) {
      optimizationLevel = 'needs-attention'
    } else if (mediumIssues > 0) {
      optimizationLevel = 'moderate'
    } else {
      optimizationLevel = 'good'
    }

    return {
      component: componentName,
      issues,
      suggestions,
      optimizationLevel,
      metrics
    }
  }

  /**
   * 生成性能报告
   */
  generateReport(): PerformanceReport {
    const components: ComponentAnalysis[] = []
    let totalScore = 0
    let criticalIssues = 0

    this.componentMetrics.forEach((_, componentName) => {
      const analysis = this.analyzeComponent(componentName)
      if (analysis) {
        components.push(analysis)
        
        // 计算组件分数
        let componentScore = 100
        analysis.issues.forEach(issue => {
          switch (issue.severity) {
            case 'critical': componentScore -= 30; criticalIssues++; break
            case 'high': componentScore -= 20; break
            case 'medium': componentScore -= 10; break
            case 'low': componentScore -= 5; break
          }
        })
        totalScore += Math.max(0, componentScore)
      }
    })

    const overallScore = components.length > 0 ? totalScore / components.length : 100

    // 生成总体建议
    const recommendations: string[] = []
    if (overallScore < 60) {
      recommendations.push('🚨 应用性能需要紧急优化')
    } else if (overallScore < 80) {
      recommendations.push('⚠️ 应用性能有改进空间')
    } else {
      recommendations.push('✅ 应用性能表现良好')
    }

    return {
      timestamp: new Date(),
      components,
      overallScore: Math.round(overallScore),
      criticalIssues,
      recommendations
    }
  }

  /**
   * 开始内存监控
   */
  private startMemoryMonitoring(): void {
    if (!('memory' in performance)) return

    setInterval(() => {
      const memory = (performance as any).memory
      if (memory) {
        const memoryPerComponent = memory.usedJSHeapSize / (this.componentMetrics.size || 1) / (1024 * 1024)
        
        this.componentMetrics.forEach((_, componentName) => {
          this.updateMetrics(componentName, { memoryUsage: memoryPerComponent })
        })
      }
    }, 5000)
  }

  /**
   * 开始渲染追踪
   */
  private startRenderTracking(): void {
    if (!window.PerformanceObserver) return

    try {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        entries.forEach(entry => {
          if (entry.entryType === 'measure' && entry.name.includes('React')) {
            console.log(`React操作: ${entry.name}, 耗时: ${entry.duration.toFixed(2)}ms`)
          }
        })
      })

      observer.observe({ entryTypes: ['measure', 'navigation'] })
      this.observers.set('render-tracker', observer)
    } catch (error) {
      console.warn('性能观察器初始化失败:', error)
    }
  }

  /**
   * 更新组件指标
   */
  private updateMetrics(componentName: string, updates: Partial<PerformanceMetrics>): void {
    const existing = this.componentMetrics.get(componentName) || {
      renderTime: 0,
      rerenderCount: 0,
      componentName,
      propsChanges: 0,
      stateChanges: 0,
      memoryUsage: 0,
      lastRenderTimestamp: Date.now()
    }

    const updated: PerformanceMetrics = {
      ...existing,
      ...updates,
      rerenderCount: existing.rerenderCount + (updates.renderTime ? 1 : 0),
      lastRenderTimestamp: updates.renderTime ? Date.now() : existing.lastRenderTimestamp
    }

    this.componentMetrics.set(componentName, updated)
  }

  /**
   * 记录组件渲染
   */
  recordRender(componentName: string, renderTime: number): void {
    if (!this.isEnabled) return
    this.updateMetrics(componentName, { renderTime })
  }

  /**
   * 清除指标数据
   */
  clearMetrics(): void {
    this.componentMetrics.clear()
  }

  /**
   * 导出性能数据
   */
  exportData(): string {
    const report = this.generateReport()
    return JSON.stringify(report, null, 2)
  }
}

// 单例实例
export const performanceAnalyzer = new ReactPerformanceAnalyzer()

// Hook用于组件内性能追踪
export function usePerformanceTracking(componentName: string) {
  const rerenderCount = React.useRef(0)
  const renderStartTime = React.useRef<number>(0)

  React.useLayoutEffect(() => {
    rerenderCount.current++
    renderStartTime.current = performance.now()
    
    return () => {
      const renderTime = performance.now() - renderStartTime.current
      performanceAnalyzer.recordRender(componentName, renderTime)
    }
  })

  return { rerenderCount: rerenderCount.current }
}

export default ReactPerformanceAnalyzer 