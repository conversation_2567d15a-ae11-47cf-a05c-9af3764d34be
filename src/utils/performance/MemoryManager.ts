import React from 'react'

/**
 * 内存管理优化器
 * 用于监控内存使用、清理资源和防止内存泄漏
 */

export interface MemoryStats {
  usedJSHeapSize: number
  totalJSHeapSize: number
  jsHeapSizeLimit: number
  timestamp: number
  percentage: number
}

export interface MemoryResource {
  id: string
  type: 'timer' | 'listener' | 'observer' | 'animation' | 'data' | 'component'
  size: number
  createdAt: number
  lastAccessed: number
  cleanup?: () => void
  metadata?: any
}

export interface MemoryThresholds {
  warning: number      // 警告阈值 (MB)
  critical: number     // 严重阈值 (MB)
  cleanup: number      // 自动清理阈值 (MB)
  maxResources: number // 最大资源数量
}

export interface MemoryOptimizationSettings {
  enableAutoCleanup: boolean
  cleanupInterval: number
  maxCacheSize: number
  enableCompression: boolean
  enableLazyLoading: boolean
  resourceTimeouts: Record<string, number>
}

export class MemoryManager {
  private static instance: MemoryManager
  private resources = new Map<string, MemoryResource>()
  private memoryStats: MemoryStats[] = []
  private cleanupInterval: number | null = null
  private isMonitoring = false
  
  private settings: MemoryOptimizationSettings = {
    enableAutoCleanup: true,
    cleanupInterval: 30000, // 30秒
    maxCacheSize: 50 * 1024 * 1024, // 50MB
    enableCompression: true,
    enableLazyLoading: true,
    resourceTimeouts: {
      timer: 60000,      // 1分钟
      listener: 300000,  // 5分钟
      observer: 180000,  // 3分钟
      animation: 10000,  // 10秒
      data: 600000,      // 10分钟
      component: 120000  // 2分钟
    }
  }

  private thresholds: MemoryThresholds = {
    warning: 100,       // 100MB
    critical: 200,      // 200MB
    cleanup: 150,       // 150MB
    maxResources: 1000
  }

  private constructor() {
    this.detectMemoryCapacity()
    this.setupMemoryPressureHandling()
  }

  static getInstance(): MemoryManager {
    if (!MemoryManager.instance) {
      MemoryManager.instance = new MemoryManager()
    }
    return MemoryManager.instance
  }

  /**
   * 检测设备内存容量并调整阈值
   */
  private detectMemoryCapacity(): void {
    const navigator = window.navigator as any
    const deviceMemory = navigator.deviceMemory || 4 // 默认4GB

    // 根据设备内存调整阈值
    if (deviceMemory <= 2) {
      // 低内存设备 (2GB及以下)
      this.thresholds = {
        warning: 50,
        critical: 100,
        cleanup: 75,
        maxResources: 500
      }
      this.settings.maxCacheSize = 20 * 1024 * 1024 // 20MB
    } else if (deviceMemory <= 4) {
      // 中等内存设备 (4GB)
      this.thresholds = {
        warning: 100,
        critical: 200,
        cleanup: 150,
        maxResources: 1000
      }
    } else {
      // 高内存设备 (8GB+)
      this.thresholds = {
        warning: 200,
        critical: 400,
        cleanup: 300,
        maxResources: 2000
      }
      this.settings.maxCacheSize = 100 * 1024 * 1024 // 100MB
    }
  }

  /**
   * 设置内存压力处理
   */
  private setupMemoryPressureHandling(): void {
    // 监听内存压力事件（如果支持）
    if ('memory' in performance) {
      const perfMemory = (performance as any).memory
      if ('addEventListener' in perfMemory) {
        perfMemory.addEventListener('memorypressure', () => {
          this.handleMemoryPressure()
        })
      }
    }

    // 监听页面可见性变化
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        this.performBackgroundCleanup()
      }
    })

    // 监听页面卸载
    window.addEventListener('beforeunload', () => {
      this.cleanup()
    })
  }

  /**
   * 开始内存监控
   */
  startMonitoring(): void {
    if (this.isMonitoring) return

    this.isMonitoring = true
    this.cleanupInterval = window.setInterval(() => {
      this.collectMemoryStats()
      this.performAutoCleanup()
    }, this.settings.cleanupInterval)

    console.log('内存监控已启动')
  }

  /**
   * 停止内存监控
   */
  stopMonitoring(): void {
    if (!this.isMonitoring) return

    this.isMonitoring = false
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval)
      this.cleanupInterval = null
    }

    console.log('内存监控已停止')
  }

  /**
   * 收集内存统计信息
   */
  private collectMemoryStats(): void {
    if (!('memory' in performance)) return

    const memory = (performance as any).memory
    const stats: MemoryStats = {
      usedJSHeapSize: memory.usedJSHeapSize,
      totalJSHeapSize: memory.totalJSHeapSize,
      jsHeapSizeLimit: memory.jsHeapSizeLimit,
      timestamp: Date.now(),
      percentage: (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100
    }

    this.memoryStats.push(stats)

    // 保留最近100个统计记录
    if (this.memoryStats.length > 100) {
      this.memoryStats = this.memoryStats.slice(-100)
    }

    // 检查内存阈值
    this.checkMemoryThresholds(stats)
  }

  /**
   * 检查内存阈值
   */
  private checkMemoryThresholds(stats: MemoryStats): void {
    const usedMB = stats.usedJSHeapSize / (1024 * 1024)

    if (usedMB > this.thresholds.critical) {
      console.warn('⚠️ 内存使用严重超标！', `${usedMB.toFixed(1)}MB`)
      this.performEmergencyCleanup()
    } else if (usedMB > this.thresholds.cleanup) {
      console.warn('🧹 自动清理内存...', `${usedMB.toFixed(1)}MB`)
      this.performAggressiveCleanup()
    } else if (usedMB > this.thresholds.warning) {
      console.warn('⚠️ 内存使用警告', `${usedMB.toFixed(1)}MB`)
      this.performMildCleanup()
    }
  }

  /**
   * 注册资源
   */
  registerResource(
    id: string, 
    type: MemoryResource['type'], 
    size: number = 0, 
    cleanup?: () => void,
    metadata?: any
  ): void {
    const resource: MemoryResource = {
      id,
      type,
      size,
      createdAt: Date.now(),
      lastAccessed: Date.now(),
      cleanup,
      metadata
    }

    this.resources.set(id, resource)

    // 检查资源数量限制
    if (this.resources.size > this.thresholds.maxResources) {
      this.cleanupOldestResources(100) // 清理最老的100个资源
    }
  }

  /**
   * 访问资源（更新最后访问时间）
   */
  accessResource(id: string): void {
    const resource = this.resources.get(id)
    if (resource) {
      resource.lastAccessed = Date.now()
    }
  }

  /**
   * 移除资源
   */
  unregisterResource(id: string): void {
    const resource = this.resources.get(id)
    if (resource) {
      if (resource.cleanup) {
        try {
          resource.cleanup()
        } catch (error) {
          console.error('资源清理失败:', id, error)
        }
      }
      this.resources.delete(id)
    }
  }

  /**
   * 执行自动清理
   */
  private performAutoCleanup(): void {
    if (!this.settings.enableAutoCleanup) return

    const now = Date.now()
    const resourcesToClean: string[] = []

    // 查找过期资源
    for (const [id, resource] of this.resources) {
      const timeout = this.settings.resourceTimeouts[resource.type] || 300000
      const isExpired = now - resource.lastAccessed > timeout

      if (isExpired) {
        resourcesToClean.push(id)
      }
    }

    // 清理过期资源
    resourcesToClean.forEach(id => {
      this.unregisterResource(id)
    })

    if (resourcesToClean.length > 0) {
      console.log(`🧹 自动清理了 ${resourcesToClean.length} 个过期资源`)
    }
  }

  /**
   * 轻度清理
   */
  private performMildCleanup(): void {
    this.cleanupOldestResources(10)
    this.cleanupUnusedEventListeners()
  }

  /**
   * 激进清理
   */
  private performAggressiveCleanup(): void {
    this.cleanupOldestResources(50)
    this.cleanupUnusedEventListeners()
    this.cleanupLargeDataStructures()
    this.forceGarbageCollection()
  }

  /**
   * 紧急清理
   */
  private performEmergencyCleanup(): void {
    console.error('🚨 执行紧急内存清理！')
    
    // 清理所有非关键资源
    this.cleanupOldestResources(200)
    this.cleanupAllAnimations()
    this.cleanupAllTimers()
    this.cleanupLargeDataStructures()
    this.forceGarbageCollection()
    
    // 降低性能设置
    this.emergencyPerformanceReduction()
  }

  /**
   * 后台清理
   */
  private performBackgroundCleanup(): void {
    this.cleanupOldestResources(30)
    this.cleanupUnusedEventListeners()
  }

  /**
   * 处理内存压力
   */
  private handleMemoryPressure(): void {
    console.warn('🔥 检测到内存压力！')
    this.performAggressiveCleanup()
  }

  /**
   * 清理最老的资源
   */
  private cleanupOldestResources(count: number): void {
    const sortedResources = Array.from(this.resources.entries())
      .sort(([, a], [, b]) => a.lastAccessed - b.lastAccessed)
      .slice(0, count)

    sortedResources.forEach(([id]) => {
      this.unregisterResource(id)
    })
  }

  /**
   * 清理未使用的事件监听器
   */
  private cleanupUnusedEventListeners(): void {
    const listenerResources = Array.from(this.resources.values())
      .filter(r => r.type === 'listener')
      .filter(r => Date.now() - r.lastAccessed > 120000) // 2分钟未使用

    listenerResources.forEach(resource => {
      this.unregisterResource(resource.id)
    })
  }

  /**
   * 清理大型数据结构
   */
  private cleanupLargeDataStructures(): void {
    const largeDataResources = Array.from(this.resources.values())
      .filter(r => r.type === 'data' && r.size > 1024 * 1024) // 大于1MB

    largeDataResources.forEach(resource => {
      this.unregisterResource(resource.id)
    })
  }

  /**
   * 清理所有动画
   */
  private cleanupAllAnimations(): void {
    const animationResources = Array.from(this.resources.values())
      .filter(r => r.type === 'animation')

    animationResources.forEach(resource => {
      this.unregisterResource(resource.id)
    })
  }

  /**
   * 清理所有定时器
   */
  private cleanupAllTimers(): void {
    const timerResources = Array.from(this.resources.values())
      .filter(r => r.type === 'timer')

    timerResources.forEach(resource => {
      this.unregisterResource(resource.id)
    })
  }

  /**
   * 强制垃圾回收（如果支持）
   */
  private forceGarbageCollection(): void {
    if ('gc' in window) {
      try {
        (window as any).gc()
        console.log('✅ 手动垃圾回收完成')
      } catch (error) {
        console.warn('手动垃圾回收失败:', error)
      }
    }
  }

  /**
   * 紧急性能降级
   */
  private emergencyPerformanceReduction(): void {
    // 通知动画优化器降低性能
    const event = new CustomEvent('memoryPressure', {
      detail: { level: 'critical' }
    })
    window.dispatchEvent(event)
  }

  /**
   * 获取内存统计
   */
  getMemoryStats(): MemoryStats | null {
    return this.memoryStats.length > 0 ? this.memoryStats[this.memoryStats.length - 1] : null
  }

  /**
   * 获取内存使用历史
   */
  getMemoryHistory(): MemoryStats[] {
    return [...this.memoryStats]
  }

  /**
   * 获取资源统计
   */
  getResourceStats(): {
    total: number
    byType: Record<string, number>
    totalSize: number
    oldestResource: MemoryResource | null
  } {
    const resources = Array.from(this.resources.values())
    const byType: Record<string, number> = {}
    let totalSize = 0
    let oldestResource: MemoryResource | null = null

    resources.forEach(resource => {
      byType[resource.type] = (byType[resource.type] || 0) + 1
      totalSize += resource.size
      
      if (!oldestResource || resource.createdAt < oldestResource.createdAt) {
        oldestResource = resource
      }
    })

    return {
      total: resources.length,
      byType,
      totalSize,
      oldestResource
    }
  }

  /**
   * 更新设置
   */
  updateSettings(newSettings: Partial<MemoryOptimizationSettings>): void {
    this.settings = { ...this.settings, ...newSettings }
  }

  /**
   * 全面清理
   */
  cleanup(): void {
    console.log('🧹 执行全面内存清理...')
    
    // 停止监控
    this.stopMonitoring()
    
    // 清理所有资源
    for (const [id] of this.resources) {
      this.unregisterResource(id)
    }
    
    // 清理统计数据
    this.memoryStats = []
    
    console.log('✅ 内存清理完成')
  }
}

// 单例实例
export const memoryManager = MemoryManager.getInstance()

// React Hook
export function useMemoryManagement(componentId: string, size: number = 0) {
  React.useEffect(() => {
    memoryManager.registerResource(componentId, 'component', size)
    
    return () => {
      memoryManager.unregisterResource(componentId)
    }
  }, [componentId, size])

  React.useEffect(() => {
    memoryManager.accessResource(componentId)
  })

  return {
    memoryStats: memoryManager.getMemoryStats(),
    resourceStats: memoryManager.getResourceStats(),
    cleanup: () => memoryManager.unregisterResource(componentId)
  }
}

// 定时器包装器
export function createManagedTimer(
  callback: () => void, 
  delay: number, 
  type: 'interval' | 'timeout' = 'timeout'
): string {
  const id = `timer-${Date.now()}-${Math.random()}`
  
  const wrappedCallback = () => {
    memoryManager.accessResource(id)
    callback()
    
    if (type === 'timeout') {
      memoryManager.unregisterResource(id)
    }
  }

  const timerId = type === 'interval' 
    ? setInterval(wrappedCallback, delay)
    : setTimeout(wrappedCallback, delay)

  const cleanup = () => {
    if (type === 'interval') {
      clearInterval(timerId)
    } else {
      clearTimeout(timerId)
    }
  }

  memoryManager.registerResource(id, 'timer', 0, cleanup, { timerId, type })
  
  return id
}

// 事件监听器包装器
export function createManagedEventListener(
  target: EventTarget,
  event: string,
  handler: EventListener,
  options?: AddEventListenerOptions
): string {
  const id = `listener-${event}-${Date.now()}-${Math.random()}`
  
  const wrappedHandler = (e: Event) => {
    memoryManager.accessResource(id)
    handler(e)
  }

  target.addEventListener(event, wrappedHandler, options)

  const cleanup = () => {
    target.removeEventListener(event, wrappedHandler, options)
  }

  memoryManager.registerResource(id, 'listener', 0, cleanup, { target, event })
  
  return id
} 