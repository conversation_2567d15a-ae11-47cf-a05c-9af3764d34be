import React from 'react'
import { memoryManager } from './MemoryManager'

/**
 * 大数据处理优化器
 * 实现数据分片、Web Workers、虚拟化、缓存等优化技术
 */

export interface DataChunk<T> {
  id: string
  data: T[]
  startIndex: number
  endIndex: number
  size: number
  processed: boolean
  priority: number
}

export interface ProcessingConfig {
  chunkSize: number
  concurrency: number
  useWebWorkers: boolean
  enableCaching: boolean
  enableVirtualization: boolean
  priority: 'low' | 'normal' | 'high' | 'critical'
}

export interface ProcessingResult<T, R> {
  id: string
  input: T[]
  output: R[]
  processingTime: number
  chunkCount: number
  errors: string[]
  cached: boolean
}

export interface DataProcessorMetrics {
  totalProcessed: number
  averageProcessingTime: number
  cacheHitRate: number
  errorRate: number
  memoryUsage: number
  workerUtilization: number
  throughput: number
}

export class DataProcessor {
  private static instance: DataProcessor
  private cache = new Map<string, any>()
  private workers: Worker[] = []
  private processingQueue: DataChunk<any>[] = []
  private metrics: DataProcessorMetrics = {
    totalProcessed: 0,
    averageProcessingTime: 0,
    cacheHitRate: 0,
    errorRate: 0,
    memoryUsage: 0,
    workerUtilization: 0,
    throughput: 0
  }

  private maxCacheSize = 100 * 1024 * 1024 // 100MB
  private maxWorkers = navigator.hardwareConcurrency || 4
  private processingInProgress = new Set<string>()

  private constructor() {
    this.initializeWorkers()
    this.startMetricsCollection()
    this.setupCleanup()
  }

  static getInstance(): DataProcessor {
    if (!DataProcessor.instance) {
      DataProcessor.instance = new DataProcessor()
    }
    return DataProcessor.instance
  }

  /**
   * 初始化Web Workers
   */
  private initializeWorkers(): void {
    const workerCode = `
      self.onmessage = function(e) {
        const { id, data, operation } = e.data;
        
        try {
          let result;
          
          switch(operation) {
            case 'map':
              result = data.map(e.data.processor);
              break;
            case 'filter':
              result = data.filter(e.data.processor);
              break;
            case 'reduce':
              result = data.reduce(e.data.processor, e.data.initialValue);
              break;
            case 'sort':
              result = [...data].sort(e.data.processor);
              break;
            case 'custom':
              result = e.data.processor(data);
              break;
            default:
              throw new Error('Unknown operation: ' + operation);
          }
          
          self.postMessage({
            id,
            success: true,
            result,
            processingTime: Date.now() - e.data.startTime
          });
        } catch (error) {
          self.postMessage({
            id,
            success: false,
            error: error.message,
            processingTime: Date.now() - e.data.startTime
          });
        }
      };
    `

    const blob = new Blob([workerCode], { type: 'application/javascript' })
    const workerUrl = URL.createObjectURL(blob)

    for (let i = 0; i < this.maxWorkers; i++) {
      try {
        const worker = new Worker(workerUrl)
        this.workers.push(worker)
      } catch (error) {
        console.warn('无法创建Web Worker:', error)
        break
      }
    }

    URL.revokeObjectURL(workerUrl)
    console.log(`✅ 初始化了 ${this.workers.length} 个Web Workers`)
  }

  /**
   * 处理大数据集
   */
  async processLargeDataset<T, R>(
    data: T[],
    processor: (chunk: T[]) => R[] | Promise<R[]>,
    config: Partial<ProcessingConfig> = {}
  ): Promise<ProcessingResult<T, R>> {
    const processingId = `process-${Date.now()}-${Math.random()}`
    const startTime = performance.now()

    const finalConfig: ProcessingConfig = {
      chunkSize: Math.min(Math.max(Math.ceil(data.length / 100), 100), 10000),
      concurrency: this.workers.length || 1,
      useWebWorkers: this.workers.length > 0,
      enableCaching: true,
      enableVirtualization: data.length > 10000,
      priority: 'normal',
      ...config
    }

    // 检查缓存
    const cacheKey = this.generateCacheKey(data, processor.toString(), finalConfig)
    if (finalConfig.enableCaching && this.cache.has(cacheKey)) {
      const cached = this.cache.get(cacheKey)
      this.updateCacheHitRate(true)
      return {
        id: processingId,
        input: data,
        output: cached,
        processingTime: performance.now() - startTime,
        chunkCount: 0,
        errors: [],
        cached: true
      }
    }

    this.updateCacheHitRate(false)

    try {
      // 数据分片
      const chunks = this.createDataChunks(data, finalConfig.chunkSize, processingId)
      
      // 处理数据
      const results = await this.processChunks(chunks, processor, finalConfig)
      
      // 合并结果
      const output = this.mergeResults(results)
      
      // 缓存结果
      if (finalConfig.enableCaching) {
        this.cacheResult(cacheKey, output)
      }

      const processingTime = performance.now() - startTime
      this.updateMetrics(processingTime, chunks.length, [])

      return {
        id: processingId,
        input: data,
        output,
        processingTime,
        chunkCount: chunks.length,
        errors: [],
        cached: false
      }

    } catch (error) {
      const processingTime = performance.now() - startTime
      const errorMessage = error instanceof Error ? error.message : String(error)
      this.updateMetrics(processingTime, 0, [errorMessage])

      throw new Error(`数据处理失败: ${errorMessage}`)
    } finally {
      this.processingInProgress.delete(processingId)
    }
  }

  /**
   * 创建数据分片
   */
  private createDataChunks<T>(
    data: T[],
    chunkSize: number,
    processingId: string
  ): DataChunk<T>[] {
    const chunks: DataChunk<T>[] = []
    
    for (let i = 0; i < data.length; i += chunkSize) {
      const chunk: DataChunk<T> = {
        id: `${processingId}-chunk-${i}`,
        data: data.slice(i, i + chunkSize),
        startIndex: i,
        endIndex: Math.min(i + chunkSize - 1, data.length - 1),
        size: Math.min(chunkSize, data.length - i),
        processed: false,
        priority: Math.floor(i / chunkSize)
      }
      
      chunks.push(chunk)
    }

    return chunks
  }

  /**
   * 处理数据分片
   */
  private async processChunks<T, R>(
    chunks: DataChunk<T>[],
    processor: (chunk: T[]) => R[] | Promise<R[]>,
    config: ProcessingConfig
  ): Promise<R[][]> {
    const results: R[][] = new Array(chunks.length)
    const processingPromises: Promise<void>[] = []

    // 控制并发数量
    const semaphore = new Semaphore(config.concurrency)

    for (let i = 0; i < chunks.length; i++) {
      const chunk = chunks[i]
      
      const processingPromise = semaphore.acquire().then(async () => {
        try {
          if (config.useWebWorkers && this.workers.length > 0) {
            results[i] = await this.processChunkWithWorker(chunk, processor)
          } else {
            results[i] = await this.processChunkInMainThread(chunk, processor)
          }
          chunk.processed = true
        } catch (error) {
          console.error(`处理分片 ${chunk.id} 失败:`, error)
          results[i] = []
        } finally {
          semaphore.release()
        }
      })

      processingPromises.push(processingPromise)

      // 如果启用虚拟化，每处理一批就yield
      if (config.enableVirtualization && i % 10 === 9) {
        await new Promise(resolve => setTimeout(resolve, 0))
      }
    }

    await Promise.all(processingPromises)
    return results
  }

  /**
   * 在Web Worker中处理分片
   */
  private async processChunkWithWorker<T, R>(
    chunk: DataChunk<T>,
    processor: (chunk: T[]) => R[] | Promise<R[]>
  ): Promise<R[]> {
    return new Promise((resolve, reject) => {
      const worker = this.getAvailableWorker()
      if (!worker) {
        // 如果没有可用的worker，回退到主线程
        this.processChunkInMainThread(chunk, processor)
          .then(resolve)
          .catch(reject)
        return
      }

      const timeout = setTimeout(() => {
        worker.terminate()
        reject(new Error(`Worker处理超时: ${chunk.id}`))
      }, 30000) // 30秒超时

      worker.onmessage = (e) => {
        clearTimeout(timeout)
        const { success, result, error } = e.data
        
        if (success) {
          resolve(result)
        } else {
          reject(new Error(error))
        }
      }

      worker.onerror = (error) => {
        clearTimeout(timeout)
        reject(error)
      }

      worker.postMessage({
        id: chunk.id,
        data: chunk.data,
        operation: 'custom',
        processor: processor.toString(),
        startTime: Date.now()
      })
    })
  }

  /**
   * 在主线程中处理分片
   */
  private async processChunkInMainThread<T, R>(
    chunk: DataChunk<T>,
    processor: (chunk: T[]) => R[] | Promise<R[]>
  ): Promise<R[]> {
    return await processor(chunk.data)
  }

  /**
   * 获取可用的Worker
   */
  private getAvailableWorker(): Worker | null {
    // 简单的轮询策略
    const workerIndex = this.metrics.totalProcessed % this.workers.length
    return this.workers[workerIndex] || null
  }

  /**
   * 合并处理结果
   */
  private mergeResults<R>(results: R[][]): R[] {
    return results.flat()
  }

  /**
   * 生成缓存键
   */
  private generateCacheKey(data: any[], processorString: string, config: ProcessingConfig): string {
    const dataHash = this.simpleHash(JSON.stringify(data.slice(0, 100))) // 只对前100项做hash
    const processorHash = this.simpleHash(processorString)
    const configHash = this.simpleHash(JSON.stringify(config))
    
    return `${dataHash}-${processorHash}-${configHash}`
  }

  /**
   * 简单哈希函数
   */
  private simpleHash(str: string): string {
    let hash = 0
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // 转换为32位整数
    }
    return hash.toString(36)
  }

  /**
   * 缓存结果
   */
  private cacheResult(key: string, result: any): void {
    const resultSize = this.estimateSize(result)
    
    // 检查缓存大小限制
    if (resultSize > this.maxCacheSize / 10) {
      console.warn('结果太大，跳过缓存')
      return
    }

    // 如果缓存满了，清理最旧的项
    while (this.getCacheSize() + resultSize > this.maxCacheSize && this.cache.size > 0) {
      const firstKey = this.cache.keys().next().value
      if (firstKey) {
        this.cache.delete(firstKey)
      } else {
        break
      }
    }

    this.cache.set(key, result)
    
    // 注册到内存管理器
    memoryManager.registerResource(key, 'data', resultSize, () => {
      this.cache.delete(key)
    }, { type: 'processedData', cacheKey: key })
  }

  /**
   * 获取缓存大小
   */
  private getCacheSize(): number {
    let size = 0
    for (const value of this.cache.values()) {
      size += this.estimateSize(value)
    }
    return size
  }

  /**
   * 估算对象大小
   */
  private estimateSize(obj: any): number {
    try {
      return JSON.stringify(obj).length * 2 // 粗略估算
    } catch {
      return 1024 // 默认1KB
    }
  }

  /**
   * 更新缓存命中率
   */
  private updateCacheHitRate(hit: boolean): void {
    const total = this.metrics.totalProcessed + 1
    const currentHits = this.metrics.cacheHitRate * this.metrics.totalProcessed
    this.metrics.cacheHitRate = (currentHits + (hit ? 1 : 0)) / total
  }

  /**
   * 更新性能指标
   */
  private updateMetrics(processingTime: number, chunkCount: number, errors: string[]): void {
    this.metrics.totalProcessed++
    
    const total = this.metrics.totalProcessed
    this.metrics.averageProcessingTime = 
      (this.metrics.averageProcessingTime * (total - 1) + processingTime) / total

    if (errors.length > 0) {
      this.metrics.errorRate = 
        (this.metrics.errorRate * (total - 1) + 1) / total
    }

    this.metrics.memoryUsage = this.getCacheSize()
    this.metrics.workerUtilization = this.processingInProgress.size / this.maxWorkers
    this.metrics.throughput = this.metrics.totalProcessed / 
      (Date.now() - (this.metrics.totalProcessed * this.metrics.averageProcessingTime))
  }

  /**
   * 开始性能指标收集
   */
  private startMetricsCollection(): void {
    setInterval(() => {
      this.logPerformanceMetrics()
    }, 60000) // 每分钟记录一次
  }

  /**
   * 记录性能指标
   */
  private logPerformanceMetrics(): void {
    if (this.metrics.totalProcessed > 0) {
      console.log('📊 数据处理性能指标:', {
        totalProcessed: this.metrics.totalProcessed,
        avgProcessingTime: `${this.metrics.averageProcessingTime.toFixed(2)}ms`,
        cacheHitRate: `${(this.metrics.cacheHitRate * 100).toFixed(1)}%`,
        errorRate: `${(this.metrics.errorRate * 100).toFixed(1)}%`,
        memoryUsage: `${(this.metrics.memoryUsage / 1024 / 1024).toFixed(1)}MB`,
        workerUtilization: `${(this.metrics.workerUtilization * 100).toFixed(1)}%`
      })
    }
  }

  /**
   * 设置清理机制
   */
  private setupCleanup(): void {
    // 页面卸载时清理
    window.addEventListener('beforeunload', () => {
      this.cleanup()
    })

    // 内存压力时清理缓存
    setInterval(() => {
      if (this.getCacheSize() > this.maxCacheSize * 0.8) {
        this.clearOldCache()
      }
    }, 30000)
  }

  /**
   * 清理旧缓存
   */
  private clearOldCache(): void {
    const entries = Array.from(this.cache.entries())
    const toDelete = Math.floor(entries.length * 0.3) // 删除30%的缓存
    
    for (let i = 0; i < toDelete; i++) {
      const key = entries[i][0]
      this.cache.delete(key)
      memoryManager.unregisterResource(key)
    }

    console.log(`🧹 清理了 ${toDelete} 个缓存项`)
  }

  /**
   * 获取性能指标
   */
  getMetrics(): DataProcessorMetrics {
    return { ...this.metrics }
  }

  /**
   * 获取优化建议
   */
  getOptimizationSuggestions(): string[] {
    const suggestions: string[] = []

    if (this.metrics.cacheHitRate < 0.3) {
      suggestions.push('缓存命中率较低，考虑调整缓存策略或增加缓存大小')
    }

    if (this.metrics.averageProcessingTime > 1000) {
      suggestions.push('处理时间较长，考虑减小分片大小或增加并发数')
    }

    if (this.metrics.errorRate > 0.05) {
      suggestions.push('错误率过高，检查数据处理逻辑')
    }

    if (this.workers.length === 0) {
      suggestions.push('Web Workers不可用，考虑优化主线程处理逻辑')
    }

    if (this.metrics.memoryUsage > this.maxCacheSize * 0.8) {
      suggestions.push('缓存使用率较高，考虑清理旧缓存或增加缓存大小')
    }

    return suggestions
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    // 终止所有Workers
    this.workers.forEach(worker => worker.terminate())
    this.workers = []

    // 清理缓存
    this.cache.clear()

    // 清理处理队列
    this.processingQueue = []
    this.processingInProgress.clear()

    console.log('✅ 数据处理器清理完成')
  }
}

/**
 * 信号量实现，用于控制并发
 */
class Semaphore {
  private permits: number
  private waitQueue: (() => void)[] = []

  constructor(permits: number) {
    this.permits = permits
  }

  async acquire(): Promise<void> {
    return new Promise(resolve => {
      if (this.permits > 0) {
        this.permits--
        resolve()
      } else {
        this.waitQueue.push(resolve)
      }
    })
  }

  release(): void {
    this.permits++
    if (this.waitQueue.length > 0) {
      const resolve = this.waitQueue.shift()!
      this.permits--
      resolve()
    }
  }
}

// 单例实例
export const dataProcessor = DataProcessor.getInstance()

// React Hooks
export function useDataProcessor<T, R>() {
  const [isProcessing, setIsProcessing] = React.useState(false)
  const [progress, setProgress] = React.useState(0)
  const [result, setResult] = React.useState<ProcessingResult<T, R> | null>(null)
  const [error, setError] = React.useState<string | null>(null)

  const processData = React.useCallback(async (
    data: T[],
    processor: (chunk: T[]) => R[] | Promise<R[]>,
    config?: Partial<ProcessingConfig>
  ) => {
    setIsProcessing(true)
    setProgress(0)
    setResult(null)
    setError(null)

    try {
      const result = await dataProcessor.processLargeDataset(data, processor, config)
      setResult(result)
      setProgress(100)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : String(err)
      setError(errorMessage)
    } finally {
      setIsProcessing(false)
    }
  }, [])

  return {
    processData,
    isProcessing,
    progress,
    result,
    error,
    metrics: dataProcessor.getMetrics()
  }
}

// 工具函数
export function createVirtualizedList<T>(
  items: T[],
  itemHeight: number,
  containerHeight: number,
  renderItem: (item: T, index: number) => React.ReactNode
) {
  return function VirtualizedList({ scrollTop }: { scrollTop: number }) {
    const startIndex = Math.floor(scrollTop / itemHeight)
    const endIndex = Math.min(
      startIndex + Math.ceil(containerHeight / itemHeight) + 1,
      items.length
    )

    const visibleItems = items.slice(startIndex, endIndex)
    const offsetY = startIndex * itemHeight

    return React.createElement('div', 
      { style: { transform: `translateY(${offsetY}px)` } },
      visibleItems.map((item, index) => 
        renderItem(item, startIndex + index)
      )
    )
  }
}

export function batchProcess<T>(
  items: T[],
  processor: (item: T) => void,
  batchSize: number = 100,
  delay: number = 16
): Promise<void> {
  return new Promise((resolve) => {
    let index = 0

    function processBatch() {
      const endIndex = Math.min(index + batchSize, items.length)
      
      for (let i = index; i < endIndex; i++) {
        processor(items[i])
      }

      index = endIndex

      if (index < items.length) {
        setTimeout(processBatch, delay)
      } else {
        resolve()
      }
    }

    processBatch()
  })
} 