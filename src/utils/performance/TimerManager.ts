import React from 'react'
import { memoryManager } from './MemoryManager'

/**
 * 定时器管理器
 * 企业级定时器管理系统，实现智能定时器池、生命周期管理、性能监控
 */

export interface TimerConfig {
  id?: string
  type: 'timeout' | 'interval' | 'animationFrame' | 'idleCallback'
  delay: number
  callback: () => void
  immediate?: boolean
  maxExecutions?: number
  priority?: 'low' | 'normal' | 'high' | 'critical'
  context?: any
}

export interface TimerInfo {
  id: string
  type: TimerConfig['type']
  delay: number
  startTime: number
  lastExecution: number
  executionCount: number
  maxExecutions?: number
  priority: TimerConfig['priority']
  isActive: boolean
  isPaused: boolean
  nextExecution: number
  avgExecutionTime: number
  totalExecutionTime: number
  errors: number
}

export interface TimerMetrics {
  totalTimers: number
  activeTimers: number
  pausedTimers: number
  avgExecutionTime: number
  totalExecutions: number
  errorRate: number
  memoryUsage: number
  cpuUsage: number
}

export class TimerManager {
  private static instance: TimerManager
  private timers = new Map<string, TimerInfo>()
  private callbacks = new Map<string, () => void>()
  private nativeIds = new Map<string, number>()
  private pausedTimers = new Map<string, { remainingTime: number; pausedAt: number }>()
  
  // 性能监控
  private metrics: TimerMetrics = {
    totalTimers: 0,
    activeTimers: 0,
    pausedTimers: 0,
    avgExecutionTime: 0,
    totalExecutions: 0,
    errorRate: 0,
    memoryUsage: 0,
    cpuUsage: 0
  }

  // 优先级队列
  private priorityQueues = {
    critical: [] as string[],
    high: [] as string[],
    normal: [] as string[],
    low: [] as string[]
  }

  // 配置
  private maxConcurrentTimers = 50
  private maxExecutionTime = 16 // 16ms threshold
  private cleanupInterval = 60000 // 1分钟清理一次
  private isThrottling = false

  private constructor() {
    this.startMetricsCollection()
    this.startPeriodicCleanup()
    this.setupVisibilityHandling()
  }

  static getInstance(): TimerManager {
    if (!TimerManager.instance) {
      TimerManager.instance = new TimerManager()
    }
    return TimerManager.instance
  }

  /**
   * 创建定时器
   */
  createTimer(config: TimerConfig): string {
    const id = config.id || `timer-${Date.now()}-${Math.random()}`
    
    // 检查并发限制
    if (this.metrics.activeTimers >= this.maxConcurrentTimers) {
      console.warn(`⚠️ 定时器数量达到限制 (${this.maxConcurrentTimers})，请考虑优化`)
      this.throttleTimers()
    }

    const timerInfo: TimerInfo = {
      id,
      type: config.type,
      delay: config.delay,
      startTime: Date.now(),
      lastExecution: 0,
      executionCount: 0,
      maxExecutions: config.maxExecutions,
      priority: config.priority || 'normal',
      isActive: true,
      isPaused: false,
      nextExecution: Date.now() + config.delay,
      avgExecutionTime: 0,
      totalExecutionTime: 0,
      errors: 0
    }

    this.timers.set(id, timerInfo)
    this.callbacks.set(id, config.callback)

    // 添加到优先级队列
    const priority = timerInfo.priority || 'normal'
    this.priorityQueues[priority].push(id)

    // 创建实际的定时器
    this.createNativeTimer(id, config)

    // 注册到内存管理器
    memoryManager.registerResource(id, 'timer', this.estimateTimerMemory(config), () => {
      this.removeTimer(id)
    }, {
      type: config.type,
      delay: config.delay,
      priority: config.priority
    })

    // 立即执行（如果配置）
    if (config.immediate && config.type === 'interval') {
      this.executeCallback(id)
    }

    this.updateMetrics()
    return id
  }

  /**
   * 创建原生定时器
   */
  private createNativeTimer(id: string, config: TimerConfig): void {
    const wrappedCallback = () => this.executeCallback(id)

    let nativeId: number

    switch (config.type) {
      case 'timeout':
        nativeId = window.setTimeout(wrappedCallback, config.delay)
        break
      case 'interval':
        nativeId = window.setInterval(wrappedCallback, config.delay)
        break
      case 'animationFrame':
        const rafCallback = () => {
          this.executeCallback(id)
          if (this.timers.get(id)?.isActive) {
            this.nativeIds.set(id, requestAnimationFrame(rafCallback))
          }
        }
        nativeId = requestAnimationFrame(rafCallback)
        break
      case 'idleCallback':
        if ('requestIdleCallback' in window) {
          const idleCallback = (deadline: IdleDeadline) => {
            if (deadline.timeRemaining() > 0) {
              this.executeCallback(id)
            }
            if (this.timers.get(id)?.isActive) {
              this.nativeIds.set(id, (window as any).requestIdleCallback(idleCallback))
            }
          }
          nativeId = (window as any).requestIdleCallback(idleCallback)
        } else {
          // 回退到 setTimeout
          nativeId = window.setTimeout(wrappedCallback, config.delay)
        }
        break
      default:
        nativeId = window.setTimeout(wrappedCallback, config.delay)
    }

    this.nativeIds.set(id, nativeId)
  }

  /**
   * 执行回调函数
   */
  private executeCallback(id: string): void {
    const timer = this.timers.get(id)
    const callback = this.callbacks.get(id)

    if (!timer || !callback || !timer.isActive || timer.isPaused) {
      return
    }

    const startTime = performance.now()

    try {
      callback()
      
      // 更新执行统计
      const executionTime = performance.now() - startTime
      timer.executionCount++
      timer.lastExecution = Date.now()
      timer.totalExecutionTime += executionTime
      timer.avgExecutionTime = timer.totalExecutionTime / timer.executionCount

      // 性能警告
      if (executionTime > this.maxExecutionTime) {
        console.warn(`⚠️ 定时器 ${id} 执行时间过长: ${executionTime.toFixed(2)}ms`)
      }

      // 检查是否达到最大执行次数
      if (timer.maxExecutions && timer.executionCount >= timer.maxExecutions) {
        this.removeTimer(id)
        return
      }

      // 更新下次执行时间
      if (timer.type === 'interval') {
        timer.nextExecution = Date.now() + timer.delay
      }

    } catch (error) {
      console.error(`❌ 定时器 ${id} 执行错误:`, error)
      timer.errors++
      
      // 错误率过高时自动移除
      if (timer.errors > 5 && timer.errors / timer.executionCount > 0.1) {
        console.warn(`🚫 定时器 ${id} 错误率过高，自动移除`)
        this.removeTimer(id)
        return
      }
    }

    // 对于 timeout 类型，执行完成后自动清理
    if (timer.type === 'timeout') {
      this.removeTimer(id)
    }

    this.updateMetrics()
  }

  /**
   * 暂停定时器
   */
  pauseTimer(id: string): boolean {
    const timer = this.timers.get(id)
    if (!timer || !timer.isActive || timer.isPaused) {
      return false
    }

    const nativeId = this.nativeIds.get(id)
    if (nativeId !== undefined) {
      this.clearNativeTimer(timer.type, nativeId)
    }

    const remainingTime = Math.max(0, timer.nextExecution - Date.now())
    this.pausedTimers.set(id, {
      remainingTime,
      pausedAt: Date.now()
    })

    timer.isPaused = true
    this.updateMetrics()
    
    return true
  }

  /**
   * 恢复定时器
   */
  resumeTimer(id: string): boolean {
    const timer = this.timers.get(id)
    const pausedInfo = this.pausedTimers.get(id)
    
    if (!timer || !timer.isActive || !timer.isPaused || !pausedInfo) {
      return false
    }

    timer.isPaused = false
    this.pausedTimers.delete(id)

    // 重新创建定时器
    const config: TimerConfig = {
      id,
      type: timer.type,
      delay: pausedInfo.remainingTime,
      callback: this.callbacks.get(id)!,
      priority: timer.priority
    }

    this.createNativeTimer(id, config)
    this.updateMetrics()
    
    return true
  }

  /**
   * 移除定时器
   */
  removeTimer(id: string): boolean {
    const timer = this.timers.get(id)
    if (!timer) {
      return false
    }

    // 清理原生定时器
    const nativeId = this.nativeIds.get(id)
    if (nativeId !== undefined) {
      this.clearNativeTimer(timer.type, nativeId)
    }

    // 从各种映射中移除
    this.timers.delete(id)
    this.callbacks.delete(id)
    this.nativeIds.delete(id)
    this.pausedTimers.delete(id)

    // 从优先级队列中移除
    const priority = timer.priority || 'normal'
    const queue = this.priorityQueues[priority]
    const index = queue.indexOf(id)
    if (index > -1) {
      queue.splice(index, 1)
    }

    // 从内存管理器注销
    memoryManager.unregisterResource(id)

    this.updateMetrics()
    return true
  }

  /**
   * 清理原生定时器
   */
  private clearNativeTimer(type: TimerConfig['type'], nativeId: number): void {
    switch (type) {
      case 'timeout':
        clearTimeout(nativeId)
        break
      case 'interval':
        clearInterval(nativeId)
        break
      case 'animationFrame':
        cancelAnimationFrame(nativeId)
        break
      case 'idleCallback':
        if ('cancelIdleCallback' in window) {
          (window as any).cancelIdleCallback(nativeId)
        } else {
          clearTimeout(nativeId)
        }
        break
    }
  }

  /**
   * 节流定时器
   */
  private throttleTimers(): void {
    if (this.isThrottling) return

    this.isThrottling = true
    console.log('🔄 开始定时器节流模式')

    // 暂停低优先级定时器
    const lowPriorityTimers = this.priorityQueues.low.slice(0, 10)
    lowPriorityTimers.forEach(id => this.pauseTimer(id))

    // 5秒后恢复
    window.setTimeout(() => {
      lowPriorityTimers.forEach(id => this.resumeTimer(id))
      this.isThrottling = false
      console.log('✅ 定时器节流模式结束')
    }, 5000)
  }

  /**
   * 估算定时器内存使用
   */
  private estimateTimerMemory(config: TimerConfig): number {
    const baseMemory = 1024 // 1KB 基础内存
    const callbackMemory = config.callback.toString().length * 2 // 回调函数内存
    const contextMemory = config.context ? JSON.stringify(config.context).length * 2 : 0
    
    return baseMemory + callbackMemory + contextMemory
  }

  /**
   * 更新性能指标
   */
  private updateMetrics(): void {
    this.metrics.totalTimers = this.timers.size
    this.metrics.activeTimers = Array.from(this.timers.values()).filter(t => t.isActive && !t.isPaused).length
    this.metrics.pausedTimers = Array.from(this.timers.values()).filter(t => t.isPaused).length

    const allTimers = Array.from(this.timers.values())
    if (allTimers.length > 0) {
      this.metrics.avgExecutionTime = allTimers.reduce((sum, t) => sum + t.avgExecutionTime, 0) / allTimers.length
      this.metrics.totalExecutions = allTimers.reduce((sum, t) => sum + t.executionCount, 0)
      const totalErrors = allTimers.reduce((sum, t) => sum + t.errors, 0)
      this.metrics.errorRate = this.metrics.totalExecutions > 0 ? totalErrors / this.metrics.totalExecutions : 0
    }

    // 估算内存使用（每个定时器约1KB）
    this.metrics.memoryUsage = this.timers.size * 1024
  }

  /**
   * 开始性能指标收集
   */
  private startMetricsCollection(): void {
    setInterval(() => {
      this.updateMetrics()
      this.logPerformanceWarnings()
    }, 30000) // 每30秒更新一次
  }

  /**
   * 记录性能警告
   */
  private logPerformanceWarnings(): void {
    if (this.metrics.activeTimers > this.maxConcurrentTimers * 0.8) {
      console.warn(`⚠️ 定时器数量接近限制: ${this.metrics.activeTimers}/${this.maxConcurrentTimers}`)
    }

    if (this.metrics.avgExecutionTime > this.maxExecutionTime) {
      console.warn(`⚠️ 定时器平均执行时间过长: ${this.metrics.avgExecutionTime.toFixed(2)}ms`)
    }

    if (this.metrics.errorRate > 0.05) {
      console.warn(`⚠️ 定时器错误率过高: ${(this.metrics.errorRate * 100).toFixed(1)}%`)
    }
  }

  /**
   * 定期清理
   */
  private startPeriodicCleanup(): void {
    setInterval(() => {
      this.cleanupExpiredTimers()
    }, this.cleanupInterval)
  }

  /**
   * 清理过期定时器
   */
  private cleanupExpiredTimers(): void {
    const now = Date.now()
    const expiredTimers: string[] = []

    for (const [id, timer] of this.timers) {
      // 清理长时间未执行的定时器
      if (timer.type === 'timeout' && now - timer.startTime > timer.delay * 2) {
        expiredTimers.push(id)
      }

      // 清理错误率过高的定时器
      if (timer.executionCount > 10 && timer.errors / timer.executionCount > 0.2) {
        expiredTimers.push(id)
      }
    }

    expiredTimers.forEach(id => {
      console.log(`🧹 清理过期定时器: ${id}`)
      this.removeTimer(id)
    })

    if (expiredTimers.length > 0) {
      console.log(`✅ 清理了 ${expiredTimers.length} 个过期定时器`)
    }
  }

  /**
   * 处理页面可见性变化
   */
  private setupVisibilityHandling(): void {
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        // 页面隐藏时暂停低优先级定时器
        this.priorityQueues.low.forEach(id => this.pauseTimer(id))
        console.log('📱 页面隐藏，暂停低优先级定时器')
      } else {
        // 页面可见时恢复定时器
        this.priorityQueues.low.forEach(id => this.resumeTimer(id))
        console.log('📱 页面可见，恢复定时器')
      }
    })
  }

  /**
   * 获取定时器信息
   */
  getTimerInfo(id: string): TimerInfo | undefined {
    return this.timers.get(id)
  }

  /**
   * 获取所有定时器信息
   */
  getAllTimers(): TimerInfo[] {
    return Array.from(this.timers.values())
  }

  /**
   * 获取性能指标
   */
  getMetrics(): TimerMetrics {
    this.updateMetrics()
    return { ...this.metrics }
  }

  /**
   * 获取优化建议
   */
  getOptimizationSuggestions(): string[] {
    const suggestions: string[] = []

    if (this.metrics.activeTimers > this.maxConcurrentTimers * 0.7) {
      suggestions.push(`考虑合并或减少定时器数量，当前: ${this.metrics.activeTimers}`)
    }

    if (this.metrics.avgExecutionTime > this.maxExecutionTime) {
      suggestions.push(`优化定时器回调函数，平均执行时间: ${this.metrics.avgExecutionTime.toFixed(2)}ms`)
    }

    if (this.metrics.errorRate > 0.02) {
      suggestions.push(`检查定时器错误处理，错误率: ${(this.metrics.errorRate * 100).toFixed(1)}%`)
    }

    const intervalCount = Array.from(this.timers.values()).filter(t => t.type === 'interval').length
    if (intervalCount > 10) {
      suggestions.push(`考虑使用 requestAnimationFrame 替代部分 setInterval`)
    }

    return suggestions
  }

  /**
   * 清理所有定时器
   */
  cleanup(): void {
    for (const id of this.timers.keys()) {
      this.removeTimer(id)
    }
    
    console.log('✅ 定时器管理器清理完成')
  }
}

// 单例实例
export const timerManager = TimerManager.getInstance()

// React Hooks
export function useTimer(
  callback: () => void,
  delay: number,
  type: TimerConfig['type'] = 'timeout',
  options?: {
    immediate?: boolean
    maxExecutions?: number
    priority?: TimerConfig['priority']
    enabled?: boolean
  }
) {
  const callbackRef = React.useRef(callback)
  const timerIdRef = React.useRef<string | null>(null)

  // 更新回调引用
  React.useEffect(() => {
    callbackRef.current = callback
  }, [callback])

  React.useEffect(() => {
    if (options?.enabled === false) {
      if (timerIdRef.current) {
        timerManager.removeTimer(timerIdRef.current)
        timerIdRef.current = null
      }
      return
    }

    const config: TimerConfig = {
      type,
      delay,
      callback: () => callbackRef.current(),
      immediate: options?.immediate,
      maxExecutions: options?.maxExecutions,
      priority: options?.priority || 'normal'
    }

    timerIdRef.current = timerManager.createTimer(config)

    return () => {
      if (timerIdRef.current) {
        timerManager.removeTimer(timerIdRef.current)
        timerIdRef.current = null
      }
    }
  }, [delay, type, options?.immediate, options?.maxExecutions, options?.priority, options?.enabled])

  const pause = React.useCallback(() => {
    if (timerIdRef.current) {
      return timerManager.pauseTimer(timerIdRef.current)
    }
    return false
  }, [])

  const resume = React.useCallback(() => {
    if (timerIdRef.current) {
      return timerManager.resumeTimer(timerIdRef.current)
    }
    return false
  }, [])

  const reset = React.useCallback(() => {
    if (timerIdRef.current) {
      timerManager.removeTimer(timerIdRef.current)
    }
    
    const config: TimerConfig = {
      type,
      delay,
      callback: () => callbackRef.current(),
      immediate: options?.immediate,
      maxExecutions: options?.maxExecutions,
      priority: options?.priority || 'normal'
    }

    timerIdRef.current = timerManager.createTimer(config)
  }, [delay, type, options?.immediate, options?.maxExecutions, options?.priority])

  return { pause, resume, reset }
}

export function useInterval(
  callback: () => void,
  delay: number,
  options?: {
    immediate?: boolean
    maxExecutions?: number
    priority?: TimerConfig['priority']
    enabled?: boolean
  }
) {
  return useTimer(callback, delay, 'interval', options)
}

export function useAnimationFrame(
  callback: () => void,
  options?: {
    priority?: TimerConfig['priority']
    enabled?: boolean
  }
) {
  return useTimer(callback, 0, 'animationFrame', options)
}

export function useIdleCallback(
  callback: () => void,
  options?: {
    priority?: TimerConfig['priority']
    enabled?: boolean
  }
) {
  return useTimer(callback, 0, 'idleCallback', options)
} 