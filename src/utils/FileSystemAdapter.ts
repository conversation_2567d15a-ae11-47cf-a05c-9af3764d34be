import { platformDetector, OperatingSystem } from './PlatformDetector'

// 文件系统类型定义
export enum FileSystemType {
  WEB = 'web',           
  ELECTRON = 'electron', 
  CORDOVA = 'cordova',   
  NODE = 'node',         
  UNKNOWN = 'unknown'
}

export enum PathType {
  ABSOLUTE = 'absolute',
  RELATIVE = 'relative',
  URL = 'url',
  BLOB = 'blob',
  DATA = 'data'
}

export interface PathInfo {
  type: PathType
  isAbsolute: boolean
  isValid: boolean
  platform: OperatingSystem
  separator: string
  parts: string[]
  directory: string
  filename: string
  extension: string
  basename: string
  root: string
  normalized: string
}

// 平台特定路径配置
const PLATFORM_PATH_CONFIG = {
  [OperatingSystem.WINDOWS]: {
    separator: '\\',
    caseSensitive: false,
    maxPathLength: 260,
    invalidChars: ['<', '>', ':', '"', '|', '?', '*'],
    reservedNames: ['CON', 'PRN', 'AUX', 'NUL'],
    drivePattern: /^[A-Za-z]:/
  },
  [OperatingSystem.MACOS]: {
    separator: '/',
    caseSensitive: false,
    maxPathLength: 1024,
    invalidChars: [':'],
    reservedNames: [],
    drivePattern: null
  },
  [OperatingSystem.LINUX]: {
    separator: '/',
    caseSensitive: true,
    maxPathLength: 4096,
    invalidChars: [],
    reservedNames: [],
    drivePattern: null
  },
  [OperatingSystem.IOS]: {
    separator: '/',
    caseSensitive: true,
    maxPathLength: 255,
    invalidChars: [],
    reservedNames: [],
    drivePattern: null
  },
  [OperatingSystem.ANDROID]: {
    separator: '/',
    caseSensitive: true,
    maxPathLength: 4096,
    invalidChars: [],
    reservedNames: [],
    drivePattern: null
  },
  [OperatingSystem.UNKNOWN]: {
    separator: '/',
    caseSensitive: true,
    maxPathLength: 255,
    invalidChars: [],
    reservedNames: [],
    drivePattern: null
  }
}

export class FileSystemAdapter {
  private static instance: FileSystemAdapter
  private fsType: FileSystemType = FileSystemType.UNKNOWN

  private constructor() {
    this.detectFileSystemType()
  }

  public static getInstance(): FileSystemAdapter {
    if (!FileSystemAdapter.instance) {
      FileSystemAdapter.instance = new FileSystemAdapter()
    }
    return FileSystemAdapter.instance
  }

  /**
   * 检测文件系统类型
   */
  private detectFileSystemType(): void {
    if (typeof window === 'undefined') {
      this.fsType = FileSystemType.NODE
      return
    }

    if ((window as any).require || (window as any).electron) {
      this.fsType = FileSystemType.ELECTRON
      return
    }

    if ((window as any).cordova || (window as any).PhoneGap) {
      this.fsType = FileSystemType.CORDOVA
      return
    }

    this.fsType = FileSystemType.WEB
  }

  /**
   * 规范化路径
   */
  public normalizePath(path: string, targetOS?: OperatingSystem): string {
    if (!path) {
      return ''
    }

    const platform = targetOS || platformDetector.getPlatformInfo().os
    const config = PLATFORM_PATH_CONFIG[platform]

    let normalized = path
    if (platform === OperatingSystem.WINDOWS) {
      normalized = normalized.replace(/\//g, '\\')
    } else {
      normalized = normalized.replace(/\\/g, '/')
    }

    // 移除重复的分隔符
    const separator = config.separator
    if (separator === '\\') {
      normalized = normalized.replace(/\\+/g, '\\')
    } else {
      normalized = normalized.replace(/\/+/g, '/')
    }

    return normalized
  }

  /**
   * 解析路径信息
   */
  public parsePath(path: string): PathInfo {
    const platform = platformDetector.getPlatformInfo().os
    const config = PLATFORM_PATH_CONFIG[platform]
    const normalized = this.normalizePath(path)

    let pathType: PathType = PathType.RELATIVE
    let isAbsolute = false

    if (normalized.startsWith('http://') || normalized.startsWith('https://')) {
      pathType = PathType.URL
      isAbsolute = true
    } else if (normalized.startsWith('blob:')) {
      pathType = PathType.BLOB
      isAbsolute = true
    } else if (normalized.startsWith('data:')) {
      pathType = PathType.DATA
      isAbsolute = true
    } else if (platform === OperatingSystem.WINDOWS) {
      if (config.drivePattern?.test(normalized)) {
        pathType = PathType.ABSOLUTE
        isAbsolute = true
      }
    } else {
      if (normalized.startsWith('/')) {
        pathType = PathType.ABSOLUTE
        isAbsolute = true
      }
    }

    const separator = config.separator
    const parts = normalized.split(separator).filter(part => part !== '')
    
    const lastPart = parts[parts.length - 1] || ''
    const dotIndex = lastPart.lastIndexOf('.')
    const filename = lastPart
    const extension = dotIndex > 0 ? lastPart.substring(dotIndex) : ''
    const basename = dotIndex > 0 ? lastPart.substring(0, dotIndex) : lastPart

    const directoryParts = parts.slice(0, -1)
    let directory = directoryParts.join(separator)
    if (isAbsolute && platform !== OperatingSystem.WINDOWS) {
      directory = separator + directory
    }

    let root = ''
    if (platform === OperatingSystem.WINDOWS) {
      if (config.drivePattern?.test(normalized)) {
        root = normalized.substring(0, 2) + separator
      }
    } else if (isAbsolute) {
      root = separator
    }

    const isValid = this.validatePath(normalized)

    return {
      type: pathType,
      isAbsolute,
      isValid,
      platform,
      separator,
      parts,
      directory,
      filename,
      extension,
      basename,
      root,
      normalized
    }
  }

  /**
   * 验证路径
   */
  public validatePath(path: string): boolean {
    if (!path) {
      return false
    }

    const platform = platformDetector.getPlatformInfo().os
    const config = PLATFORM_PATH_CONFIG[platform]

    if (path.length > config.maxPathLength) {
      return false
    }

    for (const char of config.invalidChars) {
      if (path.includes(char)) {
        return false
      }
    }

    if (platform === OperatingSystem.WINDOWS) {
      const parts = path.split(/[\\/]/)
      for (const part of parts) {
        const baseName = part.split('.')[0].toUpperCase()
        if (config.reservedNames.includes(baseName)) {
          return false
        }
      }
    }

    return true
  }

  /**
   * 连接路径
   */
  public joinPath(...paths: string[]): string {
    if (paths.length === 0) {
      return ''
    }

    const platform = platformDetector.getPlatformInfo().os
    const config = PLATFORM_PATH_CONFIG[platform]
    const separator = config.separator

    const validPaths = paths.filter(p => p && p.trim() !== '')
    if (validPaths.length === 0) {
      return ''
    }

    let startIndex = 0
    for (let i = validPaths.length - 1; i >= 0; i--) {
      const pathInfo = this.parsePath(validPaths[i])
      if (pathInfo.isAbsolute) {
        startIndex = i
        break
      }
    }

    const pathsToJoin = validPaths.slice(startIndex)
    let joined = pathsToJoin.join(separator)

    return this.normalizePath(joined)
  }

  /**
   * 转换路径格式
   */
  public convertPath(path: string, targetOS: OperatingSystem): string {
    const sourceInfo = this.parsePath(path)
    
    if (sourceInfo.platform === targetOS) {
      return path
    }

    const targetConfig = PLATFORM_PATH_CONFIG[targetOS]
    const parts = sourceInfo.parts

    if (sourceInfo.isAbsolute) {
      if (targetOS === OperatingSystem.WINDOWS) {
        return 'C:' + targetConfig.separator + parts.join(targetConfig.separator)
      } else {
        return targetConfig.separator + parts.join(targetConfig.separator)
      }
    } else {
      return parts.join(targetConfig.separator)
    }
  }

  /**
   * 获取文件系统类型
   */
  public getFileSystemType(): FileSystemType {
    return this.fsType
  }

  /**
   * 获取平台路径配置
   */
  public getPlatformConfig(platform?: OperatingSystem) {
    const targetPlatform = platform || platformDetector.getPlatformInfo().os
    return PLATFORM_PATH_CONFIG[targetPlatform]
  }
}

// 导出单例实例
export const fileSystemAdapter = FileSystemAdapter.getInstance()

// 便捷函数
export const normalizePath = (path: string, targetOS?: OperatingSystem) => fileSystemAdapter.normalizePath(path, targetOS)
export const parsePath = (path: string) => fileSystemAdapter.parsePath(path)
export const validatePath = (path: string) => fileSystemAdapter.validatePath(path)
export const joinPath = (...paths: string[]) => fileSystemAdapter.joinPath(...paths)
export const convertPath = (path: string, targetOS: OperatingSystem) => fileSystemAdapter.convertPath(path, targetOS)
export const getFileSystemType = () => fileSystemAdapter.getFileSystemType() 