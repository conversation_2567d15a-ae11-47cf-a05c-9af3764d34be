import { platformDetector, OperatingSystem, DeviceType } from './PlatformDetector';

// 响应式设计类型定义
export enum ScreenSize {
  MOBILE = 'mobile',
  TABLET = 'tablet', 
  DESKTOP = 'desktop',
  LARGE = 'large',
  ULTRA_WIDE = 'ultraWide'
}

export enum Orientation {
  PORTRAIT = 'portrait',
  LANDSCAPE = 'landscape'
}

export enum UITheme {
  LIGHT = 'light',
  DARK = 'dark',
  AUTO = 'auto',
  HIGH_CONTRAST = 'highContrast'
}

export interface ViewportInfo {
  width: number;
  height: number;
  screenSize: ScreenSize;
  orientation: Orientation;
  pixelRatio: number;
  availableWidth: number;
  availableHeight: number;
}

export interface ResponsiveBreakpoints {
  mobile: number;
  tablet: number;
  desktop: number;
  large: number;
  ultraWide: number;
}

export interface ResponsiveConfig {
  breakpoints: ResponsiveBreakpoints;
  scalingFactor: number;
  minFontSize: number;
  maxFontSize: number;
  touchTargetSize: number;
  animations: boolean;
  reducedMotion: boolean;
  theme: UITheme;
}

export interface TouchCapabilities {
  supported: boolean;
  maxTouchPoints: number;
  multiTouch: boolean;
  pressure: boolean;
}

export interface AdaptationSuggestions {
  layout: string[];
  typography: string[];
  interaction: string[];
  performance: string[];
  accessibility: string[];
}

// 默认断点配置
const DEFAULT_BREAKPOINTS: ResponsiveBreakpoints = {
  mobile: 768,
  tablet: 1024,
  desktop: 1440,
  large: 1920,
  ultraWide: 2560
};

// 平台特定配置
const PLATFORM_UI_CONFIG = {
  [OperatingSystem.WINDOWS]: {
    scrollbarWidth: 17,
    minTouchTarget: 40,
    borderRadius: 4,
    fontFamily: 'Segoe UI, system-ui, sans-serif'
  },
  [OperatingSystem.MACOS]: {
    scrollbarWidth: 15,
    minTouchTarget: 44,
    borderRadius: 8,
    fontFamily: '-apple-system, BlinkMacSystemFont, sans-serif'
  },
  [OperatingSystem.LINUX]: {
    scrollbarWidth: 16,
    minTouchTarget: 40,
    borderRadius: 4,
    fontFamily: 'system-ui, Ubuntu, Roboto, sans-serif'
  },
  [OperatingSystem.IOS]: {
    scrollbarWidth: 0,
    minTouchTarget: 44,
    borderRadius: 12,
    fontFamily: '-apple-system, SF Pro Display, sans-serif'
  },
  [OperatingSystem.ANDROID]: {
    scrollbarWidth: 0,
    minTouchTarget: 48,
    borderRadius: 8,
    fontFamily: 'Roboto, system-ui, sans-serif'
  },
  [OperatingSystem.UNKNOWN]: {
    scrollbarWidth: 16,
    minTouchTarget: 40,
    borderRadius: 4,
    fontFamily: 'system-ui, sans-serif'
  }
};

export class ResponsiveAdapter {
  private static instance: ResponsiveAdapter;
  private currentViewport: ViewportInfo | null = null;
  private touchCapabilities: TouchCapabilities | null = null;
  private config: ResponsiveConfig;
  private isInitialized = false;
  private mediaQueries: Map<string, MediaQueryList> = new Map();
  private listeners = {
    viewportChange: [] as Array<(viewport: ViewportInfo) => void>,
    orientationChange: [] as Array<(orientation: Orientation) => void>,
    themeChange: [] as Array<(theme: UITheme) => void>,
    breakpointChange: [] as Array<(screenSize: ScreenSize) => void>
  };

  private constructor() {
    this.config = this.generateDefaultConfig();
    this.setupEventListeners();
  }

  public static getInstance(): ResponsiveAdapter {
    if (!ResponsiveAdapter.instance) {
      ResponsiveAdapter.instance = new ResponsiveAdapter();
    }
    return ResponsiveAdapter.instance;
  }

  /**
   * 初始化响应式适配器
   */
  public async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    try {
      await this.detectViewport();
      await this.detectTouchCapabilities();
      this.setupMediaQueries();
      this.applyInitialAdaptations();
      this.isInitialized = true;
    } catch (error) {
      console.error('响应式适配器初始化失败:', error);
      throw error;
    }
  }

  /**
   * 生成默认配置
   */
  private generateDefaultConfig(): ResponsiveConfig {
    const platform = platformDetector.getPlatformInfo();
    const platformConfig = PLATFORM_UI_CONFIG[platform.os];
    
    return {
      breakpoints: { ...DEFAULT_BREAKPOINTS },
      scalingFactor: platform.device === DeviceType.MOBILE ? 1.2 : 1.0,
      minFontSize: platform.device === DeviceType.MOBILE ? 14 : 12,
      maxFontSize: platform.device === DeviceType.MOBILE ? 24 : 20,
      touchTargetSize: platformConfig.minTouchTarget,
      animations: platform.device !== DeviceType.MOBILE,
      reducedMotion: false,
      theme: UITheme.AUTO
    };
  }

  /**
   * 检测视口信息
   */
  private async detectViewport(): Promise<void> {
    if (typeof window === 'undefined') {
      return;
    }

    const width = window.innerWidth;
    const height = window.innerHeight;
    const pixelRatio = window.devicePixelRatio || 1;
    const screenSize = this.determineScreenSize(width);
    const orientation = this.determineOrientation(width, height);
    const availableWidth = screen.availWidth || width;
    const availableHeight = screen.availHeight || height;

    this.currentViewport = {
      width,
      height,
      screenSize,
      orientation,
      pixelRatio,
      availableWidth,
      availableHeight
    };

    this.notifyViewportChange();
  }

  /**
   * 检测触摸能力
   */
  private async detectTouchCapabilities(): Promise<void> {
    if (typeof window === 'undefined') {
      this.touchCapabilities = {
        supported: false,
        maxTouchPoints: 0,
        multiTouch: false,
        pressure: false
      };
      return;
    }

    const maxTouchPoints = navigator.maxTouchPoints || 0;
    const supported = 'ontouchstart' in window || maxTouchPoints > 0;

    this.touchCapabilities = {
      supported,
      maxTouchPoints,
      multiTouch: maxTouchPoints > 1,
      pressure: false // 简化版本，避免复杂的检测
    };
  }

  /**
   * 确定屏幕尺寸类别
   */
  private determineScreenSize(width: number): ScreenSize {
    const breakpoints = this.config.breakpoints;

    if (width < breakpoints.mobile) {
      return ScreenSize.MOBILE;
    } else if (width < breakpoints.tablet) {
      return ScreenSize.TABLET;
    } else if (width < breakpoints.desktop) {
      return ScreenSize.DESKTOP;
    } else if (width < breakpoints.large) {
      return ScreenSize.LARGE;
    } else {
      return ScreenSize.ULTRA_WIDE;
    }
  }

  /**
   * 确定设备方向
   */
  private determineOrientation(width: number, height: number): Orientation {
    return width > height ? Orientation.LANDSCAPE : Orientation.PORTRAIT;
  }

  /**
   * 设置媒体查询监听
   */
  private setupMediaQueries(): void {
    if (typeof window === 'undefined') {
      return;
    }

    // 监听断点变化
    Object.entries(this.config.breakpoints).forEach(([size, width]) => {
      const query = `(min-width: ${width}px)`;
      const mediaQuery = window.matchMedia(query);
      
      mediaQuery.addEventListener('change', () => {
        this.detectViewport();
      });
      
      this.mediaQueries.set(size, mediaQuery);
    });

    // 监听方向变化
    const orientationQuery = window.matchMedia('(orientation: landscape)');
    orientationQuery.addEventListener('change', () => {
      this.detectViewport();
    });
    this.mediaQueries.set('orientation', orientationQuery);

    // 监听主题变化
    const darkModeQuery = window.matchMedia('(prefers-color-scheme: dark)');
    darkModeQuery.addEventListener('change', (event) => {
      if (this.config.theme === UITheme.AUTO) {
        this.notifyThemeChange(event.matches ? UITheme.DARK : UITheme.LIGHT);
      }
    });
    this.mediaQueries.set('darkMode', darkModeQuery);

    // 监听减少动画偏好
    const reducedMotionQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    reducedMotionQuery.addEventListener('change', (event) => {
      this.config.reducedMotion = event.matches;
      this.config.animations = !event.matches;
    });
    this.mediaQueries.set('reducedMotion', reducedMotionQuery);
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    if (typeof window === 'undefined') {
      return;
    }

    // 监听窗口大小变化
    window.addEventListener('resize', () => {
      this.detectViewport();
    }, { passive: true });

    // 监听设备方向变化
    window.addEventListener('orientationchange', () => {
      setTimeout(() => {
        this.detectViewport();
      }, 100);
    }, { passive: true });
  }

  /**
   * 应用初始适配
   */
  private applyInitialAdaptations(): void {
    if (typeof document === 'undefined') {
      return;
    }

    const platform = platformDetector.getPlatformInfo();
    const platformConfig = PLATFORM_UI_CONFIG[platform.os];
    const root = document.documentElement;

    // 设置CSS自定义属性
    root.style.setProperty('--platform-font-family', platformConfig.fontFamily);
    root.style.setProperty('--platform-border-radius', `${platformConfig.borderRadius}px`);
    root.style.setProperty('--platform-scrollbar-width', `${platformConfig.scrollbarWidth}px`);
    root.style.setProperty('--platform-touch-target', `${platformConfig.minTouchTarget}px`);

    // 响应式断点
    Object.entries(this.config.breakpoints).forEach(([name, value]) => {
      root.style.setProperty(`--breakpoint-${name}`, `${value}px`);
    });

    // 视口信息
    if (this.currentViewport) {
      root.style.setProperty('--viewport-width', `${this.currentViewport.width}px`);
      root.style.setProperty('--viewport-height', `${this.currentViewport.height}px`);
      root.style.setProperty('--pixel-ratio', `${this.currentViewport.pixelRatio}`);
    }

    // 触摸能力
    if (this.touchCapabilities) {
      root.style.setProperty('--touch-supported', this.touchCapabilities.supported ? '1' : '0');
      root.style.setProperty('--max-touch-points', `${this.touchCapabilities.maxTouchPoints}`);
    }

    // 添加平台类名
    root.classList.add(`platform-${platform.os}`);
    root.classList.add(`device-${platform.device}`);
    root.classList.add(`browser-${platform.browser}`);

    if (this.currentViewport) {
      root.classList.add(`screen-${this.currentViewport.screenSize}`);
      root.classList.add(`orientation-${this.currentViewport.orientation}`);
    }

    if (this.touchCapabilities?.supported) {
      root.classList.add('touch-supported');
    }

    if (this.config.reducedMotion) {
      root.classList.add('reduced-motion');
    }
  }

  /**
   * 获取当前视口信息
   */
  public getViewportInfo(): ViewportInfo | null {
    return this.currentViewport;
  }

  /**
   * 获取触摸能力
   */
  public getTouchCapabilities(): TouchCapabilities | null {
    return this.touchCapabilities;
  }

  /**
   * 获取响应式配置
   */
  public getConfig(): ResponsiveConfig {
    return { ...this.config };
  }

  /**
   * 更新配置
   */
  public updateConfig(updates: Partial<ResponsiveConfig>): void {
    this.config = { ...this.config, ...updates };
    this.applyInitialAdaptations();
  }

  /**
   * 获取当前主题
   */
  public getCurrentTheme(): UITheme {
    if (this.config.theme === UITheme.AUTO) {
      if (typeof window !== 'undefined' && window.matchMedia('(prefers-color-scheme: dark)').matches) {
        return UITheme.DARK;
      }
      return UITheme.LIGHT;
    }
    return this.config.theme;
  }

  /**
   * 设置主题
   */
  public setTheme(theme: UITheme): void {
    this.config.theme = theme;
    this.notifyThemeChange(theme);
    
    if (typeof document !== 'undefined') {
      const root = document.documentElement;
      root.classList.remove('theme-light', 'theme-dark', 'theme-auto', 'theme-highContrast');
      root.classList.add(`theme-${theme}`);
    }
  }

  /**
   * 检查是否为移动设备
   */
  public isMobile(): boolean {
    return this.currentViewport?.screenSize === ScreenSize.MOBILE || false;
  }

  /**
   * 检查是否为平板设备
   */
  public isTablet(): boolean {
    return this.currentViewport?.screenSize === ScreenSize.TABLET || false;
  }

  /**
   * 检查是否为桌面设备
   */
  public isDesktop(): boolean {
    return this.currentViewport?.screenSize === ScreenSize.DESKTOP || 
           this.currentViewport?.screenSize === ScreenSize.LARGE ||
           this.currentViewport?.screenSize === ScreenSize.ULTRA_WIDE || false;
  }

  /**
   * 检查是否支持触摸
   */
  public isTouchDevice(): boolean {
    return this.touchCapabilities?.supported || false;
  }

  /**
   * 检查是否为横屏
   */
  public isLandscape(): boolean {
    return this.currentViewport?.orientation === Orientation.LANDSCAPE || false;
  }

  /**
   * 检查是否为竖屏
   */
  public isPortrait(): boolean {
    return this.currentViewport?.orientation === Orientation.PORTRAIT || false;
  }

  /**
   * 获取适配建议
   */
  public getAdaptationSuggestions(): AdaptationSuggestions {
    const platform = platformDetector.getPlatformInfo();
    const viewport = this.currentViewport;
    const touch = this.touchCapabilities;

    const suggestions: AdaptationSuggestions = {
      layout: [],
      typography: [],
      interaction: [],
      performance: [],
      accessibility: []
    };

    if (!viewport) {
      return suggestions;
    }

    // 布局建议
    if (viewport.screenSize === ScreenSize.MOBILE) {
      suggestions.layout.push('使用单列布局');
      suggestions.layout.push('增大触摸目标尺寸');
      suggestions.layout.push('简化导航结构');
    }

    // 字体建议
    if (platform.device === DeviceType.MOBILE) {
      suggestions.typography.push('使用较大的基础字体');
      suggestions.typography.push('增加行间距');
    }

    // 交互建议
    if (touch?.supported) {
      suggestions.interaction.push('优化触摸交互');
      suggestions.interaction.push('添加触觉反馈');
    }

    // 性能建议
    if (platform.device === DeviceType.MOBILE) {
      suggestions.performance.push('减少动画使用');
      suggestions.performance.push('懒加载图片');
    }

    // 无障碍建议
    if (this.config.reducedMotion) {
      suggestions.accessibility.push('禁用或减少动画');
    }
    suggestions.accessibility.push('确保足够的对比度');

    return suggestions;
  }

  /**
   * 事件监听器管理
   */
  public addEventListener<T extends keyof typeof this.listeners>(
    event: T,
    callback: typeof this.listeners[T][0]
  ): void {
    (this.listeners[event] as any[]).push(callback);
  }

  public removeEventListener<T extends keyof typeof this.listeners>(
    event: T,
    callback: typeof this.listeners[T][0]
  ): void {
    const listeners = this.listeners[event] as any[];
    const index = listeners.indexOf(callback);
    if (index > -1) {
      listeners.splice(index, 1);
    }
  }

  /**
   * 通知方法
   */
  private notifyViewportChange(): void {
    if (this.currentViewport) {
      this.listeners.viewportChange.forEach(callback => {
        try {
          callback(this.currentViewport!);
        } catch (error) {
          console.error('Viewport change listener error:', error);
        }
      });
    }
  }

  private notifyThemeChange(theme: UITheme): void {
    this.listeners.themeChange.forEach(callback => {
      try {
        callback(theme);
      } catch (error) {
        console.error('Theme change listener error:', error);
      }
    });
  }

  /**
   * 获取诊断信息
   */
  public getDiagnosticInfo(): string {
    const platform = platformDetector.getPlatformInfo();
    
    return JSON.stringify({
      isInitialized: this.isInitialized,
      platform,
      viewport: this.currentViewport,
      touchCapabilities: this.touchCapabilities,
      config: this.config,
      currentTheme: this.getCurrentTheme(),
      adaptationSuggestions: this.getAdaptationSuggestions(),
      timestamp: new Date().toISOString()
    }, null, 2);
  }

  /**
   * 释放资源
   */
  public dispose(): void {
    this.mediaQueries.forEach(mediaQuery => {
      // 清理事件监听器
    });
    this.mediaQueries.clear();

    this.listeners.viewportChange = [];
    this.listeners.orientationChange = [];
    this.listeners.themeChange = [];
    this.listeners.breakpointChange = [];
  }
}

// 导出单例实例
export const responsiveAdapter = ResponsiveAdapter.getInstance();

// 便捷函数
export const initializeResponsive = () => responsiveAdapter.initialize();
export const getViewportInfo = () => responsiveAdapter.getViewportInfo();
export const getTouchCapabilities = () => responsiveAdapter.getTouchCapabilities();
export const getResponsiveConfig = () => responsiveAdapter.getConfig();
export const updateResponsiveConfig = (config: Partial<ResponsiveConfig>) => responsiveAdapter.updateConfig(config);
export const getCurrentTheme = () => responsiveAdapter.getCurrentTheme();
export const setTheme = (theme: UITheme) => responsiveAdapter.setTheme(theme);
export const isMobileDevice = () => responsiveAdapter.isMobile();
export const isTabletDevice = () => responsiveAdapter.isTablet();
export const isDesktopDevice = () => responsiveAdapter.isDesktop();
export const isTouchDevice = () => responsiveAdapter.isTouchDevice();
export const isLandscapeMode = () => responsiveAdapter.isLandscape();
export const isPortraitMode = () => responsiveAdapter.isPortrait();
export const getAdaptationSuggestions = () => responsiveAdapter.getAdaptationSuggestions();
