/**
 * 高级自适应学习系统
 * 根据用户行为习惯和反馈数据自动调整检测标准
 */

import { PostureDataPoint, PersonalBaseline, EnhancedPostureAnalysis } from '../types/pose'

// 用户行为模式配置
export interface UserBehaviorConfig {
  // 学习参数
  learningRate: number
  adaptationThreshold: number
  minSessionsForAdaptation: number
  
  // 行为分析窗口
  shortTermWindowHours: number
  longTermWindowDays: number
  
  // 反馈权重
  explicitFeedbackWeight: number
  implicitFeedbackWeight: number
  
  // 个人化阈值
  enableDynamicThresholds: boolean
  thresholdAdaptationRate: number
  
  // 上下文学习
  enableContextualLearning: boolean
  timeOfDayLearning: boolean
  weekdayPatternLearning: boolean
}

// 用户反馈数据
export interface UserFeedback {
  timestamp: number
  type: 'explicit' | 'implicit'
  feedbackValue: number // -1 到 1，负值表示不满意，正值表示满意
  context: {
    timeOfDay: number // 0-23 小时
    dayOfWeek: number // 0-6，0为周日
    sessionDuration: number // 分钟
    environmentalConditions: any
  }
  associatedAnalysis: EnhancedPostureAnalysis
}

// 用户行为模式
export interface UserBehaviorPattern {
  // 时间模式
  timePatterns: {
    [hour: string]: {
      avgFocusScore: number
      variance: number
      sampleCount: number
    }
  }
  
  // 周模式
  weeklyPatterns: {
    [dayOfWeek: string]: {
      avgFocusScore: number
      activeHours: number[]
      variance: number
    }
  }
  
  // 姿态偏好
  posturePreferences: {
    preferredHeadPosition: string
    tolerableShoulderImbalance: number
    tolerableBodyLean: number
    adaptedThresholds: {
      focusThreshold: number
      stabilityThreshold: number
      confidenceThreshold: number
    }
  }
  
  // 环境偏好
  environmentalPreferences: {
    optimalLighting: number
    preferredCameraAngle: number
    tolerableBackgroundComplexity: number
  }
}

// 自适应学习状态
export interface AdaptiveLearningState {
  isActive: boolean
  totalSessions: number
  totalFeedbacks: number
  lastAdaptation: number
  adaptationVersion: number
  learningProgress: number // 0-1，学习进度
  confidenceLevel: number // 0-1，系统对个人化模型的信心
}

/**
 * 高级自适应学习分析器
 */
export class AdvancedAdaptiveLearningAnalyzer {
  private config: UserBehaviorConfig
  private feedbackHistory: UserFeedback[] = []
  private behaviorPattern!: UserBehaviorPattern
  private learningState!: AdaptiveLearningState
  private personalBaseline: PersonalBaseline | null = null
  
  constructor(config: Partial<UserBehaviorConfig> = {}) {
    this.config = {
      learningRate: 0.05,
      adaptationThreshold: 0.7,
      minSessionsForAdaptation: 20,
      shortTermWindowHours: 24,
      longTermWindowDays: 30,
      explicitFeedbackWeight: 0.8,
      implicitFeedbackWeight: 0.3,
      enableDynamicThresholds: true,
      thresholdAdaptationRate: 0.02,
      enableContextualLearning: true,
      timeOfDayLearning: true,
      weekdayPatternLearning: true,
      ...config
    }
    
    this.initializeBehaviorPattern()
    this.initializeLearningState()
  }
  
  private initializeBehaviorPattern(): void {
    this.behaviorPattern = {
      timePatterns: {},
      weeklyPatterns: {},
      posturePreferences: {
        preferredHeadPosition: 'center',
        tolerableShoulderImbalance: 0.15,
        tolerableBodyLean: 0.15,
        adaptedThresholds: {
          focusThreshold: 70,
          stabilityThreshold: 0.5,
          confidenceThreshold: 0.6
        }
      },
      environmentalPreferences: {
        optimalLighting: 0.7,
        preferredCameraAngle: 5,
        tolerableBackgroundComplexity: 0.5
      }
    }
  }
  
  private initializeLearningState(): void {
    this.learningState = {
      isActive: true,
      totalSessions: 0,
      totalFeedbacks: 0,
      lastAdaptation: Date.now(),
      adaptationVersion: 1,
      learningProgress: 0,
      confidenceLevel: 0
    }
  }
  
  /**
   * 添加用户反馈
   */
  public addUserFeedback(feedback: UserFeedback): void {
    this.feedbackHistory.push(feedback)
    this.learningState.totalFeedbacks++
    
    // 保持反馈历史在合理范围内
    const maxHistorySize = this.config.longTermWindowDays * 24 * 4
    if (this.feedbackHistory.length > maxHistorySize) {
      this.feedbackHistory.shift()
    }
    
    // 触发学习和适应
    if (this.shouldTriggerAdaptation()) {
      this.performAdaptation()
    }
  }
  
  /**
   * 记录会话数据
   */
  public recordSession(analysisData: EnhancedPostureAnalysis[], sessionDuration: number): void {
    this.learningState.totalSessions++
    
    if (analysisData.length === 0) return
    
    const now = new Date()
    const hour = now.getHours()
    const dayOfWeek = now.getDay()
    
    // 计算会话统计
    const avgFocusScore = analysisData.reduce((sum, analysis) => sum + analysis.focusScore, 0) / analysisData.length
    const variance = this.calculateVariance(analysisData.map(a => a.focusScore))
    
    // 更新时间模式
    if (this.config.timeOfDayLearning) {
      this.updateTimePattern(hour, avgFocusScore, variance)
    }
    
    // 更新周模式
    if (this.config.weekdayPatternLearning) {
      this.updateWeeklyPattern(dayOfWeek, hour, avgFocusScore, variance)
    }
    
    // 隐式反馈：基于会话质量自动生成反馈
    const implicitFeedback: UserFeedback = {
      timestamp: now.getTime(),
      type: 'implicit',
      feedbackValue: this.calculateImplicitFeedbackValue(avgFocusScore, variance, sessionDuration),
      context: {
        timeOfDay: hour,
        dayOfWeek,
        sessionDuration,
        environmentalConditions: analysisData[analysisData.length - 1].environmentalFactors
      },
      associatedAnalysis: analysisData[analysisData.length - 1]
    }
    
    this.addUserFeedback(implicitFeedback)
  }
  
  private updateTimePattern(hour: number, avgFocusScore: number, variance: number): void {
    const hourKey = hour.toString()
    if (!this.behaviorPattern.timePatterns[hourKey]) {
      this.behaviorPattern.timePatterns[hourKey] = {
        avgFocusScore: avgFocusScore,
        variance: variance,
        sampleCount: 1
      }
    } else {
      const pattern = this.behaviorPattern.timePatterns[hourKey]
      const learningRate = this.config.learningRate
      
      pattern.avgFocusScore = pattern.avgFocusScore * (1 - learningRate) + avgFocusScore * learningRate
      pattern.variance = pattern.variance * (1 - learningRate) + variance * learningRate
      pattern.sampleCount++
    }
  }
  
  private updateWeeklyPattern(dayOfWeek: number, hour: number, avgFocusScore: number, variance: number): void {
    const dayKey = dayOfWeek.toString()
    if (!this.behaviorPattern.weeklyPatterns[dayKey]) {
      this.behaviorPattern.weeklyPatterns[dayKey] = {
        avgFocusScore: avgFocusScore,
        activeHours: [hour],
        variance: variance
      }
    } else {
      const pattern = this.behaviorPattern.weeklyPatterns[dayKey]
      const learningRate = this.config.learningRate
      
      pattern.avgFocusScore = pattern.avgFocusScore * (1 - learningRate) + avgFocusScore * learningRate
      pattern.variance = pattern.variance * (1 - learningRate) + variance * learningRate
      
      // 更新活跃时间
      if (!pattern.activeHours.includes(hour)) {
        pattern.activeHours.push(hour)
      }
    }
  }
  
  private calculateVariance(values: number[]): number {
    if (values.length === 0) return 0
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length
    const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length
    return variance
  }
  
  private calculateImplicitFeedbackValue(avgFocusScore: number, variance: number, sessionDuration: number): number {
    // 基于专注度分数、稳定性和会话长度计算隐式反馈
    let feedbackValue = 0
    
    // 专注度分数影响 (权重: 0.5)
    feedbackValue += (avgFocusScore - 50) / 50 * 0.5
    
    // 稳定性影响 (方差越小越好，权重: 0.3)
    const stabilityScore = Math.max(0, 1 - variance / 100)
    feedbackValue += (stabilityScore - 0.5) * 2 * 0.3
    
    // 会话长度影响 (适中的会话时间最好，权重: 0.2)
    const idealSessionMinutes = 45
    const sessionScore = 1 - Math.abs(sessionDuration - idealSessionMinutes) / idealSessionMinutes
    feedbackValue += (sessionScore - 0.5) * 2 * 0.2
    
    return Math.max(-1, Math.min(1, feedbackValue))
  }
  
  private shouldTriggerAdaptation(): boolean {
    const timeSinceLastAdaptation = Date.now() - this.learningState.lastAdaptation
    const hoursSinceLastAdaptation = timeSinceLastAdaptation / (1000 * 60 * 60)
    
    return (
      this.learningState.totalSessions >= this.config.minSessionsForAdaptation &&
      hoursSinceLastAdaptation >= 24 && // 至少24小时间隔
      this.feedbackHistory.length >= 10 && // 至少有10个反馈
      this.calculateAdaptationNeed() > this.config.adaptationThreshold
    )
  }
  
  private calculateAdaptationNeed(): number {
    if (this.feedbackHistory.length === 0) return 0
    
    // 计算最近反馈的趋势
    const recentFeedbacks = this.getRecentFeedbacks(this.config.shortTermWindowHours)
    if (recentFeedbacks.length === 0) return 0
    
    const avgRecentFeedback = recentFeedbacks.reduce((sum, f) => sum + f.feedbackValue, 0) / recentFeedbacks.length
    const feedbackVariance = this.calculateVariance(recentFeedbacks.map(f => f.feedbackValue))
    
    // 负反馈或高方差表明需要适应
    const needForAdaptation = Math.max(
      Math.max(0, -avgRecentFeedback), // 负反馈
      Math.min(1, feedbackVariance) // 高方差
    )
    
    return needForAdaptation
  }
  
  private getRecentFeedbacks(hoursBack: number): UserFeedback[] {
    const cutoffTime = Date.now() - hoursBack * 60 * 60 * 1000
    return this.feedbackHistory.filter(f => f.timestamp >= cutoffTime)
  }
  
  private performAdaptation(): void {
    console.log('执行自适应学习更新...')
    
    // 分析反馈模式
    const adaptationInsights = this.analyzeFeedbackPatterns()
    
    // 更新姿态偏好
    this.adaptPosturePreferences(adaptationInsights)
    
    // 更新环境偏好
    this.adaptEnvironmentalPreferences(adaptationInsights)
    
    // 更新动态阈值
    if (this.config.enableDynamicThresholds) {
      this.adaptDynamicThresholds(adaptationInsights)
    }
    
    // 更新学习状态
    this.learningState.lastAdaptation = Date.now()
    this.learningState.adaptationVersion++
    this.learningState.learningProgress = Math.min(1, this.learningState.totalSessions / 100)
    this.learningState.confidenceLevel = this.calculateModelConfidence()
    
    console.log(`适应完成，版本: ${this.learningState.adaptationVersion}，置信度: ${this.learningState.confidenceLevel.toFixed(2)}`)
  }
  
  private analyzeFeedbackPatterns(): any {
    const recentFeedbacks = this.getRecentFeedbacks(this.config.longTermWindowDays * 24)
    
    return {
      avgFeedback: recentFeedbacks.reduce((sum, f) => sum + f.feedbackValue, 0) / recentFeedbacks.length,
      positiveFeedbackRatio: recentFeedbacks.filter(f => f.feedbackValue > 0).length / recentFeedbacks.length,
      timeOfDayCorrelations: this.analyzeTimeOfDayCorrelations(recentFeedbacks),
      postureCorrelations: this.analyzePostureCorrelations(recentFeedbacks),
      environmentalCorrelations: this.analyzeEnvironmentalCorrelations(recentFeedbacks)
    }
  }
  
  private analyzeTimeOfDayCorrelations(feedbacks: UserFeedback[]): any {
    const timeCorrelations: { [hour: string]: number[] } = {}
    
    feedbacks.forEach(feedback => {
      const hour = feedback.context.timeOfDay.toString()
      if (!timeCorrelations[hour]) {
        timeCorrelations[hour] = []
      }
      timeCorrelations[hour].push(feedback.feedbackValue)
    })
    
    const result: { [hour: string]: number } = {}
    Object.keys(timeCorrelations).forEach(hour => {
      const values = timeCorrelations[hour]
      result[hour] = values.reduce((sum, val) => sum + val, 0) / values.length
    })
    
    return result
  }
  
  private analyzePostureCorrelations(feedbacks: UserFeedback[]): any {
    const postureData: {
      headPositions: { [position: string]: number[] }
      shoulderBalances: number[]
      bodyLeans: number[]
      feedbackValues: number[]
    } = {
      headPositions: {},
      shoulderBalances: [],
      bodyLeans: [],
      feedbackValues: []
    }
    
    feedbacks.forEach(feedback => {
      const analysis = feedback.associatedAnalysis
      const headPos = analysis.headPosition
      
      if (!postureData.headPositions[headPos]) {
        postureData.headPositions[headPos] = []
      }
      postureData.headPositions[headPos].push(feedback.feedbackValue)
      
      postureData.shoulderBalances.push(analysis.shoulderBalance)
      postureData.bodyLeans.push(analysis.bodyLean)
      postureData.feedbackValues.push(feedback.feedbackValue)
    })
    
    return {
      preferredHeadPosition: this.findBestHeadPosition(postureData.headPositions),
      shoulderBalanceCorrelation: this.calculateCorrelation(postureData.shoulderBalances, postureData.feedbackValues),
      bodyLeanCorrelation: this.calculateCorrelation(postureData.bodyLeans, postureData.feedbackValues)
    }
  }
  
  private analyzeEnvironmentalCorrelations(feedbacks: UserFeedback[]): any {
    const lightingLevels: number[] = []
    const cameraAngles: number[] = []
    const backgroundComplexities: number[] = []
    const feedbackValues: number[] = []
    
    feedbacks.forEach(feedback => {
      const env = feedback.associatedAnalysis.environmentalFactors
      lightingLevels.push(env.lighting.level)
      cameraAngles.push(env.camera.angle)
      backgroundComplexities.push(env.background.complexity)
      feedbackValues.push(feedback.feedbackValue)
    })
    
    return {
      lightingCorrelation: this.calculateCorrelation(lightingLevels, feedbackValues),
      cameraAngleCorrelation: this.calculateCorrelation(cameraAngles, feedbackValues),
      backgroundComplexityCorrelation: this.calculateCorrelation(backgroundComplexities, feedbackValues),
      optimalLighting: this.findOptimalValue(lightingLevels, feedbackValues),
      optimalCameraAngle: this.findOptimalValue(cameraAngles, feedbackValues),
      optimalBackgroundComplexity: this.findOptimalValue(backgroundComplexities, feedbackValues)
    }
  }
  
  private findBestHeadPosition(headPositions: { [position: string]: number[] }): string {
    let bestPosition = 'center'
    let bestAvgFeedback = -1
    
    Object.keys(headPositions).forEach(position => {
      const values = headPositions[position]
      const avgFeedback = values.reduce((sum, val) => sum + val, 0) / values.length
      if (avgFeedback > bestAvgFeedback) {
        bestAvgFeedback = avgFeedback
        bestPosition = position
      }
    })
    
    return bestPosition
  }
  
  private calculateCorrelation(x: number[], y: number[]): number {
    if (x.length !== y.length || x.length === 0) return 0
    
    const meanX = x.reduce((sum, val) => sum + val, 0) / x.length
    const meanY = y.reduce((sum, val) => sum + val, 0) / y.length
    
    let numerator = 0
    let denomX = 0
    let denomY = 0
    
    for (let i = 0; i < x.length; i++) {
      const diffX = x[i] - meanX
      const diffY = y[i] - meanY
      numerator += diffX * diffY
      denomX += diffX * diffX
      denomY += diffY * diffY
    }
    
    if (denomX === 0 || denomY === 0) return 0
    return numerator / Math.sqrt(denomX * denomY)
  }
  
  private findOptimalValue(values: number[], feedbacks: number[]): number {
    if (values.length !== feedbacks.length || values.length === 0) return 0
    
    // 使用加权平均找到最优值
    let weightedSum = 0
    let totalWeight = 0
    
    for (let i = 0; i < values.length; i++) {
      const weight = Math.max(0, feedbacks[i]) // 只考虑正反馈
      weightedSum += values[i] * weight
      totalWeight += weight
    }
    
    return totalWeight > 0 ? weightedSum / totalWeight : values.reduce((sum, val) => sum + val, 0) / values.length
  }
  
  private adaptPosturePreferences(insights: any): void {
    const rate = this.config.thresholdAdaptationRate
    const preferences = this.behaviorPattern.posturePreferences
    
    // 更新首选头部位置
    if (insights.postureCorrelations.preferredHeadPosition) {
      preferences.preferredHeadPosition = insights.postureCorrelations.preferredHeadPosition
    }
    
    // 调整容差基于相关性分析
    if (Math.abs(insights.postureCorrelations.shoulderBalanceCorrelation) > 0.3) {
      const adjustment = insights.postureCorrelations.shoulderBalanceCorrelation > 0 ? 1.1 : 0.9
      preferences.tolerableShoulderImbalance *= adjustment
      preferences.tolerableShoulderImbalance = Math.max(0.05, Math.min(0.3, preferences.tolerableShoulderImbalance))
    }
    
    if (Math.abs(insights.postureCorrelations.bodyLeanCorrelation) > 0.3) {
      const adjustment = insights.postureCorrelations.bodyLeanCorrelation > 0 ? 1.1 : 0.9
      preferences.tolerableBodyLean *= adjustment
      preferences.tolerableBodyLean = Math.max(0.05, Math.min(0.3, preferences.tolerableBodyLean))
    }
  }
  
  private adaptEnvironmentalPreferences(insights: any): void {
    const preferences = this.behaviorPattern.environmentalPreferences
    const rate = this.config.thresholdAdaptationRate
    
    // 更新环境偏好
    if (insights.environmentalCorrelations.optimalLighting > 0) {
      preferences.optimalLighting = preferences.optimalLighting * (1 - rate) + 
                                   insights.environmentalCorrelations.optimalLighting * rate
    }
    
    if (insights.environmentalCorrelations.optimalCameraAngle !== undefined) {
      preferences.preferredCameraAngle = preferences.preferredCameraAngle * (1 - rate) + 
                                        insights.environmentalCorrelations.optimalCameraAngle * rate
    }
    
    if (insights.environmentalCorrelations.optimalBackgroundComplexity > 0) {
      preferences.tolerableBackgroundComplexity = preferences.tolerableBackgroundComplexity * (1 - rate) + 
                                                  insights.environmentalCorrelations.optimalBackgroundComplexity * rate
    }
  }
  
  private adaptDynamicThresholds(insights: any): void {
    const thresholds = this.behaviorPattern.posturePreferences.adaptedThresholds
    const rate = this.config.thresholdAdaptationRate
    
    // 基于反馈调整专注度阈值
    if (insights.avgFeedback < 0) {
      // 负反馈，降低阈值使系统更宽松
      thresholds.focusThreshold *= (1 - rate)
      thresholds.confidenceThreshold *= (1 - rate * 0.5)
    } else if (insights.avgFeedback > 0.5) {
      // 非常正面的反馈，可以适当提高阈值
      thresholds.focusThreshold *= (1 + rate * 0.5)
      thresholds.confidenceThreshold *= (1 + rate * 0.3)
    }
    
    // 确保阈值在合理范围内
    thresholds.focusThreshold = Math.max(50, Math.min(90, thresholds.focusThreshold))
    thresholds.confidenceThreshold = Math.max(0.3, Math.min(0.9, thresholds.confidenceThreshold))
    thresholds.stabilityThreshold = Math.max(0.2, Math.min(0.8, thresholds.stabilityThreshold))
  }
  
  private calculateModelConfidence(): number {
    // 基于数据量、反馈质量和适应性能计算模型置信度
    const dataConfidence = Math.min(1, this.learningState.totalSessions / 100)
    const feedbackConfidence = Math.min(1, this.feedbackHistory.length / 50)
    
    const recentFeedbacks = this.getRecentFeedbacks(this.config.shortTermWindowHours * 24)
    const feedbackQuality = recentFeedbacks.length > 0 ? 
      Math.abs(recentFeedbacks.reduce((sum, f) => sum + f.feedbackValue, 0) / recentFeedbacks.length) : 0
    
    return (dataConfidence * 0.4 + feedbackConfidence * 0.4 + feedbackQuality * 0.2)
  }
  
  /**
   * 获取个人化阈值
   */
  public getPersonalizedThresholds(): any {
    return this.behaviorPattern.posturePreferences.adaptedThresholds
  }
  
  /**
   * 获取个人化环境偏好
   */
  public getEnvironmentalPreferences(): any {
    return this.behaviorPattern.environmentalPreferences
  }
  
  /**
   * 获取当前时间的期望专注度
   */
  public getExpectedFocusScoreForTime(timestamp: number = Date.now()): number {
    const date = new Date(timestamp)
    const hour = date.getHours()
    const dayOfWeek = date.getDay()
    
    const hourPattern = this.behaviorPattern.timePatterns[hour.toString()]
    const weekPattern = this.behaviorPattern.weeklyPatterns[dayOfWeek.toString()]
    
    if (hourPattern && weekPattern) {
      return (hourPattern.avgFocusScore + weekPattern.avgFocusScore) / 2
    } else if (hourPattern) {
      return hourPattern.avgFocusScore
    } else if (weekPattern) {
      return weekPattern.avgFocusScore
    }
    
    return 70 // 默认期望值
  }
  
  /**
   * 预测用户在特定时间的表现
   */
  public predictUserPerformance(timestamp: number): {
    expectedScore: number
    confidence: number
    recommendations: string[]
  } {
    const expectedScore = this.getExpectedFocusScoreForTime(timestamp)
    const confidence = this.learningState.confidenceLevel
    const recommendations: string[] = []
    
    const date = new Date(timestamp)
    const hour = date.getHours()
    const dayOfWeek = date.getDay()
    
    // 基于模式生成建议
    const hourPattern = this.behaviorPattern.timePatterns[hour.toString()]
    if (hourPattern && hourPattern.avgFocusScore < 60) {
      recommendations.push(`在${hour}点您的专注度通常较低，建议采取额外措施提高注意力`)
    }
    
    if (this.behaviorPattern.weeklyPatterns[dayOfWeek.toString()]?.avgFocusScore < 65) {
      const dayNames = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
      recommendations.push(`${dayNames[dayOfWeek]}您的整体表现通常较低，建议调整作息时间`)
    }
    
    return { expectedScore, confidence, recommendations }
  }
  
  /**
   * 获取学习状态
   */
  public getLearningState(): AdaptiveLearningState {
    return { ...this.learningState }
  }
  
  /**
   * 获取行为模式
   */
  public getBehaviorPattern(): UserBehaviorPattern {
    return { ...this.behaviorPattern }
  }
  
  /**
   * 重置学习数据
   */
  public resetLearningData(): void {
    this.feedbackHistory = []
    this.initializeBehaviorPattern()
    this.initializeLearningState()
  }
  
  /**
   * 导出学习数据
   */
  public exportLearningData(): any {
    return {
      config: this.config,
      feedbackHistory: this.feedbackHistory,
      behaviorPattern: this.behaviorPattern,
      learningState: this.learningState,
      personalBaseline: this.personalBaseline
    }
  }
  
  /**
   * 导入学习数据
   */
  public importLearningData(data: any): void {
    this.feedbackHistory = data.feedbackHistory || []
    this.behaviorPattern = data.behaviorPattern || this.behaviorPattern
    this.learningState = data.learningState || this.learningState
    this.personalBaseline = data.personalBaseline || null
  }
}

/**
 * 创建默认的自适应学习分析器
 */
export function createAdaptiveLearningAnalyzer(config?: Partial<UserBehaviorConfig>): AdvancedAdaptiveLearningAnalyzer {
  return new AdvancedAdaptiveLearningAnalyzer(config)
}
