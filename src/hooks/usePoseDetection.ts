import { useState, useCallback, useRef, useEffect } from 'react'
import { Pose } from '@mediapipe/pose'
import { Camera } from '@mediapipe/camera_utils'
import { 
  PoseDetectionState, 
  PoseDetectionConfig, 
  PoseResults,
  PostureAnalysis,
  EnhancedPostureAnalysis,
  PoseDetectionError
} from '../types/pose'
import { analyzePose, updateStatistics } from '../utils/poseAnalysis'

interface UsePoseDetectionOptions {
  config?: Partial<PoseDetectionConfig>;
  videoElement?: HTMLVideoElement | null;
  onPoseDetected?: (results: PoseResults, analysis: PostureAnalysis | EnhancedPostureAnalysis) => void;
  autoStart?: boolean;
  useEnhancedAnalysis?: boolean;
}

export function usePoseDetection(options: UsePoseDetectionOptions = {}) {
  const {
    config = {},
    videoElement,
    onPoseDetected,
    autoStart = false,
    useEnhancedAnalysis = false
  } = options

  // 默认配置
  const defaultConfig: PoseDetectionConfig = {
    modelComplexity: 1,
    smoothLandmarks: true,
    enableSegmentation: false,
    smoothSegmentation: true,
    minDetectionConfidence: 0.7,
    minTrackingConfidence: 0.5,
    ...config
  }

  // 状态管理
  const [state, setState] = useState<PoseDetectionState>({
    isActive: false,
    isLoading: false,
    error: null,
    currentPose: null,
    postureAnalysis: null,
    statistics: {
      totalDetections: 0,
      goodPostureCount: 0,
      averageFocusScore: 0,
      sessionDuration: 0
    }
  })

  // 引用
  const poseRef = useRef<Pose | null>(null)
  const cameraRef = useRef<Camera | null>(null)
  const sessionStartTimeRef = useRef<number>(0)
  const animationFrameRef = useRef<number>(0)

  /**
   * 初始化MediaPipe Pose
   */
  const initializePose = useCallback(async () => {
    if (poseRef.current) return

    setState(prev => ({ ...prev, isLoading: true, error: null }))

    try {
      const pose = new Pose({
        locateFile: (file) => {
          return `https://cdn.jsdelivr.net/npm/@mediapipe/pose/${file}`
        }
      })

      pose.setOptions(defaultConfig)

      pose.onResults((results: any) => {
        if (!results.poseLandmarks) return

        // 转换MediaPipe的landmarks为我们的格式
        const landmarks = results.poseLandmarks.map((landmark: any) => ({
          x: landmark.x,
          y: landmark.y,
          z: landmark.z,
          visibility: landmark.visibility ?? 1.0
        }))

        // 分析姿态（使用增强版或简单版）
        const postureAnalysis = analyzePose(landmarks, useEnhancedAnalysis)
        
        // 更新统计信息
        const newStatistics = updateStatistics(
          state.statistics,
          postureAnalysis,
          sessionStartTimeRef.current
        )

        // 更新状态
        setState(prev => ({
          ...prev,
          currentPose: results,
          postureAnalysis,
          statistics: newStatistics
        }))

        // 触发回调
        onPoseDetected?.(results, postureAnalysis)
      })

      poseRef.current = pose
      setState(prev => ({ ...prev, isLoading: false }))
    } catch (error) {
      console.error('Failed to initialize pose detection:', error)
      const poseError: PoseDetectionError = {
        message: error instanceof Error ? error.message : 'Failed to initialize pose detection',
        code: 'INIT_ERROR',
        name: error instanceof Error ? error.name : 'PoseInitError'
      }
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: poseError
      }))
    }
  }, [defaultConfig, onPoseDetected, state.statistics])

  /**
   * 启动姿态检测
   */
  const startDetection = useCallback(async () => {
    if (!videoElement) {
      const poseError: PoseDetectionError = {
        message: 'Video element is required for pose detection',
        code: 'NO_VIDEO_ELEMENT',
        name: 'PoseDetectionError'
      }
      setState(prev => ({
        ...prev,
        error: poseError
      }))
      return
    }

    if (!poseRef.current) {
      await initializePose()
    }

    if (!poseRef.current) return

    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }))

      // 设置摄像头
      const camera = new Camera(videoElement, {
        onFrame: async () => {
          if (poseRef.current && videoElement) {
            await poseRef.current.send({ image: videoElement })
          }
        },
        width: 640,
        height: 480
      })

      await camera.start()
      cameraRef.current = camera
      sessionStartTimeRef.current = Date.now()

      setState(prev => ({
        ...prev,
        isActive: true,
        isLoading: false,
        statistics: {
          totalDetections: 0,
          goodPostureCount: 0,
          averageFocusScore: 0,
          sessionDuration: 0
        }
      }))
    } catch (error) {
      console.error('Failed to start pose detection:', error)
      const poseError: PoseDetectionError = {
        message: error instanceof Error ? error.message : 'Failed to start pose detection',
        code: 'START_ERROR',
        name: error instanceof Error ? error.name : 'PoseStartError'
      }
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: poseError
      }))
    }
  }, [videoElement, initializePose])

  /**
   * 停止姿态检测
   */
  const stopDetection = useCallback(() => {
    if (cameraRef.current) {
      cameraRef.current.stop()
      cameraRef.current = null
    }

    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current)
      animationFrameRef.current = 0
    }

    setState(prev => ({
      ...prev,
      isActive: false,
      currentPose: null,
      postureAnalysis: null
    }))
  }, [])

  /**
   * 重置统计信息
   */
  const resetStatistics = useCallback(() => {
    sessionStartTimeRef.current = Date.now()
    setState(prev => ({
      ...prev,
      statistics: {
        totalDetections: 0,
        goodPostureCount: 0,
        averageFocusScore: 0,
        sessionDuration: 0
      }
    }))
  }, [])

  /**
   * 更新配置
   */
  const updateConfig = useCallback((newConfig: Partial<PoseDetectionConfig>) => {
    if (poseRef.current) {
      poseRef.current.setOptions({ ...defaultConfig, ...newConfig })
    }
  }, [defaultConfig])

  /**
   * 手动触发检测
   */
  const detectPose = useCallback(async (imageElement: HTMLVideoElement | HTMLImageElement) => {
    if (!poseRef.current) {
      await initializePose()
    }

    if (poseRef.current) {
      await poseRef.current.send({ image: imageElement })
    }
  }, [initializePose])

  // 自动启动
  useEffect(() => {
    if (autoStart && videoElement && !state.isActive) {
      startDetection()
    }
  }, [autoStart, videoElement, state.isActive, startDetection])

  // 清理
  useEffect(() => {
    return () => {
      stopDetection()
      if (poseRef.current) {
        poseRef.current.close()
        poseRef.current = null
      }
    }
  }, [stopDetection])

  return {
    // 状态
    ...state,
    
    // 控制方法
    startDetection,
    stopDetection,
    resetStatistics,
    updateConfig,
    detectPose,
    
    // 计算属性
    isGoodPosture: state.postureAnalysis?.isFocused ?? false,
    postureScore: state.postureAnalysis?.focusScore ?? 0,
    isDetecting: state.isActive && !state.isLoading,
    hasWarnings: (state.postureAnalysis?.warnings.length ?? 0) > 0,
    
    // 统计信息
    sessionDuration: state.statistics.sessionDuration,
    goodPosturePercentage: state.statistics.totalDetections > 0 
      ? (state.statistics.goodPostureCount / state.statistics.totalDetections) * 100 
      : 0
  }
}

export default usePoseDetection 