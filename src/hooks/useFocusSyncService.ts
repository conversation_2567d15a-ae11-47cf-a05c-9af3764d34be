import { useEffect, useCallback, useRef, useState } from 'react'
import { PostureAnalysis, EnhancedPostureAnalysis } from '../types/pose'
import { poseBehaviorConnector } from '../managers/PoseBehaviorConnector'
import { GameStateManager } from '../managers/GameStateManager'
import { CropTimeManager } from '../managers/CropTimeManager'
import usePoseDetection from './usePoseDetection'

// 同步状态接口
export interface FocusSyncState {
  isConnected: boolean
  isReceivingData: boolean
  latency: number
  lastSyncTime: number
  errorCount: number
  syncFrequency: number // Hz
}

// 同步服务配置
export interface FocusSyncConfig {
  targetLatency: number // 目标延迟(ms)
  maxLatency: number // 最大允许延迟(ms)
  syncInterval: number // 同步间隔(ms)
  retryAttempts: number // 重试次数
  enablePerfMeasurement: boolean // 启用性能测量
}

// 同步服务选项
export interface UseFocusSyncServiceOptions {
  gameStateManager: GameStateManager | null
  cropTimeManager?: CropTimeManager | null
  videoElement?: HTMLVideoElement | null
  config?: Partial<FocusSyncConfig>
  autoStart?: boolean
  onSyncStatusChange?: (state: FocusSyncState) => void
  onLatencyWarning?: (latency: number) => void
}

/**
 * 专注度同步服务Hook
 * 连接姿态检测和游戏逻辑，提供实时专注度同步
 */
export function useFocusSyncService(options: UseFocusSyncServiceOptions) {
  const {
    gameStateManager,
    cropTimeManager,
    videoElement,
    config = {},
    autoStart = false,
    onSyncStatusChange,
    onLatencyWarning
  } = options

  // 默认配置
  const defaultConfig: FocusSyncConfig = {
    targetLatency: 200, // 目标200ms延迟
    maxLatency: 500, // 最大500ms延迟
    syncInterval: 100, // 每100ms同步一次
    retryAttempts: 3,
    enablePerfMeasurement: true,
    ...config
  }

  // 同步状态
  const [syncState, setSyncState] = useState<FocusSyncState>({
    isConnected: false,
    isReceivingData: false,
    latency: 0,
    lastSyncTime: 0,
    errorCount: 0,
    syncFrequency: 0
  })

  // 引用
  const latestAnalysisRef = useRef<PostureAnalysis | EnhancedPostureAnalysis | null>(null)
  const syncTimerRef = useRef<number | null>(null)
  const lastFrameTimeRef = useRef<number>(0)
  const frameCountRef = useRef<number>(0)
  const latencyMeasurementsRef = useRef<number[]>([])
  const performanceStartTimeRef = useRef<number>(0)
  const retryCountRef = useRef<number>(0)

  // 姿态检测回调
  const handlePoseDetected = useCallback((results: any, analysis: PostureAnalysis | EnhancedPostureAnalysis) => {
    if (!defaultConfig.enablePerfMeasurement) {
      latestAnalysisRef.current = analysis
      return
    }

    // 性能测量
    const now = performance.now()
    
    if (performanceStartTimeRef.current > 0) {
      const latency = now - performanceStartTimeRef.current
      latencyMeasurementsRef.current.push(latency)
      
      // 保持最近10次测量
      if (latencyMeasurementsRef.current.length > 10) {
        latencyMeasurementsRef.current.shift()
      }
      
      // 计算平均延迟
      const avgLatency = latencyMeasurementsRef.current.reduce((a, b) => a + b, 0) / latencyMeasurementsRef.current.length
      
      // 更新同步状态
      setSyncState(prev => ({
        ...prev,
        latency: avgLatency,
        isReceivingData: true,
        lastSyncTime: now
      }))
      
      // 延迟警告
      if (avgLatency > defaultConfig.maxLatency) {
        onLatencyWarning?.(avgLatency)
        console.warn(`⚠️ High latency detected: ${avgLatency.toFixed(1)}ms`)
      }
    }
    
    latestAnalysisRef.current = analysis
    performanceStartTimeRef.current = now
  }, [defaultConfig.enablePerfMeasurement, defaultConfig.maxLatency, onLatencyWarning])

  // 使用姿态检测（启用增强版分析）
  const poseDetection = usePoseDetection({
    videoElement,
    onPoseDetected: handlePoseDetected,
    autoStart: false, // 手动控制启动
    useEnhancedAnalysis: true // 启用增强版分析
  })

  /**
   * 同步专注度数据到游戏系统
   */
  const syncFocusData = useCallback(() => {
    if (!latestAnalysisRef.current || !gameStateManager) {
      return false
    }

    try {
      const syncStartTime = performance.now()
      
      // 发送数据到行为连接器
      poseBehaviorConnector.receivePoseData(latestAnalysisRef.current)
      
      const syncEndTime = performance.now()
      const syncLatency = syncEndTime - syncStartTime
      
      // 更新帧计数和频率
      frameCountRef.current++
      const now = Date.now()
      const deltaTime = now - lastFrameTimeRef.current
      
      if (deltaTime >= 1000) { // 每秒计算一次频率
        const frequency = frameCountRef.current / (deltaTime / 1000)
        setSyncState(prev => ({
          ...prev,
          syncFrequency: frequency,
          errorCount: 0 // 成功同步后重置错误计数
        }))
        frameCountRef.current = 0
        lastFrameTimeRef.current = now
      }
      
      // 重置重试计数
      retryCountRef.current = 0
      
      return true
    } catch (error) {
      console.error('Focus sync error:', error)
      retryCountRef.current++
      
      setSyncState(prev => ({
        ...prev,
        errorCount: prev.errorCount + 1
      }))
      
             // 达到最大重试次数后停止同步
       if (retryCountRef.current >= defaultConfig.retryAttempts) {
         console.error('❌ Focus sync failed after maximum retries, stopping sync')
         // 延迟调用stopSync以避免循环依赖
         setTimeout(() => {
           if (syncTimerRef.current) {
             clearInterval(syncTimerRef.current)
             syncTimerRef.current = null
           }
         }, 0)
       }
      
      return false
    }
  }, [gameStateManager, defaultConfig.retryAttempts])

  /**
   * 启动同步服务
   */
  const startSync = useCallback(async () => {
    if (!gameStateManager) {
      console.warn('GameStateManager is required for focus sync')
      return false
    }

    try {
      // 连接行为连接器到游戏状态管理器
      poseBehaviorConnector.connectGameStateManager(gameStateManager)
      poseBehaviorConnector.start()
      
      // 启动姿态检测
      await poseDetection.startDetection()
      
      // 启动同步定时器
      if (syncTimerRef.current) {
        clearInterval(syncTimerRef.current)
      }
      
      syncTimerRef.current = window.setInterval(() => {
        syncFocusData()
      }, defaultConfig.syncInterval)
      
      // 更新状态
      setSyncState(prev => ({
        ...prev,
        isConnected: true,
        errorCount: 0
      }))
      
      lastFrameTimeRef.current = Date.now()
      console.log('🚀 Focus sync service started')
      return true
      
    } catch (error) {
      console.error('Failed to start focus sync:', error)
      setSyncState(prev => ({
        ...prev,
        isConnected: false,
        errorCount: prev.errorCount + 1
      }))
      return false
    }
  }, [gameStateManager, poseDetection, syncFocusData, defaultConfig.syncInterval])

  /**
   * 停止同步服务
   */
  const stopSync = useCallback(() => {
    // 停止同步定时器
    if (syncTimerRef.current) {
      clearInterval(syncTimerRef.current)
      syncTimerRef.current = null
    }
    
    // 停止姿态检测
    poseDetection.stopDetection()
    
    // 停止行为连接器
    poseBehaviorConnector.stop()
    
    // 重置状态
    setSyncState({
      isConnected: false,
      isReceivingData: false,
      latency: 0,
      lastSyncTime: 0,
      errorCount: 0,
      syncFrequency: 0
    })
    
    // 重置引用
    latestAnalysisRef.current = null
    frameCountRef.current = 0
    retryCountRef.current = 0
    latencyMeasurementsRef.current = []
    
    console.log('⏹️ Focus sync service stopped')
  }, [poseDetection])

  /**
   * 重启同步服务
   */
  const restartSync = useCallback(async () => {
    stopSync()
    await new Promise(resolve => setTimeout(resolve, 1000)) // 等待1秒
    return await startSync()
  }, [stopSync, startSync])

  /**
   * 获取同步性能指标
   */
  const getPerformanceMetrics = useCallback(() => {
    const avgLatency = latencyMeasurementsRef.current.length > 0
      ? latencyMeasurementsRef.current.reduce((a, b) => a + b, 0) / latencyMeasurementsRef.current.length
      : 0

    return {
      averageLatency: avgLatency,
      currentFrequency: syncState.syncFrequency,
      isWithinTargetLatency: avgLatency <= defaultConfig.targetLatency,
      isWithinMaxLatency: avgLatency <= defaultConfig.maxLatency,
      errorRate: syncState.errorCount / Math.max(frameCountRef.current, 1),
      connectionStatus: poseBehaviorConnector.getConnectionStatus(),
      connectorStatus: poseBehaviorConnector.getStatusSummary()
    }
  }, [syncState, defaultConfig.targetLatency, defaultConfig.maxLatency])

  /**
   * 测试延迟
   */
  const testLatency = useCallback(async (): Promise<number> => {
    if (!latestAnalysisRef.current) {
      return -1
    }

    const startTime = performance.now()
    const success = syncFocusData()
    const endTime = performance.now()
    
    return success ? (endTime - startTime) : -1
  }, [syncFocusData])

  // 自动启动
  useEffect(() => {
    if (autoStart && gameStateManager && videoElement && !syncState.isConnected) {
      startSync()
    }
  }, [autoStart, gameStateManager, videoElement, syncState.isConnected, startSync])

  // 状态变化通知
  useEffect(() => {
    onSyncStatusChange?.(syncState)
  }, [syncState, onSyncStatusChange])

  // 清理
  useEffect(() => {
    return () => {
      stopSync()
      poseBehaviorConnector.disconnect()
    }
  }, [stopSync])

  return {
    // 同步状态
    syncState,
    
    // 姿态检测状态（传递）
    poseDetection: {
      isActive: poseDetection.isActive,
      isLoading: poseDetection.isLoading,
      error: poseDetection.error,
      postureAnalysis: poseDetection.postureAnalysis,
      isGoodPosture: poseDetection.isGoodPosture,
      postureScore: poseDetection.postureScore,
      statistics: poseDetection.statistics
    },
    
    // 控制方法
    startSync,
    stopSync,
    restartSync,
    testLatency,
    
    // 工具方法
    getPerformanceMetrics,
    
    // 计算属性
    isConnected: syncState.isConnected,
    isHealthy: syncState.isConnected && 
               syncState.isReceivingData && 
               syncState.latency <= defaultConfig.maxLatency &&
               syncState.errorCount < defaultConfig.retryAttempts,
    latencyStatus: syncState.latency <= defaultConfig.targetLatency ? 'good' :
                   syncState.latency <= defaultConfig.maxLatency ? 'warning' : 'critical',
    
    // 配置
    config: defaultConfig
  }
}

export default useFocusSyncService 