import { useEffect, useRef, useCallback, useState } from 'react'
import { AudioManager } from '../audio/AudioManager'
import { GameEvent, AudioSettings } from '../audio/audioConfig'

export interface UseAudioManagerReturn {
  // 播放控制
  playEffect: (effectId: string, volume?: number) => Promise<boolean>
  playMusic: (musicId: string, loop?: boolean) => Promise<boolean>
  stopMusic: () => void
  toggleMusic: () => boolean
  playGameEventSound: (event: GameEvent, volume?: number) => Promise<boolean>
  
  // 音量控制
  setVolume: (type: 'master' | 'music' | 'effects', value: number) => void
  setEnabled: (type: 'music' | 'effects', enabled: boolean) => void
  applySettings: (settings: Partial<AudioSettings>) => void
  
  // 状态
  audioStatus: {
    isInitialized: boolean
    isPlaying: boolean
    currentMusic?: string
    audioContextState?: string
    loadedAudios: number
  }
  audioSettings: AudioSettings
  
  // 系统控制
  initializeAudio: () => Promise<boolean>
  destroyAudio: () => void
}

/**
 * 音频管理Hook
 * 提供游戏音频的完整控制功能
 */
export const useAudioManager = (): UseAudioManagerReturn => {
  const audioManagerRef = useRef<AudioManager | null>(null)
  const [audioStatus, setAudioStatus] = useState({
    isInitialized: false,
    isPlaying: false,
    currentMusic: undefined as string | undefined,
    audioContextState: undefined as string | undefined,
    loadedAudios: 0
  })
  const [audioSettings, setAudioSettings] = useState<AudioSettings>({
    masterVolume: 0.7,
    musicVolume: 0.5,
    effectsVolume: 0.8,
    musicEnabled: true,
    effectsEnabled: true,
    currentMusic: undefined
  })

  // 获取音频管理器实例
  const getAudioManager = useCallback(() => {
    if (!audioManagerRef.current) {
      audioManagerRef.current = AudioManager.getInstance()
    }
    return audioManagerRef.current
  }, [])

  // 更新状态
  const updateStatus = useCallback(() => {
    const audioManager = getAudioManager()
    const status = audioManager.getStatus()
    const settings = audioManager.getSettings()
    
    setAudioStatus({
      isInitialized: status.isInitialized,
      isPlaying: status.isPlaying,
      currentMusic: status.currentMusic,
      audioContextState: status.audioContextState,
      loadedAudios: status.loadedAudios
    })
    setAudioSettings(settings)
  }, [getAudioManager])

  // 初始化音频系统
  const initializeAudio = useCallback(async (): Promise<boolean> => {
    try {
      const audioManager = getAudioManager()
      const success = await audioManager.initialize()
      
      if (success) {
        updateStatus()
        console.log('音频系统初始化成功')
      } else {
        console.error('音频系统初始化失败')
      }
      
      return success
    } catch (error) {
      console.error('音频系统初始化错误:', error)
      return false
    }
  }, [getAudioManager, updateStatus])

  // 播放音效
  const playEffect = useCallback(async (effectId: string, volume?: number): Promise<boolean> => {
    try {
      const audioManager = getAudioManager()
      const result = await audioManager.playEffect(effectId, volume)
      updateStatus()
      return result
    } catch (error) {
      console.error('播放音效失败:', error)
      return false
    }
  }, [getAudioManager, updateStatus])

  // 播放背景音乐
  const playMusic = useCallback(async (musicId: string, loop: boolean = true): Promise<boolean> => {
    try {
      const audioManager = getAudioManager()
      const result = await audioManager.playMusic(musicId, loop)
      updateStatus()
      return result
    } catch (error) {
      console.error('播放背景音乐失败:', error)
      return false
    }
  }, [getAudioManager, updateStatus])

  // 停止背景音乐
  const stopMusic = useCallback(() => {
    try {
      const audioManager = getAudioManager()
      audioManager.stopMusic()
      updateStatus()
    } catch (error) {
      console.error('停止背景音乐失败:', error)
    }
  }, [getAudioManager, updateStatus])

  // 切换背景音乐播放状态
  const toggleMusic = useCallback((): boolean => {
    try {
      const audioManager = getAudioManager()
      const result = audioManager.toggleMusic()
      updateStatus()
      return result
    } catch (error) {
      console.error('切换背景音乐失败:', error)
      return false
    }
  }, [getAudioManager, updateStatus])

  // 播放游戏事件音效
  const playGameEventSound = useCallback(async (event: GameEvent, volume?: number): Promise<boolean> => {
    try {
      const audioManager = getAudioManager()
      const result = await audioManager.playGameEventSound(event, volume)
      updateStatus()
      return result
    } catch (error) {
      console.error('播放游戏事件音效失败:', error)
      return false
    }
  }, [getAudioManager, updateStatus])

  // 设置音量
  const setVolume = useCallback((type: 'master' | 'music' | 'effects', value: number) => {
    try {
      const audioManager = getAudioManager()
      audioManager.setVolume(type, value)
      updateStatus()
    } catch (error) {
      console.error('设置音量失败:', error)
    }
  }, [getAudioManager, updateStatus])

  // 启用/禁用音频
  const setEnabled = useCallback((type: 'music' | 'effects', enabled: boolean) => {
    try {
      const audioManager = getAudioManager()
      audioManager.setEnabled(type, enabled)
      updateStatus()
    } catch (error) {
      console.error('设置音频启用状态失败:', error)
    }
  }, [getAudioManager, updateStatus])

  // 应用设置
  const applySettings = useCallback((newSettings: Partial<AudioSettings>) => {
    try {
      const audioManager = getAudioManager()
      audioManager.applySettings(newSettings)
      updateStatus()
    } catch (error) {
      console.error('应用音频设置失败:', error)
    }
  }, [getAudioManager, updateStatus])

  // 销毁音频系统
  const destroyAudio = useCallback(() => {
    try {
      const audioManager = getAudioManager()
      audioManager.destroy()
      audioManagerRef.current = null
      setAudioStatus({
        isInitialized: false,
        isPlaying: false,
        currentMusic: undefined,
        audioContextState: undefined,
        loadedAudios: 0
      })
    } catch (error) {
      console.error('销毁音频系统失败:', error)
    }
  }, [getAudioManager])

  // 定期更新状态
  useEffect(() => {
    const interval = setInterval(updateStatus, 5000) // 每5秒更新一次状态
    return () => clearInterval(interval)
  }, [updateStatus])

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      // 不在这里销毁，因为音频管理器是单例，可能被其他组件使用
    }
  }, [])

  return {
    // 播放控制
    playEffect,
    playMusic,
    stopMusic,
    toggleMusic,
    playGameEventSound,
    
    // 音量控制
    setVolume,
    setEnabled,
    applySettings,
    
    // 状态
    audioStatus,
    audioSettings,
    
    // 系统控制
    initializeAudio,
    destroyAudio
  }
}

/**
 * 音频事件Hook
 * 用于处理游戏事件触发的音效
 */
export const useGameAudioEvents = () => {
  const { playGameEventSound } = useAudioManager()

  // 种植音效
  const playPlantingSound = useCallback(async () => {
    return await playGameEventSound('CROP_PLANTED')
  }, [playGameEventSound])

  // 收获音效
  const playHarvestSound = useCallback(async () => {
    return await playGameEventSound('CROP_HARVESTED')
  }, [playGameEventSound])

  // 成长音效
  const playGrowthSound = useCallback(async () => {
    return await playGameEventSound('CROP_GROWN')
  }, [playGameEventSound])

  // 专注音效
  const playFocusSound = useCallback(async () => {
    return await playGameEventSound('FOCUS_IMPROVED')
  }, [playGameEventSound])

  // 失去专注音效
  const playFocusLostSound = useCallback(async () => {
    return await playGameEventSound('FOCUS_IMPROVED') // 使用同样的专注事件
  }, [playGameEventSound])

  // UI点击音效
  const playClickSound = useCallback(async () => {
    return await playGameEventSound('UI_INTERACTION')
  }, [playGameEventSound])

  // 成就音效
  const playAchievementSound = useCallback(async () => {
    return await playGameEventSound('ACHIEVEMENT_UNLOCKED')
  }, [playGameEventSound])

  return {
    playPlantingSound,
    playHarvestSound,
    playGrowthSound,
    playFocusSound,
    playFocusLostSound,
    playClickSound,
    playAchievementSound
  }
}

/**
 * 音频初始化Hook
 * 在应用启动时自动初始化音频系统
 */
export const useAudioInitialization = (autoInit: boolean = true) => {
  const { initializeAudio, audioStatus } = useAudioManager()
  const [initAttempted, setInitAttempted] = useState(false)

  useEffect(() => {
    if (autoInit && !initAttempted && !audioStatus.isInitialized) {
      setInitAttempted(true)
      
      // 用户交互后初始化（浏览器要求）
      const handleUserInteraction = async () => {
        const success = await initializeAudio()
        if (success) {
          console.log('音频系统自动初始化成功')
          // 移除事件监听器
          document.removeEventListener('click', handleUserInteraction)
          document.removeEventListener('keydown', handleUserInteraction)
          document.removeEventListener('touchstart', handleUserInteraction)
        }
      }

      // 监听用户交互事件
      document.addEventListener('click', handleUserInteraction, { once: true })
      document.addEventListener('keydown', handleUserInteraction, { once: true })
      document.addEventListener('touchstart', handleUserInteraction, { once: true })

      return () => {
        document.removeEventListener('click', handleUserInteraction)
        document.removeEventListener('keydown', handleUserInteraction)
        document.removeEventListener('touchstart', handleUserInteraction)
      }
    }
  }, [autoInit, initAttempted, audioStatus.isInitialized, initializeAudio])

  return {
    isInitialized: audioStatus.isInitialized,
    initAttempted
  }
} 