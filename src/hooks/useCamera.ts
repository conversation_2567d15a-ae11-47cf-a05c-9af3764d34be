import { useState, useRef, useCallback, useEffect } from 'react'

export interface CameraError {
  code: string
  message: string
  name: string
}

export type CameraStatus = 'idle' | 'requesting' | 'granted' | 'denied' | 'unavailable' | 'error'

export interface CameraState {
  status: CameraStatus
  stream: MediaStream | null
  error: CameraError | null
  isSupported: boolean
  deviceId?: string
}

export const useCamera = () => {
  const [state, setState] = useState<CameraState>({
    status: 'idle',
    stream: null,
    error: null,
    isSupported: false
  })

  const videoRef = useRef<HTMLVideoElement>(null)

  // 检查浏览器是否支持摄像头API
  const checkSupport = useCallback(() => {
    const supported = !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia)
    setState(prev => ({ ...prev, isSupported: supported }))
    return supported
  }, [])

  // 请求摄像头权限并获取视频流
  const requestCamera = useCallback(async (constraints?: MediaStreamConstraints) => {
    if (!checkSupport()) {
      setState(prev => ({
        ...prev,
        status: 'unavailable',
        error: {
          code: 'NOT_SUPPORTED',
          message: '您的浏览器不支持摄像头功能',
          name: 'NotSupportedError'
        }
      }))
      return
    }

    setState(prev => ({ ...prev, status: 'requesting', error: null }))

    try {
      const defaultConstraints: MediaStreamConstraints = {
        video: {
          width: { ideal: 640 },
          height: { ideal: 480 },
          facingMode: 'user'
        },
        audio: false
      }

      const stream = await navigator.mediaDevices.getUserMedia(
        constraints || defaultConstraints
      )

      // 获取设备ID
      const videoTrack = stream.getVideoTracks()[0]
      const deviceId = videoTrack?.getSettings().deviceId

      setState(prev => ({
        ...prev,
        status: 'granted',
        stream,
        deviceId,
        error: null
      }))

      // 如果有video元素引用，自动设置视频流
      if (videoRef.current) {
        videoRef.current.srcObject = stream
      }

    } catch (error) {
      console.error('摄像头访问失败:', error)
      
      let cameraError: CameraError

      if (error instanceof Error) {
        switch (error.name) {
          case 'NotAllowedError':
          case 'PermissionDeniedError':
            cameraError = {
              code: 'PERMISSION_DENIED',
              message: '摄像头权限被拒绝，请在浏览器设置中允许摄像头访问',
              name: error.name
            }
            setState(prev => ({ ...prev, status: 'denied', error: cameraError }))
            break

          case 'NotFoundError':
          case 'DevicesNotFoundError':
            cameraError = {
              code: 'NO_CAMERA',
              message: '未找到可用的摄像头设备',
              name: error.name
            }
            setState(prev => ({ ...prev, status: 'unavailable', error: cameraError }))
            break

          case 'NotReadableError':
          case 'TrackStartError':
            cameraError = {
              code: 'CAMERA_BUSY',
              message: '摄像头正被其他应用程序使用',
              name: error.name
            }
            setState(prev => ({ ...prev, status: 'error', error: cameraError }))
            break

          case 'OverconstrainedError':
          case 'ConstraintNotSatisfiedError':
            cameraError = {
              code: 'CONSTRAINTS_ERROR',
              message: '摄像头不支持所请求的配置',
              name: error.name
            }
            setState(prev => ({ ...prev, status: 'error', error: cameraError }))
            break

          case 'NotSupportedError':
            cameraError = {
              code: 'NOT_SUPPORTED',
              message: '您的浏览器不支持摄像头功能',
              name: error.name
            }
            setState(prev => ({ ...prev, status: 'unavailable', error: cameraError }))
            break

          case 'AbortError':
            cameraError = {
              code: 'REQUEST_ABORTED',
              message: '摄像头请求被中断',
              name: error.name
            }
            setState(prev => ({ ...prev, status: 'error', error: cameraError }))
            break

          default:
            cameraError = {
              code: 'UNKNOWN_ERROR',
              message: `摄像头访问失败: ${error.message}`,
              name: error.name
            }
            setState(prev => ({ ...prev, status: 'error', error: cameraError }))
        }
      } else {
        cameraError = {
          code: 'UNKNOWN_ERROR',
          message: '摄像头访问失败，请重试',
          name: 'UnknownError'
        }
        setState(prev => ({ ...prev, status: 'error', error: cameraError }))
      }
    }
  }, [checkSupport])

  // 停止摄像头
  const stopCamera = useCallback(() => {
    if (state.stream) {
      state.stream.getTracks().forEach(track => {
        track.stop()
      })
      
      if (videoRef.current) {
        videoRef.current.srcObject = null
      }

      setState(prev => ({
        ...prev,
        status: 'idle',
        stream: null,
        error: null
      }))
    }
  }, [state.stream])

  // 检查摄像头权限状态
  const checkPermission = useCallback(async () => {
    if (!navigator.permissions) {
      return 'prompt' // 浏览器不支持权限查询API
    }

    try {
      const permission = await navigator.permissions.query({ name: 'camera' as PermissionName })
      return permission.state // 'granted', 'denied', 'prompt'
    } catch (error) {
      console.warn('无法查询摄像头权限状态:', error)
      return 'prompt'
    }
  }, [])

  // 获取可用的摄像头设备列表
  const getDevices = useCallback(async () => {
    if (!navigator.mediaDevices || !navigator.mediaDevices.enumerateDevices) {
      return []
    }

    try {
      const devices = await navigator.mediaDevices.enumerateDevices()
      return devices.filter(device => device.kind === 'videoinput')
    } catch (error) {
      console.error('获取摄像头设备列表失败:', error)
      return []
    }
  }, [])

  // 切换摄像头设备
  const switchDevice = useCallback(async (deviceId: string) => {
    if (state.status !== 'granted') {
      return
    }

    stopCamera()
    
    await requestCamera({
      video: {
        deviceId: { exact: deviceId },
        width: { ideal: 640 },
        height: { ideal: 480 }
      },
      audio: false
    })
  }, [state.status, stopCamera, requestCamera])

  // 组件卸载时自动停止摄像头
  useEffect(() => {
    return () => {
      if (state.stream) {
        state.stream.getTracks().forEach(track => track.stop())
      }
    }
  }, [state.stream])

  // 初始化时检查支持性
  useEffect(() => {
    checkSupport()
  }, [checkSupport])

  return {
    ...state,
    videoRef,
    requestCamera,
    stopCamera,
    checkPermission,
    getDevices,
    switchDevice,
    checkSupport
  }
} 