import { WeatherType, WeatherIntensity, WeatherState } from '../types/weather'
import { WeatherVisualManager } from './WeatherVisualManager'
import { WeatherTransitionConfig } from '../types/weatherVisuals'

/**
 * 天气效果同步配置
 */
interface WeatherSyncConfig {
  enableVisualEffects: boolean
  enableAudioEffects: boolean
  syncTransitions: boolean
  transitionDelay: number // 音视频效果之间的延迟（毫秒）
  enableSpecialEffects: boolean // 雷电等特殊效果
  thunderProbability: number // 雷暴天气下雷声的概率（每分钟）
  windGustProbability: number // 风天气下阵风的概率（每分钟）
}

/**
 * 同步事件接口
 */
interface WeatherSyncEvent {
  type: 'weather_changed' | 'thunder_strike' | 'wind_gust' | 'transition_complete'
  weatherType: WeatherType
  intensity: WeatherIntensity
  timestamp: Date
  visualEffectId?: string
  audioEffectId?: string
}

/**
 * 天气效果协调器
 * 负责同步管理天气视觉效果和音效，创造沉浸式的天气体验
 */
export class WeatherEffectCoordinator {
  private static instance: WeatherEffectCoordinator
  private visualManager: WeatherVisualManager | null = null
  private audioManager: WeatherAudioManager
  private currentWeather: WeatherState | null = null
  private isInitialized: boolean = false
  private eventEmitter: Phaser.Events.EventEmitter
  
  // 配置和定时器
  private config: WeatherSyncConfig = {
    enableVisualEffects: true,
    enableAudioEffects: true,
    syncTransitions: true,
    transitionDelay: 500,
    enableSpecialEffects: true,
    thunderProbability: 0.3, // 30%每分钟
    windGustProbability: 0.2  // 20%每分钟
  }
  
  private specialEffectTimers: Map<string, number> = new Map()
  private transitionInProgress: boolean = false

  private constructor() {
    this.eventEmitter = new Phaser.Events.EventEmitter()
    this.audioManager = WeatherAudioManager.getInstance()
    this.setupEventListeners()
  }

  /**
   * 获取单例实例
   */
  static getInstance(): WeatherEffectCoordinator {
    if (!WeatherEffectCoordinator.instance) {
      WeatherEffectCoordinator.instance = new WeatherEffectCoordinator()
    }
    return WeatherEffectCoordinator.instance
  }

  /**
   * 添加事件监听器
   */
  on(event: string, listener: (...args: any[]) => void): void {
    this.eventEmitter.on(event, listener)
  }

  /**
   * 移除事件监听器
   */
  off(event: string, listener: (...args: any[]) => void): void {
    this.eventEmitter.off(event, listener)
  }

  /**
   * 移除所有监听器
   */
  removeAllListeners(): void {
    this.eventEmitter.removeAllListeners()
  }

  /**
   * 触发事件
   */
  private emit(event: string, ...args: any[]): void {
    this.eventEmitter.emit(event, ...args)
  }

  /**
   * 初始化协调器
   */
  async initialize(visualManager?: WeatherVisualManager): Promise<boolean> {
    try {
      // 初始化音频管理器
      await this.audioManager.initialize()

      // 设置视觉管理器（如果提供）
      if (visualManager) {
        this.setVisualManager(visualManager)
      }

      this.isInitialized = true
      console.log('天气效果协调器初始化成功')
      return true
    } catch (error) {
      console.error('天气效果协调器初始化失败:', error)
      return false
    }
  }

  /**
   * 设置视觉管理器
   */
  setVisualManager(visualManager: WeatherVisualManager): void {
    this.visualManager = visualManager
    
    // 监听视觉效果事件
    this.visualManager.on('weatherChanged', this.onVisualWeatherChanged.bind(this))
    this.visualManager.on('lightningStrike', this.onLightningStrike.bind(this))
    this.visualManager.on('effectIntensityChanged', this.onVisualIntensityChanged.bind(this))
    
    console.log('视觉管理器已连接到协调器')
  }

  /**
   * 设置协调器配置
   */
  setConfig(newConfig: Partial<WeatherSyncConfig>): void {
    this.config = { ...this.config, ...newConfig }
    console.log('天气效果协调器配置已更新:', this.config)
  }

  /**
   * 改变天气（同步音视频效果）
   */
  async changeWeather(
    weatherState: WeatherState, 
    options?: {
      immediate?: boolean
      audioDelay?: number
      visualDelay?: number
    }
  ): Promise<void> {
    if (!this.isInitialized) {
      console.warn('协调器未初始化，无法改变天气')
      return
    }

    this.transitionInProgress = true
    const { immediate = false, audioDelay = 0, visualDelay = 0 } = options || {}

    console.log(`协调器开始天气变化: ${weatherState.type} (强度: ${weatherState.intensity})`)

    try {
      // 停止当前的特殊效果
      this.stopSpecialEffects()

      if (immediate || !this.config.syncTransitions) {
        // 立即同时改变音视频效果
        await Promise.all([
          this.changeVisualEffects(weatherState, immediate),
          this.changeAudioEffects(weatherState, immediate)
        ])
      } else {
        // 根据配置延迟同步改变
        const visualPromise = this.changeVisualEffects(weatherState, false, visualDelay)
        const audioPromise = this.changeAudioEffects(weatherState, false, audioDelay || this.config.transitionDelay)
        
        await Promise.all([visualPromise, audioPromise])
      }

      this.currentWeather = weatherState
      this.startSpecialEffects(weatherState)
      
      // 发出同步事件
      const syncEvent: WeatherSyncEvent = {
        type: 'weather_changed',
        weatherType: weatherState.type,
        intensity: weatherState.intensity,
        timestamp: new Date()
      }
      
      this.emit('weatherSyncComplete', syncEvent)
      console.log('天气变化协调完成')

    } catch (error) {
      console.error('天气变化协调失败:', error)
      this.emit('weatherSyncError', error)
    } finally {
      this.transitionInProgress = false
    }
  }

  /**
   * 改变视觉效果
   */
  private async changeVisualEffects(
    weatherState: WeatherState, 
    immediate: boolean, 
    delay: number = 0
  ): Promise<void> {
    if (!this.config.enableVisualEffects || !this.visualManager) {
      return
    }

    if (delay > 0) {
      await new Promise(resolve => setTimeout(resolve, delay))
    }

    try {
              await this.visualManager.changeWeather(
          weatherState.type,
          weatherState.intensity,
          {
            immediate,
            transition: immediate ? undefined : {
              fromWeather: this.currentWeather?.type || WeatherType.SUNNY,
              toWeather: weatherState.type,
              duration: 3000,
              ease: 'sine.inOut'
            }
          }
        )
      console.log('视觉效果变化完成')
    } catch (error) {
      console.error('视觉效果变化失败:', error)
    }
  }

  /**
   * 改变音频效果
   */
  private async changeAudioEffects(
    weatherState: WeatherState, 
    immediate: boolean, 
    delay: number = 0
  ): Promise<void> {
    if (!this.config.enableAudioEffects) {
      return
    }

    if (delay > 0) {
      await new Promise(resolve => setTimeout(resolve, delay))
    }

    try {
      await this.audioManager.changeWeather(weatherState, immediate)
      console.log('音频效果变化完成')
    } catch (error) {
      console.error('音频效果变化失败:', error)
    }
  }

  /**
   * 调整天气强度（同步音视频）
   */
  async adjustWeatherIntensity(newIntensity: WeatherIntensity): Promise<void> {
    if (!this.currentWeather || this.transitionInProgress) {
      return
    }

    console.log(`协调器调整天气强度: ${newIntensity}`)

    try {
      // 同时调整音视频强度
      const promises: Promise<void>[] = []

      if (this.config.enableAudioEffects) {
        promises.push(this.audioManager.adjustWeatherIntensity(newIntensity))
      }

      if (this.config.enableVisualEffects && this.visualManager) {
        // 视觉管理器的强度调整（如果有相应方法）
        promises.push(this.adjustVisualIntensity(newIntensity))
      }

      await Promise.all(promises)

      this.currentWeather.intensity = newIntensity
      this.restartSpecialEffects()

      this.emit('intensityAdjusted', newIntensity)
      console.log('天气强度调整完成')

    } catch (error) {
      console.error('天气强度调整失败:', error)
    }
  }

  /**
   * 调整视觉强度（如果视觉管理器支持）
   */
  private async adjustVisualIntensity(intensity: WeatherIntensity): Promise<void> {
    // 这里假设视觉管理器有相应的方法
    // 实际实现可能需要重新触发天气变化
    if (this.visualManager && this.currentWeather) {
      await this.visualManager.changeWeather(
        this.currentWeather.type,
        intensity,
        { immediate: true }
      )
    }
  }

  /**
   * 开始特殊效果（雷声、阵风等）
   */
  private startSpecialEffects(weatherState: WeatherState): void {
    if (!this.config.enableSpecialEffects) return

    // 雷暴天气的雷声效果
    if (weatherState.type === WeatherType.THUNDERSTORM) {
      this.startThunderEffects(weatherState.intensity)
    }

    // 风天气的阵风效果
    if (weatherState.type === WeatherType.WINDY) {
      this.startWindGustEffects(weatherState.intensity)
    }
  }

  /**
   * 开始雷声效果
   */
  private startThunderEffects(intensity: WeatherIntensity): void {
    const interval = this.calculateThunderInterval(intensity)
    
    const thunderTimer = setInterval(async () => {
      if (Math.random() < this.config.thunderProbability) {
        await this.triggerThunderStrike(intensity)
      }
    }, interval)

    this.specialEffectTimers.set('thunder', thunderTimer)
    console.log(`雷声特效已启动，间隔: ${interval}ms`)
  }

  /**
   * 开始阵风效果
   */
  private startWindGustEffects(intensity: WeatherIntensity): void {
    const interval = this.calculateWindGustInterval(intensity)
    
    const windTimer = setInterval(async () => {
      if (Math.random() < this.config.windGustProbability) {
        await this.triggerWindGust(intensity)
      }
    }, interval)

    this.specialEffectTimers.set('windGust', windTimer)
    console.log(`阵风特效已启动，间隔: ${interval}ms`)
  }

  /**
   * 触发雷击效果（同步音视频）
   */
  private async triggerThunderStrike(intensity: WeatherIntensity): Promise<void> {
    console.log('触发雷击效果')

    try {
      const promises: Promise<void>[] = []

      // 播放雷声
      if (this.config.enableAudioEffects) {
        promises.push(this.audioManager.playThunderSound(intensity))
      }

      // 触发闪电视觉效果
      if (this.config.enableVisualEffects && this.visualManager) {
        // 假设视觉管理器有闪电效果方法
        promises.push(this.triggerLightningVisual(intensity))
      }

      await Promise.all(promises)

      const syncEvent: WeatherSyncEvent = {
        type: 'thunder_strike',
        weatherType: WeatherType.THUNDERSTORM,
        intensity,
        timestamp: new Date()
      }

      this.emit('thunderStrike', syncEvent)

    } catch (error) {
      console.error('雷击效果触发失败:', error)
    }
  }

  /**
   * 触发阵风效果
   */
  private async triggerWindGust(intensity: WeatherIntensity): Promise<void> {
    console.log('触发阵风效果')

    try {
      const promises: Promise<void>[] = []

      // 播放阵风声
      if (this.config.enableAudioEffects) {
        promises.push(this.audioManager.playWindGustSound(intensity))
      }

      // 触发风的视觉效果增强
      if (this.config.enableVisualEffects && this.visualManager) {
        promises.push(this.triggerWindGustVisual(intensity))
      }

      await Promise.all(promises)

      const syncEvent: WeatherSyncEvent = {
        type: 'wind_gust',
        weatherType: WeatherType.WINDY,
        intensity,
        timestamp: new Date()
      }

      this.emit('windGust', syncEvent)

    } catch (error) {
      console.error('阵风效果触发失败:', error)
    }
  }

  /**
   * 触发闪电视觉效果
   */
  private async triggerLightningVisual(intensity: WeatherIntensity): Promise<void> {
    // 这里可以调用视觉管理器的闪电效果
    // 实际实现取决于视觉管理器的API
    console.log('触发闪电视觉效果')
  }

  /**
   * 触发阵风视觉效果
   */
  private async triggerWindGustVisual(intensity: WeatherIntensity): Promise<void> {
    // 这里可以临时增强风的视觉效果
    console.log('触发阵风视觉效果')
  }

  /**
   * 计算雷声间隔
   */
  private calculateThunderInterval(intensity: WeatherIntensity): number {
    const baseInterval = 60000 // 1分钟
    const multipliers = {
      [WeatherIntensity.LIGHT]: 2.0,
      [WeatherIntensity.MODERATE]: 1.5,
      [WeatherIntensity.HEAVY]: 1.0,
      [WeatherIntensity.EXTREME]: 0.5
    }
    return baseInterval * multipliers[intensity]
  }

  /**
   * 计算阵风间隔
   */
  private calculateWindGustInterval(intensity: WeatherIntensity): number {
    const baseInterval = 45000 // 45秒
    const multipliers = {
      [WeatherIntensity.LIGHT]: 2.0,
      [WeatherIntensity.MODERATE]: 1.3,
      [WeatherIntensity.HEAVY]: 0.8,
      [WeatherIntensity.EXTREME]: 0.4
    }
    return baseInterval * multipliers[intensity]
  }

  /**
   * 停止特殊效果
   */
  private stopSpecialEffects(): void {
    for (const [effectType, timer] of this.specialEffectTimers.entries()) {
      clearInterval(timer)
      console.log(`停止特殊效果: ${effectType}`)
    }
    this.specialEffectTimers.clear()
  }

  /**
   * 重启特殊效果
   */
  private restartSpecialEffects(): void {
    if (this.currentWeather) {
      this.stopSpecialEffects()
      this.startSpecialEffects(this.currentWeather)
    }
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 监听音频管理器事件
    this.audioManager.on('weatherAudioChanged', this.onAudioWeatherChanged.bind(this))
    this.audioManager.on('thunderPlayed', this.onThunderPlayed.bind(this))
    this.audioManager.on('windGustPlayed', this.onWindGustPlayed.bind(this))
  }

  /**
   * 视觉天气变化事件处理器
   */
  private onVisualWeatherChanged(weatherData: any): void {
    console.log('视觉天气变化事件:', weatherData)
    this.emit('visualWeatherChanged', weatherData)
  }

  /**
   * 音频天气变化事件处理器
   */
  private onAudioWeatherChanged(weatherState: WeatherState): void {
    console.log('音频天气变化事件:', weatherState)
    this.emit('audioWeatherChanged', weatherState)
  }

  /**
   * 闪电攻击事件处理器
   */
  private onLightningStrike(lightningData: any): void {
    console.log('闪电攻击事件:', lightningData)
    this.emit('lightningStrike', lightningData)
  }

  /**
   * 视觉强度变化事件处理器
   */
  private onVisualIntensityChanged(intensity: WeatherIntensity): void {
    console.log('视觉强度变化事件:', intensity)
    this.emit('visualIntensityChanged', intensity)
  }

  /**
   * 雷声播放事件处理器
   */
  private onThunderPlayed(thunderData: any): void {
    console.log('雷声播放事件:', thunderData)
    this.emit('thunderPlayed', thunderData)
  }

  /**
   * 阵风播放事件处理器
   */
  private onWindGustPlayed(gustData: any): void {
    console.log('阵风播放事件:', gustData)
    this.emit('windGustPlayed', gustData)
  }

  /**
   * 启用/禁用视觉效果
   */
  setVisualEffectsEnabled(enabled: boolean): void {
    this.config.enableVisualEffects = enabled
    console.log(`视觉效果${enabled ? '启用' : '禁用'}`)
    this.emit('visualEffectsToggled', enabled)
  }

  /**
   * 启用/禁用音频效果
   */
  setAudioEffectsEnabled(enabled: boolean): void {
    this.config.enableAudioEffects = enabled
    this.audioManager.setEnabled(enabled)
    console.log(`音频效果${enabled ? '启用' : '禁用'}`)
    this.emit('audioEffectsToggled', enabled)
  }

  /**
   * 设置音效音量
   */
  setAudioVolume(volume: number): void {
    this.audioManager.setMasterVolume(volume)
    this.emit('audioVolumeChanged', volume)
  }

  /**
   * 获取当前状态
   */
  getStatus(): {
    isInitialized: boolean
    currentWeather: WeatherState | null
    transitionInProgress: boolean
    config: WeatherSyncConfig
    visualManagerConnected: boolean
    audioStatus: any
    activeSpecialEffects: string[]
  } {
    return {
      isInitialized: this.isInitialized,
      currentWeather: this.currentWeather,
      transitionInProgress: this.transitionInProgress,
      config: this.config,
      visualManagerConnected: this.visualManager !== null,
      audioStatus: this.audioManager.getStatus(),
      activeSpecialEffects: Array.from(this.specialEffectTimers.keys())
    }
  }

  /**
   * 销毁协调器
   */
  destroy(): void {
    this.stopSpecialEffects()
    this.audioManager.destroy()
    this.removeAllListeners()
    console.log('天气效果协调器已销毁')
  }
}

export default WeatherEffectCoordinator 