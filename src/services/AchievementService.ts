import { AchievementManager, AchievementCheckResult } from '../managers/AchievementManager';
import { ExperienceSystem } from '../utils/ExperienceSystem';
import { LevelManager } from '../managers/LevelManager';
import {
  AchievementEvent,
  AchievementNotification,
  ExperienceSource,
  Achievement
} from '../types/achievements';

export interface SessionData {
  sessionId: string;
  startTime: Date;
  endTime?: Date;
  duration: number; // 秒
  focusScore: number;
  postureScore: number;
  averageScore: number;
  continuousFocusTime: number; // 连续专注时间（分钟）
  breakCount: number;
  postureWarnings: number;
}

export interface DailyStats {
  date: Date;
  totalFocusTime: number; // 分钟
  sessionCount: number;
  averageFocusScore: number;
  averagePostureScore: number;
  bestSession: {
    focusScore: number;
    postureScore: number;
    duration: number;
  };
  streak: number; // 连续天数
}

export class AchievementService {
  private achievementManager: AchievementManager;
  private experienceSystem: ExperienceSystem;
  private levelManager: LevelManager;
  private notificationCallbacks: Array<(notification: AchievementNotification) => void> = [];
  private currentSession: Partial<SessionData> | null = null;

  constructor() {
    this.experienceSystem = new ExperienceSystem();
    this.levelManager = new LevelManager(this.experienceSystem);
    this.achievementManager = new AchievementManager();

    // 注册成就解锁回调
    this.achievementManager.onAchievementUnlocked((achievement: Achievement) => {
      this.handleAchievementUnlocked(achievement);
    });

    // 注册等级提升回调
    this.levelManager.onLevelUp((result) => {
      this.handleLevelUp(result);
    });
  }

  /**
   * 注册通知回调
   */
  onNotification(callback: (notification: AchievementNotification) => void): void {
    this.notificationCallbacks.push(callback);
  }

  /**
   * 开始专注会话
   */
  async startSession(sessionId: string): Promise<void> {
    const startTime = new Date();
    
    this.currentSession = {
      sessionId,
      startTime,
      duration: 0,
      focusScore: 0,
      postureScore: 0,
      averageScore: 0,
      continuousFocusTime: 0,
      breakCount: 0,
      postureWarnings: 0
    };

    // 触发会话开始事件
    const event: AchievementEvent = {
      type: 'session_started',
      data: {
        sessionId,
        sessionStartTime: startTime.toISOString(),
        startHour: startTime.getHours()
      },
      timestamp: startTime,
      sessionId
    };

    await this.processAchievementEvent(event);
  }

  /**
   * 更新会话数据
   */
  async updateSession(data: Partial<SessionData>): Promise<void> {
    if (!this.currentSession) return;

    // 更新当前会话数据
    Object.assign(this.currentSession, data);

    // 触发实时更新事件
    const event: AchievementEvent = {
      type: 'session_updated',
      data: {
        ...this.currentSession,
        focusScore: data.focusScore,
        postureScore: data.postureScore,
        continuousFocusTime: data.continuousFocusTime
      },
      timestamp: new Date(),
      sessionId: this.currentSession.sessionId
    };

    await this.processAchievementEvent(event);
  }

  /**
   * 结束专注会话
   */
  async endSession(finalData: Partial<SessionData>): Promise<{
    sessionResults: SessionData;
    achievementResults: AchievementCheckResult;
    experienceGained: number;
    levelUpResult?: any;
  }> {
    if (!this.currentSession) {
      throw new Error('No active session found');
    }

    // 完善会话数据
    const sessionData: SessionData = {
      ...this.currentSession,
      ...finalData,
      endTime: new Date()
    } as SessionData;

    // 计算平均分数
    sessionData.averageScore = (sessionData.focusScore + sessionData.postureScore) / 2;

    // 触发会话完成事件
    const event: AchievementEvent = {
      type: 'session_completed',
      data: {
        ...sessionData,
        sessionDuration: sessionData.duration,
        isPerfectSession: sessionData.focusScore >= 95 && sessionData.postureScore >= 95,
        isLongSession: sessionData.duration >= 120 * 60, // 2小时
        sessionHour: sessionData.startTime.getHours()
      },
      timestamp: new Date(),
      sessionId: sessionData.sessionId
    };

    // 处理成就检查
    const achievementResults = await this.processAchievementEvent(event);

    // 计算经验值奖励
    let experienceGained = 0;
    let levelUpResult;

    // 基础会话完成经验值
    const sessionExpResult = await this.levelManager.processExperienceGain(
      ExperienceSource.PERFECT_SESSION,
      `完成专注会话 ${Math.floor(sessionData.duration / 60)} 分钟`,
      {
        multiplier: this.calculateSessionMultiplier(sessionData),
        sessionId: sessionData.sessionId
      }
    );

    experienceGained += sessionExpResult.experienceGained;
    if (sessionExpResult.levelUpResult) {
      levelUpResult = sessionExpResult.levelUpResult;
    }

    // 成就经验值奖励
    if (achievementResults.experienceGained > 0) {
      const achievementExpResult = await this.levelManager.processExperienceGain(
        ExperienceSource.ACHIEVEMENT,
        `成就奖励经验值`,
        {
          customAmount: achievementResults.experienceGained,
          sessionId: sessionData.sessionId
        }
      );

      experienceGained += achievementExpResult.experienceGained;
      if (achievementExpResult.levelUpResult && !levelUpResult) {
        levelUpResult = achievementExpResult.levelUpResult;
      }
    }

    // 清除当前会话
    this.currentSession = null;

    return {
      sessionResults: sessionData,
      achievementResults,
      experienceGained,
      levelUpResult
    };
  }

  /**
   * 计算会话经验值倍数
   */
  private calculateSessionMultiplier(sessionData: SessionData): number {
    let multiplier = 1.0;

    // 时长奖励
    if (sessionData.duration >= 30 * 60) multiplier += 0.2; // 30分钟+20%
    if (sessionData.duration >= 60 * 60) multiplier += 0.3; // 1小时+30%
    if (sessionData.duration >= 120 * 60) multiplier += 0.5; // 2小时+50%

    // 质量奖励
    const avgScore = (sessionData.focusScore + sessionData.postureScore) / 2;
    if (avgScore >= 80) multiplier += 0.1; // 80分+10%
    if (avgScore >= 90) multiplier += 0.2; // 90分+20%
    if (avgScore >= 95) multiplier += 0.3; // 95分+30%

    // 连续专注奖励
    if (sessionData.continuousFocusTime >= 15) multiplier += 0.1; // 15分钟+10%
    if (sessionData.continuousFocusTime >= 30) multiplier += 0.2; // 30分钟+20%

    // 姿态优秀奖励
    if (sessionData.postureWarnings === 0) multiplier += 0.1; // 无姿态警告+10%

    return Math.min(multiplier, 3.0); // 最大3倍经验值
  }

  /**
   * 更新每日统计
   */
  async updateDailyStats(stats: DailyStats): Promise<AchievementCheckResult> {
    const event: AchievementEvent = {
      type: 'daily_stats_updated',
      data: {
        date: stats.date.toISOString(),
        dailyFocusTime: stats.totalFocusTime,
        dailySessionCount: stats.sessionCount,
        dailyAvgFocusScore: stats.averageFocusScore,
        dailyAvgPostureScore: stats.averagePostureScore,
        dailyMaxFocusScore: stats.bestSession.focusScore,
        dailyMaxPostureScore: stats.bestSession.postureScore,
        dailyStreak: stats.streak,
        dailyImprovement: this.calculateDailyImprovement(stats),
        dailyImprovementPercent: this.calculateImprovementPercent(stats)
      },
      timestamp: new Date()
    };

    return await this.processAchievementEvent(event);
  }

  /**
   * 计算每日改进
   */
  private calculateDailyImprovement(stats: DailyStats): number {
    // 这里应该与前一天的数据比较
    // 简化实现，返回正数表示改进
    return Math.random() > 0.5 ? 1 : 0;
  }

  /**
   * 计算改进百分比
   */
  private calculateImprovementPercent(stats: DailyStats): number {
    // 简化实现，返回改进百分比
    return Math.random() * 100;
  }

  /**
   * 处理成就事件
   */
  private async processAchievementEvent(event: AchievementEvent): Promise<AchievementCheckResult> {
    const result = await this.achievementManager.processAchievementEvent(event);

    // 发送通知
    result.notifications.forEach(notification => {
      this.sendNotification(notification);
    });

    return result;
  }

  /**
   * 处理成就解锁
   */
  private async handleAchievementUnlocked(achievement: Achievement): Promise<void> {
    console.log(`🎉 成就解锁: ${achievement.name} - ${achievement.description}`);
    
    // 可以在这里添加额外的成就解锁逻辑
    // 例如：解锁新功能、发送邮件通知等
  }

  /**
   * 处理等级提升
   */
  private async handleLevelUp(result: any): Promise<void> {
    console.log(`🌟 等级提升: ${result.oldLevel} → ${result.newLevel}`);
    
    // 发送等级提升通知
    this.sendNotification(result.notification);
  }

  /**
   * 发送通知
   */
  private sendNotification(notification: AchievementNotification): void {
    this.notificationCallbacks.forEach(callback => {
      try {
        callback(notification);
      } catch (error) {
        console.error('Notification callback error:', error);
      }
    });
  }

  /**
   * 手动触发成就检查
   */
  async triggerAchievementCheck(eventType: string, data: any): Promise<AchievementCheckResult> {
    const event: AchievementEvent = {
      type: eventType,
      data,
      timestamp: new Date(),
      sessionId: this.currentSession?.sessionId
    };

    return await this.processAchievementEvent(event);
  }

  /**
   * 获取用户成就数据
   */
  getUserAchievementData(): {
    stats: any;
    experience: any;
    levelProgress: any;
    notifications: AchievementNotification[];
  } {
    return {
      stats: this.achievementManager.getAchievementStats(),
      experience: this.experienceSystem.calculateUserExperience(),
      levelProgress: this.levelManager.getLevelProgress(),
      notifications: [] // 实际项目中应该从存储中获取
    };
  }

  /**
   * 重置每日任务
   */
  async resetDailyTasks(): Promise<void> {
    // 触发每日重置事件
    const event: AchievementEvent = {
      type: 'daily_reset',
      data: {
        resetDate: new Date().toISOString()
      },
      timestamp: new Date()
    };

    await this.processAchievementEvent(event);
  }

  /**
   * 重置周常任务
   */
  async resetWeeklyTasks(): Promise<void> {
    // 触发周常重置事件
    const event: AchievementEvent = {
      type: 'weekly_reset',
      data: {
        resetDate: new Date().toISOString()
      },
      timestamp: new Date()
    };

    await this.processAchievementEvent(event);
  }

  /**
   * 获取推荐任务
   */
  getRecommendedTasks(): {
    dailyTasks: any[];
    weeklyTasks: any[];
    nextAchievements: any[];
  } {
    // 获取进度最接近完成的成就
    const userProgress = Array.from((this.achievementManager as any).userProgress.values()) as any[];
    const nextAchievements = userProgress
      .filter((p: any) => !p.isCompleted && p.progress > 0)
      .sort((a: any, b: any) => (b.progress / b.maxProgress) - (a.progress / a.maxProgress))
      .slice(0, 3);

    return {
      dailyTasks: [], // 从 AchievementManager 获取
      weeklyTasks: [], // 从 AchievementManager 获取
      nextAchievements
    };
  }

  /**
   * 导出系统状态（用于保存/加载）
   */
  exportState(): {
    achievements: any;
    experience: any;
    level: any;
  } {
    return {
      achievements: (this.achievementManager as any).exportUserData?.() || null,
      experience: this.experienceSystem.exportRecords(),
      level: this.levelManager.getLevelProgress()
    };
  }

  /**
   * 导入系统状态
   */
  importState(state: any): void {
    if (state.achievements && (this.achievementManager as any).importUserData) {
      (this.achievementManager as any).importUserData(state.achievements);
    }
    
    if (state.experience) {
      this.experienceSystem.importRecords(state.experience);
    }
  }
} 