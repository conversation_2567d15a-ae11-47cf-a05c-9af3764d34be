// 跨端数据同步服务
export interface SyncData {
  timestamp: number;
  type: 'monitoring' | 'focus' | 'reward' | 'settings';
  action: string;
  payload: any;
  deviceId: string;
  deviceType: 'desktop' | 'mobile' | 'web';
}

export interface SyncState {
  isConnected: boolean;
  lastSyncTime: number;
  deviceId: string;
  deviceType: 'desktop' | 'mobile' | 'web';
  connectionMethod: 'websocket' | 'http' | 'electron' | 'offline';
  syncQueue: SyncData[];
  conflictResolution: 'latest' | 'desktop-priority' | 'manual';
}

export interface DeviceInfo {
  id: string;
  type: 'desktop' | 'mobile' | 'web';
  platform: string;
  userAgent: string;
  lastSeen: number;
  isOnline: boolean;
  capabilities: string[];
}

export class SyncService {
  private static instance: SyncService | null = null;
  private state: SyncState;
  private listeners: Map<string, Function[]> = new Map();
  private syncInterval: NodeJS.Timeout | null = null;
  private websocket: WebSocket | null = null;
  private deviceInfo: DeviceInfo;

  private constructor() {
    this.deviceInfo = this.generateDeviceInfo();
    this.state = {
      isConnected: false,
      lastSyncTime: 0,
      deviceId: this.deviceInfo.id,
      deviceType: this.deviceInfo.type,
      connectionMethod: 'offline',
      syncQueue: [],
      conflictResolution: 'latest'
    };

    this.initializeSync();
  }

  public static getInstance(): SyncService {
    if (!SyncService.instance) {
      SyncService.instance = new SyncService();
    }
    return SyncService.instance;
  }

  // 生成设备信息
  private generateDeviceInfo(): DeviceInfo {
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    const isElectron = !!(window as any).electronAPI;
    
    let deviceType: 'desktop' | 'mobile' | 'web';
    let capabilities: string[] = [];

    if (isElectron) {
      deviceType = 'desktop';
      capabilities = ['monitoring', 'notifications', 'file-system', 'system-integration'];
    } else if (isMobile) {
      deviceType = 'mobile';
      capabilities = ['notifications', 'vibration', 'orientation', 'touch'];
    } else {
      deviceType = 'web';
      capabilities = ['notifications', 'storage'];
    }

    // 检查PWA支持
    if ('serviceWorker' in navigator) {
      capabilities.push('pwa');
    }

    // 检查WebSocket支持
    if (typeof WebSocket !== 'undefined') {
      capabilities.push('websocket');
    }

    return {
      id: this.getOrCreateDeviceId(),
      type: deviceType,
      platform: navigator.platform,
      userAgent: navigator.userAgent,
      lastSeen: Date.now(),
      isOnline: navigator.onLine,
      capabilities
    };
  }

  // 获取或创建设备ID
  private getOrCreateDeviceId(): string {
    let deviceId = localStorage.getItem('deviceId');
    if (!deviceId) {
      deviceId = `device_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      localStorage.setItem('deviceId', deviceId);
    }
    return deviceId;
  }

  // 初始化同步
  private async initializeSync(): Promise<void> {
    try {
      // 1. 尝试Electron同步（桌面端优先）
      if (await this.tryElectronSync()) {
        this.state.connectionMethod = 'electron';
        this.state.isConnected = true;
        this.emit('connection-established', { method: 'electron' });
        return;
      }

      // 2. 尝试WebSocket同步
      if (await this.tryWebSocketSync()) {
        this.state.connectionMethod = 'websocket';
        this.state.isConnected = true;
        this.emit('connection-established', { method: 'websocket' });
        return;
      }

      // 3. 尝试HTTP轮询同步
      if (await this.tryHttpSync()) {
        this.state.connectionMethod = 'http';
        this.state.isConnected = true;
        this.emit('connection-established', { method: 'http' });
        return;
      }

      // 4. 降级到离线模式
      this.state.connectionMethod = 'offline';
      this.state.isConnected = false;
      this.emit('connection-failed', { reason: 'no-available-methods' });

    } catch (error) {
      console.error('同步初始化失败:', error);
      this.state.isConnected = false;
      this.emit('sync-error', { error });
    }

    // 启动定期同步检查
    this.startPeriodicSync();
  }

  // 尝试Electron同步
  private async tryElectronSync(): Promise<boolean> {
    try {
      if (!(window as any).electronAPI) {
        return false;
      }

      // 注册Electron事件监听
      const electronAPI = (window as any).electronAPI;
      
      // 监听监控状态更新
      electronAPI.onMonitoringUpdate?.((data: any) => {
        this.handleRemoteUpdate({
          timestamp: Date.now(),
          type: 'monitoring',
          action: 'status-update',
          payload: data,
          deviceId: 'desktop-main',
          deviceType: 'desktop'
        });
      });

      // 监听违规检测
      electronAPI.onViolationDetected?.((data: any) => {
        this.handleRemoteUpdate({
          timestamp: Date.now(),
          type: 'monitoring',
          action: 'violation-detected',
          payload: data,
          deviceId: 'desktop-main',
          deviceType: 'desktop'
        });
      });

      return true;
    } catch (error) {
      console.warn('Electron同步失败:', error);
      return false;
    }
  }

  // 尝试WebSocket同步
  private async tryWebSocketSync(): Promise<boolean> {
    return new Promise((resolve) => {
      try {
        // 尝试连接到本地WebSocket服务器
        const wsUrl = 'ws://localhost:8080/sync';
        this.websocket = new WebSocket(wsUrl);

        const timeout = setTimeout(() => {
          this.websocket?.close();
          resolve(false);
        }, 3000);

        this.websocket.onopen = () => {
          clearTimeout(timeout);
          this.setupWebSocketHandlers();
          resolve(true);
        };

        this.websocket.onerror = () => {
          clearTimeout(timeout);
          resolve(false);
        };

      } catch (error) {
        resolve(false);
      }
    });
  }

  // 设置WebSocket处理器
  private setupWebSocketHandlers(): void {
    if (!this.websocket) return;

    this.websocket.onmessage = (event) => {
      try {
        const data: SyncData = JSON.parse(event.data);
        this.handleRemoteUpdate(data);
      } catch (error) {
        console.error('WebSocket消息解析失败:', error);
      }
    };

    this.websocket.onclose = () => {
      this.state.isConnected = false;
      this.emit('connection-lost', { method: 'websocket' });
      // 尝试重连
      setTimeout(() => this.tryWebSocketSync(), 5000);
    };

    this.websocket.onerror = (error) => {
      console.error('WebSocket错误:', error);
      this.emit('sync-error', { error });
    };

    // 发送设备注册信息
    this.sendWebSocketMessage({
      timestamp: Date.now(),
      type: 'settings',
      action: 'device-register',
      payload: this.deviceInfo,
      deviceId: this.state.deviceId,
      deviceType: this.state.deviceType
    });
  }

  // 发送WebSocket消息
  private sendWebSocketMessage(data: SyncData): void {
    if (this.websocket?.readyState === WebSocket.OPEN) {
      this.websocket.send(JSON.stringify(data));
    }
  }

  // 尝试HTTP同步
  private async tryHttpSync(): Promise<boolean> {
    try {
      const response = await fetch('/api/sync/ping', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          deviceId: this.state.deviceId,
          deviceInfo: this.deviceInfo
        })
      });

      return response.ok;
    } catch (error) {
      return false;
    }
  }

  // 启动定期同步
  private startPeriodicSync(): void {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
    }

    this.syncInterval = setInterval(() => {
      this.performPeriodicSync();
    }, 30000); // 30秒同步一次
  }

  // 执行定期同步
  private async performPeriodicSync(): Promise<void> {
    try {
      // 处理同步队列
      await this.processSyncQueue();

      // 更新设备在线状态
      this.deviceInfo.lastSeen = Date.now();
      this.deviceInfo.isOnline = navigator.onLine;

      // 尝试重新连接（如果断开）
      if (!this.state.isConnected) {
        await this.initializeSync();
      }

    } catch (error) {
      console.error('定期同步失败:', error);
    }
  }

  // 处理同步队列
  private async processSyncQueue(): Promise<void> {
    const queue = [...this.state.syncQueue];
    this.state.syncQueue = [];

    for (const data of queue) {
      try {
        await this.sendSyncData(data);
      } catch (error) {
        // 发送失败，重新加入队列
        this.state.syncQueue.push(data);
        console.error('同步数据发送失败:', error);
      }
    }
  }

  // 发送同步数据
  private async sendSyncData(data: SyncData): Promise<void> {
    switch (this.state.connectionMethod) {
      case 'electron':
        await this.sendElectronSync(data);
        break;
      case 'websocket':
        this.sendWebSocketMessage(data);
        break;
      case 'http':
        await this.sendHttpSync(data);
        break;
      default:
        // 离线模式，添加到队列
        this.state.syncQueue.push(data);
    }
  }

  // 发送Electron同步
  private async sendElectronSync(data: SyncData): Promise<void> {
    // Electron环境下的同步逻辑
    // 这里可以通过IPC与主进程通信
    console.log('Electron同步:', data);
  }

  // 发送HTTP同步
  private async sendHttpSync(data: SyncData): Promise<void> {
    const response = await fetch('/api/sync', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data)
    });

    if (!response.ok) {
      throw new Error(`HTTP同步失败: ${response.status}`);
    }
  }

  // 处理远程更新
  private handleRemoteUpdate(data: SyncData): void {
    // 检查是否是自己发送的数据（避免循环）
    if (data.deviceId === this.state.deviceId) {
      return;
    }

    // 冲突解决
    if (this.hasConflict(data)) {
      this.resolveConflict(data);
      return;
    }

    // 应用更新
    this.applyUpdate(data);
    
    // 更新同步时间
    this.state.lastSyncTime = Date.now();
    
    // 触发事件
    this.emit('data-updated', data);
  }

  // 检查冲突
  private hasConflict(data: SyncData): boolean {
    // 简单的时间戳冲突检测
    const timeDiff = Math.abs(data.timestamp - Date.now());
    return timeDiff > 60000; // 超过1分钟认为可能有冲突
  }

  // 解决冲突
  private resolveConflict(data: SyncData): void {
    switch (this.state.conflictResolution) {
      case 'latest':
        if (data.timestamp > this.state.lastSyncTime) {
          this.applyUpdate(data);
        }
        break;
      case 'desktop-priority':
        if (data.deviceType === 'desktop') {
          this.applyUpdate(data);
        }
        break;
      case 'manual':
        this.emit('conflict-detected', data);
        break;
    }
  }

  // 应用更新
  private applyUpdate(data: SyncData): void {
    switch (data.type) {
      case 'monitoring':
        this.applyMonitoringUpdate(data);
        break;
      case 'focus':
        this.applyFocusUpdate(data);
        break;
      case 'reward':
        this.applyRewardUpdate(data);
        break;
      case 'settings':
        this.applySettingsUpdate(data);
        break;
    }
  }

  // 应用监控更新
  private applyMonitoringUpdate(data: SyncData): void {
    switch (data.action) {
      case 'status-update':
        // 更新监控状态到本地
        localStorage.setItem('sync-monitoring-status', JSON.stringify(data.payload));
        break;
      case 'violation-detected':
        // 处理违规检测
        this.emit('violation-synced', data.payload);
        break;
      case 'whitelist-updated':
        // 更新白名单
        localStorage.setItem('sync-whitelist', JSON.stringify(data.payload));
        break;
    }
  }

  // 应用专注更新
  private applyFocusUpdate(data: SyncData): void {
    switch (data.action) {
      case 'session-start':
      case 'session-end':
      case 'session-pause':
        localStorage.setItem('sync-focus-session', JSON.stringify(data.payload));
        this.emit('focus-session-synced', data.payload);
        break;
    }
  }

  // 应用奖励更新
  private applyRewardUpdate(data: SyncData): void {
    switch (data.action) {
      case 'rewards-blocked':
      case 'rewards-unblocked':
        localStorage.setItem('sync-reward-status', JSON.stringify(data.payload));
        this.emit('reward-status-synced', data.payload);
        break;
    }
  }

  // 应用设置更新
  private applySettingsUpdate(data: SyncData): void {
    switch (data.action) {
      case 'settings-changed':
        // 合并设置
        const currentSettings = JSON.parse(localStorage.getItem('focusSettings') || '{}');
        const newSettings = { ...currentSettings, ...data.payload };
        localStorage.setItem('focusSettings', JSON.stringify(newSettings));
        this.emit('settings-synced', newSettings);
        break;
    }
  }

  // 公共API：同步数据
  public async sync(type: SyncData['type'], action: string, payload: any): Promise<void> {
    const data: SyncData = {
      timestamp: Date.now(),
      type,
      action,
      payload,
      deviceId: this.state.deviceId,
      deviceType: this.state.deviceType
    };

    if (this.state.isConnected) {
      try {
        await this.sendSyncData(data);
      } catch (error) {
        // 发送失败，添加到队列
        this.state.syncQueue.push(data);
      }
    } else {
      // 离线模式，添加到队列
      this.state.syncQueue.push(data);
    }
  }

  // 获取同步状态
  public getSyncState(): SyncState {
    return { ...this.state };
  }

  // 获取设备信息
  public getDeviceInfo(): DeviceInfo {
    return { ...this.deviceInfo };
  }

  // 强制同步
  public async forceSync(): Promise<void> {
    await this.initializeSync();
    await this.processSyncQueue();
  }

  // 事件监听
  public on(event: string, callback: Function): void {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event)!.push(callback);
  }

  // 移除事件监听
  public off(event: string, callback: Function): void {
    const callbacks = this.listeners.get(event);
    if (callbacks) {
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    }
  }

  // 触发事件
  private emit(event: string, data?: any): void {
    const callbacks = this.listeners.get(event);
    if (callbacks) {
      callbacks.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`事件回调执行失败 (${event}):`, error);
        }
      });
    }
  }

  // 清理资源
  public destroy(): void {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
    }

    if (this.websocket) {
      this.websocket.close();
      this.websocket = null;
    }

    this.listeners.clear();
    SyncService.instance = null;
  }
} 