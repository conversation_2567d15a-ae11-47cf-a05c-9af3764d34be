import { DecorationSystem } from '../systems/DecorationSystem'
import { DecorationStorageService, StorageResult, StorageConfig } from './DecorationStorageService'
import { DecorationManager } from '../types/decoration'

// 集成配置接口
export interface IntegrationConfig {
  autoSaveEnabled: boolean
  autoSaveInterval: number
  backupEnabled: boolean
  backupInterval: number
  maxBackups: number
  syncEnabled: boolean
  userId?: string
  storageConfig?: Partial<StorageConfig>
}

// 集成事件类型
export interface IntegrationEvents {
  onSaveSuccess: (manager: DecorationManager) => void
  onSaveError: (error: string) => void
  onLoadSuccess: (manager: DecorationManager) => void
  onLoadError: (error: string) => void
  onBackupCreated: (backupId: string) => void
  onSyncComplete: () => void
  onSyncError: (error: string) => void
}

export class DecorationIntegrationService {
  private decorationSystem: DecorationSystem
  private storageService: DecorationStorageService
  private config: IntegrationConfig
  private events: Partial<IntegrationEvents> = {}
  
  private autoSaveTimer?: NodeJS.Timeout
  private backupTimer?: NodeJS.Timeout
  private isInitialized = false

  constructor(
    decorationSystem: DecorationSystem,
    config: Partial<IntegrationConfig> = {}
  ) {
    this.decorationSystem = decorationSystem
    this.config = {
      autoSaveEnabled: true,
      autoSaveInterval: 30000, // 30秒
      backupEnabled: true,
      backupInterval: 300000, // 5分钟
      maxBackups: 10,
      syncEnabled: false,
      ...config
    }

    // 初始化存储服务
    this.storageService = new DecorationStorageService(config.storageConfig)
  }

  // ============ 初始化和配置 ============

  /**
   * 初始化集成服务
   */
  async initialize(): Promise<StorageResult> {
    try {
      // 初始化存储服务
      const storageResult = await this.storageService.initialize()
      if (!storageResult.success) {
        throw new Error(`存储服务初始化失败: ${storageResult.error}`)
      }

      // 加载已有数据
      const loadResult = await this.loadDecorationData()
      if (loadResult.success && loadResult.data) {
        // 将数据导入装饰系统
        this.decorationSystem.importState(loadResult.data)
        this.events.onLoadSuccess?.(loadResult.data)
      }

      // 设置装饰系统事件监听
      this.setupSystemEventListeners()

      // 启动自动保存
      if (this.config.autoSaveEnabled) {
        this.startAutoSave()
      }

      // 启动自动备份
      if (this.config.backupEnabled) {
        this.startAutoBackup()
      }

      this.isInitialized = true

      return {
        success: true,
        timestamp: Date.now()
      }

    } catch (error) {
      return {
        success: false,
        error: `集成服务初始化失败: ${error instanceof Error ? error.message : String(error)}`,
        timestamp: Date.now()
      }
    }
  }

  /**
   * 销毁集成服务
   */
  destroy(): void {
    this.stopAutoSave()
    this.stopAutoBackup()
    this.storageService.destroy()
    this.isInitialized = false
  }

  // ============ 数据操作 ============

  /**
   * 保存装饰数据
   */
  async saveDecorationData(): Promise<StorageResult> {
    try {
      const manager = this.decorationSystem.getManager()
      const result = await this.storageService.saveDecorationManager(manager, this.config.userId)
      
      if (result.success) {
        this.events.onSaveSuccess?.(manager)
      } else {
        this.events.onSaveError?.(result.error || '保存失败')
      }

      return result

    } catch (error) {
      const errorMessage = `保存装饰数据失败: ${error instanceof Error ? error.message : String(error)}`
      this.events.onSaveError?.(errorMessage)
      
      return {
        success: false,
        error: errorMessage,
        timestamp: Date.now()
      }
    }
  }

  /**
   * 加载装饰数据
   */
  async loadDecorationData(): Promise<StorageResult<DecorationManager>> {
    try {
      const result = await this.storageService.loadDecorationManager(this.config.userId)
      
      if (result.success && result.data) {
        this.decorationSystem.importState(result.data)
        this.events.onLoadSuccess?.(result.data)
      } else if (result.error) {
        this.events.onLoadError?.(result.error)
      }

      return result

    } catch (error) {
      const errorMessage = `加载装饰数据失败: ${error instanceof Error ? error.message : String(error)}`
      this.events.onLoadError?.(errorMessage)
      
      return {
        success: false,
        error: errorMessage,
        timestamp: Date.now()
      }
    }
  }

  /**
   * 创建数据备份
   */
  async createBackup(description?: string): Promise<StorageResult> {
    try {
      const manager = this.decorationSystem.getManager()
      const result = await this.storageService.createBackup(manager, description)
      
      if (result.success && result.data) {
        this.events.onBackupCreated?.(result.data.id)
      }

      return result

    } catch (error) {
      return {
        success: false,
        error: `创建备份失败: ${error instanceof Error ? error.message : String(error)}`,
        timestamp: Date.now()
      }
    }
  }

  /**
   * 恢复数据备份
   */
  async restoreBackup(backupId: string): Promise<StorageResult> {
    try {
      const result = await this.storageService.restoreBackup(backupId)
      
      if (result.success && result.data) {
        this.decorationSystem.importState(result.data)
        this.events.onLoadSuccess?.(result.data)
        
        // 保存恢复后的数据
        await this.saveDecorationData()
      }

      return result

    } catch (error) {
      return {
        success: false,
        error: `恢复备份失败: ${error instanceof Error ? error.message : String(error)}`,
        timestamp: Date.now()
      }
    }
  }

  /**
   * 同步数据到远程
   */
  async syncToRemote(): Promise<StorageResult> {
    if (!this.config.syncEnabled || !this.config.userId) {
      return {
        success: false,
        error: '同步未启用或用户ID未设置',
        timestamp: Date.now()
      }
    }

    try {
      const manager = this.decorationSystem.getManager()
      const result = await this.storageService.syncToRemote(manager, this.config.userId)
      
      if (result.success) {
        this.events.onSyncComplete?.()
      } else {
        this.events.onSyncError?.(result.error || '同步失败')
      }

      return result

    } catch (error) {
      const errorMessage = `同步到远程失败: ${error instanceof Error ? error.message : String(error)}`
      this.events.onSyncError?.(errorMessage)
      
      return {
        success: false,
        error: errorMessage,
        timestamp: Date.now()
      }
    }
  }

  /**
   * 从远程同步数据
   */
  async syncFromRemote(): Promise<StorageResult> {
    if (!this.config.syncEnabled || !this.config.userId) {
      return {
        success: false,
        error: '同步未启用或用户ID未设置',
        timestamp: Date.now()
      }
    }

    try {
      const result = await this.storageService.syncFromRemote(this.config.userId)
      
      if (result.success && result.data) {
        this.decorationSystem.importState(result.data)
        this.events.onLoadSuccess?.(result.data)
        this.events.onSyncComplete?.()
      } else if (result.error) {
        this.events.onSyncError?.(result.error)
      }

      return result

    } catch (error) {
      const errorMessage = `从远程同步失败: ${error instanceof Error ? error.message : String(error)}`
      this.events.onSyncError?.(errorMessage)
      
      return {
        success: false,
        error: errorMessage,
        timestamp: Date.now()
      }
    }
  }

  // ============ 自动化功能 ============

  /**
   * 启动自动保存
   */
  private startAutoSave(): void {
    if (this.autoSaveTimer) {
      clearInterval(this.autoSaveTimer)
    }

    this.autoSaveTimer = setInterval(async () => {
      await this.saveDecorationData()
    }, this.config.autoSaveInterval)
  }

  /**
   * 停止自动保存
   */
  private stopAutoSave(): void {
    if (this.autoSaveTimer) {
      clearInterval(this.autoSaveTimer)
      this.autoSaveTimer = undefined
    }
  }

  /**
   * 启动自动备份
   */
  private startAutoBackup(): void {
    if (this.backupTimer) {
      clearInterval(this.backupTimer)
    }

    this.backupTimer = setInterval(async () => {
      await this.createBackup(`自动备份 ${new Date().toLocaleString()}`)
      await this.cleanupOldBackups()
    }, this.config.backupInterval)
  }

  /**
   * 停止自动备份
   */
  private stopAutoBackup(): void {
    if (this.backupTimer) {
      clearInterval(this.backupTimer)
      this.backupTimer = undefined
    }
  }

  /**
   * 清理旧备份
   */
  private async cleanupOldBackups(): Promise<void> {
    try {
      const backupListResult = await this.storageService.getBackupList()
      
      if (backupListResult.success && backupListResult.data) {
        const backups = backupListResult.data
        
        if (backups.length > this.config.maxBackups) {
          // 按时间排序，删除最旧的备份
          const sortedBackups = backups.sort((a, b) => b.timestamp - a.timestamp)
          const backupsToDelete = sortedBackups.slice(this.config.maxBackups)
          
          for (const backup of backupsToDelete) {
            await this.storageService.deleteBackup(backup.id)
          }
        }
      }
    } catch (error) {
      console.warn('清理旧备份失败:', error)
    }
  }

  /**
   * 设置装饰系统事件监听
   */
  private setupSystemEventListeners(): void {
    // 监听装饰系统的重要事件，触发自动保存
    this.decorationSystem.on('decorationPurchased', () => {
      this.saveDecorationData()
    })

    this.decorationSystem.on('decorationPlaced', () => {
      this.saveDecorationData()
    })

    this.decorationSystem.on('decorationRemoved', () => {
      this.saveDecorationData()
    })

    this.decorationSystem.on('themeApplied', () => {
      this.saveDecorationData()
    })

    this.decorationSystem.on('themeUnlocked', () => {
      this.saveDecorationData()
    })
  }

  // ============ 事件管理 ============

  /**
   * 注册事件监听器
   */
  on<K extends keyof IntegrationEvents>(event: K, callback: IntegrationEvents[K]): void {
    this.events[event] = callback
  }

  /**
   * 移除事件监听器
   */
  off<K extends keyof IntegrationEvents>(event: K): void {
    delete this.events[event]
  }

  // ============ 公共方法 ============

  /**
   * 获取存储服务实例
   */
  getStorageService(): DecorationStorageService {
    return this.storageService
  }

  /**
   * 获取装饰系统实例
   */
  getDecorationSystem(): DecorationSystem {
    return this.decorationSystem
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<IntegrationConfig>): void {
    const oldConfig = { ...this.config }
    this.config = { ...this.config, ...newConfig }

    // 重启自动保存（如果配置改变）
    if (oldConfig.autoSaveEnabled !== this.config.autoSaveEnabled ||
        oldConfig.autoSaveInterval !== this.config.autoSaveInterval) {
      this.stopAutoSave()
      if (this.config.autoSaveEnabled) {
        this.startAutoSave()
      }
    }

    // 重启自动备份（如果配置改变）
    if (oldConfig.backupEnabled !== this.config.backupEnabled ||
        oldConfig.backupInterval !== this.config.backupInterval) {
      this.stopAutoBackup()
      if (this.config.backupEnabled) {
        this.startAutoBackup()
      }
    }

    // 更新存储服务配置
    if (newConfig.storageConfig) {
      this.storageService.updateConfig(newConfig.storageConfig)
    }
  }

  /**
   * 获取当前配置
   */
  getConfig(): IntegrationConfig {
    return { ...this.config }
  }

  /**
   * 获取同步状态
   */
  getSyncStatus() {
    return this.storageService.getSyncStatus()
  }

  /**
   * 检查是否已初始化
   */
  isReady(): boolean {
    return this.isInitialized
  }

  /**
   * 导出所有数据（用于调试或迁移）
   */
  async exportAllData(): Promise<{
    manager: DecorationManager
    backups: any[]
    config: IntegrationConfig
    syncStatus: any
  }> {
    const manager = this.decorationSystem.getManager()
    const backupListResult = await this.storageService.getBackupList()
    const backups = backupListResult.success ? backupListResult.data || [] : []
    
    return {
      manager,
      backups,
      config: this.getConfig(),
      syncStatus: this.getSyncStatus()
    }
  }

  /**
   * 重置所有数据（用于测试或重新开始）
   */
  async resetAllData(): Promise<StorageResult> {
    try {
      // 重置装饰系统状态
      const defaultManager = {
        ownedDecorations: {},
        placedDecorations: [],
        currentTheme: 'natural_paradise',
        unlockedThemes: ['natural_paradise'],
        beautyStats: {
          totalBeauty: 0,
          beautyByType: {} as any,
          beautyHistory: []
        },
        shopState: {
          lastVisitTime: Date.now(),
          recentPurchases: [],
          wishlist: []
        }
      }

      this.decorationSystem.importState(defaultManager)
      
      // 保存重置后的状态
      const saveResult = await this.saveDecorationData()
      
      return saveResult

    } catch (error) {
      return {
        success: false,
        error: `重置数据失败: ${error instanceof Error ? error.message : String(error)}`,
        timestamp: Date.now()
      }
    }
  }
}

export default DecorationIntegrationService 