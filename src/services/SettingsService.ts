import { 
  UserSettings, 
  SettingsCategory, 
  SettingsValidation, 
  SettingsEvent, 
  SettingsEventType,
  PerformanceMode,
  CameraResolution,
  ExposureMode,
  FocusMode,
  AudioQuality,
  ThemeMode,
  LayoutDensity,
  BreakType,
  BackupFrequency,
  BackupLocation,
  SyncFrequency,
  ConflictResolution,
  ExportFormat,
  MergeStrategy,
  EncryptionLevel,
  Priority,
  GoalCategory,
  ValidationError,
  ValidationWarning,
  ValidationSeverity
} from '../types/settings.types'

/**
 * 设置管理服务
 * 负责用户设置的加载、保存、验证和同步
 */
export class SettingsService {
  private settings: UserSettings
  private listeners: Map<SettingsEventType, Array<(event: SettingsEvent) => void>>
  private validationRules: Map<string, (value: any) => boolean>
  private storageKey = 'farm-game-settings'
  private isInitialized = false

  constructor() {
    this.settings = this.getDefaultSettings()
    this.listeners = new Map()
    this.validationRules = new Map()
    this.initializeValidationRules()
  }

  /**
   * 初始化设置服务
   */
  async initialize(): Promise<void> {
    try {
      await this.loadSettings()
      this.isInitialized = true
      this.emitEvent(SettingsEventType.LOADED, SettingsCategory.GENERAL, 'all', null, this.settings)
    } catch (error) {
      console.error('设置服务初始化失败:', error)
      throw error
    }
  }

  /**
   * 获取默认设置
   */
  private getDefaultSettings(): UserSettings {
    return {
      general: {
        language: 'zh-CN',
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        autoStart: false,
        minimizeToTray: true,
        confirmBeforeExit: true,
        autoSaveInterval: 300, // 5分钟
        performanceMode: PerformanceMode.BALANCED
      },
      camera: {
        enabled: true,
        deviceId: '',
        resolution: { width: 1280, height: 720, label: '720p HD' },
        fps: 30,
        brightness: 50,
        contrast: 50,
        saturation: 50,
        flipHorizontal: false,
        flipVertical: false,
        exposureMode: ExposureMode.AUTO,
        focusMode: FocusMode.AUTO,
        nightVision: false,
        motionDetection: true
      },
      audio: {
        enabled: true,
        volume: {
          master: 80,
          effects: 70,
          background: 60,
          voice: 90,
          notifications: 85,
          muted: false
        },
        effects: {
          enabled: true,
          reverb: 20,
          bass: 50,
          treble: 50,
          spatialAudio: false,
          noiseReduction: true
        },
        notifications: {
          playOnFocusLoss: true,
          playOnAchievement: true,
          playOnReminder: true,
          playOnError: true,
          customSounds: {}
        },
        devices: {
          outputDeviceId: 'default',
          inputDeviceId: 'default',
          outputDevices: [],
          inputDevices: []
        },
        quality: AudioQuality.MEDIUM
      },
      focus: {
        enabled: true,
        sensitivity: {
          movement: 70,
          eyeTracking: 60,
          postureDetection: 75,
          faceDetection: 80,
          handGestures: 50
        },
        targets: {
          dailyGoals: {
            focusTime: 120, // 2小时
            sessionCount: 4,
            breakFrequency: 25, // 番茄钟
            productivityScore: 80
          },
          customGoals: [],
          activeGoalId: null
        },
        detection: {
          enableFaceDetection: true,
          enablePostureDetection: true,
          enableEyeTracking: false,
          enableHandGestures: false,
          confidenceThreshold: 75,
          detectionInterval: 1000,
          smoothingFactor: 0.3
        },
        breaks: {
          enabled: true,
          frequency: 25,
          duration: 5,
          type: BreakType.SHORT,
          reminders: true,
          forced: false,
          activities: []
        },
        rewards: {
          enabled: true,
          pointsPerMinute: 1,
          bonusMultiplier: 1.5,
          achievementRewards: true,
          levelUpRewards: true,
          streakRewards: true,
          customRewards: []
        }
      },
      privacy: {
        dataCollection: {
          behaviorAnalytics: true,
          performanceMetrics: true,
          errorReporting: true,
          usageStatistics: true,
          cameraData: false,
          audioData: false,
          locationData: false
        },
        sharing: {
          analytics: false,
          research: false,
          marketing: false,
          thirdParty: false,
          aggregatedData: true,
          anonymizedData: true
        },
        retention: {
          behaviorData: 30,
          performanceData: 7,
          errorLogs: 3,
          userGeneratedContent: 365,
          autoDelete: true
        },
        consent: {
          analytics: false,
          marketing: false,
          research: false,
          thirdParty: false,
          lastUpdated: new Date(),
          version: '1.0.0'
        },
        anonymization: true,
        encryption: {
          enabled: true,
          level: EncryptionLevel.STANDARD,
          keyRotation: true,
          localEncryption: true
        }
      },
      notifications: {
        enabled: true,
        types: {
          achievements: true,
          reminders: true,
          breaks: true,
          goals: true,
          system: true,
          social: false,
          marketing: false
        },
        delivery: {
          push: true,
          email: false,
          sms: false,
          desktop: true,
          sound: true,
          vibration: false
        },
        schedule: {
          quietHoursEnabled: true,
          quietStart: '22:00',
          quietEnd: '08:00',
          weekendsOnly: false,
          workdaysOnly: false,
          customSchedule: []
        },
        priority: {
          high: true,
          medium: true,
          low: false,
          filterByPriority: true
        }
      },
      appearance: {
        theme: {
          mode: ThemeMode.AUTO,
          primary: '#4CAF50',
          secondary: '#81C784',
          accent: '#FFC107',
          background: '#FAFAFA',
          surface: '#FFFFFF',
          darkMode: false,
          autoTheme: true,
          customThemes: []
        },
        layout: {
          density: LayoutDensity.COMFORTABLE,
          sidebar: {
            visible: true,
            collapsed: false,
            position: 'left',
            width: 280
          },
          toolbar: {
            visible: true,
            position: 'top',
            items: ['home', 'settings', 'help'],
            customizable: true
          },
          statusBar: true,
          fullscreen: false,
          compactMode: false
        },
        accessibility: {
          enabled: false,
          highContrast: false,
          largeText: false,
          screenReader: false,
          keyboardNavigation: true,
          reducedMotion: false,
          colorBlindSupport: false,
          fontSize: 14,
          lineHeight: 1.4
        },
        customization: {
          wallpaper: '',
          animations: true,
          transitions: true,
          effects: true,
          shortcuts: {},
          widgets: []
        }
      },
      data: {
        storage: {
          location: 'local',
          maxSize: 1024, // 1GB
          compression: true,
          autoCleanup: true,
          cleanupThreshold: 900 // 900MB
        },
        backup: {
          enabled: true,
          frequency: BackupFrequency.DAILY,
          retention: 7,
          location: BackupLocation.LOCAL,
          encryption: true,
          compression: true,
          incremental: true
        },
        sync: {
          enabled: false,
          frequency: SyncFrequency.EVERY_HOUR,
          conflicts: ConflictResolution.ASK_USER,
          bandwidth: {
            maxUpload: 1024,
            maxDownload: 2048,
            limitEnabled: false
          },
          devices: []
        },
        export: {
          format: ExportFormat.JSON,
          includePersonalData: false,
          includeAnalytics: true,
          includeSettings: true,
          compression: true,
          encryption: true
        },
        import: {
          validateData: true,
          mergeStrategy: MergeStrategy.MERGE,
          backupBeforeImport: true,
          skipErrors: false
        },
        cache: {
          enabled: true,
          maxSize: 256, // 256MB
          maxAge: 24, // 24小时
          cleanupFrequency: 1, // 1小时
          preload: true
        }
      }
    }
  }

  /**
   * 初始化验证规则
   */
  private initializeValidationRules(): void {
    // 通用设置验证
    this.validationRules.set('general.autoSaveInterval', (value: number) => value >= 60 && value <= 3600)
    
    // 摄像头设置验证
    this.validationRules.set('camera.fps', (value: number) => value >= 15 && value <= 60)
    this.validationRules.set('camera.brightness', (value: number) => value >= 0 && value <= 100)
    this.validationRules.set('camera.contrast', (value: number) => value >= 0 && value <= 100)
    this.validationRules.set('camera.saturation', (value: number) => value >= 0 && value <= 100)

    // 音频设置验证
    this.validationRules.set('audio.volume.master', (value: number) => value >= 0 && value <= 100)
    this.validationRules.set('audio.volume.effects', (value: number) => value >= 0 && value <= 100)
    this.validationRules.set('audio.volume.background', (value: number) => value >= 0 && value <= 100)
    this.validationRules.set('audio.volume.voice', (value: number) => value >= 0 && value <= 100)
    this.validationRules.set('audio.volume.notifications', (value: number) => value >= 0 && value <= 100)

    // 专注设置验证
    this.validationRules.set('focus.sensitivity.movement', (value: number) => value >= 0 && value <= 100)
    this.validationRules.set('focus.sensitivity.eyeTracking', (value: number) => value >= 0 && value <= 100)
    this.validationRules.set('focus.sensitivity.postureDetection', (value: number) => value >= 0 && value <= 100)
    this.validationRules.set('focus.sensitivity.faceDetection', (value: number) => value >= 0 && value <= 100)
    this.validationRules.set('focus.sensitivity.handGestures', (value: number) => value >= 0 && value <= 100)

    // 数据保留验证
    this.validationRules.set('privacy.retention.behaviorData', (value: number) => value >= 1 && value <= 365)
    this.validationRules.set('privacy.retention.performanceData', (value: number) => value >= 1 && value <= 30)
    this.validationRules.set('privacy.retention.errorLogs', (value: number) => value >= 1 && value <= 7)
  }

  /**
   * 加载设置
   */
  async loadSettings(): Promise<UserSettings> {
    try {
      const stored = localStorage.getItem(this.storageKey)
      if (stored) {
        const parsedSettings = JSON.parse(stored)
        this.settings = this.mergeWithDefaults(parsedSettings)
      }
      return this.settings
    } catch (error) {
      console.error('加载设置失败:', error)
      this.settings = this.getDefaultSettings()
      return this.settings
    }
  }

  /**
   * 保存设置
   */
  async saveSettings(settings?: UserSettings): Promise<void> {
    try {
      const settingsToSave = settings || this.settings
      const validation = this.validateSettings(settingsToSave)
      
      if (!validation.isValid) {
        throw new Error(`设置验证失败: ${validation.errors.map(e => e.message).join(', ')}`)
      }

      localStorage.setItem(this.storageKey, JSON.stringify(settingsToSave))
      this.settings = settingsToSave
      
      this.emitEvent(SettingsEventType.SAVED, SettingsCategory.GENERAL, 'all', null, settingsToSave)
    } catch (error) {
      console.error('保存设置失败:', error)
      throw error
    }
  }

  /**
   * 获取设置
   */
  getSettings(): UserSettings {
    return { ...this.settings }
  }

  /**
   * 获取特定类别的设置
   */
  getCategorySettings<T>(category: SettingsCategory): T {
    return { ...this.settings[category] } as T
  }

  /**
   * 更新设置
   */
  async updateSettings(category: SettingsCategory, key: string, value: any): Promise<void> {
    const oldValue = this.getNestedValue(this.settings, `${category}.${key}`)
    
    try {
      this.setNestedValue(this.settings, `${category}.${key}`, value)
      
      // 验证更新后的设置
      const validation = this.validateSettings(this.settings)
      if (!validation.isValid) {
        // 回滚更改
        this.setNestedValue(this.settings, `${category}.${key}`, oldValue)
        throw new Error(`设置验证失败: ${validation.errors.map(e => e.message).join(', ')}`)
      }

      await this.saveSettings()
      this.emitEvent(SettingsEventType.CHANGED, category, key, oldValue, value)
    } catch (error) {
      console.error('更新设置失败:', error)
      throw error
    }
  }

  /**
   * 批量更新设置
   */
  async updateMultipleSettings(updates: Array<{ category: SettingsCategory; key: string; value: any }>): Promise<void> {
    const oldValues: Array<{ category: SettingsCategory; key: string; value: any }> = []
    
    try {
      // 应用所有更改
      for (const update of updates) {
        const oldValue = this.getNestedValue(this.settings, `${update.category}.${update.key}`)
        oldValues.push({ category: update.category, key: update.key, value: oldValue })
        this.setNestedValue(this.settings, `${update.category}.${update.key}`, update.value)
      }

      // 验证所有更改
      const validation = this.validateSettings(this.settings)
      if (!validation.isValid) {
        // 回滚所有更改
        for (const oldValue of oldValues) {
          this.setNestedValue(this.settings, `${oldValue.category}.${oldValue.key}`, oldValue.value)
        }
        throw new Error(`设置验证失败: ${validation.errors.map(e => e.message).join(', ')}`)
      }

      await this.saveSettings()

      // 发出所有更改事件
      for (let i = 0; i < updates.length; i++) {
        this.emitEvent(
          SettingsEventType.CHANGED,
          updates[i].category,
          updates[i].key,
          oldValues[i].value,
          updates[i].value
        )
      }
    } catch (error) {
      console.error('批量更新设置失败:', error)
      throw error
    }
  }

  /**
   * 重置设置到默认值
   */
  async resetSettings(category?: SettingsCategory): Promise<void> {
    try {
      const oldSettings = { ...this.settings }
      
      if (category) {
        const defaultSettings = this.getDefaultSettings()
        this.settings[category] = defaultSettings[category]
      } else {
        this.settings = this.getDefaultSettings()
      }

      await this.saveSettings()
      this.emitEvent(SettingsEventType.RESET, category || SettingsCategory.GENERAL, 'all', oldSettings, this.settings)
    } catch (error) {
      console.error('重置设置失败:', error)
      throw error
    }
  }

  /**
   * 验证设置
   */
  validateSettings(settings: UserSettings): SettingsValidation {
    const errors: ValidationError[] = []
    const warnings: ValidationWarning[] = []

    // 验证每个设置值
    for (const [path, validator] of this.validationRules.entries()) {
      const value = this.getNestedValue(settings, path)
      if (value !== undefined && !validator(value)) {
        errors.push({
          category: path.split('.')[0] as SettingsCategory,
          key: path.split('.').slice(1).join('.'),
          message: `设置值 ${path} 无效: ${value}`,
          severity: ValidationSeverity.ERROR
        })
      }
    }

    // 业务逻辑验证
    this.validateBusinessRules(settings, errors, warnings)

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    }
  }

  /**
   * 验证业务规则
   */
  private validateBusinessRules(settings: UserSettings, errors: ValidationError[], warnings: ValidationWarning[]): void {
    // 验证专注时间和休息时间的比例
    const focusTime = settings.focus.breaks.frequency
    const breakTime = settings.focus.breaks.duration
    if (focusTime > 0 && breakTime > 0) {
      const ratio = breakTime / focusTime
      if (ratio > 0.3) {
        warnings.push({
          category: SettingsCategory.FOCUS,
          key: 'breaks',
          message: '休息时间占专注时间的比例过高，可能影响专注效果',
          suggestion: '建议将休息时间调整为专注时间的20%以内'
        })
      }
    }

    // 验证数据保留期限的一致性
    const behaviorRetention = settings.privacy.retention.behaviorData
    const performanceRetention = settings.privacy.retention.performanceData
    if (behaviorRetention < performanceRetention) {
      warnings.push({
        category: SettingsCategory.PRIVACY,
        key: 'retention',
        message: '行为数据保留期限短于性能数据，可能影响数据分析的完整性',
        suggestion: '建议将行为数据保留期限设置为不少于性能数据保留期限'
      })
    }

    // 验证音频音量设置
    if (settings.audio.volume.master < 10 && settings.notifications.delivery.sound) {
      warnings.push({
        category: SettingsCategory.AUDIO,
        key: 'volume.master',
        message: '主音量过低，可能无法听到通知声音',
        suggestion: '建议将主音量调整到至少10%'
      })
    }
  }

  /**
   * 导出设置
   */
  async exportSettings(): Promise<string> {
    try {
      const exportData = {
        version: '1.0.0',
        timestamp: new Date().toISOString(),
        settings: this.settings
      }
      return JSON.stringify(exportData, null, 2)
    } catch (error) {
      console.error('导出设置失败:', error)
      throw error
    }
  }

  /**
   * 导入设置
   */
  async importSettings(data: string): Promise<void> {
    try {
      const importData = JSON.parse(data)
      
      if (!importData.settings) {
        throw new Error('无效的设置数据格式')
      }

      const validation = this.validateSettings(importData.settings)
      if (!validation.isValid) {
        throw new Error(`导入的设置无效: ${validation.errors.map(e => e.message).join(', ')}`)
      }

      const oldSettings = { ...this.settings }
      this.settings = this.mergeWithDefaults(importData.settings)
      
      await this.saveSettings()
      this.emitEvent(SettingsEventType.CHANGED, SettingsCategory.GENERAL, 'import', oldSettings, this.settings)
    } catch (error) {
      console.error('导入设置失败:', error)
      throw error
    }
  }

  /**
   * 添加事件监听器
   */
  addEventListener(type: SettingsEventType, listener: (event: SettingsEvent) => void): void {
    if (!this.listeners.has(type)) {
      this.listeners.set(type, [])
    }
    this.listeners.get(type)!.push(listener)
  }

  /**
   * 移除事件监听器
   */
  removeEventListener(type: SettingsEventType, listener: (event: SettingsEvent) => void): void {
    const typeListeners = this.listeners.get(type)
    if (typeListeners) {
      const index = typeListeners.indexOf(listener)
      if (index > -1) {
        typeListeners.splice(index, 1)
      }
    }
  }

  /**
   * 发出事件
   */
  private emitEvent(type: SettingsEventType, category: SettingsCategory, key: string, oldValue: any, newValue: any): void {
    const event: SettingsEvent = {
      type,
      category,
      key,
      oldValue,
      newValue,
      timestamp: new Date()
    }

    const typeListeners = this.listeners.get(type)
    if (typeListeners) {
      typeListeners.forEach(listener => {
        try {
          listener(event)
        } catch (error) {
          console.error('设置事件监听器错误:', error)
        }
      })
    }
  }

  /**
   * 与默认设置合并
   */
  private mergeWithDefaults(settings: Partial<UserSettings>): UserSettings {
    const defaults = this.getDefaultSettings()
    return this.deepMerge(defaults, settings) as UserSettings
  }

  /**
   * 深度合并对象
   */
  private deepMerge(target: any, source: any): any {
    const result = { ...target }
    
    for (const key in source) {
      if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
        result[key] = this.deepMerge(target[key] || {}, source[key])
      } else {
        result[key] = source[key]
      }
    }
    
    return result
  }

  /**
   * 获取嵌套值
   */
  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj)
  }

  /**
   * 设置嵌套值
   */
  private setNestedValue(obj: any, path: string, value: any): void {
    const keys = path.split('.')
    const lastKey = keys.pop()!
    const target = keys.reduce((current, key) => {
      if (!current[key] || typeof current[key] !== 'object') {
        current[key] = {}
      }
      return current[key]
    }, obj)
    target[lastKey] = value
  }

  /**
   * 获取服务状态
   */
  isReady(): boolean {
    return this.isInitialized
  }
}

// 单例实例
export const settingsService = new SettingsService() 