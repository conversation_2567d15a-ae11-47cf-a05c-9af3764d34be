// 行为分析服务 - 用户行为追踪、统计分析和报告

import { DatabaseManager } from '../storage/DatabaseManager'
import { BehaviorRecord, BehaviorEvent } from '../types/user'

export interface BehaviorEventData {
  type: string
  category: string
  userId?: string
  value?: number
  details?: string
  metadata?: Record<string, any>
}

export interface BehaviorFilter {
  userId?: string
  category?: string
  type?: string
  startTime?: number
  endTime?: number
  limit?: number
}

export interface BehaviorStatistics {
  totalEvents: number
  uniqueUsers: number
  averageSessionLength: number
  topEvents: Array<{ type: string; count: number }>
  topCategories: Array<{ category: string; count: number }>
  timeRange: { start: number; end: number }
  eventsByDay: Record<string, number>
  userActivity: Record<string, number>
}

export interface FocusAnalytics {
  totalFocusTime: number
  averageFocusScore: number
  focusSessions: number
  focusBreaks: number
  bestStreak: number
  focusTrends: Array<{ date: string; score: number; duration: number }>
}

export interface GameplayAnalytics {
  totalPlayTime: number
  sessionCount: number
  averageSessionDuration: number
  cropsPlanted: number
  cropsHarvested: number
  achievementsUnlocked: number
  levelProgression: Array<{ level: number; timestamp: number }>
}

export class BehaviorAnalyticsService {
  private dbManager: DatabaseManager
  private eventQueue: BehaviorEventData[] = []
  private flushTimer: number | null = null
  private readonly batchSize = 10
  private readonly flushInterval = 5000 // 5秒

  constructor(dbManager: DatabaseManager) {
    this.dbManager = dbManager
    this.startBatchProcessing()
  }

  /**
   * 记录行为事件
   */
  async trackEvent(eventData: BehaviorEventData): Promise<boolean> {
    // 添加到队列以进行批处理
    this.eventQueue.push({
      ...eventData,
      metadata: {
        ...eventData.metadata,
        userAgent: navigator.userAgent,
        timestamp: new Date().toISOString(),
        url: window.location.href,
        screenResolution: `${screen.width}x${screen.height}`,
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
      }
    })

    // 如果队列满了，立即处理
    if (this.eventQueue.length >= this.batchSize) {
      return await this.flushEvents()
    }

    return true
  }

  /**
   * 批量处理事件队列
   */
  private async flushEvents(): Promise<boolean> {
    if (this.eventQueue.length === 0) {
      return true
    }

    const eventsToProcess = [...this.eventQueue]
    this.eventQueue = []

    try {
      for (const eventData of eventsToProcess) {
        await this.dbManager.logBehavior(eventData)
      }
      
      console.log(`Flushed ${eventsToProcess.length} behavior events`)
      return true
    } catch (error) {
      console.error('Failed to flush behavior events:', error)
      // 失败时将事件放回队列
      this.eventQueue.unshift(...eventsToProcess)
      return false
    }
  }

  /**
   * 启动批处理
   */
  private startBatchProcessing(): void {
    this.flushTimer = window.setInterval(async () => {
      await this.flushEvents()
    }, this.flushInterval)
  }

  /**
   * 停止批处理
   */
  private stopBatchProcessing(): void {
    if (this.flushTimer !== null) {
      clearInterval(this.flushTimer)
      this.flushTimer = null
    }
  }

  /**
   * 获取行为记录
   */
  async getBehaviorRecords(filter: BehaviorFilter = {}): Promise<BehaviorRecord[]> {
    let records = await this.dbManager.getBehaviorRecords(
      filter.userId,
      filter.category,
      filter.limit || 100
    )

    // 应用额外过滤条件
    if (filter.type) {
      records = records.filter(r => r.event.type === filter.type)
    }

    if (filter.startTime) {
      records = records.filter(r => r.timestamp >= filter.startTime!)
    }

    if (filter.endTime) {
      records = records.filter(r => r.timestamp <= filter.endTime!)
    }

    return records
  }

  /**
   * 生成行为统计报告
   */
  async generateBehaviorStatistics(filter: BehaviorFilter = {}): Promise<BehaviorStatistics> {
    const records = await this.getBehaviorRecords({ ...filter, limit: 1000 })

    if (records.length === 0) {
      return {
        totalEvents: 0,
        uniqueUsers: 0,
        averageSessionLength: 0,
        topEvents: [],
        topCategories: [],
        timeRange: { start: 0, end: 0 },
        eventsByDay: {},
        userActivity: {}
      }
    }

    // 统计基础信息
    const uniqueUsers = new Set(records.map(r => r.userId)).size
    const timestamps = records.map(r => r.timestamp)
    const timeRange = {
      start: Math.min(...timestamps),
      end: Math.max(...timestamps)
    }

    // 统计事件类型
    const eventCounts: Record<string, number> = {}
    const categoryCounts: Record<string, number> = {}
    const userActivity: Record<string, number> = {}
    const eventsByDay: Record<string, number> = {}

    records.forEach(record => {
      // 事件类型统计
      eventCounts[record.event.type] = (eventCounts[record.event.type] || 0) + 1
      
      // 类别统计
      categoryCounts[record.event.category] = (categoryCounts[record.event.category] || 0) + 1
      
      // 用户活跃度
      userActivity[record.userId] = (userActivity[record.userId] || 0) + 1
      
      // 按天统计
      const day = new Date(record.timestamp).toISOString().split('T')[0]
      eventsByDay[day] = (eventsByDay[day] || 0) + 1
    })

    // 排序统计
    const topEvents = Object.entries(eventCounts)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 10)
      .map(([type, count]) => ({ type, count }))

    const topCategories = Object.entries(categoryCounts)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 10)
      .map(([category, count]) => ({ category, count }))

    // 计算平均会话时长（基于游戏开始/结束事件）
    const sessionEvents = records.filter(r => 
      r.event.type === 'game_start' || r.event.type === 'game_end'
    )
    const averageSessionLength = this.calculateAverageSessionLength(sessionEvents)

    return {
      totalEvents: records.length,
      uniqueUsers,
      averageSessionLength,
      topEvents,
      topCategories,
      timeRange,
      eventsByDay,
      userActivity
    }
  }

  /**
   * 生成专注度分析报告
   */
  async generateFocusAnalytics(userId?: string, days: number = 30): Promise<FocusAnalytics> {
    const startTime = Date.now() - (days * 24 * 60 * 60 * 1000)
    
    const focusRecords = await this.getBehaviorRecords({
      userId,
      category: 'focus',
      startTime,
      limit: 1000
    })

    if (focusRecords.length === 0) {
      return {
        totalFocusTime: 0,
        averageFocusScore: 0,
        focusSessions: 0,
        focusBreaks: 0,
        bestStreak: 0,
        focusTrends: []
      }
    }

    // 统计专注数据
    let totalFocusTime = 0
    let totalFocusScore = 0
    let focusSessions = 0
    let focusBreaks = 0
    let bestStreak = 0
    let currentStreak = 0

    const dailyFocus: Record<string, { totalTime: number; totalScore: number; count: number }> = {}

    focusRecords.forEach(record => {
      const day = new Date(record.timestamp).toISOString().split('T')[0]
      
      if (!dailyFocus[day]) {
        dailyFocus[day] = { totalTime: 0, totalScore: 0, count: 0 }
      }

      if (record.event.type === 'focus_session') {
        focusSessions++
        const duration = record.event.value || 0
        totalFocusTime += duration
        
        dailyFocus[day].totalTime += duration
        dailyFocus[day].count++
        
        // 假设专注分数存储在details中
        const score = this.extractFocusScore(record.event.details)
        if (score > 0) {
          totalFocusScore += score
          dailyFocus[day].totalScore += score
          currentStreak += duration
          bestStreak = Math.max(bestStreak, currentStreak)
        } else {
          currentStreak = 0
        }
      } else if (record.event.type === 'focus_break') {
        focusBreaks++
        currentStreak = 0
      }
    })

    const averageFocusScore = focusSessions > 0 ? totalFocusScore / focusSessions : 0

    // 生成趋势数据
    const focusTrends = Object.entries(dailyFocus)
      .sort(([a], [b]) => a.localeCompare(b))
      .map(([date, data]) => ({
        date,
        score: data.count > 0 ? data.totalScore / data.count : 0,
        duration: data.totalTime
      }))

    return {
      totalFocusTime,
      averageFocusScore,
      focusSessions,
      focusBreaks,
      bestStreak,
      focusTrends
    }
  }

  /**
   * 生成游戏分析报告
   */
  async generateGameplayAnalytics(userId?: string, days: number = 30): Promise<GameplayAnalytics> {
    const startTime = Date.now() - (days * 24 * 60 * 60 * 1000)
    
    const gameplayRecords = await this.getBehaviorRecords({
      userId,
      category: 'gameplay',
      startTime,
      limit: 1000
    })

    let totalPlayTime = 0
    let sessionCount = 0
    let cropsPlanted = 0
    let cropsHarvested = 0
    let achievementsUnlocked = 0
    const levelProgression: Array<{ level: number; timestamp: number }> = []

    const sessionTimes: number[] = []

    gameplayRecords.forEach(record => {
      switch (record.event.type) {
        case 'game_session':
          sessionCount++
          const duration = record.event.value || 0
          totalPlayTime += duration
          sessionTimes.push(duration)
          break
        case 'crop_plant':
          cropsPlanted += record.event.value || 1
          break
        case 'crop_harvest':
          cropsHarvested += record.event.value || 1
          break
        case 'achievement_unlock':
          achievementsUnlocked++
          break
        case 'level_up':
          const level = record.event.value || 1
          levelProgression.push({ level, timestamp: record.timestamp })
          break
      }
    })

    const averageSessionDuration = sessionTimes.length > 0 
      ? sessionTimes.reduce((sum, time) => sum + time, 0) / sessionTimes.length 
      : 0

    return {
      totalPlayTime,
      sessionCount,
      averageSessionDuration,
      cropsPlanted,
      cropsHarvested,
      achievementsUnlocked,
      levelProgression: levelProgression.sort((a, b) => a.timestamp - b.timestamp)
    }
  }

  /**
   * 导出分析数据
   */
  async exportAnalytics(userId?: string, days: number = 30): Promise<{
    behavioral: BehaviorStatistics
    focus: FocusAnalytics
    gameplay: GameplayAnalytics
    exportTimestamp: number
  }> {
    const [behavioral, focus, gameplay] = await Promise.all([
      this.generateBehaviorStatistics({ userId }),
      this.generateFocusAnalytics(userId, days),
      this.generateGameplayAnalytics(userId, days)
    ])

    return {
      behavioral,
      focus,
      gameplay,
      exportTimestamp: Date.now()
    }
  }

  /**
   * 清理过期数据
   */
  async cleanupOldRecords(retentionDays: number = 90): Promise<number> {
    const cutoffTime = Date.now() - (retentionDays * 24 * 60 * 60 * 1000)
    const allRecords = await this.dbManager.getBehaviorRecords(undefined, undefined, 10000)
    
    const validRecords = allRecords.filter(record => record.timestamp > cutoffTime)
    const removedCount = allRecords.length - validRecords.length

    if (removedCount > 0) {
      await this.dbManager['storage'].save('behavior_records', validRecords)
      console.log(`Cleaned up ${removedCount} old behavior records`)
    }

    return removedCount
  }

  // 私有辅助方法

  private calculateAverageSessionLength(sessionEvents: BehaviorRecord[]): number {
    const sessions: Array<{ start: number; end?: number }> = []
    
    sessionEvents.forEach(event => {
      if (event.event.type === 'game_start') {
        sessions.push({ start: event.timestamp })
      } else if (event.event.type === 'game_end') {
        const lastSession = sessions[sessions.length - 1]
        if (lastSession && !lastSession.end) {
          lastSession.end = event.timestamp
        }
      }
    })

    const completeSessions = sessions.filter(s => s.end)
    if (completeSessions.length === 0) return 0

    const totalDuration = completeSessions.reduce((sum, session) => 
      sum + (session.end! - session.start), 0)
    
    return totalDuration / completeSessions.length
  }

  private extractFocusScore(details?: string): number {
    if (!details) return 0
    
    // 尝试从details字符串中提取专注分数
    const scoreMatch = details.match(/score[:\s]+(\d+\.?\d*)/i)
    return scoreMatch ? parseFloat(scoreMatch[1]) : 0
  }

  /**
   * 销毁服务
   */
  async destroy(): Promise<void> {
    this.stopBatchProcessing()
    // 处理剩余的事件
    await this.flushEvents()
  }

  // 便捷方法

  /**
   * 记录游戏开始
   */
  async trackGameStart(userId?: string): Promise<boolean> {
    return await this.trackEvent({
      type: 'game_start',
      category: 'system',
      userId,
      details: 'Game session started'
    })
  }

  /**
   * 记录游戏结束
   */
  async trackGameEnd(userId?: string, duration?: number): Promise<boolean> {
    return await this.trackEvent({
      type: 'game_end',
      category: 'system',
      userId,
      value: duration,
      details: duration ? `Game session ended after ${duration}ms` : 'Game session ended'
    })
  }

  /**
   * 记录作物种植
   */
  async trackCropPlant(userId?: string, cropType?: string): Promise<boolean> {
    return await this.trackEvent({
      type: 'crop_plant',
      category: 'gameplay',
      userId,
      value: 1,
      details: cropType ? `Planted ${cropType}` : 'Crop planted'
    })
  }

  /**
   * 记录作物收获
   */
  async trackCropHarvest(userId?: string, cropType?: string, quantity: number = 1): Promise<boolean> {
    return await this.trackEvent({
      type: 'crop_harvest',
      category: 'gameplay',
      userId,
      value: quantity,
      details: cropType ? `Harvested ${quantity} ${cropType}` : `Harvested ${quantity} crops`
    })
  }

  /**
   * 记录专注会话
   */
  async trackFocusSession(userId?: string, duration: number, score: number): Promise<boolean> {
    return await this.trackEvent({
      type: 'focus_session',
      category: 'focus',
      userId,
      value: duration,
      details: `Focus session completed: score ${score}, duration ${duration}ms`
    })
  }
} 