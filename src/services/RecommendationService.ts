// 个性化建议管理服务
// 负责建议的存储、检索、跟踪和用户反馈管理

import PersonalizedRecommendationEngine, {
  PersonalizedRecommendation,
  UserContext,
  RecommendationType,
  RecommendationPriority,
  RecommendationDifficulty,
  RecommendationConfig
} from './PersonalizedRecommendationEngine'

// 建议执行状态
export enum RecommendationStatus {
  ACTIVE = 'active',           // 活跃中
  COMPLETED = 'completed',     // 已完成
  DISMISSED = 'dismissed',     // 已忽略
  FAILED = 'failed',          // 执行失败
  EXPIRED = 'expired'         // 已过期
}

// 用户反馈类型
export enum FeedbackType {
  HELPFUL = 'helpful',         // 有帮助
  NOT_HELPFUL = 'not_helpful', // 无帮助
  IRRELEVANT = 'irrelevant',   // 不相关
  TOO_DIFFICULT = 'too_difficult', // 太困难
  TOO_EASY = 'too_easy',      // 太简单
  COMPLETED = 'completed',     // 已完成
  CUSTOM = 'custom'           // 自定义反馈
}

// 存储的建议记录
export interface StoredRecommendation extends PersonalizedRecommendation {
  userId: string
  status: RecommendationStatus
  assignedAt: Date
  lastInteractionAt?: Date
  completedAt?: Date
  dismissedAt?: Date
  executionProgress: number // 执行进度 0-1
  feedbacks: RecommendationFeedback[]
  trackingData: RecommendationTracking[]
}

// 用户反馈记录
export interface RecommendationFeedback {
  id: string
  recommendationId: string
  userId: string
  feedbackType: FeedbackType
  rating: number // 1-5星评分
  comment?: string
  timestamp: Date
  helpfulnessScore: number // 有用性评分
  relevanceScore: number   // 相关性评分
  difficultyScore: number  // 难度评分
}

// 建议跟踪数据
export interface RecommendationTracking {
  id: string
  recommendationId: string
  userId: string
  actionStep: string
  completedAt?: Date
  timeSpent: number // 花费时间（分钟）
  qualityRating: number // 执行质量评分 1-5
  notes?: string
  timestamp: Date
}

// 建议查询参数
export interface RecommendationQuery {
  userId?: string
  type?: RecommendationType[]
  priority?: RecommendationPriority[]
  status?: RecommendationStatus[]
  dateRange?: {
    start: Date
    end: Date
  }
  tags?: string[]
  limit?: number
  offset?: number
}

// 建议统计信息
export interface RecommendationStatistics {
  totalRecommendations: number
  activeRecommendations: number
  completedRecommendations: number
  completionRate: number
  averageRating: number
  typeDistribution: Record<RecommendationType, number>
  priorityDistribution: Record<RecommendationPriority, number>
  monthlyTrends: {
    month: string
    generated: number
    completed: number
    rating: number
  }[]
  userEngagement: {
    totalInteractions: number
    feedbackCount: number
    averageEngagementTime: number
  }
}

// 个性化建议服务类
export class RecommendationService {
  private recommendationEngine: PersonalizedRecommendationEngine
  private cache: Map<string, StoredRecommendation[]>
  private cacheExpiry: Map<string, number>

  constructor(recommendationEngine: PersonalizedRecommendationEngine) {
    this.recommendationEngine = recommendationEngine
    this.cache = new Map()
    this.cacheExpiry = new Map()
  }

  // 为用户生成新建议
  public async generateRecommendationsForUser(
    userId: string,
    userContext: UserContext,
    config?: Partial<RecommendationConfig>
  ): Promise<StoredRecommendation[]> {
    try {
      console.log(`为用户 ${userId} 生成新建议`)

      // 生成建议
      const generationResult = await this.recommendationEngine.generateRecommendations(
        userId,
        userContext,
        config
      )

      // 转换为存储格式
      const storedRecommendations: StoredRecommendation[] = generationResult.recommendations.map(rec => ({
        ...rec,
        userId,
        status: RecommendationStatus.ACTIVE,
        assignedAt: new Date(),
        executionProgress: 0,
        feedbacks: [],
        trackingData: []
      }))

      // 保存到模拟存储
      this.saveRecommendationsToCache(userId, storedRecommendations)

      console.log(`成功为用户 ${userId} 生成了 ${storedRecommendations.length} 个建议`)
      return storedRecommendations

    } catch (error) {
      console.error('生成用户建议失败:', error)
      throw new Error(`建议生成失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  // 获取用户的活跃建议
  public async getActiveRecommendations(userId: string): Promise<StoredRecommendation[]> {
    try {
      const allRecommendations = this.getRecommendationsFromCache(userId)
      return allRecommendations.filter(rec => rec.status === RecommendationStatus.ACTIVE)
    } catch (error) {
      console.error('获取活跃建议失败:', error)
      throw new Error(`获取建议失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  // 查询建议
  public async queryRecommendations(query: RecommendationQuery): Promise<StoredRecommendation[]> {
    try {
      if (!query.userId) return []
      
      let recommendations = this.getRecommendationsFromCache(query.userId)

      // 应用过滤器
      if (query.status) {
        recommendations = recommendations.filter(rec => query.status!.includes(rec.status))
      }
      if (query.type) {
        recommendations = recommendations.filter(rec => query.type!.includes(rec.type))
      }
      if (query.priority) {
        recommendations = recommendations.filter(rec => query.priority!.includes(rec.priority))
      }

      // 应用分页
      const offset = query.offset || 0
      const limit = query.limit || 50
      return recommendations.slice(offset, offset + limit)

    } catch (error) {
      console.error('查询建议失败:', error)
      throw new Error(`建议查询失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  // 更新建议状态
  public async updateRecommendationStatus(
    recommendationId: string,
    status: RecommendationStatus,
    userId: string
  ): Promise<void> {
    try {
      const recommendations = this.getRecommendationsFromCache(userId)
      const index = recommendations.findIndex(rec => rec.id === recommendationId)
      
      if (index === -1) {
        throw new Error('建议未找到')
      }

      recommendations[index].status = status
      recommendations[index].lastInteractionAt = new Date()

      if (status === RecommendationStatus.COMPLETED) {
        recommendations[index].completedAt = new Date()
        recommendations[index].executionProgress = 1.0
      } else if (status === RecommendationStatus.DISMISSED) {
        recommendations[index].dismissedAt = new Date()
      }

      this.saveRecommendationsToCache(userId, recommendations)
      console.log(`建议 ${recommendationId} 状态更新为 ${status}`)

    } catch (error) {
      console.error('更新建议状态失败:', error)
      throw new Error(`状态更新失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  // 记录用户反馈
  public async recordFeedback(
    recommendationId: string,
    userId: string,
    feedbackType: FeedbackType,
    rating: number,
    comment?: string,
    scores?: {
      helpfulness?: number
      relevance?: number
      difficulty?: number
    }
  ): Promise<void> {
    try {
      const feedback: RecommendationFeedback = {
        id: `feedback_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        recommendationId,
        userId,
        feedbackType,
        rating: Math.max(1, Math.min(5, rating)),
        comment,
        timestamp: new Date(),
        helpfulnessScore: scores?.helpfulness || rating,
        relevanceScore: scores?.relevance || rating,
        difficultyScore: scores?.difficulty || 3
      }

      const recommendations = this.getRecommendationsFromCache(userId)
      const recommendation = recommendations.find(rec => rec.id === recommendationId)
      
      if (recommendation) {
        recommendation.feedbacks.push(feedback)
        recommendation.lastInteractionAt = new Date()
        this.saveRecommendationsToCache(userId, recommendations)
      }

      console.log(`记录了建议 ${recommendationId} 的用户反馈`)

    } catch (error) {
      console.error('记录反馈失败:', error)
      throw new Error(`反馈记录失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  // 获取建议统计信息
  public async getRecommendationStatistics(userId: string): Promise<RecommendationStatistics> {
    try {
      const allRecommendations = this.getRecommendationsFromCache(userId)
      const allFeedbacks = allRecommendations.flatMap(rec => rec.feedbacks)

      const totalRecommendations = allRecommendations.length
      const activeRecommendations = allRecommendations.filter(r => r.status === RecommendationStatus.ACTIVE).length
      const completedRecommendations = allRecommendations.filter(r => r.status === RecommendationStatus.COMPLETED).length
      const completionRate = totalRecommendations > 0 ? completedRecommendations / totalRecommendations : 0

      const totalRating = allFeedbacks.reduce((sum, f) => sum + f.rating, 0)
      const averageRating = allFeedbacks.length > 0 ? totalRating / allFeedbacks.length : 0

      // 类型分布
      const typeDistribution = {} as Record<RecommendationType, number>
      Object.values(RecommendationType).forEach(type => {
        typeDistribution[type] = allRecommendations.filter(r => r.type === type).length
      })

      // 优先级分布
      const priorityDistribution = {} as Record<RecommendationPriority, number>
      Object.values(RecommendationPriority).forEach(priority => {
        priorityDistribution[priority] = allRecommendations.filter(r => r.priority === priority).length
      })

      return {
        totalRecommendations,
        activeRecommendations,
        completedRecommendations,
        completionRate,
        averageRating,
        typeDistribution,
        priorityDistribution,
        monthlyTrends: [],
        userEngagement: {
          totalInteractions: allRecommendations.filter(r => r.lastInteractionAt).length,
          feedbackCount: allFeedbacks.length,
          averageEngagementTime: 0
        }
      }

    } catch (error) {
      console.error('获取统计信息失败:', error)
      throw new Error(`统计信息获取失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  // 私有方法

  private saveRecommendationsToCache(userId: string, recommendations: StoredRecommendation[]): void {
    this.cache.set(userId, recommendations)
    this.cacheExpiry.set(userId, Date.now() + 24 * 60 * 60 * 1000) // 24小时缓存
  }

  private getRecommendationsFromCache(userId: string): StoredRecommendation[] {
    const expiry = this.cacheExpiry.get(userId)
    if (expiry && Date.now() > expiry) {
      this.cache.delete(userId)
      this.cacheExpiry.delete(userId)
      return []
    }
    return this.cache.get(userId) || []
  }
}

export default RecommendationService 