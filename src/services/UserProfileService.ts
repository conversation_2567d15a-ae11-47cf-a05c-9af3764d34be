// 用户档案管理服务

import { DatabaseManager } from '../storage/DatabaseManager'
import { 
  UserProfile, 
  UserPreferences, 
  UserStatistics,
  FocusStatistics 
} from '../types/user'

export interface CreateUserParams {
  username: string
  email?: string
  preferences?: Partial<UserPreferences>
}

export interface UpdateProfileParams {
  username?: string
  email?: string
  preferences?: Partial<UserPreferences>
}

export class UserProfileService {
  private dbManager: DatabaseManager
  private currentUserId: string | null = null

  constructor(dbManager: DatabaseManager) {
    this.dbManager = dbManager
  }

  /**
   * 创建新用户档案
   */
  async createUser(params: CreateUserParams): Promise<UserProfile | null> {
    try {
      const userId = this.generateUserId()
      const now = Date.now()

      const defaultPreferences: UserPreferences = {
        language: 'zh-CN',
        theme: 'auto',
        notifications: {
          gameProgress: true,
          achievements: true,
          dailyReminders: true
        },
        accessibility: {
          highContrast: false,
          fontSize: 'medium',
          reducedMotion: false
        },
        gameplay: {
          autoSave: true,
          autoSaveInterval: 5, // 5分钟
          showTutorials: true,
          soundEnabled: true,
          musicEnabled: true,
          effectsVolume: 80,
          musicVolume: 60
        }
      }

      const defaultStatistics: UserStatistics = {
        totalPlayTime: 0,
        sessionsPlayed: 0,
        achievementsUnlocked: 0,
        totalCropsPlanted: 0,
        totalCropsHarvested: 0,
        totalExperienceGained: 0,
        maxLevel: 1,
        focusStats: {
          totalFocusTime: 0,
          averageFocusScore: 0,
          bestFocusStreak: 0,
          totalFocusBreaks: 0,
          focusSessionsCompleted: 0,
          lastFocusSession: 0
        },
        lastUpdated: now
      }

      const userProfile: UserProfile = {
        id: userId,
        username: params.username,
        email: params.email,
        createdAt: now,
        lastLoginAt: now,
        preferences: {
          ...defaultPreferences,
          ...params.preferences
        },
        statistics: defaultStatistics
      }

      const success = await this.dbManager.saveUserProfile(userProfile)
      if (success) {
        this.currentUserId = userId
        
        // 记录用户创建事件
        await this.dbManager.logBehavior({
          type: 'profile_created',
          category: 'system',
          userId: userId,
          details: `New user: ${params.username}`
        })

        // 创建初始游戏进度
        await this.dbManager.createNewGameProgress(userId, params.username)

        return userProfile
      }
      
      return null
    } catch (error) {
      console.error('Failed to create user:', error)
      return null
    }
  }

  /**
   * 用户登录（模拟登录过程）
   */
  async loginUser(username: string): Promise<UserProfile | null> {
    try {
      const profile = await this.dbManager.getUserProfile()
      
      if (profile && profile.username === username) {
        // 更新最后登录时间
        profile.lastLoginAt = Date.now()
        await this.dbManager.saveUserProfile(profile)
        
        this.currentUserId = profile.id
        
        // 记录登录事件
        await this.dbManager.logBehavior({
          type: 'user_login',
          category: 'system',
          userId: profile.id,
          details: `User logged in: ${username}`
        })

        return profile
      }
      
      return null
    } catch (error) {
      console.error('Failed to login user:', error)
      return null
    }
  }

  /**
   * 用户登出
   */
  async logoutUser(): Promise<boolean> {
    try {
      if (this.currentUserId) {
        // 记录登出事件
        await this.dbManager.logBehavior({
          type: 'user_logout',
          category: 'system',
          userId: this.currentUserId,
          details: 'User logged out'
        })
        
        this.currentUserId = null
        return true
      }
      return false
    } catch (error) {
      console.error('Failed to logout user:', error)
      return false
    }
  }

  /**
   * 获取当前用户档案
   */
  async getCurrentUserProfile(): Promise<UserProfile | null> {
    return await this.dbManager.getUserProfile()
  }

  /**
   * 更新用户档案
   */
  async updateUserProfile(updates: UpdateProfileParams): Promise<boolean> {
    try {
      const currentProfile = await this.dbManager.getUserProfile()
      if (!currentProfile) {
        console.error('No current user profile found')
        return false
      }

      const updatedProfile: UserProfile = {
        ...currentProfile,
        ...updates,
        preferences: {
          ...currentProfile.preferences,
          ...updates.preferences
        }
      }

      const success = await this.dbManager.saveUserProfile(updatedProfile)
      
      if (success) {
        // 记录档案更新事件
        await this.dbManager.logBehavior({
          type: 'profile_updated',
          category: 'system',
          userId: currentProfile.id,
          details: `Profile updated: ${Object.keys(updates).join(', ')}`
        })
      }

      return success
    } catch (error) {
      console.error('Failed to update user profile:', error)
      return false
    }
  }

  /**
   * 更新用户偏好设置
   */
  async updateUserPreferences(preferences: Partial<UserPreferences>): Promise<boolean> {
    try {
      const success = await this.dbManager.updateUserPreferences({
        ...await this.dbManager.getUserPreferences() || {} as UserPreferences,
        ...preferences
      })

      if (success && this.currentUserId) {
        // 记录偏好更新事件
        await this.dbManager.logBehavior({
          type: 'preferences_updated',
          category: 'system',
          userId: this.currentUserId,
          details: `Preferences updated: ${Object.keys(preferences).join(', ')}`
        })
      }

      return success
    } catch (error) {
      console.error('Failed to update user preferences:', error)
      return false
    }
  }

  /**
   * 获取用户偏好设置
   */
  async getUserPreferences(): Promise<UserPreferences | null> {
    return await this.dbManager.getUserPreferences()
  }

  /**
   * 更新用户统计数据
   */
  async updateUserStatistics(statistics: Partial<UserStatistics>): Promise<boolean> {
    try {
      const success = await this.dbManager.updateUserStatistics(statistics)

      if (success && this.currentUserId) {
        // 记录统计更新事件
        await this.dbManager.logBehavior({
          type: 'statistics_updated',
          category: 'system',
          userId: this.currentUserId,
          details: `Statistics updated: ${Object.keys(statistics).join(', ')}`
        })
      }

      return success
    } catch (error) {
      console.error('Failed to update user statistics:', error)
      return false
    }
  }

  /**
   * 更新专注度统计
   */
  async updateFocusStatistics(focusStats: Partial<FocusStatistics>): Promise<boolean> {
    try {
      const currentProfile = await this.dbManager.getUserProfile()
      if (!currentProfile) {
        return false
      }

      const updatedStats: UserStatistics = {
        ...currentProfile.statistics,
        focusStats: {
          ...currentProfile.statistics.focusStats,
          ...focusStats
        },
        lastUpdated: Date.now()
      }

      return await this.dbManager.updateUserStatistics(updatedStats)
    } catch (error) {
      console.error('Failed to update focus statistics:', error)
      return false
    }
  }

  /**
   * 记录游戏会话
   */
  async recordGameSession(duration: number, experience: number): Promise<boolean> {
    try {
      const currentProfile = await this.dbManager.getUserProfile()
      if (!currentProfile) {
        return false
      }

      const updatedStats: UserStatistics = {
        ...currentProfile.statistics,
        totalPlayTime: currentProfile.statistics.totalPlayTime + duration,
        sessionsPlayed: currentProfile.statistics.sessionsPlayed + 1,
        totalExperienceGained: currentProfile.statistics.totalExperienceGained + experience,
        lastUpdated: Date.now()
      }

      const success = await this.dbManager.updateUserStatistics(updatedStats)

      if (success && this.currentUserId) {
        // 记录游戏会话事件
        await this.dbManager.logBehavior({
          type: 'game_session',
          category: 'gameplay',
          userId: this.currentUserId,
          value: duration,
          details: `Session: ${duration}ms, Experience: ${experience}`
        })
      }

      return success
    } catch (error) {
      console.error('Failed to record game session:', error)
      return false
    }
  }

  /**
   * 记录成就解锁
   */
  async recordAchievementUnlock(achievementId: string, achievementName: string): Promise<boolean> {
    try {
      const currentProfile = await this.dbManager.getUserProfile()
      if (!currentProfile) {
        return false
      }

      const updatedStats: UserStatistics = {
        ...currentProfile.statistics,
        achievementsUnlocked: currentProfile.statistics.achievementsUnlocked + 1,
        lastUpdated: Date.now()
      }

      const success = await this.dbManager.updateUserStatistics(updatedStats)

      if (success && this.currentUserId) {
        // 记录成就解锁事件
        await this.dbManager.logBehavior({
          type: 'achievement_unlock',
          category: 'progress',
          userId: this.currentUserId,
          details: `Achievement unlocked: ${achievementName} (${achievementId})`
        })
      }

      return success
    } catch (error) {
      console.error('Failed to record achievement unlock:', error)
      return false
    }
  }

  /**
   * 记录作物统计
   */
  async recordCropActivity(planted: number = 0, harvested: number = 0): Promise<boolean> {
    try {
      const currentProfile = await this.dbManager.getUserProfile()
      if (!currentProfile) {
        return false
      }

      const updatedStats: UserStatistics = {
        ...currentProfile.statistics,
        totalCropsPlanted: currentProfile.statistics.totalCropsPlanted + planted,
        totalCropsHarvested: currentProfile.statistics.totalCropsHarvested + harvested,
        lastUpdated: Date.now()
      }

      const success = await this.dbManager.updateUserStatistics(updatedStats)

      if (success && this.currentUserId) {
        // 记录作物活动事件
        const activity = []
        if (planted > 0) activity.push(`planted: ${planted}`)
        if (harvested > 0) activity.push(`harvested: ${harvested}`)
        
        await this.dbManager.logBehavior({
          type: planted > 0 ? 'crop_plant' : 'crop_harvest',
          category: 'gameplay',
          userId: this.currentUserId,
          value: planted + harvested,
          details: `Crop activity: ${activity.join(', ')}`
        })
      }

      return success
    } catch (error) {
      console.error('Failed to record crop activity:', error)
      return false
    }
  }

  /**
   * 获取用户游戏统计摘要
   */
  async getUserStatisticsSummary(): Promise<{
    profile: UserProfile | null
    recentBehaviors: any[]
    playTimeToday: number
    achievementsThisWeek: number
  }> {
    try {
      const profile = await this.dbManager.getUserProfile()
      const recentBehaviors = await this.dbManager.getBehaviorRecords(
        this.currentUserId || undefined,
        undefined,
        10
      )

      // 计算今日游戏时间
      const todayStart = new Date()
      todayStart.setHours(0, 0, 0, 0)
      const todayBehaviors = recentBehaviors.filter(
        r => r.timestamp >= todayStart.getTime() && r.event.type === 'game_start'
      )
      const playTimeToday = todayBehaviors.reduce((total, r) => total + (r.event.value || 0), 0)

      // 计算本周成就
      const weekStart = new Date()
      weekStart.setDate(weekStart.getDate() - weekStart.getDay())
      weekStart.setHours(0, 0, 0, 0)
      const weekBehaviors = recentBehaviors.filter(
        r => r.timestamp >= weekStart.getTime() && r.event.type === 'achievement_unlock'
      )
      const achievementsThisWeek = weekBehaviors.length

      return {
        profile,
        recentBehaviors,
        playTimeToday,
        achievementsThisWeek
      }
    } catch (error) {
      console.error('Failed to get user statistics summary:', error)
      return {
        profile: null,
        recentBehaviors: [],
        playTimeToday: 0,
        achievementsThisWeek: 0
      }
    }
  }

  /**
   * 检查用户是否存在
   */
  async userExists(): Promise<boolean> {
    const profile = await this.dbManager.getUserProfile()
    return profile !== null
  }

  /**
   * 删除用户档案（慎用）
   */
  async deleteUserProfile(): Promise<boolean> {
    try {
      const currentProfile = await this.dbManager.getUserProfile()
      if (!currentProfile) {
        return false
      }

      // 记录删除事件
      await this.dbManager.logBehavior({
        type: 'profile_deleted',
        category: 'system',
        userId: currentProfile.id,
        details: `Profile deleted: ${currentProfile.username}`
      })

      // 清除所有用户数据（通过删除具体的键实现）
      // 注意：这里不调用clear()方法，而是通过删除存储键来实现
      const storageKeys = ['user_profile', 'game_progress', 'behavior_records']
      for (const key of storageKeys) {
        await this.dbManager['storage'].remove(key)
      }
      this.currentUserId = null

      return true
    } catch (error) {
      console.error('Failed to delete user profile:', error)
      return false
    }
  }

  /**
   * 获取当前用户ID
   */
  getCurrentUserId(): string | null {
    return this.currentUserId
  }

  /**
   * 设置当前用户ID
   */
  setCurrentUserId(userId: string | null): void {
    this.currentUserId = userId
  }

  // 私有方法

  private generateUserId(): string {
    return `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 验证用户名
   */
  static validateUsername(username: string): { valid: boolean; error?: string } {
    if (!username || username.trim().length === 0) {
      return { valid: false, error: '用户名不能为空' }
    }

    if (username.length < 2) {
      return { valid: false, error: '用户名长度不能少于2个字符' }
    }

    if (username.length > 20) {
      return { valid: false, error: '用户名长度不能超过20个字符' }
    }

    const validUsernameRegex = /^[a-zA-Z0-9_\u4e00-\u9fa5]+$/
    if (!validUsernameRegex.test(username)) {
      return { valid: false, error: '用户名只能包含字母、数字、下划线和中文字符' }
    }

    return { valid: true }
  }

  /**
   * 验证邮箱
   */
  static validateEmail(email?: string): { valid: boolean; error?: string } {
    if (!email) {
      return { valid: true } // 邮箱是可选的
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      return { valid: false, error: '邮箱格式无效' }
    }

    return { valid: true }
  }
} 