import { WeatherManager, weatherManager } from './WeatherManager';
import { WeatherEffectProcessor, weatherEffectProcessor, FocusSession, EnhancedFocusSession } from './WeatherEffectProcessor';
import { WeatherType, WeatherState, WeatherChangeReason, WeatherPreferences } from '../types/weather';

// 天气推荐配置
export interface WeatherRecommendationConfig {
  adaptToFocusLevel: boolean;
  considerTimeOfDay: boolean;
  includeSeasonalFactors: boolean;
  userPreferenceWeight: number; // 0-1
  optimalDifficultyRange: [number, number]; // 难度范围
}

// 天气分析结果
export interface WeatherAnalysis {
  currentWeather: WeatherState;
  optimalWeatherTypes: WeatherType[];
  focusImpactScore: number; // -10 到 +10
  recommendations: WeatherRecommendation[];
  nextWeatherChange?: Date;
  forecast: Array<{
    time: Date;
    weather: WeatherType;
    focusImpact: number;
    description: string;
  }>;
}

// 天气推荐
export interface WeatherRecommendation {
  type: WeatherType;
  reason: string;
  expectedBenefit: string;
  confidence: number; // 0-1
  priority: 'high' | 'medium' | 'low';
}

// 专注会话统计
export interface FocusSessionStats {
  totalSessions: number;
  averageScore: number;
  bestWeatherType: WeatherType;
  weatherPerformance: Record<WeatherType, {
    sessions: number;
    averageScore: number;
    averageFocus: number;
    totalBonusPoints: number;
  }>;
  trends: {
    improvingInWeather: WeatherType[];
    decliningInWeather: WeatherType[];
    stableInWeather: WeatherType[];
  };
}

/**
 * 天气集成服务
 * 整合天气管理和效果处理，提供完整的天气系统API
 */
export class WeatherIntegrationService {
  private manager: WeatherManager;
  private processor: WeatherEffectProcessor;
  private sessionHistory: EnhancedFocusSession[] = [];
  private analysisCache: Map<string, WeatherAnalysis> = new Map();
  private cacheTimeout = 5 * 60 * 1000; // 5分钟缓存

  constructor(manager?: WeatherManager, processor?: WeatherEffectProcessor) {
    this.manager = manager || weatherManager;
    this.processor = processor || weatherEffectProcessor;
    
    this.initializeService();
  }

  /**
   * 初始化服务
   */
  private initializeService(): void {
    // 监听天气变化事件 - 直接使用EventEmitter的类型安全方式
    (this.manager as any).on('weatherChanged', (event: any) => {
      this.clearAnalysisCache();
      this.onWeatherChanged(event);
    });

    // 加载历史会话数据
    this.loadSessionHistory();
    
    console.log('天气集成服务已初始化');
  }

  /**
   * 处理专注会话
   */
  async processFocusSession(session: FocusSession): Promise<EnhancedFocusSession> {
    // 确保使用当前天气状态
    const currentWeather = this.manager.getCurrentWeather();
    const sessionWithCurrentWeather = {
      ...session,
      weather: currentWeather
    };

    // 处理天气影响
    const enhancedSession = this.processor.processWeatherImpact(sessionWithCurrentWeather);
    
    // 添加到历史记录
    this.addToSessionHistory(enhancedSession);
    
    // 根据专注度调整天气（如果启用）
    await this.adjustWeatherBasedOnPerformance(enhancedSession);
    
    return enhancedSession;
  }

  /**
   * 获取天气分析
   */
  async getWeatherAnalysis(config?: Partial<WeatherRecommendationConfig>): Promise<WeatherAnalysis> {
    const cacheKey = JSON.stringify(config || {});
    const cached = this.analysisCache.get(cacheKey);
    
    if (cached && Date.now() - cached.currentWeather.timestamp.getTime() < this.cacheTimeout) {
      return cached;
    }

    const analysis = await this.generateWeatherAnalysis(config);
    this.analysisCache.set(cacheKey, analysis);
    
    return analysis;
  }

  /**
   * 生成天气分析
   */
  private async generateWeatherAnalysis(config?: Partial<WeatherRecommendationConfig>): Promise<WeatherAnalysis> {
    const defaultConfig: WeatherRecommendationConfig = {
      adaptToFocusLevel: true,
      considerTimeOfDay: true,
      includeSeasonalFactors: true,
      userPreferenceWeight: 0.7,
      optimalDifficultyRange: [4, 7],
      ...config
    };

    const currentWeather = this.manager.getCurrentWeather();
    const currentEffect = this.processor.previewWeatherEffect(currentWeather.type, currentWeather.intensity);
    
    // 计算专注影响分数
    const focusImpactScore = this.calculateFocusImpactScore(currentEffect);
    
    // 获取最佳天气类型推荐
    const optimalWeatherTypes = this.getOptimalWeatherTypes(defaultConfig);
    
    // 生成推荐
    const recommendations = this.generateRecommendations(currentWeather, optimalWeatherTypes, defaultConfig);
    
    // 获取预报数据
    const forecast = this.generateForecastAnalysis();

    return {
      currentWeather,
      optimalWeatherTypes,
      focusImpactScore,
      recommendations,
      nextWeatherChange: this.manager['state']?.nextWeatherChange,
      forecast
    };
  }

  /**
   * 计算专注影响分数
   */
  private calculateFocusImpactScore(effect: any): number {
    let score = 0;
    
    // 专注倍数影响 (-5 到 +5)
    score += (effect.focusMultiplier - 1) * 5;
    
    // 舒适度影响 (-2.5 到 +2.5)
    score += (effect.comfortLevel - 5) / 2;
    
    // 心情影响 (-1.5 到 +1.5)
    score += effect.moodBonus / 2;
    
    // 难度调整影响 (-1 到 +1)
    score += -effect.difficultyModifier / 2;
    
    return Math.max(-10, Math.min(10, score));
  }

  /**
   * 获取最佳天气类型
   */
  private getOptimalWeatherTypes(config: WeatherRecommendationConfig): WeatherType[] {
    const averageDifficulty = (config.optimalDifficultyRange[0] + config.optimalDifficultyRange[1]) / 2;
    const sessionDuration = this.getAverageSessionDuration();
    
    // 基于历史数据的用户偏好
    const userPreferences = this.analyzeUserPreferences();
    
    return this.processor.recommendOptimalWeather(
      averageDifficulty,
      sessionDuration,
      userPreferences
    );
  }

  /**
   * 分析用户偏好
   */
  private analyzeUserPreferences(): Partial<any> {
    if (this.sessionHistory.length === 0) {
      return {};
    }

    // 找出表现最好的天气条件
    const weatherPerformance: Record<string, { totalScore: number, sessions: number, totalFocus: number }> = {};
    
    this.sessionHistory.forEach(session => {
      const weather = session.weather.type;
      if (!weatherPerformance[weather]) {
        weatherPerformance[weather] = { totalScore: 0, sessions: 0, totalFocus: 0 };
      }
      
      weatherPerformance[weather].totalScore += session.adjustedScore;
      weatherPerformance[weather].sessions += 1;
      weatherPerformance[weather].totalFocus += session.focusLevel;
    });

    // 找出平均表现最好的天气类型
    let bestWeather: WeatherType | null = null;
    let bestAverage = 0;
    
    Object.entries(weatherPerformance).forEach(([weather, data]) => {
      const average = data.totalScore / data.sessions;
      if (average > bestAverage) {
        bestAverage = average;
        bestWeather = weather as WeatherType;
      }
    });

    if (bestWeather) {
      return this.processor.previewWeatherEffect(bestWeather);
    }

    return {};
  }

  /**
   * 生成推荐
   */
  private generateRecommendations(
    currentWeather: WeatherState,
    optimalTypes: WeatherType[],
    config: WeatherRecommendationConfig
  ): WeatherRecommendation[] {
    const recommendations: WeatherRecommendation[] = [];
    const currentType = currentWeather.type;

    // 如果当前天气不是最佳的，推荐切换
    if (!optimalTypes.includes(currentType)) {
      const bestType = optimalTypes[0];
      const effect = this.processor.previewWeatherEffect(bestType);
      
      recommendations.push({
        type: bestType,
        reason: '基于您的专注表现历史，此天气条件可能更适合当前的训练目标',
        expectedBenefit: this.generateBenefitDescription(effect),
        confidence: 0.8,
        priority: 'high'
      });
    }

    // 基于时间段的推荐
    if (config.considerTimeOfDay) {
      const timeRecommendation = this.getTimeBasedRecommendation(currentWeather);
      if (timeRecommendation) {
        recommendations.push(timeRecommendation);
      }
    }

    // 基于即将到来的天气变化的推荐
    const forecast = this.manager.getForecast();
    if (forecast.length > 0) {
      const nextWeather = forecast[0];
      const nextEffect = this.processor.previewWeatherEffect(nextWeather.weatherState.type);
      
      if (this.calculateFocusImpactScore(nextEffect) > this.calculateFocusImpactScore(this.processor.previewWeatherEffect(currentType))) {
        recommendations.push({
          type: nextWeather.weatherState.type,
          reason: `即将到来的${this.getWeatherDisplayName(nextWeather.weatherState.type)}可能提供更好的专注环境`,
          expectedBenefit: this.generateBenefitDescription(nextEffect),
          confidence: nextWeather.confidence,
          priority: 'medium'
        });
      }
    }

    return recommendations.slice(0, 3); // 最多返回3个推荐
  }

  /**
   * 获取基于时间的推荐
   */
  private getTimeBasedRecommendation(currentWeather: WeatherState): WeatherRecommendation | null {
    const hour = new Date().getHours();
    let recommendedType: WeatherType | null = null;
    let reason = '';

    // 早晨推荐阳光
    if (hour >= 6 && hour < 10 && currentWeather.type !== WeatherType.SUNNY) {
      recommendedType = WeatherType.SUNNY;
      reason = '早晨时光配上阳光能够提升专注力和心情';
    }
    // 午后推荐雨天（降温，增强专注）
    else if (hour >= 13 && hour < 16 && currentWeather.temperature > 25) {
      recommendedType = WeatherType.RAINY;
      reason = '午后雨天能够降低温度，创造更舒适的专注环境';
    }
    // 晚上推荐雾天（安静，专注）
    else if (hour >= 19 && hour < 23) {
      recommendedType = WeatherType.FOGGY;
      reason = '夜晚雾天提供宁静环境，有利于深度专注';
    }

    if (recommendedType) {
      const effect = this.processor.previewWeatherEffect(recommendedType);
      return {
        type: recommendedType,
        reason,
        expectedBenefit: this.generateBenefitDescription(effect),
        confidence: 0.6,
        priority: 'low'
      };
    }

    return null;
  }

  /**
   * 生成收益描述
   */
  private generateBenefitDescription(effect: any): string {
    const benefits: string[] = [];
    
    if (effect.focusMultiplier > 1.1) {
      benefits.push(`专注力提升${Math.round((effect.focusMultiplier - 1) * 100)}%`);
    }
    
    if (effect.moodBonus > 1) {
      benefits.push('心情改善');
    }
    
    if (effect.comfortLevel > 7) {
      benefits.push('环境舒适');
    }
    
    if (effect.difficultyModifier < -0.5) {
      benefits.push('降低训练难度');
    }

    return benefits.length > 0 ? benefits.join('，') : '平衡的训练环境';
  }

  /**
   * 获取天气显示名称
   */
  private getWeatherDisplayName(type: WeatherType): string {
    const names = {
      [WeatherType.SUNNY]: '晴天',
      [WeatherType.PARTLY_CLOUDY]: '多云',
      [WeatherType.CLOUDY]: '阴天',
      [WeatherType.RAINY]: '雨天',
      [WeatherType.HEAVY_RAIN]: '大雨',
      [WeatherType.THUNDERSTORM]: '雷暴',
      [WeatherType.SNOWY]: '雪天',
      [WeatherType.FOGGY]: '雾天',
      [WeatherType.WINDY]: '风天'
    };
    return names[type] || type;
  }

  /**
   * 生成预报分析
   */
  private generateForecastAnalysis() {
    const forecast = this.manager.getForecast();
    
    return forecast.slice(0, 6).map(item => {
      const effect = this.processor.previewWeatherEffect(item.weatherState.type, item.weatherState.intensity);
      const focusImpact = this.calculateFocusImpactScore(effect);
      
      return {
        time: item.time,
        weather: item.weatherState.type,
        focusImpact,
        description: item.description
      };
    });
  }

  /**
   * 手动切换天气
   */
  async changeWeather(weatherType: WeatherType, reason?: WeatherChangeReason): Promise<void> {
    this.manager.changeWeather(weatherType, undefined, reason || WeatherChangeReason.USER_PREFERENCE);
    this.clearAnalysisCache();
  }

  /**
   * 获取专注会话统计
   */
  getFocusSessionStats(): FocusSessionStats {
    if (this.sessionHistory.length === 0) {
      return this.createEmptyStats();
    }

    const stats = this.calculateSessionStats();
    return stats;
  }

  /**
   * 创建空统计数据
   */
  private createEmptyStats(): FocusSessionStats {
    return {
      totalSessions: 0,
      averageScore: 0,
      bestWeatherType: WeatherType.SUNNY,
      weatherPerformance: Object.values(WeatherType).reduce((acc, type) => ({
        ...acc,
        [type]: { sessions: 0, averageScore: 0, averageFocus: 0, totalBonusPoints: 0 }
      }), {} as any),
      trends: {
        improvingInWeather: [],
        decliningInWeather: [],
        stableInWeather: []
      }
    };
  }

  /**
   * 计算会话统计
   */
  private calculateSessionStats(): FocusSessionStats {
    const totalSessions = this.sessionHistory.length;
    const totalScore = this.sessionHistory.reduce((sum, s) => sum + s.adjustedScore, 0);
    const averageScore = totalScore / totalSessions;

    // 按天气类型分组统计
    const weatherGroups = this.groupSessionsByWeather();
    let bestWeatherType = WeatherType.SUNNY;
    let bestAverage = 0;

    const weatherPerformance: Record<WeatherType, any> = Object.values(WeatherType).reduce((acc, type) => ({
      ...acc,
      [type]: {
        sessions: 0,
        averageScore: 0,
        averageFocus: 0,
        totalBonusPoints: 0
      }
    }), {} as Record<WeatherType, any>);

    Object.entries(weatherGroups).forEach(([weather, sessions]) => {
      const avgScore = sessions.reduce((sum, s) => sum + s.adjustedScore, 0) / sessions.length;
      const avgFocus = sessions.reduce((sum, s) => sum + s.focusLevel, 0) / sessions.length;
      const totalBonus = sessions.reduce((sum, s) => sum + s.bonusPoints, 0);

      weatherPerformance[weather as WeatherType] = {
        sessions: sessions.length,
        averageScore: avgScore,
        averageFocus: avgFocus,
        totalBonusPoints: totalBonus
      };

      if (avgScore > bestAverage) {
        bestAverage = avgScore;
        bestWeatherType = weather as WeatherType;
      }
    });

    // 分析趋势
    const trends = this.analyzeTrends(weatherGroups);

    return {
      totalSessions,
      averageScore,
      bestWeatherType,
      weatherPerformance,
      trends
    };
  }

  /**
   * 按天气分组会话
   */
  private groupSessionsByWeather(): Record<string, EnhancedFocusSession[]> {
    return this.sessionHistory.reduce((groups, session) => {
      const weather = session.weather.type;
      if (!groups[weather]) {
        groups[weather] = [];
      }
      groups[weather].push(session);
      return groups;
    }, {} as Record<string, EnhancedFocusSession[]>);
  }

  /**
   * 分析趋势
   */
  private analyzeTrends(weatherGroups: Record<string, EnhancedFocusSession[]>) {
    const trends = {
      improvingInWeather: [] as WeatherType[],
      decliningInWeather: [] as WeatherType[],
      stableInWeather: [] as WeatherType[]
    };

    Object.entries(weatherGroups).forEach(([weather, sessions]) => {
      if (sessions.length < 3) return; // 需要足够的数据点

      // 计算最近和早期的平均分数
      const sorted = sessions.sort((a, b) => a.startTime.getTime() - b.startTime.getTime());
      const recentSessions = sorted.slice(-Math.ceil(sessions.length / 3));
      const earlySessions = sorted.slice(0, Math.ceil(sessions.length / 3));

      const recentAvg = recentSessions.reduce((sum, s) => sum + s.adjustedScore, 0) / recentSessions.length;
      const earlyAvg = earlySessions.reduce((sum, s) => sum + s.adjustedScore, 0) / earlySessions.length;

      const improvement = (recentAvg - earlyAvg) / earlyAvg;

      if (improvement > 0.1) {
        trends.improvingInWeather.push(weather as WeatherType);
      } else if (improvement < -0.1) {
        trends.decliningInWeather.push(weather as WeatherType);
      } else {
        trends.stableInWeather.push(weather as WeatherType);
      }
    });

    return trends;
  }

  /**
   * 根据表现调整天气
   */
  private async adjustWeatherBasedOnPerformance(session: EnhancedFocusSession): Promise<void> {
    const preferences = this.manager['state']?.preferences;
    if (!preferences?.autoAdjustBasedOnFocus) {
      return;
    }

    // 如果专注度很低且当前天气舒适度也低，考虑改善天气
    if (session.focusLevel < 0.4 && session.weatherEffect.comfortLevel < 5) {
      const optimalWeathers = await this.getOptimalWeatherTypes({
        adaptToFocusLevel: true,
        considerTimeOfDay: true,
        includeSeasonalFactors: true,
        userPreferenceWeight: 0.8,
        optimalDifficultyRange: [3, 6]
      });

      if (optimalWeathers.length > 0 && optimalWeathers[0] !== session.weather.type) {
        setTimeout(() => {
          this.manager.changeWeather(optimalWeathers[0], undefined, WeatherChangeReason.USER_FOCUS_LEVEL);
        }, 30000); // 30秒延迟，避免频繁变化
      }
    }
  }

  /**
   * 添加到会话历史
   */
  private addToSessionHistory(session: EnhancedFocusSession): void {
    this.sessionHistory.push(session);
    
    // 限制历史记录数量
    if (this.sessionHistory.length > 100) {
      this.sessionHistory = this.sessionHistory.slice(-100);
    }
    
    // 保存到本地存储
    this.saveSessionHistory();
  }

  /**
   * 获取平均会话时长
   */
  private getAverageSessionDuration(): number {
    if (this.sessionHistory.length === 0) return 30; // 默认30分钟
    
    const totalDuration = this.sessionHistory.reduce((sum, s) => sum + s.duration, 0);
    return totalDuration / this.sessionHistory.length;
  }

  /**
   * 天气变化事件处理
   */
  private onWeatherChanged(event: any): void {
    console.log('天气变化检测到:', {
      from: event.previousWeather.type,
      to: event.newWeather.type,
      reason: event.changeReason
    });
  }

  /**
   * 清除分析缓存
   */
  private clearAnalysisCache(): void {
    this.analysisCache.clear();
  }

  /**
   * 保存会话历史
   */
  private saveSessionHistory(): void {
    try {
      const data = {
        sessions: this.sessionHistory.slice(-50), // 只保存最近50个会话
        lastSaved: new Date().toISOString()
      };
      localStorage.setItem('focus_session_history', JSON.stringify(data));
    } catch (error) {
      console.error('保存会话历史失败:', error);
    }
  }

  /**
   * 加载会话历史
   */
  private loadSessionHistory(): void {
    try {
      const savedData = localStorage.getItem('focus_session_history');
      if (savedData) {
        const data = JSON.parse(savedData);
        if (data.sessions) {
          this.sessionHistory = data.sessions.map((s: any) => ({
            ...s,
            startTime: new Date(s.startTime),
            weather: {
              ...s.weather,
              timestamp: new Date(s.weather.timestamp)
            }
          }));
        }
        console.log(`加载了 ${this.sessionHistory.length} 个历史会话`);
      }
    } catch (error) {
      console.error('加载会话历史失败:', error);
    }
  }

  /**
   * 更新天气偏好
   */
  updateWeatherPreferences(preferences: Partial<WeatherPreferences>): void {
    this.manager.updatePreferences(preferences);
    this.clearAnalysisCache();
  }

  /**
   * 获取当前天气状态
   */
  getCurrentWeather(): WeatherState {
    return this.manager.getCurrentWeather();
  }

  /**
   * 重置统计数据
   */
  resetStats(): void {
    this.sessionHistory = [];
    this.saveSessionHistory();
    this.clearAnalysisCache();
  }

  /**
   * 导出数据
   */
  exportData(): any {
    return {
      sessionHistory: this.sessionHistory,
      weatherStats: this.manager.getWeatherStats(),
      processorConfig: this.processor.getConfig(),
      exportedAt: new Date().toISOString()
    };
  }

  /**
   * 导入数据
   */
  importData(data: any): void {
    if (data.sessionHistory) {
      this.sessionHistory = data.sessionHistory.map((s: any) => ({
        ...s,
        startTime: new Date(s.startTime),
        weather: {
          ...s.weather,
          timestamp: new Date(s.weather.timestamp)
        }
      }));
      this.saveSessionHistory();
    }
    
    if (data.processorConfig) {
      this.processor.updateConfig(data.processorConfig);
    }
    
    this.clearAnalysisCache();
    console.log('数据导入完成');
  }
}

// 导出默认实例
export const weatherIntegrationService = new WeatherIntegrationService(); 