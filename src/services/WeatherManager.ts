import {
  WeatherType,
  WeatherIntensity,
  WeatherState,
  WeatherEffect,
  WeatherForecast,
  WeatherChangeEvent,
  WeatherChangeReason,
  WeatherPreferences,
  WeatherStats,
  WeatherSystemConfig,
  WeatherManagerState,
  WeatherTrigger,
  WeatherEventListener,
  TimeOfDay,
  Season,
  WeatherCondition,
  WEATHER_CONSTANTS
} from '../types/weather';

import { EventEmitter } from 'eventemitter3';

/**
 * 天气管理器 - 专注力应用的核心天气系统
 * 负责管理天气状态、变化、预报和与专注训练的交互
 */
export class WeatherManager extends EventEmitter {
  private state: WeatherManagerState;
  private updateTimer?: NodeJS.Timeout;
  private transitionTimer?: NodeJS.Timeout;
  private eventListeners: WeatherEventListener[] = [];
  private triggers: WeatherTrigger[] = [];

  constructor(config?: Partial<WeatherSystemConfig>) {
    super();
    
    // 初始化默认配置
    const defaultConfig: WeatherSystemConfig = {
      enableRealTimeWeather: true,
      enableSeasonalChanges: true,
      weatherUpdateInterval: 10, // 10分钟
      transitionDuration: 30, // 30秒
      maxWeatherIntensity: WeatherIntensity.HEAVY,
      enableWeatherNotifications: true,
      autoSaveWeatherData: true,
      weatherHistoryRetentionDays: 30
    };

    // 初始化默认偏好
    const defaultPreferences: WeatherPreferences = {
      favoriteWeather: [WeatherType.SUNNY, WeatherType.PARTLY_CLOUDY],
      avoidWeather: [WeatherType.THUNDERSTORM],
      autoAdjustBasedOnFocus: true,
      enableDynamicWeather: true,
      weatherChangeFrequency: 'medium',
      enableSoundEffects: true,
      soundVolume: 0.7,
      enableVisualEffects: true,
      visualEffectIntensity: 'normal'
    };

    // 初始化空统计数据
    const defaultStats: WeatherStats = {
      totalTimeInWeather: Object.values(WeatherType).reduce((acc, type) => ({
        ...acc,
        [type]: 0
      }), {} as Record<WeatherType, number>),
      favoriteWeatherByFocus: WeatherType.SUNNY,
      mostProductiveWeather: WeatherType.SUNNY,
      weatherChangesTriggered: 0,
      averageFocusInWeather: Object.values(WeatherType).reduce((acc, type) => ({
        ...acc,
        [type]: 0
      }), {} as Record<WeatherType, number>),
      weatherStreaks: Object.values(WeatherType).reduce((acc, type) => ({
        ...acc,
        [type]: 0
      }), {} as Record<WeatherType, number>)
    };

    // 初始化状态
    this.state = {
      currentWeather: this.generateDefaultWeather(),
      currentSeason: this.getCurrentSeason(),
      isTransitioning: false,
      transitionProgress: 0,
      weatherHistory: [],
      forecast: [],
      preferences: defaultPreferences,
      stats: defaultStats,
      config: { ...defaultConfig, ...config }
    };

    this.initializeWeatherSystem();
  }

  /**
   * 初始化天气系统
   */
  private initializeWeatherSystem(): void {
    // 生成初始预报
    this.generateForecast();
    
    // 启动定期更新
    if (this.state.config.enableRealTimeWeather) {
      this.startWeatherUpdates();
    }

    // 加载历史数据
    this.loadWeatherData();
    
    // 设置下次天气变化时间
    this.scheduleNextWeatherChange();

    console.log('天气系统已初始化', {
      currentWeather: this.state.currentWeather,
      season: this.state.currentSeason
    });
  }

  /**
   * 获取当前天气状态
   */
  getCurrentWeather(): WeatherState {
    return { ...this.state.currentWeather };
  }

  /**
   * 获取当前天气效果
   */
  getCurrentWeatherEffect(): WeatherEffect {
    return this.getWeatherEffect(this.state.currentWeather);
  }

  /**
   * 获取天气预报
   */
  getForecast(): WeatherForecast[] {
    return [...this.state.forecast];
  }

  /**
   * 获取天气统计数据
   */
  getWeatherStats(): WeatherStats {
    return { ...this.state.stats };
  }

  /**
   * 更新天气偏好
   */
  updatePreferences(preferences: Partial<WeatherPreferences>): void {
    this.state.preferences = {
      ...this.state.preferences,
      ...preferences
    };
    
    this.saveWeatherData();
    this.emit('preferencesUpdated', this.state.preferences);
  }

  /**
   * 手动更改天气
   */
  changeWeather(
    weatherType: WeatherType, 
    intensity?: WeatherIntensity,
    reason: WeatherChangeReason = WeatherChangeReason.USER_PREFERENCE
  ): void {
    const previousWeather = { ...this.state.currentWeather };
    
    const newWeather: WeatherState = {
      ...this.state.currentWeather,
      type: weatherType,
      intensity: intensity || this.state.currentWeather.intensity,
      timestamp: new Date()
    };

    this.applyWeatherChange(previousWeather, newWeather, reason);
  }

  /**
   * 应用天气变化
   */
  private applyWeatherChange(
    previousWeather: WeatherState,
    newWeather: WeatherState,
    reason: WeatherChangeReason
  ): void {
    // 创建变化事件
    const changeEvent: WeatherChangeEvent = {
      id: `weather_change_${Date.now()}`,
      previousWeather,
      newWeather,
      changeReason: reason,
      duration: this.state.config.transitionDuration / 60, // 转换为分钟
      timestamp: new Date()
    };

    // 开始天气转换
    this.startWeatherTransition(changeEvent);
  }

  /**
   * 开始天气转换动画
   */
  private startWeatherTransition(changeEvent: WeatherChangeEvent): void {
    this.state.isTransitioning = true;
    this.state.transitionProgress = 0;

    const duration = this.state.config.transitionDuration * 1000; // 转换为毫秒
    const startTime = Date.now();

    // 清除之前的转换计时器
    if (this.transitionTimer) {
      clearInterval(this.transitionTimer);
    }

    // 创建转换动画
    this.transitionTimer = setInterval(() => {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / duration, 1);

      this.state.transitionProgress = progress;
      this.emit('weatherTransition', {
        event: changeEvent,
        progress
      });

      if (progress >= 1) {
        this.completeWeatherTransition(changeEvent);
      }
    }, 16); // ~60fps
  }

  /**
   * 完成天气转换
   */
  private completeWeatherTransition(changeEvent: WeatherChangeEvent): void {
    if (this.transitionTimer) {
      clearInterval(this.transitionTimer);
      this.transitionTimer = undefined;
    }

    // 更新当前天气状态
    this.state.currentWeather = changeEvent.newWeather;
    this.state.isTransitioning = false;
    this.state.transitionProgress = 0;

    // 添加到历史记录
    this.addToWeatherHistory(changeEvent.newWeather);

    // 更新统计数据
    this.updateWeatherStats(changeEvent);

    // 触发事件
    this.emit('weatherChanged', changeEvent);
    this.notifyEventListeners(changeEvent);

    // 检查触发器
    this.checkWeatherTriggers(changeEvent.newWeather);

    // 重新生成预报
    this.generateForecast();

    // 安排下次天气变化
    this.scheduleNextWeatherChange();

    // 保存数据
    if (this.state.config.autoSaveWeatherData) {
      this.saveWeatherData();
    }

    console.log('天气变化完成', {
      previous: changeEvent.previousWeather.type,
      new: changeEvent.newWeather.type,
      reason: changeEvent.changeReason
    });
  }

  /**
   * 根据专注度调整天气
   */
  adjustWeatherBasedOnFocus(focusLevel: number): void {
    if (!this.state.preferences.autoAdjustBasedOnFocus) {
      return;
    }

    // 根据专注度选择合适的天气
    let targetWeather: WeatherType;
    
    if (focusLevel >= 0.8) {
      // 高专注度 - 保持良好天气
      targetWeather = WeatherType.SUNNY;
    } else if (focusLevel >= 0.6) {
      // 中等专注度 - 适度变化
      targetWeather = WeatherType.PARTLY_CLOUDY;
    } else if (focusLevel >= 0.4) {
      // 低专注度 - 增加动态性
      targetWeather = WeatherType.CLOUDY;
    } else {
      // 很低专注度 - 激励性天气变化
      targetWeather = WeatherType.RAINY;
    }

    // 如果当前天气不同，进行变化
    if (this.state.currentWeather.type !== targetWeather) {
      this.changeWeather(targetWeather, undefined, WeatherChangeReason.USER_FOCUS_LEVEL);
    }
  }

  /**
   * 生成天气预报
   */
  private generateForecast(): void {
    const forecast: WeatherForecast[] = [];
    let currentWeather = { ...this.state.currentWeather };
    
    // 生成未来24小时的预报（每2小时一个）
    for (let i = 1; i <= 12; i++) {
      const forecastTime = new Date(Date.now() + i * 2 * 60 * 60 * 1000);
      currentWeather = this.predictNextWeather(currentWeather, 2); // 2小时后
      
      forecast.push({
        time: forecastTime,
        weatherState: { ...currentWeather },
        confidence: Math.max(0.9 - i * 0.05, 0.3), // 置信度递减
        description: this.generateWeatherDescription(currentWeather)
      });
    }

    this.state.forecast = forecast;
    this.emit('forecastUpdated', forecast);
  }

  /**
   * 预测下一个天气状态
   */
  private predictNextWeather(currentWeather: WeatherState, hoursAhead: number): WeatherState {
    const transitionProbs = WEATHER_CONSTANTS.WEATHER_TRANSITION_PROBABILITIES[currentWeather.type];
    
    if (!transitionProbs) {
      return currentWeather; // 如果没有转换概率，保持当前天气
    }

    // 随机选择下一个天气类型
    const random = Math.random();
    let cumulative = 0;
    
    for (const [weatherType, probability] of Object.entries(transitionProbs)) {
      cumulative += probability;
      if (random <= cumulative) {
        return {
          ...currentWeather,
          type: weatherType as WeatherType,
          timestamp: new Date(currentWeather.timestamp.getTime() + hoursAhead * 60 * 60 * 1000),
          temperature: this.adjustTemperatureForWeather(currentWeather.temperature, weatherType as WeatherType),
          humidity: this.adjustHumidityForWeather(currentWeather.humidity, weatherType as WeatherType)
        };
      }
    }

    return currentWeather;
  }

  /**
   * 根据天气类型调整温度
   */
  private adjustTemperatureForWeather(currentTemp: number, weatherType: WeatherType): number {
    const adjustments = {
      [WeatherType.SUNNY]: 2,
      [WeatherType.PARTLY_CLOUDY]: 0,
      [WeatherType.CLOUDY]: -1,
      [WeatherType.RAINY]: -3,
      [WeatherType.HEAVY_RAIN]: -5,
      [WeatherType.THUNDERSTORM]: -4,
      [WeatherType.SNOWY]: -8,
      [WeatherType.FOGGY]: -2,
      [WeatherType.WINDY]: -1
    };

    return currentTemp + (adjustments[weatherType] || 0) + (Math.random() - 0.5) * 2;
  }

  /**
   * 根据天气类型调整湿度
   */
  private adjustHumidityForWeather(currentHumidity: number, weatherType: WeatherType): number {
    const adjustments = {
      [WeatherType.SUNNY]: -10,
      [WeatherType.PARTLY_CLOUDY]: 0,
      [WeatherType.CLOUDY]: 5,
      [WeatherType.RAINY]: 20,
      [WeatherType.HEAVY_RAIN]: 30,
      [WeatherType.THUNDERSTORM]: 25,
      [WeatherType.SNOWY]: 15,
      [WeatherType.FOGGY]: 35,
      [WeatherType.WINDY]: -5
    };

    return Math.max(0, Math.min(100, currentHumidity + (adjustments[weatherType] || 0)));
  }

  /**
   * 获取指定天气的效果配置
   */
  private getWeatherEffect(weather: WeatherState): WeatherEffect {
    const effects: Record<WeatherType, WeatherEffect> = {
      [WeatherType.SUNNY]: {
        focusMultiplier: 1.2,
        comfortLevel: 9,
        moodBonus: 3,
        difficultyModifier: -1,
        backgroundFilter: 'brightness(1.1) contrast(1.05)',
        lightingEffect: {
          brightness: 1.0,
          contrast: 1.1,
          saturation: 1.2,
          hue: 50,
          warmth: 0.3
        },
        ambientSound: 'birds',
        soundVolume: 0.6,
        specialEffects: []
      },
      [WeatherType.PARTLY_CLOUDY]: {
        focusMultiplier: 1.0,
        comfortLevel: 8,
        moodBonus: 1,
        difficultyModifier: 0,
        backgroundFilter: 'brightness(0.95)',
        lightingEffect: {
          brightness: 0.9,
          contrast: 1.0,
          saturation: 1.0,
          hue: 0,
          warmth: 0.1
        },
        ambientSound: 'wind_light',
        soundVolume: 0.4
      },
      [WeatherType.CLOUDY]: {
        focusMultiplier: 0.9,
        comfortLevel: 6,
        moodBonus: -1,
        difficultyModifier: 0,
        backgroundFilter: 'brightness(0.8) contrast(0.95)',
        lightingEffect: {
          brightness: 0.8,
          contrast: 0.9,
          saturation: 0.8,
          hue: 200,
          warmth: -0.2
        },
        ambientSound: 'wind_moderate',
        soundVolume: 0.5
      },
      [WeatherType.RAINY]: {
        focusMultiplier: 1.1,
        comfortLevel: 7,
        moodBonus: 1,
        difficultyModifier: 0,
        backgroundFilter: 'brightness(0.7) contrast(0.9)',
        particleEffect: {
          type: 'rain',
          count: 100,
          speed: 5,
          size: 1,
          opacity: 0.6,
          direction: 75
        },
        lightingEffect: {
          brightness: 0.7,
          contrast: 0.9,
          saturation: 0.9,
          hue: 210,
          warmth: -0.3
        },
        ambientSound: 'rain_light',
        soundVolume: 0.7
      },
      [WeatherType.HEAVY_RAIN]: {
        focusMultiplier: 0.8,
        comfortLevel: 5,
        moodBonus: -2,
        difficultyModifier: 1,
        backgroundFilter: 'brightness(0.6) contrast(0.8)',
        particleEffect: {
          type: 'rain',
          count: 300,
          speed: 8,
          size: 2,
          opacity: 0.8,
          direction: 80
        },
        lightingEffect: {
          brightness: 0.6,
          contrast: 0.8,
          saturation: 0.7,
          hue: 220,
          warmth: -0.4
        },
        ambientSound: 'rain_heavy',
        soundVolume: 0.9
      },
      [WeatherType.THUNDERSTORM]: {
        focusMultiplier: 0.6,
        comfortLevel: 3,
        moodBonus: -3,
        difficultyModifier: 2,
        backgroundFilter: 'brightness(0.4) contrast(1.2)',
        particleEffect: {
          type: 'rain',
          count: 400,
          speed: 10,
          size: 2,
          opacity: 0.9,
          direction: 85
        },
        lightingEffect: {
          brightness: 0.4,
          contrast: 1.2,
          saturation: 0.6,
          hue: 240,
          warmth: -0.5
        },
        ambientSound: 'thunderstorm',
        soundVolume: 0.8,
        specialEffects: [{
          type: 'lightning',
          probability: 0.1,
          duration: 0.5,
          intensity: 1.0
        }]
      },
      [WeatherType.SNOWY]: {
        focusMultiplier: 1.3,
        comfortLevel: 8,
        moodBonus: 2,
        difficultyModifier: -1,
        backgroundFilter: 'brightness(1.2) contrast(1.1)',
        particleEffect: {
          type: 'snow',
          count: 150,
          speed: 2,
          size: 3,
          opacity: 0.8,
          direction: 90,
          color: '#ffffff'
        },
        lightingEffect: {
          brightness: 1.2,
          contrast: 1.1,
          saturation: 0.8,
          hue: 200,
          warmth: -0.1
        },
        ambientSound: 'wind_cold',
        soundVolume: 0.5
      },
      [WeatherType.FOGGY]: {
        focusMultiplier: 1.4,
        comfortLevel: 6,
        moodBonus: 0,
        difficultyModifier: -1,
        backgroundFilter: 'brightness(0.8) blur(2px)',
        lightingEffect: {
          brightness: 0.8,
          contrast: 0.7,
          saturation: 0.6,
          hue: 180,
          warmth: 0
        },
        ambientSound: 'silence',
        soundVolume: 0.2,
        specialEffects: [{
          type: 'fog_overlay',
          probability: 1.0,
          duration: 0,
          intensity: 0.7
        }]
      },
      [WeatherType.WINDY]: {
        focusMultiplier: 0.8,
        comfortLevel: 6,
        moodBonus: 0,
        difficultyModifier: 1,
        backgroundFilter: 'brightness(0.9)',
        particleEffect: {
          type: 'leaves',
          count: 50,
          speed: 6,
          size: 2,
          opacity: 0.7,
          direction: 45
        },
        lightingEffect: {
          brightness: 0.9,
          contrast: 1.0,
          saturation: 1.0,
          hue: 60,
          warmth: 0.2
        },
        ambientSound: 'wind_strong',
        soundVolume: 0.8
      }
    };

    return effects[weather.type] || effects[WeatherType.SUNNY];
  }

  /**
   * 生成天气描述
   */
  private generateWeatherDescription(weather: WeatherState): string {
    const descriptions: Record<WeatherType, string[]> = {
      [WeatherType.SUNNY]: ['阳光明媚', '晴空万里', '阳光充足'],
      [WeatherType.PARTLY_CLOUDY]: ['局部多云', '云朵飘过', '时晴时云'],
      [WeatherType.CLOUDY]: ['阴云密布', '多云天气', '灰云满天'],
      [WeatherType.RAINY]: ['细雨飘洒', '小雨连绵', '雨滴轻敲'],
      [WeatherType.HEAVY_RAIN]: ['大雨倾盆', '暴雨如注', '雨势汹涌'],
      [WeatherType.THUNDERSTORM]: ['雷电交加', '暴风雨来袭', '雷声阵阵'],
      [WeatherType.SNOWY]: ['雪花飞舞', '银装素裹', '雪花纷飞'],
      [WeatherType.FOGGY]: ['雾气弥漫', '白雾茫茫', '能见度低'],
      [WeatherType.WINDY]: ['风力强劲', '狂风呼啸', '风声阵阵']
    };

    const typeDescriptions = descriptions[weather.type] || ['天气良好'];
    const randomDescription = typeDescriptions[Math.floor(Math.random() * typeDescriptions.length)];
    
    return `${randomDescription}，温度${Math.round(weather.temperature)}°C`;
  }

  /**
   * 生成默认天气状态
   */
  private generateDefaultWeather(): WeatherState {
    return {
      type: WeatherType.SUNNY,
      intensity: WeatherIntensity.MODERATE,
      temperature: 22,
      humidity: 60,
      windSpeed: 5,
      visibility: 10,
      pressure: 1013,
      timeOfDay: this.getCurrentTimeOfDay(),
      timestamp: new Date()
    };
  }

  /**
   * 获取当前时间段
   */
  private getCurrentTimeOfDay(): TimeOfDay {
    const hour = new Date().getHours();
    
    if (hour >= 5 && hour < 7) return TimeOfDay.DAWN;
    if (hour >= 7 && hour < 12) return TimeOfDay.MORNING;
    if (hour >= 12 && hour < 14) return TimeOfDay.NOON;
    if (hour >= 14 && hour < 18) return TimeOfDay.AFTERNOON;
    if (hour >= 18 && hour < 21) return TimeOfDay.EVENING;
    if (hour >= 21 || hour < 2) return TimeOfDay.NIGHT;
    return TimeOfDay.MIDNIGHT;
  }

  /**
   * 获取当前季节
   */
  private getCurrentSeason(): Season {
    const month = new Date().getMonth() + 1; // 1-12
    
    if (month >= 3 && month <= 5) return Season.SPRING;
    if (month >= 6 && month <= 8) return Season.SUMMER;
    if (month >= 9 && month <= 11) return Season.AUTUMN;
    return Season.WINTER;
  }

  /**
   * 启动天气更新定时器
   */
  private startWeatherUpdates(): void {
    if (this.updateTimer) {
      clearInterval(this.updateTimer);
    }

    this.updateTimer = setInterval(() => {
      this.updateWeatherSystem();
    }, this.state.config.weatherUpdateInterval * 60 * 1000);
  }

  /**
   * 更新天气系统
   */
  private updateWeatherSystem(): void {
    // 更新时间段
    this.state.currentWeather.timeOfDay = this.getCurrentTimeOfDay();
    
    // 检查是否需要自然天气变化
    this.checkNaturalWeatherChange();
    
    // 更新统计数据
    this.updateTimeStats();
    
    // 清理过期历史数据
    this.cleanupWeatherHistory();
    
    this.emit('weatherSystemUpdated');
  }

  /**
   * 检查自然天气变化
   */
  private checkNaturalWeatherChange(): void {
    if (this.state.nextWeatherChange && new Date() >= this.state.nextWeatherChange) {
      const newWeather = this.predictNextWeather(this.state.currentWeather, 1);
      if (newWeather.type !== this.state.currentWeather.type) {
        this.applyWeatherChange(
          this.state.currentWeather,
          newWeather,
          WeatherChangeReason.NATURAL_CYCLE
        );
      }
    }
  }

  /**
   * 安排下次天气变化
   */
  private scheduleNextWeatherChange(): void {
    const duration = WEATHER_CONSTANTS.DEFAULT_WEATHER_DURATION[this.state.currentWeather.type];
    const variationMinutes = duration * 0.3; // 30%的随机变化
    const actualDuration = duration + (Math.random() - 0.5) * variationMinutes;
    
    this.state.nextWeatherChange = new Date(Date.now() + actualDuration * 60 * 1000);
    
    console.log(`下次天气变化安排在: ${this.state.nextWeatherChange.toLocaleString()}`);
  }

  /**
   * 添加到天气历史
   */
  private addToWeatherHistory(weather: WeatherState): void {
    this.state.weatherHistory.push(weather);
    
    // 限制历史记录数量
    const maxHistory = this.state.config.weatherHistoryRetentionDays * 24; // 每小时一条记录
    if (this.state.weatherHistory.length > maxHistory) {
      this.state.weatherHistory = this.state.weatherHistory.slice(-maxHistory);
    }
  }

  /**
   * 更新天气统计数据
   */
  private updateWeatherStats(changeEvent: WeatherChangeEvent): void {
    this.state.stats.weatherChangesTriggered++;
    
    // 记录天气连续天数
    if (changeEvent.previousWeather.type !== changeEvent.newWeather.type) {
      // 重置其他天气的连续记录
      Object.keys(this.state.stats.weatherStreaks).forEach(weather => {
        if (weather !== changeEvent.newWeather.type) {
          this.state.stats.weatherStreaks[weather as WeatherType] = 0;
        }
      });
      
      // 增加新天气的连续记录
      this.state.stats.weatherStreaks[changeEvent.newWeather.type]++;
    }
  }

  /**
   * 更新时间统计
   */
  private updateTimeStats(): void {
    const currentType = this.state.currentWeather.type;
    const intervalMinutes = this.state.config.weatherUpdateInterval;
    
    this.state.stats.totalTimeInWeather[currentType] += intervalMinutes;
  }

  /**
   * 检查天气触发器
   */
  private checkWeatherTriggers(weather: WeatherState): void {
    this.triggers.forEach(trigger => {
      if (trigger.isActive && this.matchesWeatherCondition(weather, trigger.condition)) {
        // 检查冷却时间
        if (trigger.cooldownMinutes && trigger.lastTriggered) {
          const cooldownExpired = Date.now() - trigger.lastTriggered.getTime() > trigger.cooldownMinutes * 60 * 1000;
          if (!cooldownExpired) return;
        }

        this.executeTriggerAction(trigger);
        trigger.lastTriggered = new Date();
      }
    });
  }

  /**
   * 匹配天气条件
   */
  private matchesWeatherCondition(weather: WeatherState, condition: WeatherCondition): boolean {
    // 检查天气类型
    if (condition.weatherTypes && !condition.weatherTypes.includes(weather.type)) {
      return false;
    }

    // 检查强度范围
    if (condition.intensityRange) {
      const intensities = [WeatherIntensity.LIGHT, WeatherIntensity.MODERATE, WeatherIntensity.HEAVY, WeatherIntensity.EXTREME];
      const currentIndex = intensities.indexOf(weather.intensity);
      const minIndex = intensities.indexOf(condition.intensityRange[0]);
      const maxIndex = intensities.indexOf(condition.intensityRange[1]);
      if (currentIndex < minIndex || currentIndex > maxIndex) {
        return false;
      }
    }

    // 检查温度范围
    if (condition.temperatureRange) {
      if (weather.temperature < condition.temperatureRange[0] || weather.temperature > condition.temperatureRange[1]) {
        return false;
      }
    }

    // 检查时间段
    if (condition.timeOfDay && !condition.timeOfDay.includes(weather.timeOfDay)) {
      return false;
    }

    // 检查季节
    if (condition.season && !condition.season.includes(this.state.currentSeason)) {
      return false;
    }

    // 自定义匹配器
    if (condition.customMatcher && !condition.customMatcher(weather)) {
      return false;
    }

    return true;
  }

  /**
   * 执行触发器动作
   */
  private executeTriggerAction(trigger: WeatherTrigger): void {
    const action = trigger.action;
    
    setTimeout(() => {
      switch (action.type) {
        case 'change_weather':
          this.changeWeather(action.payload.weatherType, action.payload.intensity, WeatherChangeReason.RANDOM_EVENT);
          break;
        case 'show_notification':
          this.emit('notification', {
            title: action.payload.title,
            message: action.payload.message,
            type: 'weather'
          });
          break;
        case 'play_sound':
          this.emit('playSound', action.payload.soundName);
          break;
        // 可以扩展更多动作类型
      }
    }, (action.delay || 0) * 1000);
  }

  /**
   * 添加事件监听器
   */
  addWeatherEventListener(listener: WeatherEventListener): void {
    this.eventListeners.push(listener);
  }

  /**
   * 移除事件监听器
   */
  removeWeatherEventListener(listener: WeatherEventListener): void {
    const index = this.eventListeners.indexOf(listener);
    if (index > -1) {
      this.eventListeners.splice(index, 1);
    }
  }

  /**
   * 通知事件监听器
   */
  private notifyEventListeners(event: WeatherChangeEvent): void {
    this.eventListeners.forEach(listener => {
      try {
        listener(event);
      } catch (error) {
        console.error('天气事件监听器执行错误:', error);
      }
    });
  }

  /**
   * 添加天气触发器
   */
  addWeatherTrigger(trigger: WeatherTrigger): void {
    this.triggers.push(trigger);
  }

  /**
   * 移除天气触发器
   */
  removeWeatherTrigger(triggerId: string): void {
    this.triggers = this.triggers.filter(t => t.id !== triggerId);
  }

  /**
   * 清理天气历史数据
   */
  private cleanupWeatherHistory(): void {
    const retentionTime = this.state.config.weatherHistoryRetentionDays * 24 * 60 * 60 * 1000;
    const cutoffTime = Date.now() - retentionTime;
    
    this.state.weatherHistory = this.state.weatherHistory.filter(
      weather => weather.timestamp.getTime() > cutoffTime
    );
  }

  /**
   * 保存天气数据
   */
  private saveWeatherData(): void {
    try {
      const data = {
        preferences: this.state.preferences,
        stats: this.state.stats,
        history: this.state.weatherHistory.slice(-100), // 只保存最近100条记录
        lastSaved: new Date().toISOString()
      };
      
      localStorage.setItem('weather_manager_data', JSON.stringify(data));
    } catch (error) {
      console.error('保存天气数据失败:', error);
    }
  }

  /**
   * 加载天气数据
   */
  private loadWeatherData(): void {
    try {
      const savedData = localStorage.getItem('weather_manager_data');
      if (savedData) {
        const data = JSON.parse(savedData);
        
        if (data.preferences) {
          this.state.preferences = { ...this.state.preferences, ...data.preferences };
        }
        
        if (data.stats) {
          this.state.stats = { ...this.state.stats, ...data.stats };
        }
        
        if (data.history) {
          this.state.weatherHistory = data.history.map((w: any) => ({
            ...w,
            timestamp: new Date(w.timestamp)
          }));
        }
        
        console.log('天气数据加载成功');
      }
    } catch (error) {
      console.error('加载天气数据失败:', error);
    }
  }

  /**
   * 重置天气系统
   */
  reset(): void {
    this.state.currentWeather = this.generateDefaultWeather();
    this.state.weatherHistory = [];
    this.state.isTransitioning = false;
    this.state.transitionProgress = 0;
    this.generateForecast();
    this.scheduleNextWeatherChange();
    
    this.emit('weatherReset');
  }

  /**
   * 销毁天气管理器
   */
  destroy(): void {
    if (this.updateTimer) {
      clearInterval(this.updateTimer);
    }
    
    if (this.transitionTimer) {
      clearInterval(this.transitionTimer);
    }
    
    if (this.state.config.autoSaveWeatherData) {
      this.saveWeatherData();
    }
    
    this.removeAllListeners();
    this.eventListeners = [];
    this.triggers = [];
    
    console.log('天气管理器已销毁');
  }
}

// 导出单例实例
export const weatherManager = new WeatherManager(); 