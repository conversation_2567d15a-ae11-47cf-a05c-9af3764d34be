import { WeatherType, WeatherIntensity, WeatherState } from '../types/weather'
import { AchievementService } from './AchievementService'
import { WEATHER_ACHIEVEMENTS, getWeatherTypeAchievements } from '../data/weatherAchievements'
import { WeatherManager } from './WeatherManager'
import { WeatherEffectCoordinator } from './WeatherEffectCoordinator'

/**
 * 天气专注会话数据接口
 */
interface WeatherFocusSession {
  sessionId: string
  weatherType: WeatherType
  weatherIntensity: WeatherIntensity
  startTime: Date
  endTime?: Date
  duration: number // 分钟
  focusScore: number // 0-100
  isCompleted: boolean
  isPerfect: boolean // 专注分数 >= 90
  wasOptimalTiming: boolean // 是否在推荐的最佳时机
  wasPlanned: boolean // 是否是根据天气预报计划的
}

/**
 * 天气成就统计数据接口
 */
interface WeatherAchievementStats {
  completedSessionsByWeather: Map<WeatherType, number>
  totalFocusTimeByWeather: Map<WeatherType, number> // 分钟
  perfectSessionsByWeather: Map<WeatherType, number>
  currentStreaks: {
    weatherVarietyStreak: WeatherType[] // 连续不同天气类型
    sameWeatherStreak: { type: WeatherType; count: number }
  }
  seasonalFocusTime: Map<string, number> // 季节 -> 总时长（分钟）
  adverseWeatherSessions: Set<WeatherType> // 已完成的恶劣天气类型
  plannedSessions: number // 根据预报计划的会话数
  optimalTimingSessions: number // 最佳时机会话数
  weatherSyncCount: number // 与天气变化同步的次数
  lastWeatherChange: Date | null
}

/**
 * 天气成就系统集成器
 * 负责监听天气相关事件，跟踪用户在不同天气条件下的表现，
 * 并触发相应的天气成就解锁
 */
export class WeatherAchievementIntegration {
  private static instance: WeatherAchievementIntegration
  private achievementService: AchievementService
  private weatherManager: WeatherManager
  private weatherCoordinator: WeatherEffectCoordinator | null = null
  
  // 数据存储
  private weatherStats: WeatherAchievementStats
  private sessionHistory: WeatherFocusSession[] = []
  private isInitialized: boolean = false
  
  // 事件监听
  private currentSession: WeatherFocusSession | null = null
  private readonly STORAGE_KEY = 'weather_achievement_stats'
  private readonly SESSION_HISTORY_KEY = 'weather_session_history'

  private constructor() {
    this.achievementService = AchievementService.getInstance()
    this.weatherManager = WeatherManager.getInstance()
    
    // 初始化统计数据
    this.weatherStats = this.initializeStats()
    this.loadStoredData()
  }

  /**
   * 获取单例实例
   */
  static getInstance(): WeatherAchievementIntegration {
    if (!WeatherAchievementIntegration.instance) {
      WeatherAchievementIntegration.instance = new WeatherAchievementIntegration()
    }
    return WeatherAchievementIntegration.instance
  }

  /**
   * 初始化集成系统
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return

    try {
      // 等待依赖服务初始化
      await this.achievementService.initialize()
      
      // 获取天气协调器实例
      this.weatherCoordinator = WeatherEffectCoordinator.getInstance()
      
      // 注册天气成就到成就系统
      await this.registerWeatherAchievements()
      
      // 设置事件监听器
      this.setupEventListeners()
      
      this.isInitialized = true
      console.log('天气成就集成系统初始化完成')
    } catch (error) {
      console.error('天气成就集成系统初始化失败:', error)
      throw error
    }
  }

  /**
   * 开始专注会话
   */
  startFocusSession(
    sessionId: string,
    isPlanned: boolean = false,
    isOptimalTiming: boolean = false
  ): void {
    const currentWeather = this.weatherManager.getCurrentWeather()
    
    this.currentSession = {
      sessionId,
      weatherType: currentWeather.type,
      weatherIntensity: currentWeather.intensity,
      startTime: new Date(),
      duration: 0,
      focusScore: 0,
      isCompleted: false,
      isPerfect: false,
      wasOptimalTiming: isOptimalTiming,
      wasPlanned: isPlanned
    }

    // 检查是否与天气变化同步
    this.checkWeatherSyncTiming()
    
    console.log(`开始天气专注会话: ${sessionId}, 天气: ${currentWeather.type}`)
  }

  /**
   * 结束专注会话
   */
  endFocusSession(
    sessionId: string,
    duration: number,
    focusScore: number,
    isCompleted: boolean = true
  ): void {
    if (!this.currentSession || this.currentSession.sessionId !== sessionId) {
      console.warn('没有找到匹配的专注会话')
      return
    }

    // 更新会话数据
    this.currentSession.endTime = new Date()
    this.currentSession.duration = duration
    this.currentSession.focusScore = focusScore
    this.currentSession.isCompleted = isCompleted
    this.currentSession.isPerfect = focusScore >= 90

    // 添加到历史记录
    this.sessionHistory.push({ ...this.currentSession })
    
    // 更新统计数据
    this.updateWeatherStats(this.currentSession)
    
    // 检查成就
    this.checkWeatherAchievements(this.currentSession)
    
    // 保存数据
    this.saveData()
    
    console.log(`结束天气专注会话: ${sessionId}, 得分: ${focusScore}`)
    this.currentSession = null
  }

  /**
   * 初始化统计数据
   */
  private initializeStats(): WeatherAchievementStats {
    return {
      completedSessionsByWeather: new Map(),
      totalFocusTimeByWeather: new Map(),
      perfectSessionsByWeather: new Map(),
      currentStreaks: {
        weatherVarietyStreak: [],
        sameWeatherStreak: { type: WeatherType.SUNNY, count: 0 }
      },
      seasonalFocusTime: new Map(),
      adverseWeatherSessions: new Set(),
      plannedSessions: 0,
      optimalTimingSessions: 0,
      weatherSyncCount: 0,
      lastWeatherChange: null
    }
  }

  /**
   * 注册天气成就到成就系统
   */
  private async registerWeatherAchievements(): Promise<void> {
    try {
      for (const achievement of WEATHER_ACHIEVEMENTS) {
        await this.achievementService.registerAchievement(achievement)
      }
      console.log(`注册了 ${WEATHER_ACHIEVEMENTS.length} 个天气成就`)
    } catch (error) {
      console.error('注册天气成就失败:', error)
    }
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 监听天气变化事件
    if (this.weatherCoordinator) {
      this.weatherCoordinator.on('weather_changed', this.onWeatherChanged.bind(this))
    }
    
    // 监听专注训练相关事件（需要从专注系统获取）
    // 这里可以添加专注系统的事件监听
  }

  /**
   * 处理天气变化事件
   */
  private onWeatherChanged(data: { 
    weatherType: WeatherType, 
    intensity: WeatherIntensity 
  }): void {
    this.weatherStats.lastWeatherChange = new Date()
    
    // 如果当前有进行中的会话，检查是否需要更新天气信息
    if (this.currentSession && !this.currentSession.endTime) {
      console.log(`专注会话期间天气发生变化: ${data.weatherType}`)
    }
  }

  /**
   * 更新天气统计数据
   */
  private updateWeatherStats(session: WeatherFocusSession): void {
    const { weatherType, duration, isPerfect, wasPlanned, wasOptimalTiming } = session

    // 更新完成会话数
    const currentSessions = this.weatherStats.completedSessionsByWeather.get(weatherType) || 0
    this.weatherStats.completedSessionsByWeather.set(weatherType, currentSessions + 1)

    // 更新专注时长
    const currentTime = this.weatherStats.totalFocusTimeByWeather.get(weatherType) || 0
    this.weatherStats.totalFocusTimeByWeather.set(weatherType, currentTime + duration)

    // 更新完美会话数
    if (isPerfect) {
      const currentPerfect = this.weatherStats.perfectSessionsByWeather.get(weatherType) || 0
      this.weatherStats.perfectSessionsByWeather.set(weatherType, currentPerfect + 1)
    }

    // 更新连击统计
    this.updateStreakStats(weatherType)
    
    // 更新季节性统计
    this.updateSeasonalStats(duration)
    
    // 更新恶劣天气统计
    this.updateAdverseWeatherStats(weatherType)
    
    // 更新计划会话统计
    if (wasPlanned) {
      this.weatherStats.plannedSessions++
    }
    
    // 更新最佳时机统计
    if (wasOptimalTiming) {
      this.weatherStats.optimalTimingSessions++
    }
  }

  /**
   * 更新连击统计
   */
  private updateStreakStats(weatherType: WeatherType): void {
    const streaks = this.weatherStats.currentStreaks
    
    // 更新天气多样性连击
    const lastWeather = streaks.weatherVarietyStreak[streaks.weatherVarietyStreak.length - 1]
    if (lastWeather !== weatherType) {
      streaks.weatherVarietyStreak.push(weatherType)
      // 保持最近的连击记录，避免无限增长
      if (streaks.weatherVarietyStreak.length > 10) {
        streaks.weatherVarietyStreak = streaks.weatherVarietyStreak.slice(-10)
      }
    }
    
    // 更新同种天气连击
    if (streaks.sameWeatherStreak.type === weatherType) {
      streaks.sameWeatherStreak.count++
    } else {
      streaks.sameWeatherStreak = { type: weatherType, count: 1 }
    }
  }

  /**
   * 更新季节性统计
   */
  private updateSeasonalStats(duration: number): void {
    const now = new Date()
    const month = now.getMonth() + 1
    
    let season: string
    if (month >= 3 && month <= 5) season = 'spring'
    else if (month >= 6 && month <= 8) season = 'summer'
    else if (month >= 9 && month <= 11) season = 'autumn'
    else season = 'winter'
    
    const currentTime = this.weatherStats.seasonalFocusTime.get(season) || 0
    this.weatherStats.seasonalFocusTime.set(season, currentTime + duration)
  }

  /**
   * 更新恶劣天气统计
   */
  private updateAdverseWeatherStats(weatherType: WeatherType): void {
    const adverseWeathers = [
      WeatherType.THUNDERSTORM,
      WeatherType.HEAVY_RAIN,
      WeatherType.SNOWY
    ]
    
    if (adverseWeathers.includes(weatherType)) {
      this.weatherStats.adverseWeatherSessions.add(weatherType)
    }
  }

  /**
   * 检查天气变化同步时机
   */
  private checkWeatherSyncTiming(): void {
    if (this.weatherStats.lastWeatherChange) {
      const timeDiff = Date.now() - this.weatherStats.lastWeatherChange.getTime()
      // 如果在天气变化后5分钟内开始专注，算作同步
      if (timeDiff <= 5 * 60 * 1000) {
        this.weatherStats.weatherSyncCount++
      }
    }
  }

  /**
   * 检查天气相关成就
   */
  private async checkWeatherAchievements(session: WeatherFocusSession): Promise<void> {
    const { weatherType, duration, focusScore, isPerfect, wasPlanned, wasOptimalTiming } = session

    // 检查特定天气类型的成就
    await this.checkWeatherTypeAchievements(weatherType, duration, focusScore, isPerfect)
    
    // 检查天气适应性成就
    await this.checkAdaptabilityAchievements()
    
    // 检查连击成就
    await this.checkStreakAchievements()
    
    // 检查特殊条件成就
    await this.checkSpecialConditionAchievements(session)
    
    // 检查计划相关成就
    if (wasPlanned) {
      await this.checkPlanningAchievements()
    }
    
    // 检查最佳时机成就
    if (wasOptimalTiming) {
      await this.checkOptimalTimingAchievements()
    }
  }

  /**
   * 检查特定天气类型成就
   */
  private async checkWeatherTypeAchievements(
    weatherType: WeatherType,
    duration: number,
    focusScore: number,
    isPerfect: boolean
  ): Promise<void> {
    const achievements = getWeatherTypeAchievements(weatherType)
    
    for (const achievement of achievements) {
      const req = achievement.requirement
      let shouldUnlock = false
      
      switch (req.type) {
        case 'weather_focus_time':
          if (duration >= req.value) shouldUnlock = true
          break
          
        case 'weather_session_complete':
          if (duration > 0) shouldUnlock = true
          break
          
        case 'weather_perfect_session':
          if (isPerfect) shouldUnlock = true
          break
          
        case 'negative_weather_high_score':
          // 检查是否在负面天气影响下仍获得高分
          if (this.isNegativeWeather(weatherType) && focusScore >= req.value) {
            shouldUnlock = true
          }
          break
      }
      
      if (shouldUnlock) {
        await this.achievementService.unlockAchievement(achievement.id)
      }
    }
  }

  /**
   * 检查天气适应性成就
   */
  private async checkAdaptabilityAchievements(): Promise<void> {
    const completedWeatherTypes = Array.from(this.weatherStats.completedSessionsByWeather.keys())
    
    // 检查天气适应大师（5种天气）
    if (completedWeatherTypes.length >= 5) {
      await this.achievementService.unlockAchievement('weather_adaptability')
    }
    
    // 检查全天候专注大师（9种天气）
    if (completedWeatherTypes.length >= 9) {
      await this.achievementService.unlockAchievement('weather_master')
    }
  }

  /**
   * 检查连击成就
   */
  private async checkStreakAchievements(): Promise<void> {
    const streaks = this.weatherStats.currentStreaks
    
    // 检查天气多样性连击
    const uniqueRecentWeathers = new Set(streaks.weatherVarietyStreak.slice(-5))
    if (uniqueRecentWeathers.size >= 5) {
      await this.achievementService.unlockAchievement('weather_combo_5')
    }
    
    // 检查同种天气连击成就
    if (streaks.sameWeatherStreak.count >= 3 && 
        streaks.sameWeatherStreak.type === WeatherType.SUNNY) {
      await this.achievementService.unlockAchievement('sunny_streak')
    }
  }

  /**
   * 检查特殊条件成就
   */
  private async checkSpecialConditionAchievements(session: WeatherFocusSession): Promise<void> {
    // 检查恶劣天气冠军
    if (this.weatherStats.adverseWeatherSessions.size >= 2) {
      await this.achievementService.unlockAchievement('adverse_weather_champion')
    }
    
    // 检查季节性成就
    const seasonsWith100Min = Array.from(this.weatherStats.seasonalFocusTime.values())
      .filter(time => time >= 100)
    if (seasonsWith100Min.length >= 4) {
      await this.achievementService.unlockAchievement('seasonal_dedication')
    }
    
    // 检查天气同步成就
    if (this.weatherStats.weatherSyncCount >= 10) {
      await this.achievementService.unlockAchievement('weather_harmony')
    }
  }

  /**
   * 检查计划相关成就
   */
  private async checkPlanningAchievements(): Promise<void> {
    if (this.weatherStats.plannedSessions >= 5) {
      await this.achievementService.unlockAchievement('weather_planner')
    }
  }

  /**
   * 检查最佳时机成就
   */
  private async checkOptimalTimingAchievements(): Promise<void> {
    if (this.weatherStats.optimalTimingSessions >= 1) {
      await this.achievementService.unlockAchievement('optimal_timing')
    }
  }

  /**
   * 判断是否为负面天气
   */
  private isNegativeWeather(weatherType: WeatherType): boolean {
    const negativeWeathers = [
      WeatherType.THUNDERSTORM,
      WeatherType.HEAVY_RAIN,
      WeatherType.FOGGY,
      WeatherType.WINDY
    ]
    return negativeWeathers.includes(weatherType)
  }

  /**
   * 保存数据到本地存储
   */
  private saveData(): void {
    try {
      // 转换Map为普通对象以便序列化
      const statsData = {
        ...this.weatherStats,
        completedSessionsByWeather: Object.fromEntries(this.weatherStats.completedSessionsByWeather),
        totalFocusTimeByWeather: Object.fromEntries(this.weatherStats.totalFocusTimeByWeather),
        perfectSessionsByWeather: Object.fromEntries(this.weatherStats.perfectSessionsByWeather),
        seasonalFocusTime: Object.fromEntries(this.weatherStats.seasonalFocusTime),
        adverseWeatherSessions: Array.from(this.weatherStats.adverseWeatherSessions)
      }
      
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(statsData))
      localStorage.setItem(this.SESSION_HISTORY_KEY, JSON.stringify(this.sessionHistory.slice(-100))) // 保留最近100条
    } catch (error) {
      console.error('保存天气成就数据失败:', error)
    }
  }

  /**
   * 从本地存储加载数据
   */
  private loadStoredData(): void {
    try {
      // 加载统计数据
      const statsData = localStorage.getItem(this.STORAGE_KEY)
      if (statsData) {
        const parsed = JSON.parse(statsData)
        this.weatherStats = {
          ...parsed,
          completedSessionsByWeather: new Map(Object.entries(parsed.completedSessionsByWeather || {})),
          totalFocusTimeByWeather: new Map(Object.entries(parsed.totalFocusTimeByWeather || {})),
          perfectSessionsByWeather: new Map(Object.entries(parsed.perfectSessionsByWeather || {})),
          seasonalFocusTime: new Map(Object.entries(parsed.seasonalFocusTime || {})),
          adverseWeatherSessions: new Set(parsed.adverseWeatherSessions || []),
          lastWeatherChange: parsed.lastWeatherChange ? new Date(parsed.lastWeatherChange) : null
        }
      }
      
      // 加载会话历史
      const historyData = localStorage.getItem(this.SESSION_HISTORY_KEY)
      if (historyData) {
        this.sessionHistory = JSON.parse(historyData).map((session: any) => ({
          ...session,
          startTime: new Date(session.startTime),
          endTime: session.endTime ? new Date(session.endTime) : undefined
        }))
      }
    } catch (error) {
      console.error('加载天气成就数据失败:', error)
    }
  }

  /**
   * 获取天气成就统计信息
   */
  getWeatherAchievementStats(): WeatherAchievementStats {
    return { ...this.weatherStats }
  }

  /**
   * 获取会话历史
   */
  getSessionHistory(): WeatherFocusSession[] {
    return [...this.sessionHistory]
  }

  /**
   * 清理资源
   */
  dispose(): void {
    this.saveData()
    this.currentSession = null
    this.isInitialized = false
  }
}

export default WeatherAchievementIntegration 