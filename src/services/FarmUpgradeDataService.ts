// 农场升级数据持久化服务
// 专门处理农场升级系统相关数据的存储和加载

import { FarmLevel, UpgradeHistory, UpgradeCondition, FarmLevelConfig } from '../systems/FarmUpgradeSystem'
import { UnlockContent, UnlockType } from '../systems/FarmUnlockSystem'
import { CropType } from '../types/crop'

// 解锁历史记录
export interface UnlockHistory {
  contentId: string
  contentName: string
  type: UnlockType
  level: FarmLevel
  unlockedAt: Date
  prerequisites: string[]
}

// 解锁进度记录
export interface UnlockProgress {
  contentId: string
  isUnlocked: boolean
  unlockedAt?: Date
  lastChecked: Date
  progress: number // 0-100
}

// 存储键名常量
const STORAGE_KEYS = {
  FARM_LEVEL: 'farm_upgrade_level',
  UPGRADE_HISTORY: 'farm_upgrade_history', 
  UNLOCK_HISTORY: 'farm_unlock_history',
  UPGRADE_PROGRESS: 'farm_upgrade_progress',
  UNLOCK_PROGRESS: 'farm_unlock_progress',
  LEVEL_STATISTICS: 'farm_level_statistics',
  UPGRADE_SETTINGS: 'farm_upgrade_settings',
  LAST_SYNC: 'farm_upgrade_last_sync',
  DATA_VERSION: 'farm_upgrade_data_version'
} as const

// 农场升级进度数据
export interface FarmUpgradeProgress {
  currentLevel: FarmLevel
  currentExperience: number
  currentFocusTime: number
  cropsHarvested: number
  cropVariety: CropType[]
  achievementsCompleted: number
  currentStreak: number
  lastStreakDate?: Date
  perfectSessions: number
  marathonSessions: number
  personalRecords: number
  averageScore: number
  lastUpdated: Date
}

// 等级统计数据
export interface FarmLevelStatistics {
  totalTimeInLevel: Record<FarmLevel, number> // 每个等级停留时间（毫秒）
  upgradeAttempts: Record<FarmLevel, number> // 每个等级的升级尝试次数
  conditionsProgress: Record<FarmLevel, Record<string, number>> // 每个等级的条件进度记录
  fastestUpgrades: Record<FarmLevel, number> // 每个等级的最快升级时间
  upgradeSuccessRate: Record<FarmLevel, number> // 升级成功率
  levelFirstAchieved: Record<FarmLevel, Date> // 首次达到等级的时间
  totalExperienceGained: number
  totalFocusTimeLogged: number
  totalCropsHarvested: number
  lastUpdated: Date
}

// 升级系统设置
export interface FarmUpgradeSettings {
  enableNotifications: boolean
  autoShowUpgradeDialog: boolean
  enableUpgradeAnimations: boolean
  enableUpgradeSounds: boolean
  showProgressHints: boolean
  enableAutoSave: boolean
  backupFrequency: number // 小时
  dataRetentionDays: number
  enableAnalytics: boolean
  lastModified: Date
}

// 数据备份信息
export interface FarmUpgradeBackup {
  version: string
  timestamp: Date
  farmLevel: FarmLevel
  upgradeProgress: FarmUpgradeProgress
  upgradeHistory: UpgradeHistory[]
  unlockHistory: UnlockHistory[]
  unlockProgress: UnlockProgress[]
  levelStatistics: FarmLevelStatistics
  settings: FarmUpgradeSettings
  checksum: string
}

// 数据同步状态
interface SyncStatus {
  lastSync: Date | null
  hasPendingChanges: boolean
  syncInProgress: boolean
  lastError: string | null
}

/**
 * 农场升级数据持久化服务
 * 提供完整的数据存储、加载、备份和恢复功能
 */
export class FarmUpgradeDataService {
  private static instance: FarmUpgradeDataService | null = null
  private syncStatus: SyncStatus = {
    lastSync: null,
    hasPendingChanges: false,
    syncInProgress: false,
    lastError: null
  }
  private changeListeners: Array<(key: string, data: any) => void> = []
  private autoSaveTimer: number | null = null
  private readonly DATA_VERSION = '1.0.0'

  constructor() {
    this.initializeData()
    this.setupAutoSave()
  }

  // 单例模式
  static getInstance(): FarmUpgradeDataService {
    if (!FarmUpgradeDataService.instance) {
      FarmUpgradeDataService.instance = new FarmUpgradeDataService()
    }
    return FarmUpgradeDataService.instance
  }

  // ===== 初始化和设置 =====

  /**
   * 初始化数据结构
   */
  private async initializeData(): Promise<void> {
    try {
      // 检查数据版本
      const currentVersion = localStorage.getItem(STORAGE_KEYS.DATA_VERSION)
      if (!currentVersion || currentVersion !== this.DATA_VERSION) {
        await this.migrateData(currentVersion)
        localStorage.setItem(STORAGE_KEYS.DATA_VERSION, this.DATA_VERSION)
      }

      // 初始化默认数据
      const upgradeProgress = await this.getFarmUpgradeProgress()
      if (!upgradeProgress) {
        await this.saveFarmUpgradeProgress(this.getDefaultUpgradeProgress())
      }

      const settings = await this.getUpgradeSettings()
      if (!settings) {
        await this.saveUpgradeSettings(this.getDefaultSettings())
      }

      const statistics = await this.getLevelStatistics()
      if (!statistics) {
        await this.saveLevelStatistics(this.getDefaultStatistics())
      }

      console.log('✅ Farm upgrade data service initialized')
    } catch (error) {
      console.error('❌ Failed to initialize farm upgrade data service:', error)
    }
  }

  /**
   * 设置自动保存
   */
  private setupAutoSave(): void {
    const settings = JSON.parse(localStorage.getItem(STORAGE_KEYS.UPGRADE_SETTINGS) || '{}')
    if (settings.enableAutoSave !== false) {
      this.autoSaveTimer = window.setInterval(() => {
        if (this.syncStatus.hasPendingChanges) {
          this.createBackup('auto')
        }
      }, (settings.backupFrequency || 6) * 60 * 60 * 1000) // 默认6小时
    }
  }

  // ===== 农场等级和升级进度 =====

  /**
   * 获取当前农场等级
   */
  async getCurrentFarmLevel(): Promise<FarmLevel> {
    try {
      const data = localStorage.getItem(STORAGE_KEYS.FARM_LEVEL)
      return data ? parseInt(data) as FarmLevel : FarmLevel.NOVICE
    } catch (error) {
      console.error('❌ Error loading farm level:', error)
      return FarmLevel.NOVICE
    }
  }

  /**
   * 保存当前农场等级
   */
  async saveCurrentFarmLevel(level: FarmLevel): Promise<void> {
    try {
      localStorage.setItem(STORAGE_KEYS.FARM_LEVEL, level.toString())
      this.notifyChange(STORAGE_KEYS.FARM_LEVEL, level)
      this.syncStatus.hasPendingChanges = true
    } catch (error) {
      console.error('❌ Error saving farm level:', error)
      throw error
    }
  }

  /**
   * 获取农场升级进度
   */
  async getFarmUpgradeProgress(): Promise<FarmUpgradeProgress | null> {
    try {
      const data = localStorage.getItem(STORAGE_KEYS.UPGRADE_PROGRESS)
      if (!data) return null

      const parsed = JSON.parse(data)
      return {
        ...parsed,
        lastStreakDate: parsed.lastStreakDate ? new Date(parsed.lastStreakDate) : undefined,
        lastUpdated: new Date(parsed.lastUpdated)
      }
    } catch (error) {
      console.error('❌ Error loading upgrade progress:', error)
      return null
    }
  }

  /**
   * 保存农场升级进度
   */
  async saveFarmUpgradeProgress(progress: FarmUpgradeProgress): Promise<void> {
    try {
      progress.lastUpdated = new Date()
      localStorage.setItem(STORAGE_KEYS.UPGRADE_PROGRESS, JSON.stringify(progress))
      this.notifyChange(STORAGE_KEYS.UPGRADE_PROGRESS, progress)
      this.syncStatus.hasPendingChanges = true
    } catch (error) {
      console.error('❌ Error saving upgrade progress:', error)
      throw error
    }
  }

  /**
   * 更新升级进度的特定字段
   */
  async updateUpgradeProgress(updates: Partial<FarmUpgradeProgress>): Promise<void> {
    const currentProgress = await this.getFarmUpgradeProgress() || this.getDefaultUpgradeProgress()
    const updatedProgress = { ...currentProgress, ...updates }
    await this.saveFarmUpgradeProgress(updatedProgress)
  }

  // ===== 升级历史 =====

  /**
   * 获取升级历史
   */
  async getUpgradeHistory(): Promise<UpgradeHistory[]> {
    try {
      const data = localStorage.getItem(STORAGE_KEYS.UPGRADE_HISTORY)
      if (!data) return []

      const parsed = JSON.parse(data)
      return parsed.map((item: any) => ({
        ...item,
        achievedAt: new Date(item.achievedAt)
      }))
    } catch (error) {
      console.error('❌ Error loading upgrade history:', error)
      return []
    }
  }

  /**
   * 添加升级历史记录
   */
  async addUpgradeHistory(record: UpgradeHistory): Promise<void> {
    try {
      const history = await this.getUpgradeHistory()
      history.push(record)
      
      // 按时间降序排序
      history.sort((a, b) => b.achievedAt.getTime() - a.achievedAt.getTime())

      // 限制历史记录数量
      const maxRecords = 100
      if (history.length > maxRecords) {
        history.splice(maxRecords)
      }

      localStorage.setItem(STORAGE_KEYS.UPGRADE_HISTORY, JSON.stringify(history))
      this.notifyChange(STORAGE_KEYS.UPGRADE_HISTORY, history)
      this.syncStatus.hasPendingChanges = true
    } catch (error) {
      console.error('❌ Error adding upgrade history:', error)
      throw error
    }
  }

  // ===== 解锁历史和进度 =====

  /**
   * 获取解锁历史
   */
  async getUnlockHistory(): Promise<UnlockHistory[]> {
    try {
      const data = localStorage.getItem(STORAGE_KEYS.UNLOCK_HISTORY)
      if (!data) return []

      const parsed = JSON.parse(data)
      return parsed.map((item: any) => ({
        ...item,
        unlockedAt: new Date(item.unlockedAt)
      }))
    } catch (error) {
      console.error('❌ Error loading unlock history:', error)
      return []
    }
  }

  /**
   * 添加解锁历史记录
   */
  async addUnlockHistory(record: UnlockHistory): Promise<void> {
    try {
      const history = await this.getUnlockHistory()
      history.push(record)
      
      // 按时间降序排序
      history.sort((a, b) => b.unlockedAt.getTime() - a.unlockedAt.getTime())

      localStorage.setItem(STORAGE_KEYS.UNLOCK_HISTORY, JSON.stringify(history))
      this.notifyChange(STORAGE_KEYS.UNLOCK_HISTORY, history)
      this.syncStatus.hasPendingChanges = true
    } catch (error) {
      console.error('❌ Error adding unlock history:', error)
      throw error
    }
  }

  /**
   * 获取解锁进度
   */
  async getUnlockProgress(): Promise<UnlockProgress[]> {
    try {
      const data = localStorage.getItem(STORAGE_KEYS.UNLOCK_PROGRESS)
      if (!data) return []

      const parsed = JSON.parse(data)
      return parsed.map((item: any) => ({
        ...item,
        unlockedAt: item.unlockedAt ? new Date(item.unlockedAt) : undefined,
        lastChecked: new Date(item.lastChecked)
      }))
    } catch (error) {
      console.error('❌ Error loading unlock progress:', error)
      return []
    }
  }

  /**
   * 保存解锁进度
   */
  async saveUnlockProgress(progress: UnlockProgress[]): Promise<void> {
    try {
      localStorage.setItem(STORAGE_KEYS.UNLOCK_PROGRESS, JSON.stringify(progress))
      this.notifyChange(STORAGE_KEYS.UNLOCK_PROGRESS, progress)
      this.syncStatus.hasPendingChanges = true
    } catch (error) {
      console.error('❌ Error saving unlock progress:', error)
      throw error
    }
  }

  // ===== 等级统计 =====

  /**
   * 获取等级统计数据
   */
  async getLevelStatistics(): Promise<FarmLevelStatistics | null> {
    try {
      const data = localStorage.getItem(STORAGE_KEYS.LEVEL_STATISTICS)
      if (!data) return null

      const parsed = JSON.parse(data)
      return {
        ...parsed,
        levelFirstAchieved: Object.fromEntries(
          Object.entries(parsed.levelFirstAchieved || {}).map(([key, value]) => [key, new Date(value as string)])
        ),
        lastUpdated: new Date(parsed.lastUpdated)
      }
    } catch (error) {
      console.error('❌ Error loading level statistics:', error)
      return null
    }
  }

  /**
   * 保存等级统计数据
   */
  async saveLevelStatistics(statistics: FarmLevelStatistics): Promise<void> {
    try {
      statistics.lastUpdated = new Date()
      localStorage.setItem(STORAGE_KEYS.LEVEL_STATISTICS, JSON.stringify(statistics))
      this.notifyChange(STORAGE_KEYS.LEVEL_STATISTICS, statistics)
      this.syncStatus.hasPendingChanges = true
    } catch (error) {
      console.error('❌ Error saving level statistics:', error)
      throw error
    }
  }

  /**
   * 更新等级统计的特定字段
   */
  async updateLevelStatistics(updates: Partial<FarmLevelStatistics>): Promise<void> {
    const currentStats = await this.getLevelStatistics() || this.getDefaultStatistics()
    const updatedStats = { ...currentStats, ...updates }
    await this.saveLevelStatistics(updatedStats)
  }

  // ===== 设置管理 =====

  /**
   * 获取升级系统设置
   */
  async getUpgradeSettings(): Promise<FarmUpgradeSettings | null> {
    try {
      const data = localStorage.getItem(STORAGE_KEYS.UPGRADE_SETTINGS)
      if (!data) return null

      const parsed = JSON.parse(data)
      return {
        ...parsed,
        lastModified: new Date(parsed.lastModified)
      }
    } catch (error) {
      console.error('❌ Error loading upgrade settings:', error)
      return null
    }
  }

  /**
   * 保存升级系统设置
   */
  async saveUpgradeSettings(settings: FarmUpgradeSettings): Promise<void> {
    try {
      settings.lastModified = new Date()
      localStorage.setItem(STORAGE_KEYS.UPGRADE_SETTINGS, JSON.stringify(settings))
      this.notifyChange(STORAGE_KEYS.UPGRADE_SETTINGS, settings)
      this.syncStatus.hasPendingChanges = true

      // 重新设置自动保存
      if (this.autoSaveTimer) {
        clearInterval(this.autoSaveTimer)
        this.autoSaveTimer = null
      }
      this.setupAutoSave()
    } catch (error) {
      console.error('❌ Error saving upgrade settings:', error)
      throw error
    }
  }

  /**
   * 更新设置的特定字段
   */
  async updateSettings(updates: Partial<FarmUpgradeSettings>): Promise<void> {
    const currentSettings = await this.getUpgradeSettings() || this.getDefaultSettings()
    const updatedSettings = { ...currentSettings, ...updates }
    await this.saveUpgradeSettings(updatedSettings)
  }

  // ===== 数据备份和恢复 =====

  /**
   * 创建数据备份
   */
  async createBackup(type: 'manual' | 'auto' = 'manual'): Promise<FarmUpgradeBackup> {
    try {
      const backup: FarmUpgradeBackup = {
        version: this.DATA_VERSION,
        timestamp: new Date(),
        farmLevel: await this.getCurrentFarmLevel(),
        upgradeProgress: await this.getFarmUpgradeProgress() || this.getDefaultUpgradeProgress(),
        upgradeHistory: await this.getUpgradeHistory(),
        unlockHistory: await this.getUnlockHistory(),
        unlockProgress: await this.getUnlockProgress(),
        levelStatistics: await this.getLevelStatistics() || this.getDefaultStatistics(),
        settings: await this.getUpgradeSettings() || this.getDefaultSettings(),
        checksum: ''
      }

      // 生成校验和
      backup.checksum = this.generateChecksum(backup)

      // 保存备份
      const backupKey = `${STORAGE_KEYS.FARM_LEVEL}_backup_${type}_${Date.now()}`
      localStorage.setItem(backupKey, JSON.stringify(backup))

      // 清理旧备份
      await this.cleanOldBackups()

      this.syncStatus.hasPendingChanges = false
      this.syncStatus.lastSync = new Date()

      console.log(`✅ Farm upgrade data backup created (${type})`)
      return backup
    } catch (error) {
      console.error('❌ Error creating backup:', error)
      throw error
    }
  }

  /**
   * 从备份恢复数据
   */
  async restoreFromBackup(backup: FarmUpgradeBackup): Promise<void> {
    try {
      // 验证备份
      if (!this.validateBackup(backup)) {
        throw new Error('Invalid backup data')
      }

      // 恢复数据
      await this.saveCurrentFarmLevel(backup.farmLevel)
      await this.saveFarmUpgradeProgress(backup.upgradeProgress)
      await this.saveLevelStatistics(backup.levelStatistics)
      await this.saveUpgradeSettings(backup.settings)

      // 恢复历史记录
      localStorage.setItem(STORAGE_KEYS.UPGRADE_HISTORY, JSON.stringify(backup.upgradeHistory))
      localStorage.setItem(STORAGE_KEYS.UNLOCK_HISTORY, JSON.stringify(backup.unlockHistory))
      localStorage.setItem(STORAGE_KEYS.UNLOCK_PROGRESS, JSON.stringify(backup.unlockProgress))

      console.log('✅ Farm upgrade data restored from backup')
    } catch (error) {
      console.error('❌ Error restoring from backup:', error)
      throw error
    }
  }

  /**
   * 导出数据为JSON字符串
   */
  async exportData(): Promise<string> {
    const backup = await this.createBackup('manual')
    return JSON.stringify(backup, null, 2)
  }

  /**
   * 从JSON字符串导入数据
   */
  async importData(dataString: string): Promise<void> {
    try {
      const backup = JSON.parse(dataString) as FarmUpgradeBackup
      await this.restoreFromBackup(backup)
    } catch (error) {
      console.error('❌ Error importing data:', error)
      throw error
    }
  }

  // ===== 数据清理和维护 =====

  /**
   * 清理过期数据
   */
  async cleanupOldData(): Promise<void> {
    try {
      const settings = await this.getUpgradeSettings() || this.getDefaultSettings()
      const retentionDate = new Date(Date.now() - settings.dataRetentionDays * 24 * 60 * 60 * 1000)

      // 清理过期的历史记录
      const upgradeHistory = await this.getUpgradeHistory()
      const filteredUpgradeHistory = upgradeHistory.filter(record => record.achievedAt > retentionDate)
      if (filteredUpgradeHistory.length !== upgradeHistory.length) {
        localStorage.setItem(STORAGE_KEYS.UPGRADE_HISTORY, JSON.stringify(filteredUpgradeHistory))
      }

      const unlockHistory = await this.getUnlockHistory()
      const filteredUnlockHistory = unlockHistory.filter(record => record.unlockedAt > retentionDate)
      if (filteredUnlockHistory.length !== unlockHistory.length) {
        localStorage.setItem(STORAGE_KEYS.UNLOCK_HISTORY, JSON.stringify(filteredUnlockHistory))
      }

      console.log('✅ Old farm upgrade data cleaned up')
    } catch (error) {
      console.error('❌ Error cleaning up old data:', error)
    }
  }

  /**
   * 清理旧备份
   */
  private async cleanOldBackups(): Promise<void> {
    try {
      const keys = Object.keys(localStorage)
      const backupKeys = keys.filter(key => key.includes('_backup_'))
      
      if (backupKeys.length > 10) { // 保留最近10个备份
        backupKeys.sort().slice(0, backupKeys.length - 10).forEach(key => {
          localStorage.removeItem(key)
        })
      }
    } catch (error) {
      console.error('❌ Error cleaning old backups:', error)
    }
  }

  // ===== 数据验证和校验 =====

  /**
   * 验证备份数据的完整性
   */
  private validateBackup(backup: FarmUpgradeBackup): boolean {
    try {
      // 检查基本字段
      if (!backup.version || !backup.timestamp || !backup.farmLevel) {
        return false
      }

      // 验证校验和
      const calculatedChecksum = this.generateChecksum({ ...backup, checksum: '' })
      if (calculatedChecksum !== backup.checksum) {
        console.warn('Backup checksum mismatch')
        return false
      }

      return true
    } catch (error) {
      console.error('❌ Error validating backup:', error)
      return false
    }
  }

  /**
   * 生成数据校验和
   */
  private generateChecksum(data: any): string {
    const str = JSON.stringify(data)
    let hash = 0
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // 转换为32位整数
    }
    return hash.toString(16)
  }

  // ===== 默认数据生成 =====

  /**
   * 获取默认升级进度
   */
  private getDefaultUpgradeProgress(): FarmUpgradeProgress {
    return {
      currentLevel: FarmLevel.NOVICE,
      currentExperience: 0,
      currentFocusTime: 0,
      cropsHarvested: 0,
      cropVariety: [],
      achievementsCompleted: 0,
      currentStreak: 0,
      perfectSessions: 0,
      marathonSessions: 0,
      personalRecords: 0,
      averageScore: 0,
      lastUpdated: new Date()
    }
  }

  /**
   * 获取默认等级统计
   */
  private getDefaultStatistics(): FarmLevelStatistics {
    return {
      totalTimeInLevel: {} as Record<FarmLevel, number>,
      upgradeAttempts: {} as Record<FarmLevel, number>,
      conditionsProgress: {} as Record<FarmLevel, Record<string, number>>,
      fastestUpgrades: {} as Record<FarmLevel, number>,
      upgradeSuccessRate: {} as Record<FarmLevel, number>,
      levelFirstAchieved: {} as Record<FarmLevel, Date>,
      totalExperienceGained: 0,
      totalFocusTimeLogged: 0,
      totalCropsHarvested: 0,
      lastUpdated: new Date()
    }
  }

  /**
   * 获取默认设置
   */
  private getDefaultSettings(): FarmUpgradeSettings {
    return {
      enableNotifications: true,
      autoShowUpgradeDialog: true,
      enableUpgradeAnimations: true,
      enableUpgradeSounds: true,
      showProgressHints: true,
      enableAutoSave: true,
      backupFrequency: 6,
      dataRetentionDays: 30,
      enableAnalytics: true,
      lastModified: new Date()
    }
  }

  // ===== 数据迁移 =====

  /**
   * 数据版本迁移
   */
  private async migrateData(fromVersion: string | null): Promise<void> {
    console.log(`🔄 Migrating farm upgrade data from version ${fromVersion} to ${this.DATA_VERSION}`)
    
    // 这里可以添加版本迁移逻辑
    // 例如：从旧版本的数据结构转换到新版本
    
    console.log('✅ Farm upgrade data migration completed')
  }

  // ===== 事件和监听 =====

  /**
   * 注册数据变更监听器
   */
  addChangeListener(callback: (key: string, data: any) => void): void {
    this.changeListeners.push(callback)
  }

  /**
   * 移除数据变更监听器
   */
  removeChangeListener(callback: (key: string, data: any) => void): void {
    const index = this.changeListeners.indexOf(callback)
    if (index > -1) {
      this.changeListeners.splice(index, 1)
    }
  }

  /**
   * 通知数据变更
   */
  private notifyChange(key: string, data: any): void {
    this.changeListeners.forEach(callback => {
      try {
        callback(key, data)
      } catch (error) {
        console.error('❌ Error in change listener:', error)
      }
    })
  }

  // ===== 清理资源 =====

  /**
   * 销毁服务实例
   */
  destroy(): void {
    if (this.autoSaveTimer) {
      clearInterval(this.autoSaveTimer)
      this.autoSaveTimer = null
    }
    this.changeListeners.length = 0
    FarmUpgradeDataService.instance = null
  }
}

export default FarmUpgradeDataService