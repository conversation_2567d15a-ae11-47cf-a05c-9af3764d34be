// 数据安全与备份服务

import { DatabaseManager } from '../storage/DatabaseManager'
import { BackupInfo, DataIntegrityCheck } from '../types/user'

export interface EncryptionOptions {
  algorithm: 'AES-GCM' | 'AES-CBC'
  keyLength: 128 | 192 | 256
  ivLength?: number
}

export interface BackupOptions {
  includeUserData: boolean
  includeGameProgress: boolean
  includeBehaviorData: boolean
  compression: boolean
  encryption: boolean
}

export interface RestoreOptions {
  validateIntegrity: boolean
  mergeWithExisting: boolean
  createBackupBeforeRestore: boolean
}

export interface SecurityReport {
  lastBackup: number | null
  backupCount: number
  dataIntegrity: DataIntegrityCheck
  encryptionStatus: 'enabled' | 'disabled'
  lastSecurityCheck: number
  vulnerabilities: Array<{
    type: string
    severity: 'low' | 'medium' | 'high'
    description: string
    recommendation: string
  }>
}

export class DataSecurityService {
  private dbManager: DatabaseManager
  private encryptionKey: CryptoKey | null = null
  private backupScheduleTimer: number | null = null
  private securityCheckTimer: number | null = null

  constructor(dbManager: DatabaseManager) {
    this.dbManager = dbManager
  }

  /**
   * 初始化安全服务
   */
  async initialize(options: { enableEncryption?: boolean; autoBackup?: boolean }): Promise<boolean> {
    try {
      // 初始化加密（如果启用）
      if (options.enableEncryption) {
        await this.initializeEncryption()
      }

      // 启动自动备份（如果启用）
      if (options.autoBackup) {
        this.startAutoBackup()
      }

      // 启动定期安全检查
      this.startSecurityChecks()

      console.log('DataSecurityService initialized successfully')
      return true
    } catch (error) {
      console.error('Failed to initialize DataSecurityService:', error)
      return false
    }
  }

  /**
   * 初始化加密系统
   */
  private async initializeEncryption(options: EncryptionOptions = {
    algorithm: 'AES-GCM',
    keyLength: 256
  }): Promise<void> {
    try {
      // 生成或恢复加密密钥
      const savedKeyData = localStorage.getItem('app_encryption_key')
      
      if (savedKeyData) {
        // 恢复现有密钥
        const keyData = JSON.parse(savedKeyData)
        this.encryptionKey = await crypto.subtle.importKey(
          'raw',
          new Uint8Array(keyData),
          { name: options.algorithm },
          false,
          ['encrypt', 'decrypt']
        )
      } else {
        // 生成新密钥
        this.encryptionKey = await crypto.subtle.generateKey(
          {
            name: options.algorithm,
            length: options.keyLength
          },
          true,
          ['encrypt', 'decrypt']
        )

        // 保存密钥（注意：生产环境中应使用更安全的密钥管理）
        const keyData = await crypto.subtle.exportKey('raw', this.encryptionKey)
        localStorage.setItem('app_encryption_key', JSON.stringify(Array.from(new Uint8Array(keyData))))
      }

      console.log('Encryption system initialized')
    } catch (error) {
      console.error('Failed to initialize encryption:', error)
      throw error
    }
  }

  /**
   * 加密数据
   */
  async encryptData(data: string): Promise<{ encrypted: string; iv: string } | null> {
    if (!this.encryptionKey) {
      console.warn('Encryption key not available')
      return null
    }

    try {
      const encoder = new TextEncoder()
      const dataBuffer = encoder.encode(data)
      
      // 生成随机IV
      const iv = crypto.getRandomValues(new Uint8Array(12))
      
      // 加密数据
      const encryptedBuffer = await crypto.subtle.encrypt(
        { name: 'AES-GCM', iv },
        this.encryptionKey,
        dataBuffer
      )

      return {
        encrypted: Array.from(new Uint8Array(encryptedBuffer)).map(b => b.toString(16).padStart(2, '0')).join(''),
        iv: Array.from(iv).map(b => b.toString(16).padStart(2, '0')).join('')
      }
    } catch (error) {
      console.error('Encryption failed:', error)
      return null
    }
  }

  /**
   * 解密数据
   */
  async decryptData(encryptedData: string, ivString: string): Promise<string | null> {
    if (!this.encryptionKey) {
      console.warn('Encryption key not available')
      return null
    }

    try {
      // 转换十六进制字符串回Uint8Array
      const encryptedBuffer = new Uint8Array(
        encryptedData.match(/.{1,2}/g)!.map(byte => parseInt(byte, 16))
      )
      const iv = new Uint8Array(
        ivString.match(/.{1,2}/g)!.map(byte => parseInt(byte, 16))
      )

      // 解密数据
      const decryptedBuffer = await crypto.subtle.decrypt(
        { name: 'AES-GCM', iv },
        this.encryptionKey,
        encryptedBuffer
      )

      const decoder = new TextDecoder()
      return decoder.decode(decryptedBuffer)
    } catch (error) {
      console.error('Decryption failed:', error)
      return null
    }
  }

  /**
   * 创建完整数据备份
   */
  async createBackup(
    description: string,
    options: BackupOptions = {
      includeUserData: true,
      includeGameProgress: true,
      includeBehaviorData: true,
      compression: true,
      encryption: false
    }
  ): Promise<BackupInfo | null> {
    try {
      const backupData: any = {
        timestamp: Date.now(),
        version: '1.0.0',
        description,
        options
      }

      // 收集数据
      if (options.includeUserData) {
        backupData.userData = await this.dbManager.getUserProfile()
      }

      if (options.includeGameProgress) {
        backupData.gameProgress = await this.dbManager.getGameProgress()
      }

      if (options.includeBehaviorData) {
        backupData.behaviorData = await this.dbManager.getBehaviorRecords(undefined, undefined, 1000)
      }

      // 序列化数据
      let dataString = JSON.stringify(backupData)

      // 压缩（简单的方式，生产环境可使用更好的压缩算法）
      if (options.compression) {
        // 这里可以实现压缩逻辑
        console.log('Compression enabled (placeholder)')
      }

      // 加密
      let encryptionInfo = null
      if (options.encryption && this.encryptionKey) {
        const encrypted = await this.encryptData(dataString)
        if (encrypted) {
          dataString = JSON.stringify(encrypted)
          encryptionInfo = { algorithm: 'AES-GCM', encrypted: true }
        }
      }

      // 创建备份信息
      const backupInfo: BackupInfo = {
        id: `backup_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        timestamp: Date.now(),
        description,
        size: dataString.length,
        checksum: await this.calculateChecksum(dataString),
        version: '1.0.0',
        encrypted: options.encryption,
        compressed: options.compression,
        metadata: {
          userAgent: navigator.userAgent,
          platform: navigator.platform,
          ...encryptionInfo
        }
      }

      // 保存备份
      const success = await this.dbManager.createBackup(description, backupInfo)
      
      if (success) {
        // 保存备份数据到本地存储
        localStorage.setItem(`backup_${backupInfo.id}`, dataString)
        
        console.log(`Backup created: ${backupInfo.id}`)
        return backupInfo
      }

      return null
    } catch (error) {
      console.error('Failed to create backup:', error)
      return null
    }
  }

  /**
   * 获取备份列表
   */
  async getBackupList(): Promise<BackupInfo[]> {
    return await this.dbManager.getBackupList()
  }

  /**
   * 恢复备份
   */
  async restoreBackup(
    backupId: string,
    options: RestoreOptions = {
      validateIntegrity: true,
      mergeWithExisting: false,
      createBackupBeforeRestore: true
    }
  ): Promise<boolean> {
    try {
      // 创建恢复前备份
      if (options.createBackupBeforeRestore) {
        await this.createBackup(`Pre-restore backup - ${new Date().toISOString()}`)
      }

      // 获取备份数据
      const backupDataString = localStorage.getItem(`backup_${backupId}`)
      if (!backupDataString) {
        console.error('Backup data not found')
        return false
      }

      // 验证完整性
      if (options.validateIntegrity) {
        const backupInfo = (await this.getBackupList()).find(b => b.id === backupId)
        if (backupInfo) {
          const currentChecksum = await this.calculateChecksum(backupDataString)
          if (currentChecksum !== backupInfo.checksum) {
            console.error('Backup integrity check failed')
            return false
          }
        }
      }

      // 处理数据
      let backupData
      try {
        const rawData = JSON.parse(backupDataString)
        
        // 如果数据是加密的，解密
        if (rawData.encrypted && rawData.iv) {
          const decrypted = await this.decryptData(rawData.encrypted, rawData.iv)
          if (!decrypted) {
            console.error('Failed to decrypt backup data')
            return false
          }
          backupData = JSON.parse(decrypted)
        } else {
          backupData = rawData
        }
      } catch (error) {
        console.error('Failed to parse backup data:', error)
        return false
      }

      // 恢复数据
      let success = true

      if (backupData.userData) {
        success = success && await this.dbManager.saveUserProfile(backupData.userData)
      }

      if (backupData.gameProgress) {
        success = success && await this.dbManager.saveGameProgress(backupData.gameProgress)
      }

      if (backupData.behaviorData && Array.isArray(backupData.behaviorData)) {
        // 如果合并现有数据
        if (options.mergeWithExisting) {
          for (const record of backupData.behaviorData) {
            await this.dbManager.logBehavior(record.event)
          }
        } else {
          // 替换现有数据（这需要额外的实现）
          console.log('Full behavior data replacement not implemented')
        }
      }

      if (success) {
        // 记录恢复事件
        await this.dbManager.logBehavior({
          type: 'backup_restored',
          category: 'system',
          details: `Backup ${backupId} restored successfully`
        })

        console.log(`Backup ${backupId} restored successfully`)
      }

      return success
    } catch (error) {
      console.error('Failed to restore backup:', error)
      return false
    }
  }

  /**
   * 删除备份
   */
  async deleteBackup(backupId: string): Promise<boolean> {
    try {
      // 从本地存储删除
      localStorage.removeItem(`backup_${backupId}`)
      
      // 记录删除事件
      await this.dbManager.logBehavior({
        type: 'backup_deleted',
        category: 'system',
        details: `Backup ${backupId} deleted`
      })

      console.log(`Backup ${backupId} deleted`)
      return true
    } catch (error) {
      console.error('Failed to delete backup:', error)
      return false
    }
  }

  /**
   * 执行数据完整性检查
   */
  async performIntegrityCheck(): Promise<DataIntegrityCheck> {
    const errors: string[] = []
    const warnings: string[] = []

    try {
      // 检查用户数据
      const userProfile = await this.dbManager.getUserProfile()
      if (userProfile) {
        if (!userProfile.id || !userProfile.username) {
          errors.push('User profile missing required fields')
        }
        if (userProfile.createdAt > Date.now()) {
          warnings.push('User creation date is in the future')
        }
      }

      // 检查游戏进度
      const gameProgress = await this.dbManager.getGameProgress()
      if (gameProgress) {
        if (gameProgress.level < 1) {
          errors.push('Invalid game level')
        }
        if (gameProgress.experience < 0) {
          errors.push('Negative experience value')
        }
      }

      // 检查行为数据
      const recentBehaviors = await this.dbManager.getBehaviorRecords(undefined, undefined, 100)
      const futureEvents = recentBehaviors.filter(r => r.timestamp > Date.now())
      if (futureEvents.length > 0) {
        warnings.push(`${futureEvents.length} behavior events have future timestamps`)
      }

      return {
        isValid: errors.length === 0,
        timestamp: Date.now(),
        errors,
        warnings,
        checkedItems: ['userProfile', 'gameProgress', 'behaviorData']
      }
    } catch (error) {
      console.error('Integrity check failed:', error)
      return {
        isValid: false,
        timestamp: Date.now(),
        errors: [`Integrity check failed: ${error}`],
        warnings,
        checkedItems: []
      }
    }
  }

  /**
   * 生成安全报告
   */
  async generateSecurityReport(): Promise<SecurityReport> {
    const backupList = await this.getBackupList()
    const integrityCheck = await this.performIntegrityCheck()
    const vulnerabilities: SecurityReport['vulnerabilities'] = []

    // 检查备份状态
    const lastBackup = backupList.length > 0 
      ? Math.max(...backupList.map(b => b.timestamp))
      : null

    if (!lastBackup || (Date.now() - lastBackup > 7 * 24 * 60 * 60 * 1000)) {
      vulnerabilities.push({
        type: 'backup',
        severity: 'medium',
        description: 'No recent backups found',
        recommendation: 'Create regular backups to prevent data loss'
      })
    }

    // 检查加密状态
    const encryptionStatus = this.encryptionKey ? 'enabled' : 'disabled'
    if (encryptionStatus === 'disabled') {
      vulnerabilities.push({
        type: 'encryption',
        severity: 'low',
        description: 'Data encryption is not enabled',
        recommendation: 'Enable encryption for sensitive data protection'
      })
    }

    // 检查数据完整性
    if (!integrityCheck.isValid) {
      vulnerabilities.push({
        type: 'integrity',
        severity: 'high',
        description: 'Data integrity issues detected',
        recommendation: 'Review and fix data integrity errors immediately'
      })
    }

    return {
      lastBackup,
      backupCount: backupList.length,
      dataIntegrity: integrityCheck,
      encryptionStatus,
      lastSecurityCheck: Date.now(),
      vulnerabilities
    }
  }

  /**
   * 启动自动备份
   */
  private startAutoBackup(intervalHours: number = 24): void {
    this.stopAutoBackup()
    
    this.backupScheduleTimer = window.setInterval(async () => {
      const backup = await this.createBackup('Automatic backup')
      if (backup) {
        console.log('Automatic backup completed')
      } else {
        console.warn('Automatic backup failed')
      }
    }, intervalHours * 60 * 60 * 1000)
  }

  /**
   * 停止自动备份
   */
  private stopAutoBackup(): void {
    if (this.backupScheduleTimer !== null) {
      clearInterval(this.backupScheduleTimer)
      this.backupScheduleTimer = null
    }
  }

  /**
   * 启动定期安全检查
   */
  private startSecurityChecks(intervalHours: number = 6): void {
    this.stopSecurityChecks()
    
    this.securityCheckTimer = window.setInterval(async () => {
      const report = await this.generateSecurityReport()
      const highSeverityIssues = report.vulnerabilities.filter(v => v.severity === 'high')
      
      if (highSeverityIssues.length > 0) {
        console.warn('High severity security issues detected:', highSeverityIssues)
      }
    }, intervalHours * 60 * 60 * 1000)
  }

  /**
   * 停止定期安全检查
   */
  private stopSecurityChecks(): void {
    if (this.securityCheckTimer !== null) {
      clearInterval(this.securityCheckTimer)
      this.securityCheckTimer = null
    }
  }

  /**
   * 计算校验和
   */
  private async calculateChecksum(data: string): Promise<string> {
    const encoder = new TextEncoder()
    const dataBuffer = encoder.encode(data)
    const hashBuffer = await crypto.subtle.digest('SHA-256', dataBuffer)
    const hashArray = new Uint8Array(hashBuffer)
    return Array.from(hashArray).map(b => b.toString(16).padStart(2, '0')).join('')
  }

  /**
   * 清理过期备份
   */
  async cleanupOldBackups(retentionDays: number = 30, maxBackups: number = 10): Promise<number> {
    const backupList = await this.getBackupList()
    const cutoffTime = Date.now() - (retentionDays * 24 * 60 * 60 * 1000)
    
    // 按时间排序，保留最新的备份
    const sortedBackups = backupList.sort((a, b) => b.timestamp - a.timestamp)
    const backupsToDelete = sortedBackups.filter((backup, index) => 
      backup.timestamp < cutoffTime || index >= maxBackups
    )

    let deletedCount = 0
    for (const backup of backupsToDelete) {
      const success = await this.deleteBackup(backup.id)
      if (success) {
        deletedCount++
      }
    }

    if (deletedCount > 0) {
      console.log(`Cleaned up ${deletedCount} old backups`)
    }

    return deletedCount
  }

  /**
   * 销毁服务
   */
  destroy(): void {
    this.stopAutoBackup()
    this.stopSecurityChecks()
    this.encryptionKey = null
  }
} 