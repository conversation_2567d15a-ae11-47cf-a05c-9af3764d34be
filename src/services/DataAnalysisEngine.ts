// 数据分析算法引擎
// 专门处理用户行为数据，生成深度分析结果和洞察

import DataAnalyticsCollector, { 
  DailyData, 
  WeeklyData, 
  MonthlyData, 
  SessionData,
  CropAnalyticsData,
  BehaviorPatternData,
  UserAnalyticsProfile
} from './DataAnalyticsCollector'
import { CropType } from '../types/crop'

// 分析结果类型定义
export interface TrendAnalysis {
  direction: 'increasing' | 'decreasing' | 'stable' | 'volatile'
  strength: number  // 0-100, 趋势强度
  confidence: number  // 0-100, 置信度
  changeRate: number  // 变化率百分比
  significance: 'high' | 'medium' | 'low'
}

export interface PerformanceInsights {
  overallScore: number  // 0-100, 综合表现分数
  strengths: string[]  // 优势领域
  weaknesses: string[]  // 需要改进的领域
  recommendations: string[]  // 具体建议
  keyMetrics: {
    consistencyScore: number  // 一致性分数
    improvementRate: number   // 改进速度
    engagementLevel: number   // 参与度
    efficiencyScore: number   // 效率分数
  }
}

export interface HabitFormationAnalysis {
  habitStrength: number  // 0-100, 习惯形成强度
  stability: number      // 0-100, 稳定性
  trend: TrendAnalysis
  streakAnalysis: {
    currentStreak: number
    longestStreak: number
    averageStreak: number
    streakReliability: number  // 连续性可靠性
  }
  riskFactors: string[]  // 可能影响习惯的风险因素
}

export interface ProductivityMetrics {
  focusEfficiency: number     // 专注效率
  timeUtilization: number     // 时间利用率
  taskCompletion: number      // 任务完成率
  qualityScore: number        // 质量分数
  peakPerformanceTimes: string[]  // 高效时段
  energyPattern: {
    morningEnergy: number
    afternoonEnergy: number
    eveningEnergy: number
    optimalWorkWindow: string
  }
}

export interface BehaviorPrediction {
  predictedScore: number      // 预测分数
  confidence: number          // 预测置信度
  timeHorizon: number         // 预测时间范围（天）
  factors: {
    name: string
    impact: number            // 影响权重 -100 到 100
    confidence: number
  }[]
  recommendations: string[]
}

export interface ComprehensiveAnalysis {
  userId: string
  generatedAt: number
  analysisType: 'daily' | 'weekly' | 'monthly' | 'comprehensive'
  
  // 核心分析结果
  performanceInsights: PerformanceInsights
  trendAnalysis: TrendAnalysis
  habitFormation: HabitFormationAnalysis
  productivityMetrics: ProductivityMetrics
  
  // 预测分析
  predictions: {
    nextWeek: BehaviorPrediction
    nextMonth: BehaviorPrediction
  }
  
  // 比较分析
  comparisons: {
    vsLastPeriod: number      // 与上期对比百分比
    vsPersonalBest: number    // 与个人最佳对比
    vsTarget: number          // 与目标对比
  }
  
  // 行为模式
  patterns: BehaviorPatternData
  
  // 作物分析
  cropInsights: CropAnalyticsData[]
  
  // 元数据
  metadata: {
    dataQuality: number       // 数据质量分数
    analysisConfidence: number // 分析可信度
    sampleSize: number        // 样本大小
    timeSpan: number          // 分析时间跨度（天）
  }
}

/**
 * 数据分析算法引擎
 * 提供多种分析算法和洞察生成功能
 */
export class DataAnalysisEngine {
  private collector: DataAnalyticsCollector
  
  // 算法参数
  private readonly TREND_SMOOTHING_FACTOR = 0.3
  private readonly CONFIDENCE_THRESHOLD = 0.7
  private readonly VOLATILITY_THRESHOLD = 0.2
  private readonly MIN_DATA_POINTS = 5
  
  constructor(collector: DataAnalyticsCollector) {
    this.collector = collector
  }

  // ===== 主要分析入口 =====

  /**
   * 生成综合分析报告
   */
  async generateComprehensiveAnalysis(
    userId: string, 
    analysisType: 'daily' | 'weekly' | 'monthly' | 'comprehensive' = 'comprehensive'
  ): Promise<ComprehensiveAnalysis> {
    
    // 收集基础数据
    const userData = await this.collectAnalysisData(userId, analysisType)
    
    // 执行各种分析
    const performanceInsights = await this.analyzePerformance(userData)
    const trendAnalysis = this.analyzeTrends(userData)
    const habitFormation = this.analyzeHabitFormation(userData)
    const productivityMetrics = this.analyzeProductivity(userData)
    const predictions = await this.generatePredictions(userData)
    const comparisons = await this.generateComparisons(userData, analysisType)
    const patterns = await this.collectBehaviorPatternData()
    const cropInsights = await this.collector.collectCropAnalyticsData()
    
    // 计算元数据
    const metadata = this.calculateAnalysisMetadata(userData)

    return {
      userId,
      generatedAt: Date.now(),
      analysisType,
      performanceInsights,
      trendAnalysis,
      habitFormation,
      productivityMetrics,
      predictions,
      comparisons,
      patterns,
      cropInsights,
      metadata
    }
  }

  /**
   * 分析用户表现洞察
   */
  async analyzePerformance(userData: any): Promise<PerformanceInsights> {
    const { dailyData, sessionData } = userData
    
    // 计算综合表现分数
    const overallScore = this.calculateOverallScore(dailyData, sessionData)
    
    // 识别优势和弱点
    const strengths = this.identifyStrengths(dailyData, sessionData)
    const weaknesses = this.identifyWeaknesses(dailyData, sessionData)
    
    // 生成建议
    const recommendations = this.generatePerformanceRecommendations(strengths, weaknesses, dailyData)
    
    // 计算关键指标
    const keyMetrics = {
      consistencyScore: this.calculateConsistencyScore(dailyData),
      improvementRate: this.calculateImprovementRate(dailyData),
      engagementLevel: this.calculateEngagementLevel(sessionData),
      efficiencyScore: this.calculateEfficiencyScore(sessionData)
    }

    return {
      overallScore,
      strengths,
      weaknesses,
      recommendations,
      keyMetrics
    }
  }

  /**
   * 分析数据趋势
   */
  analyzeTrends(userData: any): TrendAnalysis {
    const { dailyData } = userData
    
    if (dailyData.length < this.MIN_DATA_POINTS) {
      return {
        direction: 'stable',
        strength: 0,
        confidence: 0,
        changeRate: 0,
        significance: 'low'
      }
    }

    // 提取专注时间序列
    const focusTimeSeries = dailyData.map((day: DailyData) => day.totalFocusTime)
    const focusScoreSeries = dailyData.map((day: DailyData) => day.averageFocusScore)
    
    // 计算趋势
    const focusTrend = this.calculateLinearTrend(focusTimeSeries)
    const scoreTrend = this.calculateLinearTrend(focusScoreSeries)
    
    // 综合趋势分析
    const avgSlope = (focusTrend.slope + scoreTrend.slope) / 2
    const avgR2 = (focusTrend.r2 + scoreTrend.r2) / 2
    
    // 确定趋势方向
    const direction = this.determineTrendDirection(avgSlope, focusTimeSeries)
    
    // 计算趋势强度和置信度
    const strength = Math.min(100, Math.abs(avgSlope * 100))
    const confidence = Math.min(100, avgR2 * 100)
    
    // 计算变化率
    const changeRate = this.calculateChangeRate(focusTimeSeries)
    
    // 确定显著性
    const significance = this.determineTrendSignificance(strength, confidence, changeRate)

    return {
      direction,
      strength,
      confidence,
      changeRate,
      significance
    }
  }

  /**
   * 分析习惯形成情况
   */
  analyzeHabitFormation(userData: any): HabitFormationAnalysis {
    const { dailyData, sessionData } = userData
    
    // 计算习惯强度
    const habitStrength = this.calculateHabitStrength(dailyData)
    
    // 计算稳定性
    const stability = this.calculateHabitStability(dailyData)
    
    // 分析趋势
    const trend = this.analyzeTrends(userData)
    
    // 连续性分析
    const streakAnalysis = this.analyzeStreaks(dailyData)
    
    // 识别风险因素
    const riskFactors = this.identifyHabitRiskFactors(dailyData, sessionData)

    return {
      habitStrength,
      stability,
      trend,
      streakAnalysis,
      riskFactors
    }
  }

  /**
   * 分析生产力指标
   */
  analyzeProductivity(userData: any): ProductivityMetrics {
    const { dailyData, sessionData } = userData
    
    // 专注效率
    const focusEfficiency = this.calculateFocusEfficiency(sessionData)
    
    // 时间利用率
    const timeUtilization = this.calculateTimeUtilization(dailyData)
    
    // 任务完成率（基于作物收获）
    const taskCompletion = this.calculateTaskCompletionRate(dailyData)
    
    // 质量分数
    const qualityScore = this.calculateQualityScore(sessionData)
    
    // 识别高效时段
    const peakPerformanceTimes = this.identifyPeakPerformanceTimes(sessionData)
    
    // 能量模式分析
    const energyPattern = this.analyzeEnergyPattern(sessionData)

    return {
      focusEfficiency,
      timeUtilization,
      taskCompletion,
      qualityScore,
      peakPerformanceTimes,
      energyPattern
    }
  }

  /**
   * 生成行为预测
   */
  async generatePredictions(userData: any): Promise<{
    nextWeek: BehaviorPrediction
    nextMonth: BehaviorPrediction
  }> {
    const { dailyData, sessionData } = userData
    
    // 下周预测
    const nextWeek = await this.predictBehavior(dailyData, sessionData, 7)
    
    // 下月预测
    const nextMonth = await this.predictBehavior(dailyData, sessionData, 30)

    return {
      nextWeek,
      nextMonth
    }
  }

  // ===== 辅助计算方法 =====

  /**
   * 收集分析所需的数据
   */
  private async collectAnalysisData(userId: string, analysisType: string): Promise<any> {
    let dailyData: DailyData[] = []
    
    // 根据分析类型确定数据范围
    const today = new Date().toISOString().split('T')[0]
    let startDate: string
    
    switch (analysisType) {
      case 'daily':
        startDate = today
        dailyData = [await this.collector.collectDailyData(today)]
        break
      case 'weekly':
        const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
        startDate = weekAgo.toISOString().split('T')[0]
        dailyData = await this.collector.collectDailyDataRange(startDate, today)
        break
      case 'monthly':
        const monthAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
        startDate = monthAgo.toISOString().split('T')[0]
        dailyData = await this.collector.collectDailyDataRange(startDate, today)
        break
      default: // comprehensive
        const threeMonthsAgo = new Date(Date.now() - 90 * 24 * 60 * 60 * 1000)
        startDate = threeMonthsAgo.toISOString().split('T')[0]
        dailyData = await this.collector.collectDailyDataRange(startDate, today)
        break
    }
    
    // 提取所有会话数据
    const sessionData = dailyData.flatMap(day => day.sessionData)
    
    return {
      dailyData,
      sessionData,
      timeRange: { startDate, endDate: today }
    }
  }

  /**
   * 计算综合表现分数
   */
  private calculateOverallScore(dailyData: DailyData[], sessionData: SessionData[]): number {
    if (dailyData.length === 0) return 0
    
    // 多维度评分
    const consistencyScore = this.calculateConsistencyScore(dailyData)
    const averageFocusScore = dailyData.reduce((sum, day) => sum + day.averageFocusScore, 0) / dailyData.length
    const engagementScore = this.calculateEngagementLevel(sessionData)
    const growthScore = this.calculateGrowthScore(dailyData)
    
    // 加权平均
    const weights = { consistency: 0.3, focus: 0.3, engagement: 0.2, growth: 0.2 }
    
    return Math.round(
      consistencyScore * weights.consistency +
      averageFocusScore * weights.focus +
      engagementScore * weights.engagement +
      growthScore * weights.growth
    )
  }

  /**
   * 计算一致性分数
   */
  private calculateConsistencyScore(dailyData: DailyData[]): number {
    if (dailyData.length < 2) return 0
    
    const focusTimes = dailyData.map(day => day.totalFocusTime)
    const mean = focusTimes.reduce((a, b) => a + b, 0) / focusTimes.length
    const variance = focusTimes.reduce((sum, time) => sum + Math.pow(time - mean, 2), 0) / focusTimes.length
    const stdDev = Math.sqrt(variance)
    
    // 一致性分数：标准差越小，分数越高
    const consistency = Math.max(0, 100 - (stdDev / mean) * 100)
    return Math.min(100, consistency)
  }

  /**
   * 计算线性趋势
   */
  private calculateLinearTrend(data: number[]): { slope: number; r2: number } {
    if (data.length < 2) return { slope: 0, r2: 0 }
    
    const n = data.length
    const xSum = (n * (n - 1)) / 2  // 0 + 1 + 2 + ... + (n-1)
    const ySum = data.reduce((a, b) => a + b, 0)
    const xySum = data.reduce((sum, y, x) => sum + x * y, 0)
    const x2Sum = (n * (n - 1) * (2 * n - 1)) / 6  // 0² + 1² + 2² + ... + (n-1)²
    
    const slope = (n * xySum - xSum * ySum) / (n * x2Sum - xSum * xSum)
    
    // 计算R²
    const yMean = ySum / n
    const ssTotal = data.reduce((sum, y) => sum + Math.pow(y - yMean, 2), 0)
    const ssRes = data.reduce((sum, y, x) => {
      const predicted = slope * x + (ySum - slope * xSum) / n
      return sum + Math.pow(y - predicted, 2)
    }, 0)
    
    const r2 = ssTotal > 0 ? 1 - (ssRes / ssTotal) : 0
    
    return { slope, r2: Math.max(0, r2) }
  }

  /**
   * 确定趋势方向
   */
  private determineTrendDirection(slope: number, data: number[]): TrendAnalysis['direction'] {
    if (Math.abs(slope) < 0.01) return 'stable'
    
    // 检查波动性
    const volatility = this.calculateVolatility(data)
    if (volatility > this.VOLATILITY_THRESHOLD) return 'volatile'
    
    return slope > 0 ? 'increasing' : 'decreasing'
  }

  /**
   * 计算数据波动性
   */
  private calculateVolatility(data: number[]): number {
    if (data.length < 2) return 0
    
    const changes = []
    for (let i = 1; i < data.length; i++) {
      if (data[i - 1] !== 0) {
        changes.push((data[i] - data[i - 1]) / data[i - 1])
      }
    }
    
    if (changes.length === 0) return 0
    
    const meanChange = changes.reduce((a, b) => a + b, 0) / changes.length
    const variance = changes.reduce((sum, change) => sum + Math.pow(change - meanChange, 2), 0) / changes.length
    
    return Math.sqrt(variance)
  }

  /**
   * 计算变化率
   */
  private calculateChangeRate(data: number[]): number {
    if (data.length < 2) return 0
    
    const first = data[0]
    const last = data[data.length - 1]
    
    if (first === 0) return 0
    
    return ((last - first) / first) * 100
  }

  /**
   * 确定趋势显著性
   */
  private determineTrendSignificance(strength: number, confidence: number, changeRate: number): 'high' | 'medium' | 'low' {
    const significanceScore = (strength + confidence + Math.abs(changeRate)) / 3
    
    if (significanceScore > 60) return 'high'
    if (significanceScore > 30) return 'medium'
    return 'low'
  }

  // ===== 待实现的方法 =====

  private identifyStrengths(dailyData: DailyData[], sessionData: SessionData[]): string[] {
    const strengths: string[] = []
    
    // 专注时间一致性
    if (this.calculateConsistencyScore(dailyData) > 70) {
      strengths.push('专注时间保持稳定一致')
    }
    
    // 高专注分数
    const avgFocusScore = dailyData.reduce((sum, day) => sum + day.averageFocusScore, 0) / dailyData.length
    if (avgFocusScore > 75) {
      strengths.push('专注质量表现优秀')
    }
    
    // 作物收获效率
    const harvestRate = this.calculateHarvestEfficiency(dailyData)
    if (harvestRate > 80) {
      strengths.push('任务完成效率很高')
    }
    
    return strengths
  }

  private identifyWeaknesses(dailyData: DailyData[], sessionData: SessionData[]): string[] {
    const weaknesses: string[] = []
    
    // 专注时间不足
    const avgFocusTime = dailyData.reduce((sum, day) => sum + day.totalFocusTime, 0) / dailyData.length
    if (avgFocusTime < 30 * 60 * 1000) { // 少于30分钟
      weaknesses.push('每日专注时间偏少')
    }
    
    // 一致性差
    if (this.calculateConsistencyScore(dailyData) < 40) {
      weaknesses.push('专注时间不够稳定')
    }
    
    // 会话质量低
    const avgFocusScore = dailyData.reduce((sum, day) => sum + day.averageFocusScore, 0) / dailyData.length
    if (avgFocusScore < 60) {
      weaknesses.push('专注质量有待提升')
    }
    
    return weaknesses
  }

  private generatePerformanceRecommendations(strengths: string[], weaknesses: string[], dailyData: DailyData[]): string[] {
    const recommendations: string[] = []
    
    if (weaknesses.includes('每日专注时间偏少')) {
      recommendations.push('建议逐步增加每日专注时间，可从15分钟开始递增')
    }
    
    if (weaknesses.includes('专注时间不够稳定')) {
      recommendations.push('尝试固定每日专注时间段，建立稳定的习惯节奏')
    }
    
    if (weaknesses.includes('专注质量有待提升')) {
      recommendations.push('减少干扰因素，营造更好的专注环境')
    }
    
    // 基于优势给出发展建议
    if (strengths.includes('专注时间保持稳定一致')) {
      recommendations.push('保持当前良好的一致性，可尝试挑战更高的专注目标')
    }
    
    return recommendations
  }

  private calculateImprovementRate(dailyData: DailyData[]): number {
    if (dailyData.length < 7) return 0
    
    const firstWeek = dailyData.slice(0, 7)
    const lastWeek = dailyData.slice(-7)
    
    const firstWeekAvg = firstWeek.reduce((sum, day) => sum + day.averageFocusScore, 0) / 7
    const lastWeekAvg = lastWeek.reduce((sum, day) => sum + day.averageFocusScore, 0) / 7
    
    if (firstWeekAvg === 0) return 0
    
    return ((lastWeekAvg - firstWeekAvg) / firstWeekAvg) * 100
  }

  private calculateEngagementLevel(sessionData: SessionData[]): number {
    if (sessionData.length === 0) return 0
    
    const avgSessionLength = sessionData.reduce((sum, session) => sum + session.duration, 0) / sessionData.length
    const avgFocusScore = sessionData.reduce((sum, session) => sum + session.focusScore, 0) / sessionData.length
    
    // 结合会话时长和专注分数
    const lengthScore = Math.min(100, (avgSessionLength / (60 * 60 * 1000)) * 100) // 1小时为满分
    return (lengthScore + avgFocusScore) / 2
  }

  private calculateEfficiencyScore(sessionData: SessionData[]): number {
    if (sessionData.length === 0) return 0
    
    const totalTime = sessionData.reduce((sum, session) => sum + session.duration, 0)
    const totalAchievements = sessionData.reduce((sum, session) => sum + session.achievementsUnlocked, 0)
    const totalCrops = sessionData.reduce((sum, session) => sum + session.cropsHarvested, 0)
    
    if (totalTime === 0) return 0
    
    // 基于单位时间的产出计算效率
    const achievementRate = (totalAchievements / (totalTime / (60 * 60 * 1000))) * 10
    const cropRate = (totalCrops / (totalTime / (60 * 60 * 1000))) * 20
    
    return Math.min(100, achievementRate + cropRate)
  }

  private calculateGrowthScore(dailyData: DailyData[]): number {
    if (dailyData.length < 2) return 50
    
    const trend = this.calculateLinearTrend(dailyData.map(day => day.averageFocusScore))
    const growthScore = 50 + (trend.slope * 50) // 转换为0-100分数
    
    return Math.max(0, Math.min(100, growthScore))
  }

  private calculateHarvestEfficiency(dailyData: DailyData[]): number {
    const totalPlanted = dailyData.reduce((sum, day) => sum + day.cropsPlanted, 0)
    const totalHarvested = dailyData.reduce((sum, day) => sum + day.cropsHarvested, 0)
    
    if (totalPlanted === 0) return 0
    
    return (totalHarvested / totalPlanted) * 100
  }

  // ===== 习惯形成分析方法 =====

  /**
   * 计算习惯形成强度
   */
  private calculateHabitStrength(dailyData: DailyData[]): number {
    if (dailyData.length < 7) return 0
    
    // 基于连续性和一致性计算习惯强度
    const consistencyScore = this.calculateConsistencyScore(dailyData)
    const streakData = this.analyzeStreaks(dailyData)
    
    // 习惯强度 = 一致性 * 连续性权重
    const streakWeight = Math.min(1, streakData.currentStreak / 21) // 21天养成习惯理论
    const habitStrength = consistencyScore * 0.6 + streakWeight * 40
    
    return Math.min(100, habitStrength)
  }

  /**
   * 计算习惯稳定性
   */
  private calculateHabitStability(dailyData: DailyData[]): number {
    if (dailyData.length < 14) return 0
    
    // 分析最近两周的稳定性
    const recentTwo = dailyData.slice(-14)
    const firstWeek = recentTwo.slice(0, 7)
    const secondWeek = recentTwo.slice(7, 14)
    
    const firstWeekAvg = firstWeek.reduce((sum, day) => sum + day.totalFocusTime, 0) / 7
    const secondWeekAvg = secondWeek.reduce((sum, day) => sum + day.totalFocusTime, 0) / 7
    
    // 稳定性基于两周间差异的倒数
    const difference = Math.abs(firstWeekAvg - secondWeekAvg) / Math.max(firstWeekAvg, secondWeekAvg)
    const stability = Math.max(0, 100 - difference * 100)
    
    return Math.min(100, stability)
  }

  /**
   * 分析连续性数据
   */
  private analyzeStreaks(dailyData: DailyData[]): {
    currentStreak: number
    longestStreak: number
    averageStreak: number
    streakReliability: number
  } {
    if (dailyData.length === 0) {
      return { currentStreak: 0, longestStreak: 0, averageStreak: 0, streakReliability: 0 }
    }

    let currentStreak = 0
    let longestStreak = 0
    let streaks: number[] = []
    let tempStreak = 0

    // 定义"有效专注日"的标准（至少15分钟专注时间）
    const MIN_FOCUS_TIME = 15 * 60 * 1000

    // 计算连续性
    for (let i = dailyData.length - 1; i >= 0; i--) {
      const day = dailyData[i]
      
      if (day.totalFocusTime >= MIN_FOCUS_TIME) {
        tempStreak++
        if (i === dailyData.length - 1) {
          currentStreak = tempStreak
        }
      } else {
        if (tempStreak > 0) {
          streaks.push(tempStreak)
          longestStreak = Math.max(longestStreak, tempStreak)
        }
        tempStreak = 0
      }
    }

    // 处理最后一个连续段
    if (tempStreak > 0) {
      streaks.push(tempStreak)
      longestStreak = Math.max(longestStreak, tempStreak)
    }

    const averageStreak = streaks.length > 0 ? streaks.reduce((a, b) => a + b, 0) / streaks.length : 0
    const streakReliability = streaks.length > 0 ? (streaks.filter(s => s >= 3).length / streaks.length) * 100 : 0

    return {
      currentStreak,
      longestStreak,
      averageStreak,
      streakReliability
    }
  }

  /**
   * 识别习惯风险因素
   */
  private identifyHabitRiskFactors(dailyData: DailyData[], sessionData: SessionData[]): string[] {
    const riskFactors: string[] = []
    
    // 检查一致性
    if (this.calculateConsistencyScore(dailyData) < 50) {
      riskFactors.push('专注时间不稳定，缺乏固定节奏')
    }
    
    // 检查连续性
    const streaks = this.analyzeStreaks(dailyData)
    if (streaks.currentStreak === 0) {
      riskFactors.push('当前连续专注天数为零，习惯中断')
    }
    
    // 检查专注质量下降
    const recentFocusScores = dailyData.slice(-7).map(day => day.averageFocusScore)
    const trend = this.calculateLinearTrend(recentFocusScores)
    if (trend.slope < -0.1) {
      riskFactors.push('近期专注质量呈下降趋势')
    }
    
    // 检查会话时长
    const avgSessionLength = sessionData.length > 0 
      ? sessionData.reduce((sum, session) => sum + session.duration, 0) / sessionData.length 
      : 0
    if (avgSessionLength < 10 * 60 * 1000) { // 少于10分钟
      riskFactors.push('平均专注会话时长过短')
    }
    
    return riskFactors
  }

  // ===== 生产力分析方法 =====

  /**
   * 计算专注效率
   */
  private calculateFocusEfficiency(sessionData: SessionData[]): number {
    if (sessionData.length === 0) return 0
    
    // 效率 = 平均专注分数 * 时长权重
    const avgFocusScore = sessionData.reduce((sum, session) => sum + session.focusScore, 0) / sessionData.length
    const avgDuration = sessionData.reduce((sum, session) => sum + session.duration, 0) / sessionData.length
    
    // 时长权重：30-60分钟为最佳效率区间
    const optimalDuration = 45 * 60 * 1000 // 45分钟
    const durationWeight = Math.max(0.5, 1 - Math.abs(avgDuration - optimalDuration) / optimalDuration)
    
    return Math.min(100, avgFocusScore * durationWeight)
  }

  /**
   * 计算时间利用率
   */
  private calculateTimeUtilization(dailyData: DailyData[]): number {
    if (dailyData.length === 0) return 0
    
    const avgFocusTime = dailyData.reduce((sum, day) => sum + day.totalFocusTime, 0) / dailyData.length
    const targetFocusTime = 2 * 60 * 60 * 1000 // 目标2小时
    
    return Math.min(100, (avgFocusTime / targetFocusTime) * 100)
  }

  /**
   * 计算任务完成率
   */
  private calculateTaskCompletionRate(dailyData: DailyData[]): number {
    if (dailyData.length === 0) return 0
    
    const totalPlanted = dailyData.reduce((sum, day) => sum + day.cropsPlanted, 0)
    const totalHarvested = dailyData.reduce((sum, day) => sum + day.cropsHarvested, 0)
    
    if (totalPlanted === 0) return 0
    
    return (totalHarvested / totalPlanted) * 100
  }

  /**
   * 计算质量分数
   */
  private calculateQualityScore(sessionData: SessionData[]): number {
    if (sessionData.length === 0) return 0
    
    // 质量基于专注分数和完成的成就
    const avgFocusScore = sessionData.reduce((sum, session) => sum + session.focusScore, 0) / sessionData.length
    const avgAchievements = sessionData.reduce((sum, session) => sum + session.achievementsUnlocked, 0) / sessionData.length
    
    // 结合专注分数和成就密度
    const achievementBonus = Math.min(20, avgAchievements * 5) // 最多加20分
    
    return Math.min(100, avgFocusScore + achievementBonus)
  }

  /**
   * 识别高效时段
   */
  private identifyPeakPerformanceTimes(sessionData: SessionData[]): string[] {
    if (sessionData.length === 0) return []
    
    // 按小时分组分析表现
    const hourlyPerformance: { [hour: number]: { totalScore: number; count: number } } = {}
    
    sessionData.forEach(session => {
      const hour = new Date(session.startTime).getHours()
      
      if (!hourlyPerformance[hour]) {
        hourlyPerformance[hour] = { totalScore: 0, count: 0 }
      }
      
      hourlyPerformance[hour].totalScore += session.focusScore
      hourlyPerformance[hour].count += 1
    })
    
    // 计算每小时平均分数
    const hourlyAverages = Object.entries(hourlyPerformance).map(([hour, data]) => ({
      hour: parseInt(hour),
      avgScore: data.totalScore / data.count
    }))
    
    // 找出高于平均水平的时段
    const overallAvg = hourlyAverages.reduce((sum, h) => sum + h.avgScore, 0) / hourlyAverages.length
    const peakHours = hourlyAverages
      .filter(h => h.avgScore > overallAvg + 10) // 高于平均10分以上
      .sort((a, b) => b.avgScore - a.avgScore)
      .slice(0, 3) // 取前3个
      .map(h => `${h.hour}:00-${h.hour + 1}:00`)
    
    return peakHours
  }

  /**
   * 分析能量模式
   */
  private analyzeEnergyPattern(sessionData: SessionData[]): {
    morningEnergy: number
    afternoonEnergy: number
    eveningEnergy: number
    optimalWorkWindow: string
  } {
    if (sessionData.length === 0) {
      return {
        morningEnergy: 0,
        afternoonEnergy: 0,
        eveningEnergy: 0,
        optimalWorkWindow: '未知'
      }
    }
    
    // 按时段分类
    const morningData = sessionData.filter(s => new Date(s.startTime).getHours() < 12)
    const afternoonData = sessionData.filter(s => {
      const hour = new Date(s.startTime).getHours()
      return hour >= 12 && hour < 18
    })
    const eveningData = sessionData.filter(s => new Date(s.startTime).getHours() >= 18)
    
    // 计算各时段平均能量水平
    const morningEnergy = morningData.length > 0 
      ? morningData.reduce((sum, s) => sum + s.focusScore, 0) / morningData.length 
      : 0
    const afternoonEnergy = afternoonData.length > 0 
      ? afternoonData.reduce((sum, s) => sum + s.focusScore, 0) / afternoonData.length 
      : 0
    const eveningEnergy = eveningData.length > 0 
      ? eveningData.reduce((sum, s) => sum + s.focusScore, 0) / eveningData.length 
      : 0
    
    // 确定最佳工作时段
    const energyLevels = [
      { period: '上午', energy: morningEnergy },
      { period: '下午', energy: afternoonEnergy },
      { period: '晚上', energy: eveningEnergy }
    ]
    
    const optimalPeriod = energyLevels.reduce((max, current) => 
      current.energy > max.energy ? current : max
    )
    
    return {
      morningEnergy,
      afternoonEnergy,
      eveningEnergy,
      optimalWorkWindow: optimalPeriod.period
    }
  }

  // ===== 预测分析方法 =====

  /**
   * 预测行为表现
   */
  private async predictBehavior(dailyData: DailyData[], sessionData: SessionData[], days: number): Promise<BehaviorPrediction> {
    if (dailyData.length < 7) {
      return {
        predictedScore: 0,
        confidence: 0,
        timeHorizon: days,
        factors: [],
        recommendations: ['需要更多历史数据才能进行准确预测']
      }
    }
    
    // 基于历史趋势预测
    const focusScores = dailyData.map(day => day.averageFocusScore)
    const trend = this.calculateLinearTrend(focusScores)
    
    // 预测分数 = 当前平均分 + 趋势斜率 * 预测天数
    const currentAvg = focusScores.slice(-7).reduce((a, b) => a + b, 0) / 7
    const predictedScore = Math.max(0, Math.min(100, currentAvg + trend.slope * days))
    
    // 置信度基于R²值和数据量
    const dataQuality = Math.min(1, dailyData.length / 30) // 30天为满分
    const confidence = Math.min(100, trend.r2 * 100 * dataQuality)
    
    // 影响因素分析
    const factors = [
      {
        name: '历史趋势',
        impact: trend.slope * 50,
        confidence: trend.r2 * 100
      },
      {
        name: '一致性水平',
        impact: (this.calculateConsistencyScore(dailyData) - 50) * 0.6,
        confidence: 80
      },
      {
        name: '连续性',
        impact: (this.analyzeStreaks(dailyData).currentStreak - 7) * 2,
        confidence: 90
      }
    ]
    
    // 生成建议
    const recommendations = this.generatePredictionRecommendations(predictedScore, factors)
    
    return {
      predictedScore,
      confidence,
      timeHorizon: days,
      factors,
      recommendations
    }
  }

  /**
   * 生成预测建议
   */
  private generatePredictionRecommendations(predictedScore: number, factors: any[]): string[] {
    const recommendations: string[] = []
    
    if (predictedScore < 60) {
      recommendations.push('预测表现偏低，建议加强专注训练')
    }
    
    const trendFactor = factors.find(f => f.name === '历史趋势')
    if (trendFactor && trendFactor.impact < 0) {
      recommendations.push('当前呈下降趋势，需要调整专注策略')
    }
    
    const consistencyFactor = factors.find(f => f.name === '一致性水平')
    if (consistencyFactor && consistencyFactor.impact < 0) {
      recommendations.push('提高专注时间的一致性，建立固定作息')
    }
    
    return recommendations
  }

  // ===== 比较分析方法 =====

  /**
   * 生成比较分析
   */
  private async generateComparisons(userData: any, analysisType: string): Promise<{
    vsLastPeriod: number
    vsPersonalBest: number
    vsTarget: number
  }> {
    const { dailyData } = userData
    
    if (dailyData.length === 0) {
      return { vsLastPeriod: 0, vsPersonalBest: 0, vsTarget: 0 }
    }
    
    // 计算当前期间平均分
    const currentPeriodScore = dailyData.reduce((sum: number, day: DailyData) => sum + day.averageFocusScore, 0) / dailyData.length
    
    // 与上期对比（简化实现）
    const vsLastPeriod = this.calculatePeriodComparison(dailyData, analysisType)
    
    // 与个人最佳对比
    const personalBest = Math.max(...dailyData.map((day: DailyData) => day.averageFocusScore))
    const vsPersonalBest = personalBest > 0 ? (currentPeriodScore / personalBest) * 100 - 100 : 0
    
    // 与目标对比（假设目标为80分）
    const target = 80
    const vsTarget = (currentPeriodScore / target) * 100 - 100
    
    return {
      vsLastPeriod,
      vsPersonalBest,
      vsTarget
    }
  }

  /**
   * 计算与上期的对比
   */
  private calculatePeriodComparison(dailyData: DailyData[], analysisType: string): number {
    const periodLength = analysisType === 'weekly' ? 7 : analysisType === 'monthly' ? 30 : 7
    
    if (dailyData.length < periodLength * 2) return 0
    
    const currentPeriod = dailyData.slice(-periodLength)
    const lastPeriod = dailyData.slice(-periodLength * 2, -periodLength)
    
    const currentAvg = currentPeriod.reduce((sum, day) => sum + day.averageFocusScore, 0) / periodLength
    const lastAvg = lastPeriod.reduce((sum, day) => sum + day.averageFocusScore, 0) / periodLength
    
    if (lastAvg === 0) return 0
    
    return ((currentAvg - lastAvg) / lastAvg) * 100
  }

  // ===== 元数据计算方法 =====

  /**
   * 计算分析元数据
   */
  private calculateAnalysisMetadata(userData: any): {
    dataQuality: number
    analysisConfidence: number
    sampleSize: number
    timeSpan: number
  } {
    const { dailyData, sessionData } = userData
    
    // 数据质量基于完整性和一致性
    const completeness = dailyData.length > 0 ? 
      dailyData.filter((day: DailyData) => day.totalSessions > 0).length / dailyData.length : 0
    const dataQuality = Math.min(100, completeness * 100)
    
    // 分析置信度基于样本大小和数据质量
    const sampleSizeScore = Math.min(1, dailyData.length / 30) // 30天为理想样本
    const analysisConfidence = Math.min(100, (dataQuality * 0.6 + sampleSizeScore * 40))
    
    return {
      dataQuality,
      analysisConfidence,
      sampleSize: dailyData.length,
      timeSpan: dailyData.length
    }
  }

  /**
   * 收集行为模式数据
   */
  private async collectBehaviorPatternData(): Promise<BehaviorPatternData> {
    // 模拟行为模式数据收集
    return {
      mostActiveHours: [9, 10, 14, 15, 16, 20, 21],
      mostActiveDays: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'],
      sessionLengthDistribution: {
        '0-15min': 20,
        '15-30min': 35,
        '30-60min': 30,
        '60+min': 15
      },
      focusScoreDistribution: {
        '0-20': 5,
        '21-40': 10,
        '41-60': 25,
        '61-80': 35,
        '81-100': 25
      },
      distractionPatterns: {
        commonTimes: ['13:00-14:00', '17:00-18:00'],
        averageRecoveryTime: 45000, // 45秒
        triggerFactors: ['通知', '噪音', '疲劳']
      }
    }
  }
}

export default DataAnalysisEngine 