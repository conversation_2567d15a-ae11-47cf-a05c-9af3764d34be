// 趋势分析和预测引擎
// 基于历史数据进行时间序列分析和行为预测

import DataAnalyticsCollector, { DailyData } from './DataAnalyticsCollector'
import DataAnalysisEngine, { TrendAnalysis, BehaviorPrediction } from './DataAnalysisEngine'

// 趋势分析结果
export interface TrendAnalysisResult {
  // 基本趋势信息
  trend: TrendAnalysis
  confidence: number
  dataQuality: number
  
  // 预测结果
  predictions: {
    shortTerm: BehaviorPrediction[]  // 未来7天
    mediumTerm: BehaviorPrediction[] // 未来30天
    longTerm: BehaviorPrediction[]   // 未来90天
  }
  
  // 关键指标预测
  keyMetricsPrediction: {
    focusTime: MetricPrediction
    habitStrength: MetricPrediction
    consistency: MetricPrediction
    productivity: MetricPrediction
  }
  
  // 风险分析
  risks: RiskAssessment[]
  
  // 机会识别
  opportunities: OpportunityAnalysis[]
  
  // 建议的行动计划
  recommendations: TrendRecommendation[]
}

// 指标预测
export interface MetricPrediction {
  metric: string
  currentValue: number
  predictions: {
    day7: number
    day30: number
    day90: number
  }
  confidence: {
    day7: number
    day30: number
    day90: number
  }
  trend: 'increasing' | 'decreasing' | 'stable' | 'volatile'
  expectedChange: number // 百分比变化
}

// 风险评估
export interface RiskAssessment {
  type: 'habit_decline' | 'consistency_drop' | 'motivation_loss' | 'burnout_risk' | 'plateau_effect'
  severity: 'low' | 'medium' | 'high' | 'critical'
  probability: number // 0-1
  timeframe: number // 天数
  description: string
  impact: string
  mitigation: string[]
}

// 机会分析
export interface OpportunityAnalysis {
  type: 'habit_growth' | 'consistency_boost' | 'new_milestone' | 'efficiency_gain' | 'motivation_peak'
  potential: 'low' | 'medium' | 'high'
  probability: number
  timeframe: number
  description: string
  expectedBenefit: string
  actionPlan: string[]
}

// 趋势建议
export interface TrendRecommendation {
  category: 'immediate' | 'short_term' | 'long_term'
  priority: 'low' | 'medium' | 'high' | 'critical'
  title: string
  description: string
  reasoning: string
  expectedOutcome: string
  timeToSeeResults: number // 天数
  difficulty: 'easy' | 'medium' | 'hard'
  resources: string[]
}

// 时间序列数据点
export interface TimeSeriesDataPoint {
  timestamp: number
  value: number
  metadata?: any
}

// 季节性模式
export interface SeasonalPattern {
  pattern: 'daily' | 'weekly' | 'monthly'
  strength: number
  peaks: number[]
  valleys: number[]
  description: string
}

// 趋势分析引擎类
export class TrendAnalysisEngine {
  private dataCollector: DataAnalyticsCollector
  private analysisEngine: DataAnalysisEngine

  constructor(
    dataCollector: DataAnalyticsCollector,
    analysisEngine: DataAnalysisEngine
  ) {
    this.dataCollector = dataCollector
    this.analysisEngine = analysisEngine
  }

  // 主要的趋势分析方法
  public async generateTrendAnalysis(userId: string, analysisWindow: number = 90): Promise<TrendAnalysisResult> {
    try {
      console.log(`开始为用户 ${userId} 生成趋势分析，分析窗口：${analysisWindow}天`)

      // 获取历史数据
      const historicalData = await this.getHistoricalData(userId, analysisWindow)
      
      // 数据质量评估
      const dataQuality = this.assessDataQuality(historicalData)
      
      // 基础趋势分析
      const baseTrend = await this.performBasicTrendAnalysis(historicalData)
      
      // 生成预测
      const predictions = await this.generatePredictions(historicalData)
      
      // 关键指标预测
      const keyMetricsPrediction = await this.predictKeyMetrics(historicalData)
      
      // 风险分析
      const risks = this.analyzeRisks(historicalData, predictions)
      
      // 机会识别
      const opportunities = this.identifyOpportunities(historicalData, predictions)
      
      // 生成建议
      const recommendations = this.generateRecommendations(baseTrend, risks, opportunities)

      return {
        trend: baseTrend,
        confidence: this.calculateOverallConfidence(dataQuality, baseTrend),
        dataQuality,
        predictions,
        keyMetricsPrediction,
        risks,
        opportunities,
        recommendations
      }
    } catch (error) {
      console.error('趋势分析生成失败:', error)
      throw new Error(`趋势分析失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  // 获取历史数据
  private async getHistoricalData(userId: string, days: number): Promise<DailyData[]> {
    const endDate = new Date()
    const startDate = new Date(endDate.getTime() - days * 24 * 60 * 60 * 1000)
    
    // 模拟获取历史数据
    const historicalData: DailyData[] = []
    
    for (let i = 0; i < days; i++) {
      const date = new Date(startDate.getTime() + i * 24 * 60 * 60 * 1000)
      
      // 生成模拟数据，带有趋势和季节性
      const dayOfWeek = date.getDay()
      const weekendFactor = (dayOfWeek === 0 || dayOfWeek === 6) ? 0.7 : 1.0
      const trendFactor = 1 + (i / days) * 0.3 // 30%的正增长趋势
      const randomFactor = 0.8 + Math.random() * 0.4 // 80%-120%的随机波动
      
      const baseProductivity = 75 * weekendFactor * trendFactor * randomFactor
      
      historicalData.push({
        date: date.toISOString().split('T')[0],
        sessionsCount: Math.floor(3 + Math.random() * 4),
        totalFocusTime: Math.floor(120 + Math.random() * 120),
        averageSessionLength: Math.floor(25 + Math.random() * 20),
        distractionCount: Math.floor(Math.random() * 15),
        productivityScore: Math.min(100, Math.floor(baseProductivity)),
        energyLevel: Math.floor(60 + Math.random() * 40),
        moodRating: Math.floor(6 + Math.random() * 4),
        sleepHours: 6.5 + Math.random() * 2,
        exerciseMinutes: Math.floor(Math.random() * 90),
        weatherCondition: 'sunny',
        completedHabits: Math.floor(3 + Math.random() * 4),
        streakCount: Math.max(1, i - Math.floor(Math.random() * 5)),
        notes: ''
      })
    }
    
    return historicalData
  }

  // 数据质量评估
  private assessDataQuality(data: DailyData[]): number {
    if (data.length === 0) return 0
    
    const factors = {
      completeness: this.assessCompleteness(data),
      consistency: this.assessConsistency(data),
      recency: this.assessRecency(data),
      coverage: this.assessCoverage(data)
    }
    
    // 加权平均
    return (
      factors.completeness * 0.3 +
      factors.consistency * 0.25 +
      factors.recency * 0.25 +
      factors.coverage * 0.2
    )
  }

  // 完整性评估
  private assessCompleteness(data: DailyData[]): number {
    const totalFields = 12 // DailyData的字段数
    let totalScore = 0
    
    data.forEach(day => {
      let filledFields = 0
      if (day.sessionsCount > 0) filledFields++
      if (day.totalFocusTime > 0) filledFields++
      if (day.averageSessionLength > 0) filledFields++
      if (day.productivityScore > 0) filledFields++
      if (day.energyLevel > 0) filledFields++
      if (day.moodRating > 0) filledFields++
      if (day.sleepHours > 0) filledFields++
      if (day.completedHabits >= 0) filledFields++
      if (day.streakCount >= 0) filledFields++
      if (day.exerciseMinutes >= 0) filledFields++
      if (day.distractionCount >= 0) filledFields++
      if (day.weatherCondition) filledFields++
      
      totalScore += filledFields / totalFields
    })
    
    return totalScore / data.length
  }

  // 一致性评估
  private assessConsistency(data: DailyData[]): number {
    if (data.length < 2) return 1
    
    const metrics = ['productivityScore', 'energyLevel', 'moodRating']
    let consistencyScore = 0
    
    metrics.forEach(metric => {
      const values = data.map(d => (d as any)[metric]).filter(v => v != null)
      if (values.length < 2) return
      
      const mean = values.reduce((sum, val) => sum + val, 0) / values.length
      const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length
      const coefficient = Math.sqrt(variance) / mean
      
      // 变异系数越小，一致性越高
      consistencyScore += Math.max(0, 1 - coefficient)
    })
    
    return consistencyScore / metrics.length
  }

  // 新近性评估
  private assessRecency(data: DailyData[]): number {
    if (data.length === 0) return 0
    
    const today = new Date()
    const lastDataDate = new Date(data[data.length - 1].date)
    const daysSinceLastData = Math.floor((today.getTime() - lastDataDate.getTime()) / (24 * 60 * 60 * 1000))
    
    // 数据越新，分数越高
    return Math.max(0, 1 - daysSinceLastData / 7) // 7天后开始衰减
  }

  // 覆盖率评估
  private assessCoverage(data: DailyData[]): number {
    const targetDays = 90 // 期望的数据天数
    return Math.min(1, data.length / targetDays)
  }

  // 基础趋势分析
  private async performBasicTrendAnalysis(data: DailyData[]): Promise<TrendAnalysis> {
    const timeSeries = this.convertToTimeSeries(data, 'productivityScore')
    
    // 线性趋势计算
    const trend = this.calculateLinearTrend(timeSeries)
    
    // 季节性检测
    const seasonality = this.detectSeasonality(timeSeries)
    
    return {
      direction: trend.slope > 0.1 ? 'increasing' : trend.slope < -0.1 ? 'decreasing' : 'stable',
      strength: Math.min(1, Math.abs(trend.slope) / 10),
      confidence: trend.rSquared,
      changeRate: trend.slope,
      significance: trend.pValue < 0.05,
      seasonalPatterns: seasonality
    }
  }

  // 转换为时间序列
  private convertToTimeSeries(data: DailyData[], metric: keyof DailyData): TimeSeriesDataPoint[] {
    return data.map(day => ({
      timestamp: new Date(day.date).getTime(),
      value: (day as any)[metric] || 0
    }))
  }

  // 计算线性趋势
  private calculateLinearTrend(timeSeries: TimeSeriesDataPoint[]): { slope: number; intercept: number; rSquared: number; pValue: number } {
    if (timeSeries.length < 2) {
      return { slope: 0, intercept: 0, rSquared: 0, pValue: 1 }
    }

    const n = timeSeries.length
    const x = timeSeries.map((_, i) => i)
    const y = timeSeries.map(point => point.value)
    
    // 计算线性回归
    const sumX = x.reduce((sum, val) => sum + val, 0)
    const sumY = y.reduce((sum, val) => sum + val, 0)
    const sumXY = x.reduce((sum, val, i) => sum + val * y[i], 0)
    const sumXX = x.reduce((sum, val) => sum + val * val, 0)
    const sumYY = y.reduce((sum, val) => sum + val * val, 0)
    
    const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX)
    const intercept = (sumY - slope * sumX) / n
    
    // 计算R²
    const yMean = sumY / n
    const totalSumSquares = y.reduce((sum, val) => sum + Math.pow(val - yMean, 2), 0)
    const residualSumSquares = y.reduce((sum, val, i) => {
      const predicted = slope * x[i] + intercept
      return sum + Math.pow(val - predicted, 2)
    }, 0)
    
    const rSquared = totalSumSquares > 0 ? 1 - (residualSumSquares / totalSumSquares) : 0
    
    // 简化的p值计算
    const pValue = rSquared > 0.5 ? 0.01 : rSquared > 0.3 ? 0.05 : 0.1
    
    return { slope, intercept, rSquared, pValue }
  }

  // 检测季节性
  private detectSeasonality(timeSeries: TimeSeriesDataPoint[]): SeasonalPattern[] {
    const patterns: SeasonalPattern[] = []
    
    // 检测周模式
    const weeklyPattern = this.detectWeeklyPattern(timeSeries)
    if (weeklyPattern.strength > 0.3) {
      patterns.push(weeklyPattern)
    }
    
    return patterns
  }

  // 检测周模式
  private detectWeeklyPattern(timeSeries: TimeSeriesDataPoint[]): SeasonalPattern {
    const weeklyData: number[][] = Array.from({ length: 7 }, () => [])
    
    timeSeries.forEach(point => {
      const date = new Date(point.timestamp)
      const dayOfWeek = date.getDay()
      weeklyData[dayOfWeek].push(point.value)
    })
    
    const weeklyAverages = weeklyData.map(dayData => 
      dayData.length > 0 ? dayData.reduce((sum, val) => sum + val, 0) / dayData.length : 0
    )
    
    // 计算模式强度
    const overall = weeklyAverages.reduce((sum, avg) => sum + avg, 0) / 7
    const variance = weeklyAverages.reduce((sum, avg) => sum + Math.pow(avg - overall, 2), 0) / 7
    const strength = Math.sqrt(variance) / overall
    
    // 找出峰值和谷值
    const peaks: number[] = []
    const valleys: number[] = []
    
    weeklyAverages.forEach((avg, day) => {
      if (avg > overall * 1.1) peaks.push(day)
      if (avg < overall * 0.9) valleys.push(day)
    })
    
    return {
      pattern: 'weekly',
      strength: Math.min(1, strength),
      peaks,
      valleys,
      description: `周模式强度: ${(strength * 100).toFixed(1)}%`
    }
  }

  // 生成预测
  private async generatePredictions(data: DailyData[]): Promise<{
    shortTerm: BehaviorPrediction[]
    mediumTerm: BehaviorPrediction[]
    longTerm: BehaviorPrediction[]
  }> {
    return {
      shortTerm: await this.generateShortTermPredictions(data),
      mediumTerm: await this.generateMediumTermPredictions(data),
      longTerm: await this.generateLongTermPredictions(data)
    }
  }

  // 短期预测（7天）
  private async generateShortTermPredictions(data: DailyData[]): Promise<BehaviorPrediction[]> {
    return [
      {
        metric: 'focusTime',
        currentValue: data[data.length - 1]?.totalFocusTime || 0,
        predictedValue: this.predictFocusTime(data, 7),
        confidence: 0.85,
        timeframe: 7,
        factors: ['recent_performance', 'weekly_pattern', 'habit_strength'],
        reasoning: '基于近期表现和周模式预测'
      },
      {
        metric: 'productivity',
        currentValue: data[data.length - 1]?.productivityScore || 0,
        predictedValue: this.predictProductivity(data, 7),
        confidence: 0.80,
        timeframe: 7,
        factors: ['productivity_trend', 'energy_levels', 'consistency'],
        reasoning: '基于生产力趋势和能量水平预测'
      }
    ]
  }

  // 中期预测（30天）
  private async generateMediumTermPredictions(data: DailyData[]): Promise<BehaviorPrediction[]> {
    return [
      {
        metric: 'habitStrength',
        currentValue: this.calculateCurrentHabitStrength(data),
        predictedValue: this.predictHabitStrength(data, 30),
        confidence: 0.70,
        timeframe: 30,
        factors: ['habit_consistency', 'motivation_level', 'external_factors'],
        reasoning: '基于习惯一致性和动机水平预测'
      }
    ]
  }

  // 长期预测（90天）
  private async generateLongTermPredictions(data: DailyData[]): Promise<BehaviorPrediction[]> {
    return [
      {
        metric: 'overallGrowth',
        currentValue: this.calculateOverallScore(data),
        predictedValue: this.predictOverallGrowth(data, 90),
        confidence: 0.60,
        timeframe: 90,
        factors: ['long_term_trends', 'habit_formation', 'lifestyle_factors'],
        reasoning: '基于长期趋势和生活方式因素预测'
      }
    ]
  }

  // 预测关键指标
  private async predictKeyMetrics(data: DailyData[]): Promise<TrendAnalysisResult['keyMetricsPrediction']> {
    const currentValues = this.getCurrentMetricValues(data)
    
    return {
      focusTime: {
        metric: 'focusTime',
        currentValue: currentValues.focusTime,
        predictions: {
          day7: this.predictFocusTime(data, 7),
          day30: this.predictFocusTime(data, 30),
          day90: this.predictFocusTime(data, 90)
        },
        confidence: {
          day7: 0.85,
          day30: 0.70,
          day90: 0.55
        },
        trend: this.determineTrend(data, 'totalFocusTime'),
        expectedChange: this.calculateExpectedChange(data, 'totalFocusTime')
      },
      habitStrength: {
        metric: 'habitStrength',
        currentValue: currentValues.habitStrength,
        predictions: {
          day7: this.predictHabitStrength(data, 7),
          day30: this.predictHabitStrength(data, 30),
          day90: this.predictHabitStrength(data, 90)
        },
        confidence: {
          day7: 0.80,
          day30: 0.65,
          day90: 0.50
        },
        trend: this.determineTrend(data, 'completedHabits'),
        expectedChange: this.calculateExpectedChange(data, 'completedHabits')
      },
      consistency: {
        metric: 'consistency',
        currentValue: currentValues.consistency,
        predictions: {
          day7: this.predictConsistency(data, 7),
          day30: this.predictConsistency(data, 30),
          day90: this.predictConsistency(data, 90)
        },
        confidence: {
          day7: 0.75,
          day30: 0.60,
          day90: 0.45
        },
        trend: this.determineConsistencyTrend(data),
        expectedChange: this.calculateConsistencyChange(data)
      },
      productivity: {
        metric: 'productivity',
        currentValue: currentValues.productivity,
        predictions: {
          day7: this.predictProductivity(data, 7),
          day30: this.predictProductivity(data, 30),
          day90: this.predictProductivity(data, 90)
        },
        confidence: {
          day7: 0.80,
          day30: 0.65,
          day90: 0.50
        },
        trend: this.determineTrend(data, 'productivityScore'),
        expectedChange: this.calculateExpectedChange(data, 'productivityScore')
      }
    }
  }

  // 风险分析
  private analyzeRisks(data: DailyData[], predictions: any): RiskAssessment[] {
    const risks: RiskAssessment[] = []
    
    // 习惯衰退风险
    if (this.detectHabitDeclineRisk(data)) {
      risks.push({
        type: 'habit_decline',
        severity: 'medium',
        probability: 0.35,
        timeframe: 14,
        description: '近期习惯完成率有下降趋势',
        impact: '可能导致长期习惯养成失败',
        mitigation: ['增加提醒频率', '简化习惯难度', '寻找额外动机']
      })
    }
    
    // 一致性下降风险
    if (this.detectConsistencyRisk(data)) {
      risks.push({
        type: 'consistency_drop',
        severity: 'high',
        probability: 0.45,
        timeframe: 21,
        description: '行为一致性显著波动',
        impact: '影响长期目标达成',
        mitigation: ['建立固定时间表', '创建环境提示', '制定应急计划']
      })
    }
    
    return risks
  }

  // 机会识别
  private identifyOpportunities(data: DailyData[], predictions: any): OpportunityAnalysis[] {
    const opportunities: OpportunityAnalysis[] = []
    
    // 习惯增长机会
    if (this.detectGrowthOpportunity(data)) {
      opportunities.push({
        type: 'habit_growth',
        potential: 'high',
        probability: 0.70,
        timeframe: 30,
        description: '当前习惯表现良好，有扩展空间',
        expectedBenefit: '可以增加新的习惯或提高现有习惯难度',
        actionPlan: ['确定下一个习惯目标', '逐步增加挑战', '庆祝小胜利']
      })
    }
    
    return opportunities
  }

  // 生成建议
  private generateRecommendations(trend: TrendAnalysis, risks: RiskAssessment[], opportunities: OpportunityAnalysis[]): TrendRecommendation[] {
    const recommendations: TrendRecommendation[] = []
    
    // 基于趋势的建议
    if (trend.direction === 'decreasing') {
      recommendations.push({
        category: 'immediate',
        priority: 'high',
        title: '扭转下降趋势',
        description: '您的表现有下降趋势，需要立即采取行动',
        reasoning: '趋势分析显示持续下降可能影响长期目标',
        expectedOutcome: '阻止进一步下降，开始恢复',
        timeToSeeResults: 7,
        difficulty: 'medium',
        resources: ['重新评估目标', '寻求支持', '调整策略']
      })
    }
    
    // 基于风险的建议
    risks.forEach(risk => {
      if (risk.severity === 'high' || risk.severity === 'critical') {
        recommendations.push({
          category: 'short_term',
          priority: risk.severity === 'critical' ? 'critical' : 'high',
          title: `应对${risk.description}`,
          description: risk.mitigation.join('，'),
          reasoning: `风险概率${(risk.probability * 100).toFixed(0)}%，影响：${risk.impact}`,
          expectedOutcome: '降低风险发生概率',
          timeToSeeResults: Math.floor(risk.timeframe / 2),
          difficulty: 'medium',
          resources: risk.mitigation
        })
      }
    })
    
    return recommendations
  }

  // 辅助方法
  private getCurrentMetricValues(data: DailyData[]) {
    const recent = data.slice(-7) // 最近7天
    return {
      focusTime: recent.reduce((sum, d) => sum + d.totalFocusTime, 0) / recent.length,
      habitStrength: recent.reduce((sum, d) => sum + d.completedHabits, 0) / recent.length,
      consistency: this.calculateConsistencyScore(recent),
      productivity: recent.reduce((sum, d) => sum + d.productivityScore, 0) / recent.length
    }
  }

  private predictFocusTime(data: DailyData[], days: number): number {
    const recent = data.slice(-14)
    const trend = this.calculateRecentTrend(recent)
    const current = recent[recent.length - 1]?.totalFocusTime || 0
    return Math.max(0, current + trend * days)
  }

  private predictProductivity(data: DailyData[], days: number): number {
    const recent = data.slice(-14)
    const trend = this.calculateRecentTrend(recent, 'productivityScore')
    const current = recent[recent.length - 1]?.productivityScore || 0
    return Math.min(100, Math.max(0, current + trend * days))
  }

  private predictHabitStrength(data: DailyData[], days: number): number {
    const recent = data.slice(-21)
    const trend = this.calculateRecentTrend(recent, 'completedHabits')
    const current = this.calculateCurrentHabitStrength(recent)
    return Math.min(100, Math.max(0, current + trend * days))
  }

  private predictConsistency(data: DailyData[], days: number): number {
    const consistencyTrend = this.calculateConsistencyTrend(data)
    const current = this.calculateConsistencyScore(data.slice(-7))
    return Math.min(100, Math.max(0, current + consistencyTrend * days))
  }

  private calculateRecentTrend(data: DailyData[], metric: keyof DailyData = 'totalFocusTime'): number {
    if (data.length < 2) return 0
    
    const values = data.map(d => (d as any)[metric] || 0)
    const x = values.map((_, i) => i)
    const n = values.length
    
    const sumX = x.reduce((sum, val) => sum + val, 0)
    const sumY = values.reduce((sum, val) => sum + val, 0)
    const sumXY = x.reduce((sum, val, i) => sum + val * values[i], 0)
    const sumXX = x.reduce((sum, val) => sum + val * val, 0)
    
    return (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX) || 0
  }

  private calculateCurrentHabitStrength(data: DailyData[]): number {
    if (data.length === 0) return 0
    
    const recent = data.slice(-7)
    const avgCompleted = recent.reduce((sum, d) => sum + d.completedHabits, 0) / recent.length
    const maxPossible = 6 // 假设最多6个习惯
    
    return (avgCompleted / maxPossible) * 100
  }

  private calculateConsistencyScore(data: DailyData[]): number {
    if (data.length === 0) return 0
    
    const values = data.map(d => d.productivityScore)
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length
    const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length
    
    // 一致性分数：方差越小，一致性越高
    return Math.max(0, 100 - Math.sqrt(variance))
  }

  private calculateOverallConfidence(dataQuality: number, trend: TrendAnalysis): number {
    return (dataQuality + trend.confidence) / 2
  }

  private calculateOverallScore(data: DailyData[]): number {
    if (data.length === 0) return 0
    return data.reduce((sum, d) => sum + d.productivityScore, 0) / data.length
  }

  private predictOverallGrowth(data: DailyData[], days: number): number {
    const trend = this.calculateRecentTrend(data, 'productivityScore')
    const current = this.calculateOverallScore(data.slice(-30))
    return Math.min(100, Math.max(0, current + trend * days))
  }

  private determineTrend(data: DailyData[], metric: keyof DailyData): 'increasing' | 'decreasing' | 'stable' | 'volatile' {
    const trend = this.calculateRecentTrend(data, metric)
    if (Math.abs(trend) < 0.1) return 'stable'
    return trend > 0 ? 'increasing' : 'decreasing'
  }

  private calculateExpectedChange(data: DailyData[], metric: keyof DailyData): number {
    const trend = this.calculateRecentTrend(data, metric)
    const current = data[data.length - 1] ? (data[data.length - 1] as any)[metric] || 0 : 0
    return current > 0 ? (trend * 30 / current) * 100 : 0 // 30天变化百分比
  }

  private determineConsistencyTrend(data: DailyData[]): 'increasing' | 'decreasing' | 'stable' | 'volatile' {
    // 简化实现
    return 'stable'
  }

  private calculateConsistencyChange(data: DailyData[]): number {
    // 简化实现
    return 0
  }

  private calculateConsistencyTrend(data: DailyData[]): number {
    // 简化实现
    return 0
  }

  private detectHabitDeclineRisk(data: DailyData[]): boolean {
    const recent = data.slice(-7)
    const older = data.slice(-14, -7)
    
    if (recent.length === 0 || older.length === 0) return false
    
    const recentAvg = recent.reduce((sum, d) => sum + d.completedHabits, 0) / recent.length
    const olderAvg = older.reduce((sum, d) => sum + d.completedHabits, 0) / older.length
    
    return recentAvg < olderAvg * 0.8 // 下降20%以上
  }

  private detectConsistencyRisk(data: DailyData[]): boolean {
    const recent = data.slice(-7)
    const variance = this.calculateVariance(recent.map(d => d.productivityScore))
    return variance > 400 // 高方差表示不一致
  }

  private detectGrowthOpportunity(data: DailyData[]): boolean {
    const recent = data.slice(-7)
    const avgCompleted = recent.reduce((sum, d) => sum + d.completedHabits, 0) / recent.length
    return avgCompleted >= 4 // 习惯完成率高，有扩展空间
  }

  private calculateVariance(values: number[]): number {
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length
    return values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length
  }
}

export default TrendAnalysisEngine
