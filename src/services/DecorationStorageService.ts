import { 
  DecorationManager,
  PlacedDecoration,
  BeautyHistoryPoint,
  FarmTheme
} from '../types/decoration'

// 存储配置接口
export interface StorageConfig {
  useLocalStorage: boolean
  useIndexedDB: boolean
  useRemoteAPI: boolean
  apiEndpoint?: string
  storagePrefix: string
  autoSaveInterval?: number // 自动保存间隔(毫秒)
  enableCompression?: boolean
  enableEncryption?: boolean
}

// 存储操作结果
export interface StorageResult<T = any> {
  success: boolean
  data?: T
  error?: string
  timestamp: number
}

// 数据同步状态
export interface SyncStatus {
  lastSyncTime: number
  localVersion: number
  remoteVersion?: number
  hasPendingChanges: boolean
  conflictResolution: 'local' | 'remote' | 'merge'
}

// 备份元数据
export interface BackupMetadata {
  id: string
  timestamp: number
  version: string
  userAgent: string
  size: number
  checksum: string
  description?: string
}

// 装饰系统数据存储服务
export class DecorationStorageService {
  private config: StorageConfig
  private autoSaveTimer?: NodeJS.Timeout
  private syncStatus: SyncStatus
  private isInitialized: boolean = false

  constructor(config: Partial<StorageConfig> = {}) {
    this.config = {
      useLocalStorage: true,
      useIndexedDB: true,
      useRemoteAPI: false,
      storagePrefix: 'selfgame_decoration_',
      autoSaveInterval: 30000, // 30秒
      enableCompression: true,
      enableEncryption: false,
      ...config
    }

    this.syncStatus = {
      lastSyncTime: 0,
      localVersion: 0,
      hasPendingChanges: false,
      conflictResolution: 'local'
    }
  }

  // ============ 初始化和配置 ============

  /**
   * 初始化存储服务
   */
  async initialize(): Promise<StorageResult> {
    try {
      // 初始化IndexedDB
      if (this.config.useIndexedDB) {
        await this.initializeIndexedDB()
      }

      // 检查本地存储权限
      if (this.config.useLocalStorage) {
        if (!this.isLocalStorageAvailable()) {
          console.warn('LocalStorage不可用，将使用内存存储')
          this.config.useLocalStorage = false
        }
      }

      // 初始化远程API连接
      if (this.config.useRemoteAPI && this.config.apiEndpoint) {
        const connectionResult = await this.testRemoteConnection()
        if (!connectionResult.success) {
          console.warn('远程API连接失败，将使用本地存储模式')
        }
      }

      // 设置自动保存
      if (this.config.autoSaveInterval && this.config.autoSaveInterval > 0) {
        this.setupAutoSave()
      }

      this.isInitialized = true

      return {
        success: true,
        timestamp: Date.now()
      }
    } catch (error) {
      return {
        success: false,
        error: `初始化存储服务失败: ${error instanceof Error ? error.message : String(error)}`,
        timestamp: Date.now()
      }
    }
  }

  /**
   * 销毁存储服务
   */
  destroy(): void {
    if (this.autoSaveTimer) {
      clearInterval(this.autoSaveTimer)
      this.autoSaveTimer = undefined
    }
    this.isInitialized = false
  }

  // ============ 装饰管理器数据操作 ============

  /**
   * 保存装饰管理器状态
   */
  async saveDecorationManager(manager: DecorationManager, userId?: string): Promise<StorageResult> {
    if (!this.isInitialized) {
      await this.initialize()
    }

    try {
      const timestamp = Date.now()
      const dataToSave = {
        ...manager,
        lastSaved: timestamp,
        version: this.syncStatus.localVersion + 1
      }

      const results = await Promise.allSettled([
        this.config.useLocalStorage ? this.saveToLocalStorage('manager', dataToSave) : Promise.resolve({ success: true }),
        this.config.useIndexedDB ? this.saveToIndexedDB('decorationManager', dataToSave) : Promise.resolve({ success: true }),
        this.config.useRemoteAPI ? this.saveToRemoteAPI('manager', dataToSave, userId) : Promise.resolve({ success: true })
      ])

      // 检查是否至少有一种存储方式成功
      const hasSuccess = results.some(result => 
        result.status === 'fulfilled' && result.value.success
      )

      if (hasSuccess) {
        this.syncStatus.localVersion++
        this.syncStatus.hasPendingChanges = false
        this.syncStatus.lastSyncTime = timestamp

        return {
          success: true,
          data: dataToSave,
          timestamp
        }
      } else {
        throw new Error('所有存储方式都失败了')
      }

    } catch (error) {
      return {
        success: false,
        error: `保存装饰管理器数据失败: ${error instanceof Error ? error.message : String(error)}`,
        timestamp: Date.now()
      }
    }
  }

  /**
   * 加载装饰管理器状态
   */
  async loadDecorationManager(userId?: string): Promise<StorageResult<DecorationManager>> {
    if (!this.isInitialized) {
      await this.initialize()
    }

    try {
      let bestData: DecorationManager | null = null
      let bestTimestamp = 0

      // 尝试从各种存储源加载
      const loadPromises = []

      if (this.config.useLocalStorage) {
        loadPromises.push(this.loadFromLocalStorage('manager'))
      }

      if (this.config.useIndexedDB) {
        loadPromises.push(this.loadFromIndexedDB('decorationManager'))
      }

      if (this.config.useRemoteAPI) {
        loadPromises.push(this.loadFromRemoteAPI('manager', userId))
      }

      const results = await Promise.allSettled(loadPromises)

      // 选择最新的数据
      for (const result of results) {
        if (result.status === 'fulfilled' && result.value.success && result.value.data) {
          const data = result.value.data
          const dataTimestamp = data.lastSaved || data.beautyStats?.beautyHistory?.[0]?.timestamp || 0

          if (dataTimestamp > bestTimestamp) {
            bestData = data
            bestTimestamp = dataTimestamp
          }
        }
      }

      if (bestData) {
        // 数据清理和验证
        const cleanedData = this.validateAndCleanManagerData(bestData)
        
        return {
          success: true,
          data: cleanedData,
          timestamp: Date.now()
        }
      } else {
        // 返回默认的装饰管理器
        const defaultManager = this.createDefaultManager()
        return {
          success: true,
          data: defaultManager,
          timestamp: Date.now()
        }
      }

    } catch (error) {
      return {
        success: false,
        error: `加载装饰管理器数据失败: ${error instanceof Error ? error.message : String(error)}`,
        timestamp: Date.now()
      }
    }
  }

  // ============ 备份和恢复 ============

  /**
   * 创建数据备份
   */
  async createBackup(manager: DecorationManager, description?: string): Promise<StorageResult<BackupMetadata>> {
    try {
      const backupId = `backup_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      const timestamp = Date.now()
      
      const backupData = {
        id: backupId,
        timestamp,
        manager,
        metadata: {
          version: '1.0.0',
          userAgent: navigator.userAgent,
          description: description || `自动备份 ${new Date(timestamp).toLocaleString()}`
        }
      }

      // 压缩数据
      const serializedData = this.config.enableCompression 
        ? await this.compressData(JSON.stringify(backupData))
        : JSON.stringify(backupData)

      // 计算校验和
      const checksum = await this.calculateChecksum(serializedData)

      const metadata: BackupMetadata = {
        id: backupId,
        timestamp,
        version: '1.0.0',
        userAgent: navigator.userAgent,
        size: serializedData.length,
        checksum,
        description: backupData.metadata.description
      }

      // 保存备份
      const saveResult = await this.saveToIndexedDB(`backup_${backupId}`, {
        metadata,
        data: serializedData
      })

      if (saveResult.success) {
        // 保存备份列表
        await this.updateBackupList(metadata)
        
        return {
          success: true,
          data: metadata,
          timestamp: Date.now()
        }
      } else {
        throw new Error('保存备份失败')
      }

    } catch (error) {
      return {
        success: false,
        error: `创建备份失败: ${error instanceof Error ? error.message : String(error)}`,
        timestamp: Date.now()
      }
    }
  }

  /**
   * 恢复数据备份
   */
  async restoreBackup(backupId: string): Promise<StorageResult<DecorationManager>> {
    try {
      const backupResult = await this.loadFromIndexedDB(`backup_${backupId}`)
      
      if (!backupResult.success || !backupResult.data) {
        throw new Error('备份数据不存在')
      }

      const { metadata, data } = backupResult.data

      // 验证校验和
      const actualChecksum = await this.calculateChecksum(data)
      if (actualChecksum !== metadata.checksum) {
        throw new Error('备份数据已损坏')
      }

      // 解压缩数据
      const decompressedData = this.config.enableCompression 
        ? await this.decompressData(data)
        : data

      const backupData = JSON.parse(decompressedData)
      
      if (!backupData.manager) {
        throw new Error('备份数据格式错误')
      }

      const cleanedManager = this.validateAndCleanManagerData(backupData.manager)

      return {
        success: true,
        data: cleanedManager,
        timestamp: Date.now()
      }

    } catch (error) {
      return {
        success: false,
        error: `恢复备份失败: ${error instanceof Error ? error.message : String(error)}`,
        timestamp: Date.now()
      }
    }
  }

  /**
   * 获取备份列表
   */
  async getBackupList(): Promise<StorageResult<BackupMetadata[]>> {
    try {
      const result = await this.loadFromIndexedDB('backupList')
      
      if (result.success && result.data) {
        return {
          success: true,
          data: result.data,
          timestamp: Date.now()
        }
      } else {
        return {
          success: true,
          data: [],
          timestamp: Date.now()
        }
      }

    } catch (error) {
      return {
        success: false,
        error: `获取备份列表失败: ${error instanceof Error ? error.message : String(error)}`,
        timestamp: Date.now()
      }
    }
  }

  /**
   * 删除备份
   */
  async deleteBackup(backupId: string): Promise<StorageResult> {
    try {
      // 删除备份数据
      await this.deleteFromIndexedDB(`backup_${backupId}`)
      
      // 更新备份列表
      const listResult = await this.getBackupList()
      if (listResult.success && listResult.data) {
        const updatedList = listResult.data.filter(backup => backup.id !== backupId)
        await this.saveToIndexedDB('backupList', updatedList)
      }

      return {
        success: true,
        timestamp: Date.now()
      }

    } catch (error) {
      return {
        success: false,
        error: `删除备份失败: ${error instanceof Error ? error.message : String(error)}`,
        timestamp: Date.now()
      }
    }
  }

  // ============ 数据同步 ============

  /**
   * 同步数据到远程服务器
   */
  async syncToRemote(manager: DecorationManager, userId: string): Promise<StorageResult> {
    if (!this.config.useRemoteAPI || !this.config.apiEndpoint) {
      return {
        success: false,
        error: '远程API未配置',
        timestamp: Date.now()
      }
    }

    try {
      const result = await this.saveToRemoteAPI('manager', manager, userId)
      
      if (result.success) {
        this.syncStatus.lastSyncTime = Date.now()
        this.syncStatus.hasPendingChanges = false
      }

      return result

    } catch (error) {
      return {
        success: false,
        error: `同步到远程失败: ${error instanceof Error ? error.message : String(error)}`,
        timestamp: Date.now()
      }
    }
  }

  /**
   * 从远程服务器同步数据
   */
  async syncFromRemote(userId: string): Promise<StorageResult<DecorationManager>> {
    if (!this.config.useRemoteAPI || !this.config.apiEndpoint) {
      return {
        success: false,
        error: '远程API未配置',
        timestamp: Date.now()
      }
    }

    try {
      const result = await this.loadFromRemoteAPI('manager', userId)
      
      if (result.success && result.data) {
        // 自动保存到本地
        if (this.config.useLocalStorage) {
          await this.saveToLocalStorage('manager', result.data)
        }
        if (this.config.useIndexedDB) {
          await this.saveToIndexedDB('decorationManager', result.data)
        }

        this.syncStatus.lastSyncTime = Date.now()
      }

      return result

    } catch (error) {
      return {
        success: false,
        error: `从远程同步失败: ${error instanceof Error ? error.message : String(error)}`,
        timestamp: Date.now()
      }
    }
  }

  // ============ 私有方法 ============

  private async initializeIndexedDB(): Promise<void> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(`${this.config.storagePrefix}db`, 1)
      
      request.onerror = () => reject(new Error('IndexedDB初始化失败'))
      
      request.onsuccess = () => resolve()
      
      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result
        
        // 创建对象存储
        if (!db.objectStoreNames.contains('decorations')) {
          db.createObjectStore('decorations', { keyPath: 'key' })
        }
        
        if (!db.objectStoreNames.contains('backups')) {
          db.createObjectStore('backups', { keyPath: 'key' })
        }
      }
    })
  }

  private isLocalStorageAvailable(): boolean {
    try {
      const test = '__storage_test__'
      localStorage.setItem(test, test)
      localStorage.removeItem(test)
      return true
    } catch {
      return false
    }
  }

  private async testRemoteConnection(): Promise<StorageResult> {
    try {
      const response = await fetch(`${this.config.apiEndpoint}/health`, {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' }
      })
      
      return {
        success: response.ok,
        timestamp: Date.now()
      }
    } catch (error) {
      return {
        success: false,
        error: `远程连接测试失败: ${error instanceof Error ? error.message : String(error)}`,
        timestamp: Date.now()
      }
    }
  }

  private setupAutoSave(): void {
    if (this.autoSaveTimer) {
      clearInterval(this.autoSaveTimer)
    }

    this.autoSaveTimer = setInterval(() => {
      if (this.syncStatus.hasPendingChanges) {
        // 这里需要从外部获取当前状态
        // 实际使用时需要通过回调或事件系统来获取
        console.log('自动保存检查...')
      }
    }, this.config.autoSaveInterval)
  }

  private async saveToLocalStorage(key: string, data: any): Promise<StorageResult> {
    try {
      const fullKey = `${this.config.storagePrefix}${key}`
      const serialized = JSON.stringify(data)
      localStorage.setItem(fullKey, serialized)
      
      return { success: true, timestamp: Date.now() }
    } catch (error) {
      return {
        success: false,
        error: `LocalStorage保存失败: ${error instanceof Error ? error.message : String(error)}`,
        timestamp: Date.now()
      }
    }
  }

  private async loadFromLocalStorage(key: string): Promise<StorageResult> {
    try {
      const fullKey = `${this.config.storagePrefix}${key}`
      const serialized = localStorage.getItem(fullKey)
      
      if (serialized) {
        const data = JSON.parse(serialized)
        return { success: true, data, timestamp: Date.now() }
      } else {
        return { success: false, error: '数据不存在', timestamp: Date.now() }
      }
    } catch (error) {
      return {
        success: false,
        error: `LocalStorage加载失败: ${error instanceof Error ? error.message : String(error)}`,
        timestamp: Date.now()
      }
    }
  }

  private async saveToIndexedDB(key: string, data: any): Promise<StorageResult> {
    return new Promise((resolve) => {
      const request = indexedDB.open(`${this.config.storagePrefix}db`)
      
      request.onsuccess = () => {
        const db = request.result
        const transaction = db.transaction(['decorations'], 'readwrite')
        const store = transaction.objectStore('decorations')
        
        store.put({ key, data, timestamp: Date.now() })
        
        transaction.oncomplete = () => {
          resolve({ success: true, timestamp: Date.now() })
        }
        
        transaction.onerror = () => {
          resolve({
            success: false,
            error: 'IndexedDB保存失败',
            timestamp: Date.now()
          })
        }
      }
      
      request.onerror = () => {
        resolve({
          success: false,
          error: 'IndexedDB连接失败',
          timestamp: Date.now()
        })
      }
    })
  }

  private async loadFromIndexedDB(key: string): Promise<StorageResult> {
    return new Promise((resolve) => {
      const request = indexedDB.open(`${this.config.storagePrefix}db`)
      
      request.onsuccess = () => {
        const db = request.result
        const transaction = db.transaction(['decorations'], 'readonly')
        const store = transaction.objectStore('decorations')
        const getRequest = store.get(key)
        
        getRequest.onsuccess = () => {
          const result = getRequest.result
          if (result) {
            resolve({ success: true, data: result.data, timestamp: Date.now() })
          } else {
            resolve({ success: false, error: '数据不存在', timestamp: Date.now() })
          }
        }
        
        getRequest.onerror = () => {
          resolve({
            success: false,
            error: 'IndexedDB读取失败',
            timestamp: Date.now()
          })
        }
      }
      
      request.onerror = () => {
        resolve({
          success: false,
          error: 'IndexedDB连接失败',
          timestamp: Date.now()
        })
      }
    })
  }

  private async deleteFromIndexedDB(key: string): Promise<StorageResult> {
    return new Promise((resolve) => {
      const request = indexedDB.open(`${this.config.storagePrefix}db`)
      
      request.onsuccess = () => {
        const db = request.result
        const transaction = db.transaction(['decorations'], 'readwrite')
        const store = transaction.objectStore('decorations')
        
        store.delete(key)
        
        transaction.oncomplete = () => {
          resolve({ success: true, timestamp: Date.now() })
        }
        
        transaction.onerror = () => {
          resolve({
            success: false,
            error: 'IndexedDB删除失败',
            timestamp: Date.now()
          })
        }
      }
    })
  }

  private async saveToRemoteAPI(key: string, data: any, userId?: string): Promise<StorageResult> {
    try {
      const payload = {
        key,
        data,
        userId,
        timestamp: Date.now()
      }

      const response = await fetch(`${this.config.apiEndpoint}/decorations/save`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(userId && { 'Authorization': `Bearer ${userId}` })
        },
        body: JSON.stringify(payload)
      })

      if (response.ok) {
        const result = await response.json()
        return { success: true, data: result, timestamp: Date.now() }
      } else {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

    } catch (error) {
      return {
        success: false,
        error: `远程API保存失败: ${error instanceof Error ? error.message : String(error)}`,
        timestamp: Date.now()
      }
    }
  }

  private async loadFromRemoteAPI(key: string, userId?: string): Promise<StorageResult> {
    try {
      const url = new URL(`${this.config.apiEndpoint}/decorations/load`)
      url.searchParams.set('key', key)
      if (userId) url.searchParams.set('userId', userId)

      const response = await fetch(url.toString(), {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          ...(userId && { 'Authorization': `Bearer ${userId}` })
        }
      })

      if (response.ok) {
        const result = await response.json()
        return { success: true, data: result.data, timestamp: Date.now() }
      } else if (response.status === 404) {
        return { success: false, error: '数据不存在', timestamp: Date.now() }
      } else {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

    } catch (error) {
      return {
        success: false,
        error: `远程API加载失败: ${error instanceof Error ? error.message : String(error)}`,
        timestamp: Date.now()
      }
    }
  }

  private validateAndCleanManagerData(data: any): DecorationManager {
    // 数据验证和清理逻辑
    const defaultManager = this.createDefaultManager()
    
    return {
      ownedDecorations: data.ownedDecorations || defaultManager.ownedDecorations,
      placedDecorations: Array.isArray(data.placedDecorations) 
        ? data.placedDecorations.filter(this.validatePlacedDecoration)
        : defaultManager.placedDecorations,
      currentTheme: data.currentTheme || defaultManager.currentTheme,
      unlockedThemes: Array.isArray(data.unlockedThemes) 
        ? data.unlockedThemes 
        : defaultManager.unlockedThemes,
      beautyStats: data.beautyStats || defaultManager.beautyStats,
      shopState: data.shopState || defaultManager.shopState
    }
  }

  private validatePlacedDecoration(decoration: any): boolean {
    return (
      decoration &&
      typeof decoration.instanceId === 'string' &&
      typeof decoration.decorationId === 'string' &&
      typeof decoration.x === 'number' &&
      typeof decoration.y === 'number'
    )
  }

  private createDefaultManager(): DecorationManager {
    return {
      ownedDecorations: {},
      placedDecorations: [],
      currentTheme: 'natural_paradise',
      unlockedThemes: ['natural_paradise'],
      beautyStats: {
        totalBeauty: 0,
        beautyByType: {} as any,
        beautyHistory: []
      },
      shopState: {
        lastVisitTime: Date.now(),
        recentPurchases: [],
        wishlist: []
      }
    }
  }

  private async updateBackupList(metadata: BackupMetadata): Promise<void> {
    const listResult = await this.getBackupList()
    const currentList = listResult.success ? listResult.data || [] : []
    
    currentList.push(metadata)
    
    // 保持最多20个备份
    if (currentList.length > 20) {
      const sortedList = currentList.sort((a, b) => b.timestamp - a.timestamp)
      const removedBackups = sortedList.slice(20)
      
      // 删除旧备份
      for (const backup of removedBackups) {
        await this.deleteFromIndexedDB(`backup_${backup.id}`)
      }
      
      await this.saveToIndexedDB('backupList', sortedList.slice(0, 20))
    } else {
      await this.saveToIndexedDB('backupList', currentList)
    }
  }

  private async compressData(data: string): Promise<string> {
    // 简单的压缩实现（实际应用中可以使用更好的压缩库）
    return btoa(data)
  }

  private async decompressData(data: string): Promise<string> {
    return atob(data)
  }

  private async calculateChecksum(data: string): Promise<string> {
    // 使用Web Crypto API计算SHA-256校验和
    const encoder = new TextEncoder()
    const dataBuffer = encoder.encode(data)
    const hashBuffer = await crypto.subtle.digest('SHA-256', dataBuffer)
    const hashArray = Array.from(new Uint8Array(hashBuffer))
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('')
  }

  // ============ 公共属性访问器 ============

  getSyncStatus(): SyncStatus {
    return { ...this.syncStatus }
  }

  getConfig(): StorageConfig {
    return { ...this.config }
  }

  updateConfig(newConfig: Partial<StorageConfig>): void {
    this.config = { ...this.config, ...newConfig }
  }
}

export default DecorationStorageService 