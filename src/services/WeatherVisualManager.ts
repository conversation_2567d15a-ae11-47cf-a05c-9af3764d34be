import Phaser from 'phaser';
import { WeatherType, WeatherIntensity, WeatherState, Season } from '../types/weather';
import {
  WeatherVisualConfig,
  WeatherTransitionConfig,
  WeatherVisualManagerState,
  VisualPerformanceConfig,
  VisualQuality,
  ParticleEffectConfig,
  BackgroundEffectConfig,
  LightningConfig,
  WindEffectConfig,
  FogEffectConfig,
  WeatherVisualEvent,
  WEATHER_VISUAL_PRESETS,
  SeasonalVisualAdjust
} from '../types/weatherVisuals';

/**
 * 天气视觉效果管理器
 * 负责在Phaser.js场景中创建和管理天气视觉效果
 */
export class WeatherVisualManager {
  private scene: Phaser.Scene;
  private state: WeatherVisualManagerState;
  private backgroundGraphics?: Phaser.GameObjects.Graphics;
  private fogOverlay?: Phaser.GameObjects.Graphics;
  private lightningFlash?: Phaser.GameObjects.Graphics;
  private windSway: Map<string, Phaser.Tweens.Tween> = new Map();
  private eventEmitter: Phaser.Events.EventEmitter;
  
  // 粒子系统池
  private particlePool: Map<string, Phaser.GameObjects.Particles.ParticleEmitter[]> = new Map();
  private particleTextures: Map<string, string> = new Map();
  
  // 动画和效果
  private activeAnimations: Map<string, Phaser.Tweens.Tween> = new Map();
  private updateTimer?: Phaser.Time.TimerEvent;
  
  // 性能监控
  private performanceStats = {
    activeParticles: 0,
    frameTime: 0,
    memoryUsage: 0
  };

  constructor(scene: Phaser.Scene, config?: Partial<VisualPerformanceConfig>) {
    this.scene = scene;
    this.eventEmitter = new Phaser.Events.EventEmitter();
    
    // 初始化默认性能配置
    const defaultPerformanceConfig: VisualPerformanceConfig = {
      quality: VisualQuality.MEDIUM,
      maxParticles: 500,
      enableAdvancedEffects: true,
      enablePostProcessing: false,
      particlePoolSize: 10,
      updateFrequency: 16 // ~60fps
    };

    // 初始化季节调整
    const defaultSeasonalAdjust: SeasonalVisualAdjust = {
      spring: { colorTint: 0x90EE90, brightness: 1.1, particleBonus: 1.2 },
      summer: { colorTint: 0xFFD700, brightness: 1.3, particleBonus: 1.0 },
      autumn: { colorTint: 0xFF8C00, brightness: 0.9, particleBonus: 1.1 },
      winter: { colorTint: 0xB0C4DE, brightness: 0.7, particleBonus: 1.3 }
    };

    // 初始化状态
    this.state = {
      currentWeather: WeatherType.SUNNY,
      currentIntensity: WeatherIntensity.LIGHT,
      isTransitioning: false,
      transitionProgress: 0,
      activeEffects: new Map(),
      particleEmitters: new Map(),
      performanceConfig: { ...defaultPerformanceConfig, ...config },
      seasonalAdjustment: defaultSeasonalAdjust
    };

    this.initializeVisualSystem();
  }

  /**
   * 添加事件监听器
   */
  on(event: string, listener: (...args: any[]) => void): void {
    this.eventEmitter.on(event, listener);
  }

  /**
   * 移除事件监听器
   */
  off(event: string, listener: (...args: any[]) => void): void {
    this.eventEmitter.off(event, listener);
  }

  /**
   * 触发事件
   */
  private emit(event: string, ...args: any[]): void {
    this.eventEmitter.emit(event, ...args);
  }

  /**
   * 初始化视觉系统
   */
  private initializeVisualSystem(): void {
    // 创建粒子纹理
    this.createParticleTextures();
    
    // 创建背景图形对象
    this.createBackgroundGraphics();
    
    // 初始化粒子池
    this.initializeParticlePool();
    
    // 启动更新循环
    this.startUpdateLoop();
    
    console.log('天气视觉效果系统已初始化', {
      quality: this.state.performanceConfig.quality,
      maxParticles: this.state.performanceConfig.maxParticles
    });
  }

  /**
   * 创建粒子纹理
   */
  private createParticleTextures(): void {
    const graphics = this.scene.add.graphics();
    
    // 雨滴纹理
    graphics.clear();
    graphics.fillStyle(0x6495ED, 1);
    graphics.fillRect(0, 0, 2, 8);
    graphics.generateTexture('rain_drop', 2, 8);
    this.particleTextures.set('rain_drop', 'rain_drop');

    // 雪花纹理
    graphics.clear();
    graphics.fillStyle(0xFFFAFA, 1);
    graphics.fillCircle(4, 4, 3);
    graphics.fillRect(2, 4, 4, 1);
    graphics.fillRect(4, 2, 1, 4);
    graphics.generateTexture('snowflake', 8, 8);
    this.particleTextures.set('snowflake', 'snowflake');

    // 光粒子纹理
    graphics.clear();
    graphics.fillGradientStyle(0xFFD700, 0xFFD700, 0xFFFF00, 0xFFFF00, 1, 0.5, 1, 0);
    graphics.fillCircle(8, 8, 6);
    graphics.generateTexture('light_particle', 16, 16);
    this.particleTextures.set('light_particle', 'light_particle');

    // 叶子粒子纹理
    graphics.clear();
    graphics.fillStyle(0x90EE90, 1);
    graphics.beginPath();
    graphics.moveTo(4, 0);
    graphics.lineTo(8, 6);
    graphics.lineTo(4, 8);
    graphics.lineTo(0, 6);
    graphics.closePath();
    graphics.fillPath();
    graphics.generateTexture('leaf_particle', 8, 8);
    this.particleTextures.set('leaf_particle', 'leaf_particle');

    // 清理临时图形对象
    graphics.destroy();
  }

  /**
   * 创建背景图形对象
   */
  private createBackgroundGraphics(): void {
    this.backgroundGraphics = this.scene.add.graphics();
    this.backgroundGraphics.setDepth(-100); // 确保在最底层
    
    this.fogOverlay = this.scene.add.graphics();
    this.fogOverlay.setDepth(50); // 雾在前景
    this.fogOverlay.setAlpha(0);
    
    this.lightningFlash = this.scene.add.graphics();
    this.lightningFlash.setDepth(100); // 闪电在最顶层
    this.lightningFlash.setAlpha(0);
  }

  /**
   * 初始化粒子池
   */
  private initializeParticlePool(): void {
    const poolSize = this.state.performanceConfig.particlePoolSize;
    
    for (const [textureKey, textureName] of this.particleTextures) {
      const pool: Phaser.GameObjects.Particles.ParticleEmitter[] = [];
      
      for (let i = 0; i < poolSize; i++) {
        const emitter = this.scene.add.particles(0, 0, textureName, {
          active: false,
          visible: false
        });
        emitter.setDepth(10);
        pool.push(emitter);
      }
      
      this.particlePool.set(textureKey, pool);
    }
  }

  /**
   * 启动更新循环
   */
  private startUpdateLoop(): void {
    this.updateTimer = this.scene.time.addEvent({
      delay: this.state.performanceConfig.updateFrequency,
      callback: this.updateVisualEffects,
      callbackScope: this,
      loop: true
    });
  }

  /**
   * 更新视觉效果
   */
  private updateVisualEffects(): void {
    // 更新性能统计
    this.updatePerformanceStats();
    
    // 如果正在过渡中，更新过渡进度
    if (this.state.isTransitioning) {
      this.updateTransition();
    }
    
    // 更新粒子效果
    this.updateParticleEffects();
    
    // 自动性能调节
    this.autoAdjustPerformance();
  }

  /**
   * 更新粒子效果
   */
  private updateParticleEffects(): void {
    // 清理已经停止的粒子发射器
    this.state.particleEmitters.forEach((emitter, key) => {
      if (!emitter.active && emitter.getAliveParticleCount() === 0) {
        this.state.particleEmitters.delete(key);
        this.state.activeEffects.delete(key);
      }
    });
  }

  /**
   * 更新性能统计
   */
  private updatePerformanceStats(): void {
    let activeParticles = 0;
    this.state.particleEmitters.forEach(emitter => {
      if (emitter.active) {
        activeParticles += emitter.getAliveParticleCount();
      }
    });
    
    this.performanceStats.activeParticles = activeParticles;
    this.performanceStats.frameTime = this.scene.game.loop.actualFps;
  }

  /**
   * 自动性能调节
   */
  private autoAdjustPerformance(): void {
    const fps = this.performanceStats.frameTime;
    const config = this.state.performanceConfig;
    
    // 如果帧率低于30fps，降低质量
    if (fps < 30 && config.quality !== VisualQuality.LOW) {
      this.adjustQuality(VisualQuality.LOW);
    }
    // 如果帧率高于55fps且当前不是最高质量，可以提升
    else if (fps > 55 && config.quality === VisualQuality.LOW) {
      this.adjustQuality(VisualQuality.MEDIUM);
    }
  }

  /**
   * 调整视觉质量
   */
  adjustQuality(quality: VisualQuality): void {
    const config = this.state.performanceConfig;
    
    switch (quality) {
      case VisualQuality.LOW:
        config.maxParticles = 200;
        config.enableAdvancedEffects = false;
        break;
      case VisualQuality.MEDIUM:
        config.maxParticles = 500;
        config.enableAdvancedEffects = true;
        break;
      case VisualQuality.HIGH:
        config.maxParticles = 800;
        config.enableAdvancedEffects = true;
        break;
      case VisualQuality.ULTRA:
        config.maxParticles = 1200;
        config.enableAdvancedEffects = true;
        config.enablePostProcessing = true;
        break;
    }
    
    config.quality = quality;
    this.emit('qualityChanged', quality);
  }

  /**
   * 更改天气
   */
  async changeWeather(
    weatherType: WeatherType,
    intensity: WeatherIntensity = WeatherIntensity.MODERATE,
    options?: {
      transition?: WeatherTransitionConfig;
      season?: Season;
      immediate?: boolean;
    }
  ): Promise<void> {
    const config = WEATHER_VISUAL_PRESETS[weatherType];
    if (!config) {
      console.warn(`未找到天气类型 ${weatherType} 的视觉配置`);
      return;
    }

    // 创建天气变化事件
    const event: WeatherVisualEvent = {
      type: 'weather_visual_start',
      weatherType,
      intensity,
      timestamp: new Date()
    };
    this.emit('weatherVisualStart', event);

    if (options?.immediate) {
      await this.applyWeatherImmediate(config, intensity, options.season);
    } else {
      await this.applyWeatherTransition(config, intensity, options?.transition, options?.season);
    }

    // 更新状态
    this.state.currentWeather = weatherType;
    this.state.currentIntensity = intensity;

    // 发送完成事件
    this.emit('weatherVisualComplete', {
      ...event,
      type: 'weather_visual_complete'
    });
  }

  /**
   * 立即应用天气效果
   */
  private async applyWeatherImmediate(
    config: WeatherVisualConfig,
    intensity: WeatherIntensity,
    season?: Season
  ): Promise<void> {
    // 停止当前所有效果
    this.clearAllEffects();
    
    // 应用背景效果
    this.applyBackgroundEffect(config.background, season);
    
    // 应用粒子效果
    if (config.particles) {
      config.particles.forEach((particleConfig, index) => {
        this.createParticleEffect(`particle_${index}`, particleConfig, intensity);
      });
    }
    
    // 应用动画效果
    if (config.animations) {
      this.applyAnimationEffects(config.animations);
    }
  }

  /**
   * 应用天气过渡效果
   */
  private async applyWeatherTransition(
    config: WeatherVisualConfig,
    intensity: WeatherIntensity,
    transition?: WeatherTransitionConfig,
    season?: Season
  ): Promise<void> {
    const duration = transition?.duration || config.transitionDuration;
    
    this.state.isTransitioning = true;
    this.state.transitionProgress = 0;

    // 淡出当前效果
    await this.fadeOutCurrentEffects(duration / 2);
    
    // 应用新天气效果
    await this.applyWeatherImmediate(config, intensity, season);
    
    // 淡入新效果
    await this.fadeInNewEffects(duration / 2);
    
    this.state.isTransitioning = false;
    this.state.transitionProgress = 1;
  }

  /**
   * 淡出当前效果
   */
  private async fadeOutCurrentEffects(duration: number): Promise<void> {
    const promises: Promise<void>[] = [];
    
    // 淡出粒子效果
    this.state.particleEmitters.forEach(emitter => {
      promises.push(new Promise(resolve => {
        this.scene.tweens.add({
          targets: emitter,
          alpha: 0,
          duration: duration,
          onComplete: () => {
            emitter.stop();
            resolve();
          }
        });
      }));
    });
    
    // 淡出背景效果
    if (this.backgroundGraphics) {
      promises.push(new Promise(resolve => {
        this.scene.tweens.add({
          targets: this.backgroundGraphics,
          alpha: 0,
          duration: duration,
          onComplete: () => resolve()
        });
      }));
    }
    
    await Promise.all(promises);
  }

  /**
   * 淡入新效果
   */
  private async fadeInNewEffects(duration: number): Promise<void> {
    const promises: Promise<void>[] = [];
    
    // 淡入粒子效果
    this.state.particleEmitters.forEach(emitter => {
      emitter.setAlpha(0);
      promises.push(new Promise(resolve => {
        this.scene.tweens.add({
          targets: emitter,
          alpha: 1,
          duration: duration,
          onComplete: () => resolve()
        });
      }));
    });
    
    // 淡入背景效果
    if (this.backgroundGraphics) {
      this.backgroundGraphics.setAlpha(0);
      promises.push(new Promise(resolve => {
        this.scene.tweens.add({
          targets: this.backgroundGraphics,
          alpha: 1,
          duration: duration,
          onComplete: () => resolve()
        });
      }));
    }
    
    await Promise.all(promises);
  }

  /**
   * 应用背景效果
   */
  private applyBackgroundEffect(config: BackgroundEffectConfig, season?: Season): void {
    if (!this.backgroundGraphics) return;
    
    this.backgroundGraphics.clear();
    
    const screenWidth = this.scene.cameras.main.width;
    const screenHeight = this.scene.cameras.main.height;
    
    // 应用季节调整
    let topColor = config.skyGradient.topColor;
    let bottomColor = config.skyGradient.bottomColor;
    
    if (season && this.state.seasonalAdjustment[season]) {
      const adjustment = this.state.seasonalAdjustment[season];
      topColor = this.blendColors(topColor, adjustment.colorTint, 0.3);
      bottomColor = this.blendColors(bottomColor, adjustment.colorTint, 0.3);
    }
    
    // 创建天空渐变
    this.backgroundGraphics.fillGradientStyle(
      topColor, topColor, bottomColor, bottomColor, 1
    );
    this.backgroundGraphics.fillRect(0, 0, screenWidth, screenHeight);
    
    // 应用雾覆盖层
    if (config.fogOverlay && this.fogOverlay) {
      this.fogOverlay.clear();
      this.fogOverlay.fillStyle(config.fogOverlay.color, config.fogOverlay.alpha);
      this.fogOverlay.fillRect(0, 0, screenWidth, screenHeight);
    }
    
    // 应用光照效果
    this.applyLightingEffect(config.lightingLevel);
  }

  /**
   * 应用光照效果
   */
  private applyLightingEffect(lightingLevel: number): void {
    // 简单的亮度调整 - 通过所有游戏对象的色调调整
    const allObjects = this.scene.children.list;
    allObjects.forEach(obj => {
      if ((obj as any).setTint && typeof (obj as any).setTint === 'function') {
        const tintValue = Math.floor(255 * lightingLevel);
        const tint = (tintValue << 16) | (tintValue << 8) | tintValue;
        (obj as any).setTint(tint);
      }
    });
  }

  /**
   * 创建粒子效果
   */
  private createParticleEffect(
    id: string,
    config: ParticleEffectConfig,
    intensity: WeatherIntensity
  ): void {
    const textureKey = config.texture || 'rain_drop';
    const pool = this.particlePool.get(textureKey);
    
    if (!pool || pool.length === 0) {
      console.warn(`粒子池为空: ${textureKey}`);
      return;
    }
    
    // 从池中获取粒子发射器
    const emitter = pool.find(e => !e.active);
    if (!emitter) {
      console.warn(`没有可用的粒子发射器: ${textureKey}`);
      return;
    }
    
    // 根据强度调整参数
    const intensityMultiplier = this.getIntensityMultiplier(intensity);
    const adjustedConfig = this.adjustConfigForIntensity(config, intensityMultiplier);
    
    // 配置粒子发射器
    emitter.setConfig({
      x: { min: -100, max: this.scene.cameras.main.width + 100 },
      y: -50,
      speedX: { min: adjustedConfig.speed.min, max: adjustedConfig.speed.max },
      speedY: { min: adjustedConfig.speed.min, max: adjustedConfig.speed.max },
      scale: { min: adjustedConfig.scale.min, max: adjustedConfig.scale.max },
      alpha: { min: adjustedConfig.alpha.min, max: adjustedConfig.alpha.max },
      lifespan: adjustedConfig.lifespan,
      frequency: adjustedConfig.emissionRate > 0 ? 1000 / adjustedConfig.emissionRate : 100,
      gravityX: adjustedConfig.gravity?.x || 0,
      gravityY: adjustedConfig.gravity?.y || 300,
      maxParticles: Math.min(adjustedConfig.particleCount, this.state.performanceConfig.maxParticles),
      tint: adjustedConfig.tint,
      blendMode: adjustedConfig.blend || Phaser.BlendModes.NORMAL
    });
    
    // 启动发射器
    emitter.start();
    emitter.setActive(true);
    emitter.setVisible(true);
    
    // 存储到状态中
    this.state.particleEmitters.set(id, emitter);
    this.state.activeEffects.set(id, emitter);
  }

  /**
   * 根据强度调整配置
   */
  private adjustConfigForIntensity(
    config: ParticleEffectConfig,
    multiplier: number
  ): ParticleEffectConfig {
    return {
      ...config,
      particleCount: Math.floor(config.particleCount * multiplier),
      emissionRate: Math.floor(config.emissionRate * multiplier),
      speed: {
        min: config.speed.min * multiplier,
        max: config.speed.max * multiplier
      }
    };
  }

  /**
   * 获取强度倍数
   */
  private getIntensityMultiplier(intensity: WeatherIntensity): number {
    switch (intensity) {
      case WeatherIntensity.LIGHT: return 0.6;
      case WeatherIntensity.MODERATE: return 1.0;
      case WeatherIntensity.HEAVY: return 1.5;
      case WeatherIntensity.EXTREME: return 2.0;
      default: return 1.0;
    }
  }

  /**
   * 应用动画效果
   */
  private applyAnimationEffects(animations: any): void {
    // 闪电效果
    if (animations.lightning) {
      this.createLightningEffect(animations.lightning);
    }
    
    // 风效果
    if (animations.wind) {
      this.createWindEffect(animations.wind);
    }
    
    // 云朵动画
    if (animations.clouds) {
      this.createCloudAnimation(animations.clouds);
    }
  }

  /**
   * 创建闪电效果
   */
  private createLightningEffect(config: any): void {
    if (!this.lightningFlash || !this.state.performanceConfig.enableAdvancedEffects) return;
    
    const createFlash = () => {
      // 清除之前的闪电
      this.lightningFlash!.clear();
      
      // 创建闪电分支
      const screenWidth = this.scene.cameras.main.width;
      const screenHeight = this.scene.cameras.main.height;
      
      this.lightningFlash!.lineStyle(3, 0xFFFFFF, 1);
      
      // 主闪电路径
      const startX = Phaser.Math.Between(screenWidth * 0.2, screenWidth * 0.8);
      let currentX = startX;
      let currentY = 0;
      
      this.lightningFlash!.beginPath();
      this.lightningFlash!.moveTo(currentX, currentY);
      
      // 生成锯齿路径
      while (currentY < screenHeight * 0.8) {
        currentY += Phaser.Math.Between(30, 80);
        currentX += Phaser.Math.Between(-50, 50);
        this.lightningFlash!.lineTo(currentX, currentY);
      }
      
      this.lightningFlash!.strokePath();
      
      // 闪烁效果
      this.scene.tweens.add({
        targets: this.lightningFlash,
        alpha: { from: 1, to: 0 },
        duration: 100,
        repeat: 2,
        yoyo: true,
        onComplete: () => {
          this.lightningFlash!.clear();
        }
      });
      
      // 全屏闪光
      this.scene.cameras.main.flash(100, 255, 255, 255, false);
    };
    
    // 定期触发闪电
    const lightningTimer = this.scene.time.addEvent({
      delay: Phaser.Math.Between(3000, 8000),
      callback: createFlash,
      loop: true
    });
    
    this.activeAnimations.set('lightning', lightningTimer as any);
  }

  /**
   * 创建风效果
   */
  private createWindEffect(config: any): void {
    // 对场景中的对象添加摇摆效果
    const swayableObjects = this.scene.children.list.filter(obj => 
      obj.type === 'Image' || obj.type === 'Sprite'
    );
    
    swayableObjects.forEach((obj, index) => {
      const tween = this.scene.tweens.add({
        targets: obj,
        rotation: { from: -0.05, to: 0.05 },
        duration: config.duration || 1000,
        ease: config.ease || 'Sine.easeInOut',
        repeat: -1,
        yoyo: true,
        delay: index * 100 // 错开动画时间
      });
      
      this.windSway.set(`wind_${index}`, tween);
    });
  }

  /**
   * 创建云朵动画
   */
  private createCloudAnimation(config: any): void {
    // 如果场景中有云朵对象，添加移动动画
    const clouds = this.scene.children.list.filter(obj => 
      obj.name && obj.name.includes('cloud')
    );
    
    clouds.forEach((cloud, index) => {
      const tween = this.scene.tweens.add({
        targets: cloud,
        x: (cloud as any).x + this.scene.cameras.main.width,
        duration: config.duration || 20000,
        ease: 'Linear',
        repeat: -1,
        onRepeat: () => {
          (cloud as any).x = -100; // 重置到左侧
        }
      });
      
      this.activeAnimations.set(`cloud_${index}`, tween);
    });
  }

  /**
   * 清除所有效果
   */
  private clearAllEffects(): void {
    // 停止所有粒子发射器
    this.state.particleEmitters.forEach(emitter => {
      emitter.stop();
      emitter.setActive(false);
      emitter.setVisible(false);
    });
    this.state.particleEmitters.clear();
    
    // 停止所有动画
    this.activeAnimations.forEach(tween => {
      if (tween.destroy) tween.destroy();
    });
    this.activeAnimations.clear();
    
    // 停止风摇摆效果
    this.windSway.forEach(tween => {
      tween.destroy();
    });
    this.windSway.clear();
    
    // 清除图形效果
    if (this.lightningFlash) this.lightningFlash.clear();
    if (this.fogOverlay) this.fogOverlay.clear();
    
    this.state.activeEffects.clear();
  }

  /**
   * 更新过渡进度
   */
  private updateTransition(): void {
    // 这里可以添加平滑过渡的逻辑
    // 例如颜色插值、粒子数量渐变等
  }

  /**
   * 颜色混合
   */
  private blendColors(color1: number, color2: number, ratio: number): number {
    const r1 = (color1 >> 16) & 0xFF;
    const g1 = (color1 >> 8) & 0xFF;
    const b1 = color1 & 0xFF;
    
    const r2 = (color2 >> 16) & 0xFF;
    const g2 = (color2 >> 8) & 0xFF;
    const b2 = color2 & 0xFF;
    
    const r = Math.floor(r1 * (1 - ratio) + r2 * ratio);
    const g = Math.floor(g1 * (1 - ratio) + g2 * ratio);
    const b = Math.floor(b1 * (1 - ratio) + b2 * ratio);
    
    return (r << 16) | (g << 8) | b;
  }

  /**
   * 获取当前天气状态
   */
  getCurrentWeather(): { type: WeatherType; intensity: WeatherIntensity } {
    return {
      type: this.state.currentWeather,
      intensity: this.state.currentIntensity
    };
  }

  /**
   * 获取性能统计
   */
  getPerformanceStats() {
    return { ...this.performanceStats };
  }

  /**
   * 设置季节调整
   */
  setSeasonalAdjustment(season: Season, adjustment: any): void {
    this.state.seasonalAdjustment[season] = adjustment;
  }

  /**
   * 销毁管理器
   */
  destroy(): void {
    // 清除所有效果
    this.clearAllEffects();
    
    // 停止更新循环
    if (this.updateTimer) {
      this.updateTimer.destroy();
    }
    
    // 清理粒子池
    this.particlePool.forEach(pool => {
      pool.forEach(emitter => emitter.destroy());
    });
    this.particlePool.clear();
    
    // 清理图形对象
    if (this.backgroundGraphics) this.backgroundGraphics.destroy();
    if (this.fogOverlay) this.fogOverlay.destroy();
    if (this.lightningFlash) this.lightningFlash.destroy();
    
    // 移除所有事件监听器
    this.eventEmitter.removeAllListeners();
    
    console.log('天气视觉效果管理器已销毁');
  }
}

// 导出单例实例（可选）
export let weatherVisualManager: WeatherVisualManager | null = null;

export function initializeWeatherVisualManager(
  scene: Phaser.Scene,
  config?: Partial<VisualPerformanceConfig>
): WeatherVisualManager {
  if (weatherVisualManager) {
    weatherVisualManager.destroy();
  }
  
  weatherVisualManager = new WeatherVisualManager(scene, config);
  return weatherVisualManager;
}

export function getWeatherVisualManager(): WeatherVisualManager | null {
  return weatherVisualManager;
} 