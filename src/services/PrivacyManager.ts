// 隐私管理服务 - 数据加密、用户同意管理和本地处理

import { DataSecurityService } from './DataSecurityService'
import { DatabaseManager } from '../storage/DatabaseManager'

export interface PrivacySettings {
  dataCollection: boolean
  analytics: boolean
  poseTracking: boolean
  localProcessingOnly: boolean
  shareUsageStats: boolean
  autoDelete: {
    enabled: boolean
    retentionDays: number
  }
}

export interface ConsentRecord {
  id: string
  timestamp: number
  version: string
  settings: PrivacySettings
  ipAddress?: string
  userAgent: string
  consentGiven: boolean
  withdrawnAt?: number
}

export interface DataCategory {
  id: string
  name: string
  description: string
  essential: boolean // 是否为应用必需
  dataTypes: string[]
  retention: number // 保留天数
  processing: 'local' | 'remote' | 'both'
}

export interface PrivacyReport {
  dataCategories: DataCategory[]
  currentSettings: PrivacySettings
  dataRetention: {
    category: string
    itemCount: number
    oldestTimestamp: number
    totalSize: number // bytes
  }[]
  consentHistory: ConsentRecord[]
  lastAudit: number
  complianceStatus: {
    gdpr: boolean
    ccpa: boolean
    pipeda: boolean
  }
}

export class PrivacyManager {
  private securityService: DataSecurityService
  private dbManager: DatabaseManager
  private currentSettings: PrivacySettings
  private isInitialized: boolean = false
  private readonly CONSENT_VERSION = '1.0.0'

  // 数据分类定义
  private readonly DATA_CATEGORIES: DataCategory[] = [
    {
      id: 'user_profile',
      name: '用户档案',
      description: '用户名、设置和偏好',
      essential: true,
      dataTypes: ['username', 'preferences', 'settings'],
      retention: 365, // 1年
      processing: 'local'
    },
    {
      id: 'game_progress',
      name: '游戏进度',
      description: '等级、成就、作物数据',
      essential: true,
      dataTypes: ['level', 'experience', 'crops', 'achievements'],
      retention: 365,
      processing: 'local'
    },
    {
      id: 'pose_data',
      name: '姿态数据',
      description: '摄像头捕获的姿态分析数据',
      essential: false,
      dataTypes: ['pose_keypoints', 'focus_scores', 'posture_analysis'],
      retention: 30, // 30天
      processing: 'local'
    },
    {
      id: 'behavior_analytics',
      name: '行为分析',
      description: '使用模式和交互统计',
      essential: false,
      dataTypes: ['session_duration', 'feature_usage', 'error_logs'],
      retention: 90, // 90天
      processing: 'local'
    },
    {
      id: 'performance_data',
      name: '性能数据',
      description: '应用性能和错误报告',
      essential: false,
      dataTypes: ['frame_rates', 'memory_usage', 'crash_reports'],
      retention: 60, // 60天
      processing: 'local'
    }
  ]

  constructor(dbManager: DatabaseManager) {
    this.dbManager = dbManager
    this.securityService = new DataSecurityService(dbManager)
    this.currentSettings = this.getDefaultSettings()
  }

  /**
   * 初始化隐私管理器
   */
  async initialize(): Promise<boolean> {
    try {
      // 初始化安全服务
      await this.securityService.initialize({
        enableEncryption: true,
        autoBackup: true
      })

      // 加载现有设置
      await this.loadPrivacySettings()

      // 检查是否需要更新同意
      await this.checkConsentStatus()

      this.isInitialized = true
      console.log('PrivacyManager initialized successfully')
      return true
    } catch (error) {
      console.error('Failed to initialize PrivacyManager:', error)
      return false
    }
  }

  /**
   * 获取默认隐私设置
   */
  private getDefaultSettings(): PrivacySettings {
    return {
      dataCollection: false, // 默认关闭
      analytics: false,
      poseTracking: false,
      localProcessingOnly: true, // 默认只本地处理
      shareUsageStats: false,
      autoDelete: {
        enabled: true,
        retentionDays: 90
      }
    }
  }

  /**
   * 检查用户同意状态
   */
  private async checkConsentStatus(): Promise<void> {
    const consentRecords = await this.getConsentHistory()
    const latestConsent = consentRecords[0] // 假设按时间排序

    if (!latestConsent || latestConsent.version !== this.CONSENT_VERSION) {
      // 需要获取新的同意
      console.log('Consent required or outdated')
      this.emit('consent_required', { 
        reason: latestConsent ? 'version_outdated' : 'first_time' 
      })
    } else if (latestConsent.withdrawnAt) {
      // 用户已撤回同意
      console.log('User consent withdrawn')
      await this.applyMinimalSettings()
    }
  }

  /**
   * 记录用户同意
   */
  async recordConsent(settings: PrivacySettings, userAgent?: string): Promise<boolean> {
    try {
      const consentRecord: ConsentRecord = {
        id: `consent_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        timestamp: Date.now(),
        version: this.CONSENT_VERSION,
        settings,
        userAgent: userAgent || navigator.userAgent,
        consentGiven: true
      }

      // 保存同意记录
      await this.dbManager.logBehavior({
        type: 'consent_given',
        category: 'privacy',
        details: JSON.stringify(consentRecord)
      })

      // 更新当前设置
      this.currentSettings = { ...settings }
      await this.savePrivacySettings()

      console.log('User consent recorded successfully')
      return true
    } catch (error) {
      console.error('Failed to record consent:', error)
      return false
    }
  }

  /**
   * 撤回用户同意
   */
  async withdrawConsent(): Promise<boolean> {
    try {
      const consentRecords = await this.getConsentHistory()
      const latestConsent = consentRecords[0]

      if (latestConsent && !latestConsent.withdrawnAt) {
        // 记录撤回
        await this.dbManager.logBehavior({
          type: 'consent_withdrawn',
          category: 'privacy',
          details: JSON.stringify({
            originalConsentId: latestConsent.id,
            withdrawnAt: Date.now()
          })
        })

        // 应用最小设置
        await this.applyMinimalSettings()

        console.log('User consent withdrawn')
        return true
      }

      return false
    } catch (error) {
      console.error('Failed to withdraw consent:', error)
      return false
    }
  }

  /**
   * 应用最小隐私设置（仅保留必需功能）
   */
  private async applyMinimalSettings(): Promise<void> {
    this.currentSettings = {
      dataCollection: false,
      analytics: false,
      poseTracking: false,
      localProcessingOnly: true,
      shareUsageStats: false,
      autoDelete: {
        enabled: true,
        retentionDays: 30 // 更短的保留期
      }
    }

    await this.savePrivacySettings()
    
    // 清理非必需数据
    await this.cleanupNonEssentialData()
  }

  /**
   * 更新隐私设置
   */
  async updatePrivacySettings(newSettings: Partial<PrivacySettings>): Promise<boolean> {
    try {
      this.currentSettings = { ...this.currentSettings, ...newSettings }
      await this.savePrivacySettings()

      // 记录设置变更
      await this.dbManager.logBehavior({
        type: 'privacy_settings_updated',
        category: 'privacy',
        details: JSON.stringify(newSettings)
      })

      console.log('Privacy settings updated')
      return true
    } catch (error) {
      console.error('Failed to update privacy settings:', error)
      return false
    }
  }

  /**
   * 获取当前隐私设置
   */
  getPrivacySettings(): Readonly<PrivacySettings> {
    return Object.freeze({ ...this.currentSettings })
  }

  /**
   * 加密并保存敏感数据
   */
  async saveEncryptedData(key: string, data: any): Promise<boolean> {
    if (!this.currentSettings.dataCollection) {
      console.warn('Data collection disabled, cannot save data')
      return false
    }

    try {
      const encryptedData = await this.securityService.encryptData(JSON.stringify(data))
      if (!encryptedData) {
        throw new Error('Encryption failed')
      }

      await this.dbManager['storage'].save(`encrypted_${key}`, encryptedData)
      return true
    } catch (error) {
      console.error('Failed to save encrypted data:', error)
      return false
    }
  }

  /**
   * 加载并解密敏感数据
   */
  async loadEncryptedData(key: string): Promise<any | null> {
    try {
      const encryptedData = await this.dbManager['storage'].load(`encrypted_${key}`)
      if (!encryptedData || !encryptedData.encrypted || !encryptedData.iv) {
        return null
      }

      const decryptedData = await this.securityService.decryptData(
        encryptedData.encrypted, 
        encryptedData.iv
      )
      
      return decryptedData ? JSON.parse(decryptedData) : null
    } catch (error) {
      console.error('Failed to load encrypted data:', error)
      return null
    }
  }

  /**
   * 检查是否允许特定数据处理
   */
  isDataProcessingAllowed(category: string): boolean {
    switch (category) {
      case 'pose_data':
        return this.currentSettings.poseTracking
      case 'analytics':
        return this.currentSettings.analytics
      case 'behavior_analytics':
        return this.currentSettings.dataCollection
      default:
        return true // 允许基本功能
    }
  }

  /**
   * 确保所有处理都在本地进行
   */
  ensureLocalProcessing(): boolean {
    return this.currentSettings.localProcessingOnly
  }

  /**
   * 删除所有用户数据
   */
  async deleteAllUserData(): Promise<boolean> {
    try {
      // 创建删除前备份
      await this.securityService.createBackup('Pre-deletion backup')

      // 删除所有分类的数据
      for (const category of this.DATA_CATEGORIES) {
        await this.deleteDataCategory(category.id)
      }

      // 记录删除操作
      await this.dbManager.logBehavior({
        type: 'user_data_deleted',
        category: 'privacy',
        details: 'All user data deleted by request'
      })

      console.log('All user data deleted successfully')
      return true
    } catch (error) {
      console.error('Failed to delete user data:', error)
      return false
    }
  }

  /**
   * 删除特定分类的数据
   */
  async deleteDataCategory(categoryId: string): Promise<boolean> {
    try {
      const category = this.DATA_CATEGORIES.find(c => c.id === categoryId)
      if (!category) {
        console.warn(`Unknown data category: ${categoryId}`)
        return false
      }

      // 根据分类删除相应数据
      switch (categoryId) {
        case 'user_profile':
          await this.dbManager['storage'].remove('user_profile')
          break
        case 'game_progress':
          await this.dbManager['storage'].remove('game_progress')
          break
        case 'pose_data':
          // 删除姿态相关的行为记录
          const behaviors = await this.dbManager.getBehaviorRecords(undefined, 'focus', 1000)
          // 这里需要实现具体的删除逻辑
          break
        case 'behavior_analytics':
          await this.dbManager['storage'].remove('behavior_records')
          break
      }

      console.log(`Data category ${categoryId} deleted`)
      return true
    } catch (error) {
      console.error(`Failed to delete data category ${categoryId}:`, error)
      return false
    }
  }

  /**
   * 清理非必需数据
   */
  private async cleanupNonEssentialData(): Promise<void> {
    for (const category of this.DATA_CATEGORIES) {
      if (!category.essential) {
        await this.deleteDataCategory(category.id)
      }
    }
  }

  /**
   * 生成隐私报告
   */
  async generatePrivacyReport(): Promise<PrivacyReport> {
    const consentHistory = await this.getConsentHistory()
    const dataRetention = await this.analyzeDataRetention()

    return {
      dataCategories: this.DATA_CATEGORIES,
      currentSettings: this.currentSettings,
      dataRetention,
      consentHistory,
      lastAudit: Date.now(),
      complianceStatus: {
        gdpr: this.checkGDPRCompliance(),
        ccpa: this.checkCCPACompliance(),
        pipeda: this.checkPIPEDACompliance()
      }
    }
  }

  /**
   * 分析数据保留情况
   */
  private async analyzeDataRetention(): Promise<PrivacyReport['dataRetention']> {
    const retention: PrivacyReport['dataRetention'] = []

    for (const category of this.DATA_CATEGORIES) {
      // 这里需要实现具体的数据大小和计数逻辑
      // 简化实现
      retention.push({
        category: category.id,
        itemCount: 0, // 需要实际计算
        oldestTimestamp: Date.now() - (category.retention * 24 * 60 * 60 * 1000),
        totalSize: 0 // 需要实际计算
      })
    }

    return retention
  }

  /**
   * 检查GDPR合规性
   */
  private checkGDPRCompliance(): boolean {
    // 检查关键GDPR要求
    return (
      this.currentSettings.localProcessingOnly && // 数据本地化
      this.currentSettings.autoDelete.enabled && // 数据保留限制
      this.hasValidConsent() // 有效同意
    )
  }

  /**
   * 检查CCPA合规性
   */
  private checkCCPACompliance(): boolean {
    // 检查关键CCPA要求
    return (
      !this.currentSettings.shareUsageStats && // 不出售数据
      this.currentSettings.localProcessingOnly // 本地处理
    )
  }

  /**
   * 检查PIPEDA合规性
   */
  private checkPIPEDACompliance(): boolean {
    // 检查加拿大PIPEDA要求
    return (
      this.currentSettings.localProcessingOnly &&
      this.hasValidConsent() &&
      this.currentSettings.autoDelete.enabled
    )
  }

  /**
   * 检查是否有有效同意
   */
  private hasValidConsent(): boolean {
    // 简化检查，实际应该验证同意记录
    return this.currentSettings.dataCollection
  }

  /**
   * 获取同意历史
   */
  private async getConsentHistory(): Promise<ConsentRecord[]> {
    try {
      const behaviors = await this.dbManager.getBehaviorRecords(undefined, 'privacy', 100)
      const consentRecords: ConsentRecord[] = []

      behaviors.forEach(behavior => {
        if (behavior.event.type === 'consent_given' || behavior.event.type === 'consent_withdrawn') {
          try {
            const record = JSON.parse(behavior.event.details || '{}')
            consentRecords.push(record)
          } catch (error) {
            console.warn('Failed to parse consent record:', error)
          }
        }
      })

      return consentRecords.sort((a, b) => b.timestamp - a.timestamp)
    } catch (error) {
      console.error('Failed to get consent history:', error)
      return []
    }
  }

  /**
   * 保存隐私设置
   */
  private async savePrivacySettings(): Promise<void> {
    await this.dbManager['storage'].save('privacy_settings', this.currentSettings)
  }

  /**
   * 加载隐私设置
   */
  private async loadPrivacySettings(): Promise<void> {
    try {
      const savedSettings = await this.dbManager['storage'].load('privacy_settings')
      if (savedSettings) {
        this.currentSettings = { ...this.getDefaultSettings(), ...savedSettings }
      }
    } catch (error) {
      console.warn('Failed to load privacy settings, using defaults:', error)
    }
  }

  /**
   * 定期数据清理
   */
  async performScheduledCleanup(): Promise<void> {
    if (!this.currentSettings.autoDelete.enabled) {
      return
    }

    const cutoffTime = Date.now() - (this.currentSettings.autoDelete.retentionDays * 24 * 60 * 60 * 1000)

    // 清理过期数据
    for (const category of this.DATA_CATEGORIES) {
      if (category.retention > 0) {
        const categorycutoff = Date.now() - (category.retention * 24 * 60 * 60 * 1000)
        await this.cleanupCategoryData(category.id, categoryCutoff)
      }
    }
  }

  /**
   * 清理分类数据
   */
  private async cleanupCategoryData(categoryId: string, cutoffTime: number): Promise<void> {
    // 实现具体的数据清理逻辑
    console.log(`Cleaning up ${categoryId} data older than ${new Date(cutoffTime)}`)
  }

  /**
   * 事件发射器（简化版）
   */
  private emit(event: string, data?: any): void {
    // 这里可以集成实际的事件系统
    console.log(`Privacy event: ${event}`, data)
  }

  /**
   * 销毁服务
   */
  destroy(): void {
    this.securityService.destroy()
  }
} 