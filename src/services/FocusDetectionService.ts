import { FocusSettings, SensitivitySettings, DailyGoals, CustomGoal, Priority, GoalCategory, BreakType } from '../types/settings.types'

/**
 * 专注状态枚举
 */
export enum FocusState {
  FOCUSED = 'focused',
  DISTRACTED = 'distracted',
  BREAK = 'break',
  OFFLINE = 'offline'
}

/**
 * 检测类型枚举
 */
export enum DetectionType {
  MOVEMENT = 'movement',
  EYE_TRACKING = 'eye_tracking',
  POSTURE = 'posture',
  FACE_DETECTION = 'face_detection',
  HAND_GESTURES = 'hand_gestures'
}

/**
 * 专注会话数据
 */
export interface FocusSession {
  id: string
  startTime: Date
  endTime?: Date
  duration: number // 毫秒
  quality: number // 0-100
  distractions: DistractionEvent[]
  breaks: BreakEvent[]
  goalId?: string
  metadata: {
    averageHeadPose: number
    eyeTrackingAccuracy: number
    movementLevel: number
    postureScore: number
  }
}

/**
 * 干扰事件
 */
export interface DistractionEvent {
  id: string
  timestamp: Date
  type: DetectionType
  severity: number // 0-100
  duration: number // 毫秒
  recovered: boolean
  details: { [key: string]: any }
}

/**
 * 休息事件
 */
export interface BreakEvent {
  id: string
  startTime: Date
  endTime: Date
  type: BreakType
  planned: boolean
  quality: number // 0-100
}

/**
 * 专注统计数据
 */
export interface FocusStatistics {
  today: DailyStats
  week: WeeklyStats
  month: MonthlyStats
  lifetime: LifetimeStats
}

export interface DailyStats {
  date: Date
  totalFocusTime: number // 分钟
  sessionsCount: number
  averageSessionDuration: number // 分钟
  qualityScore: number // 0-100
  distractionCount: number
  breakCount: number
  goalsCompleted: number
  streak: number // 连续天数
}

export interface WeeklyStats {
  weekStart: Date
  totalFocusTime: number
  dailyAverage: number
  bestDay: { date: Date; focusTime: number }
  worstDay: { date: Date; focusTime: number }
  improvement: number // 相比上周的百分比
}

export interface MonthlyStats {
  monthStart: Date
  totalFocusTime: number
  averageWeekly: number
  progression: number[] // 每周的专注时间
  achievements: string[]
}

export interface LifetimeStats {
  totalFocusTime: number // 分钟
  totalSessions: number
  averageQuality: number
  longestSession: number // 分钟
  longestStreak: number // 天数
  levelsUnlocked: number
  totalPoints: number
}

/**
 * 检测结果
 */
export interface DetectionResult {
  type: DetectionType
  confidence: number // 0-1
  value: number // 检测到的数值
  threshold: number // 触发阈值
  triggered: boolean
  timestamp: Date
  metadata?: { [key: string]: any }
}

/**
 * 专注检测服务
 */
export class FocusDetectionService {
  private settings: FocusSettings = {
    enabled: true,
    sensitivity: {
      movement: 70,
      eyeTracking: 60,
      postureDetection: 65,
      faceDetection: 75,
      handGestures: 50
    },
    targets: {
      dailyGoals: {
        focusTime: 120, // 2小时
        sessionCount: 4,
        breakFrequency: 25, // 25分钟番茄工作法
        productivityScore: 80
      },
      customGoals: [],
      activeGoalId: null
    },
    detection: {
      enableFaceDetection: true,
      enablePostureDetection: true,
      enableEyeTracking: false,
      enableHandGestures: false,
      confidenceThreshold: 70,
      detectionInterval: 1000,
      smoothingFactor: 0.3
    },
    breaks: {
      enabled: true,
      frequency: 25,
      duration: 5,
      type: 'short' as any,
      reminders: true,
      forced: false,
      activities: []
    },
    rewards: {
      enabled: true,
      pointsPerMinute: 2,
      bonusMultiplier: 1.5,
      achievementRewards: true,
      levelUpRewards: true,
      streakRewards: true,
      customRewards: []
    }
  }

  private currentSession: FocusSession | null = null
  private currentState: FocusState = FocusState.OFFLINE
  private detectionInterval: NodeJS.Timeout | null = null
  private breakTimeout: NodeJS.Timeout | null = null
  
  private sessions: FocusSession[] = []
  private statistics: FocusStatistics | null = null
  
  private listeners: Map<string, Array<(data: any) => void>> = new Map()
  private isInitialized = false

  /**
   * 初始化服务
   */
  async initialize(): Promise<void> {
    try {
      // 加载历史数据
      await this.loadHistoricalData()
      
      // 计算统计数据
      this.updateStatistics()
      
      this.isInitialized = true
      this.emit('initialized', { statistics: this.statistics })
      
    } catch (error) {
      console.error('专注检测服务初始化失败:', error)
      throw error
    }
  }

  /**
   * 开始专注会话
   */
  startFocusSession(goalId?: string): FocusSession {
    if (this.currentSession) {
      this.endFocusSession()
    }

    const session: FocusSession = {
      id: this.generateSessionId(),
      startTime: new Date(),
      duration: 0,
      quality: 100,
      distractions: [],
      breaks: [],
      goalId,
      metadata: {
        averageHeadPose: 0,
        eyeTrackingAccuracy: 0,
        movementLevel: 0,
        postureScore: 100
      }
    }

    this.currentSession = session
    this.currentState = FocusState.FOCUSED
    
    // 开始检测
    this.startDetection()
    
    // 设置休息提醒
    if (this.settings.breaks.enabled) {
      this.scheduleBreakReminder()
    }

    this.emit('sessionStarted', session)
    return session
  }

  /**
   * 结束专注会话
   */
  endFocusSession(): FocusSession | null {
    if (!this.currentSession) return null

    this.currentSession.endTime = new Date()
    this.currentSession.duration = this.currentSession.endTime.getTime() - this.currentSession.startTime.getTime()
    
    // 计算会话质量
    this.calculateSessionQuality(this.currentSession)
    
    // 停止检测
    this.stopDetection()
    
    // 保存会话
    this.sessions.push(this.currentSession)
    
    // 更新统计数据
    this.updateStatistics()
    
    // 检查目标完成
    this.checkGoalCompletion(this.currentSession)

    const completedSession = this.currentSession
    this.currentSession = null
    this.currentState = FocusState.OFFLINE

    this.emit('sessionEnded', completedSession)
    return completedSession
  }

  /**
   * 开始检测
   */
  private startDetection(): void {
    if (!this.settings.enabled || this.detectionInterval) return

    this.detectionInterval = setInterval(() => {
      this.performDetection()
    }, this.settings.detection.detectionInterval)
  }

  /**
   * 停止检测
   */
  private stopDetection(): void {
    if (this.detectionInterval) {
      clearInterval(this.detectionInterval)
      this.detectionInterval = null
    }
    
    if (this.breakTimeout) {
      clearTimeout(this.breakTimeout)
      this.breakTimeout = null
    }
  }

  /**
   * 执行检测
   */
  private async performDetection(): Promise<void> {
    if (!this.currentSession || this.currentState === FocusState.BREAK) return

    const results: DetectionResult[] = []

    // 模拟各种检测
    if (this.settings.detection.enableFaceDetection) {
      results.push(await this.detectFace())
    }

    if (this.settings.detection.enablePostureDetection) {
      results.push(await this.detectPosture())
    }

    if (this.settings.detection.enableEyeTracking) {
      results.push(await this.detectEyeMovement())
    }

    if (this.settings.detection.enableHandGestures) {
      results.push(await this.detectHandGestures())
    }

    // 运动检测总是启用
    results.push(await this.detectMovement())

    // 分析结果
    this.analyzeDetectionResults(results)
  }

  /**
   * 人脸检测
   */
  private async detectFace(): Promise<DetectionResult> {
    // 模拟人脸检测
    const confidence = Math.random() * 0.3 + 0.7 // 70-100%
    const headPose = Math.random() * 20 - 10 // -10 to 10 degrees
    const threshold = (100 - this.settings.sensitivity.faceDetection) / 100
    
    return {
      type: DetectionType.FACE_DETECTION,
      confidence,
      value: Math.abs(headPose),
      threshold: threshold * 15,
      triggered: Math.abs(headPose) > threshold * 15,
      timestamp: new Date(),
      metadata: { headPose, confidence }
    }
  }

  /**
   * 姿态检测
   */
  private async detectPosture(): Promise<DetectionResult> {
    // 模拟姿态检测
    const postureScore = Math.random() * 30 + 70 // 70-100
    const threshold = this.settings.sensitivity.postureDetection
    
    return {
      type: DetectionType.POSTURE,
      confidence: 0.85,
      value: 100 - postureScore,
      threshold: 100 - threshold,
      triggered: postureScore < threshold,
      timestamp: new Date(),
      metadata: { postureScore }
    }
  }

  /**
   * 眼动追踪
   */
  private async detectEyeMovement(): Promise<DetectionResult> {
    // 模拟眼动追踪
    const gazeStability = Math.random() * 40 + 60 // 60-100
    const threshold = this.settings.sensitivity.eyeTracking
    
    return {
      type: DetectionType.EYE_TRACKING,
      confidence: 0.75,
      value: 100 - gazeStability,
      threshold: 100 - threshold,
      triggered: gazeStability < threshold,
      timestamp: new Date(),
      metadata: { gazeStability }
    }
  }

  /**
   * 手势检测
   */
  private async detectHandGestures(): Promise<DetectionResult> {
    // 模拟手势检测
    const handActivity = Math.random() * 50 // 0-50
    const threshold = this.settings.sensitivity.handGestures
    
    return {
      type: DetectionType.HAND_GESTURES,
      confidence: 0.70,
      value: handActivity,
      threshold: threshold,
      triggered: handActivity > threshold,
      timestamp: new Date(),
      metadata: { handActivity }
    }
  }

  /**
   * 运动检测
   */
  private async detectMovement(): Promise<DetectionResult> {
    // 模拟运动检测
    const movementLevel = Math.random() * 80 // 0-80
    const threshold = this.settings.sensitivity.movement
    
    return {
      type: DetectionType.MOVEMENT,
      confidence: 0.90,
      value: movementLevel,
      threshold: threshold,
      triggered: movementLevel > threshold,
      timestamp: new Date(),
      metadata: { movementLevel }
    }
  }

  /**
   * 分析检测结果
   */
  private analyzeDetectionResults(results: DetectionResult[]): void {
    if (!this.currentSession) return

    let distractionDetected = false
    let severitySum = 0
    let triggerCount = 0

    for (const result of results) {
      if (result.triggered) {
        distractionDetected = true
        triggerCount++
        severitySum += (result.value / result.threshold) * 100
        
        // 记录干扰事件
        this.recordDistraction(result)
      }
      
      // 更新会话元数据
      this.updateSessionMetadata(result)
    }

    // 更新专注状态
    if (distractionDetected && this.currentState === FocusState.FOCUSED) {
      this.currentState = FocusState.DISTRACTED
      this.emit('distractionDetected', {
        session: this.currentSession,
        results,
        severity: severitySum / triggerCount
      })
    } else if (!distractionDetected && this.currentState === FocusState.DISTRACTED) {
      this.currentState = FocusState.FOCUSED
      this.emit('focusRestored', {
        session: this.currentSession,
        results
      })
    }
  }

  /**
   * 记录干扰事件
   */
  private recordDistraction(result: DetectionResult): void {
    if (!this.currentSession) return

    const distraction: DistractionEvent = {
      id: this.generateId(),
      timestamp: result.timestamp,
      type: result.type,
      severity: (result.value / result.threshold) * 100,
      duration: 0, // 将在后续更新
      recovered: false,
      details: result.metadata || {}
    }

    this.currentSession.distractions.push(distraction)
  }

  /**
   * 更新会话元数据
   */
  private updateSessionMetadata(result: DetectionResult): void {
    if (!this.currentSession) return

    const metadata = this.currentSession.metadata

    switch (result.type) {
      case DetectionType.FACE_DETECTION:
        if (result.metadata?.headPose !== undefined) {
          metadata.averageHeadPose = (metadata.averageHeadPose + Math.abs(result.metadata.headPose)) / 2
        }
        break
      case DetectionType.EYE_TRACKING:
        if (result.metadata?.gazeStability !== undefined) {
          metadata.eyeTrackingAccuracy = (metadata.eyeTrackingAccuracy + result.metadata.gazeStability) / 2
        }
        break
      case DetectionType.MOVEMENT:
        if (result.metadata?.movementLevel !== undefined) {
          metadata.movementLevel = (metadata.movementLevel + result.metadata.movementLevel) / 2
        }
        break
      case DetectionType.POSTURE:
        if (result.metadata?.postureScore !== undefined) {
          metadata.postureScore = (metadata.postureScore + result.metadata.postureScore) / 2
        }
        break
    }
  }

  /**
   * 计算会话质量
   */
  private calculateSessionQuality(session: FocusSession): void {
    const baseQuality = 100
    const distractionPenalty = session.distractions.length * 5
    const durationBonus = Math.min(session.duration / 1000 / 60 / 60, 2) * 10 // 最多20分加成
    
    let qualityScore = baseQuality - distractionPenalty + durationBonus
    
    // 根据元数据调整
    qualityScore *= (session.metadata.postureScore / 100)
    qualityScore *= Math.max(0.7, session.metadata.eyeTrackingAccuracy / 100)
    
    session.quality = Math.max(0, Math.min(100, qualityScore))
  }

  /**
   * 安排休息提醒
   */
  private scheduleBreakReminder(): void {
    if (this.breakTimeout) {
      clearTimeout(this.breakTimeout)
    }

    this.breakTimeout = setTimeout(() => {
      this.triggerBreakReminder()
    }, this.settings.breaks.frequency * 60 * 1000)
  }

  /**
   * 触发休息提醒
   */
  private triggerBreakReminder(): void {
    this.emit('breakReminder', {
      type: this.settings.breaks.type,
      duration: this.settings.breaks.duration,
      forced: this.settings.breaks.forced
    })

    if (this.settings.breaks.forced) {
      this.startBreak(this.settings.breaks.type)
    }
  }

  /**
   * 开始休息
   */
  startBreak(type: BreakType): void {
    if (!this.currentSession) return

    this.currentState = FocusState.BREAK
    
    const breakEvent: BreakEvent = {
      id: this.generateId(),
      startTime: new Date(),
      endTime: new Date(Date.now() + this.settings.breaks.duration * 60 * 1000),
      type,
      planned: true,
      quality: 0
    }

    this.currentSession.breaks.push(breakEvent)
    this.emit('breakStarted', breakEvent)

    // 自动结束休息
    setTimeout(() => {
      this.endBreak()
    }, this.settings.breaks.duration * 60 * 1000)
  }

  /**
   * 结束休息
   */
  endBreak(): void {
    if (this.currentState !== FocusState.BREAK || !this.currentSession) return

    const lastBreak = this.currentSession.breaks[this.currentSession.breaks.length - 1]
    if (lastBreak) {
      lastBreak.endTime = new Date()
      lastBreak.quality = Math.random() * 40 + 60 // 60-100
    }

    this.currentState = FocusState.FOCUSED
    this.emit('breakEnded', lastBreak)

    // 重新安排下次休息
    if (this.settings.breaks.enabled) {
      this.scheduleBreakReminder()
    }
  }

  /**
   * 创建自定义目标
   */
  createCustomGoal(goal: Omit<CustomGoal, 'id' | 'createdAt' | 'updatedAt'>): CustomGoal {
    const newGoal: CustomGoal = {
      id: this.generateId(),
      ...goal,
      createdAt: new Date(),
      updatedAt: new Date()
    }

    this.settings.targets.customGoals.push(newGoal)
    this.emit('goalCreated', newGoal)
    
    return newGoal
  }

  /**
   * 更新自定义目标
   */
  updateCustomGoal(goalId: string, updates: Partial<CustomGoal>): CustomGoal | null {
    const goalIndex = this.settings.targets.customGoals.findIndex(g => g.id === goalId)
    if (goalIndex === -1) return null

    this.settings.targets.customGoals[goalIndex] = {
      ...this.settings.targets.customGoals[goalIndex],
      ...updates,
      updatedAt: new Date()
    }

    const updatedGoal = this.settings.targets.customGoals[goalIndex]
    this.emit('goalUpdated', updatedGoal)
    
    return updatedGoal
  }

  /**
   * 删除自定义目标
   */
  deleteCustomGoal(goalId: string): boolean {
    const goalIndex = this.settings.targets.customGoals.findIndex(g => g.id === goalId)
    if (goalIndex === -1) return false

    const deletedGoal = this.settings.targets.customGoals.splice(goalIndex, 1)[0]
    
    // 如果删除的是活动目标，清除活动目标ID
    if (this.settings.targets.activeGoalId === goalId) {
      this.settings.targets.activeGoalId = null
    }

    this.emit('goalDeleted', deletedGoal)
    return true
  }

  /**
   * 设置活动目标
   */
  setActiveGoal(goalId: string | null): void {
    this.settings.targets.activeGoalId = goalId
    this.emit('activeGoalChanged', goalId)
  }

  /**
   * 检查目标完成
   */
  private checkGoalCompletion(session: FocusSession): void {
    const today = this.getTodayStats()
    
    // 检查每日目标
    const dailyGoals = this.settings.targets.dailyGoals
    if (today.totalFocusTime >= dailyGoals.focusTime) {
      this.emit('goalCompleted', { type: 'daily', goal: 'focusTime', value: today.totalFocusTime })
    }
    
    if (today.sessionsCount >= dailyGoals.sessionCount) {
      this.emit('goalCompleted', { type: 'daily', goal: 'sessionCount', value: today.sessionsCount })
    }

    // 检查自定义目标
    if (session.goalId) {
      const goal = this.settings.targets.customGoals.find(g => g.id === session.goalId)
      if (goal && session.duration >= goal.duration * 60 * 1000) {
        this.emit('goalCompleted', { type: 'custom', goal, session })
      }
    }
  }

  /**
   * 更新统计数据
   */
  private updateStatistics(): void {
    this.statistics = {
      today: this.getTodayStats(),
      week: this.getWeekStats(),
      month: this.getMonthStats(),
      lifetime: this.getLifetimeStats()
    }

    this.emit('statisticsUpdated', this.statistics)
  }

  /**
   * 获取今日统计
   */
  private getTodayStats(): DailyStats {
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    
    const todaySessions = this.sessions.filter(s => 
      s.startTime >= today && s.endTime && s.endTime <= new Date()
    )

    const totalFocusTime = todaySessions.reduce((sum, s) => sum + (s.duration / 1000 / 60), 0)
    const averageQuality = todaySessions.reduce((sum, s) => sum + s.quality, 0) / Math.max(1, todaySessions.length)

    return {
      date: today,
      totalFocusTime,
      sessionsCount: todaySessions.length,
      averageSessionDuration: totalFocusTime / Math.max(1, todaySessions.length),
      qualityScore: averageQuality,
      distractionCount: todaySessions.reduce((sum, s) => sum + s.distractions.length, 0),
      breakCount: todaySessions.reduce((sum, s) => sum + s.breaks.length, 0),
      goalsCompleted: 0, // TODO: 实现目标完成统计
      streak: this.calculateStreak()
    }
  }

  /**
   * 获取本周统计
   */
  private getWeekStats(): WeeklyStats {
    // 简化实现
    const weekStart = new Date()
    weekStart.setDate(weekStart.getDate() - weekStart.getDay())
    weekStart.setHours(0, 0, 0, 0)

    return {
      weekStart,
      totalFocusTime: 0,
      dailyAverage: 0,
      bestDay: { date: new Date(), focusTime: 0 },
      worstDay: { date: new Date(), focusTime: 0 },
      improvement: 0
    }
  }

  /**
   * 获取本月统计
   */
  private getMonthStats(): MonthlyStats {
    // 简化实现
    return {
      monthStart: new Date(),
      totalFocusTime: 0,
      averageWeekly: 0,
      progression: [],
      achievements: []
    }
  }

  /**
   * 获取总体统计
   */
  private getLifetimeStats(): LifetimeStats {
    const totalFocusTime = this.sessions.reduce((sum, s) => sum + (s.duration / 1000 / 60), 0)
    const averageQuality = this.sessions.reduce((sum, s) => sum + s.quality, 0) / Math.max(1, this.sessions.length)
    const longestSession = Math.max(...this.sessions.map(s => s.duration / 1000 / 60), 0)

    return {
      totalFocusTime,
      totalSessions: this.sessions.length,
      averageQuality,
      longestSession,
      longestStreak: 0, // TODO: 实现
      levelsUnlocked: Math.floor(totalFocusTime / 60), // 每小时一个等级
      totalPoints: Math.floor(totalFocusTime * this.settings.rewards.pointsPerMinute)
    }
  }

  /**
   * 计算连续天数
   */
  private calculateStreak(): number {
    // 简化实现
    return 1
  }

  /**
   * 加载历史数据
   */
  private async loadHistoricalData(): Promise<void> {
    // 从本地存储加载数据
    try {
      const savedSessions = localStorage.getItem('focus_sessions')
      if (savedSessions) {
        this.sessions = JSON.parse(savedSessions).map((s: any) => ({
          ...s,
          startTime: new Date(s.startTime),
          endTime: s.endTime ? new Date(s.endTime) : undefined
        }))
      }
    } catch (error) {
      console.warn('加载历史数据失败:', error)
    }
  }

  /**
   * 保存数据
   */
  private saveData(): void {
    try {
      localStorage.setItem('focus_sessions', JSON.stringify(this.sessions))
    } catch (error) {
      console.warn('保存数据失败:', error)
    }
  }

  /**
   * 工具方法
   */
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private generateId(): string {
    return `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 更新设置
   */
  updateSettings(newSettings: Partial<FocusSettings>): void {
    this.settings = { ...this.settings, ...newSettings }
    this.emit('settingsUpdated', this.settings)
  }

  /**
   * 获取当前状态
   */
  getCurrentState(): FocusState {
    return this.currentState
  }

  getCurrentSession(): FocusSession | null {
    return this.currentSession
  }

  getStatistics(): FocusStatistics | null {
    return this.statistics
  }

  getSettings(): FocusSettings {
    return { ...this.settings }
  }

  isReady(): boolean {
    return this.isInitialized
  }

  /**
   * 事件管理
   */
  addEventListener(event: string, listener: (data: any) => void): void {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, [])
    }
    this.listeners.get(event)!.push(listener)
  }

  removeEventListener(event: string, listener: (data: any) => void): void {
    const eventListeners = this.listeners.get(event)
    if (eventListeners) {
      const index = eventListeners.indexOf(listener)
      if (index > -1) {
        eventListeners.splice(index, 1)
      }
    }
  }

  private emit(event: string, data: any): void {
    const eventListeners = this.listeners.get(event)
    if (eventListeners) {
      eventListeners.forEach(listener => {
        try {
          listener(data)
        } catch (error) {
          console.error(`专注事件监听器错误 (${event}):`, error)
        }
      })
    }
  }

  /**
   * 销毁服务
   */
  destroy(): void {
    this.stopDetection()
    this.saveData()
    this.listeners.clear()
    this.currentSession = null
    this.isInitialized = false
  }
}

// 单例实例
export const focusDetectionService = new FocusDetectionService() 