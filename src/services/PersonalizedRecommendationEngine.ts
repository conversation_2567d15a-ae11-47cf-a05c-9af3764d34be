// 个性化建议引擎
// 基于用户行为分析和趋势预测生成个性化改进建议

import DataAnalysisEngine, { 
  PerformanceInsights, 
  HabitFormationAnalysis, 
  ProductivityMetrics,
  BehaviorPrediction 
} from './DataAnalysisEngine'
import TrendAnalysisEngine, { 
  TrendAnalysisResult, 
  RiskAssessment, 
  OpportunityAnalysis 
} from './TrendAnalysisEngine'
import DataAnalyticsCollector, { DailyData } from './DataAnalyticsCollector'

// 建议类型枚举
export enum RecommendationType {
  IMMEDIATE = 'immediate',      // 立即行动建议
  SHORT_TERM = 'short_term',    // 短期改进建议
  LONG_TERM = 'long_term',      // 长期发展建议
  HABIT_FORMING = 'habit_forming', // 习惯养成建议
  PRODUCTIVITY = 'productivity',   // 生产力提升建议
  WELLNESS = 'wellness',          // 健康福祉建议
  MOTIVATION = 'motivation'       // 激励类建议
}

// 建议优先级枚举
export enum RecommendationPriority {
  CRITICAL = 'critical',
  HIGH = 'high',
  MEDIUM = 'medium',
  LOW = 'low'
}

// 建议难度枚举
export enum RecommendationDifficulty {
  EASY = 'easy',
  MEDIUM = 'medium',
  HARD = 'hard'
}

// 个性化建议接口
export interface PersonalizedRecommendation {
  id: string
  type: RecommendationType
  priority: RecommendationPriority
  difficulty: RecommendationDifficulty
  title: string
  description: string
  reasoning: string
  expectedOutcome: string
  actionSteps: string[]
  timeframe: number // 预计见效时间（天）
  resources: string[]
  successMetrics: string[]
  category: string
  confidence: number // 建议的置信度 0-1
  personalizationScore: number // 个性化程度 0-1
  createdAt: Date
  effectiveUntil?: Date
  tags: string[]
}

// 建议生成配置
export interface RecommendationConfig {
  maxRecommendations: number
  priorityWeights: {
    critical: number
    high: number
    medium: number
    low: number
  }
  difficultyDistribution: {
    easy: number
    medium: number
    hard: number
  }
  typeDistribution: {
    [key in RecommendationType]: number
  }
  personalizationThreshold: number
  confidenceThreshold: number
}

// 用户上下文信息
export interface UserContext {
  userId: string
  currentStreak: number
  averageProductivity: number
  preferredDifficulty: RecommendationDifficulty
  focusAreas: string[]
  timeConstraints: {
    availableTimePerDay: number
    preferredTimeSlots: string[]
  }
  personalityTraits: {
    selfMotivation: number
    consistencyPreference: number
    challengeAppetite: number
    socialNeed: number
  }
  goals: {
    shortTerm: string[]
    longTerm: string[]
  }
}

// 建议生成结果
export interface RecommendationGenerationResult {
  recommendations: PersonalizedRecommendation[]
  totalGenerated: number
  filteringResults: {
    beforeFiltering: number
    afterPriorityFilter: number
    afterConfidenceFilter: number
    afterPersonalizationFilter: number
  }
  generationMetadata: {
    analysisDate: Date
    dataQuality: number
    userContextComplete: boolean
    algorithmsUsed: string[]
  }
}

// 个性化建议引擎类
export class PersonalizedRecommendationEngine {
  private analysisEngine: DataAnalysisEngine
  private trendEngine: TrendAnalysisEngine
  private dataCollector: DataAnalyticsCollector

  // 默认配置
  private defaultConfig: RecommendationConfig = {
    maxRecommendations: 10,
    priorityWeights: {
      critical: 1.0,
      high: 0.8,
      medium: 0.6,
      low: 0.4
    },
    difficultyDistribution: {
      easy: 0.4,
      medium: 0.4,
      hard: 0.2
    },
    typeDistribution: {
      [RecommendationType.IMMEDIATE]: 0.2,
      [RecommendationType.SHORT_TERM]: 0.3,
      [RecommendationType.LONG_TERM]: 0.2,
      [RecommendationType.HABIT_FORMING]: 0.15,
      [RecommendationType.PRODUCTIVITY]: 0.1,
      [RecommendationType.WELLNESS]: 0.03,
      [RecommendationType.MOTIVATION]: 0.02
    },
    personalizationThreshold: 0.6,
    confidenceThreshold: 0.7
  }

  constructor(
    analysisEngine: DataAnalysisEngine,
    trendEngine: TrendAnalysisEngine,
    dataCollector: DataAnalyticsCollector
  ) {
    this.analysisEngine = analysisEngine
    this.trendEngine = trendEngine
    this.dataCollector = dataCollector
  }

  // 生成个性化建议的主要方法
  public async generateRecommendations(
    userId: string,
    userContext: UserContext,
    config: Partial<RecommendationConfig> = {}
  ): Promise<RecommendationGenerationResult> {
    try {
      console.log(`开始为用户 ${userId} 生成个性化建议`)

      const finalConfig = { ...this.defaultConfig, ...config }
      
      // 获取用户数据分析结果
      const performanceInsights = await this.analysisEngine.generatePerformanceInsights(userId)
      const habitAnalysis = await this.analysisEngine.analyzeHabitFormation(userId)
      const productivityMetrics = await this.analysisEngine.calculateProductivityMetrics(userId)
      const behaviorPredictions = await this.analysisEngine.generateBehaviorPredictions(userId)
      
      // 获取趋势分析结果
      const trendAnalysis = await this.trendEngine.generateTrendAnalysis(userId)
      
      // 生成不同类型的建议
      const allRecommendations: PersonalizedRecommendation[] = []
      
      // 立即行动建议
      allRecommendations.push(
        ...await this.generateImmediateRecommendations(userContext, performanceInsights, trendAnalysis)
      )
      
      // 短期改进建议
      allRecommendations.push(
        ...await this.generateShortTermRecommendations(userContext, habitAnalysis, trendAnalysis)
      )
      
      // 长期发展建议
      allRecommendations.push(
        ...await this.generateLongTermRecommendations(userContext, productivityMetrics, trendAnalysis)
      )
      
      // 习惯养成建议
      allRecommendations.push(
        ...await this.generateHabitFormingRecommendations(userContext, habitAnalysis, behaviorPredictions)
      )
      
      // 生产力提升建议
      allRecommendations.push(
        ...await this.generateProductivityRecommendations(userContext, productivityMetrics, trendAnalysis)
      )
      
      // 健康福祉建议
      allRecommendations.push(
        ...await this.generateWellnessRecommendations(userContext, performanceInsights, trendAnalysis)
      )
      
      // 激励建议
      allRecommendations.push(
        ...await this.generateMotivationRecommendations(userContext, trendAnalysis)
      )

      // 过滤和排序建议
      const filteringResults = {
        beforeFiltering: allRecommendations.length,
        afterPriorityFilter: 0,
        afterConfidenceFilter: 0,
        afterPersonalizationFilter: 0
      }

      // 置信度过滤
      const confidenceFiltered = allRecommendations.filter(rec => 
        rec.confidence >= finalConfig.confidenceThreshold
      )
      filteringResults.afterConfidenceFilter = confidenceFiltered.length

      // 个性化程度过滤
      const personalizationFiltered = confidenceFiltered.filter(rec => 
        rec.personalizationScore >= finalConfig.personalizationThreshold
      )
      filteringResults.afterPersonalizationFilter = personalizationFiltered.length

      // 优先级排序
      const prioritySorted = this.sortByPriorityAndRelevance(personalizationFiltered, finalConfig)
      filteringResults.afterPriorityFilter = prioritySorted.length

      // 限制数量
      const finalRecommendations = prioritySorted.slice(0, finalConfig.maxRecommendations)

      return {
        recommendations: finalRecommendations,
        totalGenerated: finalRecommendations.length,
        filteringResults,
        generationMetadata: {
          analysisDate: new Date(),
          dataQuality: trendAnalysis.dataQuality,
          userContextComplete: this.validateUserContext(userContext),
          algorithmsUsed: [
            'performance_analysis',
            'habit_formation_analysis', 
            'trend_prediction',
            'personalization_scoring',
            'priority_ranking'
          ]
        }
      }
    } catch (error) {
      console.error('生成个性化建议失败:', error)
      throw new Error(`建议生成失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  // 生成立即行动建议
  private async generateImmediateRecommendations(
    userContext: UserContext,
    performanceInsights: PerformanceInsights,
    trendAnalysis: TrendAnalysisResult
  ): Promise<PersonalizedRecommendation[]> {
    const recommendations: PersonalizedRecommendation[] = []

    // 基于当前表现的立即建议
    if (performanceInsights.consistencyScore < 0.7) {
      recommendations.push({
        id: `immediate_consistency_${Date.now()}`,
        type: RecommendationType.IMMEDIATE,
        priority: RecommendationPriority.HIGH,
        difficulty: RecommendationDifficulty.EASY,
        title: '今日专注一致性提升',
        description: '您今天的专注一致性有所下降，建议立即采取行动来恢复节奏',
        reasoning: `当前一致性分数为 ${(performanceInsights.consistencyScore * 100).toFixed(1)}%，低于理想水平`,
        expectedOutcome: '在1-2小时内恢复专注状态，提升当日整体表现',
        actionSteps: [
          '设置一个25分钟的番茄钟专注会话',
          '选择一个相对简单的任务开始',
          '确保环境无干扰（静音手机、关闭无关应用）',
          '专注结束后奖励自己5分钟休息'
        ],
        timeframe: 1,
        resources: ['番茄钟应用', '任务清单', '安静环境'],
        successMetrics: ['完成25分钟专注会话', '任务进度提升', '专注质量评分>80%'],
        category: '专注力恢复',
        confidence: 0.85,
        personalizationScore: this.calculatePersonalizationScore(userContext, 'immediate_focus'),
        createdAt: new Date(),
        tags: ['专注力', '一致性', '立即行动']
      })
    }

    // 基于风险评估的立即建议
    const highRisks = trendAnalysis.risks.filter(risk => 
      risk.severity === 'high' || risk.severity === 'critical'
    )
    
    for (const risk of highRisks.slice(0, 2)) {
      recommendations.push({
        id: `immediate_risk_${risk.type}_${Date.now()}`,
        type: RecommendationType.IMMEDIATE,
        priority: risk.severity === 'critical' ? RecommendationPriority.CRITICAL : RecommendationPriority.HIGH,
        difficulty: RecommendationDifficulty.MEDIUM,
        title: `紧急应对：${this.translateRiskType(risk.type)}`,
        description: risk.description,
        reasoning: `风险概率 ${(risk.probability * 100).toFixed(1)}%，需要立即采取行动`,
        expectedOutcome: risk.mitigation[0] || '降低风险影响',
        actionSteps: risk.mitigation.slice(0, 4),
        timeframe: risk.timeframe,
        resources: ['行为追踪工具', '提醒设置', '支持资源'],
        successMetrics: ['风险指标下降', '预防措施执行率>90%'],
        category: '风险管理',
        confidence: risk.probability,
        personalizationScore: this.calculatePersonalizationScore(userContext, 'risk_mitigation'),
        createdAt: new Date(),
        tags: ['风险管理', risk.type, '紧急']
      })
    }

    return recommendations
  }

  // 生成短期改进建议
  private async generateShortTermRecommendations(
    userContext: UserContext,
    habitAnalysis: HabitFormationAnalysis,
    trendAnalysis: TrendAnalysisResult
  ): Promise<PersonalizedRecommendation[]> {
    const recommendations: PersonalizedRecommendation[] = []

    // 基于习惯强度的短期建议
    if (habitAnalysis.habitStrength < 0.8) {
      recommendations.push({
        id: `short_term_habit_${Date.now()}`,
        type: RecommendationType.SHORT_TERM,
        priority: RecommendationPriority.HIGH,
        difficulty: RecommendationDifficulty.MEDIUM,
        title: '7天习惯强化计划',
        description: '通过一周的集中训练来增强您的核心自律习惯',
        reasoning: `当前习惯强度为 ${(habitAnalysis.habitStrength * 100).toFixed(1)}%，有明显提升空间`,
        expectedOutcome: '一周后习惯强度提升15-25%，自律行为更加稳定',
        actionSteps: [
          '每天同一时间进行核心习惯练习',
          '设置每日完成度检查提醒',
          '记录每日习惯执行质量评分',
          '周中进行一次进度回顾和调整',
          '周末总结经验并制定下周计划'
        ],
        timeframe: 7,
        resources: ['习惯追踪应用', '日程提醒', '进度记录表'],
        successMetrics: ['7天习惯完成率>85%', '习惯强度提升>15%', '自我评价改善'],
        category: '习惯养成',
        confidence: 0.8,
        personalizationScore: this.calculatePersonalizationScore(userContext, 'habit_strengthening'),
        createdAt: new Date(),
        tags: ['习惯强化', '7天计划', '短期目标']
      })
    }

    // 基于机会分析的短期建议
    const opportunities = trendAnalysis.opportunities.filter(opp => 
      opp.timeframe <= 14 && opp.potential === 'high'
    )

    for (const opportunity of opportunities.slice(0, 2)) {
      recommendations.push({
        id: `short_term_opportunity_${opportunity.type}_${Date.now()}`,
        type: RecommendationType.SHORT_TERM,
        priority: RecommendationPriority.MEDIUM,
        difficulty: RecommendationDifficulty.MEDIUM,
        title: `抓住机会：${this.translateOpportunityType(opportunity.type)}`,
        description: opportunity.description,
        reasoning: `成功概率 ${(opportunity.probability * 100).toFixed(1)}%，预期收益显著`,
        expectedOutcome: opportunity.expectedBenefit,
        actionSteps: opportunity.actionPlan.slice(0, 5),
        timeframe: opportunity.timeframe,
        resources: ['目标设定工具', '进度监控', '反馈机制'],
        successMetrics: ['目标达成率>80%', '预期收益实现>70%'],
        category: '机会抓取',
        confidence: opportunity.probability,
        personalizationScore: this.calculatePersonalizationScore(userContext, 'opportunity_taking'),
        createdAt: new Date(),
        tags: ['机会抓取', opportunity.type, '短期收益']
      })
    }

    return recommendations
  }

  // 生成长期发展建议
  private async generateLongTermRecommendations(
    userContext: UserContext,
    productivityMetrics: ProductivityMetrics,
    trendAnalysis: TrendAnalysisResult
  ): Promise<PersonalizedRecommendation[]> {
    const recommendations: PersonalizedRecommendation[] = []

    // 基于生产力趋势的长期建议
    if (productivityMetrics.overallScore < 0.75) {
      recommendations.push({
        id: `long_term_productivity_${Date.now()}`,
        type: RecommendationType.LONG_TERM,
        priority: RecommendationPriority.MEDIUM,
        difficulty: RecommendationDifficulty.HARD,
        title: '90天生产力系统重构',
        description: '通过系统性改进来大幅提升个人生产力和效率',
        reasoning: `当前生产力评分 ${(productivityMetrics.overallScore * 100).toFixed(1)}%，需要系统性提升`,
        expectedOutcome: '三个月后生产力提升30-50%，工作效率显著改善',
        actionSteps: [
          '第1-2周：评估当前工作流程和时间分配',
          '第3-4周：设计个人生产力系统和工具链',
          '第5-8周：逐步实施新系统，调整和优化',
          '第9-12周：深化系统使用，形成自动化习惯'
        ],
        timeframe: 90,
        resources: ['生产力工具', '时间管理课程', '效率评估工具'],
        successMetrics: ['生产力评分提升>30%', '时间利用率>85%', '系统化程度>90%'],
        category: '系统优化',
        confidence: 0.75,
        personalizationScore: this.calculatePersonalizationScore(userContext, 'system_building'),
        createdAt: new Date(),
        tags: ['生产力', '系统重构', '长期发展']
      })
    }

    return recommendations
  }

  // 生成习惯养成建议（其他方法类似实现...）
  private async generateHabitFormingRecommendations(
    userContext: UserContext,
    habitAnalysis: HabitFormationAnalysis,
    behaviorPredictions: BehaviorPrediction[]
  ): Promise<PersonalizedRecommendation[]> {
    // 实现习惯养成建议逻辑
    return []
  }

  private async generateProductivityRecommendations(
    userContext: UserContext,
    productivityMetrics: ProductivityMetrics,
    trendAnalysis: TrendAnalysisResult
  ): Promise<PersonalizedRecommendation[]> {
    // 实现生产力建议逻辑
    return []
  }

  private async generateWellnessRecommendations(
    userContext: UserContext,
    performanceInsights: PerformanceInsights,
    trendAnalysis: TrendAnalysisResult
  ): Promise<PersonalizedRecommendation[]> {
    // 实现健康福祉建议逻辑
    return []
  }

  private async generateMotivationRecommendations(
    userContext: UserContext,
    trendAnalysis: TrendAnalysisResult
  ): Promise<PersonalizedRecommendation[]> {
    // 实现激励建议逻辑
    return []
  }

  // 辅助方法
  private calculatePersonalizationScore(userContext: UserContext, recommendationType: string): number {
    // 基于用户上下文计算个性化程度
    let score = 0.5 // 基础分

    // 根据用户偏好调整
    if (userContext.preferredDifficulty) score += 0.1
    if (userContext.focusAreas.length > 0) score += 0.1
    if (userContext.goals.shortTerm.length > 0) score += 0.1
    if (userContext.personalityTraits) score += 0.1

    return Math.min(1.0, score)
  }

  private sortByPriorityAndRelevance(
    recommendations: PersonalizedRecommendation[],
    config: RecommendationConfig
  ): PersonalizedRecommendation[] {
    return recommendations.sort((a, b) => {
      // 优先级权重
      const aPriorityWeight = config.priorityWeights[a.priority]
      const bPriorityWeight = config.priorityWeights[b.priority]
      
      // 综合评分
      const aScore = aPriorityWeight * a.confidence * a.personalizationScore
      const bScore = bPriorityWeight * b.confidence * b.personalizationScore
      
      return bScore - aScore
    })
  }

  private validateUserContext(userContext: UserContext): boolean {
    return !!(
      userContext.userId &&
      userContext.preferredDifficulty &&
      userContext.focusAreas.length > 0 &&
      userContext.personalityTraits
    )
  }

  private translateRiskType(riskType: string): string {
    const translations: Record<string, string> = {
      'habit_decline': '习惯衰退',
      'consistency_drop': '一致性下降',
      'motivation_loss': '动机缺失',
      'burnout_risk': '倦怠风险',
      'plateau_effect': '平台期效应'
    }
    return translations[riskType] || riskType
  }

  private translateOpportunityType(opportunityType: string): string {
    const translations: Record<string, string> = {
      'habit_growth': '习惯增长',
      'consistency_boost': '一致性提升',
      'new_milestone': '新里程碑',
      'efficiency_gain': '效率提升',
      'motivation_peak': '动机高峰'
    }
    return translations[opportunityType] || opportunityType
  }
}

export default PersonalizedRecommendationEngine 