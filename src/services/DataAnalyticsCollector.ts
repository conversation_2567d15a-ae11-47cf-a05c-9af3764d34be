// 数据分析收集器
// 专门为数据分析和报告功能收集所需的用户行为数据

import { DatabaseManager } from '../storage/DatabaseManager'
import { BehaviorAnalyticsService } from './BehaviorAnalyticsService'
import { AchievementService } from './AchievementService'
import { GameProgressService } from './GameProgressService'
import { BehaviorRecord } from '../types/user'
import { CropType } from '../types/crop'

// 分析数据类型定义
export interface AnalyticsDataPoint {
  timestamp: number
  value: number
  metadata?: Record<string, any>
}

export interface SessionData {
  sessionId: string
  startTime: number
  endTime: number
  duration: number  // 毫秒
  focusScore: number  // 0-100
  distractionCount: number
  cropsPlanted: number
  cropsHarvested: number
  achievementsUnlocked: number
  farmLevelAtStart: number
  farmLevelAtEnd: number
  sessionType: 'focus' | 'meditation' | 'reading' | 'social' | 'mixed'
}

export interface DailyData {
  date: string  // YYYY-MM-DD
  totalFocusTime: number  // 毫秒
  totalSessions: number
  averageFocusScore: number
  cropsPlanted: number
  cropsHarvested: number
  achievementsUnlocked: number
  farmLevel: number
  streakDays: number
  sessionData: SessionData[]
}

export interface WeeklyData {
  weekStart: string  // YYYY-MM-DD
  weekEnd: string    // YYYY-MM-DD
  totalFocusTime: number
  totalSessions: number
  averageFocusScore: number
  dailyData: DailyData[]
  weeklyGrowth: {
    focusTimeGrowth: number  // 百分比
    sessionCountGrowth: number
    focusScoreGrowth: number
  }
}

export interface MonthlyData {
  month: string  // YYYY-MM
  totalFocusTime: number
  totalSessions: number
  averageFocusScore: number
  weeklyData: WeeklyData[]
  monthlyTrends: {
    bestWeek: WeeklyData
    worstWeek: WeeklyData
    averageDailyProgress: number
  }
}

export interface CropAnalyticsData {
  cropType: CropType
  totalPlanted: number
  totalHarvested: number
  successRate: number  // 收获率
  averageGrowthTime: number  // 平均生长时间
  focusTimeInvested: number  // 投入的专注时间
  preferredTimeSlots: string[]  // 偏好的种植时段
}

export interface BehaviorPatternData {
  mostActiveHours: number[]  // 最活跃的小时
  mostActiveDays: string[]   // 最活跃的星期几
  sessionLengthDistribution: Record<string, number>  // 会话时长分布
  focusScoreDistribution: Record<string, number>     // 专注分数分布
  distractionPatterns: {
    commonTimes: string[]
    averageRecoveryTime: number
    triggerFactors: string[]
  }
}

export interface UserAnalyticsProfile {
  userId: string
  profileCreatedAt: number
  totalGameTime: number
  currentLevel: number
  totalAchievements: number
  favoriteActivities: string[]
  improvementAreas: string[]
  personalBests: Record<string, any>
}

/**
 * 数据分析收集器
 * 整合各种数据源，为分析报告提供结构化数据
 */
export class DataAnalyticsCollector {
  private dbManager: DatabaseManager
  private behaviorService: BehaviorAnalyticsService
  private achievementService: AchievementService
  private gameProgressService: GameProgressService
  
  // 缓存
  private cache: Map<string, any> = new Map()
  private cacheExpiry: Map<string, number> = new Map()
  private readonly CACHE_DURATION = 5 * 60 * 1000  // 5分钟缓存

  constructor(
    dbManager: DatabaseManager,
    behaviorService: BehaviorAnalyticsService,
    achievementService: AchievementService,
    gameProgressService: GameProgressService
  ) {
    this.dbManager = dbManager
    this.behaviorService = behaviorService
    this.achievementService = achievementService
    this.gameProgressService = gameProgressService
  }

  // ===== 会话数据收集 =====

  /**
   * 开始新的分析会话
   */
  async startAnalyticsSession(sessionType: SessionData['sessionType'] = 'focus'): Promise<string> {
    const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

    const sessionStart: Partial<SessionData> = {
      sessionId,
      startTime: Date.now(),
      sessionType,
      farmLevelAtStart: 1,
      distractionCount: 0,
      cropsPlanted: 0,
      cropsHarvested: 0,
      achievementsUnlocked: 0
    }

    await this.cacheSet(`session_${sessionId}`, sessionStart)
    
    // 记录会话开始事件
    await this.behaviorService.trackEvent({
      type: 'analytics_session_start',
      category: 'analytics',
      details: `Session started: ${sessionType}`,
      metadata: { sessionId }
    })

    return sessionId
  }

  /**
   * 更新会话数据
   */
  async updateSessionData(sessionId: string, updates: Partial<SessionData>): Promise<void> {
    const currentSession = await this.cacheGet(`session_${sessionId}`) || {}
    const updatedSession = { ...currentSession, ...updates }
    await this.cacheSet(`session_${sessionId}`, updatedSession)
  }

  /**
   * 结束分析会话
   */
  async endAnalyticsSession(sessionId: string, focusScore: number): Promise<SessionData> {
    const sessionData = await this.cacheGet(`session_${sessionId}`) || {}

    const completedSession: SessionData = {
      ...sessionData,
      sessionId,
      endTime: Date.now(),
      duration: Date.now() - (sessionData.startTime || Date.now()),
      focusScore,
      farmLevelAtEnd: 1
    } as SessionData

    // 保存完整的会话数据
    await this.behaviorService.trackEvent({
      type: 'analytics_session_complete',
      category: 'analytics',
      value: focusScore,
      details: JSON.stringify(completedSession),
      metadata: { sessionId }
    })

    // 清除缓存
    this.cache.delete(`session_${sessionId}`)
    this.cacheExpiry.delete(`session_${sessionId}`)

    return completedSession
  }

  // ===== 每日数据收集 =====

  /**
   * 收集指定日期的每日数据
   */
  async collectDailyData(date: string): Promise<DailyData> {
    const cacheKey = `daily_${date}`
    const cached = await this.cacheGet(cacheKey)
    if (cached) return cached

    const startTime = new Date(date).getTime()
    const endTime = startTime + 24 * 60 * 60 * 1000 - 1

    // 获取当天的行为记录
    const behaviorRecords = await this.getBehaviorRecordsInRange(startTime, endTime)
    
    // 提取会话数据
    const sessionData = this.extractSessionDataFromBehaviors(behaviorRecords)
    
    // 计算每日统计
    const dailyStats = this.calculateDailyStats(sessionData, behaviorRecords)
    
    const dailyData: DailyData = {
      date,
      ...dailyStats,
      sessionData
    }

    await this.cacheSet(cacheKey, dailyData)
    return dailyData
  }

  /**
   * 收集指定日期范围的每日数据
   */
  async collectDailyDataRange(startDate: string, endDate: string): Promise<DailyData[]> {
    const dailyDataList: DailyData[] = []
    const start = new Date(startDate)
    const end = new Date(endDate)

    for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {
      const dateStr = d.toISOString().split('T')[0]
      const dailyData = await this.collectDailyData(dateStr)
      dailyDataList.push(dailyData)
    }

    return dailyDataList
  }

  // ===== 每周数据收集 =====

  /**
   * 收集指定周的数据
   */
  async collectWeeklyData(weekStart: string): Promise<WeeklyData> {
    const cacheKey = `weekly_${weekStart}`
    const cached = await this.cacheGet(cacheKey)
    if (cached) return cached

    // 计算一周的结束日期
    const startDate = new Date(weekStart)
    const endDate = new Date(startDate)
    endDate.setDate(startDate.getDate() + 6)
    const weekEnd = endDate.toISOString().split('T')[0]

    // 收集一周内的每日数据
    const dailyData = await this.collectDailyDataRange(weekStart, weekEnd)
    
    // 计算每周统计
    const weeklyStats = this.calculateWeeklyStats(dailyData)
    
    // 计算增长数据（对比前一周）
    const previousWeek = new Date(startDate)
    previousWeek.setDate(previousWeek.getDate() - 7)
    const previousWeekStart = previousWeek.toISOString().split('T')[0]
    const weeklyGrowth = await this.calculateWeeklyGrowth(weekStart, previousWeekStart)

    const weeklyData: WeeklyData = {
      weekStart,
      weekEnd,
      ...weeklyStats,
      dailyData,
      weeklyGrowth
    }

    await this.cacheSet(cacheKey, weeklyData)
    return weeklyData
  }

  // ===== 每月数据收集 =====

  /**
   * 收集指定月的数据
   */
  async collectMonthlyData(month: string): Promise<MonthlyData> {
    const cacheKey = `monthly_${month}`
    const cached = await this.cacheGet(cacheKey)
    if (cached) return cached

    // 获取月份的所有周数据
    const weeklyData = await this.getWeeklyDataForMonth(month)
    
    // 计算月度统计
    const monthlyStats = this.calculateMonthlyStats(weeklyData)
    
    // 计算月度趋势
    const monthlyTrends = this.calculateMonthlyTrends(weeklyData)

    const monthlyData: MonthlyData = {
      month,
      ...monthlyStats,
      weeklyData,
      monthlyTrends
    }

    await this.cacheSet(cacheKey, monthlyData)
    return monthlyData
  }

  // ===== 作物分析数据 =====

  /**
   * 收集作物分析数据
   */
  async collectCropAnalyticsData(): Promise<CropAnalyticsData[]> {
    const cacheKey = 'crop_analytics'
    const cached = await this.cacheGet(cacheKey)
    if (cached) return cached

    const cropTypes = Object.values(CropType)
    const cropAnalytics: CropAnalyticsData[] = []

    for (const cropType of cropTypes) {
      const cropData = await this.analyzeCropType(cropType)
      cropAnalytics.push(cropData)
    }

    await this.cacheSet(cacheKey, cropAnalytics)
    return cropAnalytics
  }

  // ===== 辅助方法 =====

  /**
   * 获取指定时间范围内的行为记录
   */
  private async getBehaviorRecordsInRange(startTime: number, endTime: number): Promise<BehaviorRecord[]> {
    const allRecords = await this.dbManager.getBehaviorRecords(undefined, undefined, 10000)
    return allRecords.filter(record => 
      record.timestamp >= startTime && record.timestamp <= endTime
    )
  }

  /**
   * 从行为记录中提取会话数据
   */
  private extractSessionDataFromBehaviors(records: BehaviorRecord[]): SessionData[] {
    const sessionMap = new Map<string, Partial<SessionData>>()
    
    records.forEach(record => {
      // 查找分析会话相关的记录（使用现有的focus_session类型）
      if (record.event.type === 'focus_session' && record.event.details?.includes('session_')) {
        try {
          const sessionData = JSON.parse(record.event.details || '{}')
          if (sessionData.sessionId) {
            sessionMap.set(sessionData.sessionId, sessionData)
          }
        } catch (error) {
          console.warn('Failed to parse session data:', error)
        }
      }
    })

    return Array.from(sessionMap.values()) as SessionData[]
  }

  /**
   * 计算每日统计数据
   */
  private calculateDailyStats(sessionData: SessionData[], behaviorRecords: BehaviorRecord[]) {
    const totalFocusTime = sessionData.reduce((sum, session) => sum + session.duration, 0)
    const totalSessions = sessionData.length
    const averageFocusScore = sessionData.length > 0 
      ? sessionData.reduce((sum, session) => sum + session.focusScore, 0) / sessionData.length 
      : 0

    // 统计作物相关数据
    const cropPlantedRecords = behaviorRecords.filter(r => r.event.type === 'crop_plant')
    const cropHarvestedRecords = behaviorRecords.filter(r => r.event.type === 'crop_harvest')
    const achievementRecords = behaviorRecords.filter(r => r.event.type === 'achievement_unlock')

    return {
      totalFocusTime,
      totalSessions,
      averageFocusScore,
      cropsPlanted: cropPlantedRecords.length,
      cropsHarvested: cropHarvestedRecords.length,
      achievementsUnlocked: achievementRecords.length,
      farmLevel: 1,  // 默认值
      streakDays: 0  // 需要从成就数据获取
    }
  }

  /**
   * 计算每周统计数据
   */
  private calculateWeeklyStats(dailyData: DailyData[]) {
    const totalFocusTime = dailyData.reduce((sum, day) => sum + day.totalFocusTime, 0)
    const totalSessions = dailyData.reduce((sum, day) => sum + day.totalSessions, 0)
    const averageFocusScore = dailyData.length > 0
      ? dailyData.reduce((sum, day) => sum + day.averageFocusScore, 0) / dailyData.length
      : 0

    return {
      totalFocusTime,
      totalSessions,
      averageFocusScore
    }
  }

  /**
   * 计算每月统计数据
   */
  private calculateMonthlyStats(weeklyData: WeeklyData[]) {
    const totalFocusTime = weeklyData.reduce((sum, week) => sum + week.totalFocusTime, 0)
    const totalSessions = weeklyData.reduce((sum, week) => sum + week.totalSessions, 0)
    const averageFocusScore = weeklyData.length > 0
      ? weeklyData.reduce((sum, week) => sum + week.averageFocusScore, 0) / weeklyData.length
      : 0

    return {
      totalFocusTime,
      totalSessions,
      averageFocusScore
    }
  }

  // ===== 缓存管理 =====

  /**
   * 设置缓存
   */
  private async cacheSet(key: string, value: any): Promise<void> {
    this.cache.set(key, value)
    this.cacheExpiry.set(key, Date.now() + this.CACHE_DURATION)
  }

  /**
   * 获取缓存
   */
  private async cacheGet(key: string): Promise<any> {
    const expiry = this.cacheExpiry.get(key)
    if (expiry && Date.now() > expiry) {
      this.cache.delete(key)
      this.cacheExpiry.delete(key)
      return null
    }
    return this.cache.get(key)
  }

  /**
   * 清除缓存
   */
  clearCache(): void {
    this.cache.clear()
    this.cacheExpiry.clear()
  }

  // ===== 待实现的方法 =====
  
  private async calculateWeeklyGrowth(currentWeek: string, previousWeek: string): Promise<any> {
    return { focusTimeGrowth: 0, sessionCountGrowth: 0, focusScoreGrowth: 0 }
  }

  private calculateMonthlyTrends(weeklyData: WeeklyData[]): any {
    return { bestWeek: weeklyData[0], worstWeek: weeklyData[0], averageDailyProgress: 0 }
  }

  private async getWeeklyDataForMonth(month: string): Promise<WeeklyData[]> {
    return []
  }

  private async analyzeCropType(cropType: CropType): Promise<CropAnalyticsData> {
    return {
      cropType,
      totalPlanted: 0,
      totalHarvested: 0,
      successRate: 0,
      averageGrowthTime: 0,
      focusTimeInvested: 0,
      preferredTimeSlots: []
    }
  }
}

export default DataAnalyticsCollector 