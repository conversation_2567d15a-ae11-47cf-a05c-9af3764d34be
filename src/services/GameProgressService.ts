// 游戏进度服务 - 处理游戏数据的保存和加载

import { DatabaseManager } from '../storage/DatabaseManager'
import { GameStateManager } from '../managers/GameStateManager'
import { GameProgress, CropProgressData } from '../types/user'
import { CropInstance } from '../types/crop'

export interface SaveGameOptions {
  autoSave?: boolean
  saveDescription?: string
  createBackup?: boolean
}

export interface LoadGameOptions {
  skipVersionCheck?: boolean
  mergeWithCurrent?: boolean
}

export class GameProgressService {
  private dbManager: DatabaseManager
  private gameStateManager: GameStateManager
  private autoSaveTimer: number | null = null
  private autoSaveInterval: number = 5 * 60 * 1000 // 5分钟
  private isAutoSaveEnabled: boolean = true

  constructor(dbManager: DatabaseManager, gameStateManager: GameStateManager) {
    this.dbManager = dbManager
    this.gameStateManager = gameStateManager
  }

  /**
   * 初始化服务
   */
  async initialize(): Promise<boolean> {
    try {
      // 检查是否有保存的游戏进度
      const savedProgress = await this.dbManager.getGameProgress()
      
      if (savedProgress) {
        await this.loadGameProgress(savedProgress)
        console.log('Game progress loaded on initialization')
      } else {
        // 创建新的游戏进度
        await this.createNewGameProgress()
        console.log('New game progress created')
      }

      // 启动自动保存
      this.startAutoSave()
      
      return true
    } catch (error) {
      console.error('Failed to initialize GameProgressService:', error)
      return false
    }
  }

  /**
   * 保存游戏进度
   */
  async saveGame(options: SaveGameOptions = {}): Promise<boolean> {
    try {
      const gameState = this.gameStateManager.getGameState()
      const progress = await this.createGameProgressFromState(gameState)
      
      const success = await this.dbManager.saveGameProgress(progress)
      
      if (success) {
        // 记录保存事件
        await this.dbManager.logBehavior({
          type: 'game_saved',
          category: 'system',
          details: options.saveDescription || (options.autoSave ? 'Auto save' : 'Manual save')
        })

        // 创建备份（如果请求）
        if (options.createBackup) {
          await this.dbManager.createBackup(`Game save backup - ${new Date().toISOString()}`)
        }

        console.log('Game progress saved successfully')
        return true
      }
      
      return false
    } catch (error) {
      console.error('Failed to save game progress:', error)
      return false
    }
  }

  /**
   * 加载游戏进度
   */
  async loadGame(options: LoadGameOptions = {}): Promise<boolean> {
    try {
      const progress = await this.dbManager.getGameProgress()
      
      if (!progress) {
        console.warn('No saved game progress found')
        return false
      }

      return await this.loadGameProgress(progress, options)
    } catch (error) {
      console.error('Failed to load game progress:', error)
      return false
    }
  }

  /**
   * 从进度数据加载游戏状态
   */
  private async loadGameProgress(progress: GameProgress, options: LoadGameOptions = {}): Promise<boolean> {
    try {
      // 版本检查
      if (!options.skipVersionCheck && progress.version !== '1.0.0') {
        console.warn(`Game version mismatch. Saved: ${progress.version}, Current: 1.0.0`)
        // 这里可以添加版本迁移逻辑
      }

      // 恢复基本游戏状态（这里需要GameStateManager支持）
      // 注意：由于GameStateManager的接口限制，我们需要通过事件或直接操作来恢复状态
      
      // 记录加载事件
      await this.dbManager.logBehavior({
        type: 'game_loaded',
        category: 'system',
        details: `Loaded game from ${new Date(progress.lastSaved).toISOString()}`
      })

      console.log('Game progress loaded successfully')
      return true
    } catch (error) {
      console.error('Failed to load game progress:', error)
      return false
    }
  }

  /**
   * 创建新游戏进度
   */
  async createNewGameProgress(): Promise<GameProgress> {
    const profile = await this.dbManager.getUserProfile()
    
    const newProgress: GameProgress = {
      userId: profile?.id || 'anonymous',
      gameId: `game_${Date.now()}`,
      level: 1,
      experience: 0,
      gameTime: 0,
      farmGrid: {},
      crops: {},
      inventory: {
        seeds: { 
          tomato: 5, 
          carrot: 3, 
          potato: 2 
        },
        harvested: {},
        tools: {},
        currency: 100
      },
      achievements: [],
      lastSaved: Date.now(),
      version: '1.0.0'
    }

    await this.dbManager.saveGameProgress(newProgress)
    
    // 记录新游戏创建
    await this.dbManager.logBehavior({
      type: 'game_start',
      category: 'system',
      details: 'New game created'
    })

    return newProgress
  }

  /**
   * 从游戏状态创建进度数据
   */
  private async createGameProgressFromState(gameState: any): Promise<GameProgress> {
    const profile = await this.dbManager.getUserProfile()
    
    // 转换农场网格
    const farmGrid: Record<string, string> = {}
    if (gameState.farmGrid?.plots) {
      gameState.farmGrid.plots.forEach((row: any[], y: number) => {
        row.forEach((crop: any, x: number) => {
          if (crop) {
            farmGrid[`${x}_${y}`] = crop.id
          }
        })
      })
    }

    // 转换作物数据
    const crops: Record<string, CropProgressData> = {}
    if (gameState.crops instanceof Map) {
      gameState.crops.forEach((crop: CropInstance, cropId: string) => {
        crops[cropId] = {
          id: crop.id,
          type: crop.type,
          stage: crop.stage,
          plantedAt: crop.plantedAt,
          lastUpdateTime: crop.lastUpdateTime,
          position: crop.position,
          quality: crop.quality || 1.0,
          focusTimeReceived: 0,
          growthMultiplier: 1.0
        }
      })
    }

    return {
      userId: profile?.id || 'anonymous',
      gameId: gameState.gameId || `game_${Date.now()}`,
      level: gameState.level || 1,
      experience: gameState.experience || 0,
      gameTime: gameState.gameTime?.total || 0,
      farmGrid,
      crops,
      inventory: {
        seeds: gameState.inventory?.seeds ? Object.fromEntries(gameState.inventory.seeds) : {},
        harvested: gameState.inventory?.harvested ? Object.fromEntries(gameState.inventory.harvested) : {},
        tools: {},
        currency: gameState.inventory?.currency || 0
      },
      achievements: gameState.achievements || [],
      lastSaved: Date.now(),
      version: '1.0.0'
    }
  }

  /**
   * 快速保存（用于自动保存或频繁操作）
   */
  async quickSave(): Promise<boolean> {
    return await this.saveGame({ autoSave: true })
  }

  /**
   * 创建保存点（包含备份）
   */
  async createSavepoint(description?: string): Promise<boolean> {
    return await this.saveGame({ 
      createBackup: true, 
      saveDescription: description || `Savepoint - ${new Date().toISOString()}`
    })
  }

  /**
   * 获取保存统计信息
   */
  async getSaveStatistics(): Promise<{
    lastSaveTime: number | null
    totalSaves: number
    autoSaves: number
    manualSaves: number
    saveSize: number
  }> {
    try {
      const progress = await this.dbManager.getGameProgress()
      const behaviors = await this.dbManager.getBehaviorRecords(undefined, 'system', 100)
      
      const saveEvents = behaviors.filter(b => b.event.type === 'game_saved')
      const autoSaves = saveEvents.filter(b => b.event.details?.includes('Auto save')).length
      const manualSaves = saveEvents.length - autoSaves

      // 获取保存文件大小
      const saveSize = progress ? JSON.stringify(progress).length : 0

      return {
        lastSaveTime: progress?.lastSaved || null,
        totalSaves: saveEvents.length,
        autoSaves,
        manualSaves,
        saveSize
      }
    } catch (error) {
      console.error('Failed to get save statistics:', error)
      return {
        lastSaveTime: null,
        totalSaves: 0,
        autoSaves: 0,
        manualSaves: 0,
        saveSize: 0
      }
    }
  }

  /**
   * 设置自动保存
   */
  setAutoSave(enabled: boolean, intervalMinutes?: number): void {
    this.isAutoSaveEnabled = enabled
    
    if (intervalMinutes) {
      this.autoSaveInterval = intervalMinutes * 60 * 1000
    }

    if (enabled) {
      this.startAutoSave()
    } else {
      this.stopAutoSave()
    }
  }

  /**
   * 启动自动保存
   */
  private startAutoSave(): void {
    this.stopAutoSave() // 清除现有定时器
    
    if (this.isAutoSaveEnabled) {
      this.autoSaveTimer = window.setInterval(async () => {
        const success = await this.quickSave()
        if (success) {
          console.log('Auto-save completed')
        } else {
          console.warn('Auto-save failed')
        }
      }, this.autoSaveInterval)
    }
  }

  /**
   * 停止自动保存
   */
  private stopAutoSave(): void {
    if (this.autoSaveTimer !== null) {
      clearInterval(this.autoSaveTimer)
      this.autoSaveTimer = null
    }
  }

  /**
   * 检查游戏进度是否存在
   */
  async hasSavedProgress(): Promise<boolean> {
    const progress = await this.dbManager.getGameProgress()
    return progress !== null
  }

  /**
   * 删除游戏进度
   */
  async deleteProgress(): Promise<boolean> {
    try {
      // 这里需要通过存储适配器直接删除
      const success = await this.dbManager['storage'].remove('game_progress')
      
      if (success) {
        await this.dbManager.logBehavior({
          type: 'game_deleted',
          category: 'system',
          details: 'Game progress deleted'
        })
      }
      
      return success
    } catch (error) {
      console.error('Failed to delete game progress:', error)
      return false
    }
  }

  /**
   * 导出游戏进度
   */
  async exportProgress(): Promise<string | null> {
    try {
      const progress = await this.dbManager.getGameProgress()
      if (!progress) {
        return null
      }

      const exportData = {
        timestamp: Date.now(),
        version: '1.0.0',
        gameProgress: progress
      }

      return JSON.stringify(exportData, null, 2)
    } catch (error) {
      console.error('Failed to export game progress:', error)
      return null
    }
  }

  /**
   * 导入游戏进度
   */
  async importProgress(progressData: string): Promise<boolean> {
    try {
      const importData = JSON.parse(progressData)
      
      if (!importData.gameProgress) {
        throw new Error('Invalid progress data format')
      }

      const success = await this.dbManager.saveGameProgress(importData.gameProgress)
      
      if (success) {
        await this.dbManager.logBehavior({
          type: 'game_imported',
          category: 'system',
          details: `Game progress imported from ${new Date(importData.timestamp).toISOString()}`
        })
      }
      
      return success
    } catch (error) {
      console.error('Failed to import game progress:', error)
      return false
    }
  }

  /**
   * 销毁服务
   */
  destroy(): void {
    this.stopAutoSave()
  }
} 