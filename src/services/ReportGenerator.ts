// 报告生成系统
// 根据数据分析结果生成格式化的用户行为报告

import DataAnalysisEngine, { 
  ComprehensiveAnalysis,
  PerformanceInsights,
  TrendAnalysis,
  HabitFormationAnalysis,
  ProductivityMetrics
} from './DataAnalysisEngine'
import DataAnalyticsCollector from './DataAnalyticsCollector'

// 报告类型定义
export type ReportType = 'daily' | 'weekly' | 'monthly' | 'comprehensive'
export type ReportFormat = 'html' | 'pdf' | 'json' | 'markdown'

// 报告配置接口
export interface ReportConfig {
  type: ReportType
  format: ReportFormat
  includeCharts: boolean
  includeRecommendations: boolean
  includePredictions: boolean
  language: 'zh' | 'en'
  theme: 'light' | 'dark' | 'minimal'
}

// 报告模板接口
export interface ReportTemplate {
  title: string
  sections: ReportSection[]
  styles: ReportStyles
}

export interface ReportSection {
  id: string
  title: string
  content: string
  charts?: ChartConfig[]
  order: number
}

export interface ChartConfig {
  type: 'line' | 'bar' | 'pie' | 'radar' | 'area'
  data: any
  options: any
  title: string
}

export interface ReportStyles {
  primaryColor: string
  secondaryColor: string
  fontFamily: string
  fontSize: string
  backgroundColor: string
}

// 生成的报告接口
export interface GeneratedReport {
  id: string
  userId: string
  type: ReportType
  format: ReportFormat
  title: string
  content: string
  metadata: {
    generatedAt: number
    dataRange: {
      startDate: number
      endDate: number
    }
    analysisVersion: string
    reportVersion: string
  }
  attachments?: {
    charts: string[]
    exports: string[]
  }
}

// 报告生成器类
export class ReportGenerator {
  private analysisEngine: DataAnalysisEngine
  private collector: DataAnalyticsCollector
  private templates: Map<string, ReportTemplate> = new Map()
  
  // 默认配置
  private readonly DEFAULT_CONFIG: ReportConfig = {
    type: 'comprehensive',
    format: 'html',
    includeCharts: true,
    includeRecommendations: true,
    includePredictions: true,
    language: 'zh',
    theme: 'light'
  }

  constructor(analysisEngine: DataAnalysisEngine, collector: DataAnalyticsCollector) {
    this.analysisEngine = analysisEngine
    this.collector = collector
    this.initializeTemplates()
  }

  /**
   * 生成用户行为报告
   */
  async generateReport(
    userId: string, 
    config: Partial<ReportConfig> = {}
  ): Promise<GeneratedReport> {
    const finalConfig = { ...this.DEFAULT_CONFIG, ...config }
    
    // 生成分析数据
    const analysis = await this.analysisEngine.generateComprehensiveAnalysis(
      userId, 
      finalConfig.type
    )
    
    // 获取报告模板
    const template = this.getTemplate(finalConfig.type, finalConfig.theme)
    
    // 生成报告内容
    const content = await this.generateReportContent(analysis, template, finalConfig)
    
    // 生成报告元数据
    const metadata = this.generateReportMetadata(analysis, finalConfig)
    
    // 创建最终报告
    const report: GeneratedReport = {
      id: this.generateReportId(),
      userId,
      type: finalConfig.type,
      format: finalConfig.format,
      title: this.generateReportTitle(analysis, finalConfig),
      content,
      metadata
    }

    // 如果需要，生成图表和附件
    if (finalConfig.includeCharts) {
      report.attachments = await this.generateAttachments(analysis, finalConfig)
    }

    return report
  }

  /**
   * 生成批量报告
   */
  async generateBatchReports(
    userId: string,
    reportTypes: ReportType[],
    config: Partial<ReportConfig> = {}
  ): Promise<GeneratedReport[]> {
    const reports: GeneratedReport[] = []
    
    for (const type of reportTypes) {
      const reportConfig = { ...config, type }
      const report = await this.generateReport(userId, reportConfig)
      reports.push(report)
    }
    
    return reports
  }

  /**
   * 生成报告内容
   */
  private async generateReportContent(
    analysis: ComprehensiveAnalysis,
    template: ReportTemplate,
    config: ReportConfig
  ): Promise<string> {
    let content = ''
    
    // 按顺序生成各个部分
    const sortedSections = template.sections.sort((a, b) => a.order - b.order)
    
    for (const section of sortedSections) {
      const sectionContent = await this.generateSectionContent(section, analysis, config)
      content += sectionContent
    }
    
    // 根据格式包装内容
    return this.wrapContentWithFormat(content, template, config)
  }

  /**
   * 生成报告段落内容
   */
  private async generateSectionContent(
    section: ReportSection,
    analysis: ComprehensiveAnalysis,
    config: ReportConfig
  ): Promise<string> {
    switch (section.id) {
      case 'summary':
        return this.generateSummarySection(analysis, config)
      case 'performance':
        return this.generatePerformanceSection(analysis.performanceInsights, config)
      case 'trends':
        return this.generateTrendsSection(analysis.trendAnalysis, config)
      case 'habits':
        return this.generateHabitsSection(analysis.habitFormation, config)
      case 'productivity':
        return this.generateProductivitySection(analysis.productivityMetrics, config)
      case 'predictions':
        return config.includePredictions ? 
          this.generatePredictionsSection(analysis.predictions, config) : ''
      case 'recommendations':
        return config.includeRecommendations ? 
          this.generateRecommendationsSection(analysis, config) : ''
      case 'charts':
        return config.includeCharts ? 
          this.generateChartsSection(analysis, config) : ''
      default:
        return section.content
    }
  }

  /**
   * 生成概要部分
   */
  private generateSummarySection(analysis: ComprehensiveAnalysis, config: ReportConfig): string {
    const { performanceInsights, metadata } = analysis
    const timeRange = this.formatTimeRange(analysis.analysisType)
    
    return `
    <div class="summary-section">
      <h2>📊 ${timeRange}行为分析概要</h2>
      <div class="summary-cards">
        <div class="summary-card">
          <h3>综合表现</h3>
          <div class="score ${this.getScoreClass(performanceInsights.overallScore)}">
            ${performanceInsights.overallScore.toFixed(1)}分
          </div>
        </div>
        <div class="summary-card">
          <h3>数据质量</h3>
          <div class="score">
            ${metadata.dataQuality.toFixed(1)}%
          </div>
        </div>
        <div class="summary-card">
          <h3>分析置信度</h3>
          <div class="score">
            ${metadata.analysisConfidence.toFixed(1)}%
          </div>
        </div>
        <div class="summary-card">
          <h3>样本大小</h3>
          <div class="score">
            ${metadata.sampleSize}天
          </div>
        </div>
      </div>
    </div>
    `
  }

  /**
   * 生成表现分析部分
   */
  private generatePerformanceSection(insights: PerformanceInsights, config: ReportConfig): string {
    return `
    <div class="performance-section">
      <h2>🎯 表现分析</h2>
      
      <div class="performance-metrics">
        <h3>关键指标</h3>
        <div class="metrics-grid">
          <div class="metric-item">
            <span class="metric-label">一致性分数</span>
            <span class="metric-value">${insights.keyMetrics.consistencyScore.toFixed(1)}</span>
          </div>
          <div class="metric-item">
            <span class="metric-label">改进速度</span>
            <span class="metric-value">${insights.keyMetrics.improvementRate.toFixed(1)}%</span>
          </div>
          <div class="metric-item">
            <span class="metric-label">参与度</span>
            <span class="metric-value">${insights.keyMetrics.engagementLevel.toFixed(1)}</span>
          </div>
          <div class="metric-item">
            <span class="metric-label">效率分数</span>
            <span class="metric-value">${insights.keyMetrics.efficiencyScore.toFixed(1)}</span>
          </div>
        </div>
      </div>

      <div class="strengths-weaknesses">
        <div class="strengths">
          <h3>✅ 优势领域</h3>
          <ul>
            ${insights.strengths.map(strength => `<li>${strength}</li>`).join('')}
          </ul>
        </div>
        
        <div class="weaknesses">
          <h3>⚠️ 需要改进</h3>
          <ul>
            ${insights.weaknesses.map(weakness => `<li>${weakness}</li>`).join('')}
          </ul>
        </div>
      </div>

      <div class="recommendations">
        <h3>💡 改进建议</h3>
        <ol>
          ${insights.recommendations.map(rec => `<li>${rec}</li>`).join('')}
        </ol>
      </div>
    </div>
    `
  }

  /**
   * 生成趋势分析部分
   */
  private generateTrendsSection(trends: TrendAnalysis, config: ReportConfig): string {
    const directionIcon = this.getTrendIcon(trends.direction)
    const significanceClass = this.getSignificanceClass(trends.significance)
    
    return `
    <div class="trends-section">
      <h2>📈 趋势分析</h2>
      
      <div class="trend-overview">
        <div class="trend-direction">
          <span class="trend-icon">${directionIcon}</span>
          <span class="trend-text">${this.getTrendText(trends.direction)}</span>
        </div>
        
        <div class="trend-metrics">
          <div class="trend-metric">
            <span class="label">趋势强度</span>
            <div class="progress-bar">
              <div class="progress-fill" style="width: ${trends.strength}%"></div>
            </div>
            <span class="value">${trends.strength.toFixed(1)}%</span>
          </div>
          
          <div class="trend-metric">
            <span class="label">置信度</span>
            <div class="progress-bar">
              <div class="progress-fill" style="width: ${trends.confidence}%"></div>
            </div>
            <span class="value">${trends.confidence.toFixed(1)}%</span>
          </div>
          
          <div class="trend-metric">
            <span class="label">变化率</span>
            <span class="value ${trends.changeRate >= 0 ? 'positive' : 'negative'}">
              ${trends.changeRate >= 0 ? '+' : ''}${trends.changeRate.toFixed(1)}%
            </span>
          </div>
        </div>
        
        <div class="trend-significance ${significanceClass}">
          显著性: ${this.getSignificanceText(trends.significance)}
        </div>
      </div>
    </div>
    `
  }

  /**
   * 生成习惯形成分析部分
   */
  private generateHabitsSection(habits: HabitFormationAnalysis, config: ReportConfig): string {
    return `
    <div class="habits-section">
      <h2>🔄 习惯形成分析</h2>
      
      <div class="habit-overview">
        <div class="habit-strength">
          <h3>习惯强度</h3>
          <div class="circular-progress">
            <div class="progress-circle" data-percentage="${habits.habitStrength}">
              <span>${habits.habitStrength.toFixed(1)}%</span>
            </div>
          </div>
        </div>
        
        <div class="habit-stability">
          <h3>稳定性</h3>
          <div class="stability-indicator ${this.getStabilityClass(habits.stability)}">
            ${habits.stability.toFixed(1)}%
          </div>
        </div>
      </div>

      <div class="streak-analysis">
        <h3>连续性分析</h3>
        <div class="streak-stats">
          <div class="streak-stat">
            <span class="label">当前连续</span>
            <span class="value">${habits.streakAnalysis.currentStreak}天</span>
          </div>
          <div class="streak-stat">
            <span class="label">最长连续</span>
            <span class="value">${habits.streakAnalysis.longestStreak}天</span>
          </div>
          <div class="streak-stat">
            <span class="label">平均连续</span>
            <span class="value">${habits.streakAnalysis.averageStreak.toFixed(1)}天</span>
          </div>
          <div class="streak-stat">
            <span class="label">可靠性</span>
            <span class="value">${habits.streakAnalysis.streakReliability.toFixed(1)}%</span>
          </div>
        </div>
      </div>

      ${habits.riskFactors.length > 0 ? `
      <div class="risk-factors">
        <h3>⚠️ 风险因素</h3>
        <ul>
          ${habits.riskFactors.map(factor => `<li>${factor}</li>`).join('')}
        </ul>
      </div>
      ` : ''}
    </div>
    `
  }

  /**
   * 生成生产力指标部分
   */
  private generateProductivitySection(productivity: ProductivityMetrics, config: ReportConfig): string {
    return `
    <div class="productivity-section">
      <h2>⚡ 生产力分析</h2>
      
      <div class="productivity-metrics">
        <div class="metric-card">
          <h3>专注效率</h3>
          <div class="metric-value">${productivity.focusEfficiency.toFixed(1)}%</div>
        </div>
        
        <div class="metric-card">
          <h3>时间利用率</h3>
          <div class="metric-value">${productivity.timeUtilization.toFixed(1)}%</div>
        </div>
        
        <div class="metric-card">
          <h3>任务完成率</h3>
          <div class="metric-value">${productivity.taskCompletion.toFixed(1)}%</div>
        </div>
        
        <div class="metric-card">
          <h3>质量分数</h3>
          <div class="metric-value">${productivity.qualityScore.toFixed(1)}</div>
        </div>
      </div>

      <div class="energy-pattern">
        <h3>能量模式分析</h3>
        <div class="energy-chart">
          <div class="energy-bar">
            <span class="label">上午能量</span>
            <div class="bar">
              <div class="fill" style="width: ${productivity.energyPattern.morningEnergy}%"></div>
            </div>
            <span class="value">${productivity.energyPattern.morningEnergy.toFixed(1)}%</span>
          </div>
          
          <div class="energy-bar">
            <span class="label">下午能量</span>
            <div class="bar">
              <div class="fill" style="width: ${productivity.energyPattern.afternoonEnergy}%"></div>
            </div>
            <span class="value">${productivity.energyPattern.afternoonEnergy.toFixed(1)}%</span>
          </div>
          
          <div class="energy-bar">
            <span class="label">晚上能量</span>
            <div class="bar">
              <div class="fill" style="width: ${productivity.energyPattern.eveningEnergy}%"></div>
            </div>
            <span class="value">${productivity.energyPattern.eveningEnergy.toFixed(1)}%</span>
          </div>
        </div>
        
        <div class="optimal-window">
          <strong>最佳工作时段:</strong> ${productivity.energyPattern.optimalWorkWindow}
        </div>
      </div>

      ${productivity.peakPerformanceTimes.length > 0 ? `
      <div class="peak-times">
        <h3>高效时段</h3>
        <div class="time-tags">
          ${productivity.peakPerformanceTimes.map(time => 
            `<span class="time-tag">${time}</span>`
          ).join('')}
        </div>
      </div>
      ` : ''}
    </div>
    `
  }

  /**
   * 生成预测分析部分
   */
  private generatePredictionsSection(predictions: any, config: ReportConfig): string {
    return `
    <div class="predictions-section">
      <h2>🔮 行为预测</h2>
      
      <div class="prediction-cards">
        <div class="prediction-card">
          <h3>下周预测</h3>
          <div class="prediction-score ${this.getScoreClass(predictions.nextWeek.predictedScore)}">
            ${predictions.nextWeek.predictedScore.toFixed(1)}分
          </div>
          <div class="confidence">
            置信度: ${predictions.nextWeek.confidence.toFixed(1)}%
          </div>
          
          ${predictions.nextWeek.factors.length > 0 ? `
          <div class="prediction-factors">
            <h4>影响因素</h4>
            ${predictions.nextWeek.factors.map(factor => `
              <div class="factor">
                <span class="factor-name">${factor.name}</span>
                <span class="factor-impact ${factor.impact >= 0 ? 'positive' : 'negative'}">
                  ${factor.impact >= 0 ? '+' : ''}${factor.impact.toFixed(1)}
                </span>
              </div>
            `).join('')}
          </div>
          ` : ''}
        </div>
        
        <div class="prediction-card">
          <h3>下月预测</h3>
          <div class="prediction-score ${this.getScoreClass(predictions.nextMonth.predictedScore)}">
            ${predictions.nextMonth.predictedScore.toFixed(1)}分
          </div>
          <div class="confidence">
            置信度: ${predictions.nextMonth.confidence.toFixed(1)}%
          </div>
          
          ${predictions.nextMonth.factors.length > 0 ? `
          <div class="prediction-factors">
            <h4>影响因素</h4>
            ${predictions.nextMonth.factors.map(factor => `
              <div class="factor">
                <span class="factor-name">${factor.name}</span>
                <span class="factor-impact ${factor.impact >= 0 ? 'positive' : 'negative'}">
                  ${factor.impact >= 0 ? '+' : ''}${factor.impact.toFixed(1)}
                </span>
              </div>
            `).join('')}
          </div>
          ` : ''}
        </div>
      </div>
    </div>
    `
  }

  /**
   * 生成综合建议部分
   */
  private generateRecommendationsSection(analysis: ComprehensiveAnalysis, config: ReportConfig): string {
    const allRecommendations = [
      ...analysis.performanceInsights.recommendations,
      ...(analysis.predictions.nextWeek.recommendations || []),
      ...(analysis.predictions.nextMonth.recommendations || [])
    ]
    
    // 去重并分类
    const uniqueRecommendations = Array.from(new Set(allRecommendations))
    
    return `
    <div class="recommendations-section">
      <h2>💡 个性化建议</h2>
      
      <div class="recommendations-list">
        ${uniqueRecommendations.map((rec, index) => `
          <div class="recommendation-item">
            <div class="recommendation-number">${index + 1}</div>
            <div class="recommendation-content">${rec}</div>
          </div>
        `).join('')}
      </div>
      
      <div class="action-plan">
        <h3>行动计划</h3>
        <p>基于以上分析，建议您：</p>
        <ol>
          <li>重点关注表现分析中的弱项领域</li>
          <li>利用高效时段进行重要任务</li>
          <li>根据趋势调整专注策略</li>
          <li>定期回顾习惯形成进度</li>
        </ol>
      </div>
    </div>
    `
  }

  /**
   * 生成图表部分
   */
  private generateChartsSection(analysis: ComprehensiveAnalysis, config: ReportConfig): string {
    return `
    <div class="charts-section">
      <h2>📊 数据可视化</h2>
      
      <div class="chart-container">
        <div id="trend-chart" class="chart">
          <!-- 趋势图表将在这里渲染 -->
        </div>
        
        <div id="performance-chart" class="chart">
          <!-- 表现雷达图将在这里渲染 -->
        </div>
        
        <div id="habits-chart" class="chart">
          <!-- 习惯形成图表将在这里渲染 -->
        </div>
      </div>
    </div>
    `
  }

  // ===== 辅助方法 =====

  /**
   * 初始化报告模板
   */
  private initializeTemplates(): void {
    // 日报模板
    this.templates.set('daily_light', {
      title: '每日行为分析报告',
      sections: [
        { id: 'summary', title: '概要', content: '', order: 1 },
        { id: 'performance', title: '表现分析', content: '', order: 2 },
        { id: 'habits', title: '习惯分析', content: '', order: 3 },
        { id: 'recommendations', title: '建议', content: '', order: 4 }
      ],
      styles: this.getLightThemeStyles()
    })

    // 周报模板
    this.templates.set('weekly_light', {
      title: '每周行为分析报告',
      sections: [
        { id: 'summary', title: '概要', content: '', order: 1 },
        { id: 'performance', title: '表现分析', content: '', order: 2 },
        { id: 'trends', title: '趋势分析', content: '', order: 3 },
        { id: 'habits', title: '习惯分析', content: '', order: 4 },
        { id: 'productivity', title: '生产力分析', content: '', order: 5 },
        { id: 'charts', title: '图表', content: '', order: 6 },
        { id: 'recommendations', title: '建议', content: '', order: 7 }
      ],
      styles: this.getLightThemeStyles()
    })

    // 月报模板
    this.templates.set('monthly_light', {
      title: '每月行为分析报告',
      sections: [
        { id: 'summary', title: '概要', content: '', order: 1 },
        { id: 'performance', title: '表现分析', content: '', order: 2 },
        { id: 'trends', title: '趋势分析', content: '', order: 3 },
        { id: 'habits', title: '习惯分析', content: '', order: 4 },
        { id: 'productivity', title: '生产力分析', content: '', order: 5 },
        { id: 'predictions', title: '预测分析', content: '', order: 6 },
        { id: 'charts', title: '图表', content: '', order: 7 },
        { id: 'recommendations', title: '建议', content: '', order: 8 }
      ],
      styles: this.getLightThemeStyles()
    })

    // 综合报告模板
    this.templates.set('comprehensive_light', {
      title: '综合行为分析报告',
      sections: [
        { id: 'summary', title: '概要', content: '', order: 1 },
        { id: 'performance', title: '表现分析', content: '', order: 2 },
        { id: 'trends', title: '趋势分析', content: '', order: 3 },
        { id: 'habits', title: '习惯分析', content: '', order: 4 },
        { id: 'productivity', title: '生产力分析', content: '', order: 5 },
        { id: 'predictions', title: '预测分析', content: '', order: 6 },
        { id: 'charts', title: '图表', content: '', order: 7 },
        { id: 'recommendations', title: '建议', content: '', order: 8 }
      ],
      styles: this.getLightThemeStyles()
    })
  }

  /**
   * 获取报告模板
   */
  private getTemplate(type: ReportType, theme: string): ReportTemplate {
    const templateKey = `${type}_${theme}`
    return this.templates.get(templateKey) || this.templates.get('comprehensive_light')!
  }

  /**
   * 获取亮色主题样式
   */
  private getLightThemeStyles(): ReportStyles {
    return {
      primaryColor: '#3B82F6',
      secondaryColor: '#64748B',
      fontFamily: '"Inter", "PingFang SC", "Microsoft YaHei", sans-serif',
      fontSize: '14px',
      backgroundColor: '#FFFFFF'
    }
  }

  /**
   * 包装内容为指定格式
   */
  private wrapContentWithFormat(
    content: string,
    template: ReportTemplate,
    config: ReportConfig
  ): string {
    switch (config.format) {
      case 'html':
        return this.wrapAsHTML(content, template)
      case 'markdown':
        return this.wrapAsMarkdown(content)
      case 'json':
        return JSON.stringify({ content, template, config }, null, 2)
      default:
        return content
    }
  }

  /**
   * 包装为HTML格式
   */
  private wrapAsHTML(content: string, template: ReportTemplate): string {
    return `
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>${template.title}</title>
      <style>
        ${this.generateCSS(template.styles)}
      </style>
    </head>
    <body>
      <div class="report-container">
        <header class="report-header">
          <h1>${template.title}</h1>
          <div class="report-date">${new Date().toLocaleDateString('zh-CN')}</div>
        </header>
        
        <main class="report-content">
          ${content}
        </main>
        
        <footer class="report-footer">
          <p>本报告由 SelfGame 自律农场系统自动生成</p>
        </footer>
      </div>
    </body>
    </html>
    `
  }

  /**
   * 包装为Markdown格式
   */
  private wrapAsMarkdown(content: string): string {
    // 简化版本，将HTML转换为Markdown
    return content
      .replace(/<h2>/g, '## ')
      .replace(/<\/h2>/g, '\n')
      .replace(/<h3>/g, '### ')
      .replace(/<\/h3>/g, '\n')
      .replace(/<li>/g, '- ')
      .replace(/<\/li>/g, '\n')
      .replace(/<[^>]*>/g, '') // 移除其他HTML标签
  }

  /**
   * 生成CSS样式
   */
  private generateCSS(styles: ReportStyles): string {
    return `
      * {
        box-sizing: border-box;
        margin: 0;
        padding: 0;
      }

      body {
        font-family: ${styles.fontFamily};
        font-size: ${styles.fontSize};
        line-height: 1.6;
        color: #333;
        background-color: ${styles.backgroundColor};
      }

      .report-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
      }

      .report-header {
        text-align: center;
        margin-bottom: 40px;
        padding-bottom: 20px;
        border-bottom: 2px solid ${styles.primaryColor};
      }

      .report-header h1 {
        color: ${styles.primaryColor};
        font-size: 2.5em;
        margin-bottom: 10px;
      }

      .report-date {
        color: ${styles.secondaryColor};
        font-size: 1.1em;
      }

      .summary-section,
      .performance-section,
      .trends-section,
      .habits-section,
      .productivity-section,
      .predictions-section,
      .recommendations-section,
      .charts-section {
        margin-bottom: 40px;
        padding: 20px;
        background: #f8fafc;
        border-radius: 12px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      }

      .summary-cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-top: 20px;
      }

      .summary-card {
        background: white;
        padding: 20px;
        border-radius: 8px;
        text-align: center;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
      }

      .summary-card h3 {
        color: ${styles.secondaryColor};
        margin-bottom: 10px;
        font-size: 0.9em;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }

      .score {
        font-size: 2em;
        font-weight: bold;
        color: ${styles.primaryColor};
      }

      .score.excellent { color: #10b981; }
      .score.good { color: #3b82f6; }
      .score.average { color: #f59e0b; }
      .score.poor { color: #ef4444; }

      .metrics-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        margin: 20px 0;
      }

      .metric-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 15px;
        background: white;
        border-radius: 6px;
        border-left: 4px solid ${styles.primaryColor};
      }

      .strengths-weaknesses {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 30px;
        margin: 20px 0;
      }

      .strengths, .weaknesses {
        background: white;
        padding: 20px;
        border-radius: 8px;
      }

      .strengths h3 {
        color: #10b981;
        margin-bottom: 15px;
      }

      .weaknesses h3 {
        color: #f59e0b;
        margin-bottom: 15px;
      }

      .progress-bar {
        background: #e5e7eb;
        height: 8px;
        border-radius: 4px;
        overflow: hidden;
        margin: 0 10px;
        flex: 1;
      }

      .progress-fill {
        background: ${styles.primaryColor};
        height: 100%;
        transition: width 0.3s ease;
      }

      .positive { color: #10b981; }
      .negative { color: #ef4444; }

      .report-footer {
        text-align: center;
        margin-top: 40px;
        padding-top: 20px;
        border-top: 1px solid #e5e7eb;
        color: ${styles.secondaryColor};
      }

      @media (max-width: 768px) {
        .summary-cards {
          grid-template-columns: 1fr;
        }
        
        .strengths-weaknesses {
          grid-template-columns: 1fr;
        }
        
        .metrics-grid {
          grid-template-columns: 1fr;
        }
      }
    `
  }

  /**
   * 生成附件（图表等）
   */
  private async generateAttachments(
    analysis: ComprehensiveAnalysis,
    config: ReportConfig
  ): Promise<{ charts: string[]; exports: string[] }> {
    // 这里应该生成实际的图表文件
    // 简化实现，返回图表配置
    return {
      charts: [
        'trend-chart.svg',
        'performance-radar.svg',
        'habits-progress.svg'
      ],
      exports: [
        'report-data.json'
      ]
    }
  }

  /**
   * 生成报告元数据
   */
  private generateReportMetadata(
    analysis: ComprehensiveAnalysis,
    config: ReportConfig
  ): GeneratedReport['metadata'] {
    const now = Date.now()
    const daysBack = config.type === 'daily' ? 1 : 
                     config.type === 'weekly' ? 7 : 
                     config.type === 'monthly' ? 30 : 30

    return {
      generatedAt: now,
      dataRange: {
        startDate: now - (daysBack * 24 * 60 * 60 * 1000),
        endDate: now
      },
      analysisVersion: '1.0.0',
      reportVersion: '1.0.0'
    }
  }

  /**
   * 生成报告标题
   */
  private generateReportTitle(analysis: ComprehensiveAnalysis, config: ReportConfig): string {
    const timeRange = this.formatTimeRange(config.type)
    return `${timeRange}自律行为分析报告`
  }

  /**
   * 生成报告ID
   */
  private generateReportId(): string {
    return `report_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 格式化时间范围
   */
  private formatTimeRange(type: ReportType): string {
    switch (type) {
      case 'daily': return '每日'
      case 'weekly': return '每周'
      case 'monthly': return '每月'
      case 'comprehensive': return '综合'
      default: return '综合'
    }
  }

  /**
   * 获取分数等级样式类
   */
  private getScoreClass(score: number): string {
    if (score >= 85) return 'excellent'
    if (score >= 70) return 'good'
    if (score >= 50) return 'average'
    return 'poor'
  }

  /**
   * 获取趋势图标
   */
  private getTrendIcon(direction: TrendAnalysis['direction']): string {
    switch (direction) {
      case 'increasing': return '📈'
      case 'decreasing': return '📉'
      case 'stable': return '➡️'
      case 'volatile': return '📊'
      default: return '📊'
    }
  }

  /**
   * 获取趋势文本
   */
  private getTrendText(direction: TrendAnalysis['direction']): string {
    switch (direction) {
      case 'increasing': return '上升趋势'
      case 'decreasing': return '下降趋势'
      case 'stable': return '稳定趋势'
      case 'volatile': return '波动趋势'
      default: return '未知趋势'
    }
  }

  /**
   * 获取显著性样式类
   */
  private getSignificanceClass(significance: TrendAnalysis['significance']): string {
    switch (significance) {
      case 'high': return 'significance-high'
      case 'medium': return 'significance-medium'
      case 'low': return 'significance-low'
      default: return 'significance-low'
    }
  }

  /**
   * 获取显著性文本
   */
  private getSignificanceText(significance: TrendAnalysis['significance']): string {
    switch (significance) {
      case 'high': return '高显著性'
      case 'medium': return '中等显著性'
      case 'low': return '低显著性'
      default: return '低显著性'
    }
  }

  /**
   * 获取稳定性样式类
   */
  private getStabilityClass(stability: number): string {
    if (stability >= 80) return 'stability-high'
    if (stability >= 60) return 'stability-medium'
    return 'stability-low'
  }
}

export default ReportGenerator 