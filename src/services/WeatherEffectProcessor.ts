import {
  WeatherType,
  WeatherState,
  WeatherEffect,
  WeatherIntensity,
  TimeOfDay,
  Season
} from '../types/weather';

// 专注训练会话接口
export interface FocusSession {
  startTime: Date;
  duration: number; // 分钟
  baseScore: number;
  difficulty: number; // 1-10
  focusLevel: number; // 0-1
  weather: WeatherState;
}

// 天气影响后的会话结果
export interface EnhancedFocusSession extends FocusSession {
  weatherEffect: WeatherEffect;
  adjustedScore: number;
  adjustedDifficulty: number;
  moodAdjustment: number;
  experienceMultiplier: number;
  bonusPoints: number;
  weatherDescription: string;
  impactSummary: string;
}

// 天气影响配置
export interface WeatherImpactConfig {
  enableWeatherEffects: boolean;
  maxDifficultyAdjustment: number; // 最大难度调整幅度
  maxScoreMultiplier: number; // 最大分数倍数
  moodImpactWeight: number; // 心情影响权重 0-1
  comfortLevelWeight: number; // 舒适度影响权重 0-1
  intensityImpactScale: number; // 强度影响比例 0-1
  timeOfDayBonus: Record<TimeOfDay, number>; // 时间段加成
  seasonalBonus: Record<Season, number>; // 季节加成
}

/**
 * 天气效果处理器
 * 负责计算天气对专注训练的具体影响
 */
export class WeatherEffectProcessor {
  private config: WeatherImpactConfig;

  constructor(config?: Partial<WeatherImpactConfig>) {
    this.config = {
      enableWeatherEffects: true,
      maxDifficultyAdjustment: 3,
      maxScoreMultiplier: 2.5,
      moodImpactWeight: 0.3,
      comfortLevelWeight: 0.4,
      intensityImpactScale: 0.2,
      timeOfDayBonus: {
        [TimeOfDay.DAWN]: 1.1,
        [TimeOfDay.MORNING]: 1.2,
        [TimeOfDay.NOON]: 1.0,
        [TimeOfDay.AFTERNOON]: 1.1,
        [TimeOfDay.EVENING]: 1.05,
        [TimeOfDay.NIGHT]: 0.9,
        [TimeOfDay.MIDNIGHT]: 0.8
      },
      seasonalBonus: {
        [Season.SPRING]: 1.15,
        [Season.SUMMER]: 1.1,
        [Season.AUTUMN]: 1.05,
        [Season.WINTER]: 1.0
      },
      ...config
    };
  }

  /**
   * 处理天气对专注会话的影响
   */
  processWeatherImpact(session: FocusSession): EnhancedFocusSession {
    if (!this.config.enableWeatherEffects) {
      return this.createUnaffectedSession(session);
    }

    const weatherEffect = this.getWeatherEffect(session.weather);
    const enhancedSession = this.applyWeatherEffects(session, weatherEffect);

    return enhancedSession;
  }

  /**
   * 获取天气效果配置
   */
  private getWeatherEffect(weather: WeatherState): WeatherEffect {
    const baseEffects = this.getBaseWeatherEffects();
    const baseEffect = baseEffects[weather.type];
    
    // 根据强度调整效果
    const intensityMultiplier = this.getIntensityMultiplier(weather.intensity);
    
    return {
      ...baseEffect,
      focusMultiplier: this.adjustByIntensity(baseEffect.focusMultiplier, intensityMultiplier),
      comfortLevel: this.adjustByIntensity(baseEffect.comfortLevel, intensityMultiplier),
      moodBonus: this.adjustByIntensity(baseEffect.moodBonus, intensityMultiplier),
      difficultyModifier: this.adjustByIntensity(baseEffect.difficultyModifier, intensityMultiplier),
      soundVolume: Math.min(1, baseEffect.soundVolume * intensityMultiplier)
    };
  }

  /**
   * 获取基础天气效果配置
   */
  private getBaseWeatherEffects(): Record<WeatherType, WeatherEffect> {
    return {
      [WeatherType.SUNNY]: {
        focusMultiplier: 1.2,
        comfortLevel: 9,
        moodBonus: 3,
        difficultyModifier: -1,
        backgroundFilter: 'brightness(1.1) contrast(1.05)',
        lightingEffect: {
          brightness: 1.0,
          contrast: 1.1,
          saturation: 1.2,
          hue: 50,
          warmth: 0.3
        },
        ambientSound: 'birds_chirping',
        soundVolume: 0.6,
        specialEffects: [{
          type: 'rainbow',
          probability: 0.05,
          duration: 10,
          intensity: 0.3
        }]
      },
      [WeatherType.PARTLY_CLOUDY]: {
        focusMultiplier: 1.0,
        comfortLevel: 8,
        moodBonus: 1,
        difficultyModifier: 0,
        backgroundFilter: 'brightness(0.95)',
        lightingEffect: {
          brightness: 0.9,
          contrast: 1.0,
          saturation: 1.0,
          hue: 0,
          warmth: 0.1
        },
        ambientSound: 'gentle_wind',
        soundVolume: 0.4
      },
      [WeatherType.CLOUDY]: {
        focusMultiplier: 0.9,
        comfortLevel: 6,
        moodBonus: -1,
        difficultyModifier: 0,
        backgroundFilter: 'brightness(0.8) contrast(0.95)',
        lightingEffect: {
          brightness: 0.8,
          contrast: 0.9,
          saturation: 0.8,
          hue: 200,
          warmth: -0.2
        },
        ambientSound: 'wind_rustling',
        soundVolume: 0.5
      },
      [WeatherType.RAINY]: {
        focusMultiplier: 1.15,
        comfortLevel: 7,
        moodBonus: 2,
        difficultyModifier: -0.5,
        backgroundFilter: 'brightness(0.7) contrast(0.9)',
        particleEffect: {
          type: 'rain',
          count: 100,
          speed: 5,
          size: 1,
          opacity: 0.6,
          direction: 75
        },
        lightingEffect: {
          brightness: 0.7,
          contrast: 0.9,
          saturation: 0.9,
          hue: 210,
          warmth: -0.3
        },
        ambientSound: 'rain_gentle',
        soundVolume: 0.7
      },
      [WeatherType.HEAVY_RAIN]: {
        focusMultiplier: 0.8,
        comfortLevel: 5,
        moodBonus: -2,
        difficultyModifier: 1,
        backgroundFilter: 'brightness(0.6) contrast(0.8)',
        particleEffect: {
          type: 'rain',
          count: 300,
          speed: 8,
          size: 2,
          opacity: 0.8,
          direction: 80
        },
        lightingEffect: {
          brightness: 0.6,
          contrast: 0.8,
          saturation: 0.7,
          hue: 220,
          warmth: -0.4
        },
        ambientSound: 'rain_heavy',
        soundVolume: 0.9
      },
      [WeatherType.THUNDERSTORM]: {
        focusMultiplier: 0.6,
        comfortLevel: 3,
        moodBonus: -3,
        difficultyModifier: 2,
        backgroundFilter: 'brightness(0.4) contrast(1.2)',
        particleEffect: {
          type: 'rain',
          count: 400,
          speed: 10,
          size: 2,
          opacity: 0.9,
          direction: 85
        },
        lightingEffect: {
          brightness: 0.4,
          contrast: 1.2,
          saturation: 0.6,
          hue: 240,
          warmth: -0.5
        },
        ambientSound: 'thunderstorm',
        soundVolume: 0.8,
        specialEffects: [{
          type: 'lightning',
          probability: 0.15,
          duration: 0.5,
          intensity: 1.0
        }]
      },
      [WeatherType.SNOWY]: {
        focusMultiplier: 1.3,
        comfortLevel: 8,
        moodBonus: 2,
        difficultyModifier: -1,
        backgroundFilter: 'brightness(1.2) contrast(1.1)',
        particleEffect: {
          type: 'snow',
          count: 150,
          speed: 2,
          size: 3,
          opacity: 0.8,
          direction: 90,
          color: '#ffffff'
        },
        lightingEffect: {
          brightness: 1.2,
          contrast: 1.1,
          saturation: 0.8,
          hue: 200,
          warmth: -0.1
        },
        ambientSound: 'wind_soft',
        soundVolume: 0.5
      },
      [WeatherType.FOGGY]: {
        focusMultiplier: 1.4,
        comfortLevel: 6,
        moodBonus: 0,
        difficultyModifier: -1.5,
        backgroundFilter: 'brightness(0.8) blur(2px)',
        lightingEffect: {
          brightness: 0.8,
          contrast: 0.7,
          saturation: 0.6,
          hue: 180,
          warmth: 0
        },
        ambientSound: 'silence',
        soundVolume: 0.2,
        specialEffects: [{
          type: 'fog_overlay',
          probability: 1.0,
          duration: 0,
          intensity: 0.7
        }]
      },
      [WeatherType.WINDY]: {
        focusMultiplier: 0.85,
        comfortLevel: 6,
        moodBonus: 0,
        difficultyModifier: 0.5,
        backgroundFilter: 'brightness(0.9)',
        particleEffect: {
          type: 'leaves',
          count: 50,
          speed: 6,
          size: 2,
          opacity: 0.7,
          direction: 45
        },
        lightingEffect: {
          brightness: 0.9,
          contrast: 1.0,
          saturation: 1.0,
          hue: 60,
          warmth: 0.2
        },
        ambientSound: 'wind_moderate',
        soundVolume: 0.8
      }
    };
  }

  /**
   * 获取强度倍数
   */
  private getIntensityMultiplier(intensity: WeatherIntensity): number {
    const multipliers = {
      [WeatherIntensity.LIGHT]: 0.7,
      [WeatherIntensity.MODERATE]: 1.0,
      [WeatherIntensity.HEAVY]: 1.3,
      [WeatherIntensity.EXTREME]: 1.6
    };
    return multipliers[intensity];
  }

  /**
   * 根据强度调整数值
   */
  private adjustByIntensity(baseValue: number, intensityMultiplier: number): number {
    const adjustment = (baseValue - 1) * this.config.intensityImpactScale * (intensityMultiplier - 1);
    return baseValue + adjustment;
  }

  /**
   * 应用天气效果到专注会话
   */
  private applyWeatherEffects(session: FocusSession, weatherEffect: WeatherEffect): EnhancedFocusSession {
    // 计算基础调整
    const focusAdjustment = this.calculateFocusAdjustment(session, weatherEffect);
    const difficultyAdjustment = this.calculateDifficultyAdjustment(session, weatherEffect);
    const scoreMultiplier = this.calculateScoreMultiplier(session, weatherEffect);
    const moodAdjustment = this.calculateMoodAdjustment(session, weatherEffect);
    
    // 应用时间段和季节加成
    const timeBonus = this.config.timeOfDayBonus[session.weather.timeOfDay] || 1.0;
    const seasonBonus = this.config.seasonalBonus[this.getCurrentSeason()] || 1.0;
    const totalMultiplier = scoreMultiplier * timeBonus * seasonBonus;
    
    // 计算调整后的分数
    const adjustedScore = Math.round(session.baseScore * totalMultiplier);
    const adjustedDifficulty = Math.max(1, Math.min(10, session.difficulty + difficultyAdjustment));
    
    // 计算经验值倍数
    const experienceMultiplier = this.calculateExperienceMultiplier(weatherEffect, timeBonus, seasonBonus);
    
    // 计算奖励积分
    const bonusPoints = this.calculateBonusPoints(session, weatherEffect, totalMultiplier);
    
    // 生成描述
    const weatherDescription = this.generateWeatherDescription(session.weather, weatherEffect);
    const impactSummary = this.generateImpactSummary(weatherEffect, totalMultiplier, adjustedDifficulty - session.difficulty);

    return {
      ...session,
      weatherEffect,
      adjustedScore,
      adjustedDifficulty,
      moodAdjustment,
      experienceMultiplier,
      bonusPoints,
      weatherDescription,
      impactSummary
    };
  }

  /**
   * 计算专注力调整
   */
  private calculateFocusAdjustment(session: FocusSession, weatherEffect: WeatherEffect): number {
    const baseAdjustment = (weatherEffect.focusMultiplier - 1) * session.focusLevel;
    const comfortAdjustment = (weatherEffect.comfortLevel - 5) / 10 * this.config.comfortLevelWeight;
    const moodAdjustment = weatherEffect.moodBonus / 10 * this.config.moodImpactWeight;
    
    return baseAdjustment + comfortAdjustment + moodAdjustment;
  }

  /**
   * 计算难度调整
   */
  private calculateDifficultyAdjustment(session: FocusSession, weatherEffect: WeatherEffect): number {
    const baseAdjustment = weatherEffect.difficultyModifier;
    const focusBasedAdjustment = (1 - session.focusLevel) * 0.5; // 专注度低时增加难度
    
    return Math.max(-this.config.maxDifficultyAdjustment, 
           Math.min(this.config.maxDifficultyAdjustment, baseAdjustment + focusBasedAdjustment));
  }

  /**
   * 计算分数倍数
   */
  private calculateScoreMultiplier(session: FocusSession, weatherEffect: WeatherEffect): number {
    const baseMult = weatherEffect.focusMultiplier;
    const comfortBonus = (weatherEffect.comfortLevel - 5) / 20; // -0.25 到 +0.25
    const moodBonus = weatherEffect.moodBonus / 30; // -0.1 到 +0.1
    const focusBonus = session.focusLevel * 0.2; // 专注度加成
    
    const totalMultiplier = baseMult + comfortBonus + moodBonus + focusBonus;
    
    return Math.max(0.5, Math.min(this.config.maxScoreMultiplier, totalMultiplier));
  }

  /**
   * 计算心情调整
   */
  private calculateMoodAdjustment(session: FocusSession, weatherEffect: WeatherEffect): number {
    const baseMood = weatherEffect.moodBonus;
    const comfortMood = (weatherEffect.comfortLevel - 5) / 2;
    const durationMood = Math.min(session.duration / 60, 1) * 2; // 长时间专注奖励
    
    return baseMood + comfortMood + durationMood;
  }

  /**
   * 计算经验值倍数
   */
  private calculateExperienceMultiplier(weatherEffect: WeatherEffect, timeBonus: number, seasonBonus: number): number {
    const baseMult = weatherEffect.focusMultiplier;
    const comfortMult = 1 + (weatherEffect.comfortLevel - 5) / 20;
    const totalMult = baseMult * comfortMult * timeBonus * seasonBonus;
    
    return Math.max(0.8, Math.min(2.0, totalMult));
  }

  /**
   * 计算奖励积分
   */
  private calculateBonusPoints(session: FocusSession, weatherEffect: WeatherEffect, scoreMultiplier: number): number {
    let bonus = 0;
    
    // 天气挑战奖励
    if (weatherEffect.comfortLevel <= 4) {
      bonus += 50; // 恶劣天气挑战奖励
    }
    
    // 完美专注奖励
    if (session.focusLevel >= 0.9) {
      bonus += Math.round(30 * scoreMultiplier);
    }
    
    // 长时间专注奖励
    if (session.duration >= 45) {
      bonus += Math.round(20 * scoreMultiplier);
    }
    
    // 特殊天气奖励
    if (weatherEffect.specialEffects && weatherEffect.specialEffects.length > 0) {
      bonus += 25;
    }

    return bonus;
  }

  /**
   * 生成天气描述
   */
  private generateWeatherDescription(weather: WeatherState, effect: WeatherEffect): string {
    const typeNames = {
      [WeatherType.SUNNY]: '阳光明媚',
      [WeatherType.PARTLY_CLOUDY]: '局部多云',
      [WeatherType.CLOUDY]: '阴云密布',
      [WeatherType.RAINY]: '细雨飘洒',
      [WeatherType.HEAVY_RAIN]: '大雨倾盆',
      [WeatherType.THUNDERSTORM]: '雷电交加',
      [WeatherType.SNOWY]: '雪花飞舞',
      [WeatherType.FOGGY]: '雾气弥漫',
      [WeatherType.WINDY]: '清风徐来'
    };

    const intensityDesc = {
      [WeatherIntensity.LIGHT]: '轻微',
      [WeatherIntensity.MODERATE]: '适中',
      [WeatherIntensity.HEAVY]: '强烈',
      [WeatherIntensity.EXTREME]: '极端'
    };

    const comfortDesc = effect.comfortLevel >= 8 ? '非常舒适' : 
                       effect.comfortLevel >= 6 ? '较为舒适' :
                       effect.comfortLevel >= 4 ? '一般' : '具有挑战性';

    return `${typeNames[weather.type]}，强度${intensityDesc[weather.intensity]}，${comfortDesc}的专注环境。温度${Math.round(weather.temperature)}°C，湿度${weather.humidity}%。`;
  }

  /**
   * 生成影响总结
   */
  private generateImpactSummary(effect: WeatherEffect, scoreMultiplier: number, difficultyChange: number): string {
    const parts: string[] = [];

    // 分数影响
    if (scoreMultiplier > 1.1) {
      parts.push(`分数提升${Math.round((scoreMultiplier - 1) * 100)}%`);
    } else if (scoreMultiplier < 0.9) {
      parts.push(`分数降低${Math.round((1 - scoreMultiplier) * 100)}%`);
    }

    // 难度影响
    if (difficultyChange > 0.5) {
      parts.push(`难度增加${difficultyChange.toFixed(1)}`);
    } else if (difficultyChange < -0.5) {
      parts.push(`难度降低${Math.abs(difficultyChange).toFixed(1)}`);
    }

    // 心情影响
    if (effect.moodBonus > 2) {
      parts.push('心情大幅提升');
    } else if (effect.moodBonus > 0) {
      parts.push('心情有所提升');
    } else if (effect.moodBonus < -2) {
      parts.push('心情略受影响');
    }

    // 专注力影响
    if (effect.focusMultiplier > 1.2) {
      parts.push('专注力显著增强');
    } else if (effect.focusMultiplier > 1.0) {
      parts.push('专注力有所增强');
    } else if (effect.focusMultiplier < 0.8) {
      parts.push('专注力面临挑战');
    }

    return parts.length > 0 ? parts.join('，') : '天气影响平稳';
  }

  /**
   * 创建未受影响的会话
   */
  private createUnaffectedSession(session: FocusSession): EnhancedFocusSession {
    const neutralEffect: WeatherEffect = {
      focusMultiplier: 1.0,
      comfortLevel: 5,
      moodBonus: 0,
      difficultyModifier: 0,
      backgroundFilter: 'none',
      soundVolume: 0.5
    };

    return {
      ...session,
      weatherEffect: neutralEffect,
      adjustedScore: session.baseScore,
      adjustedDifficulty: session.difficulty,
      moodAdjustment: 0,
      experienceMultiplier: 1.0,
      bonusPoints: 0,
      weatherDescription: '天气影响已禁用',
      impactSummary: '无天气影响'
    };
  }

  /**
   * 获取当前季节
   */
  private getCurrentSeason(): Season {
    const month = new Date().getMonth() + 1;
    if (month >= 3 && month <= 5) return Season.SPRING;
    if (month >= 6 && month <= 8) return Season.SUMMER;
    if (month >= 9 && month <= 11) return Season.AUTUMN;
    return Season.WINTER;
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<WeatherImpactConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * 获取当前配置
   */
  getConfig(): WeatherImpactConfig {
    return { ...this.config };
  }

  /**
   * 预览天气效果
   * 用于测试和调试
   */
  previewWeatherEffect(weatherType: WeatherType, intensity: WeatherIntensity = WeatherIntensity.MODERATE): WeatherEffect {
    const weather: WeatherState = {
      type: weatherType,
      intensity,
      temperature: 20,
      humidity: 60,
      windSpeed: 10,
      visibility: 10,
      pressure: 1013,
      timeOfDay: TimeOfDay.AFTERNOON,
      timestamp: new Date()
    };

    return this.getWeatherEffect(weather);
  }

  /**
   * 计算最佳天气推荐
   * 基于用户的专注目标和偏好
   */
  recommendOptimalWeather(targetDifficulty: number, sessionDuration: number, userPreferences?: Partial<WeatherEffect>): WeatherType[] {
    const recommendations: Array<{ type: WeatherType, score: number }> = [];

    Object.values(WeatherType).forEach(weatherType => {
      const effect = this.previewWeatherEffect(weatherType);
      let score = 0;

      // 基于目标难度评分
      const difficultyMatch = 1 - Math.abs(effect.difficultyModifier - (targetDifficulty - 5)) / 5;
      score += difficultyMatch * 40;

      // 基于会话时长评分
      if (sessionDuration > 30 && effect.focusMultiplier > 1.1) {
        score += 20; // 长会话偏向高专注天气
      }

      // 舒适度评分
      score += effect.comfortLevel * 4;

      // 心情加成评分
      score += Math.max(0, effect.moodBonus) * 3;

      // 用户偏好匹配
      if (userPreferences) {
        if (userPreferences.focusMultiplier && Math.abs(effect.focusMultiplier - userPreferences.focusMultiplier) < 0.2) {
          score += 15;
        }
        if (userPreferences.comfortLevel && Math.abs(effect.comfortLevel - userPreferences.comfortLevel) < 2) {
          score += 10;
        }
      }

      recommendations.push({ type: weatherType, score });
    });

    // 按评分排序并返回前3个
    return recommendations
      .sort((a, b) => b.score - a.score)
      .slice(0, 3)
      .map(r => r.type);
  }
}

// 导出默认实例
export const weatherEffectProcessor = new WeatherEffectProcessor(); 