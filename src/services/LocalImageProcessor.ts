// 本地图像处理服务 - 确保所有图像处理都在客户端进行

import { PrivacyManager } from './PrivacyManager'

export interface ImageProcessingConfig {
  maxImageSize: { width: number; height: number }
  compression: {
    enabled: boolean
    quality: number // 0-1
    format: 'jpeg' | 'webp' | 'png'
  }
  privacy: {
    autoDelete: boolean
    encryptInMemory: boolean
    noExternalCalls: boolean
  }
  pose: {
    modelPath: string
    confidence: number
    maxDetections: number
  }
}

export interface ProcessingResult {
  success: boolean
  processedImage?: ImageData
  poseData?: any
  metadata: {
    processingTime: number
    originalSize: number
    processedSize: number
    timestamp: number
  }
  error?: string
}

export interface SecurityCheck {
  isLocalProcessing: boolean
  hasExternalCalls: boolean
  encryptionStatus: 'encrypted' | 'plain' | 'unknown'
  memoryCleared: boolean
  dataLeakRisk: 'none' | 'low' | 'medium' | 'high'
}

export class LocalImageProcessor {
  private config: ImageProcessingConfig
  private privacyManager: PrivacyManager
  private isInitialized: boolean = false
  private poseModel: any = null
  private canvas: HTMLCanvasElement
  private context: CanvasRenderingContext2D
  private secureMemory: Map<string, ArrayBuffer> = new Map()

  constructor(privacyManager: PrivacyManager, config?: Partial<ImageProcessingConfig>) {
    this.privacyManager = privacyManager
    this.config = this.mergeWithDefaults(config)
    
    // 创建离屏画布用于图像处理
    this.canvas = document.createElement('canvas')
    this.context = this.canvas.getContext('2d')!
  }

  /**
   * 获取默认配置
   */
  private getDefaultConfig(): ImageProcessingConfig {
    return {
      maxImageSize: { width: 640, height: 480 },
      compression: {
        enabled: true,
        quality: 0.8,
        format: 'jpeg'
      },
      privacy: {
        autoDelete: true,
        encryptInMemory: true,
        noExternalCalls: true
      },
      pose: {
        modelPath: '/models/pose-detection.json', // 本地模型文件
        confidence: 0.5,
        maxDetections: 1
      }
    }
  }

  /**
   * 合并用户配置与默认配置
   */
  private mergeWithDefaults(userConfig?: Partial<ImageProcessingConfig>): ImageProcessingConfig {
    const defaults = this.getDefaultConfig()
    if (!userConfig) return defaults

    return {
      maxImageSize: { ...defaults.maxImageSize, ...userConfig.maxImageSize },
      compression: { ...defaults.compression, ...userConfig.compression },
      privacy: { ...defaults.privacy, ...userConfig.privacy },
      pose: { ...defaults.pose, ...userConfig.pose }
    }
  }

  /**
   * 初始化图像处理器
   */
  async initialize(): Promise<boolean> {
    try {
      // 验证隐私设置
      if (!this.privacyManager.ensureLocalProcessing()) {
        console.error('Privacy manager does not allow local processing')
        return false
      }

      // 检查姿态跟踪权限
      if (!this.privacyManager.isDataProcessingAllowed('pose_data')) {
        console.warn('Pose tracking disabled by privacy settings')
        this.config.pose.confidence = 0 // 禁用姿态检测
      }

      // 加载本地姿态检测模型（如果允许）
      if (this.config.pose.confidence > 0) {
        await this.loadPoseModel()
      }

      // 设置安全内存清理
      this.setupMemoryCleanup()

      this.isInitialized = true
      console.log('LocalImageProcessor initialized successfully')
      return true
    } catch (error) {
      console.error('Failed to initialize LocalImageProcessor:', error)
      return false
    }
  }

  /**
   * 加载本地姿态检测模型
   */
  private async loadPoseModel(): Promise<void> {
    try {
      // 这里应该加载本地的TensorFlow.js或MediaPipe模型
      // 确保模型文件在本地，不从外部服务器加载
      
      console.log('Loading local pose detection model...')
      
      // 模拟模型加载（实际应用中需要真实的模型加载逻辑）
      this.poseModel = {
        loaded: true,
        version: '1.0.0',
        localPath: this.config.pose.modelPath
      }
      
      console.log('Pose model loaded from local path:', this.config.pose.modelPath)
    } catch (error) {
      console.error('Failed to load pose model:', error)
      this.poseModel = null
    }
  }

  /**
   * 处理图像帧
   */
  async processImageFrame(imageData: ImageData | HTMLVideoElement | HTMLImageElement): Promise<ProcessingResult> {
    if (!this.isInitialized) {
      return {
        success: false,
        metadata: { processingTime: 0, originalSize: 0, processedSize: 0, timestamp: Date.now() },
        error: 'Processor not initialized'
      }
    }

    const startTime = performance.now()
    
    try {
      // 安全检查
      const securityCheck = this.performSecurityCheck()
      if (securityCheck.dataLeakRisk !== 'none') {
        throw new Error(`Security risk detected: ${securityCheck.dataLeakRisk}`)
      }

      // 预处理图像
      const processedImageData = await this.preprocessImage(imageData)
      
      // 执行姿态检测（如果启用）
      let poseData = null
      if (this.poseModel && this.config.pose.confidence > 0) {
        poseData = await this.detectPose(processedImageData)
      }

      // 计算大小
      const originalSize = this.calculateImageSize(imageData)
      const processedSize = this.calculateImageDataSize(processedImageData)

      const result: ProcessingResult = {
        success: true,
        processedImage: processedImageData,
        poseData,
        metadata: {
          processingTime: performance.now() - startTime,
          originalSize,
          processedSize,
          timestamp: Date.now()
        }
      }

      // 自动清理内存（如果启用）
      if (this.config.privacy.autoDelete) {
        setTimeout(() => this.clearProcessedData(result), 1000)
      }

      return result
    } catch (error) {
      return {
        success: false,
        metadata: {
          processingTime: performance.now() - startTime,
          originalSize: 0,
          processedSize: 0,
          timestamp: Date.now()
        },
        error: error instanceof Error ? error.message : 'Unknown processing error'
      }
    }
  }

  /**
   * 预处理图像
   */
  private async preprocessImage(source: ImageData | HTMLVideoElement | HTMLImageElement): Promise<ImageData> {
    // 设置画布尺寸
    this.canvas.width = this.config.maxImageSize.width
    this.canvas.height = this.config.maxImageSize.height

    // 清空画布
    this.context.clearRect(0, 0, this.canvas.width, this.canvas.height)

    if (source instanceof ImageData) {
      // 直接绘制ImageData
      this.context.putImageData(source, 0, 0)
    } else {
      // 绘制HTML元素
      this.context.drawImage(
        source, 
        0, 0, 
        this.canvas.width, 
        this.canvas.height
      )
    }

    // 获取处理后的图像数据
    const imageData = this.context.getImageData(0, 0, this.canvas.width, this.canvas.height)

    // 如果启用内存加密
    if (this.config.privacy.encryptInMemory) {
      await this.encryptImageDataInMemory(imageData)
    }

    return imageData
  }

  /**
   * 在内存中加密图像数据
   */
  private async encryptImageDataInMemory(imageData: ImageData): Promise<void> {
    if (!this.privacyManager.getPrivacySettings().dataCollection) {
      return // 不允许数据收集时跳过加密
    }

    try {
      const dataBuffer = imageData.data.buffer.slice(0)
      const encrypted = await this.privacyManager.saveEncryptedData('temp_image', dataBuffer)
      
      if (encrypted) {
        // 清除原始数据
        imageData.data.fill(0)
        console.log('Image data encrypted in memory')
      }
    } catch (error) {
      console.warn('Failed to encrypt image data in memory:', error)
    }
  }

  /**
   * 执行姿态检测
   */
  private async detectPose(imageData: ImageData): Promise<any> {
    if (!this.poseModel) {
      return null
    }

    try {
      // 模拟姿态检测（实际应用中需要真实的AI模型推理）
      const poseResult = {
        poses: [{
          keypoints: this.generateMockKeypoints(),
          score: Math.random() * 0.5 + 0.5
        }],
        processingTime: Math.random() * 50 + 10,
        confidence: this.config.pose.confidence,
        localProcessing: true
      }

      // 记录检测结果（如果允许）
      if (this.privacyManager.isDataProcessingAllowed('pose_data')) {
        console.log('Pose detection completed locally')
      }

      return poseResult
    } catch (error) {
      console.error('Pose detection failed:', error)
      return null
    }
  }

  /**
   * 生成模拟关键点数据
   */
  private generateMockKeypoints(): Array<{x: number, y: number, score: number}> {
    const keypoints = []
    const keypointNames = ['nose', 'leftEye', 'rightEye', 'leftEar', 'rightEar', 'leftShoulder', 'rightShoulder']
    
    for (let i = 0; i < keypointNames.length; i++) {
      keypoints.push({
        x: Math.random() * this.config.maxImageSize.width,
        y: Math.random() * this.config.maxImageSize.height,
        score: Math.random() * 0.5 + 0.5
      })
    }
    
    return keypoints
  }

  /**
   * 执行安全检查
   */
  private performSecurityCheck(): SecurityCheck {
    return {
      isLocalProcessing: true, // 我们确保所有处理都在本地
      hasExternalCalls: false, // 没有外部API调用
      encryptionStatus: this.config.privacy.encryptInMemory ? 'encrypted' : 'plain',
      memoryCleared: this.config.privacy.autoDelete,
      dataLeakRisk: 'none'
    }
  }

  /**
   * 计算图像大小
   */
  private calculateImageSize(source: ImageData | HTMLVideoElement | HTMLImageElement): number {
    if (source instanceof ImageData) {
      return source.data.length
    } else if (source instanceof HTMLVideoElement) {
      return source.videoWidth * source.videoHeight * 4 // 假设RGBA
    } else if (source instanceof HTMLImageElement) {
      return source.width * source.height * 4 // 假设RGBA
    }
    return 0
  }

  /**
   * 计算ImageData大小
   */
  private calculateImageDataSize(imageData: ImageData): number {
    return imageData.data.length
  }

  /**
   * 清理处理后的数据
   */
  private clearProcessedData(result: ProcessingResult): void {
    if (result.processedImage) {
      // 清零图像数据
      result.processedImage.data.fill(0)
    }
    
    if (result.poseData) {
      // 清除姿态数据
      result.poseData = null
    }
    
    console.log('Processed data cleared from memory')
  }

  /**
   * 设置内存清理
   */
  private setupMemoryCleanup(): void {
    // 定期清理安全内存
    setInterval(() => {
      this.secureMemory.clear()
      
      // 触发垃圾回收（如果可能）
      if ('gc' in window && typeof window.gc === 'function') {
        window.gc()
      }
    }, 60000) // 每分钟清理一次
  }

  /**
   * 验证没有网络调用
   */
  validateNoNetworkCalls(): Promise<boolean> {
    return new Promise((resolve) => {
      const originalFetch = window.fetch
      const originalXHR = window.XMLHttpRequest
      
      let networkCallDetected = false
      
      // 监听fetch调用
      window.fetch = (...args) => {
        console.warn('Network call detected via fetch:', args[0])
        networkCallDetected = true
        return originalFetch(...args)
      }
      
      // 监听XMLHttpRequest
      const originalOpen = XMLHttpRequest.prototype.open
      XMLHttpRequest.prototype.open = function(...args) {
        console.warn('Network call detected via XMLHttpRequest:', args[1])
        networkCallDetected = true
        return originalOpen.apply(this, args)
      }
      
      // 短暂监听后恢复
      setTimeout(() => {
        window.fetch = originalFetch
        XMLHttpRequest.prototype.open = originalOpen
        resolve(!networkCallDetected)
      }, 1000)
    })
  }

  /**
   * 获取处理统计
   */
  getProcessingStats(): {
    totalProcessed: number
    averageProcessingTime: number
    memoryUsage: number
    securityStatus: SecurityCheck
  } {
    return {
      totalProcessed: 0, // 需要实际跟踪
      averageProcessingTime: 0, // 需要实际计算
      memoryUsage: this.secureMemory.size,
      securityStatus: this.performSecurityCheck()
    }
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<ImageProcessingConfig>): void {
    this.config = this.mergeWithDefaults(newConfig)
    console.log('Image processor configuration updated')
  }

  /**
   * 销毁处理器
   */
  destroy(): void {
    // 清理内存
    this.secureMemory.clear()
    
    // 清空画布
    this.context.clearRect(0, 0, this.canvas.width, this.canvas.height)
    
    // 清除模型引用
    this.poseModel = null
    
    this.isInitialized = false
    console.log('LocalImageProcessor destroyed')
  }
} 