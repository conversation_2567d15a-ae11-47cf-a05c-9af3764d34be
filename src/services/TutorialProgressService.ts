import { TutorialState } from '../types/tutorial'

export interface TutorialProgressData {
  tutorialProgress: TutorialState['tutorialProgress']
  currentStepIndex: number
  completedSteps: string[]
  lastAccessTime: Date
  totalTimeSpent: number
  sessionCount: number
  skipCount: number
  version: string
}

export interface TutorialProgressBackup {
  id: string
  timestamp: Date
  data: TutorialProgressData
  reason: 'manual' | 'auto' | 'pre-update'
  description?: string
}

export class TutorialProgressService {
  private static readonly STORAGE_KEY = 'selfgame_tutorial_progress'
  private static readonly BACKUP_KEY = 'selfgame_tutorial_backups'
  private static readonly ANALYTICS_KEY = 'selfgame_tutorial_analytics'
  private static readonly VERSION = '1.0.0'
  private static readonly MAX_BACKUPS = 10

  private sessionStartTime: Date | null = null
  private sessionSkipCount: number = 0

  /**
   * 保存引导进度
   */
  async saveProgress(progressData: Partial<TutorialProgressData>): Promise<boolean> {
    try {
      const currentData = this.loadProgress()
      const updatedData: TutorialProgressData = {
        ...currentData,
        ...progressData,
        lastAccessTime: new Date(),
        version: TutorialProgressService.VERSION
      }

      localStorage.setItem(TutorialProgressService.STORAGE_KEY, JSON.stringify(updatedData))
      
      // 记录分析数据
      this.recordAnalytics('progress_saved', { step: progressData.currentStepIndex })
      
      return true
    } catch (error) {
      console.error('保存引导进度失败:', error)
      return false
    }
  }

  /**
   * 加载引导进度
   */
  loadProgress(): TutorialProgressData {
    try {
      const savedData = localStorage.getItem(TutorialProgressService.STORAGE_KEY)
      if (!savedData) {
        return this.getDefaultProgress()
      }

      const parsed = JSON.parse(savedData)
      
      // 版本兼容性检查
      if (parsed.version !== TutorialProgressService.VERSION) {
        console.warn(`Tutorial progress version mismatch. Migrating from ${parsed.version} to ${TutorialProgressService.VERSION}`)
        return this.migrateProgress(parsed)
      }

      return {
        ...parsed,
        lastAccessTime: new Date(parsed.lastAccessTime)
      }
    } catch (error) {
      console.error('加载引导进度失败:', error)
      return this.getDefaultProgress()
    }
  }

  /**
   * 创建进度备份
   */
  createBackup(reason: TutorialProgressBackup['reason'], description?: string): boolean {
    try {
      const currentData = this.loadProgress()
      const backupId = `backup_${Date.now()}`
      
      const backup: TutorialProgressBackup = {
        id: backupId,
        timestamp: new Date(),
        data: currentData,
        reason,
        description
      }

      const backups = this.loadBackups()
      backups.push(backup)

      // 限制备份数量
      if (backups.length > TutorialProgressService.MAX_BACKUPS) {
        backups.splice(0, backups.length - TutorialProgressService.MAX_BACKUPS)
      }

      localStorage.setItem(TutorialProgressService.BACKUP_KEY, JSON.stringify(backups))
      
      this.recordAnalytics('backup_created', { reason, backupId })
      
      return true
    } catch (error) {
      console.error('创建备份失败:', error)
      return false
    }
  }

  /**
   * 从备份恢复进度
   */
  restoreFromBackup(backupId: string): boolean {
    try {
      const backups = this.loadBackups()
      const backup = backups.find(b => b.id === backupId)
      
      if (!backup) {
        console.error(`备份 ${backupId} 不存在`)
        return false
      }

      // 在恢复前创建当前状态的备份
      this.createBackup('manual', '恢复前自动备份')

      localStorage.setItem(TutorialProgressService.STORAGE_KEY, JSON.stringify(backup.data))
      
      this.recordAnalytics('backup_restored', { backupId })
      
      return true
    } catch (error) {
      console.error('从备份恢复失败:', error)
      return false
    }
  }

  /**
   * 获取所有备份
   */
  loadBackups(): TutorialProgressBackup[] {
    try {
      const backupsData = localStorage.getItem(TutorialProgressService.BACKUP_KEY)
      if (!backupsData) return []

      return JSON.parse(backupsData).map((backup: any) => ({
        ...backup,
        timestamp: new Date(backup.timestamp),
        data: {
          ...backup.data,
          lastAccessTime: new Date(backup.data.lastAccessTime)
        }
      }))
    } catch (error) {
      console.error('加载备份失败:', error)
      return []
    }
  }

  /**
   * 清除所有进度数据
   */
  clearAllProgress(): boolean {
    try {
      // 创建清除前备份
      this.createBackup('manual', '清除前备份')
      
      localStorage.removeItem(TutorialProgressService.STORAGE_KEY)
      
      this.recordAnalytics('progress_cleared')
      
      return true
    } catch (error) {
      console.error('清除进度失败:', error)
      return false
    }
  }

  /**
   * 开始会话追踪
   */
  startSession(): void {
    this.sessionStartTime = new Date()
    this.sessionSkipCount = 0
    this.recordAnalytics('session_started')
  }

  /**
   * 结束会话追踪
   */
  endSession(): void {
    if (!this.sessionStartTime) return

    const sessionDuration = Date.now() - this.sessionStartTime.getTime()
    const currentData = this.loadProgress()
    
    this.saveProgress({
      totalTimeSpent: currentData.totalTimeSpent + sessionDuration,
      sessionCount: currentData.sessionCount + 1
    })

    this.recordAnalytics('session_ended', {
      duration: sessionDuration,
      skipCount: this.sessionSkipCount
    })

    this.sessionStartTime = null
    this.sessionSkipCount = 0
  }

  /**
   * 记录跳过步骤
   */
  recordSkip(): void {
    this.sessionSkipCount++
    const currentData = this.loadProgress()
    
    this.saveProgress({
      skipCount: currentData.skipCount + 1
    })

    this.recordAnalytics('step_skipped')
  }

  /**
   * 获取进度统计
   */
  getProgressStats(): {
    completionRate: number
    totalStepsCompleted: number
    averageSessionTime: number
    skipRate: number
    lastActiveDate: Date | null
  } {
    const data = this.loadProgress()
    
    const totalSteps = Object.keys(data.tutorialProgress).length - 1 // 排除completed字段
    const completedSteps = Object.values(data.tutorialProgress).filter(Boolean).length - 1
    
    return {
      completionRate: totalSteps > 0 ? (completedSteps / totalSteps) * 100 : 0,
      totalStepsCompleted: data.completedSteps.length,
      averageSessionTime: data.sessionCount > 0 ? data.totalTimeSpent / data.sessionCount : 0,
      skipRate: data.sessionCount > 0 ? (data.skipCount / data.sessionCount) * 100 : 0,
      lastActiveDate: data.lastAccessTime
    }
  }

  /**
   * 导出进度数据
   */
  exportProgress(): string {
    const data = this.loadProgress()
    const backups = this.loadBackups()
    const analytics = this.loadAnalytics()
    
    return JSON.stringify({
      progress: data,
      backups,
      analytics,
      exportTime: new Date().toISOString(),
      version: TutorialProgressService.VERSION
    }, null, 2)
  }

  /**
   * 导入进度数据
   */
  importProgress(jsonData: string): boolean {
    try {
      const imported = JSON.parse(jsonData)
      
      // 验证数据格式
      if (!imported.progress || !imported.version) {
        throw new Error('无效的导入数据格式')
      }

      // 创建导入前备份
      this.createBackup('manual', '导入前备份')

      // 导入进度数据
      localStorage.setItem(TutorialProgressService.STORAGE_KEY, JSON.stringify(imported.progress))
      
      // 导入备份数据（可选）
      if (imported.backups) {
        localStorage.setItem(TutorialProgressService.BACKUP_KEY, JSON.stringify(imported.backups))
      }

      this.recordAnalytics('progress_imported', { version: imported.version })
      
      return true
    } catch (error) {
      console.error('导入进度失败:', error)
      return false
    }
  }

  /**
   * 记录分析数据
   */
  private recordAnalytics(event: string, data?: any): void {
    try {
      const analytics = this.loadAnalytics()
      analytics.push({
        event,
        timestamp: new Date(),
        data
      })

      // 限制分析数据大小
      if (analytics.length > 1000) {
        analytics.splice(0, analytics.length - 1000)
      }

      localStorage.setItem(TutorialProgressService.ANALYTICS_KEY, JSON.stringify(analytics))
    } catch (error) {
      console.error('记录分析数据失败:', error)
    }
  }

  /**
   * 加载分析数据
   */
  private loadAnalytics(): any[] {
    try {
      const data = localStorage.getItem(TutorialProgressService.ANALYTICS_KEY)
      return data ? JSON.parse(data) : []
    } catch (error) {
      console.error('加载分析数据失败:', error)
      return []
    }
  }

  /**
   * 获取默认进度数据
   */
  private getDefaultProgress(): TutorialProgressData {
    return {
      tutorialProgress: {
        cameraSetup: false,
        firstPlanting: false,
        basicOperations: false,
        completed: false
      },
      currentStepIndex: 0,
      completedSteps: [],
      lastAccessTime: new Date(),
      totalTimeSpent: 0,
      sessionCount: 0,
      skipCount: 0,
      version: TutorialProgressService.VERSION
    }
  }

  /**
   * 迁移旧版本进度数据
   */
  private migrateProgress(oldData: any): TutorialProgressData {
    // 这里处理版本迁移逻辑
    const defaultProgress = this.getDefaultProgress()
    
    return {
      ...defaultProgress,
      ...oldData,
      version: TutorialProgressService.VERSION
    }
  }
} 