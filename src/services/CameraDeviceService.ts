import { CameraSettings, CameraResolution, ExposureMode, FocusMode } from '../types/settings.types'

/**
 * 扩展的摄像头能力接口
 */
interface ExtendedMediaTrackCapabilities extends MediaTrackCapabilities {
  zoom?: ConstrainDouble
  torch?: ConstrainBoolean
  focusMode?: string[]
  exposureMode?: string[]
  brightness?: ConstrainDouble
  contrast?: ConstrainDouble
  saturation?: ConstrainDouble
}

/**
 * 扩展的摄像头约束接口
 */
interface ExtendedMediaTrackConstraints extends MediaTrackConstraints {
  brightness?: ConstrainDouble
  contrast?: ConstrainDouble
  saturation?: ConstrainDouble
  exposureMode?: ConstrainDOMString
  focusMode?: ConstrainDOMString
}

/**
 * 摄像头设备信息
 */
export interface CameraDeviceInfo extends MediaDeviceInfo {
  capabilities?: ExtendedMediaTrackCapabilities
  supported?: {
    resolutions: CameraResolution[]
    frameRates: number[]
    zoom: boolean
    torch: boolean
    focus: boolean
    exposure: boolean
  }
}

/**
 * 摄像头流信息
 */
export interface CameraStreamInfo {
  stream: MediaStream
  track: MediaStreamTrack
  settings: MediaTrackSettings
  capabilities: MediaTrackCapabilities
}

/**
 * 摄像头测试结果
 */
export interface CameraTestResult {
  success: boolean
  deviceId: string
  deviceName: string
  resolution: CameraResolution
  frameRate: number
  errors: string[]
  performance: {
    initTime: number
    firstFrameTime: number
    averageFrameRate: number
  }
  quality: {
    brightness: number
    contrast: number
    sharpness: number
    noiseLevel: number
  }
}

/**
 * 摄像头设备管理服务
 */
export class CameraDeviceService {
  private devices: CameraDeviceInfo[] = []
  private activeStream: MediaStream | null = null
  private activeTrack: MediaStreamTrack | null = null
  private listeners: Map<string, Array<(data: any) => void>> = new Map()
  private isInitialized = false

  /**
   * 初始化服务
   */
  async initialize(): Promise<void> {
    try {
      await this.requestPermissions()
      await this.detectDevices()
      this.isInitialized = true
      this.emit('initialized', { devices: this.devices })
    } catch (error) {
      console.error('摄像头服务初始化失败:', error)
      throw error
    }
  }

  /**
   * 请求摄像头权限
   */
  async requestPermissions(): Promise<boolean> {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ 
        video: { width: 640, height: 480 } 
      })
      
      // 立即停止流，我们只是为了获取权限
      stream.getTracks().forEach(track => track.stop())
      
      this.emit('permissionGranted', { granted: true })
      return true
    } catch (error) {
      console.error('摄像头权限请求失败:', error)
      this.emit('permissionDenied', { error: error.message })
      return false
    }
  }

  /**
   * 检测可用的摄像头设备
   */
  async detectDevices(): Promise<CameraDeviceInfo[]> {
    try {
      const devices = await navigator.mediaDevices.enumerateDevices()
      const videoDevices = devices.filter(device => device.kind === 'videoinput')
      
      this.devices = []
      
      for (const device of videoDevices) {
        const deviceInfo: CameraDeviceInfo = {
          ...device,
          supported: await this.getDeviceCapabilities(device.deviceId)
        }
        this.devices.push(deviceInfo)
      }

      this.emit('devicesDetected', { devices: this.devices })
      return this.devices
    } catch (error) {
      console.error('设备检测失败:', error)
      throw error
    }
  }

     /**
    * 获取设备能力
    */
   private async getDeviceCapabilities(deviceId: string) {
     try {
       const stream = await navigator.mediaDevices.getUserMedia({
         video: { deviceId: { exact: deviceId } }
       })
       
       const track = stream.getVideoTracks()[0]
       const capabilities = track.getCapabilities() as ExtendedMediaTrackCapabilities
       
       // 检测支持的分辨率
       const resolutions = await this.detectSupportedResolutions(deviceId)
       
       // 检测支持的帧率
       const frameRates = capabilities.frameRate 
         ? [capabilities.frameRate.min, capabilities.frameRate.max]
         : [15, 30]

       stream.getTracks().forEach(t => t.stop())

       return {
         resolutions,
         frameRates,
         zoom: !!capabilities.zoom,
         torch: !!capabilities.torch,
         focus: !!capabilities.focusMode,
         exposure: !!capabilities.exposureMode
       }
     } catch (error) {
       console.warn(`获取设备 ${deviceId} 能力失败:`, error)
       return {
         resolutions: [
           { width: 640, height: 480, label: '480p' },
           { width: 1280, height: 720, label: '720p HD' },
           { width: 1920, height: 1080, label: '1080p Full HD' }
         ],
         frameRates: [15, 30],
         zoom: false,
         torch: false,
         focus: false,
         exposure: false
       }
     }
   }

  /**
   * 检测支持的分辨率
   */
  private async detectSupportedResolutions(deviceId: string): Promise<CameraResolution[]> {
    const testResolutions: CameraResolution[] = [
      { width: 320, height: 240, label: '240p' },
      { width: 640, height: 480, label: '480p' },
      { width: 854, height: 480, label: '480p Wide' },
      { width: 1280, height: 720, label: '720p HD' },
      { width: 1920, height: 1080, label: '1080p Full HD' },
      { width: 2560, height: 1440, label: '1440p 2K' },
      { width: 3840, height: 2160, label: '2160p 4K' }
    ]

    const supportedResolutions: CameraResolution[] = []

    for (const resolution of testResolutions) {
      try {
        const stream = await navigator.mediaDevices.getUserMedia({
          video: {
            deviceId: { exact: deviceId },
            width: { exact: resolution.width },
            height: { exact: resolution.height }
          }
        })
        
        const track = stream.getVideoTracks()[0]
        const settings = track.getSettings()
        
        if (settings.width === resolution.width && settings.height === resolution.height) {
          supportedResolutions.push(resolution)
        }
        
        stream.getTracks().forEach(t => t.stop())
      } catch (error) {
        // 分辨率不支持，跳过
      }
    }

    return supportedResolutions.length > 0 ? supportedResolutions : [
      { width: 640, height: 480, label: '480p' },
      { width: 1280, height: 720, label: '720p HD' }
    ]
  }

  /**
   * 启动摄像头流
   */
  async startCamera(settings: CameraSettings): Promise<CameraStreamInfo> {
    try {
      // 停止当前流
      await this.stopCamera()

      const constraints: MediaStreamConstraints = {
        video: {
          deviceId: settings.deviceId ? { exact: settings.deviceId } : undefined,
          width: { ideal: settings.resolution.width },
          height: { ideal: settings.resolution.height },
          frameRate: { ideal: settings.fps }
        }
      }

      const stream = await navigator.mediaDevices.getUserMedia(constraints)
      const track = stream.getVideoTracks()[0]
      
      // 应用设置
      await this.applyCameraSettings(track, settings)
      
      this.activeStream = stream
      this.activeTrack = track
      
      const streamInfo: CameraStreamInfo = {
        stream,
        track,
        settings: track.getSettings(),
        capabilities: track.getCapabilities()
      }

      this.emit('cameraStarted', streamInfo)
      return streamInfo
    } catch (error) {
      console.error('启动摄像头失败:', error)
      this.emit('cameraError', { error: error.message })
      throw error
    }
  }

  /**
   * 停止摄像头流
   */
  async stopCamera(): Promise<void> {
    if (this.activeStream) {
      this.activeStream.getTracks().forEach(track => track.stop())
      this.activeStream = null
      this.activeTrack = null
      this.emit('cameraStopped', {})
    }
  }

     /**
    * 应用摄像头设置
    */
   private async applyCameraSettings(track: MediaStreamTrack, settings: CameraSettings): Promise<void> {
     try {
       const capabilities = track.getCapabilities() as ExtendedMediaTrackCapabilities
       const constraints: ExtendedMediaTrackConstraints = {}

       // 应用亮度设置
       if (capabilities.brightness) {
         const brightnessRange = capabilities.brightness.max - capabilities.brightness.min
         const brightnessValue = capabilities.brightness.min + (brightnessRange * settings.brightness / 100)
         constraints.brightness = { ideal: brightnessValue }
       }

       // 应用对比度设置
       if (capabilities.contrast) {
         const contrastRange = capabilities.contrast.max - capabilities.contrast.min
         const contrastValue = capabilities.contrast.min + (contrastRange * settings.contrast / 100)
         constraints.contrast = { ideal: contrastValue }
       }

       // 应用饱和度设置
       if (capabilities.saturation) {
         const saturationRange = capabilities.saturation.max - capabilities.saturation.min
         const saturationValue = capabilities.saturation.min + (saturationRange * settings.saturation / 100)
         constraints.saturation = { ideal: saturationValue }
       }

       // 应用曝光模式
       if (capabilities.exposureMode && settings.exposureMode !== ExposureMode.AUTO) {
         constraints.exposureMode = { ideal: settings.exposureMode as any }
       }

       // 应用对焦模式
       if (capabilities.focusMode && settings.focusMode !== FocusMode.AUTO) {
         constraints.focusMode = { ideal: settings.focusMode as any }
       }

       await track.applyConstraints(constraints)
     } catch (error) {
       console.warn('应用摄像头设置失败:', error)
     }
   }

  /**
   * 测试摄像头设备
   */
  async testCamera(deviceId: string, resolution?: CameraResolution): Promise<CameraTestResult> {
    const startTime = performance.now()
    const testResult: CameraTestResult = {
      success: false,
      deviceId,
      deviceName: '',
      resolution: resolution || { width: 640, height: 480, label: '480p' },
      frameRate: 0,
      errors: [],
      performance: {
        initTime: 0,
        firstFrameTime: 0,
        averageFrameRate: 0
      },
      quality: {
        brightness: 0,
        contrast: 0,
        sharpness: 0,
        noiseLevel: 0
      }
    }

    try {
      // 找到设备名称
      const device = this.devices.find(d => d.deviceId === deviceId)
      testResult.deviceName = device?.label || '未知设备'

      // 启动测试流
      const stream = await navigator.mediaDevices.getUserMedia({
        video: {
          deviceId: { exact: deviceId },
          width: { ideal: testResult.resolution.width },
          height: { ideal: testResult.resolution.height }
        }
      })

      const track = stream.getVideoTracks()[0]
      const settings = track.getSettings()
      
      testResult.performance.initTime = performance.now() - startTime
      testResult.frameRate = settings.frameRate || 30

      // 创建视频元素进行质量分析
      const video = document.createElement('video')
      video.srcObject = stream
      video.play()

      // 等待第一帧
      await new Promise<void>((resolve) => {
        video.addEventListener('loadeddata', () => {
          testResult.performance.firstFrameTime = performance.now() - startTime
          resolve()
        })
      })

      // 进行质量分析
      await this.analyzeVideoQuality(video, testResult)

      testResult.success = true
      stream.getTracks().forEach(t => t.stop())
      
    } catch (error) {
      testResult.errors.push(error.message)
    }

    this.emit('testCompleted', testResult)
    return testResult
  }

  /**
   * 分析视频质量
   */
  private async analyzeVideoQuality(video: HTMLVideoElement, result: CameraTestResult): Promise<void> {
    return new Promise((resolve) => {
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')!
      
      canvas.width = video.videoWidth
      canvas.height = video.videoHeight

      // 采集几帧进行分析
      let frameCount = 0
      const maxFrames = 10
      let totalBrightness = 0
      let totalContrast = 0

      const analyzeFrame = () => {
        ctx.drawImage(video, 0, 0)
        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height)
        const data = imageData.data

        // 计算亮度
        let brightness = 0
        for (let i = 0; i < data.length; i += 4) {
          brightness += (data[i] + data[i + 1] + data[i + 2]) / 3
        }
        brightness = brightness / (data.length / 4)
        totalBrightness += brightness

        // 计算对比度（简化版）
        let contrast = 0
        for (let i = 0; i < data.length; i += 4) {
          const pixel = (data[i] + data[i + 1] + data[i + 2]) / 3
          contrast += Math.abs(pixel - brightness)
        }
        contrast = contrast / (data.length / 4)
        totalContrast += contrast

        frameCount++
        
        if (frameCount < maxFrames) {
          requestAnimationFrame(analyzeFrame)
        } else {
          result.quality.brightness = Math.round(totalBrightness / frameCount)
          result.quality.contrast = Math.round(totalContrast / frameCount)
          result.quality.sharpness = Math.round(totalContrast / frameCount * 2) // 简化的锐度
          result.quality.noiseLevel = Math.round(Math.random() * 20) // 模拟噪声检测
          resolve()
        }
      }

      // 开始分析
      requestAnimationFrame(analyzeFrame)
    })
  }

  /**
   * 获取当前活动的流
   */
  getActiveStream(): MediaStream | null {
    return this.activeStream
  }

  /**
   * 获取设备列表
   */
  getDevices(): CameraDeviceInfo[] {
    return [...this.devices]
  }

  /**
   * 获取特定设备信息
   */
  getDevice(deviceId: string): CameraDeviceInfo | undefined {
    return this.devices.find(device => device.deviceId === deviceId)
  }

     /**
    * 截图
    */
   async capturePhoto(): Promise<string | null> {
     if (!this.activeStream) return null

     const video = document.createElement('video')
     video.srcObject = this.activeStream
     video.play()

     return new Promise<string>((resolve) => {
       video.addEventListener('loadedmetadata', () => {
         const canvas = document.createElement('canvas')
         canvas.width = video.videoWidth
         canvas.height = video.videoHeight
         
         const ctx = canvas.getContext('2d')!
         ctx.drawImage(video, 0, 0)
         
         const dataURL = canvas.toDataURL('image/jpeg', 0.8)
         resolve(dataURL)
       })
     })
   }

  /**
   * 事件监听
   */
  addEventListener(event: string, listener: (data: any) => void): void {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, [])
    }
    this.listeners.get(event)!.push(listener)
  }

  /**
   * 移除事件监听
   */
  removeEventListener(event: string, listener: (data: any) => void): void {
    const eventListeners = this.listeners.get(event)
    if (eventListeners) {
      const index = eventListeners.indexOf(listener)
      if (index > -1) {
        eventListeners.splice(index, 1)
      }
    }
  }

  /**
   * 发出事件
   */
  private emit(event: string, data: any): void {
    const eventListeners = this.listeners.get(event)
    if (eventListeners) {
      eventListeners.forEach(listener => {
        try {
          listener(data)
        } catch (error) {
          console.error(`事件监听器错误 (${event}):`, error)
        }
      })
    }
  }

  /**
   * 检查是否已初始化
   */
  isReady(): boolean {
    return this.isInitialized
  }

  /**
   * 销毁服务
   */
  destroy(): void {
    this.stopCamera()
    this.listeners.clear()
    this.devices = []
    this.isInitialized = false
  }
}

// 单例实例
export const cameraDeviceService = new CameraDeviceService() 