import {
  WeatherType,
  WeatherIntensity,
  WeatherState,
  WeatherForecast,
  WeatherChangeReason,
  TimeOfDay,
  Season
} from '../types/weather'
import { WeatherManager } from './WeatherManager'

/**
 * 天气预报项扩展接口
 */
interface ExtendedWeatherForecast extends WeatherForecast {
  shortTermTrend: 'improving' | 'stable' | 'deteriorating'
  alertLevel: 'none' | 'low' | 'medium' | 'high'
  recommendations: string[]
  impactOnFocus: {
    score: number // -10 到 +10
    description: string
    suggestions: string[]
  }
}

/**
 * 天气警报接口
 */
interface WeatherAlert {
  id: string
  type: 'severe_weather' | 'focus_impact' | 'weather_change' | 'optimal_conditions'
  severity: 'info' | 'warning' | 'alert' | 'emergency'
  title: string
  description: string
  weatherType: WeatherType
  startTime: Date
  endTime?: Date
  isActive: boolean
  isDismissed: boolean
  recommendations: string[]
}

/**
 * 预报分析结果
 */
interface ForecastAnalysis {
  patterns: {
    isStableWeather: boolean
    weatherCycleLength: number
    dominantWeather: WeatherType
    volatilityScore: number // 0-10，天气变化频繁程度
  }
  focusOptimization: {
    bestTimes: Array<{
      time: Date
      weatherType: WeatherType
      focusScore: number
      duration: number // 分钟
    }>
    worstTimes: Array<{
      time: Date
      weatherType: WeatherType
      focusScore: number
      reason: string
    }>
  }
  alerts: WeatherAlert[]
}

/**
 * 通知配置
 */
interface NotificationConfig {
  enableWeatherChangeNotifications: boolean
  enableFocusOptimizationNotifications: boolean
  enableSevereWeatherAlerts: boolean
  notificationLeadTime: number // 提前通知时间（分钟）
  quietHours: {
    start: string // 'HH:MM' 格式
    end: string
  }
  preferredNotificationTypes: ('popup' | 'sound' | 'visual')[]
}

/**
 * 天气预报服务
 * 提供增强的天气预报、警报和通知功能
 */
export class WeatherForecastService {
  private static instance: WeatherForecastService
  private weatherManager: WeatherManager
  private extendedForecasts: ExtendedWeatherForecast[] = []
  private activeAlerts: Map<string, WeatherAlert> = new Map()
  private notificationConfig: NotificationConfig
  private analysisCache: Map<string, ForecastAnalysis> = new Map()
  private notificationTimers: Map<string, number> = new Map()
  private eventEmitter: Phaser.Events.EventEmitter

  private constructor(weatherManager: WeatherManager) {
    this.weatherManager = weatherManager
    this.eventEmitter = new Phaser.Events.EventEmitter()
    
    // 默认通知配置
    this.notificationConfig = {
      enableWeatherChangeNotifications: true,
      enableFocusOptimizationNotifications: true,
      enableSevereWeatherAlerts: true,
      notificationLeadTime: 15, // 15分钟提前
      quietHours: {
        start: '23:00',
        end: '07:00'
      },
      preferredNotificationTypes: ['popup', 'visual']
    }

    this.setupEventListeners()
    this.initializeForecastService()
  }

  /**
   * 获取单例实例
   */
  static getInstance(weatherManager?: WeatherManager): WeatherForecastService {
    if (!WeatherForecastService.instance) {
      if (!weatherManager) {
        throw new Error('WeatherManager is required for first initialization')
      }
      WeatherForecastService.instance = new WeatherForecastService(weatherManager)
    }
    return WeatherForecastService.instance
  }

  /**
   * 初始化预报服务
   */
  private initializeForecastService(): void {
    this.generateExtendedForecast()
    this.startPeriodicUpdates()
    console.log('天气预报服务已初始化')
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // WeatherManager继承自EventEmitter，应该有on方法
    // 如果没有，我们可以直接监听相关事件或使用其他方式
    try {
      if (typeof (this.weatherManager as any).on === 'function') {
        (this.weatherManager as any).on('forecastUpdated', this.onForecastUpdated.bind(this))
        ;(this.weatherManager as any).on('weatherChanged', this.onWeatherChanged.bind(this))
      }
    } catch (error) {
      console.warn('无法设置WeatherManager事件监听器:', error)
    }
  }

  /**
   * 天气预报更新事件处理器
   */
  private onForecastUpdated(forecast: WeatherForecast[]): void {
    this.generateExtendedForecast()
    this.analyzeWeatherPatterns()
    this.checkForAlerts()
  }

  /**
   * 天气变化事件处理器
   */
  private onWeatherChanged(event: any): void {
    this.updateActiveAlerts()
    this.generateExtendedForecast()
  }

  /**
   * 生成扩展天气预报
   */
  private generateExtendedForecast(): void {
    const baseForecast = this.weatherManager.getForecast()
    this.extendedForecasts = baseForecast.map((forecast, index) => {
      return {
        ...forecast,
        shortTermTrend: this.calculateShortTermTrend(forecast, baseForecast, index),
        alertLevel: this.calculateAlertLevel(forecast.weatherState.type),
        recommendations: this.generateRecommendations(forecast.weatherState),
        impactOnFocus: this.analyzeFocusImpact(forecast.weatherState)
      }
    })

    this.eventEmitter.emit('extendedForecastUpdated', this.extendedForecasts)
  }

  /**
   * 计算短期趋势
   */
  private calculateShortTermTrend(
    current: WeatherForecast, 
    allForecasts: WeatherForecast[], 
    index: number
  ): 'improving' | 'stable' | 'deteriorating' {
    if (index === 0) return 'stable'

    const previous = allForecasts[index - 1]
    const currentSeverity = this.getWeatherSeverity(current.weatherState.type)
    const previousSeverity = this.getWeatherSeverity(previous.weatherState.type)

    if (currentSeverity < previousSeverity) return 'improving'
    if (currentSeverity > previousSeverity) return 'deteriorating'
    return 'stable'
  }

  /**
   * 获取天气严重程度评分
   */
  private getWeatherSeverity(weatherType: WeatherType): number {
    const severityMap = {
      [WeatherType.SUNNY]: 1,
      [WeatherType.PARTLY_CLOUDY]: 2,
      [WeatherType.CLOUDY]: 3,
      [WeatherType.FOGGY]: 4,
      [WeatherType.WINDY]: 5,
      [WeatherType.RAINY]: 6,
      [WeatherType.HEAVY_RAIN]: 7,
      [WeatherType.THUNDERSTORM]: 8,
      [WeatherType.SNOWY]: 6
    }
    return severityMap[weatherType] || 5
  }

  /**
   * 计算警报级别
   */
  private calculateAlertLevel(weatherType: WeatherType): 'none' | 'low' | 'medium' | 'high' {
    const severity = this.getWeatherSeverity(weatherType)

    if (weatherType === WeatherType.THUNDERSTORM) {
      return severity >= 8 ? 'high' : 'medium'
    }

    if (severity >= 7) return 'medium'
    if (severity >= 5) return 'low'
    return 'none'
  }

  /**
   * 生成建议
   */
  private generateRecommendations(weatherState: WeatherState): string[] {
    const recommendations: string[] = []
    
    switch (weatherState.type) {
      case WeatherType.SUNNY:
        recommendations.push('绝佳的专注时间！利用明亮的天气进行重要任务')
        recommendations.push('可以进行较长时间的专注训练')
        break
      
      case WeatherType.RAINY:
        recommendations.push('雨声有助于专注，适合进行深度工作')
        recommendations.push('避免视觉分散，专注于听觉集中')
        break
      
      case WeatherType.THUNDERSTORM:
        recommendations.push('天气可能影响专注，建议进行短时间任务')
        recommendations.push('使用降噪技术减少干扰')
        break
      
      case WeatherType.CLOUDY:
        recommendations.push('平稳的天气适合常规专注训练')
        recommendations.push('可以尝试中等难度的任务')
        break
      
      case WeatherType.WINDY:
        recommendations.push('风声可能带来干扰，建议关闭窗户')
        recommendations.push('适合进行动态思维训练')
        break
      
      default:
        recommendations.push('根据个人偏好调整专注训练强度')
    }

    return recommendations
  }

  /**
   * 分析专注影响
   */
  private analyzeFocusImpact(weatherState: WeatherState): {
    score: number
    description: string
    suggestions: string[]
  } {
    const weatherEffect = this.weatherManager.getCurrentWeatherEffect()
    const focusMultiplier = weatherEffect.focusMultiplier
    const moodBonus = weatherEffect.moodBonus
    
    // 计算综合专注评分 (-10 到 +10)
    const score = Math.round((focusMultiplier - 1) * 10 + moodBonus * 0.5)
    
    let description = ''
    const suggestions: string[] = []

    if (score >= 5) {
      description = '非常有利于专注的天气条件'
      suggestions.push('安排重要或困难的任务')
      suggestions.push('延长专注训练时间')
      suggestions.push('尝试具有挑战性的目标')
    } else if (score >= 2) {
      description = '有利于专注的天气条件'
      suggestions.push('进行常规专注训练')
      suggestions.push('适合中等难度的任务')
    } else if (score >= -2) {
      description = '中性的天气条件'
      suggestions.push('保持正常的训练强度')
      suggestions.push('注意调节环境因素')
    } else if (score >= -5) {
      description = '可能影响专注的天气条件'
      suggestions.push('减少任务难度')
      suggestions.push('缩短专注训练时间')
      suggestions.push('使用专注辅助工具')
    } else {
      description = '不利于专注的天气条件'
      suggestions.push('考虑推迟重要任务')
      suggestions.push('进行轻松的活动')
      suggestions.push('等待天气改善')
    }

    return { score, description, suggestions }
  }

  /**
   * 分析天气模式
   */
  private analyzeWeatherPatterns(): ForecastAnalysis {
    const cacheKey = `analysis_${Date.now()}`
    
    // 检查缓存
    const cached = this.analysisCache.get(cacheKey)
    if (cached) return cached

    const forecasts = this.extendedForecasts
    const analysis: ForecastAnalysis = {
      patterns: this.analyzePatterns(forecasts),
      focusOptimization: this.optimizeForFocus(forecasts),
      alerts: this.generateAlerts(forecasts)
    }

    // 缓存分析结果
    this.analysisCache.set(cacheKey, analysis)
    
    // 清理旧缓存
    if (this.analysisCache.size > 10) {
      const firstKey = this.analysisCache.keys().next().value
      this.analysisCache.delete(firstKey)
    }

    return analysis
  }

  /**
   * 分析天气模式
   */
  private analyzePatterns(forecasts: ExtendedWeatherForecast[]): ForecastAnalysis['patterns'] {
    const weatherTypes = forecasts.map(f => f.weatherState.type)
    const uniqueWeathers = new Set(weatherTypes)
    
    // 计算稳定性
    const isStableWeather = uniqueWeathers.size <= 2
    
    // 计算周期长度
    const weatherCycleLength = this.calculateCycleLength(weatherTypes)
    
    // 找出主导天气
    const weatherCounts = weatherTypes.reduce((acc, weather) => {
      acc[weather] = (acc[weather] || 0) + 1
      return acc
    }, {} as Record<WeatherType, number>)
    
    const sortedWeathers = Object.entries(weatherCounts)
      .sort(([,a], [,b]) => b - a)
    const dominantWeather = (sortedWeathers.length > 0 ? sortedWeathers[0][0] : WeatherType.SUNNY) as WeatherType
    
    // 计算波动性评分
    const volatilityScore = this.calculateVolatility(forecasts)

    return {
      isStableWeather,
      weatherCycleLength,
      dominantWeather,
      volatilityScore
    }
  }

  /**
   * 计算天气周期长度
   */
  private calculateCycleLength(weatherTypes: WeatherType[]): number {
    let cycleLength = 1
    const maxCycle = Math.floor(weatherTypes.length / 2)
    
    for (let len = 2; len <= maxCycle; len++) {
      let isRepeating = true
      for (let i = 0; i < weatherTypes.length - len; i++) {
        if (weatherTypes[i] !== weatherTypes[i + len]) {
          isRepeating = false
          break
        }
      }
      if (isRepeating) {
        cycleLength = len
        break
      }
    }
    
    return cycleLength
  }

  /**
   * 计算天气波动性
   */
  private calculateVolatility(forecasts: ExtendedWeatherForecast[]): number {
    let changeCount = 0
    let severityVariation = 0
    
    for (let i = 1; i < forecasts.length; i++) {
      const current = forecasts[i].weatherState.type
      const previous = forecasts[i - 1].weatherState.type
      
      if (current !== previous) {
        changeCount++
        
        const currentSeverity = this.getWeatherSeverity(current)
        const previousSeverity = this.getWeatherSeverity(previous)
        severityVariation += Math.abs(currentSeverity - previousSeverity)
      }
    }
    
    const changeRate = changeCount / (forecasts.length - 1)
    const avgSeverityChange = severityVariation / Math.max(changeCount, 1)
    
    return Math.min(10, Math.round((changeRate * 5) + (avgSeverityChange * 0.5)))
  }

  /**
   * 专注优化分析
   */
  private optimizeForFocus(forecasts: ExtendedWeatherForecast[]): ForecastAnalysis['focusOptimization'] {
    const bestTimes: any[] = []
    const worstTimes: any[] = []
    
    forecasts.forEach((forecast, index) => {
      const focusScore = forecast.impactOnFocus.score
      const duration = 120 // 假设每个预报时段2小时
      
      if (focusScore >= 3) {
        bestTimes.push({
          time: forecast.time,
          weatherType: forecast.weatherState.type,
          focusScore,
          duration
        })
      } else if (focusScore <= -3) {
        worstTimes.push({
          time: forecast.time,
          weatherType: forecast.weatherState.type,
          focusScore,
          reason: forecast.impactOnFocus.description
        })
      }
    })
    
    // 按照专注评分排序
    bestTimes.sort((a, b) => b.focusScore - a.focusScore)
    worstTimes.sort((a, b) => a.focusScore - b.focusScore)
    
    return {
      bestTimes: bestTimes.slice(0, 5), // 取前5个最佳时间
      worstTimes: worstTimes.slice(0, 3)  // 取前3个最差时间
    }
  }

  /**
   * 生成警报
   */
  private generateAlerts(forecasts: ExtendedWeatherForecast[]): WeatherAlert[] {
    const alerts: WeatherAlert[] = []
    
    forecasts.forEach((forecast, index) => {
      // 严重天气警报
      if (forecast.alertLevel === 'high') {
        alerts.push({
          id: `severe_${forecast.time.getTime()}`,
          type: 'severe_weather',
          severity: 'alert',
          title: '严重天气警报',
          description: `预计 ${forecast.time.toLocaleTimeString()} 将出现${this.getWeatherDisplayName(forecast.weatherState.type)}`,
          weatherType: forecast.weatherState.type,
          startTime: forecast.time,
          isActive: true,
          isDismissed: false,
          recommendations: forecast.recommendations
        })
      }
      
      // 专注影响警报
      if (forecast.impactOnFocus.score <= -5) {
        alerts.push({
          id: `focus_${forecast.time.getTime()}`,
          type: 'focus_impact',
          severity: 'warning',
          title: '专注条件警告',
          description: `${forecast.time.toLocaleTimeString()} 的天气可能严重影响专注力`,
          weatherType: forecast.weatherState.type,
          startTime: forecast.time,
          isActive: true,
          isDismissed: false,
          recommendations: forecast.impactOnFocus.suggestions
        })
      }
      
      // 最佳专注时机提醒
      if (forecast.impactOnFocus.score >= 5) {
        alerts.push({
          id: `optimal_${forecast.time.getTime()}`,
          type: 'optimal_conditions',
          severity: 'info',
          title: '最佳专注时机',
          description: `${forecast.time.toLocaleTimeString()} 将是专注训练的绝佳时机！`,
          weatherType: forecast.weatherState.type,
          startTime: forecast.time,
          isActive: true,
          isDismissed: false,
          recommendations: forecast.impactOnFocus.suggestions
        })
      }
    })
    
    return alerts
  }

  /**
   * 获取天气显示名称
   */
  private getWeatherDisplayName(weatherType: WeatherType): string {
    const names = {
      [WeatherType.SUNNY]: '晴天',
      [WeatherType.PARTLY_CLOUDY]: '多云',
      [WeatherType.CLOUDY]: '阴天',
      [WeatherType.RAINY]: '雨天',
      [WeatherType.HEAVY_RAIN]: '大雨',
      [WeatherType.THUNDERSTORM]: '雷暴',
      [WeatherType.SNOWY]: '雪天',
      [WeatherType.FOGGY]: '雾天',
      [WeatherType.WINDY]: '大风'
    }
    return names[weatherType] || weatherType
  }

  /**
   * 检查警报
   */
  private checkForAlerts(): void {
    const analysis = this.analyzeWeatherPatterns()
    
    // 处理新警报
    analysis.alerts.forEach(alert => {
      if (!this.activeAlerts.has(alert.id)) {
        this.activeAlerts.set(alert.id, alert)
        this.scheduleNotification(alert)
      }
    })
    
    this.eventEmitter.emit('alertsUpdated', Array.from(this.activeAlerts.values()))
  }

  /**
   * 安排通知
   */
  private scheduleNotification(alert: WeatherAlert): void {
    if (!this.shouldSendNotification(alert)) return
    
    const notificationTime = new Date(alert.startTime.getTime() - this.notificationConfig.notificationLeadTime * 60 * 1000)
    const delay = notificationTime.getTime() - Date.now()
    
    if (delay > 0) {
      const timer = setTimeout(() => {
        this.sendNotification(alert)
        this.notificationTimers.delete(alert.id)
      }, delay)
      
      this.notificationTimers.set(alert.id, timer)
    } else {
      // 立即发送通知
      this.sendNotification(alert)
    }
  }

  /**
   * 判断是否应该发送通知
   */
  private shouldSendNotification(alert: WeatherAlert): boolean {
    // 检查通知类型设置
    if (alert.type === 'severe_weather' && !this.notificationConfig.enableSevereWeatherAlerts) {
      return false
    }
    
    if (alert.type === 'focus_impact' && !this.notificationConfig.enableFocusOptimizationNotifications) {
      return false
    }
    
    // 检查静音时间
    if (this.isInQuietHours()) {
      return alert.severity === 'emergency'
    }
    
    return true
  }

  /**
   * 检查是否在静音时间
   */
  private isInQuietHours(): boolean {
    const now = new Date()
    const currentTime = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`
    
    const { start, end } = this.notificationConfig.quietHours
    
    if (start <= end) {
      return currentTime >= start && currentTime <= end
    } else {
      // 跨午夜的情况
      return currentTime >= start || currentTime <= end
    }
  }

  /**
   * 发送通知
   */
  private sendNotification(alert: WeatherAlert): void {
    console.log(`天气通知: ${alert.title} - ${alert.description}`)
    
    // 触发通知事件
    this.eventEmitter.emit('weatherNotification', {
      alert,
      timestamp: new Date(),
      notificationTypes: this.notificationConfig.preferredNotificationTypes
    })
  }

  /**
   * 更新活跃警报
   */
  private updateActiveAlerts(): void {
    const now = Date.now()
    
    for (const [id, alert] of this.activeAlerts.entries()) {
      // 移除过期警报
      if (alert.endTime && now > alert.endTime.getTime()) {
        this.activeAlerts.delete(id)
        
        // 清理相关的通知定时器
        const timer = this.notificationTimers.get(id)
        if (timer) {
          clearTimeout(timer)
          this.notificationTimers.delete(id)
        }
      }
    }
  }

  /**
   * 开始周期性更新
   */
  private startPeriodicUpdates(): void {
    // 每10分钟更新一次预报
    setInterval(() => {
      this.generateExtendedForecast()
      this.analyzeWeatherPatterns()
      this.checkForAlerts()
      this.updateActiveAlerts()
    }, 10 * 60 * 1000)
  }

  /**
   * 获取扩展预报
   */
  getExtendedForecast(): ExtendedWeatherForecast[] {
    return [...this.extendedForecasts]
  }

  /**
   * 获取天气分析
   */
  getWeatherAnalysis(): ForecastAnalysis {
    return this.analyzeWeatherPatterns()
  }

  /**
   * 获取活跃警报
   */
  getActiveAlerts(): WeatherAlert[] {
    return Array.from(this.activeAlerts.values()).filter(alert => alert.isActive && !alert.isDismissed)
  }

  /**
   * 忽略警报
   */
  dismissAlert(alertId: string): void {
    const alert = this.activeAlerts.get(alertId)
    if (alert) {
      alert.isDismissed = true
      this.eventEmitter.emit('alertDismissed', alert)
    }
  }

  /**
   * 更新通知配置
   */
  updateNotificationConfig(config: Partial<NotificationConfig>): void {
    this.notificationConfig = { ...this.notificationConfig, ...config }
    console.log('通知配置已更新:', this.notificationConfig)
  }

  /**
   * 手动触发预报更新
   */
  refreshForecast(): void {
    this.generateExtendedForecast()
    this.analyzeWeatherPatterns()
    this.checkForAlerts()
  }

  /**
   * 事件监听器方法
   */
  on(event: string, listener: (...args: any[]) => void): void {
    this.eventEmitter.on(event, listener)
  }

  off(event: string, listener: (...args: any[]) => void): void {
    this.eventEmitter.off(event, listener)
  }

  /**
   * 销毁服务
   */
  destroy(): void {
    // 清理所有定时器
    for (const timer of this.notificationTimers.values()) {
      clearTimeout(timer)
    }
    this.notificationTimers.clear()
    
    // 清理缓存
    this.analysisCache.clear()
    this.activeAlerts.clear()
    
    // 移除事件监听器
    this.eventEmitter.removeAllListeners()
    
    console.log('天气预报服务已销毁')
  }
}

export default WeatherForecastService 