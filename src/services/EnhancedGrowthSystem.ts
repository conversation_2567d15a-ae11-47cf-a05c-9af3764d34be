import { 
  CropInstance, 
  CropConfig, 
  CropStage, 
  CropType,
  CROP_CONFIGS 
} from '../types/crop'
import { 
  SelfDisciplineType,
  BehaviorDetectionCriteria,
  getCropDetectionConfig,
  CROP_DETECTION_CONFIGS 
} from '../data/cropSpecifications'
import { GrowthCalculator } from '../utils/cropGrowth'

// 行为检测结果接口
export interface BehaviorDetectionResult {
  isDetected: boolean
  confidence: number // 0-1, 检测置信度
  qualityScore: number // 0-100, 行为质量分数
  behaviorType: SelfDisciplineType
  sessionDuration: number
  metadata: {
    consistency: number
    intensity: number
    timeOfDayBonus: number
    streakCount: number
  }
}

// 会话数据接口
export interface BehaviorSession {
  id: string
  cropId: string
  behaviorType: SelfDisciplineType
  startTime: number
  endTime: number
  focusScores: number[]
  averageFocusScore: number
  qualityMetrics: {
    consistency: number
    intensity: number
    duration: number
  }
  isValid: boolean
  bonusFactors: {
    timeOfDay: number
    streak: number
    weather?: number
  }
}

// 增强生长计算器
export class EnhancedGrowthCalculator extends GrowthCalculator {
  private behaviorSessions: Map<string, BehaviorSession[]> = new Map()
  private streakCounters: Map<string, number> = new Map()
  private lastSessionTimes: Map<string, number> = new Map()
  
  /**
   * 基于行为检测的专注度计算
   */
  calculateBehaviorBasedFocus(
    crop: CropInstance,
    currentBehavior?: BehaviorDetectionResult
  ): number {
    const config = getCropDetectionConfig(crop.type)
    const criteria = config.detectionCriteria
    
    if (!currentBehavior || !currentBehavior.isDetected) {
      // 没有检测到对应行为，返回基础分数
      return Math.max(criteria.focusScoreThreshold * 0.7, 30)
    }
    
    // 基于行为检测结果计算专注度
    const baseScore = currentBehavior.qualityScore
    const confidenceBonus = currentBehavior.confidence * 10
    const behaviorAlignment = this.calculateBehaviorAlignment(crop.type, currentBehavior.behaviorType)
    
    // 应用质量权重
    const metrics = criteria.sessionQualityMetrics
    const weightedScore = 
      baseScore * metrics.consistencyWeight * currentBehavior.metadata.consistency +
      baseScore * metrics.intensityWeight * currentBehavior.metadata.intensity +
      baseScore * metrics.durationWeight * (currentBehavior.sessionDuration / criteria.minimumDuration)
    
    // 应用奖励加成
    const timeBonus = currentBehavior.metadata.timeOfDayBonus * (config.growthModifiers.timeOfDayBonus || 0) * 100
    const streakBonus = currentBehavior.metadata.streakCount * config.growthModifiers.streakBonus * 10
    
    const finalScore = Math.min(100, weightedScore + confidenceBonus + timeBonus + streakBonus) * behaviorAlignment
    
    return Math.max(finalScore, 20) // 最低保底分数
  }
  
  /**
   * 计算行为对齐度
   */
  private calculateBehaviorAlignment(cropType: CropType, detectedBehavior: SelfDisciplineType): number {
    const expectedBehavior = getCropDetectionConfig(cropType).behaviorType
    
    if (detectedBehavior === expectedBehavior) {
      return 1.0 // 完全对齐
    }
    
    // 计算行为相关性
    const relationshipMap: Record<SelfDisciplineType, Partial<Record<SelfDisciplineType, number>>> = {
      [SelfDisciplineType.LEARNING]: {
        [SelfDisciplineType.READING]: 0.8,
        [SelfDisciplineType.DEEP_FOCUS]: 0.7,
        [SelfDisciplineType.TIME_MANAGEMENT]: 0.6
      },
      [SelfDisciplineType.DEEP_FOCUS]: {
        [SelfDisciplineType.LEARNING]: 0.7,
        [SelfDisciplineType.MEDITATION]: 0.6,
        [SelfDisciplineType.TIME_MANAGEMENT]: 0.5
      },
      [SelfDisciplineType.EXERCISE]: {
        [SelfDisciplineType.TIME_MANAGEMENT]: 0.4,
        [SelfDisciplineType.SOCIAL_INTERACTION]: 0.3
      },
      [SelfDisciplineType.MEDITATION]: {
        [SelfDisciplineType.DEEP_FOCUS]: 0.6,
        [SelfDisciplineType.LEARNING]: 0.4
      },
      [SelfDisciplineType.READING]: {
        [SelfDisciplineType.LEARNING]: 0.8,
        [SelfDisciplineType.DEEP_FOCUS]: 0.5
      },
      [SelfDisciplineType.TIME_MANAGEMENT]: {
        [SelfDisciplineType.LEARNING]: 0.6,
        [SelfDisciplineType.DEEP_FOCUS]: 0.5,
        [SelfDisciplineType.EXERCISE]: 0.4
      },
      [SelfDisciplineType.SOCIAL_INTERACTION]: {
        [SelfDisciplineType.EXERCISE]: 0.3
      }
    }
    
    return relationshipMap[expectedBehavior]?.[detectedBehavior] || 0.2 // 默认低对齐度
  }
  
  /**
   * 记录行为会话
   */
  recordBehaviorSession(session: BehaviorSession): void {
    if (!this.behaviorSessions.has(session.cropId)) {
      this.behaviorSessions.set(session.cropId, [])
    }
    
    const sessions = this.behaviorSessions.get(session.cropId)!
    sessions.push(session)
    
    // 保持最近50个会话
    if (sessions.length > 50) {
      sessions.shift()
    }
    
    // 更新连击计数
    this.updateStreakCounter(session)
    
    console.log(`📝 记录行为会话: 作物${session.cropId}, 类型${session.behaviorType}, 持续${session.endTime - session.startTime}ms`)
  }
  
  /**
   * 更新连击计数器
   */
  private updateStreakCounter(session: BehaviorSession): void {
    const cropId = session.cropId
    const lastTime = this.lastSessionTimes.get(cropId) || 0
    const currentStreak = this.streakCounters.get(cropId) || 0
    
    // 检查是否在连击时间窗口内
    const timeDiff = session.startTime - lastTime
    const config = getCropDetectionConfig(session.behaviorType as any)
    const maxGap = config.detectionCriteria.specificRequirements?.minBreaksBetweenSessions || 30 * 60 * 1000
    
    if (session.isValid && timeDiff <= maxGap * 3) { // 允许3倍的最大间隔
      this.streakCounters.set(cropId, currentStreak + 1)
    } else {
      this.streakCounters.set(cropId, session.isValid ? 1 : 0)
    }
    
    this.lastSessionTimes.set(cropId, session.endTime)
  }
  
  /**
   * 验证会话有效性
   */
  validateSession(
    cropType: CropType,
    behaviorType: SelfDisciplineType,
    sessionData: {
      duration: number
      averageFocusScore: number
      consistency: number
      intensity: number
    }
  ): boolean {
    const config = getCropDetectionConfig(cropType)
    const criteria = config.detectionCriteria
    
    // 检查行为类型匹配
    if (behaviorType !== criteria.type) {
      return false
    }
    
    // 检查最小持续时间
    if (sessionData.duration < criteria.minimumDuration) {
      return false
    }
    
    // 检查专注度阈值
    if (sessionData.averageFocusScore < criteria.focusScoreThreshold) {
      return false
    }
    
    // 检查质量指标
    const qualityScore = 
      sessionData.consistency * criteria.sessionQualityMetrics.consistencyWeight +
      sessionData.intensity * criteria.sessionQualityMetrics.intensityWeight +
      (sessionData.duration / criteria.minimumDuration) * criteria.sessionQualityMetrics.durationWeight
    
    return qualityScore >= 0.6 // 60%质量阈值
  }
  
  /**
   * 获取作物的行为统计
   */
  getBehaviorStats(cropId: string): {
    totalSessions: number
    validSessions: number
    averageQuality: number
    bestStreak: number
    currentStreak: number
    recentSessions: BehaviorSession[]
    behaviorTypes: Record<SelfDisciplineType, number>
  } {
    const sessions = this.behaviorSessions.get(cropId) || []
    const validSessions = sessions.filter(s => s.isValid)
    
    // 计算平均质量
    const averageQuality = validSessions.length > 0 
      ? validSessions.reduce((sum, s) => sum + s.averageFocusScore, 0) / validSessions.length 
      : 0
    
    // 计算最佳连击
    let bestStreak = 0
    let tempStreak = 0
    for (const session of sessions) {
      if (session.isValid) {
        tempStreak++
        bestStreak = Math.max(bestStreak, tempStreak)
      } else {
        tempStreak = 0
      }
    }
    
    // 统计行为类型
    const behaviorTypes: Record<SelfDisciplineType, number> = {} as any
    Object.values(SelfDisciplineType).forEach(type => {
      behaviorTypes[type] = 0
    })
    
    sessions.forEach(session => {
      behaviorTypes[session.behaviorType]++
    })
    
    return {
      totalSessions: sessions.length,
      validSessions: validSessions.length,
      averageQuality,
      bestStreak,
      currentStreak: this.streakCounters.get(cropId) || 0,
      recentSessions: sessions.slice(-10), // 最近10个会话
      behaviorTypes
    }
  }
  
  /**
   * 计算当前生长速度加成
   */
  calculateCurrentGrowthBonus(cropId: string, crop: CropInstance): number {
    const config = getCropDetectionConfig(crop.type)
    const stats = this.getBehaviorStats(cropId)
    
    // 基础倍数
    let bonus = config.growthModifiers.baseMultiplier
    
    // 连击加成
    const streakBonus = Math.min(stats.currentStreak * config.growthModifiers.streakBonus, 1.0)
    bonus += streakBonus
    
    // 质量加成
    if (stats.averageQuality > 0) {
      const qualityBonus = (stats.averageQuality / 100) * config.growthModifiers.qualityBonus
      bonus += qualityBonus
    }
    
    // 一致性加成（基于最近会话的行为类型一致性）
    const recentBehaviorConsistency = this.calculateBehaviorConsistency(stats.recentSessions, config.behaviorType)
    bonus += recentBehaviorConsistency * 0.3
    
    return Math.max(bonus, 0.1) // 最低10%增长速度
  }
  
  /**
   * 计算行为一致性
   */
  private calculateBehaviorConsistency(sessions: BehaviorSession[], expectedType: SelfDisciplineType): number {
    if (sessions.length === 0) return 0
    
    const matchingBehaviors = sessions.filter(s => s.behaviorType === expectedType).length
    return matchingBehaviors / sessions.length
  }
  
  /**
   * 清理过期数据
   */
  cleanupExpiredData(): void {
    const now = Date.now()
    const maxAge = 7 * 24 * 60 * 60 * 1000 // 7天
    
    this.behaviorSessions.forEach((sessions, cropId) => {
      const validSessions = sessions.filter(s => now - s.endTime < maxAge)
      this.behaviorSessions.set(cropId, validSessions)
    })
    
    console.log('🧹 清理过期行为数据完成')
  }
  
  /**
   * 导出作物行为数据
   */
  exportCropBehaviorData(cropId: string): any {
    return {
      sessions: this.behaviorSessions.get(cropId) || [],
      streak: this.streakCounters.get(cropId) || 0,
      lastSessionTime: this.lastSessionTimes.get(cropId) || 0,
      stats: this.getBehaviorStats(cropId)
    }
  }
  
  /**
   * 导入作物行为数据
   */
  importCropBehaviorData(cropId: string, data: any): void {
    if (data.sessions) {
      this.behaviorSessions.set(cropId, data.sessions)
    }
    if (data.streak !== undefined) {
      this.streakCounters.set(cropId, data.streak)
    }
    if (data.lastSessionTime) {
      this.lastSessionTimes.set(cropId, data.lastSessionTime)
    }
    
    console.log(`📥 导入作物${cropId}的行为数据完成`)
  }
}

// 行为检测器接口
export interface BehaviorDetector {
  detectBehavior(
    behaviorType: SelfDisciplineType,
    focusData: {
      score: number
      duration: number
      timestamp: number
    }[]
  ): BehaviorDetectionResult
}

// 模拟行为检测器（实际项目中需要接入真实的行为检测API）
export class MockBehaviorDetector implements BehaviorDetector {
  detectBehavior(
    behaviorType: SelfDisciplineType,
    focusData: { score: number; duration: number; timestamp: number }[]
  ): BehaviorDetectionResult {
    if (focusData.length === 0) {
      return {
        isDetected: false,
        confidence: 0,
        qualityScore: 0,
        behaviorType,
        sessionDuration: 0,
        metadata: {
          consistency: 0,
          intensity: 0,
          timeOfDayBonus: 0,
          streakCount: 0
        }
      }
    }
    
    // 模拟检测逻辑
    const totalDuration = focusData.reduce((sum, data) => sum + data.duration, 0)
    const averageScore = focusData.reduce((sum, data) => sum + data.score, 0) / focusData.length
    const consistency = this.calculateConsistency(focusData)
    const intensity = averageScore / 100
    
    // 简单的检测逻辑
    const isDetected = averageScore > 50 && totalDuration > 10000 // 10秒以上且平均分数大于50
    const confidence = Math.min(averageScore / 100, 1.0)
    
    return {
      isDetected,
      confidence,
      qualityScore: averageScore,
      behaviorType,
      sessionDuration: totalDuration,
      metadata: {
        consistency,
        intensity,
        timeOfDayBonus: this.calculateTimeOfDayBonus(),
        streakCount: 0 // 会在外部计算
      }
    }
  }
  
  private calculateConsistency(focusData: { score: number; duration: number; timestamp: number }[]): number {
    if (focusData.length < 2) return 1.0
    
    const scores = focusData.map(d => d.score)
    const average = scores.reduce((sum, score) => sum + score, 0) / scores.length
    const variance = scores.reduce((sum, score) => sum + Math.pow(score - average, 2), 0) / scores.length
    const standardDeviation = Math.sqrt(variance)
    
    // 一致性 = 1 - 标准差/100（归一化）
    return Math.max(0, 1 - standardDeviation / 100)
  }
  
  private calculateTimeOfDayBonus(): number {
    const hour = new Date().getHours()
    
    // 早晨6-10点和晚上7-10点有奖励
    if ((hour >= 6 && hour <= 10) || (hour >= 19 && hour <= 22)) {
      return 0.2
    }
    
    return 0
  }
}

// 导出单例实例
export const enhancedGrowthCalculator = new EnhancedGrowthCalculator()
export const mockBehaviorDetector = new MockBehaviorDetector() 