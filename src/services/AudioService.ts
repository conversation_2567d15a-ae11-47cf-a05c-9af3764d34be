import { AudioSettings, AudioQuality } from '../types/settings.types'

/**
 * 音频设备信息
 */
export interface AudioDeviceInfo extends MediaDeviceInfo {
  capabilities?: {
    channelCount?: { min: number; max: number }
    sampleRate?: { min: number; max: number }
    echoCancellation?: boolean
    noiseSuppression?: boolean
    autoGainControl?: boolean
  }
}

/**
 * 音效资源
 */
export interface SoundEffect {
  id: string
  name: string
  category: 'ui' | 'notification' | 'achievement' | 'background' | 'ambient'
  url: string
  volume?: number
  loop?: boolean
  preload?: boolean
  description?: string
}

/**
 * 音频分析器数据
 */
export interface AudioAnalyzerData {
  volume: number
  frequency: number
  waveform: number[]
  spectrum: number[]
  timestamp: number
}

/**
 * 音效播放选项
 */
export interface PlayOptions {
  volume?: number
  loop?: boolean
  fadeIn?: number
  fadeOut?: number
  delay?: number
  onEnd?: () => void
  onError?: (error: Error) => void
}

/**
 * 音频服务类
 */
export class AudioService {
  private audioContext: AudioContext | null = null
  private masterGainNode: GainNode | null = null
  private compressorNode: DynamicsCompressorNode | null = null
  private analyzerNode: AnalyserNode | null = null
  
  private soundEffects: Map<string, SoundEffect> = new Map()
  private audioElements: Map<string, HTMLAudioElement> = new Map()
  private soundInstances: Map<string, AudioBufferSourceNode[]> = new Map()
  
  private inputDevices: AudioDeviceInfo[] = []
  private outputDevices: AudioDeviceInfo[] = []
  private currentMicStream: MediaStream | null = null
  
  private settings: AudioSettings = {
    enabled: true,
    volume: {
      master: 80,
      effects: 70,
      background: 50,
      voice: 80,
      notifications: 75,
      muted: false
    },
    effects: {
      enabled: true,
      reverb: 20,
      bass: 50,
      treble: 50,
      spatialAudio: false,
      noiseReduction: true
    },
    notifications: {
      playOnFocusLoss: true,
      playOnAchievement: true,
      playOnReminder: true,
      playOnError: true,
      customSounds: {}
    },
    devices: {
      outputDeviceId: '',
      inputDeviceId: '',
      outputDevices: [],
      inputDevices: []
    },
    quality: AudioQuality.MEDIUM
  }
  
  private listeners: Map<string, Array<(data: any) => void>> = new Map()
  private isInitialized = false

  /**
   * 初始化音频服务
   */
  async initialize(): Promise<void> {
    try {
      // 初始化Web Audio API
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)()
      
      // 创建音频节点图
      await this.createAudioGraph()
      
      // 检测音频设备
      await this.detectAudioDevices()
      
      // 加载默认音效
      await this.loadDefaultSounds()
      
      this.isInitialized = true
      this.emit('initialized', { 
        devices: { input: this.inputDevices, output: this.outputDevices },
        soundsLoaded: this.soundEffects.size
      })
      
    } catch (error) {
      console.error('音频服务初始化失败:', error)
      throw error
    }
  }

  /**
   * 创建音频处理图
   */
  private async createAudioGraph(): Promise<void> {
    if (!this.audioContext) return

    // 创建主增益节点
    this.masterGainNode = this.audioContext.createGain()
    this.masterGainNode.gain.value = this.settings.volume.master / 100

    // 创建压缩器节点
    this.compressorNode = this.audioContext.createDynamicsCompressor()
    this.compressorNode.threshold.value = -24
    this.compressorNode.knee.value = 30
    this.compressorNode.ratio.value = 12
    this.compressorNode.attack.value = 0.003
    this.compressorNode.release.value = 0.25

    // 创建分析器节点
    this.analyzerNode = this.audioContext.createAnalyser()
    this.analyzerNode.fftSize = 2048
    this.analyzerNode.smoothingTimeConstant = 0.8

    // 连接音频图
    this.masterGainNode
      .connect(this.compressorNode)
      .connect(this.analyzerNode)
      .connect(this.audioContext.destination)
  }

  /**
   * 检测音频设备
   */
  async detectAudioDevices(): Promise<void> {
    try {
      const devices = await navigator.mediaDevices.enumerateDevices()
      
      this.inputDevices = devices
        .filter(device => device.kind === 'audioinput')
        .map(device => ({
          ...device,
          capabilities: {
            channelCount: { min: 1, max: 2 },
            sampleRate: { min: 8000, max: 96000 },
            echoCancellation: true,
            noiseSuppression: true,
            autoGainControl: true
          }
        }))

      this.outputDevices = devices
        .filter(device => device.kind === 'audiooutput')
        .map(device => ({
          ...device,
          capabilities: {
            channelCount: { min: 1, max: 8 },
            sampleRate: { min: 8000, max: 192000 }
          }
        }))

      this.emit('devicesDetected', {
        input: this.inputDevices,
        output: this.outputDevices
      })

    } catch (error) {
      console.error('音频设备检测失败:', error)
      throw error
    }
  }

  /**
   * 加载默认音效
   */
  private async loadDefaultSounds(): Promise<void> {
    const defaultSounds: SoundEffect[] = [
      {
        id: 'click',
        name: '点击音效',
        category: 'ui',
        url: '/sounds/click.mp3',
        volume: 0.7,
        description: '按钮点击和UI交互音效'
      },
      {
        id: 'focus',
        name: '专注提示',
        category: 'notification',
        url: '/sounds/focus.mp3',
        volume: 0.8,
        description: '开始专注时的提示音'
      },
      {
        id: 'break',
        name: '休息提示',
        category: 'notification',
        url: '/sounds/break.mp3',
        volume: 0.8,
        description: '休息时间到达提示音'
      },
      {
        id: 'achievement',
        name: '成就解锁',
        category: 'achievement',
        url: '/sounds/achievement.mp3',
        volume: 0.9,
        description: '获得成就时的音效'
      },
      {
        id: 'levelup',
        name: '升级音效',
        category: 'achievement',
        url: '/sounds/levelup.mp3',
        volume: 0.9,
        description: '经验升级时的音效'
      },
      {
        id: 'nature',
        name: '自然环境音',
        category: 'ambient',
        url: '/sounds/nature.mp3',
        volume: 0.4,
        loop: true,
        description: '鸟鸣、流水等自然环境音'
      },
      {
        id: 'rain',
        name: '雨声',
        category: 'ambient',
        url: '/sounds/rain.mp3',
        volume: 0.3,
        loop: true,
        description: '轻柔的雨声背景音'
      },
      {
        id: 'background',
        name: '背景音乐',
        category: 'background',
        url: '/sounds/background.mp3',
        volume: 0.3,
        loop: true,
        description: '轻柔的背景音乐'
      }
    ]

    for (const sound of defaultSounds) {
      await this.loadSound(sound)
    }
  }

  /**
   * 加载音效
   */
  async loadSound(sound: SoundEffect): Promise<void> {
    try {
      this.soundEffects.set(sound.id, sound)
      
      // 预加载音频
      if (sound.preload !== false) {
        const audio = new Audio(sound.url)
        audio.preload = 'auto'
        audio.volume = (sound.volume || 1) * (this.settings.volume.effects / 100)
        audio.loop = sound.loop || false
        
        this.audioElements.set(sound.id, audio)
        
        // 等待加载完成
        await new Promise<void>((resolve, reject) => {
          audio.addEventListener('loadeddata', () => resolve())
          audio.addEventListener('error', (e) => reject(new Error(`音效加载失败: ${sound.url}`)))
        })
      }
      
    } catch (error) {
      console.warn(`音效 ${sound.id} 加载失败:`, error)
    }
  }

  /**
   * 播放音效
   */
  async playSound(soundId: string, options: PlayOptions = {}): Promise<void> {
    if (!this.settings.enabled || this.settings.volume.muted) return

    const sound = this.soundEffects.get(soundId)
    if (!sound) {
      console.warn(`音效不存在: ${soundId}`)
      return
    }

    try {
      const audio = this.audioElements.get(soundId)
      if (audio) {
        // 使用HTML Audio元素播放
        await this.playWithAudioElement(audio, sound, options)
      } else {
        // 使用Web Audio API播放
        await this.playWithWebAudio(sound, options)
      }
      
      this.emit('soundPlayed', { soundId, options })
      
    } catch (error) {
      console.error(`音效播放失败 ${soundId}:`, error)
      options.onError?.(error as Error)
    }
  }

  /**
   * 使用HTML Audio元素播放
   */
  private async playWithAudioElement(
    audio: HTMLAudioElement, 
    sound: SoundEffect, 
    options: PlayOptions
  ): Promise<void> {
    audio.currentTime = 0
    
    // 设置音量
    const volume = (options.volume ?? sound.volume ?? 1) * 
                  (this.getCategoryVolume(sound.category) / 100) *
                  (this.settings.volume.master / 100)
    audio.volume = Math.max(0, Math.min(1, volume))
    
    // 设置循环
    audio.loop = options.loop ?? sound.loop ?? false
    
    // 延迟播放
    if (options.delay) {
      await new Promise(resolve => setTimeout(resolve, options.delay))
    }
    
    // 淡入效果
    if (options.fadeIn) {
      audio.volume = 0
      await audio.play()
      await this.fadeVolume(audio, volume, options.fadeIn)
    } else {
      await audio.play()
    }
    
    // 监听播放结束
    const onEnded = () => {
      audio.removeEventListener('ended', onEnded)
      options.onEnd?.()
    }
    audio.addEventListener('ended', onEnded)
  }

  /**
   * 使用Web Audio API播放
   */
  private async playWithWebAudio(sound: SoundEffect, options: PlayOptions): Promise<void> {
    if (!this.audioContext || !this.masterGainNode) return

    // 加载音频数据
    const response = await fetch(sound.url)
    const arrayBuffer = await response.arrayBuffer()
    const audioBuffer = await this.audioContext.decodeAudioData(arrayBuffer)

    // 创建音频源
    const source = this.audioContext.createBufferSource()
    const gainNode = this.audioContext.createGain()
    
    source.buffer = audioBuffer
    source.loop = options.loop ?? sound.loop ?? false
    
    // 设置音量
    const volume = (options.volume ?? sound.volume ?? 1) * 
                  (this.getCategoryVolume(sound.category) / 100)
    gainNode.gain.value = volume
    
    // 连接音频图
    source.connect(gainNode).connect(this.masterGainNode)
    
    // 存储实例
    if (!this.soundInstances.has(sound.id)) {
      this.soundInstances.set(sound.id, [])
    }
    this.soundInstances.get(sound.id)!.push(source)
    
    // 播放结束清理
    source.addEventListener('ended', () => {
      const instances = this.soundInstances.get(sound.id)
      if (instances) {
        const index = instances.indexOf(source)
        if (index > -1) {
          instances.splice(index, 1)
        }
      }
      options.onEnd?.()
    })
    
    // 延迟播放
    const startTime = this.audioContext.currentTime + (options.delay || 0) / 1000
    source.start(startTime)
  }

  /**
   * 获取分类音量
   */
  private getCategoryVolume(category: string): number {
    switch (category) {
      case 'ui':
      case 'notification':
        return this.settings.volume.effects
      case 'achievement':
        return this.settings.volume.effects * 1.2 // 成就音效稍微大声一点
      case 'background':
      case 'ambient':
        return this.settings.volume.background
      case 'voice':
        return this.settings.volume.voice
      default:
        return this.settings.volume.effects
    }
  }

  /**
   * 停止音效
   */
  stopSound(soundId: string): void {
    // 停止HTML Audio元素
    const audio = this.audioElements.get(soundId)
    if (audio) {
      audio.pause()
      audio.currentTime = 0
    }

    // 停止Web Audio API实例
    const instances = this.soundInstances.get(soundId)
    if (instances) {
      instances.forEach(source => {
        try {
          source.stop()
        } catch (error) {
          // 忽略已经停止的源
        }
      })
      instances.length = 0
    }
  }

  /**
   * 停止所有音效
   */
  stopAllSounds(): void {
    this.soundEffects.forEach((_, soundId) => {
      this.stopSound(soundId)
    })
  }

  /**
   * 音量淡变效果
   */
  private async fadeVolume(
    audio: HTMLAudioElement, 
    targetVolume: number, 
    duration: number
  ): Promise<void> {
    const startVolume = audio.volume
    const volumeDiff = targetVolume - startVolume
    const steps = 20
    const stepDuration = duration / steps
    
    for (let i = 1; i <= steps; i++) {
      const progress = i / steps
      audio.volume = startVolume + (volumeDiff * progress)
      await new Promise(resolve => setTimeout(resolve, stepDuration))
    }
  }

  /**
   * 开始麦克风监听
   */
  async startMicrophone(deviceId?: string): Promise<MediaStream> {
    try {
             const constraints: MediaStreamConstraints = {
         audio: {
           deviceId: deviceId ? { exact: deviceId } : undefined,
           echoCancellation: this.settings.effects.noiseReduction,
           noiseSuppression: this.settings.effects.noiseReduction,
           autoGainControl: true,
           sampleRate: 44100
         }
       }

      this.currentMicStream = await navigator.mediaDevices.getUserMedia(constraints)
      
      // 连接到音频分析器
      if (this.audioContext && this.analyzerNode) {
        const source = this.audioContext.createMediaStreamSource(this.currentMicStream)
        source.connect(this.analyzerNode)
      }

      this.emit('microphoneStarted', { deviceId, stream: this.currentMicStream })
      return this.currentMicStream

    } catch (error) {
      console.error('麦克风启动失败:', error)
      throw error
    }
  }

  /**
   * 停止麦克风监听
   */
  stopMicrophone(): void {
    if (this.currentMicStream) {
      this.currentMicStream.getTracks().forEach(track => track.stop())
      this.currentMicStream = null
      this.emit('microphoneStopped', {})
    }
  }

  /**
   * 获取音频分析数据
   */
  getAudioAnalysis(): AudioAnalyzerData | null {
    if (!this.analyzerNode) return null

    const bufferLength = this.analyzerNode.frequencyBinCount
    const dataArray = new Uint8Array(bufferLength)
    const waveArray = new Uint8Array(bufferLength)
    
    this.analyzerNode.getByteFrequencyData(dataArray)
    this.analyzerNode.getByteTimeDomainData(waveArray)

    // 计算平均音量
    const volume = dataArray.reduce((sum, value) => sum + value, 0) / bufferLength

    // 计算主要频率
    let maxIndex = 0
    let maxValue = 0
    for (let i = 0; i < dataArray.length; i++) {
      if (dataArray[i] > maxValue) {
        maxValue = dataArray[i]
        maxIndex = i
      }
    }
    const frequency = maxIndex * (this.audioContext!.sampleRate / 2) / bufferLength

    return {
      volume: volume / 255,
      frequency,
      waveform: Array.from(waveArray).map(v => (v - 128) / 128),
      spectrum: Array.from(dataArray).map(v => v / 255),
      timestamp: Date.now()
    }
  }

  /**
   * 更新设置
   */
  updateSettings(newSettings: Partial<AudioSettings>): void {
    this.settings = { ...this.settings, ...newSettings }
    
    // 更新主音量
    if (this.masterGainNode) {
      this.masterGainNode.gain.value = this.settings.volume.muted ? 0 : 
        (this.settings.volume.master / 100)
    }
    
    // 更新音效元素音量
    this.audioElements.forEach((audio, soundId) => {
      const sound = this.soundEffects.get(soundId)
      if (sound) {
        const volume = (sound.volume || 1) * 
                      (this.getCategoryVolume(sound.category) / 100) *
                      (this.settings.volume.master / 100)
        audio.volume = this.settings.volume.muted ? 0 : Math.max(0, Math.min(1, volume))
      }
    })

    this.emit('settingsUpdated', this.settings)
  }

  /**
   * 获取设备列表
   */
  getInputDevices(): AudioDeviceInfo[] {
    return [...this.inputDevices]
  }

  getOutputDevices(): AudioDeviceInfo[] {
    return [...this.outputDevices]
  }

  /**
   * 获取音效列表
   */
  getSoundEffects(): SoundEffect[] {
    return Array.from(this.soundEffects.values())
  }

  /**
   * 检查服务状态
   */
  isReady(): boolean {
    return this.isInitialized && this.audioContext !== null
  }

  /**
   * 事件监听
   */
  addEventListener(event: string, listener: (data: any) => void): void {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, [])
    }
    this.listeners.get(event)!.push(listener)
  }

  removeEventListener(event: string, listener: (data: any) => void): void {
    const eventListeners = this.listeners.get(event)
    if (eventListeners) {
      const index = eventListeners.indexOf(listener)
      if (index > -1) {
        eventListeners.splice(index, 1)
      }
    }
  }

  private emit(event: string, data: any): void {
    const eventListeners = this.listeners.get(event)
    if (eventListeners) {
      eventListeners.forEach(listener => {
        try {
          listener(data)
        } catch (error) {
          console.error(`音频事件监听器错误 (${event}):`, error)
        }
      })
    }
  }

  /**
   * 销毁服务
   */
  destroy(): void {
    this.stopAllSounds()
    this.stopMicrophone()
    
    if (this.audioContext) {
      this.audioContext.close()
      this.audioContext = null
    }
    
    this.audioElements.clear()
    this.soundEffects.clear()
    this.soundInstances.clear()
    this.listeners.clear()
    this.isInitialized = false
  }
}

// 单例实例
export const audioService = new AudioService() 