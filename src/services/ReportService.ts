// 报告服务
// 管理报告的生成、存储、检索和导出

import ReportGenerator, { 
  GeneratedReport, 
  ReportConfig, 
  ReportType, 
  ReportFormat 
} from './ReportGenerator'
import DataAnalysisEngine from './DataAnalysisEngine'
import DataAnalyticsCollector from './DataAnalyticsCollector'
import { DatabaseManager } from '../storage/DatabaseManager'

// 报告存储接口
export interface StoredReport {
  id: string
  userId: string
  type: ReportType
  format: ReportFormat
  title: string
  filePath: string
  size: number
  createdAt: number
  updatedAt: number
  metadata: any
  isPublic: boolean
  tags: string[]
}

// 报告查询参数
export interface ReportQuery {
  userId?: string
  type?: ReportType
  format?: ReportFormat
  startDate?: number
  endDate?: number
  tags?: string[]
  limit?: number
  offset?: number
  sortBy?: 'createdAt' | 'updatedAt' | 'type'
  sortOrder?: 'asc' | 'desc'
}

// 报告统计信息
export interface ReportStats {
  totalReports: number
  reportsByType: Record<ReportType, number>
  reportsByFormat: Record<ReportFormat, number>
  totalSize: number
  averageSize: number
  oldestReport: number
  newestReport: number
}

// 报告导出选项
export interface ExportOptions {
  format: ReportFormat
  includeAttachments: boolean
  compression: boolean
  password?: string
}

// 报告服务类
export class ReportService {
  private generator: ReportGenerator
  private dbManager: DatabaseManager
  private collector: DataAnalyticsCollector
  private analysisEngine: DataAnalysisEngine
  
  // 存储路径
  private readonly REPORTS_DIR = './.reports'
  private readonly CACHE_DIR = './.reports/cache'
  private readonly EXPORTS_DIR = './.reports/exports'
  
  // 缓存设置
  private reportCache: Map<string, GeneratedReport> = new Map()
  private readonly CACHE_TTL = 30 * 60 * 1000 // 30分钟
  
  constructor(
    collector: DataAnalyticsCollector,
    analysisEngine: DataAnalysisEngine,
    dbManager: DatabaseManager
  ) {
    this.collector = collector
    this.analysisEngine = analysisEngine
    this.dbManager = dbManager
    this.generator = new ReportGenerator(analysisEngine, collector)
    
    this.initializeDirectories()
  }

  /**
   * 生成并保存报告
   */
  async generateAndSaveReport(
    userId: string,
    config: Partial<ReportConfig> = {}
  ): Promise<StoredReport> {
    try {
      // 生成报告
      const report = await this.generator.generateReport(userId, config)
      
      // 保存到文件系统
      const filePath = await this.saveReportToFile(report)
      
      // 保存到数据库
      const storedReport = await this.saveReportToDatabase(report, filePath)
      
      // 缓存报告
      this.cacheReport(report)
      
      return storedReport
      
    } catch (error) {
      console.error('生成报告失败:', error)
      throw new Error(`报告生成失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 批量生成报告
   */
  async generateBatchReports(
    userId: string,
    reportTypes: ReportType[],
    config: Partial<ReportConfig> = {}
  ): Promise<StoredReport[]> {
    const results: StoredReport[] = []
    
    for (const type of reportTypes) {
      try {
        const reportConfig = { ...config, type }
        const storedReport = await this.generateAndSaveReport(userId, reportConfig)
        results.push(storedReport)
      } catch (error) {
        console.error(`生成${type}报告失败:`, error)
        // 继续处理其他报告
      }
    }
    
    return results
  }

  /**
   * 获取报告
   */
  async getReport(reportId: string): Promise<GeneratedReport | null> {
    try {
      // 先检查缓存
      const cached = this.reportCache.get(reportId)
      if (cached && this.isCacheValid(reportId)) {
        return cached
      }
      
      // 从数据库获取报告信息
      const storedReport = await this.getStoredReportById(reportId)
      if (!storedReport) {
        return null
      }
      
      // 从文件系统读取报告内容
      const report = await this.loadReportFromFile(storedReport.filePath)
      
      // 缓存报告
      this.cacheReport(report)
      
      return report
      
    } catch (error) {
      console.error('获取报告失败:', error)
      return null
    }
  }

  /**
   * 查询报告列表
   */
  async queryReports(query: ReportQuery = {}): Promise<StoredReport[]> {
    try {
      return await this.queryStoredReports(query)
    } catch (error) {
      console.error('查询报告列表失败:', error)
      return []
    }
  }

  /**
   * 获取用户报告统计
   */
  async getReportStats(userId: string): Promise<ReportStats> {
    try {
      const reports = await this.queryReports({ userId })
      
      const stats: ReportStats = {
        totalReports: reports.length,
        reportsByType: {} as Record<ReportType, number>,
        reportsByFormat: {} as Record<ReportFormat, number>,
        totalSize: 0,
        averageSize: 0,
        oldestReport: 0,
        newestReport: 0
      }
      
      if (reports.length === 0) {
        return stats
      }
      
      // 计算统计信息
      reports.forEach(report => {
        // 按类型统计
        stats.reportsByType[report.type] = (stats.reportsByType[report.type] || 0) + 1
        
        // 按格式统计
        stats.reportsByFormat[report.format] = (stats.reportsByFormat[report.format] || 0) + 1
        
        // 大小统计
        stats.totalSize += report.size
        
        // 时间统计
        if (stats.oldestReport === 0 || report.createdAt < stats.oldestReport) {
          stats.oldestReport = report.createdAt
        }
        if (report.createdAt > stats.newestReport) {
          stats.newestReport = report.createdAt
        }
      })
      
      stats.averageSize = stats.totalSize / reports.length
      
      return stats
      
    } catch (error) {
      console.error('获取报告统计失败:', error)
      throw error
    }
  }

  /**
   * 删除报告
   */
  async deleteReport(reportId: string): Promise<boolean> {
    try {
      // 获取报告信息
      const storedReport = await this.getStoredReportById(reportId)
      if (!storedReport) {
        return false
      }
      
      // 删除文件
      await this.deleteReportFile(storedReport.filePath)
      
      // 从数据库删除
      await this.deleteStoredReport(reportId)
      
      // 清除缓存
      this.reportCache.delete(reportId)
      
      return true
      
    } catch (error) {
      console.error('删除报告失败:', error)
      return false
    }
  }

  /**
   * 导出报告
   */
  async exportReport(
    reportId: string,
    options: ExportOptions
  ): Promise<string> {
    try {
      // 获取报告
      const report = await this.getReport(reportId)
      if (!report) {
        throw new Error('报告不存在')
      }
      
      // 生成导出文件名
      const exportFileName = this.generateExportFileName(report, options)
      const exportPath = `${this.EXPORTS_DIR}/${exportFileName}`
      
      // 根据格式导出
      await this.saveExportFile(report, exportPath, options)
      
      return exportPath
      
    } catch (error) {
      console.error('导出报告失败:', error)
      throw error
    }
  }

  /**
   * 清理过期报告
   */
  async cleanupExpiredReports(retentionDays: number = 90): Promise<number> {
    try {
      const cutoffTime = Date.now() - (retentionDays * 24 * 60 * 60 * 1000)
      const expiredReports = await this.queryReports({
        endDate: cutoffTime,
        sortBy: 'createdAt',
        sortOrder: 'asc'
      })
      
      let deletedCount = 0
      
      for (const report of expiredReports) {
        if (await this.deleteReport(report.id)) {
          deletedCount++
        }
      }
      
      return deletedCount
      
    } catch (error) {
      console.error('清理过期报告失败:', error)
      return 0
    }
  }

  /**
   * 获取报告预览
   */
  async getReportPreview(reportId: string): Promise<{
    title: string
    summary: string
    charts: string[]
    keyMetrics: any
  } | null> {
    try {
      const report = await this.getReport(reportId)
      if (!report) {
        return null
      }
      
      // 提取预览信息
      return {
        title: report.title,
        summary: this.extractSummary(report.content),
        charts: report.attachments?.charts || [],
        keyMetrics: this.extractKeyMetrics(report.content)
      }
      
    } catch (error) {
      console.error('获取报告预览失败:', error)
      return null
    }
  }

  // ===== 私有方法 =====

  /**
   * 初始化目录结构
   */
  private async initializeDirectories(): Promise<void> {
    // 这里应该创建必要的目录
    // 简化实现
  }

  /**
   * 保存报告到文件
   */
  private async saveReportToFile(report: GeneratedReport): Promise<string> {
    const fileName = `${report.id}.${report.format}`
    const filePath = `${this.REPORTS_DIR}/${fileName}`
    
    // 这里应该实际保存文件
    // 简化实现，返回文件路径
    return filePath
  }

  /**
   * 保存报告到数据库
   */
  private async saveReportToDatabase(
    report: GeneratedReport,
    filePath: string
  ): Promise<StoredReport> {
    const storedReport: StoredReport = {
      id: report.id,
      userId: report.userId,
      type: report.type,
      format: report.format,
      title: report.title,
      filePath,
      size: report.content.length,
      createdAt: Date.now(),
      updatedAt: Date.now(),
      metadata: report.metadata,
      isPublic: false,
      tags: []
    }
    
    // 这里应该保存到实际数据库
    // 简化实现
    return storedReport
  }

  /**
   * 从文件加载报告
   */
  private async loadReportFromFile(filePath: string): Promise<GeneratedReport> {
    // 这里应该从文件系统读取报告
    // 简化实现，返回空报告
    return {
      id: '',
      userId: '',
      type: 'comprehensive',
      format: 'html',
      title: '',
      content: '',
      metadata: {
        generatedAt: 0,
        dataRange: { startDate: 0, endDate: 0 },
        analysisVersion: '',
        reportVersion: ''
      }
    }
  }

  /**
   * 从数据库获取存储的报告信息
   */
  private async getStoredReportById(reportId: string): Promise<StoredReport | null> {
    // 这里应该从数据库查询
    // 简化实现
    return null
  }

  /**
   * 查询存储的报告
   */
  private async queryStoredReports(query: ReportQuery): Promise<StoredReport[]> {
    // 这里应该执行数据库查询
    // 简化实现
    return []
  }

  /**
   * 删除存储的报告
   */
  private async deleteStoredReport(reportId: string): Promise<void> {
    // 这里应该从数据库删除
    // 简化实现
  }

  /**
   * 删除报告文件
   */
  private async deleteReportFile(filePath: string): Promise<void> {
    // 这里应该删除文件系统中的文件
    // 简化实现
  }

  /**
   * 缓存报告
   */
  private cacheReport(report: GeneratedReport): void {
    this.reportCache.set(report.id, report)
    
    // 设置过期清理
    setTimeout(() => {
      this.reportCache.delete(report.id)
    }, this.CACHE_TTL)
  }

  /**
   * 检查缓存是否有效
   */
  private isCacheValid(reportId: string): boolean {
    return this.reportCache.has(reportId)
  }

  /**
   * 生成导出文件名
   */
  private generateExportFileName(report: GeneratedReport, options: ExportOptions): string {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
    const extension = options.format === 'pdf' ? 'pdf' : 
                     options.format === 'json' ? 'json' : 
                     options.format === 'markdown' ? 'md' : 'html'
    
    return `${report.type}-report-${timestamp}.${extension}`
  }

  /**
   * 保存导出文件
   */
  private async saveExportFile(
    report: GeneratedReport,
    exportPath: string,
    options: ExportOptions
  ): Promise<void> {
    // 这里应该根据选项保存导出文件
    // 简化实现
  }

  /**
   * 提取报告摘要
   */
  private extractSummary(content: string): string {
    // 简化实现，提取前200字符
    return content.replace(/<[^>]*>/g, '').substring(0, 200) + '...'
  }

  /**
   * 提取关键指标
   */
  private extractKeyMetrics(content: string): any {
    // 简化实现，返回空对象
    return {}
  }
}

export default ReportService 