import {
  Achievement,
  UserAchievementProgress,
  UserExperience,
  ExperienceRecord,
  UserReward,
  UserDailyTaskProgress,
  UserWeeklyTaskProgress,
  AchievementStats,
  LevelUpHistory,
  AchievementNotification
} from '../types/achievements';

// 存储键名常量
const STORAGE_KEYS = {
  USER_EXPERIENCE: 'achievement_user_experience',
  ACHIEVEMENT_PROGRESS: 'achievement_progress',
  EXPERIENCE_RECORDS: 'achievement_experience_records',
  USER_REWARDS: 'achievement_user_rewards',
  DAILY_TASK_PROGRESS: 'achievement_daily_tasks',
  WEEKLY_TASK_PROGRESS: 'achievement_weekly_tasks',
  LEVEL_UP_HISTORY: 'achievement_level_history',
  ACHIEVEMENT_STATS: 'achievement_stats',
  NOTIFICATIONS: 'achievement_notifications',
  LAST_SYNC: 'achievement_last_sync',
  USER_SETTINGS: 'achievement_user_settings'
} as const;

// 用户设置接口
interface UserSettings {
  enableNotifications: boolean;
  autoHideNotifications: boolean;
  notificationDelay: number;
  enableSounds: boolean;
  enableAnimations: boolean;
  dataRetentionDays: number;
  lastCleanup: Date;
}

// 数据备份接口
interface DataBackup {
  version: string;
  timestamp: Date;
  userExperience: UserExperience;
  achievementProgress: UserAchievementProgress[];
  experienceRecords: ExperienceRecord[];
  userRewards: UserReward[];
  dailyTaskProgress: UserDailyTaskProgress[];
  weeklyTaskProgress: UserWeeklyTaskProgress[];
  levelUpHistory: LevelUpHistory[];
  achievementStats: AchievementStats;
  userSettings: UserSettings;
}

// 数据同步状态
interface SyncStatus {
  lastSync: Date;
  isOnline: boolean;
  hasPendingChanges: boolean;
  errorCount: number;
  lastError?: string;
}

export class AchievementDataService {
  private readonly version = '1.0.0';
  private syncStatus: SyncStatus;
  private changeListeners: Set<(key: string, data: any) => void> = new Set();
  private isInitialized = false;

  constructor() {
    this.syncStatus = {
      lastSync: new Date(0),
      isOnline: navigator.onLine,
      hasPendingChanges: false,
      errorCount: 0
    };

    // 监听在线状态变化
    window.addEventListener('online', () => {
      this.syncStatus.isOnline = true;
      this.trySyncPendingChanges();
    });

    window.addEventListener('offline', () => {
      this.syncStatus.isOnline = false;
    });
  }

  // 初始化服务
  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      // 初始化默认数据
      await this.initializeDefaultData();
      
      // 执行数据清理
      await this.cleanupOldData();
      
      // 尝试同步数据
      if (this.syncStatus.isOnline) {
        await this.trySyncPendingChanges();
      }

      this.isInitialized = true;
      console.log('🔧 AchievementDataService initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize AchievementDataService:', error);
      throw error;
    }
  }

  // ===== 用户经验值相关 =====

  async getUserExperience(): Promise<UserExperience> {
    try {
      const data = localStorage.getItem(STORAGE_KEYS.USER_EXPERIENCE);
      if (!data) {
        return this.getDefaultUserExperience();
      }

      const parsed = JSON.parse(data);
      // 确保日期字段正确解析
      if (parsed.lastLevelUp) {
        parsed.lastLevelUp = new Date(parsed.lastLevelUp);
      }

      return parsed;
    } catch (error) {
      console.error('❌ Error loading user experience:', error);
      return this.getDefaultUserExperience();
    }
  }

  async saveUserExperience(experience: UserExperience): Promise<void> {
    try {
      localStorage.setItem(STORAGE_KEYS.USER_EXPERIENCE, JSON.stringify(experience));
      this.notifyChange(STORAGE_KEYS.USER_EXPERIENCE, experience);
      this.syncStatus.hasPendingChanges = true;
    } catch (error) {
      console.error('❌ Error saving user experience:', error);
      throw error;
    }
  }

  // ===== 成就进度相关 =====

  async getAchievementProgress(): Promise<UserAchievementProgress[]> {
    try {
      const data = localStorage.getItem(STORAGE_KEYS.ACHIEVEMENT_PROGRESS);
      if (!data) return [];

      const parsed = JSON.parse(data);
      return parsed.map((item: any) => ({
        ...item,
        lastUpdated: new Date(item.lastUpdated),
        completedAt: item.completedAt ? new Date(item.completedAt) : undefined
      }));
    } catch (error) {
      console.error('❌ Error loading achievement progress:', error);
      return [];
    }
  }

  async saveAchievementProgress(progress: UserAchievementProgress[]): Promise<void> {
    try {
      localStorage.setItem(STORAGE_KEYS.ACHIEVEMENT_PROGRESS, JSON.stringify(progress));
      this.notifyChange(STORAGE_KEYS.ACHIEVEMENT_PROGRESS, progress);
      this.syncStatus.hasPendingChanges = true;
    } catch (error) {
      console.error('❌ Error saving achievement progress:', error);
      throw error;
    }
  }

  async updateAchievementProgress(achievementId: string, progress: Partial<UserAchievementProgress>): Promise<void> {
    const allProgress = await this.getAchievementProgress();
    const index = allProgress.findIndex(p => p.achievementId === achievementId);

    if (index >= 0) {
      allProgress[index] = { ...allProgress[index], ...progress, lastUpdated: new Date() };
    } else {
      allProgress.push({
        achievementId,
        progress: 0,
        maxProgress: 1,
        isCompleted: false,
        lastUpdated: new Date(),
        ...progress
      });
    }

    await this.saveAchievementProgress(allProgress);
  }

  // ===== 经验值记录相关 =====

  async getExperienceRecords(): Promise<ExperienceRecord[]> {
    try {
      const data = localStorage.getItem(STORAGE_KEYS.EXPERIENCE_RECORDS);
      if (!data) return [];

      const parsed = JSON.parse(data);
      return parsed.map((item: any) => ({
        ...item,
        earnedAt: new Date(item.earnedAt)
      }));
    } catch (error) {
      console.error('❌ Error loading experience records:', error);
      return [];
    }
  }

  async addExperienceRecord(record: Omit<ExperienceRecord, 'id'>): Promise<void> {
    try {
      const records = await this.getExperienceRecords();
      const newRecord: ExperienceRecord = {
        ...record,
        id: `exp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      };

      records.push(newRecord);
      
      // 限制记录数量，保留最近的1000条
      if (records.length > 1000) {
        records.sort((a, b) => b.earnedAt.getTime() - a.earnedAt.getTime());
        records.splice(1000);
      }

      localStorage.setItem(STORAGE_KEYS.EXPERIENCE_RECORDS, JSON.stringify(records));
      this.notifyChange(STORAGE_KEYS.EXPERIENCE_RECORDS, records);
      this.syncStatus.hasPendingChanges = true;
    } catch (error) {
      console.error('❌ Error adding experience record:', error);
      throw error;
    }
  }

  // ===== 等级晋升历史 =====

  async getLevelUpHistory(): Promise<LevelUpHistory[]> {
    try {
      const data = localStorage.getItem(STORAGE_KEYS.LEVEL_UP_HISTORY);
      if (!data) return [];

      const parsed = JSON.parse(data);
      return parsed.map((item: any) => ({
        ...item,
        timestamp: new Date(item.timestamp)
      }));
    } catch (error) {
      console.error('❌ Error loading level up history:', error);
      return [];
    }
  }

  async addLevelUpRecord(record: LevelUpHistory): Promise<void> {
    try {
      const history = await this.getLevelUpHistory();
      history.push(record);
      
      // 按时间降序排序
      history.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());

      localStorage.setItem(STORAGE_KEYS.LEVEL_UP_HISTORY, JSON.stringify(history));
      this.notifyChange(STORAGE_KEYS.LEVEL_UP_HISTORY, history);
      this.syncStatus.hasPendingChanges = true;
    } catch (error) {
      console.error('❌ Error adding level up record:', error);
      throw error;
    }
  }

  // ===== 通知管理 =====

  async getNotifications(): Promise<AchievementNotification[]> {
    try {
      const data = localStorage.getItem(STORAGE_KEYS.NOTIFICATIONS);
      if (!data) return [];

      const parsed = JSON.parse(data);
      return parsed.map((item: any) => ({
        ...item,
        createdAt: new Date(item.createdAt)
      }));
    } catch (error) {
      console.error('❌ Error loading notifications:', error);
      return [];
    }
  }

  async addNotification(notification: AchievementNotification): Promise<void> {
    try {
      const notifications = await this.getNotifications();
      notifications.push(notification);

      // 限制通知数量，保留最近的50条
      if (notifications.length > 50) {
        notifications.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
        notifications.splice(50);
      }

      localStorage.setItem(STORAGE_KEYS.NOTIFICATIONS, JSON.stringify(notifications));
      this.notifyChange(STORAGE_KEYS.NOTIFICATIONS, notifications);
    } catch (error) {
      console.error('❌ Error adding notification:', error);
      throw error;
    }
  }

  async markNotificationAsRead(index: number): Promise<void> {
    try {
      const notifications = await this.getNotifications();
      if (notifications[index]) {
        notifications[index].isRead = true;
        localStorage.setItem(STORAGE_KEYS.NOTIFICATIONS, JSON.stringify(notifications));
        this.notifyChange(STORAGE_KEYS.NOTIFICATIONS, notifications);
      }
    } catch (error) {
      console.error('❌ Error marking notification as read:', error);
      throw error;
    }
  }

  async clearAllNotifications(): Promise<void> {
    try {
      localStorage.setItem(STORAGE_KEYS.NOTIFICATIONS, JSON.stringify([]));
      this.notifyChange(STORAGE_KEYS.NOTIFICATIONS, []);
    } catch (error) {
      console.error('❌ Error clearing notifications:', error);
      throw error;
    }
  }

  // ===== 用户设置 =====

  async getUserSettings(): Promise<UserSettings> {
    try {
      const data = localStorage.getItem(STORAGE_KEYS.USER_SETTINGS);
      if (!data) {
        return this.getDefaultUserSettings();
      }

      const parsed = JSON.parse(data);
      return {
        ...this.getDefaultUserSettings(),
        ...parsed,
        lastCleanup: new Date(parsed.lastCleanup || 0)
      };
    } catch (error) {
      console.error('❌ Error loading user settings:', error);
      return this.getDefaultUserSettings();
    }
  }

  async saveUserSettings(settings: Partial<UserSettings>): Promise<void> {
    try {
      const currentSettings = await this.getUserSettings();
      const newSettings = { ...currentSettings, ...settings };
      
      localStorage.setItem(STORAGE_KEYS.USER_SETTINGS, JSON.stringify(newSettings));
      this.notifyChange(STORAGE_KEYS.USER_SETTINGS, newSettings);
      this.syncStatus.hasPendingChanges = true;
    } catch (error) {
      console.error('❌ Error saving user settings:', error);
      throw error;
    }
  }

  // ===== 数据导出/导入 =====

  async exportData(): Promise<DataBackup> {
    try {
      const backup: DataBackup = {
        version: this.version,
        timestamp: new Date(),
        userExperience: await this.getUserExperience(),
        achievementProgress: await this.getAchievementProgress(),
        experienceRecords: await this.getExperienceRecords(),
        userRewards: [], // TODO: 实现用户奖励系统
        dailyTaskProgress: [], // TODO: 实现每日任务进度
        weeklyTaskProgress: [], // TODO: 实现每周任务进度
        levelUpHistory: await this.getLevelUpHistory(),
        achievementStats: await this.calculateStats(),
        userSettings: await this.getUserSettings()
      };

      return backup;
    } catch (error) {
      console.error('❌ Error exporting data:', error);
      throw error;
    }
  }

  async importData(backup: DataBackup): Promise<void> {
    try {
      // 验证备份版本兼容性
      if (!this.isVersionCompatible(backup.version)) {
        throw new Error(`Incompatible backup version: ${backup.version}`);
      }

      // 导入所有数据
      await this.saveUserExperience(backup.userExperience);
      await this.saveAchievementProgress(backup.achievementProgress);
      
      // 导入经验值记录
      for (const record of backup.experienceRecords) {
        await this.addExperienceRecord(record);
      }

      // 导入等级晋升历史
      localStorage.setItem(STORAGE_KEYS.LEVEL_UP_HISTORY, JSON.stringify(backup.levelUpHistory));
      
      // 导入用户设置
      await this.saveUserSettings(backup.userSettings);

      console.log('✅ Data import completed successfully');
    } catch (error) {
      console.error('❌ Error importing data:', error);
      throw error;
    }
  }

  // ===== 数据统计 =====

  async calculateStats(): Promise<AchievementStats> {
    try {
      const progress = await this.getAchievementProgress();
      const totalAchievements = progress.length;
      const completedAchievements = progress.filter(p => p.isCompleted).length;
      const completionRate = totalAchievements > 0 ? (completedAchievements / totalAchievements) * 100 : 0;

      // 获取最后完成的成就
      const lastCompleted = progress
        .filter(p => p.isCompleted && p.completedAt)
        .sort((a, b) => (b.completedAt?.getTime() || 0) - (a.completedAt?.getTime() || 0))[0];

      return {
        totalAchievements,
        completedAchievements,
        completionRate,
        lastAchievement: lastCompleted ? {
          achievement: {} as Achievement, // 需要从配置中获取
          completedAt: lastCompleted.completedAt!
        } : undefined,
        categoryCounts: {
          focus: { total: 0, completed: 0 },
          posture: { total: 0, completed: 0 },
          time: { total: 0, completed: 0 },
          consistency: { total: 0, completed: 0 },
          improvement: { total: 0, completed: 0 }
        }
      };
    } catch (error) {
      console.error('❌ Error calculating stats:', error);
      throw error;
    }
  }

  // ===== 数据清理 =====

  async cleanupOldData(): Promise<void> {
    try {
      const settings = await this.getUserSettings();
      const now = new Date();
      const cutoffDate = new Date(now.getTime() - settings.dataRetentionDays * 24 * 60 * 60 * 1000);

      // 清理旧的经验值记录
      const records = await this.getExperienceRecords();
      const filteredRecords = records.filter(r => r.earnedAt > cutoffDate);
      
      if (filteredRecords.length !== records.length) {
        localStorage.setItem(STORAGE_KEYS.EXPERIENCE_RECORDS, JSON.stringify(filteredRecords));
        console.log(`🧹 Cleaned up ${records.length - filteredRecords.length} old experience records`);
      }

      // 清理旧通知
      const notifications = await this.getNotifications();
      const filteredNotifications = notifications.filter(n => n.createdAt > cutoffDate);
      
      if (filteredNotifications.length !== notifications.length) {
        localStorage.setItem(STORAGE_KEYS.NOTIFICATIONS, JSON.stringify(filteredNotifications));
        console.log(`🧹 Cleaned up ${notifications.length - filteredNotifications.length} old notifications`);
      }

      // 更新最后清理时间
      await this.saveUserSettings({ lastCleanup: now });
    } catch (error) {
      console.error('❌ Error during data cleanup:', error);
    }
  }

  // ===== 变更监听 =====

  addChangeListener(callback: (key: string, data: any) => void): () => void {
    this.changeListeners.add(callback);
    return () => this.changeListeners.delete(callback);
  }

  private notifyChange(key: string, data: any): void {
    this.changeListeners.forEach(callback => {
      try {
        callback(key, data);
      } catch (error) {
        console.error('❌ Error in change listener:', error);
      }
    });
  }

  // ===== 私有辅助方法 =====

  private async initializeDefaultData(): Promise<void> {
    // 初始化用户经验值
    const userExp = await this.getUserExperience();
    if (userExp.currentLevel === 0) {
      await this.saveUserExperience(this.getDefaultUserExperience());
    }

    // 初始化用户设置
    const settings = await this.getUserSettings();
    if (!settings.lastCleanup || settings.lastCleanup.getTime() === 0) {
      await this.saveUserSettings({ lastCleanup: new Date() });
    }
  }

  private getDefaultUserExperience(): UserExperience {
    return {
      currentLevel: 1,
      currentExperience: 0,
      totalExperience: 0,
      experienceToNextLevel: 1000
    };
  }

  private getDefaultUserSettings(): UserSettings {
    return {
      enableNotifications: true,
      autoHideNotifications: true,
      notificationDelay: 5000,
      enableSounds: true,
      enableAnimations: true,
      dataRetentionDays: 90,
      lastCleanup: new Date()
    };
  }

  private isVersionCompatible(version: string): boolean {
    // 简单的版本兼容性检查
    const [major] = version.split('.').map(Number);
    const [currentMajor] = this.version.split('.').map(Number);
    return major === currentMajor;
  }

  private async trySyncPendingChanges(): Promise<void> {
    if (!this.syncStatus.hasPendingChanges || !this.syncStatus.isOnline) {
      return;
    }

    try {
      // TODO: 实现云端同步逻辑
      console.log('🔄 Syncing pending changes...');
      
      this.syncStatus.lastSync = new Date();
      this.syncStatus.hasPendingChanges = false;
      this.syncStatus.errorCount = 0;
      
      localStorage.setItem(STORAGE_KEYS.LAST_SYNC, this.syncStatus.lastSync.toISOString());
    } catch (error) {
      this.syncStatus.errorCount++;
      this.syncStatus.lastError = (error as Error).message;
      console.error('❌ Error during sync:', error);
    }
  }

  // ===== 公共API =====

  getSyncStatus(): SyncStatus {
    return { ...this.syncStatus };
  }

  async forceSyncData(): Promise<void> {
    this.syncStatus.hasPendingChanges = true;
    await this.trySyncPendingChanges();
  }

  async clearAllData(): Promise<void> {
    try {
      Object.values(STORAGE_KEYS).forEach(key => {
        localStorage.removeItem(key);
      });
      
      console.log('🗑️ All achievement data cleared');
      
      // 重新初始化默认数据
      await this.initializeDefaultData();
    } catch (error) {
      console.error('❌ Error clearing data:', error);
      throw error;
    }
  }

  getStorageUsage(): { used: number; total: number; percentage: number } {
    try {
      let used = 0;
      Object.values(STORAGE_KEYS).forEach(key => {
        const item = localStorage.getItem(key);
        if (item) {
          used += new Blob([item]).size;
        }
      });

      const total = 5 * 1024 * 1024; // 假设 5MB 限制
      const percentage = (used / total) * 100;

      return { used, total, percentage };
    } catch (error) {
      console.error('❌ Error calculating storage usage:', error);
      return { used: 0, total: 0, percentage: 0 };
    }
  }
} 