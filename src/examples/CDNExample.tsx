import React, { useState, useEffect } from 'react'
import { 
  CDNConfig, 
  CDNProvider, 
  CDNUrlGenerator, 
  CDNMonitor, 
  getEnvironmentCDNConfig,
  CDNPerformanceMetrics 
} from '../../config/cdn.config'

// CDN使用示例组件
const CDNExample: React.FC = () => {
  const [cdnConfig, setCdnConfig] = useState<CDNConfig>()
  const [urlGenerator, setUrlGenerator] = useState<CDNUrlGenerator>()
  const [monitor, setMonitor] = useState<CDNMonitor>()
  const [performanceData, setPerformanceData] = useState<CDNPerformanceMetrics[]>([])
  const [healthStatus, setHealthStatus] = useState<any>()

  useEffect(() => {
    // 初始化CDN配置
    const config = getEnvironmentCDNConfig('production')
    setCdnConfig(config)
    
    // 创建URL生成器
    const generator = new CDNUrlGenerator(config)
    setUrlGenerator(generator)
    
    // 创建性能监控器
    const cdnMonitor = new CDNMonitor()
    setMonitor(cdnMonitor)
    
    // 模拟性能数据
    const mockMetrics: CDNPerformanceMetrics = {
      provider: config.provider,
      region: 'global',
      loadTime: Math.random() * 200 + 50, // 50-250ms
      cacheHitRate: Math.random() * 30 + 70, // 70-100%
      errorRate: Math.random() * 5, // 0-5%
      bandwidth: Math.random() * 1000 + 500, // 500-1500 KB/s
      timestamp: Date.now()
    }
    
    cdnMonitor.recordMetrics(mockMetrics)
    setPerformanceData([mockMetrics])
  }, [])

  // 测试CDN健康状态
  const testCDNHealth = async () => {
    if (!monitor || !cdnConfig) return
    
    const baseUrl = cdnConfig.provider === CDNProvider.JSDELIVR 
      ? 'https://cdn.jsdelivr.net'
      : 'https://example-cdn.com'
      
    const health = await monitor.checkCDNHealth(baseUrl)
    setHealthStatus(health)
  }

  // 生成示例资源URL
  const generateExampleUrls = () => {
    if (!urlGenerator) return []
    
    return [
      urlGenerator.generateAssetUrl('assets/main-12345678.js'),
      urlGenerator.generateAssetUrl('assets/style-87654321.css'),
      urlGenerator.generateAssetUrl('images/logo-abcdef12.png'),
      urlGenerator.generateAssetUrl('fonts/roboto-98765432.woff2')
    ]
  }

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-3xl font-bold mb-6">🌐 CDN集成示例</h1>
      
      {/* CDN配置信息 */}
      <div className="bg-white rounded-lg shadow p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4">📋 CDN配置信息</h2>
        {cdnConfig && (
          <div className="grid grid-cols-2 gap-4">
            <div>
              <span className="font-medium">提供商:</span> {cdnConfig.provider}
            </div>
            <div>
              <span className="font-medium">状态:</span> 
              <span className={`ml-2 px-2 py-1 rounded text-xs ${
                cdnConfig.enabled ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
              }`}>
                {cdnConfig.enabled ? '启用' : '禁用'}
              </span>
            </div>
            <div>
              <span className="font-medium">缓存TTL:</span> {cdnConfig.cacheTTL}秒
            </div>
            <div>
              <span className="font-medium">回退机制:</span> 
              <span className={`ml-2 px-2 py-1 rounded text-xs ${
                cdnConfig.fallbackEnabled ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'
              }`}>
                {cdnConfig.fallbackEnabled ? '启用' : '禁用'}
              </span>
            </div>
          </div>
        )}
      </div>

      {/* 示例URL生成 */}
      <div className="bg-white rounded-lg shadow p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4">🔗 CDN URL生成示例</h2>
        <div className="space-y-3">
          {generateExampleUrls().map((url, index) => (
            <div key={index} className="flex items-center space-x-3 p-3 bg-gray-50 rounded">
              <span className="text-sm font-mono bg-white px-2 py-1 rounded border">
                {url}
              </span>
              <button 
                onClick={() => window.open(url, '_blank')}
                className="text-blue-600 hover:text-blue-800 text-sm"
              >
                测试链接
              </button>
            </div>
          ))}
        </div>
      </div>

      {/* 性能监控 */}
      <div className="bg-white rounded-lg shadow p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4">📊 性能监控</h2>
        {performanceData.length > 0 && monitor && (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {(() => {
              const report = monitor.getPerformanceReport()
              return [
                { label: '平均加载时间', value: `${Math.round(report.averageLoadTime)}ms`, color: 'blue' },
                { label: '缓存命中率', value: `${Math.round(report.cacheHitRate)}%`, color: 'green' },
                { label: '错误率', value: `${Math.round(report.errorRate)}%`, color: 'red' },
                { label: '总带宽', value: `${Math.round(report.totalBandwidth / 1024)}MB`, color: 'purple' }
              ].map((metric, index) => (
                <div key={index} className={`p-4 rounded-lg bg-${metric.color}-50`}>
                  <div className={`text-2xl font-bold text-${metric.color}-600`}>
                    {metric.value}
                  </div>
                  <div className={`text-sm text-${metric.color}-700`}>
                    {metric.label}
                  </div>
                </div>
              ))
            })()}
          </div>
        )}
      </div>

      {/* CDN健康检查 */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold mb-4">🏥 CDN健康检查</h2>
        <div className="flex items-center space-x-4 mb-4">
          <button 
            onClick={testCDNHealth}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            测试CDN健康状态
          </button>
        </div>
        
        {healthStatus && (
          <div className="mt-4 p-4 bg-gray-50 rounded">
            <div className="grid grid-cols-3 gap-4">
              <div>
                <span className="font-medium">状态:</span>
                <span className={`ml-2 px-2 py-1 rounded text-xs ${
                  healthStatus.isHealthy ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                }`}>
                  {healthStatus.isHealthy ? '健康' : '异常'}
                </span>
              </div>
              <div>
                <span className="font-medium">响应时间:</span> {Math.round(healthStatus.responseTime)}ms
              </div>
              <div>
                <span className="font-medium">状态码:</span> {healthStatus.statusCode || 'N/A'}
              </div>
            </div>
            {healthStatus.error && (
              <div className="mt-2 text-red-600 text-sm">
                错误: {healthStatus.error}
              </div>
            )}
          </div>
        )}
      </div>

      {/* CDN最佳实践提示 */}
      <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
        <h3 className="font-semibold text-yellow-800 mb-2">💡 CDN最佳实践</h3>
        <ul className="text-sm text-yellow-700 space-y-1">
          <li>• 为静态资源配置适当的缓存头</li>
          <li>• 使用版本化URL避免缓存问题</li>
          <li>• 启用GZIP压缩减少传输大小</li>
          <li>• 配置回退机制确保高可用性</li>
          <li>• 定期监控CDN性能和成本</li>
          <li>• 选择靠近用户的CDN边缘节点</li>
        </ul>
      </div>
    </div>
  )
}

export default CDNExample 