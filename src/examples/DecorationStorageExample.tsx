import React, { useState, useEffect } from 'react'
import { DecorationSystem } from '../systems/DecorationSystem'
import { DecorationIntegrationService } from '../services/DecorationIntegrationService'
import { DecorationStorageService, BackupMetadata } from '../services/DecorationStorageService'

interface Props {
  userId?: string
}

export const DecorationStorageExample: React.FC<Props> = ({ userId }) => {
  const [decorationSystem] = useState(() => new DecorationSystem())
  const [integrationService] = useState(() => 
    new DecorationIntegrationService(decorationSystem, {
      userId,
      autoSaveEnabled: true,
      autoSaveInterval: 10000, // 10秒演示
      backupEnabled: true,
      backupInterval: 30000, // 30秒演示
      maxBackups: 5
    })
  )

  const [isInitialized, setIsInitialized] = useState(false)
  const [status, setStatus] = useState<string>('未初始化')
  const [backups, setBackups] = useState<BackupMetadata[]>([])
  const [logs, setLogs] = useState<string[]>([])

  // 添加日志
  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString()
    setLogs(prev => [`[${timestamp}] ${message}`, ...prev.slice(0, 19)])
  }

  // 初始化系统
  useEffect(() => {
    const initializeSystem = async () => {
      try {
        addLog('开始初始化装饰存储系统...')

        // 设置事件监听
        integrationService.on('onSaveSuccess', () => {
          addLog('✅ 数据保存成功')
          setStatus('已保存')
        })

        integrationService.on('onSaveError', (error) => {
          addLog(`❌ 保存失败: ${error}`)
          setStatus('保存失败')
        })

        integrationService.on('onLoadSuccess', () => {
          addLog('✅ 数据加载成功')
          setStatus('已加载')
        })

        integrationService.on('onLoadError', (error) => {
          addLog(`❌ 加载失败: ${error}`)
        })

        integrationService.on('onBackupCreated', (backupId) => {
          addLog(`📦 备份已创建: ${backupId}`)
          refreshBackups()
        })

        integrationService.on('onSyncComplete', () => {
          addLog('🔄 同步完成')
        })

        integrationService.on('onSyncError', (error) => {
          addLog(`🔄 同步失败: ${error}`)
        })

        // 初始化集成服务
        const result = await integrationService.initialize()
        
        if (result.success) {
          setIsInitialized(true)
          setStatus('已初始化')
          addLog('✅ 装饰存储系统初始化成功')
          await refreshBackups()
        } else {
          addLog(`❌ 初始化失败: ${result.error}`)
        }

      } catch (error) {
        addLog(`❌ 初始化异常: ${error instanceof Error ? error.message : String(error)}`)
      }
    }

    initializeSystem()

    // 清理
    return () => {
      integrationService.destroy()
    }
  }, [])

  // 刷新备份列表
  const refreshBackups = async () => {
    try {
      const storageService = integrationService.getStorageService()
      const result = await storageService.getBackupList()
      
      if (result.success && result.data) {
        setBackups(result.data)
      }
    } catch (error) {
      addLog(`刷新备份列表失败: ${error instanceof Error ? error.message : String(error)}`)
    }
  }

  // 手动保存
  const handleManualSave = async () => {
    addLog('执行手动保存...')
    await integrationService.saveDecorationData()
  }

  // 手动加载
  const handleManualLoad = async () => {
    addLog('执行手动加载...')
    await integrationService.loadDecorationData()
  }

  // 创建备份
  const handleCreateBackup = async () => {
    const description = `手动备份 ${new Date().toLocaleString()}`
    addLog('创建手动备份...')
    await integrationService.createBackup(description)
  }

  // 恢复备份
  const handleRestoreBackup = async (backupId: string) => {
    addLog(`恢复备份: ${backupId}`)
    const result = await integrationService.restoreBackup(backupId)
    
    if (result.success) {
      addLog('✅ 备份恢复成功')
    } else {
      addLog(`❌ 备份恢复失败: ${result.error}`)
    }
  }

  // 删除备份
  const handleDeleteBackup = async (backupId: string) => {
    addLog(`删除备份: ${backupId}`)
    const storageService = integrationService.getStorageService()
    const result = await storageService.deleteBackup(backupId)
    
    if (result.success) {
      addLog('✅ 备份删除成功')
      await refreshBackups()
    } else {
      addLog(`❌ 备份删除失败: ${result.error}`)
    }
  }

  // 模拟装饰操作
  const handleSimulateDecorationActions = async () => {
    addLog('模拟装饰操作...')
    
    try {
      // 模拟购买装饰道具
      const purchaseResult = await decorationSystem.purchaseDecoration('wooden_fence', 1)
      if (purchaseResult.success) {
        addLog('✅ 购买装饰道具成功')
      }

      // 模拟放置装饰
      const placeResult = decorationSystem.placeDecoration('wooden_fence', 5, 5)
      if (placeResult.success) {
        addLog('✅ 放置装饰成功')
      }

      // 模拟切换主题
      const themeResult = decorationSystem.applyTheme('natural_paradise')
      if (themeResult.success) {
        addLog('✅ 切换主题成功')
      }

    } catch (error) {
      addLog(`模拟操作失败: ${error instanceof Error ? error.message : String(error)}`)
    }
  }

  // 导出数据
  const handleExportData = async () => {
    try {
      const data = await integrationService.exportAllData()
      const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `decoration_data_${Date.now()}.json`
      a.click()
      URL.revokeObjectURL(url)
      addLog('✅ 数据导出成功')
    } catch (error) {
      addLog(`❌ 数据导出失败: ${error instanceof Error ? error.message : String(error)}`)
    }
  }

  // 重置数据
  const handleResetData = async () => {
    if (window.confirm('确定要重置所有装饰数据吗？此操作不可撤销。')) {
      addLog('重置所有数据...')
      const result = await integrationService.resetAllData()
      
      if (result.success) {
        addLog('✅ 数据重置成功')
        await refreshBackups()
      } else {
        addLog(`❌ 数据重置失败: ${result.error}`)
      }
    }
  }

  if (!isInitialized) {
    return (
      <div style={{ padding: '2rem', textAlign: 'center' }}>
        <h2>装饰存储系统</h2>
        <p>正在初始化...</p>
        <div>状态：{status}</div>
      </div>
    )
  }

  return (
    <div style={{ padding: '2rem', maxWidth: '1200px', margin: '0 auto' }}>
      <h1>装饰存储系统演示</h1>
      
      {/* 状态信息 */}
      <div style={{ 
        background: '#f8fafc', 
        padding: '1rem', 
        borderRadius: '8px', 
        marginBottom: '2rem',
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
        gap: '1rem'
      }}>
        <div>
          <strong>系统状态：</strong> {status}
        </div>
        <div>
          <strong>用户ID：</strong> {userId || '游客'}
        </div>
        <div>
          <strong>备份数量：</strong> {backups.length}
        </div>
        <div>
          <strong>自动保存：</strong> {integrationService.getConfig().autoSaveEnabled ? '开启' : '关闭'}
        </div>
      </div>

      {/* 操作按钮 */}
      <div style={{ 
        display: 'grid', 
        gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', 
        gap: '1rem', 
        marginBottom: '2rem' 
      }}>
        <button 
          onClick={handleManualSave}
          style={{ padding: '0.75rem', background: '#3b82f6', color: 'white', border: 'none', borderRadius: '6px' }}
        >
          手动保存
        </button>
        
        <button 
          onClick={handleManualLoad}
          style={{ padding: '0.75rem', background: '#10b981', color: 'white', border: 'none', borderRadius: '6px' }}
        >
          手动加载
        </button>
        
        <button 
          onClick={handleCreateBackup}
          style={{ padding: '0.75rem', background: '#f59e0b', color: 'white', border: 'none', borderRadius: '6px' }}
        >
          创建备份
        </button>
        
        <button 
          onClick={handleSimulateDecorationActions}
          style={{ padding: '0.75rem', background: '#8b5cf6', color: 'white', border: 'none', borderRadius: '6px' }}
        >
          模拟操作
        </button>
        
        <button 
          onClick={handleExportData}
          style={{ padding: '0.75rem', background: '#6b7280', color: 'white', border: 'none', borderRadius: '6px' }}
        >
          导出数据
        </button>
        
        <button 
          onClick={handleResetData}
          style={{ padding: '0.75rem', background: '#ef4444', color: 'white', border: 'none', borderRadius: '6px' }}
        >
          重置数据
        </button>
      </div>

      <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '2rem' }}>
        {/* 备份管理 */}
        <div>
          <h3>备份管理</h3>
          <div style={{ 
            background: 'white', 
            border: '1px solid #e5e7eb', 
            borderRadius: '8px', 
            padding: '1rem',
            maxHeight: '400px',
            overflowY: 'auto'
          }}>
            {backups.length === 0 ? (
              <p style={{ color: '#6b7280', textAlign: 'center' }}>暂无备份</p>
            ) : (
              backups.map(backup => (
                <div 
                  key={backup.id}
                  style={{ 
                    padding: '0.75rem', 
                    border: '1px solid #e5e7eb', 
                    borderRadius: '6px', 
                    marginBottom: '0.5rem',
                    background: '#f9fafb'
                  }}
                >
                  <div style={{ fontWeight: 'bold', marginBottom: '0.25rem' }}>
                    {backup.description}
                  </div>
                  <div style={{ fontSize: '0.875rem', color: '#6b7280', marginBottom: '0.5rem' }}>
                    {new Date(backup.timestamp).toLocaleString()} | {(backup.size / 1024).toFixed(1)} KB
                  </div>
                  <div style={{ display: 'flex', gap: '0.5rem' }}>
                    <button 
                      onClick={() => handleRestoreBackup(backup.id)}
                      style={{ 
                        padding: '0.25rem 0.75rem', 
                        background: '#10b981', 
                        color: 'white', 
                        border: 'none', 
                        borderRadius: '4px',
                        fontSize: '0.875rem'
                      }}
                    >
                      恢复
                    </button>
                    <button 
                      onClick={() => handleDeleteBackup(backup.id)}
                      style={{ 
                        padding: '0.25rem 0.75rem', 
                        background: '#ef4444', 
                        color: 'white', 
                        border: 'none', 
                        borderRadius: '4px',
                        fontSize: '0.875rem'
                      }}
                    >
                      删除
                    </button>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>

        {/* 操作日志 */}
        <div>
          <h3>操作日志</h3>
          <div style={{ 
            background: '#1f2937', 
            color: '#f9fafb', 
            padding: '1rem', 
            borderRadius: '8px', 
            fontFamily: 'monospace',
            fontSize: '0.875rem',
            maxHeight: '400px',
            overflowY: 'auto'
          }}>
            {logs.length === 0 ? (
              <div style={{ color: '#9ca3af' }}>暂无日志</div>
            ) : (
              logs.map((log, index) => (
                <div key={index} style={{ marginBottom: '0.25rem' }}>
                  {log}
                </div>
              ))
            )}
          </div>
        </div>
      </div>

      {/* 配置信息 */}
      <div style={{ marginTop: '2rem' }}>
        <h3>系统配置</h3>
        <div style={{ 
          background: 'white', 
          border: '1px solid #e5e7eb', 
          borderRadius: '8px', 
          padding: '1rem'
        }}>
          <pre style={{ margin: 0, fontSize: '0.875rem' }}>
            {JSON.stringify(integrationService.getConfig(), null, 2)}
          </pre>
        </div>
      </div>
    </div>
  )
}

export default DecorationStorageExample 