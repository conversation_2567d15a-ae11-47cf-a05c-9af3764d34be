import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { GameItem, Quality, ItemCategory, AgriculturalVariety, IndustrialVariety, EquipmentType, QUALITY_CONFIGS } from '../types/enhanced-items'
import { SynthesisSystem, SynthesisResult, FocusTimeState } from '../systems/SynthesisSystem'

// 背包状态接口
export interface InventoryState {
  items: GameItem[]
  maxSlots: number
  usedSlots: number
  selectedItems: GameItem[]
  searchFilter: string
  categoryFilter: ItemCategory | 'all'
  qualityFilter: Quality | 'all'
}

// 合成状态接口
export interface SynthesisState {
  isOpen: boolean
  selectedMaterials: GameItem[]
  lastResult: SynthesisResult | null
  isProcessing: boolean
  cooldownEndTime: number
}

// 装备状态接口
export interface EquipmentState {
  equippedItems: {
    head?: GameItem
    wrist?: GameItem
    desk?: GameItem
  }
  activeEffects: {
    focusBonus: number
    productionBonus: number
    qualityBonus: number
  }
}

// 交易状态接口
export interface TradingState {
  isMarketOpen: boolean
  listings: MarketListing[]
  myListings: MarketListing[]
  tradeHistory: TradeRecord[]
}

export interface MarketListing {
  id: string
  sellerId: string
  item: GameItem
  price: number
  quantity: number
  listedAt: number
  expiresAt: number
}

export interface TradeRecord {
  id: string
  buyerId: string
  sellerId: string
  item: GameItem
  price: number
  quantity: number
  timestamp: number
}

// 游戏统计接口
export interface GameStats {
  totalItemsObtained: number
  totalSynthesisAttempts: number
  successfulSynthesis: number
  totalFocusTime: number
  itemsDiscovered: Set<string>
  achievementsUnlocked: string[]
}

// 主要游戏状态接口
interface GameState {
  // 背包管理
  inventory: InventoryState
  addItem: (item: GameItem) => void
  removeItem: (itemId: string) => void
  selectItem: (item: GameItem) => void
  deselectItem: (itemId: string) => void
  clearSelection: () => void
  setInventoryFilter: (type: 'search' | 'category' | 'quality', value: string) => void
  
  // 合成管理
  synthesis: SynthesisState
  synthesisSystem: SynthesisSystem
  openSynthesis: () => void
  closeSynthesis: () => void
  addMaterial: (item: GameItem) => void
  removeMaterial: (itemId: string) => void
  clearMaterials: () => void
  performSynthesis: () => Promise<void>
  
  // 装备管理
  equipment: EquipmentState
  equipItem: (item: GameItem) => void
  unequipItem: (slot: 'head' | 'wrist' | 'desk') => void
  updateActiveEffects: () => void
  
  // 交易管理
  trading: TradingState
  openMarket: () => void
  closeMarket: () => void
  listItem: (item: GameItem, price: number, quantity: number) => void
  buyItem: (listing: MarketListing) => void
  cancelListing: (listingId: string) => void
  
  // 专注时间管理
  focusState: FocusTimeState
  updateFocusTime: (minutes: number) => void
  
  // 游戏统计
  stats: GameStats
  updateStats: (type: 'synthesis' | 'focus' | 'trade', data: any) => void
  
  // 工具函数
  getFilteredItems: () => GameItem[]
  getItemsByCategory: (category: ItemCategory) => GameItem[]
  getItemsByQuality: (quality: Quality) => GameItem[]
  canSynthesize: () => boolean
}

// 创建store
export const useGameStore = create<GameState>()(
  persist(
    (set, get) => ({
      // 初始状态
      inventory: {
        items: [],
        maxSlots: 50,
        usedSlots: 0,
        selectedItems: [],
        searchFilter: '',
        categoryFilter: 'all',
        qualityFilter: 'all'
      },
      
      synthesis: {
        isOpen: false,
        selectedMaterials: [],
        lastResult: null,
        isProcessing: false,
        cooldownEndTime: 0
      },
      
      synthesisSystem: new SynthesisSystem(),
      
      equipment: {
        equippedItems: {},
        activeEffects: {
          focusBonus: 0,
          productionBonus: 0,
          qualityBonus: 0
        }
      },
      
      trading: {
        isMarketOpen: false,
        listings: [],
        myListings: [],
        tradeHistory: []
      },
      
      focusState: {
        dailyFocusMinutes: 0,
        focusStreak: 0,
        lastFocusDate: ''
      },
      
      stats: {
        totalItemsObtained: 0,
        totalSynthesisAttempts: 0,
        successfulSynthesis: 0,
        totalFocusTime: 0,
        itemsDiscovered: new Set(),
        achievementsUnlocked: []
      },

      // 背包管理方法
      addItem: (item: GameItem) => {
        set((state) => {
          const newItems = [...state.inventory.items, item]
          return {
            inventory: {
              ...state.inventory,
              items: newItems,
              usedSlots: newItems.length
            },
            stats: {
              ...state.stats,
              totalItemsObtained: state.stats.totalItemsObtained + 1,
              itemsDiscovered: new Set([...state.stats.itemsDiscovered, item.name])
            }
          }
        })
      },

      removeItem: (itemId: string) => {
        set((state) => {
          const newItems = state.inventory.items.filter(item => item.id !== itemId)
          return {
            inventory: {
              ...state.inventory,
              items: newItems,
              usedSlots: newItems.length,
              selectedItems: state.inventory.selectedItems.filter(item => item.id !== itemId)
            }
          }
        })
      },

      selectItem: (item: GameItem) => {
        set((state) => ({
          inventory: {
            ...state.inventory,
            selectedItems: [...state.inventory.selectedItems, item]
          }
        }))
      },

      deselectItem: (itemId: string) => {
        set((state) => ({
          inventory: {
            ...state.inventory,
            selectedItems: state.inventory.selectedItems.filter(item => item.id !== itemId)
          }
        }))
      },

      clearSelection: () => {
        set((state) => ({
          inventory: {
            ...state.inventory,
            selectedItems: []
          }
        }))
      },

      setInventoryFilter: (type: 'search' | 'category' | 'quality', value: string) => {
        set((state) => ({
          inventory: {
            ...state.inventory,
            [type + 'Filter']: value
          }
        }))
      },

      // 合成管理方法
      openSynthesis: () => {
        set((state) => ({
          synthesis: {
            ...state.synthesis,
            isOpen: true
          }
        }))
      },

      closeSynthesis: () => {
        set((state) => ({
          synthesis: {
            ...state.synthesis,
            isOpen: false,
            selectedMaterials: []
          }
        }))
      },

      addMaterial: (item: GameItem) => {
        set((state) => {
          if (state.synthesis.selectedMaterials.length >= 2) {
            return state // 最多只能选择2个材料
          }
          
          return {
            synthesis: {
              ...state.synthesis,
              selectedMaterials: [...state.synthesis.selectedMaterials, item]
            }
          }
        })
      },

      removeMaterial: (itemId: string) => {
        set((state) => ({
          synthesis: {
            ...state.synthesis,
            selectedMaterials: state.synthesis.selectedMaterials.filter(item => item.id !== itemId)
          }
        }))
      },

      clearMaterials: () => {
        set((state) => ({
          synthesis: {
            ...state.synthesis,
            selectedMaterials: []
          }
        }))
      },

      performSynthesis: async () => {
        const state = get()
        
        if (!state.canSynthesize()) {
          return
        }

        if (Date.now() < state.synthesis.cooldownEndTime) {
          return // 还在冷却中
        }

        set((currentState) => ({
          synthesis: {
            ...currentState.synthesis,
            isProcessing: true
          }
        }))

        try {
          // 执行合成
          const result = state.synthesisSystem.synthesize(state.synthesis.selectedMaterials)
          
                   // 更新状态
         set((currentState) => {
           const updatedState: any = {
             synthesis: {
               ...currentState.synthesis,
               lastResult: result,
               isProcessing: false,
               cooldownEndTime: Date.now() + 5000, // 5秒冷却
               selectedMaterials: []
             },
             stats: {
               ...currentState.stats,
               totalSynthesisAttempts: currentState.stats.totalSynthesisAttempts + 1,
               successfulSynthesis: result.success 
                 ? currentState.stats.successfulSynthesis + 1 
                 : currentState.stats.successfulSynthesis
             }
           }

           // 如果合成成功，添加新道具并移除消耗的材料
           if (result.success && result.resultItem) {
             const newItems = [...currentState.inventory.items, result.resultItem]
             const itemsToRemove = result.consumedItems.map(item => item.id)
             const remainingItems = newItems.filter(item => !itemsToRemove.includes(item.id))
             
             updatedState.inventory = {
               ...currentState.inventory,
               items: remainingItems,
               usedSlots: remainingItems.length
             }
           }

           return updatedState
         })

        } catch (error) {
          set((currentState) => ({
            synthesis: {
              ...currentState.synthesis,
              isProcessing: false,
              lastResult: {
                success: false,
                consumedItems: [],
                successRate: 0,
                appliedBonuses: [],
                message: '合成过程中发生错误',
                timestamp: Date.now()
              }
            }
          }))
        }
      },

      // 装备管理方法
      equipItem: (item: GameItem) => {
        if (item.category !== ItemCategory.EQUIPMENT) return

        const equipItem = item as any
        const slot = equipItem.slot

        set((state) => {
          const newEquippedItems = { ...state.equipment.equippedItems }
          
          // 如果该槽位有装备，先卸下
          if (newEquippedItems[slot]) {
            const oldItem = newEquippedItems[slot]!
            ;(oldItem as any).isEquipped = false
          }
          
          // 装备新道具
          newEquippedItems[slot] = item
          equipItem.isEquipped = true
          
          const newState = {
            equipment: {
              ...state.equipment,
              equippedItems: newEquippedItems
            }
          }
          
          // 更新装备效果
          get().updateActiveEffects()
          
          return newState
        })
      },

      unequipItem: (slot: 'head' | 'wrist' | 'desk') => {
        set((state) => {
          const newEquippedItems = { ...state.equipment.equippedItems }
          
          if (newEquippedItems[slot]) {
            const item = newEquippedItems[slot]!
            ;(item as any).isEquipped = false
            delete newEquippedItems[slot]
          }
          
          const newState = {
            equipment: {
              ...state.equipment,
              equippedItems: newEquippedItems
            }
          }
          
          // 更新装备效果
          get().updateActiveEffects()
          
          return newState
        })
      },

      updateActiveEffects: () => {
        const state = get()
        let totalFocusBonus = 0
        let totalProductionBonus = 0
        let totalQualityBonus = 0

        Object.values(state.equipment.equippedItems).forEach(item => {
          if (item && item.category === ItemCategory.EQUIPMENT) {
            const equipItem = item as any
            totalFocusBonus += equipItem.attributes.focusBonus
            totalProductionBonus += equipItem.attributes.productionBonus
            totalQualityBonus += equipItem.attributes.qualityBonus
          }
        })

        set((currentState) => ({
          equipment: {
            ...currentState.equipment,
            activeEffects: {
              focusBonus: totalFocusBonus,
              productionBonus: totalProductionBonus,
              qualityBonus: totalQualityBonus
            }
          }
        }))
      },

      // 交易管理方法
      openMarket: () => {
        set((state) => ({
          trading: {
            ...state.trading,
            isMarketOpen: true
          }
        }))
      },

      closeMarket: () => {
        set((state) => ({
          trading: {
            ...state.trading,
            isMarketOpen: false
          }
        }))
      },

      listItem: (item: GameItem, price: number, quantity: number) => {
        const listing: MarketListing = {
          id: `listing_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          sellerId: 'player', // 简化实现，实际应该从用户系统获取
          item,
          price,
          quantity,
          listedAt: Date.now(),
          expiresAt: Date.now() + 7 * 24 * 60 * 60 * 1000 // 7天后过期
        }

        set((state) => ({
          trading: {
            ...state.trading,
            myListings: [...state.trading.myListings, listing],
            listings: [...state.trading.listings, listing]
          }
        }))
      },

      buyItem: (listing: MarketListing) => {
        const tradeRecord: TradeRecord = {
          id: `trade_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          buyerId: 'player',
          sellerId: listing.sellerId,
          item: listing.item,
          price: listing.price,
          quantity: listing.quantity,
          timestamp: Date.now()
        }

        set((state) => {
          // 添加道具到背包
          get().addItem(listing.item)
          
          return {
            trading: {
              ...state.trading,
              listings: state.trading.listings.filter(l => l.id !== listing.id),
              tradeHistory: [...state.trading.tradeHistory, tradeRecord]
            }
          }
        })
      },

      cancelListing: (listingId: string) => {
        set((state) => ({
          trading: {
            ...state.trading,
            listings: state.trading.listings.filter(l => l.id !== listingId),
            myListings: state.trading.myListings.filter(l => l.id !== listingId)
          }
        }))
      },

      // 专注时间管理
      updateFocusTime: (minutes: number) => {
        const state = get()
        state.synthesisSystem.updateFocusTime(minutes)
        
        set((currentState) => ({
          focusState: state.synthesisSystem.getFocusTimeState(),
          stats: {
            ...currentState.stats,
            totalFocusTime: currentState.stats.totalFocusTime + minutes
          }
        }))
      },

      // 统计更新
      updateStats: (type: 'synthesis' | 'focus' | 'trade', data: any) => {
        set((state) => {
          const newStats = { ...state.stats }
          
          switch (type) {
            case 'synthesis':
              newStats.totalSynthesisAttempts += 1
              if (data.success) {
                newStats.successfulSynthesis += 1
              }
              break
            case 'focus':
              newStats.totalFocusTime += data.minutes
              break
            case 'trade':
              // 处理交易统计
              break
          }
          
          return { stats: newStats }
        })
      },

      // 工具函数
      getFilteredItems: () => {
        const state = get()
        let items = state.inventory.items

        // 搜索过滤
        if (state.inventory.searchFilter) {
          items = items.filter(item => 
            item.name.toLowerCase().includes(state.inventory.searchFilter.toLowerCase()) ||
            item.description.toLowerCase().includes(state.inventory.searchFilter.toLowerCase())
          )
        }

        // 分类过滤
        if (state.inventory.categoryFilter !== 'all') {
          items = items.filter(item => item.category === state.inventory.categoryFilter)
        }

        // 品质过滤
        if (state.inventory.qualityFilter !== 'all') {
          items = items.filter(item => item.quality === state.inventory.qualityFilter)
        }

        return items
      },

      getItemsByCategory: (category: ItemCategory) => {
        return get().inventory.items.filter(item => item.category === category)
      },

      getItemsByQuality: (quality: Quality) => {
        return get().inventory.items.filter(item => item.quality === quality)
      },

      canSynthesize: () => {
        const state = get()
        return state.synthesis.selectedMaterials.length === 2 && 
               !state.synthesis.isProcessing &&
               Date.now() >= state.synthesis.cooldownEndTime
      }
    }),
    {
      name: 'game-storage',
      // 只持久化部分状态
      partialize: (state) => ({
        inventory: state.inventory,
        equipment: state.equipment,
        trading: {
          ...state.trading,
          isMarketOpen: false // 不持久化市场开启状态
        },
        focusState: state.focusState,
                 stats: {
           ...state.stats,
           itemsDiscovered: Array.from(state.stats.itemsDiscovered) as any // Set转换为Array进行序列化
         }
      }),
      // 反序列化时恢复Set
      onRehydrateStorage: () => (state) => {
        if (state?.stats?.itemsDiscovered) {
          state.stats.itemsDiscovered = new Set(state.stats.itemsDiscovered as string[])
        }
      }
    }
  )
) 