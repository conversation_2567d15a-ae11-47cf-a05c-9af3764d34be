import React from 'react'

export const SimpleLootboxDemo: React.FC = () => {
  return (
    <div style={{
      padding: '50px',
      fontSize: '24px',
      textAlign: 'center',
      backgroundColor: '#f0f8ff',
      minHeight: '100vh',
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'center',
      alignItems: 'center'
    }}>
      <h1 style={{ color: '#2c5530', marginBottom: '30px' }}>
        🎁 期货盲盒系统
      </h1>
      
      <div style={{
        fontSize: '48px',
        marginBottom: '30px'
      }}>
        📦🌾💰
      </div>
      
      <p style={{
        color: '#666',
        fontSize: '18px',
        maxWidth: '600px',
        lineHeight: '1.6',
        marginBottom: '30px'
      }}>
        这是期货游戏盲盒系统的简化演示。组件已成功加载，说明基础功能正常运行。
      </p>
      
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
        gap: '20px',
        width: '100%',
        maxWidth: '800px'
      }}>
        <div style={{
          padding: '20px',
          backgroundColor: 'white',
          borderRadius: '10px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
          textAlign: 'center'
        }}>
          <div style={{ fontSize: '32px', marginBottom: '10px' }}>🌾</div>
          <h3 style={{ color: '#4CAF50', marginBottom: '10px' }}>农业盒</h3>
          <p style={{ fontSize: '14px', color: '#666' }}>玉米、小麦、大豆等</p>
        </div>
        
        <div style={{
          padding: '20px',
          backgroundColor: 'white',
          borderRadius: '10px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
          textAlign: 'center'
        }}>
          <div style={{ fontSize: '32px', marginBottom: '10px' }}>🏭</div>
          <h3 style={{ color: '#2196F3', marginBottom: '10px' }}>工业盒</h3>
          <p style={{ fontSize: '14px', color: '#666' }}>铜、铝、黄金等</p>
        </div>
        
        <div style={{
          padding: '20px',
          backgroundColor: 'white',
          borderRadius: '10px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
          textAlign: 'center'
        }}>
          <div style={{ fontSize: '32px', marginBottom: '10px' }}>⚡</div>
          <h3 style={{ color: '#FF9800', marginBottom: '10px' }}>装备盒</h3>
          <p style={{ fontSize: '14px', color: '#666' }}>专注装备等</p>
        </div>
      </div>
      
      <div style={{
        marginTop: '40px',
        padding: '20px',
        backgroundColor: '#fff3e0',
        borderRadius: '10px',
        border: '2px solid #FF9800'
      }}>
        <p style={{ 
          color: '#E65100', 
          margin: 0,
          fontSize: '16px'
        }}>
          ✅ 组件加载成功 - 基础功能正常！
        </p>
      </div>
    </div>
  )
}

export default SimpleLootboxDemo 