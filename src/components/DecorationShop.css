/* ============ 装饰商店主容器 ============ */
.decoration-shop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: 20px;
}

.decoration-shop > div {
  background: linear-gradient(145deg, #ffffff, #f5f7fa);
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  max-width: 90vw;
  max-height: 90vh;
  width: 1200px;
  height: 800px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* ============ 加载状态 ============ */
.decoration-shop.loading {
  align-items: center;
  justify-content: center;
}

.loading-content {
  text-align: center;
  color: white;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid #4CAF50;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ============ 商店头部 ============ */
.shop-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 30px;
  border-bottom: 2px solid #e0e6ed;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.shop-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
}

.close-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  font-size: 24px;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

/* ============ 标签页 ============ */
.shop-tabs {
  display: flex;
  background: #f8f9fc;
  border-bottom: 1px solid #e0e6ed;
}

.tab {
  flex: 1;
  padding: 15px 20px;
  background: none;
  border: none;
  font-size: 16px;
  font-weight: 500;
  color: #64748b;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.tab:hover {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
}

.tab.active {
  color: #667eea;
  background: white;
}

.tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #667eea, #764ba2);
}

/* ============ 装饰道具标签页 ============ */
.decorations-tab {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.decorations-controls {
  display: flex;
  gap: 20px;
  padding: 20px 30px;
  background: #f8f9fc;
  border-bottom: 1px solid #e0e6ed;
}

.search-bar {
  flex: 1;
}

.search-input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e0e6ed;
  border-radius: 12px;
  font-size: 14px;
  background: white;
  transition: all 0.3s ease;
}

.search-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.filter-controls {
  display: flex;
  gap: 10px;
}

.sort-select {
  padding: 12px 16px;
  border: 2px solid #e0e6ed;
  border-radius: 12px;
  background: white;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.sort-select:focus {
  outline: none;
  border-color: #667eea;
}

/* ============ 分类按钮 ============ */
.categories {
  display: flex;
  gap: 10px;
  padding: 20px 30px;
  background: white;
  border-bottom: 1px solid #e0e6ed;
  overflow-x: auto;
}

.category-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background: #f1f5f9;
  border: 2px solid transparent;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 500;
  color: #64748b;
  cursor: pointer;
  white-space: nowrap;
  transition: all 0.3s ease;
  position: relative;
}

.category-btn:hover {
  background: #e2e8f0;
  transform: translateY(-2px);
}

.category-btn.active {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border-color: #667eea;
}

.category-btn.new::after {
  content: '';
  position: absolute;
  top: -5px;
  right: -5px;
  width: 8px;
  height: 8px;
  background: #ef4444;
  border-radius: 50%;
}

.category-btn.popular::before {
  content: '🔥';
  font-size: 12px;
}

.category-icon {
  font-size: 16px;
}

.new-badge, .popular-badge {
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 6px;
  color: white;
  font-weight: 600;
}

.new-badge {
  background: #ef4444;
}

.popular-badge {
  background: #f59e0b;
}

/* ============ 装饰道具网格 ============ */
.decorations-grid {
  flex: 1;
  padding: 20px 30px;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  overflow-y: auto;
}

.decoration-card {
  background: white;
  border-radius: 16px;
  border: 2px solid #e0e6ed;
  padding: 20px;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  height: fit-content;
}

.decoration-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.decoration-image {
  position: relative;
  margin-bottom: 16px;
}

.decoration-image img {
  width: 100%;
  height: 150px;
  object-fit: cover;
  border-radius: 12px;
  background: #f1f5f9;
}

.rarity-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 10px;
  font-weight: 600;
  color: white;
  text-transform: uppercase;
}

.decoration-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.decoration-name {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
}

.decoration-description {
  margin: 0 0 16px 0;
  font-size: 14px;
  color: #64748b;
  line-height: 1.5;
  flex: 1;
}

.decoration-stats {
  margin-bottom: 16px;
}

.stat {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #64748b;
}

.stat-value {
  font-size: 14px;
  font-weight: 600;
  color: #1e293b;
}

.effects {
  margin-top: 12px;
}

.effects-label {
  font-size: 12px;
  font-weight: 600;
  color: #667eea;
  margin-bottom: 4px;
  display: block;
}

.effects-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.effect-item {
  font-size: 12px;
  color: #64748b;
  margin-bottom: 2px;
  padding-left: 12px;
  position: relative;
}

.effect-item::before {
  content: '•';
  color: #667eea;
  position: absolute;
  left: 0;
}

.decoration-price {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
  padding-top: 16px;
  border-top: 1px solid #e0e6ed;
}

.price-info {
  display: flex;
  flex-direction: column;
}

.price-amount {
  font-size: 18px;
  font-weight: 700;
  color: #1e293b;
}

.price-currency {
  font-size: 12px;
  color: #64748b;
}

.purchase-btn {
  padding: 10px 20px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border: none;
  border-radius: 10px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.purchase-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(102, 126, 234, 0.3);
}

.purchase-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.unlock-requirement {
  margin-top: 8px;
  font-size: 12px;
  color: #f59e0b;
  text-align: center;
  font-weight: 500;
}

/* ============ 主题标签页 ============ */
.themes-tab {
  flex: 1;
  overflow: hidden;
}

.themes-grid {
  padding: 20px 30px;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
  height: 100%;
  overflow-y: auto;
}

.theme-card {
  background: white;
  border-radius: 16px;
  border: 2px solid #e0e6ed;
  overflow: hidden;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
}

.theme-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.theme-card.active {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.theme-preview {
  position: relative;
  height: 200px;
}

.theme-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.active-badge {
  position: absolute;
  top: 12px;
  left: 12px;
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  padding: 6px 12px;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 600;
}

.theme-info {
  padding: 20px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.theme-name {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
  color: #1e293b;
}

.theme-description {
  margin: 0 0 16px 0;
  font-size: 14px;
  color: #64748b;
  line-height: 1.5;
  flex: 1;
}

.theme-effects {
  margin-bottom: 20px;
}

.theme-actions {
  margin-top: auto;
}

.theme-purchase {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.theme-price {
  display: flex;
  flex-direction: column;
}

.price-free {
  font-size: 16px;
  font-weight: 600;
  color: #10b981;
}

.apply-btn {
  padding: 12px 24px;
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  border: none;
  border-radius: 10px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.apply-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(16, 185, 129, 0.3);
}

.active-indicator {
  color: #10b981;
  font-weight: 600;
  font-size: 14px;
  text-align: center;
}

/* ============ 已拥有标签页 ============ */
.owned-tab {
  flex: 1;
  overflow: hidden;
}

.owned-list {
  padding: 20px 30px;
  height: 100%;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.owned-item {
  display: flex;
  gap: 16px;
  background: white;
  border: 2px solid #e0e6ed;
  border-radius: 12px;
  padding: 16px;
  transition: all 0.3s ease;
}

.owned-item:hover {
  border-color: #667eea;
  transform: translateX(5px);
}

.owned-image {
  position: relative;
}

.owned-image img {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 8px;
  background: #f1f5f9;
}

.quantity-badge {
  position: absolute;
  top: -6px;
  right: -6px;
  background: #667eea;
  color: white;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  border: 2px solid white;
}

.owned-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.owned-name {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
}

.owned-description {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #64748b;
  flex: 1;
}

.owned-actions {
  margin-top: auto;
}

.use-btn {
  padding: 8px 16px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.use-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(102, 126, 234, 0.3);
}

/* ============ 空状态 ============ */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
  color: #64748b;
  grid-column: 1 / -1;
}

.empty-state p {
  font-size: 18px;
  margin-bottom: 20px;
}

.empty-state button {
  padding: 12px 24px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border: none;
  border-radius: 10px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.empty-state button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(102, 126, 234, 0.3);
}

/* ============ 响应式设计 ============ */
@media (max-width: 768px) {
  .decoration-shop > div {
    width: 95vw;
    height: 95vh;
    margin: 0;
  }

  .shop-header {
    padding: 15px 20px;
  }

  .shop-title {
    font-size: 20px;
  }

  .decorations-controls {
    flex-direction: column;
    gap: 15px;
    padding: 15px 20px;
  }

  .categories {
    padding: 15px 20px;
    gap: 8px;
  }

  .category-btn {
    padding: 8px 12px;
    font-size: 12px;
  }

  .decorations-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 15px;
    padding: 15px 20px;
  }

  .themes-grid {
    grid-template-columns: 1fr;
    padding: 15px 20px;
  }

  .owned-list {
    padding: 15px 20px;
  }

  .owned-item {
    flex-direction: column;
    text-align: center;
  }

  .owned-image {
    align-self: center;
  }
}

@media (max-width: 480px) {
  .decoration-shop {
    padding: 10px;
  }

  .decorations-grid {
    grid-template-columns: 1fr;
  }

  .decoration-card {
    padding: 15px;
  }

  .decoration-price {
    flex-direction: column;
    gap: 10px;
    text-align: center;
  }
} 