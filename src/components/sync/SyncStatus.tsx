import React, { useState, useEffect } from 'react';
import { SyncService, SyncState, DeviceInfo } from '../../services/SyncService';
import './SyncStatus.css';

interface SyncStatusProps {
  showDetails?: boolean;
  onSyncAction?: (action: string) => void;
}

export const SyncStatus: React.FC<SyncStatusProps> = ({ 
  showDetails = false, 
  onSyncAction 
}) => {
  const [syncState, setSyncState] = useState<SyncState | null>(null);
  const [deviceInfo, setDeviceInfo] = useState<DeviceInfo | null>(null);
  const [isExpanded, setIsExpanded] = useState(false);
  const [syncHistory, setSyncHistory] = useState<any[]>([]);
  const [isSyncing, setIsSyncing] = useState(false);

  useEffect(() => {
    const syncService = SyncService.getInstance();
    
    // 获取初始状态
    setSyncState(syncService.getSyncState());
    setDeviceInfo(syncService.getDeviceInfo());

    // 监听同步事件
    const handleConnectionEstablished = (data: any) => {
      setSyncState(syncService.getSyncState());
      addSyncHistory('连接建立', `通过 ${data.method} 建立连接`, 'success');
    };

    const handleConnectionLost = (data: any) => {
      setSyncState(syncService.getSyncState());
      addSyncHistory('连接丢失', `${data.method} 连接断开`, 'warning');
    };

    const handleDataUpdated = (data: any) => {
      setSyncState(syncService.getSyncState());
      addSyncHistory('数据更新', `收到 ${data.type} 类型的更新`, 'info');
    };

    const handleSyncError = (data: any) => {
      addSyncHistory('同步错误', data.error?.message || '未知错误', 'error');
    };

    const handleConflictDetected = (data: any) => {
      addSyncHistory('冲突检测', `检测到 ${data.type} 数据冲突`, 'warning');
      onSyncAction?.('conflict-detected');
    };

    // 注册事件监听
    syncService.on('connection-established', handleConnectionEstablished);
    syncService.on('connection-lost', handleConnectionLost);
    syncService.on('data-updated', handleDataUpdated);
    syncService.on('sync-error', handleSyncError);
    syncService.on('conflict-detected', handleConflictDetected);

    // 定期更新状态
    const statusInterval = setInterval(() => {
      setSyncState(syncService.getSyncState());
      setDeviceInfo(syncService.getDeviceInfo());
    }, 5000);

    return () => {
      clearInterval(statusInterval);
      syncService.off('connection-established', handleConnectionEstablished);
      syncService.off('connection-lost', handleConnectionLost);
      syncService.off('data-updated', handleDataUpdated);
      syncService.off('sync-error', handleSyncError);
      syncService.off('conflict-detected', handleConflictDetected);
    };
  }, [onSyncAction]);

  const addSyncHistory = (title: string, message: string, type: 'success' | 'warning' | 'error' | 'info') => {
    const newEntry = {
      id: Date.now(),
      timestamp: new Date().toLocaleTimeString(),
      title,
      message,
      type
    };
    setSyncHistory(prev => [newEntry, ...prev.slice(0, 9)]); // 保留最近10条
  };

  const handleForceSync = async () => {
    if (isSyncing) return;
    
    setIsSyncing(true);
    try {
      const syncService = SyncService.getInstance();
      await syncService.forceSync();
      addSyncHistory('手动同步', '强制同步完成', 'success');
      onSyncAction?.('force-sync-completed');
    } catch (error) {
      addSyncHistory('同步失败', '强制同步失败', 'error');
    } finally {
      setIsSyncing(false);
    }
  };

  const getConnectionIcon = () => {
    if (!syncState?.isConnected) return '🔴';
    switch (syncState.connectionMethod) {
      case 'electron': return '🖥️';
      case 'websocket': return '🌐';
      case 'http': return '📡';
      default: return '📱';
    }
  };

  const getConnectionStatus = () => {
    if (!syncState) return '初始化中...';
    if (!syncState.isConnected) return '离线模式';
    
    const methodNames = {
      electron: 'Electron同步',
      websocket: 'WebSocket实时',
      http: 'HTTP轮询',
      offline: '离线模式'
    };
    
    return methodNames[syncState.connectionMethod] || '未知连接';
  };

  const formatLastSyncTime = () => {
    if (!syncState?.lastSyncTime) return '从未同步';
    const diff = Date.now() - syncState.lastSyncTime;
    const minutes = Math.floor(diff / 60000);
    const seconds = Math.floor((diff % 60000) / 1000);
    
    if (minutes > 0) return `${minutes}分钟前`;
    return `${seconds}秒前`;
  };

  if (!showDetails) {
    // 简化显示模式
    return (
      <div className="sync-status-simple">
        <span className="sync-icon">{getConnectionIcon()}</span>
        <span className="sync-text">{getConnectionStatus()}</span>
        {syncState?.syncQueue && syncState.syncQueue.length > 0 && (
          <span className="sync-queue">({syncState.syncQueue.length})</span>
        )}
      </div>
    );
  }

  return (
    <div className="sync-status-panel">
      <div className="sync-header" onClick={() => setIsExpanded(!isExpanded)}>
        <div className="sync-main-info">
          <span className="sync-icon">{getConnectionIcon()}</span>
          <div className="sync-details">
            <div className="sync-status-text">{getConnectionStatus()}</div>
            <div className="sync-meta">
                             最后同步: {formatLastSyncTime()}
               {syncState?.syncQueue && syncState.syncQueue.length > 0 && (
                 <span className="queue-info"> | 队列: {syncState.syncQueue.length}</span>
               )}
            </div>
          </div>
        </div>
        <button 
          className={`sync-action-btn ${isSyncing ? 'syncing' : ''}`}
          onClick={(e) => {
            e.stopPropagation();
            handleForceSync();
          }}
          disabled={isSyncing}
        >
          {isSyncing ? '同步中...' : '强制同步'}
        </button>
      </div>

      {isExpanded && (
        <div className="sync-expanded-content">
          {/* 设备信息 */}
          <div className="sync-section">
            <h4>设备信息</h4>
            <div className="device-info">
              <div className="device-item">
                <span className="device-label">设备ID:</span>
                <span className="device-value">{deviceInfo?.id.slice(-8)}</span>
              </div>
              <div className="device-item">
                <span className="device-label">设备类型:</span>
                <span className="device-value">{deviceInfo?.type}</span>
              </div>
              <div className="device-item">
                <span className="device-label">平台:</span>
                <span className="device-value">{deviceInfo?.platform}</span>
              </div>
              <div className="device-item">
                <span className="device-label">在线状态:</span>
                <span className={`device-value ${deviceInfo?.isOnline ? 'online' : 'offline'}`}>
                  {deviceInfo?.isOnline ? '在线' : '离线'}
                </span>
              </div>
            </div>
          </div>

          {/* 功能支持 */}
          <div className="sync-section">
            <h4>支持功能</h4>
            <div className="capabilities">
              {deviceInfo?.capabilities.map(cap => (
                <span key={cap} className="capability-tag">{cap}</span>
              ))}
            </div>
          </div>

          {/* 同步配置 */}
          <div className="sync-section">
            <h4>同步配置</h4>
            <div className="sync-config">
              <div className="config-item">
                <span className="config-label">冲突解决:</span>
                <select 
                  value={syncState?.conflictResolution || 'latest'}
                  onChange={(e) => {
                    // 这里可以添加配置更新逻辑
                    onSyncAction?.(`set-conflict-resolution-${e.target.value}`);
                  }}
                >
                  <option value="latest">最新优先</option>
                  <option value="desktop-priority">桌面端优先</option>
                  <option value="manual">手动解决</option>
                </select>
              </div>
            </div>
          </div>

          {/* 同步历史 */}
          <div className="sync-section">
            <h4>同步历史</h4>
            <div className="sync-history">
              {syncHistory.length === 0 ? (
                <div className="no-history">暂无同步记录</div>
              ) : (
                syncHistory.map(entry => (
                  <div key={entry.id} className={`history-item ${entry.type}`}>
                    <div className="history-time">{entry.timestamp}</div>
                    <div className="history-content">
                      <div className="history-title">{entry.title}</div>
                      <div className="history-message">{entry.message}</div>
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>

          {/* 调试信息 */}
          {process.env.NODE_ENV === 'development' && (
            <div className="sync-section">
              <h4>调试信息</h4>
              <div className="debug-info">
                <pre>{JSON.stringify(syncState, null, 2)}</pre>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}; 