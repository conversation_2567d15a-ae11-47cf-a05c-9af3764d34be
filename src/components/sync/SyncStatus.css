/* 同步状态组件样式 */

/* 简化显示模式 */
.sync-status-simple {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  font-size: 12px;
  color: #fff;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.sync-status-simple .sync-icon {
  font-size: 14px;
}

.sync-status-simple .sync-text {
  font-weight: 500;
}

.sync-status-simple .sync-queue {
  color: #ffd700;
  font-weight: 600;
}

/* 详细面板模式 */
.sync-status-panel {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  color: white;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  overflow: hidden;
  max-width: 500px;
  margin: 0 auto;
}

.sync-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  cursor: pointer;
  transition: background 0.2s ease;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.sync-header:hover {
  background: rgba(255, 255, 255, 0.15);
}

.sync-main-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.sync-main-info .sync-icon {
  font-size: 20px;
}

.sync-details {
  flex: 1;
}

.sync-status-text {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 4px;
}

.sync-meta {
  font-size: 12px;
  opacity: 0.8;
  display: flex;
  align-items: center;
}

.queue-info {
  color: #ffd700;
  font-weight: 600;
}

.sync-action-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.sync-action-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

.sync-action-btn:disabled,
.sync-action-btn.syncing {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* 展开内容 */
.sync-expanded-content {
  padding: 0 20px 20px;
  background: rgba(0, 0, 0, 0.1);
}

.sync-section {
  margin-bottom: 20px;
}

.sync-section h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #ffd700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* 设备信息 */
.device-info {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.device-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  font-size: 12px;
}

.device-label {
  opacity: 0.8;
}

.device-value {
  font-weight: 600;
}

.device-value.online {
  color: #4ade80;
}

.device-value.offline {
  color: #f87171;
}

/* 功能支持 */
.capabilities {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.capability-tag {
  background: rgba(255, 255, 255, 0.2);
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* 同步配置 */
.sync-config {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.config-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  font-size: 12px;
}

.config-label {
  opacity: 0.8;
}

.config-item select {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
}

.config-item select option {
  background: #333;
  color: white;
}

/* 同步历史 */
.sync-history {
  max-height: 200px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.no-history {
  text-align: center;
  padding: 20px;
  opacity: 0.6;
  font-size: 12px;
}

.history-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  font-size: 11px;
  border-left: 3px solid transparent;
}

.history-item.success {
  border-left-color: #4ade80;
}

.history-item.warning {
  border-left-color: #fbbf24;
}

.history-item.error {
  border-left-color: #f87171;
}

.history-item.info {
  border-left-color: #60a5fa;
}

.history-time {
  font-weight: 600;
  opacity: 0.8;
  min-width: 60px;
}

.history-content {
  flex: 1;
}

.history-title {
  font-weight: 600;
  margin-bottom: 2px;
}

.history-message {
  opacity: 0.8;
  line-height: 1.3;
}

/* 调试信息 */
.debug-info {
  background: rgba(0, 0, 0, 0.3);
  padding: 12px;
  border-radius: 8px;
  max-height: 200px;
  overflow-y: auto;
}

.debug-info pre {
  margin: 0;
  font-size: 10px;
  color: #a3a3a3;
  line-height: 1.4;
}

/* 滚动条样式 */
.sync-history::-webkit-scrollbar,
.debug-info::-webkit-scrollbar {
  width: 4px;
}

.sync-history::-webkit-scrollbar-track,
.debug-info::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
}

.sync-history::-webkit-scrollbar-thumb,
.debug-info::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .sync-status-panel {
    max-width: 100%;
    margin: 0;
    border-radius: 0;
  }

  .sync-header {
    padding: 12px 16px;
  }

  .sync-expanded-content {
    padding: 0 16px 16px;
  }

  .device-info {
    grid-template-columns: 1fr;
  }

  .config-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .capabilities {
    justify-content: center;
  }
}

/* 动画效果 */
@keyframes syncPulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
}

.sync-action-btn.syncing {
  animation: syncPulse 1.5s ease-in-out infinite;
}

.sync-expanded-content {
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
} 