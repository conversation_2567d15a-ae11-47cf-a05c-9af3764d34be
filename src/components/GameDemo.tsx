import React, { useEffect, useState } from 'react'
import { useGameStore } from '../stores/gameStore'
import { ItemCard } from './ItemCard'
import { SynthesisPanel } from './SynthesisPanel'
import { ItemFactory, createDemoInventory } from '../utils/ItemFactory'
import { GameItem, Quality, ItemCategory, QUALITY_CONFIGS } from '../types/enhanced-items'

export const GameDemo: React.FC = () => {
  const {
    inventory,
    synthesis,
    equipment,
    addItem,
    openSynthesis,
    closeSynthesis,
    addMaterial,
    updateFocusTime,
    getFilteredItems,
    setInventoryFilter,
    stats,
    focusState
  } = useGameStore()

  const [selectedTab, setSelectedTab] = useState<'inventory' | 'synthesis' | 'equipment' | 'stats'>('inventory')

  // 初始化演示数据
  useEffect(() => {
    if (inventory.items.length === 0) {
      const demoItems = createDemoInventory()
      demoItems.forEach(item => addItem(item))
    }
  }, [inventory.items.length, addItem])

  const handleAddRandomItem = () => {
    const randomItem = ItemFactory.createRandomItem()
    addItem(randomItem)
  }

  const handleAddFocusTime = (minutes: number) => {
    updateFocusTime(minutes)
  }

  const handleItemClick = (item: GameItem) => {
    if (synthesis.isOpen && synthesis.selectedMaterials.length < 2) {
      addMaterial(item)
    }
  }

  const renderInventoryTab = () => (
    <div className="space-y-4">
      {/* 筛选器 */}
      <div className="bg-white rounded-lg p-4 shadow">
        <h3 className="font-semibold mb-3">筛选器</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* 搜索 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">搜索</label>
            <input
              type="text"
              placeholder="搜索道具..."
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              value={inventory.searchFilter}
              onChange={(e) => setInventoryFilter('search', e.target.value)}
            />
          </div>

          {/* 分类筛选 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">分类</label>
            <select
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              value={inventory.categoryFilter}
              onChange={(e) => setInventoryFilter('category', e.target.value)}
            >
              <option value="all">全部分类</option>
              <option value={ItemCategory.AGRICULTURAL}>农业产品</option>
              <option value={ItemCategory.INDUSTRIAL}>工业产品</option>
              <option value={ItemCategory.EQUIPMENT}>装备</option>
            </select>
          </div>

          {/* 品质筛选 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">品质</label>
            <select
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              value={inventory.qualityFilter}
              onChange={(e) => setInventoryFilter('quality', e.target.value)}
            >
              <option value="all">全部品质</option>
              {Object.entries(QUALITY_CONFIGS).map(([quality, config]) => (
                <option key={quality} value={quality}>{config.name}</option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* 道具网格 */}
      <div className="bg-white rounded-lg p-4 shadow">
        <div className="flex items-center justify-between mb-4">
          <h3 className="font-semibold">
            背包 ({getFilteredItems().length}/{inventory.maxSlots})
          </h3>
          <button
            onClick={handleAddRandomItem}
            className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
          >
            添加随机道具
          </button>
        </div>

        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
          {getFilteredItems().map((item) => (
            <ItemCard
              key={item.id}
              item={item}
              showDetails={true}
              showTooltip={true}
              onClick={handleItemClick}
              className="w-full"
            />
          ))}
        </div>
      </div>
    </div>
  )

  const renderSynthesisTab = () => (
    <div className="space-y-4">
      {/* 合成控制 */}
      <div className="bg-white rounded-lg p-4 shadow">
        <div className="flex items-center justify-between mb-4">
          <h3 className="font-semibold">合成系统</h3>
          <button
            onClick={openSynthesis}
            className="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"
          >
            打开合成界面
          </button>
        </div>
        
        <div className="text-sm text-gray-600">
          <p>• 点击背包中的道具将其添加为合成材料</p>
          <p>• 相同品质的同类道具可以合成为更高品质</p>
          <p>• 专注时间越长，合成成功率越高</p>
        </div>
      </div>

      {/* 专注时间控制 */}
      <div className="bg-white rounded-lg p-4 shadow">
        <h3 className="font-semibold mb-4">专注时间模拟</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <button
            onClick={() => handleAddFocusTime(15)}
            className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
          >
            +15分钟
          </button>
          <button
            onClick={() => handleAddFocusTime(30)}
            className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
          >
            +30分钟
          </button>
          <button
            onClick={() => handleAddFocusTime(60)}
            className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
          >
            +60分钟
          </button>
          <button
            onClick={() => handleAddFocusTime(120)}
            className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
          >
            +120分钟
          </button>
        </div>
        
        <div className="mt-4 p-3 bg-blue-50 rounded-lg">
          <div className="text-sm text-blue-800">
            <div>今日专注: {focusState.dailyFocusMinutes} 分钟</div>
            <div>连续专注: {focusState.focusStreak} 天</div>
          </div>
        </div>
      </div>

      {/* 最近合成结果 */}
      {synthesis.lastResult && (
        <div className="bg-white rounded-lg p-4 shadow">
          <h3 className="font-semibold mb-4">最近合成结果</h3>
          <div 
            className={`p-4 rounded-lg border ${
              synthesis.lastResult.success 
                ? 'bg-green-50 border-green-200 text-green-800' 
                : 'bg-red-50 border-red-200 text-red-800'
            }`}
          >
            <div className="font-semibold mb-2">
              {synthesis.lastResult.success ? '🎉 合成成功' : '💥 合成失败'}
            </div>
            <div className="text-sm">{synthesis.lastResult.message}</div>
            <div className="text-xs mt-2">
              成功率: {synthesis.lastResult.successRate}%
            </div>
            {synthesis.lastResult.appliedBonuses.length > 0 && (
              <div className="text-xs mt-1">
                加成: {synthesis.lastResult.appliedBonuses.join(', ')}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  )

  const renderEquipmentTab = () => (
    <div className="space-y-4">
      <div className="bg-white rounded-lg p-4 shadow">
        <h3 className="font-semibold mb-4">装备槽位</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          {/* 头部装备 */}
          <div className="text-center">
            <h4 className="font-medium text-gray-700 mb-2">头部装备</h4>
            <div className="w-24 h-24 mx-auto border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center">
              {equipment.equippedItems.head ? (
                <div className="text-center">
                  <div className="text-2xl">{equipment.equippedItems.head.icon}</div>
                  <div className="text-xs">{equipment.equippedItems.head.name}</div>
                </div>
              ) : (
                <div className="text-xs text-gray-500">未装备</div>
              )}
            </div>
          </div>

          {/* 手腕装备 */}
          <div className="text-center">
            <h4 className="font-medium text-gray-700 mb-2">手腕装备</h4>
            <div className="w-24 h-24 mx-auto border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center">
              {equipment.equippedItems.wrist ? (
                <div className="text-center">
                  <div className="text-2xl">{equipment.equippedItems.wrist.icon}</div>
                  <div className="text-xs">{equipment.equippedItems.wrist.name}</div>
                </div>
              ) : (
                <div className="text-xs text-gray-500">未装备</div>
              )}
            </div>
          </div>

          {/* 桌面装备 */}
          <div className="text-center">
            <h4 className="font-medium text-gray-700 mb-2">桌面装备</h4>
            <div className="w-24 h-24 mx-auto border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center">
              {equipment.equippedItems.desk ? (
                <div className="text-center">
                  <div className="text-2xl">{equipment.equippedItems.desk.icon}</div>
                  <div className="text-xs">{equipment.equippedItems.desk.name}</div>
                </div>
              ) : (
                <div className="text-xs text-gray-500">未装备</div>
              )}
            </div>
          </div>
        </div>

        {/* 装备效果 */}
        <div className="bg-gray-50 rounded-lg p-4">
          <h4 className="font-medium text-gray-700 mb-2">当前装备效果</h4>
          <div className="grid grid-cols-3 gap-4 text-center">
            <div>
              <div className="text-lg font-bold text-blue-600">
                +{equipment.activeEffects.focusBonus}%
              </div>
              <div className="text-xs text-gray-600">专注力</div>
            </div>
            <div>
              <div className="text-lg font-bold text-green-600">
                +{equipment.activeEffects.productionBonus}%
              </div>
              <div className="text-xs text-gray-600">生产力</div>
            </div>
            <div>
              <div className="text-lg font-bold text-purple-600">
                +{equipment.activeEffects.qualityBonus}%
              </div>
              <div className="text-xs text-gray-600">品质</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )

  const renderStatsTab = () => (
    <div className="space-y-4">
      <div className="bg-white rounded-lg p-4 shadow">
        <h3 className="font-semibold mb-4">游戏统计</h3>
        
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center p-4 bg-blue-50 rounded-lg">
            <div className="text-2xl font-bold text-blue-600">{stats.totalItemsObtained}</div>
            <div className="text-sm text-gray-600">获得道具</div>
          </div>
          
          <div className="text-center p-4 bg-green-50 rounded-lg">
            <div className="text-2xl font-bold text-green-600">{stats.totalSynthesisAttempts}</div>
            <div className="text-sm text-gray-600">合成尝试</div>
          </div>
          
          <div className="text-center p-4 bg-purple-50 rounded-lg">
            <div className="text-2xl font-bold text-purple-600">{stats.successfulSynthesis}</div>
            <div className="text-sm text-gray-600">成功合成</div>
          </div>
          
          <div className="text-center p-4 bg-orange-50 rounded-lg">
            <div className="text-2xl font-bold text-orange-600">
              {stats.totalSynthesisAttempts > 0 
                ? Math.round((stats.successfulSynthesis / stats.totalSynthesisAttempts) * 100) 
                : 0}%
            </div>
            <div className="text-sm text-gray-600">成功率</div>
          </div>
        </div>

        <div className="mt-6">
          <h4 className="font-medium text-gray-700 mb-2">
            已发现道具 ({stats.itemsDiscovered.size}种)
          </h4>
          <div className="text-sm text-gray-600">
            {Array.from(stats.itemsDiscovered).join(', ') || '暂无'}
          </div>
        </div>

        <div className="mt-4">
          <h4 className="font-medium text-gray-700 mb-2">专注统计</h4>
          <div className="grid grid-cols-2 gap-4">
            <div className="text-center p-3 bg-blue-50 rounded-lg">
              <div className="text-lg font-bold text-blue-600">{stats.totalFocusTime}</div>
              <div className="text-sm text-gray-600">总专注时间(分钟)</div>
            </div>
            <div className="text-center p-3 bg-green-50 rounded-lg">
              <div className="text-lg font-bold text-green-600">{focusState.focusStreak}</div>
              <div className="text-sm text-gray-600">连续专注天数</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )

  return (
    <div className="min-h-screen bg-gray-100 p-4">
      <div className="max-w-7xl mx-auto">
        {/* 标题 */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-800 mb-2">期货游戏道具系统演示</h1>
          <p className="text-gray-600">
            基于中国期货品种的农业产品、工业产品和装备类道具系统
          </p>
        </div>

        {/* 标签页导航 */}
        <div className="bg-white rounded-lg shadow mb-6">
          <div className="flex border-b">
            <button
              onClick={() => setSelectedTab('inventory')}
              className={`px-6 py-3 font-medium transition-colors ${
                selectedTab === 'inventory'
                  ? 'text-blue-600 border-b-2 border-blue-600 bg-blue-50'
                  : 'text-gray-600 hover:text-gray-800'
              }`}
            >
              背包系统
            </button>
            <button
              onClick={() => setSelectedTab('synthesis')}
              className={`px-6 py-3 font-medium transition-colors ${
                selectedTab === 'synthesis'
                  ? 'text-blue-600 border-b-2 border-blue-600 bg-blue-50'
                  : 'text-gray-600 hover:text-gray-800'
              }`}
            >
              合成系统
            </button>
            <button
              onClick={() => setSelectedTab('equipment')}
              className={`px-6 py-3 font-medium transition-colors ${
                selectedTab === 'equipment'
                  ? 'text-blue-600 border-b-2 border-blue-600 bg-blue-50'
                  : 'text-gray-600 hover:text-gray-800'
              }`}
            >
              装备系统
            </button>
            <button
              onClick={() => setSelectedTab('stats')}
              className={`px-6 py-3 font-medium transition-colors ${
                selectedTab === 'stats'
                  ? 'text-blue-600 border-b-2 border-blue-600 bg-blue-50'
                  : 'text-gray-600 hover:text-gray-800'
              }`}
            >
              游戏统计
            </button>
          </div>
        </div>

        {/* 标签页内容 */}
        {selectedTab === 'inventory' && renderInventoryTab()}
        {selectedTab === 'synthesis' && renderSynthesisTab()}
        {selectedTab === 'equipment' && renderEquipmentTab()}
        {selectedTab === 'stats' && renderStatsTab()}

        {/* 合成面板 */}
        <SynthesisPanel 
          isOpen={synthesis.isOpen} 
          onClose={closeSynthesis} 
        />
      </div>
    </div>
  )
} 