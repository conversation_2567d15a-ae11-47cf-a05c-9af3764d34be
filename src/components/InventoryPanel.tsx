import React, { useState, useEffect } from 'react';
import { InventorySystem } from '../systems/InventorySystem';
import { InventoryItem, InventoryState, RARITY_NAMES, RARITY_LEVELS } from '../types/inventory';
import { SynthesisRecipe, SynthesisResult } from '../types/inventory';
import { ItemRarity, ItemCategory, RARITY_COLORS } from '../types/lootbox';
import { SynthesisWorkbench } from './SynthesisWorkbench';
import InventoryTooltip from './InventoryTooltip';

interface InventoryPanelProps {
  inventorySystem: InventorySystem;
  onClose: () => void;
}

export const InventoryPanel: React.FC<InventoryPanelProps> = ({ inventorySystem, onClose }) => {
  console.log('InventoryPanel组件开始渲染');
  console.log('当前库存状态:', inventorySystem.getState());
  console.log('库存物品数量:', inventorySystem.getState().items.length);
  console.log('库存物品详情:', inventorySystem.getState().items);
  const [inventoryState, setInventoryState] = useState<InventoryState>(inventorySystem.getState());
  const [selectedTab, setSelectedTab] = useState<'inventory' | 'synthesis' | 'workbench'>('inventory');
  const [selectedRecipe, setSelectedRecipe] = useState<SynthesisRecipe | null>(null);
  const [synthesisResult, setSynthesisResult] = useState<SynthesisResult | null>(null);

  const [showWorkbench, setShowWorkbench] = useState(false);

  useEffect(() => {
    const unsubscribe = inventorySystem.subscribe(setInventoryState);
    return unsubscribe;
  }, [inventorySystem]);

  // 获取可用的合成配方
  const availableRecipes = inventorySystem.getAvailableRecipes();

  // 执行合成
  const handleSynthesize = (recipeId: string) => {
    const result = inventorySystem.synthesize(recipeId);
    setSynthesisResult(result);
    setTimeout(() => setSynthesisResult(null), 3000);
  };

  // 批量合成函数
  const handleBatchSynthesize = () => {
    console.log('批量合成函数被调用');
    let synthesizedCount = 0;
    
    // 找到所有可用的配方并尝试合成
    availableRecipes.forEach(recipe => {
      const result = inventorySystem.synthesize(recipe.id);
      if (result.success) {
        synthesizedCount++;
      }
    });
    
    if (synthesizedCount > 0) {
      setSynthesisResult({
        success: true,
        consumedItems: [],
        message: `批量合成成功！完成了 ${synthesizedCount} 次合成`
      });
    } else {
      setSynthesisResult({
        success: false,
        consumedItems: [],
        message: '没有可用的合成配方'
      });
    }
    
    setTimeout(() => setSynthesisResult(null), 3000);
  };



  // 渲染合成配方
  const renderSynthesisTab = () => (
    <div className="synthesis-tab space-y-4">
      <div className="text-center mb-4">
        <h3 className="text-lg font-bold text-blue-700">物品合成</h3>
        <p className="text-sm text-gray-600">使用相同或特定品质的物品进行合成升级</p>
      </div>

      {availableRecipes.length === 0 ? (
        <div className="text-center py-8 text-gray-500">
          <div className="text-4xl mb-2">🔧</div>
          <p>暂无可用的合成配方</p>
          <p className="text-xs">收集更多物品来解锁合成功能</p>
        </div>
      ) : (
        <div className="recipes-list space-y-3">
          {availableRecipes.map(recipe => (
            <div 
              key={recipe.id} 
              className="recipe-card p-3 border border-gray-300 rounded hover:border-blue-400 cursor-pointer transition-colors"
              onClick={() => setSelectedRecipe(recipe)}
            >
              <div className="flex justify-between items-start">
                <div className="flex-1">
                  <h4 className="font-bold text-sm">{recipe.name}</h4>
                  <p className="text-xs text-gray-600 mb-2">{recipe.description}</p>
                  
                  <div className="requirements text-xs">
                    <span className="font-medium">需要材料：</span>
                    {recipe.requiredItems.map((req, index) => (
                      <span key={index} className="ml-1">
                        {index > 0 && ' + '}
                        <span style={{ color: RARITY_COLORS[req.rarity] }}>
                          {req.quantity}个{RARITY_NAMES[req.rarity]}
                          {req.category && `(${req.category === ItemCategory.AGRICULTURAL ? '农产品' : '工业品'})`}
                        </span>
                      </span>
                    ))}
                  </div>
                  
                  <div className="result text-xs mt-1">
                    <span className="font-medium">产出：</span>
                    <span style={{ color: RARITY_COLORS[recipe.resultRarity] }}>
                      1个{RARITY_NAMES[recipe.resultRarity]}物品
                    </span>
                    <span className="text-gray-500 ml-2">
                      (成功率: {(recipe.successRate * 100).toFixed(0)}%)
                    </span>
                  </div>
                </div>
                
                <button 
                  className="synthesize-btn px-3 py-1 bg-blue-500 text-white text-xs rounded hover:bg-blue-600 transition-colors"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleSynthesize(recipe.id);
                  }}
                >
                  合成
                </button>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* 合成结果弹窗 */}
      {synthesisResult && (
        <div 
          className="synthesis-result-modal fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center"
          style={{ zIndex: 15200 }}
        >
          <div className="bg-white p-6 rounded-lg max-w-sm mx-4">
            <div className="text-center">
              <div className="text-4xl mb-2">
                {synthesisResult.success ? '✨' : '💥'}
              </div>
              <h3 className={`text-lg font-bold mb-2 ${synthesisResult.success ? 'text-green-600' : 'text-red-600'}`}>
                {synthesisResult.message}
              </h3>
              
              {synthesisResult.success && synthesisResult.resultItem && (
                <div className="result-item mb-3">
                  <div className="text-3xl mb-1">{synthesisResult.resultItem.icon}</div>
                  <div className="font-medium">{synthesisResult.resultItem.name}</div>
                  <div 
                    className="text-sm font-bold"
                    style={{ color: RARITY_COLORS[synthesisResult.resultItem.rarity] }}
                  >
                    {RARITY_NAMES[synthesisResult.resultItem.rarity]}
                  </div>
                </div>
              )}
              
              <div className="consumed-items text-xs text-gray-600 mb-4">
                <span className="font-medium">消耗材料：</span>
                {synthesisResult.consumedItems.map((item, index) => (
                  <div key={index}>
                    {item.icon} {item.name} ×{item.quantity}
                  </div>
                ))}
              </div>
              
              <button 
                className="close-btn px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors"
                onClick={() => setSynthesisResult(null)}
              >
                确定
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );

  return (
    <div 
      className="magical-inventory-modal" 
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        zIndex: 15000,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        background: 'rgba(0, 0, 0, 0.7)',
        backdropFilter: 'blur(5px)',
        padding: '20px'
      }}
      onClick={(e) => {
        // 点击背景关闭窗口
        if (e.target === e.currentTarget) {
          onClose();
        }
      }}
    >
      <div 
        className="magical-inventory-window" 
        style={{
          width: '90vw',
          maxWidth: '1200px',
          height: '85vh',
          maxHeight: '800px',
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          borderRadius: '16px',
          position: 'relative',
          display: 'flex',
          flexDirection: 'column',
          overflow: 'hidden',
          boxShadow: '0 25px 50px rgba(0, 0, 0, 0.5), 0 0 0 1px rgba(255, 255, 255, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.2)',
          animation: 'fadeInScale 0.3s ease-out'
        }}
        onClick={(e) => e.stopPropagation()}
      >
        {/* 魔法背景效果 */}
        <div className="absolute inset-0 opacity-20 pointer-events-none">
          <div className="magical-particles"></div>
          <div className="floating-orbs"></div>
        </div>
        
        {/* 头部 */}
        <div className="magical-header relative z-10 p-4 text-center border-b border-white border-opacity-20 flex-shrink-0">
          <h2 className="text-2xl font-bold text-white mb-1 drop-shadow-lg">
            ✨ 魔法物品背包 ✨
          </h2>
          <p className="text-white opacity-80 text-sm">
            {inventoryState.usedSlots}/{inventoryState.maxSlots} 槽位已使用
          </p>
          
          {/* 关闭按钮 */}
          <button
            onClick={(e) => {
              e.stopPropagation();
              onClose();
            }}
            className="absolute top-3 right-4 text-white hover:text-red-300 text-2xl font-bold transition-colors transform hover:scale-110 hover:rotate-90 duration-300"
            style={{ 
              width: '32px', 
              height: '32px', 
              display: 'flex', 
              alignItems: 'center', 
              justifyContent: 'center',
              zIndex: 10
            }}
          >
            ×
          </button>
        </div>

        {/* 标签切换和功能按钮 */}
        <div className="magical-tabs relative z-10 flex flex-col items-center gap-3 p-4 border-b border-white border-opacity-20 flex-shrink-0">
          <div className="tab-buttons flex bg-black bg-opacity-30 rounded-full p-1">
            <button 
              className={`px-3 py-2 text-xs rounded-full transition-all ${
                selectedTab === 'inventory' 
                  ? 'bg-white text-purple-600 font-bold shadow-lg' 
                  : 'text-white hover:bg-white hover:bg-opacity-20'
              }`}
              onClick={(e) => {
                e.stopPropagation();
                setSelectedTab('inventory');
              }}
            >
              📦 物品背包
            </button>
            <button 
              className={`px-3 py-2 text-xs rounded-full transition-all ${
                selectedTab === 'synthesis' 
                  ? 'bg-white text-purple-600 font-bold shadow-lg' 
                  : 'text-white hover:bg-white hover:bg-opacity-20'
              }`}
              onClick={(e) => {
                e.stopPropagation();
                setSelectedTab('synthesis');
              }}
            >
              ⚗️ 传统合成 {availableRecipes.length > 0 && `(${availableRecipes.length})`}
            </button>
          </div>
          
          {/* 功能按钮 */}
          <div className="action-buttons flex flex-wrap gap-2 justify-center">
            <button 
              className="magical-btn px-3 py-1 bg-gradient-to-r from-yellow-400 to-orange-500 text-white font-bold text-xs rounded-lg shadow-lg hover:from-yellow-500 hover:to-orange-600 transform hover:scale-105 transition-all"
              onClick={(e) => {
                e.stopPropagation();
                console.log('拖拽工作台按钮被点击');
                setShowWorkbench(true);
              }}
            >
              🧪 拖拽工作台
            </button>
            
            <button 
              className="magical-btn px-3 py-1 bg-gradient-to-r from-green-400 to-cyan-500 text-white font-bold text-xs rounded-lg shadow-lg hover:from-green-500 hover:to-cyan-600 transform hover:scale-105 transition-all"
              onClick={(e) => {
                e.stopPropagation();
                console.log('批量合成按钮被点击');
                handleBatchSynthesize();
              }}
              disabled={availableRecipes.length === 0}
            >
              ⚡ 批量合成
            </button>
            
            <button 
              className="magical-btn px-3 py-1 bg-gradient-to-r from-indigo-400 to-purple-500 text-white font-bold text-xs rounded-lg shadow-lg hover:from-indigo-500 hover:to-purple-600 transform hover:scale-105 transition-all"
              onClick={(e) => {
                e.stopPropagation();
                console.log('整理背包按钮被点击');
                // 整理功能暂时留空
                alert('整理背包功能即将推出！');
              }}
            >
              📋 整理背包
            </button>
          </div>
        </div>

        {/* 内容区域 */}
        <div className="magical-content flex-1 p-4 overflow-y-auto relative z-10">
          <div className="content-wrapper bg-white bg-opacity-95 rounded-xl p-4 shadow-xl backdrop-blur-sm h-full">
            {selectedTab === 'inventory' ? (
              <div className="h-full flex flex-col">
                {/* 背包信息 */}
                <div className="inventory-info mb-4 text-center">
                  <p className="text-sm text-gray-600 font-medium">
                    🎒 物品展示：每个物品单独显示，鼠标悬停查看详情
                  </p>
                  <p className="text-xs text-gray-500 mt-1">
                    💎 品质越高，边框颜色越亮丽，还有特殊光效
                  </p>
                </div>

                {/* 物品格子 */}
                <div className="flex-1 overflow-y-auto">
                  {(() => {
                    const currentItems = inventorySystem.getState().items;
                    console.log('渲染物品列表，当前物品数量:', currentItems.length);
                    console.log('当前物品:', currentItems);
                    
                    if (currentItems.length === 0) {
                      return (
                        <div className="empty-inventory text-center py-12 text-gray-500">
                          <div className="text-6xl mb-4">📦</div>
                          <h3 className="text-lg font-bold mb-2 text-gray-700">魔法背包空空如也</h3>
                          <p className="text-sm">快去开启神秘盲盒，收集珍贵物品吧！</p>
                        </div>
                      );
                    }

                    // 展开物品：每个物品根据数量分别显示
                    const expandedItems: Array<{item: InventoryItem, index: number}> = [];
                    currentItems.forEach((item) => {
                      for (let i = 0; i < item.quantity; i++) {
                        expandedItems.push({ item, index: i });
                      }
                    });
                    
                    console.log('展开后物品数量:', expandedItems.length);
                    
                    return (
                      <div style={{ 
                        display: 'grid', 
                        gridTemplateColumns: 'repeat(8, 1fr)', 
                        gap: '12px', 
                        padding: '16px',
                        backgroundColor: '#f8f9fa',
                        borderRadius: '8px'
                      }}>
                        {expandedItems.map(({item, index}, globalIndex) => {
                          console.log(`渲染物品格子 ${globalIndex}:`, item.name, item.rarity);
                          
                          return (
                            <InventoryTooltip key={`${item.id}-${index}-${globalIndex}`} item={item}>
                              <div 
                                style={{ 
                                  backgroundColor: '#ffffff',
                                  border: `3px solid ${RARITY_COLORS[item.rarity] || '#ccc'}`,
                                  borderRadius: '12px',
                                  minHeight: '100px',
                                  display: 'flex',
                                  flexDirection: 'column',
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                  padding: '8px',
                                  position: 'relative',
                                  cursor: 'pointer',
                                  transition: 'transform 0.2s ease, box-shadow 0.2s ease'
                                }}
                                title={`${item.name} (${RARITY_NAMES[item.rarity]})`}
                                onMouseEnter={(e) => {
                                  e.currentTarget.style.transform = 'scale(1.05)';
                                  e.currentTarget.style.boxShadow = `0 8px 16px ${RARITY_COLORS[item.rarity]}40`;
                                }}
                                onMouseLeave={(e) => {
                                  e.currentTarget.style.transform = 'scale(1)';
                                  e.currentTarget.style.boxShadow = 'none';
                                }}
                              >
                                {/* 物品图标 */}
                                <div style={{ 
                                  fontSize: '2rem', 
                                  marginBottom: '4px',
                                  filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))'
                                }}>
                                  {item.icon}
                                </div>
                                
                                {/* 物品名称 */}
                                <div style={{ 
                                  fontSize: '10px', 
                                  fontWeight: 'bold', 
                                  textAlign: 'center',
                                  color: '#333',
                                  overflow: 'hidden',
                                  textOverflow: 'ellipsis',
                                  whiteSpace: 'nowrap',
                                  width: '100%',
                                  textShadow: '0 1px 2px rgba(255,255,255,0.8)'
                                }}>
                                  {item.name}
                                </div>
                                
                                {/* 品质标记 */}
                                <div style={{ 
                                  position: 'absolute',
                                  top: '4px',
                                  left: '4px',
                                  fontSize: '8px',
                                  fontWeight: 'bold',
                                  color: RARITY_COLORS[item.rarity] || '#666',
                                  backgroundColor: 'rgba(255,255,255,0.9)',
                                  padding: '2px 4px',
                                  borderRadius: '4px',
                                  textShadow: 'none'
                                }}>
                                  {RARITY_NAMES[item.rarity] || '普通'}
                                </div>

                                {/* 产量指示器 - 仅农业产品显示 */}
                                {item.category === 'agricultural' && (
                                  <div style={{
                                    position: 'absolute',
                                    top: '4px',
                                    right: '4px',
                                    fontSize: '12px',
                                    backgroundColor: 'rgba(76, 175, 80, 0.9)',
                                    color: 'white',
                                    padding: '2px 4px',
                                    borderRadius: '4px',
                                    fontWeight: 'bold'
                                  }}>
                                    📈
                                  </div>
                                )}

                                {/* 品质光效 */}
                                <div style={{
                                  position: 'absolute',
                                  inset: '0',
                                  borderRadius: '12px',
                                  background: `linear-gradient(45deg, ${RARITY_COLORS[item.rarity]}20, transparent 50%, ${RARITY_COLORS[item.rarity]}20)`,
                                  opacity: '0.3',
                                  pointerEvents: 'none'
                                }} />
                              </div>
                            </InventoryTooltip>
                          );
                        })}
                      </div>
                    );
                  })()}
                </div>
              </div>
            ) : (
              renderSynthesisTab()
            )}
          </div>
        </div>

        {/* 拖拽工作台 - 放在背包窗口之上 */}
        {showWorkbench && (
          <div 
            className="fixed inset-0"
            style={{ zIndex: 15100 }}
          >
            <SynthesisWorkbench 
              inventorySystem={inventorySystem}
              onClose={() => {
                console.log('关闭拖拽工作台');
                setShowWorkbench(false);
              }}
            />
          </div>
        )}
      </div>
    </div>
  );
}; 