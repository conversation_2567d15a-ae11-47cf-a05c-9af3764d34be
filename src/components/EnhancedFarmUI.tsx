import React, { useState, useEffect } from 'react'
import { EnhancedFarmSystem } from '../systems/EnhancedFarmSystem'
import { ExtendedFarmData, TerrainType, Season, WeatherType } from '../types/gameModels'
import { CHINESE_FUTURES_PRODUCTS, ChineseFuturesProduct } from '../data/chineseFuturesProducts'

// 畜牧业专用数据结构
export interface Animal {
  id: string
  type: 'chicken' | 'pig'
  age: number // 天数
  health: number // 0-100
  happiness: number // 0-100
  productivity: number // 生产力 0-100
  feedLevel: number // 饱食度 0-100
  lastFed: number // 上次喂食时间戳
  lastProduced: number // 上次产出时间戳
  position: { x: number, y: number } // 在6x6网格中的位置
}

export interface Building {
  id: string
  type: 'chicken_coop' | 'pig_pen' | 'feed_storage' | 'processing_plant'
  level: number
  capacity: number // 最大容纳动物数量
  efficiency: number // 效率加成
  durability: number // 耐久度 0-100
  position: { x: number, y: number }
  size: { width: number, height: number } // 建筑占用的格子数
}

export interface LivestockFarmData {
  animals: Animal[]
  buildings: Building[]
  feed: number // 饲料储存量
  products: { [key: string]: number } // 产品库存 (egg, pork等)
  coins: number
  experience: number
  level: number
  lastUpdate: number
}

// 建筑类型定义
export const BUILDING_TYPES = {
  chicken_coop: {
    name: '鸡舍',
    icon: '🏠',
    cost: 500,
    capacity: 10,
    size: { width: 2, height: 2 },
    description: '用于饲养蛋鸡的专用建筑'
  },
  pig_pen: {
    name: '猪圈',
    icon: '🏘️',
    cost: 800,
    capacity: 6,
    size: { width: 2, height: 2 },
    description: '用于饲养生猪的专用圈舍'
  },
  feed_storage: {
    name: '饲料仓库',
    icon: '🏭',
    cost: 300,
    capacity: 0,
    size: { width: 1, height: 1 },
    description: '储存饲料的仓库，增加饲料储存上限'
  },
  processing_plant: {
    name: '加工厂',
    icon: '🏭',
    cost: 1200,
    capacity: 0,
    size: { width: 2, height: 2 },
    description: '处理畜产品，提高产品价值'
  }
}

// 动物类型定义
export const ANIMAL_TYPES = {
  chicken: {
    name: '小鸡',
    icon: '🐥',
    cost: 50,
    maturityDays: 7,
    productionInterval: 24, // 小时
    product: 'egg',
    productIcon: '🥚',
    maxAge: 365
  },
  pig: {
    name: '小猪',
    icon: '🐷',
    cost: 200,
    maturityDays: 30,
    productionInterval: 72, // 小时  
    product: 'pork',
    productIcon: '🥩',
    maxAge: 180
  }
}

// 专门化农场类型
export interface SpecializedFarm {
  id: string
  product: ChineseFuturesProduct
  name: string
  description: string
  optimalTerrain: TerrainType[]
  specialBonus: string
  unlockLevel: number
}

// 主要农产品的专门化农场
const SPECIALIZED_FARMS: SpecializedFarm[] = [
  {
    id: 'corn-farm',
    product: CHINESE_FUTURES_PRODUCTS.find(p => p.id === 'corn')!,
    name: '玉米专业农场',
    description: '现代化玉米种植基地，高产优质',
    optimalTerrain: [TerrainType.PLAINS, TerrainType.FERTILE_VALLEY],
    specialBonus: '玉米产量+60%，抗病能力+50%',
    unlockLevel: 1
  },
  {
    id: 'wheat-farm',
    product: CHINESE_FUTURES_PRODUCTS.find(p => p.id === 'wheat')!,
    name: '小麦专业农场',
    description: '专业小麦种植基地，采用现代农业技术',
    optimalTerrain: [TerrainType.PLAINS, TerrainType.HILLS],
    specialBonus: '小麦产量+50%，病虫害抗性+40%',
    unlockLevel: 1
  },
  {
    id: 'soybean-farm',
    product: CHINESE_FUTURES_PRODUCTS.find(p => p.id === 'soybean')!,
    name: '大豆专业农场',
    description: '大豆种植专业化农场，注重土壤改良',
    optimalTerrain: [TerrainType.PLAINS, TerrainType.HILLS],
    specialBonus: '大豆产量+45%，土壤肥力自然恢复+100%',
    unlockLevel: 2
  },
  {
    id: 'cotton-farm',
    product: CHINESE_FUTURES_PRODUCTS.find(p => p.id === 'cotton')!,
    name: '棉花专业农场',
    description: '现代化棉花种植基地，机械化程度高',
    optimalTerrain: [TerrainType.HILLS, TerrainType.PLAINS],
    specialBonus: '棉花产量+55%，纤维品质+40%',
    unlockLevel: 3
  },
  {
    id: 'apple-farm',
    product: CHINESE_FUTURES_PRODUCTS.find(p => p.id === 'apple')!,
    name: '苹果专业果园',
    description: '现代化苹果果园，果树栽培专业化',
    optimalTerrain: [TerrainType.MOUNTAINSIDE, TerrainType.HILLS],
    specialBonus: '苹果产量+70%，果实品质+60%',
    unlockLevel: 3
  },
  {
    id: 'egg-farm',
    product: CHINESE_FUTURES_PRODUCTS.find(p => p.id === 'egg')!,
    name: '鸡蛋专业养殖场',
    description: '现代化蛋鸡养殖基地，科学饲养管理',
    optimalTerrain: [TerrainType.PLAINS, TerrainType.HILLS],
    specialBonus: '鸡蛋产量+65%，蛋品质量+45%',
    unlockLevel: 4
  },
  {
    id: 'pig-farm',
    product: CHINESE_FUTURES_PRODUCTS.find(p => p.id === 'live_pig')!,
    name: '生猪专业养殖场',
    description: '规模化生猪养殖基地，智能化管理',
    optimalTerrain: [TerrainType.PLAINS, TerrainType.FERTILE_VALLEY],
    specialBonus: '生猪出栏重量+50%，饲料转化率+40%',
    unlockLevel: 4
  },
  {
    id: 'rapeseed-farm',
    product: CHINESE_FUTURES_PRODUCTS.find(p => p.id === 'rapeseed')!,
    name: '油菜专业农场',
    description: '油菜籽专业种植基地，榨油工艺优化',
    optimalTerrain: [TerrainType.PLAINS, TerrainType.WETLANDS],
    specialBonus: '油菜产量+50%，出油率+25%',
    unlockLevel: 5
  },
  {
    id: 'peanut-farm',
    product: CHINESE_FUTURES_PRODUCTS.find(p => p.id === 'peanut')!,
    name: '花生专业农场',
    description: '花生种植专业化基地，沙质土壤优化',
    optimalTerrain: [TerrainType.HILLS, TerrainType.PLAINS],
    specialBonus: '花生产量+55%，坚果饱满度+45%',
    unlockLevel: 5
  },
  {
    id: 'red-jujube-farm',
    product: CHINESE_FUTURES_PRODUCTS.find(p => p.id === 'red_jujube')!,
    name: '红枣专业果园',
    description: '专业红枣种植基地，传统与现代工艺结合',
    optimalTerrain: [TerrainType.HILLS, TerrainType.MOUNTAINSIDE],
    specialBonus: '红枣产量+65%，糖分含量+40%',
    unlockLevel: 6
  }
]

// 增强农场界面
export const EnhancedFarmUI: React.FC = () => {
  const [farmSystem, setFarmSystem] = useState<EnhancedFarmSystem | null>(null)
  const [farmData, setFarmData] = useState<ExtendedFarmData | null>(null)
  const [selectedPlot, setSelectedPlot] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState<'farm' | 'terrain-shop' | 'explore' | 'plot-shop'>('farm')
  const [playerCoins, setPlayerCoins] = useState(5000) // 增加初始金币支持地块购买
  const [selectedFarmType, setSelectedFarmType] = useState<SpecializedFarm | null>(null)
  const [playerLevel, setPlayerLevel] = useState(6) // 增加玩家等级以解锁红枣
  const [ownedPlots, setOwnedPlots] = useState<Set<string>>(new Set<string>()) // 默认拥有0个地块
  // 🎉 添加最近购买地块的状态，用于视觉反馈
  const [recentlyPurchased, setRecentlyPurchased] = useState<string | null>(null)

  // 🐷 畜牧业专用状态
  const [livestockData, setLivestockData] = useState<LivestockFarmData | null>(null)
  const [selectedBuilding, setSelectedBuilding] = useState<string | null>(null)
  const [selectedAnimal, setSelectedAnimal] = useState<string | null>(null)
  const [livestockTab, setLivestockTab] = useState<'buildings' | 'animals' | 'shop' | 'production'>('buildings')

  // 判断当前是否为畜牧业农场
  const isLivestockFarm = selectedFarmType?.id === 'egg-farm' || selectedFarmType?.id === 'pig-farm'
  const livestockType = selectedFarmType?.id === 'egg-farm' ? 'chicken' : 'pig'

  // 获取可用的专业农场（基于玩家等级）
  const availableFarms = SPECIALIZED_FARMS.filter(farm => farm.unlockLevel <= playerLevel)

  // 获取当前农场可种植的作物
  const availableCrops = selectedFarmType ? [
    { 
      id: selectedFarmType.product.id, 
      name: `${selectedFarmType.product.icon} ${selectedFarmType.product.name}`,
      product: selectedFarmType.product
    }
  ] : []

  // 地块购买价格表
  const getPlotPrice = (x: number, y: number): number => {
    const centerX = 4, centerY = 4
    const distance = Math.abs(x - centerX) + Math.abs(y - centerY)
    const basePrice = 100
    return basePrice + (distance * 50) // 距离中心越远越贵
  }

  // 地块所有权持久化功能
  const saveOwnedPlotsToStorage = (farmType: string, ownedPlots: Set<string>) => {
    try {
      const storageKey = `owned_plots_${farmType}`
      localStorage.setItem(storageKey, JSON.stringify([...ownedPlots]))
      console.log(`💾 地块所有权已保存到 ${storageKey}:`, [...ownedPlots])
    } catch (error) {
      console.error('保存地块所有权失败:', error)
    }
  }

  const loadOwnedPlotsFromStorage = (farmType: string): Set<string> => {
    try {
      const storageKey = `owned_plots_${farmType}`
      const savedData = localStorage.getItem(storageKey)
      if (savedData) {
        const parsedData = JSON.parse(savedData)
        const ownedPlotsSet = new Set<string>(parsedData)
        console.log(`📂 从 ${storageKey} 加载地块所有权: ${ownedPlotsSet.size} 个地块`)
        return ownedPlotsSet
      }
    } catch (error) {
      console.error('加载地块所有权失败:', error)
    }
    
    // 🔧 新用户默认拥有0个地块，不自动添加任何地块
    // 用户需要主动购买地块
    console.log(`🌱 ${farmType} 使用默认地块所有权: 0个地块`)
    return new Set<string>() // 默认拥有0个地块
  }

  // 农场数据持久化相关函数
  const saveFarmDataToStorage = (data: ExtendedFarmData, farmType: string) => {
    try {
      const storageKey = `enhanced_farm_${farmType}`
      localStorage.setItem(storageKey, JSON.stringify({
        ...data,
        lastSaved: Date.now()
      }))
      console.log(`💾 农场数据已保存到 ${storageKey}`)
    } catch (error) {
      console.error('保存农场数据失败:', error)
    }
  }

  const loadFarmDataFromStorage = (farmType: string): ExtendedFarmData | null => {
    try {
      const storageKey = `enhanced_farm_${farmType}`
      const savedData = localStorage.getItem(storageKey)
      if (savedData) {
        const parsedData = JSON.parse(savedData)
        console.log(`📂 从 ${storageKey} 加载农场数据`)
        return parsedData
      }
    } catch (error) {
      console.error('加载农场数据失败:', error)
    }
    return null
  }

  // 🐷 畜牧业数据持久化
  const saveLivestockDataToStorage = (data: LivestockFarmData, farmType: string) => {
    try {
      const storageKey = `livestock_farm_${farmType}`
      localStorage.setItem(storageKey, JSON.stringify({
        ...data,
        lastSaved: Date.now()
      }))
      console.log(`🐷 畜牧业数据已保存到 ${storageKey}`)
    } catch (error) {
      console.error('保存畜牧业数据失败:', error)
    }
  }

  const loadLivestockDataFromStorage = (farmType: string): LivestockFarmData | null => {
    try {
      const storageKey = `livestock_farm_${farmType}`
      const savedData = localStorage.getItem(storageKey)
      if (savedData) {
        const parsedData = JSON.parse(savedData)
        console.log(`🐷 从 ${storageKey} 加载畜牧业数据`)
        return parsedData
      }
    } catch (error) {
      console.error('加载畜牧业数据失败:', error)
    }
    return null
  }

  // 🐷 初始化畜牧业数据
  const initializeLivestockData = (farmType: string): LivestockFarmData => {
    return {
      animals: [],
      buildings: [],
      feed: 100, // 初始饲料
      products: { egg: 0, pork: 0 },
      coins: 1000,
      experience: 0,
      level: 1,
      lastUpdate: Date.now()
    }
  }

  // 畜牧业核心逻辑函数

  // 建设建筑
  const handleBuildStructure = (buildingType: keyof typeof BUILDING_TYPES, position: { x: number, y: number }) => {
    if (!livestockData) return

    const buildingInfo = BUILDING_TYPES[buildingType]
    if (livestockData.coins < buildingInfo.cost) {
      alert('💰 金币不足！')
      return
    }

    // 检查位置是否可建设
    if (!canPlaceBuilding(position, buildingInfo.size)) {
      alert('❌ 此位置无法建设！')
      return
    }

    const newBuilding: Building = {
      id: `building_${Date.now()}`,
      type: buildingType,
      level: 1,
      capacity: buildingInfo.capacity,
      efficiency: 100,
      durability: 100,
      position,
      size: buildingInfo.size
    }

    const updatedData = {
      ...livestockData,
      buildings: [...livestockData.buildings, newBuilding],
      coins: livestockData.coins - buildingInfo.cost
    }

    setLivestockData(updatedData)
    saveLivestockDataToStorage(updatedData, selectedFarmType!.id)
    console.log(`🏗️ 建设了 ${buildingInfo.name}`)
  }

  // 检查建筑是否可放置
  const canPlaceBuilding = (position: { x: number, y: number }, size: { width: number, height: number }): boolean => {
    if (!livestockData) return false

    // 检查边界
    if (position.x + size.width > 6 || position.y + size.height > 6) {
      return false
    }

    // 检查是否与现有建筑重叠
    return !livestockData.buildings.some(building => {
      const bx = building.position.x
      const by = building.position.y
      const bw = building.size.width
      const bh = building.size.height

      return !(position.x >= bx + bw || 
               position.x + size.width <= bx || 
               position.y >= by + bh || 
               position.y + size.height <= by)
    })
  }

  // 购买动物
  const handleBuyAnimal = (animalType: 'chicken' | 'pig', position: { x: number, y: number }) => {
    if (!livestockData) return

    const animalInfo = ANIMAL_TYPES[animalType]
    if (livestockData.coins < animalInfo.cost) {
      alert('💰 金币不足！')
      return
    }

    // 检查是否有合适的建筑容量
    const suitableBuilding = livestockData.buildings.find(building => 
      (building.type === 'chicken_coop' && animalType === 'chicken') ||
      (building.type === 'pig_pen' && animalType === 'pig')
    )

    if (!suitableBuilding) {
      alert(`❌ 需要先建设${animalType === 'chicken' ? '鸡舍' : '猪圈'}！`)
      return
    }

    const animalsInBuilding = livestockData.animals.filter(animal => 
      animal.type === animalType
    ).length

    if (animalsInBuilding >= suitableBuilding.capacity) {
      alert(`❌ ${animalType === 'chicken' ? '鸡舍' : '猪圈'}已满！`)
      return
    }

    const newAnimal: Animal = {
      id: `animal_${Date.now()}`,
      type: animalType,
      age: 0,
      health: 100,
      happiness: 80,
      productivity: 50,
      feedLevel: 100,
      lastFed: Date.now(),
      lastProduced: Date.now(),
      position
    }

    const updatedData = {
      ...livestockData,
      animals: [...livestockData.animals, newAnimal],
      coins: livestockData.coins - animalInfo.cost
    }

    setLivestockData(updatedData)
    saveLivestockDataToStorage(updatedData, selectedFarmType!.id)
    console.log(`🐣 购买了一只 ${animalInfo.name}`)
  }

  // 喂食动物
  const handleFeedAnimals = () => {
    if (!livestockData) return

    if (livestockData.feed < 10) {
      alert('🌾 饲料不足！')
      return
    }

    const updatedAnimals = livestockData.animals.map(animal => ({
      ...animal,
      feedLevel: Math.min(100, animal.feedLevel + 30),
      happiness: Math.min(100, animal.happiness + 10),
      lastFed: Date.now()
    }))

    const updatedData = {
      ...livestockData,
      animals: updatedAnimals,
      feed: livestockData.feed - 10
    }

    setLivestockData(updatedData)
    saveLivestockDataToStorage(updatedData, selectedFarmType!.id)
    console.log('🍽️ 喂食了所有动物')
  }

  // 收集产品
  const handleCollectProducts = () => {
    if (!livestockData) return

    let totalProducts = { egg: 0, pork: 0 }
    const now = Date.now()

    const updatedAnimals = livestockData.animals.map(animal => {
      const animalInfo = ANIMAL_TYPES[animal.type]
      const hoursSinceLastProduction = (now - animal.lastProduced) / (1000 * 60 * 60)

      if (hoursSinceLastProduction >= animalInfo.productionInterval && 
          animal.age >= animalInfo.maturityDays &&
          animal.health > 50 &&
          animal.feedLevel > 20) {
        
        const productAmount = Math.floor(animal.productivity / 50)
        totalProducts[animalInfo.product as keyof typeof totalProducts] += productAmount

        return {
          ...animal,
          lastProduced: now,
          feedLevel: Math.max(0, animal.feedLevel - 10)
        }
      }
      return animal
    })

    if (totalProducts.egg > 0 || totalProducts.pork > 0) {
      const updatedData = {
        ...livestockData,
        animals: updatedAnimals,
        products: {
          egg: livestockData.products.egg + totalProducts.egg,
          pork: livestockData.products.pork + totalProducts.pork
        }
      }

      setLivestockData(updatedData)
      saveLivestockDataToStorage(updatedData, selectedFarmType!.id)
      
      if (totalProducts.egg > 0) console.log(`🥚 收集了 ${totalProducts.egg} 个鸡蛋`)
      if (totalProducts.pork > 0) console.log(`🥩 收集了 ${totalProducts.pork} 份猪肉`)
    } else {
      alert('📦 暂无可收集的产品')
    }
  }

  // 销售产品
  const handleSellProducts = (productType: 'egg' | 'pork', amount: number) => {
    if (!livestockData) return

    if (livestockData.products[productType] < amount) {
      alert('📦 产品数量不足！')
      return
    }

    const price = productType === 'egg' ? 5 : 20 // 每个鸡蛋5金币，每份猪肉20金币
    const revenue = amount * price

    const updatedData = {
      ...livestockData,
      products: {
        ...livestockData.products,
        [productType]: livestockData.products[productType] - amount
      },
      coins: livestockData.coins + revenue
    }

    setLivestockData(updatedData)
    saveLivestockDataToStorage(updatedData, selectedFarmType!.id)
    console.log(`💰 销售了 ${amount} 个${productType === 'egg' ? '鸡蛋' : '猪肉'}，获得 ${revenue} 金币`)
  }

  // 初始化时设置默认农场（玉米农场）
  useEffect(() => {
    // 如果没有选择农场类型，默认选择第一个可用的农场（玉米农场）
    if (!selectedFarmType && availableFarms.length > 0) {
      const defaultFarm = availableFarms[0] // 玉米农场
      setSelectedFarmType(defaultFarm)
      console.log(`🌱 默认选择农场类型: ${defaultFarm.name}`)
    }
  }, [])

  // 当农场类型改变时，初始化农场系统
  useEffect(() => {
    if (!selectedFarmType) return

    const farmType = selectedFarmType.id
    console.log(`🔄 切换到农场类型: ${farmType}`)
    
    // 检测并清除旧的3x3农场数据
    let savedFarmData = loadFarmDataFromStorage(farmType)
    
    // 先尝试加载该农场类型的地块所有权
    let loadedOwnedPlots = loadOwnedPlotsFromStorage(farmType)
    console.log(`📦 尝试加载${farmType}的地块所有权: ${loadedOwnedPlots.size} 个地块，详情:`, [...loadedOwnedPlots])
    
    // 如果加载的农场数据是旧格式，清除农场数据但保留有效的地块所有权
    if (savedFarmData && savedFarmData.size && savedFarmData.size.totalPlots === 36) {
      console.log('🔄 检测到旧的6x6农场数据，将清除农场数据但保留地块所有权')
      localStorage.removeItem(`enhanced_farm_${farmType}`)
      savedFarmData = null
      // 保留现有地块所有权，不重置
      console.log('✅ 保留现有地块所有权，不重置')
    }
    
    // 设置地块所有权状态
    setOwnedPlots(loadedOwnedPlots)
    console.log(`📦 最终设置${farmType}的地块所有权: ${loadedOwnedPlots.size} 个地块，详情:`, [...loadedOwnedPlots])
    
    let initialFarmData: ExtendedFarmData
    
    if (savedFarmData && savedFarmData.size && savedFarmData.size.totalPlots === 36) {
      // 使用保存的6x6数据
      initialFarmData = savedFarmData
      console.log(`📂 使用已保存的${farmType}数据`)
    } else {
      // 创建新的专业农场数据
      initialFarmData = {
        id: `${farmType}_1`,
        ownerId: 'player_1',
        name: selectedFarmType.name,
        level: 1,
        size: { 
          width: 6,
          height: 6,
          totalPlots: 36, 
          usedPlots: 0 
        },
        terrain: selectedFarmType.optimalTerrain[0], // 使用最优地形
        plots: [],
        currentSeason: Season.SPRING,
        currentWeather: WeatherType.SUNNY,
        soilHealth: 85, // 专业农场土壤健康更高
        waterLevel: 90, // 专业农场水分管理更好
        buildings: [],
        equipment: [],
        storage: {
          capacity: 200, // 专业农场存储容量更大
          items: []
        },
        decorations: [],
        totalValue: 0,
        income: 0,
        expenses: 0,
        theme: farmType,
        createdAt: new Date(),
        lastUpdated: new Date()
      }
      console.log(`🌱 创建新的${selectedFarmType.name}数据`)
    }

    const system = new EnhancedFarmSystem(initialFarmData)
    
    // 如果是新建的农场，为专业农场设置特殊地形分布
    if (!savedFarmData) {
      const plots = system.getAllPlots()
      plots.forEach((plot, index) => {
        // 专业农场有更多适宜的地形
        const terrainOptions = selectedFarmType.optimalTerrain
        const selectedTerrain = terrainOptions[index % terrainOptions.length]
        system.transformTerrain(plot.id, selectedTerrain)
      })
    }
    
    // 验证农场系统生成了正确数量的地块
    const allPlots = system.getAllPlots()
    console.log(`✅ ${farmType}农场系统已生成 ${allPlots.length} 个地块`)
    if (allPlots.length !== 36) {
      console.error(`❌ 错误: 期望36个地块，但生成了${allPlots.length}个`)
    }
    
    setFarmSystem(system)
    setFarmData(system.getFarmData())

    const updateCallback = () => {
      const currentData = system.getFarmData()
      setFarmData(currentData)
      // 自动保存到localStorage
      saveFarmDataToStorage(currentData, farmType)
    }
    system.addUpdateCallback(updateCallback)

    // 组件卸载时保存数据
    return () => {
      system.removeUpdateCallback(updateCallback)
      saveFarmDataToStorage(system.getFarmData(), farmType)
      // 🔧 移除cleanup中的地块保存逻辑，因为地块购买时已经立即保存
      system.destroy()
    }
  }, [selectedFarmType]) // 🔧 移除ownedPlots依赖，避免购买地块时重新初始化农场系统

  // 处理农场类型选择
  const handleSelectFarmType = (farmType: SpecializedFarm) => {
    console.log(`🌱 选择农场类型: ${farmType.name}`)
    setSelectedFarmType(farmType)
    
    // 🐷 检查是否为畜牧业农场
    if (farmType.id === 'egg-farm' || farmType.id === 'pig-farm') {
      // 畜牧业农场逻辑
      const savedLivestockData = loadLivestockDataFromStorage(farmType.id)
      if (savedLivestockData) {
        setLivestockData(savedLivestockData)
        console.log(`🐷 加载了 ${farmType.name} 的畜牧业数据`)
      } else {
        // 创建新的畜牧业数据
        const newLivestockData = initializeLivestockData(farmType.id)
        setLivestockData(newLivestockData)
        saveLivestockDataToStorage(newLivestockData, farmType.id)
        console.log(`🐷 初始化了 ${farmType.name} 的畜牧业数据`)
      }
      
      // 清除传统农场数据
      setFarmSystem(null)
      setFarmData(null)
      setOwnedPlots(new Set<string>())
    } else {
      // 传统种植业农场逻辑
      const savedOwnedPlots = loadOwnedPlotsFromStorage(farmType.id)
      setOwnedPlots(savedOwnedPlots)
      
      const savedFarmData = loadFarmDataFromStorage(farmType.id)
      if (savedFarmData) {
        setFarmData(savedFarmData)
        console.log(`📂 为 ${farmType.name} 加载已保存的农场数据`)
      } else {
        setFarmData(null)
        console.log(`🌱 ${farmType.name} 将创建新的农场数据`)
      }
      
      // 清除畜牧业数据
      setLivestockData(null)
      setSelectedBuilding(null)
      setSelectedAnimal(null)
    }
  }

  // 购买地块功能
  const handlePurchasePlot = (plotId: string) => {
    const [, xStr, yStr] = plotId.split('_')
    const x = parseInt(xStr), y = parseInt(yStr)
    const price = getPlotPrice(x, y)
    
    if (ownedPlots.has(plotId)) {
      alert('✅ 您已经拥有这个地块了!')
      return
    }
    
    if (playerCoins < price) {
      alert(`💰 金币不足! 需要 ${price} 金币，但您只有 ${playerCoins} 金币`)
      return
    }
    
    if (!selectedFarmType) {
      alert('⚠️ 请先选择农场类型!')
      return
    }
    
    // 购买成功
    setPlayerCoins(prev => prev - price)
    const newOwnedPlots = new Set([...ownedPlots, plotId])
    setOwnedPlots(newOwnedPlots)
    
    // 立即保存地块所有权到当前农场类型的存储
    saveOwnedPlotsToStorage(selectedFarmType.id, newOwnedPlots)
    
    // 🎉 添加购买成功的视觉反馈
    setRecentlyPurchased(plotId)
    setTimeout(() => setRecentlyPurchased(null), 2000) // 2秒后清除高亮
    
    console.log(`🛒 购买地块成功: ${plotId}, 农场: ${selectedFarmType.id}, 花费: ${price} 金币`)
    console.log(`📊 当前${selectedFarmType.name}拥有地块:`, [...newOwnedPlots])
    // 🔧 移除alert，避免UI中断导致的闪烁
    console.log(`🎉 在${selectedFarmType.name}成功购买地块 ${plotId}! 花费 ${price} 金币`)
  }

  const handlePlantCrop = (plotId: string, cropId: string) => {
    // 检查是否拥有该地块
    if (!ownedPlots.has(plotId)) {
      alert('⚠️ 您还未拥有这个地块，请先购买!')
      return
    }
    
    if (farmSystem) {
      const success = farmSystem.plantCrop(plotId, cropId)
      if (!success) {
        alert('❌ 种植失败，请检查地块状态和地形适宜性')
      }
    }
  }

  const handleHarvestCrop = (plotId: string) => {
    // 检查是否拥有该地块
    if (!ownedPlots.has(plotId)) {
      alert('⚠️ 您还未拥有这个地块!')
      return
    }
    
    if (farmSystem) {
      const result = farmSystem.harvestCrop(plotId)
      if (result) {
        // 显示收获结果
        alert(`🎉 收获成功! 获得了 ${result.cropType}`)
      } else {
        alert('❌ 收获失败，作物可能还未成熟')
      }
    }
  }

  const handleTransformTerrain = (plotId: string, newTerrain: TerrainType) => {
    if (farmSystem) {
      const result = farmSystem.transformTerrain(plotId, newTerrain)
      alert(result.message)
    }
  }

  const handleDiscoverTerrain = () => {
    if (farmSystem) {
      const result = farmSystem.discoverNewTerrain()
      alert(result.message)
    }
  }

  const handlePurchaseTerrainTransform = (terrain: TerrainType) => {
    if (farmSystem) {
      const result = farmSystem.purchaseTerrainTransform(terrain, playerCoins)
      alert(result.message)
      if (result.success && result.newCoins !== undefined) {
        setPlayerCoins(result.newCoins)
      }
    }
  }

  if (!farmData) {
    return (
      <div style={{ padding: '20px', textAlign: 'center' }}>
        <h2>🌱 正在初始化增强农场系统...</h2>
      </div>
    )
  }

  const environment = farmSystem?.getCurrentEnvironment()

  return (
    <>
      {/* 🎉 添加购买成功的动画CSS定义 */}
      <style>
        {`
          @keyframes purchaseGlow {
            from {
              box-shadow: 0 0 15px #00FF00;
              border-color: #00FF00;
            }
            to {
              box-shadow: 0 0 25px #32CD32, 0 0 35px #32CD32;
              border-color: #32CD32;
            }
          }
          
          @keyframes slideUp {
            0% {
              transform: translateX(-50%) translateY(10px);
              opacity: 0;
            }
            10% {
              transform: translateX(-50%) translateY(-10px);
              opacity: 1;
            }
            90% {
              transform: translateX(-50%) translateY(-15px);
              opacity: 1;
            }
            100% {
              transform: translateX(-50%) translateY(-25px);
              opacity: 0;
            }
          }
        `}
      </style>
      
      <div style={{
        height: '100%',
        width: '100%',
        background: 'linear-gradient(135deg, #87CEEB 0%, #98FB98 100%)',
        padding: '10px',
        overflow: 'auto',
        boxSizing: 'border-box'
      }}>
        <div style={{ 
          textAlign: 'center', 
          marginBottom: '15px',
          flexShrink: 0
        }}>
          <h1 style={{ fontSize: '1.8rem', color: '#2C5530', margin: '0 0 10px 0' }}>
            专业农场收集系统
          </h1>
          
          {/* 农产品品种选择 */}
          <div style={{
            background: 'rgba(255,255,255,0.9)',
            borderRadius: '12px',
            padding: '12px',
            marginBottom: '15px',
            boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
          }}>
            <h3 style={{ margin: '0 0 10px 0', fontSize: '1.1rem', textAlign: 'center' }}>
              🏭 专业农场类型选择
            </h3>
            
            {/* 当前选择显示 */}
            {selectedFarmType && (
              <div style={{
                background: 'linear-gradient(45deg, #4CAF50, #45a049)',
                color: 'white',
                padding: '8px 12px',
                borderRadius: '8px',
                marginBottom: '10px',
                textAlign: 'center'
              }}>
                <strong>当前: {selectedFarmType.name}</strong>
                <div style={{ fontSize: '0.8rem', opacity: 0.9 }}>
                  {selectedFarmType.specialBonus}
                </div>
              </div>
            )}
            
            {/* 农场类型选择按钮 */}
            <div style={{
              display: 'flex',
              gap: '8px',
              flexWrap: 'wrap',
              justifyContent: 'center',
              marginBottom: '8px'
            }}>
              {/* 专业农场按钮 */}
              {availableFarms.map(farm => (
                <button
                  key={farm.id}
                  onClick={() => handleSelectFarmType(farm)}
                  disabled={farm.unlockLevel > playerLevel}
                  style={{
                    padding: '8px 12px',
                    background: selectedFarmType?.id === farm.id ? '#4CAF50' : 
                             farm.unlockLevel > playerLevel ? '#ccc' : '#f0f0f0',
                    color: selectedFarmType?.id === farm.id ? 'white' : 
                          farm.unlockLevel > playerLevel ? '#999' : '#333',
                    border: 'none',
                    borderRadius: '8px',
                    cursor: farm.unlockLevel > playerLevel ? 'not-allowed' : 'pointer',
                    fontSize: '0.8rem',
                    fontWeight: 'bold',
                    transition: 'all 0.2s ease',
                    minWidth: '100px',
                    position: 'relative'
                  }}
                  title={farm.unlockLevel > playerLevel ? 
                    `需要等级 ${farm.unlockLevel} 解锁` : 
                    `${farm.description}\n${farm.specialBonus}`}
                >
                  {farm.product.icon} {farm.product.name}
                  {farm.unlockLevel > playerLevel && (
                    <span style={{
                      position: 'absolute',
                      top: '-8px',
                      right: '-8px',
                      background: '#ff4444',
                      color: 'white',
                      borderRadius: '50%',
                      width: '20px',
                      height: '20px',
                      fontSize: '0.7rem',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }}>
                      🔒
                    </span>
                  )}
                </button>
              ))}
            </div>
            
            {/* 玩家状态显示 */}
            <div style={{
              display: 'flex',
              justifyContent: 'center',
              gap: '15px',
              fontSize: '0.8rem'
            }}>
              <span style={{
                background: '#E3F2FD',
                padding: '4px 8px',
                borderRadius: '6px',
                color: '#1976D2'
              }}>
                👤 等级: {playerLevel}
              </span>
              <span style={{
                background: '#FFF3E0',
                padding: '4px 8px',
                borderRadius: '6px',
                color: '#F57C00'
              }}>
                💰 金币: {playerCoins}
              </span>
            </div>
          </div>
          
          {/* 标签页导航 */}
          <div style={{
            display: 'flex',
            justifyContent: 'center',
            gap: '8px',
            marginBottom: '10px'
          }}>
            {isLivestockFarm ? (
              // 🐷 畜牧业农场标签页
              [
                { key: 'buildings', label: '🏠 建筑', icon: '🏠' },
                { key: 'animals', label: '🐷 动物', icon: '🐷' },
                { key: 'shop', label: '🛒 商店', icon: '🛒' },
                { key: 'production', label: '📦 生产', icon: '📦' }
              ].map(tab => (
                <button
                  key={tab.key}
                  onClick={() => setLivestockTab(tab.key as any)}
                  style={{
                    padding: '8px 16px',
                    background: livestockTab === tab.key ? '#4CAF50' : '#f0f0f0',
                    color: livestockTab === tab.key ? 'white' : '#333',
                    border: 'none',
                    borderRadius: '20px',
                    cursor: 'pointer',
                    fontSize: '0.9rem',
                    fontWeight: 'bold',
                    transition: 'all 0.2s ease'
                  }}
                >
                  {tab.label}
                </button>
              ))
            ) : (
              // 🌱 传统农场标签页
              [
                { key: 'farm', label: '🌱 农场', icon: '🌱' },
                { key: 'plot-shop', label: '🛒 地块商店', icon: '🛒' },
                { key: 'terrain-shop', label: '🏪 地形商店', icon: '🏪' },
                { key: 'explore', label: '🔍 地形探索', icon: '🔍' }
              ].map(tab => (
                <button
                  key={tab.key}
                  onClick={() => setActiveTab(tab.key as any)}
                  style={{
                    padding: '8px 16px',
                    background: activeTab === tab.key ? '#4CAF50' : '#f0f0f0',
                    color: activeTab === tab.key ? 'white' : '#333',
                    border: 'none',
                    borderRadius: '20px',
                    cursor: 'pointer',
                    fontSize: '0.9rem',
                    fontWeight: 'bold',
                    transition: 'all 0.2s ease'
                  }}
                >
                  {tab.label}
                </button>
              ))
            )}
          </div>
          

          
          {environment && (
            <div style={{ 
              display: 'flex', 
              justifyContent: 'center', 
              gap: '15px',
              flexWrap: 'wrap'
            }}>
              <div style={{
                background: '#FFB6C1',
                padding: '6px 12px',
                borderRadius: '15px',
                fontWeight: 'bold',
                fontSize: '0.9rem'
              }}>
                {environment.season === Season.SPRING && '🌸 春季'}
                {environment.season === Season.SUMMER && '☀️ 夏季'}
                {environment.season === Season.AUTUMN && '🍂 秋季'}
                {environment.season === Season.WINTER && '❄️ 冬季'}
              </div>
              <div style={{
                background: '#FFD700',
                padding: '6px 12px',
                borderRadius: '15px',
                fontWeight: 'bold',
                fontSize: '0.9rem'
              }}>
                {environment.weather === WeatherType.SUNNY && '☀️ 晴天'}
                {environment.weather === WeatherType.RAINY && '🌧️ 雨天'}
                {environment.weather === WeatherType.CLOUDY && '☁️ 多云'}
                {environment.weather === WeatherType.STORMY && '⛈️ 暴风雨'}
                {environment.weather === WeatherType.DROUGHT && '🌵 干旱'}
                {environment.weather === WeatherType.SNOW && '🌨️ 下雪'}
              </div>
            </div>
          )}
        </div>

        <div style={{
          display: 'grid',
          gridTemplateColumns: '280px 1fr',
          gap: '15px',
          maxWidth: '1100px',
          margin: '0 auto',
          minHeight: 'calc(100% - 120px)',
          flexShrink: 0
        }}>
          {/* 控制面板 */}
          <div style={{
            background: 'rgba(255,255,255,0.95)',
            borderRadius: '12px',
            padding: '15px',
            height: 'fit-content',
            maxHeight: '100%',
            overflow: 'auto'
          }}>
            {activeTab === 'farm' && (
              <>
                <h3 style={{ margin: '0 0 12px 0', fontSize: '1.1rem' }}>🌱 农场控制</h3>
                
                <div style={{
                  background: '#f8f9fa',
                  padding: '12px',
                  borderRadius: '8px',
                  marginBottom: '12px'
                }}>
                  <h4 style={{ margin: '0 0 8px 0', fontSize: '1rem' }}>
                    {selectedPlot ? `📍 地块详情: ${selectedPlot}` : '📊 农场状态'}
                  </h4>
                  <div style={{ fontSize: '0.9rem', lineHeight: '1.4' }}>
                    {selectedPlot ? (
                      // 显示选定地块的详细信息
                      (() => {
                        const plot = farmSystem?.getPlot(selectedPlot)
                        if (!plot) return <div>地块不存在</div>
                        
                        return (
                          <>
                            <div>🌍 地形类型: {farmSystem?.getTerrainName(plot.terrain) || '未知'}</div>
                            <div>🌱 土壤肥力: <span style={{
                              color: plot.soilFertility >= 80 ? '#4CAF50' : 
                                    plot.soilFertility >= 60 ? '#FFA000' : 
                                    plot.soilFertility >= 40 ? '#FF9800' : '#F44336'
                            }}>{Math.round(plot.soilFertility)}%</span></div>
                            {plot.currentCrop && (
                              <>
                                <div>🌾 种植作物: {availableCrops.find(c => c.id === plot.currentCrop?.cropType)?.name || '未知'}</div>
                                <div>📈 生长进度: <span style={{
                                  color: plot.currentCrop.growthStage >= 100 ? '#4CAF50' : '#FFA000'
                                }}>{Math.round(plot.currentCrop.growthStage)}%</span></div>
                                <div>💚 作物健康: <span style={{
                                  color: plot.currentCrop.health >= 80 ? '#4CAF50' : 
                                        plot.currentCrop.health >= 60 ? '#FFA000' : 
                                        plot.currentCrop.health >= 40 ? '#FF9800' : '#F44336'
                                }}>{Math.round(plot.currentCrop.health)}%</span></div>
                                <div>⏱️ 种植时间: {plot.currentCrop.plantedAt ? new Date(plot.currentCrop.plantedAt).toLocaleTimeString() : '未知'}</div>
                              </>
                            )}
                            {plot.rotationHistory.length > 0 && (
                              <div>🔄 轮作历史: {plot.rotationHistory.slice(-2).join(' → ')}</div>
                            )}
                            {plot.diseases.length > 0 && (
                              <div style={{ color: '#F44336' }}>
                                🦠 疾病: {plot.diseases.map(d => `${d.type} (${Math.round(d.severity)}%)`).join(', ')}
                              </div>
                            )}
                            {plot.pests.length > 0 && (
                              <div style={{ color: '#FF9800' }}>
                                🐛 虫害: {plot.pests.map(p => `${p.type} (${Math.round(p.damage)}%)`).join(', ')}
                              </div>
                            )}
                            {!plot.currentCrop && (
                              <div style={{ color: '#666', fontStyle: 'italic' }}>
                                💡 点击下方按钮种植作物
                              </div>
                            )}
                          </>
                        )
                      })()
                    ) : (
                      // 显示整体农场信息
                      <>
                        <div>🏭 农场类型: {selectedFarmType ? selectedFarmType.name : '无'}</div>
                        {selectedFarmType && (
                          <div style={{ 
                            fontSize: '0.8rem', 
                            color: '#4CAF50', 
                            fontStyle: 'italic',
                            margin: '2px 0' 
                          }}>
                            ✨ {selectedFarmType.specialBonus}
                          </div>
                        )}
                        <div>🌍 地形类型: {selectedFarmType ? '专业优化' : '多样化'}</div>
                        <div>农场等级: {farmData?.level}</div>
                        <div>使用地块: {farmData?.size.usedPlots}/{farmData?.size.totalPlots}</div>
                        <div>平均土壤健康: {farmData?.soilHealth}%</div>
                        <div>水位: {farmData?.waterLevel}%</div>
                        {selectedFarmType && (
                          <div style={{ 
                            fontSize: '0.8rem', 
                            color: '#666',
                            marginTop: '4px'
                          }}>
                            📦 存储容量: {farmData?.storage.capacity}
                          </div>
                        )}
                        <div style={{ 
                          color: '#666', 
                          fontStyle: 'italic',
                          marginTop: '4px'
                        }}>
                          💡 点击地块查看详细信息
                        </div>
                      </>
                    )}
                  </div>
                </div>

                {selectedPlot && availableCrops.length > 0 && (
                  <div style={{
                    background: '#e8f5e8',
                    padding: '12px',
                    borderRadius: '8px',
                    border: '2px solid #4CAF50',
                    marginBottom: '12px'
                  }}>
                    <h4 style={{ margin: '0 0 10px 0', fontSize: '1rem' }}>地块操作: {selectedPlot}</h4>
                    {availableCrops.map(crop => (
                      <button
                        key={crop.id}
                        onClick={() => handlePlantCrop(selectedPlot, crop.id)}
                        style={{
                          display: 'block',
                          width: '100%',
                          margin: '4px 0',
                          padding: '8px',
                          background: '#4CAF50',
                          color: 'white',
                          border: 'none',
                          borderRadius: '6px',
                          cursor: 'pointer',
                          fontSize: '0.9rem'
                        }}
                      >
                        种植 {crop.name}
                      </button>
                    ))}
                    <button
                      onClick={() => handleHarvestCrop(selectedPlot)}
                      style={{
                        width: '100%',
                        padding: '8px',
                        background: '#FF6B35',
                        color: 'white',
                        border: 'none',
                        borderRadius: '6px',
                        cursor: 'pointer',
                        marginTop: '8px',
                        fontSize: '0.9rem'
                      }}
                    >
                      🎉 收获作物
                    </button>
                    
                    {/* 地形改造选项 */}
                    <div style={{
                      marginTop: '12px',
                      padding: '8px',
                      background: '#f0f8ff',
                      borderRadius: '6px'
                    }}>
                      <h5 style={{ margin: '0 0 8px 0', fontSize: '0.9rem' }}>🌍 地形改造</h5>
                      <select 
                        onChange={(e) => {
                          if (e.target.value) {
                            handleTransformTerrain(selectedPlot, e.target.value as TerrainType)
                            e.target.value = ''
                          }
                        }}
                        style={{
                          width: '100%',
                          padding: '6px',
                          borderRadius: '4px',
                          border: '1px solid #ccc',
                          fontSize: '0.8rem'
                        }}
                      >
                        <option value="">选择新地形...</option>
                        <option value={TerrainType.PLAINS}>🌾 平原</option>
                        <option value={TerrainType.HILLS}>⛰️ 山地</option>
                        <option value={TerrainType.WETLANDS}>🌿 湿地</option>
                        <option value={TerrainType.FERTILE_VALLEY}>🌺 富饶山谷</option>
                        <option value={TerrainType.MOUNTAINSIDE}>🏔️ 山腰</option>
                      </select>
                    </div>
                    
                    {/* 土壤治理选项 */}
                    <div style={{
                      marginTop: '12px',
                      padding: '8px',
                      background: '#f0f8e6',
                      borderRadius: '6px'
                    }}>
                      <h5 style={{ margin: '0 0 8px 0', fontSize: '0.9rem' }}>🌱 土壤治理</h5>
                      {(() => {
                        const plot = farmSystem?.getPlot(selectedPlot)
                        if (!plot) return null
                        
                        return (
                          <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
                            <button
                              onClick={() => {
                                if (farmSystem) {
                                  farmSystem.improveSoil(selectedPlot, 'fertilizer')
                                  setPlayerCoins(prev => Math.max(0, prev - 50))
                                }
                              }}
                              disabled={playerCoins < 50}
                              style={{
                                padding: '6px 8px',
                                background: playerCoins >= 50 ? '#4CAF50' : '#ccc',
                                color: 'white',
                                border: 'none',
                                borderRadius: '4px',
                                cursor: playerCoins >= 50 ? 'pointer' : 'not-allowed',
                                fontSize: '0.8rem'
                              }}
                            >
                              💰 化肥改良 (-50金币, +20%肥力)
                            </button>
                            <button
                              onClick={() => {
                                if (farmSystem) {
                                  farmSystem.improveSoil(selectedPlot, 'compost')
                                  setPlayerCoins(prev => Math.max(0, prev - 80))
                                }
                              }}
                              disabled={playerCoins < 80}
                              style={{
                                padding: '6px 8px',
                                background: playerCoins >= 80 ? '#2E7D32' : '#ccc',
                                color: 'white',
                                border: 'none',
                                borderRadius: '4px',
                                cursor: playerCoins >= 80 ? 'pointer' : 'not-allowed',
                                fontSize: '0.8rem'
                              }}
                            >
                              🍂 有机肥 (-80金币, +15%肥力)
                            </button>
                            <button
                              onClick={() => {
                                if (farmSystem) {
                                  farmSystem.improveSoil(selectedPlot, 'lime')
                                  setPlayerCoins(prev => Math.max(0, prev - 30))
                                }
                              }}
                              disabled={playerCoins < 30}
                              style={{
                                padding: '6px 8px',
                                background: playerCoins >= 30 ? '#FF9800' : '#ccc',
                                color: 'white',
                                border: 'none',
                                borderRadius: '4px',
                                cursor: playerCoins >= 30 ? 'pointer' : 'not-allowed',
                                fontSize: '0.8rem'
                              }}
                            >
                              🧪 石灰调节 (-30金币, +10%肥力)
                            </button>
                            <div style={{
                              fontSize: '0.7rem',
                              color: '#666',
                              marginTop: '4px',
                              textAlign: 'center'
                            }}>
                              当前土壤肥力: {Math.round(plot.soilFertility)}%
                            </div>
                          </div>
                        )
                      })()}
                    </div>
                  </div>
                )}
              </>
            )}

            {activeTab === 'terrain-shop' && (
              <>
                <h3 style={{ margin: '0 0 12px 0', fontSize: '1.1rem' }}>🏪 地形商店</h3>
                <p style={{ fontSize: '0.9rem', color: '#666', margin: '0 0 12px 0' }}>
                  购买地形改造包来提升农场效率
                </p>
                
                {farmSystem && farmSystem.getTerrainShop().map((item, index) => (
                  <div key={index} style={{
                    background: '#f8f9fa',
                    padding: '12px',
                    borderRadius: '8px',
                    marginBottom: '8px',
                    border: item.rarity === 'rare' ? '2px solid #FFD700' : 
                           item.rarity === 'uncommon' ? '2px solid #87CEEB' : '1px solid #ddd'
                  }}>
                    <div style={{ fontWeight: 'bold', fontSize: '0.9rem' }}>{item.name}</div>
                    <div style={{ fontSize: '0.8rem', color: '#666', margin: '4px 0' }}>
                      {item.description}
                    </div>
                    <div style={{ 
                      display: 'flex', 
                      justifyContent: 'space-between', 
                      alignItems: 'center',
                      marginTop: '8px'
                    }}>
                      <span style={{ fontSize: '0.9rem', fontWeight: 'bold' }}>
                        💰 {item.cost} 金币
                      </span>
                      <button
                        onClick={() => handlePurchaseTerrainTransform(item.terrain)}
                        disabled={playerCoins < item.cost}
                        style={{
                          padding: '6px 12px',
                          background: playerCoins >= item.cost ? '#4CAF50' : '#ccc',
                          color: 'white',
                          border: 'none',
                          borderRadius: '4px',
                          cursor: playerCoins >= item.cost ? 'pointer' : 'not-allowed',
                          fontSize: '0.8rem'
                        }}
                      >
                        购买
                      </button>
                    </div>
                  </div>
                ))}
              </>
            )}

            {activeTab === 'explore' && (
              <>
                <h3 style={{ margin: '0 0 12px 0', fontSize: '1.1rem' }}>🔍 地形探索</h3>
                <p style={{ fontSize: '0.9rem', color: '#666', margin: '0 0 12px 0' }}>
                  探索发现新的地形类型
                </p>
                
                <button
                  onClick={handleDiscoverTerrain}
                  style={{
                    width: '100%',
                    padding: '12px',
                    background: 'linear-gradient(45deg, #4CAF50, #45a049)',
                    color: 'white',
                    border: 'none',
                    borderRadius: '8px',
                    cursor: 'pointer',
                    fontSize: '1rem',
                    fontWeight: 'bold',
                    marginBottom: '12px'
                  }}
                >
                  🔍 探索新地形
                </button>
                
                <div style={{
                  background: '#f8f9fa',
                  padding: '12px',
                  borderRadius: '8px'
                }}>
                  <h4 style={{ margin: '0 0 8px 0', fontSize: '1rem' }}>📊 地形统计</h4>
                  {farmData.plots.reduce((acc, plot) => {
                    acc[plot.terrain] = (acc[plot.terrain] || 0) + 1
                    return acc
                  }, {} as Record<TerrainType, number>).toString() !== '{}' && 
                    Object.entries(farmData.plots.reduce((acc, plot) => {
                      acc[plot.terrain] = (acc[plot.terrain] || 0) + 1
                      return acc
                    }, {} as Record<TerrainType, number>)).map(([terrain, count]) => (
                      <div key={terrain} style={{ fontSize: '0.9rem', margin: '2px 0' }}>
                        {terrain === TerrainType.PLAINS && '🌾 平原' ||
                         terrain === TerrainType.HILLS && '⛰️ 山地' ||
                         terrain === TerrainType.WETLANDS && '🌿 湿地' ||
                         terrain === TerrainType.FERTILE_VALLEY && '🌺 富饶山谷' ||
                         terrain === TerrainType.MOUNTAINSIDE && '🏔️ 山腰' ||
                         '未知'}: {count} 个地块
                      </div>
                    ))}
                </div>
              </>
            )}

            {activeTab === 'plot-shop' && (
              <>
                <h3 style={{ margin: '0 0 12px 0', fontSize: '1.1rem' }}>🛒 地块商店</h3>
                <p style={{ fontSize: '0.9rem', color: '#666', margin: '0 0 12px 0' }}>
                  购买地块以扩展农场规模
                </p>
                
                <div style={{
                  display: 'grid',
                  gridTemplateColumns: 'repeat(3, 1fr)',
                  gap: '12px',
                  background: 'rgba(255,255,255,0.95)',
                  padding: '20px',
                  borderRadius: '15px'
                }}>
                  {farmData.plots.map((plot) => {
                    const isOwned = ownedPlots.has(plot.id)
                    const isRecentlyPurchased = recentlyPurchased === plot.id
                    const [, xStr, yStr] = plot.id.split('_')
                    const x = parseInt(xStr), y = parseInt(yStr)
                    const plotPrice = getPlotPrice(x, y)
                    
                    return (
                      <div
                        key={plot.id}
                        onClick={() => {
                          if (isOwned) {
                            setSelectedPlot(plot.id)
                          } else {
                            handlePurchasePlot(plot.id)
                          }
                        }}
                        style={{
                          width: '110px',
                          height: '110px',
                          background: plot.currentCrop ? '#90EE90' : (
                            plot.terrain === TerrainType.PLAINS ? '#DEB887' :
                            plot.terrain === TerrainType.HILLS ? '#D2B48C' :
                            plot.terrain === TerrainType.WETLANDS ? '#87CEEB' :
                            plot.terrain === TerrainType.FERTILE_VALLEY ? '#98FB98' :
                            plot.terrain === TerrainType.MOUNTAINSIDE ? '#A0A0A0' :
                            '#DEB887'
                          ),
                          border: !isOwned ? '2px dashed #999' :
                                 isRecentlyPurchased ? '3px solid #00FF00' : // 🎉 最近购买的地块显示绿色边框
                                 selectedPlot === plot.id ? '3px solid #FF6B35' : '2px solid #4CAF50',
                          borderRadius: '12px',
                          cursor: 'pointer',
                          display: 'flex',
                          flexDirection: 'column',
                          alignItems: 'center',
                          justifyContent: 'center',
                          position: 'relative',
                          transition: 'all 0.2s ease',
                          opacity: !isOwned ? 0.6 : 1,
                          // 🎉 最近购买地块的闪烁动画
                          ...(isRecentlyPurchased && {
                            animation: 'purchaseGlow 0.5s ease-in-out infinite alternate',
                            boxShadow: '0 0 15px #00FF00'
                          })
                        }}
                      >
                        {/* 🎉 购买成功的浮动提示 */}
                        {isRecentlyPurchased && (
                          <div style={{
                            position: 'absolute',
                            top: '-25px',
                            left: '50%',
                            transform: 'translateX(-50%)',
                            background: '#4CAF50',
                            color: 'white',
                            padding: '2px 6px',
                            borderRadius: '12px',
                            fontSize: '8px',
                            fontWeight: 'bold',
                            zIndex: 10,
                            animation: 'slideUp 2s ease-out'
                          }}>
                            🎉 购买成功!
                          </div>
                        )}
                        
                        {!isOwned ? (
                          <div style={{ textAlign: 'center' }}>
                            <div style={{ fontSize: '20px' }}>🔒</div>
                            <div style={{ fontSize: '8px', fontWeight: 'bold' }}>
                              💰{plotPrice}
                            </div>
                          </div>
                        ) : (
                          <>
                            <div style={{ fontSize: '8px', fontWeight: 'bold' }}>
                              {plot.id.replace('plot_', '')}
                              <span style={{ marginLeft: '2px' }}>
                                {plot.terrain === TerrainType.PLAINS && '🌾' ||
                                 plot.terrain === TerrainType.HILLS && '⛰️' ||
                                 plot.terrain === TerrainType.WETLANDS && '🌿' ||
                                 plot.terrain === TerrainType.FERTILE_VALLEY && '🌺' ||
                                 plot.terrain === TerrainType.MOUNTAINSIDE && '🏔️' ||
                                 '🌍'}
                              </span>
                            </div>

                            {plot.currentCrop ? (
                              <div style={{ textAlign: 'center' }}>
                                <div style={{ fontSize: '16px' }}>
                                  {availableCrops.find(c => c.id === plot.currentCrop?.cropType)?.name?.split(' ')[0] || '🌱'}
                                </div>
                                <div style={{ fontSize: '7px' }}>
                                  {Math.round(plot.currentCrop.growthStage)}%
                                </div>
                                <div style={{
                                  width: '40px',
                                  height: '2px',
                                  background: '#ddd',
                                  borderRadius: '1px',
                                  margin: '1px auto'
                                }}>
                                  <div style={{
                                    width: `${plot.currentCrop.growthStage}%`,
                                    height: '100%',
                                    background: plot.currentCrop.growthStage >= 100 ? '#4CAF50' : '#FFA000'
                                  }} />
                                </div>
                              </div>
                            ) : (
                              <div style={{ fontSize: '20px', opacity: 0.6 }}>🌍</div>
                            )}

                            <div style={{
                              position: 'absolute',
                              bottom: '1px',
                              fontSize: '7px',
                              color: '#666'
                            }}>
                              🌱{Math.round(plot.soilFertility)}%
                            </div>

                            {(plot.diseases.length > 0 || plot.pests.length > 0) && (
                              <div style={{
                                position: 'absolute',
                                top: '1px',
                                right: '1px',
                                display: 'flex',
                                gap: '1px'
                              }}>
                                {plot.diseases.length > 0 && <span style={{ fontSize: '8px' }}>🦠</span>}
                                {plot.pests.length > 0 && <span style={{ fontSize: '8px' }}>🐛</span>}
                              </div>
                            )}
                          </>
                        )}
                      </div>
                    )
                  })}
                </div>
              </>
            )}

            {/* 🐷 畜牧业农场控制面板 */}
            {isLivestockFarm && (
              <>
                {/* 建筑标签页 */}
                {livestockTab === 'buildings' && (
                  <>
                    <h3 style={{ margin: '0 0 12px 0', fontSize: '1.1rem' }}>🏠 建筑管理</h3>
                    
                    {/* 农场状态 */}
                    <div style={{
                      background: '#f8f9fa',
                      padding: '12px',
                      borderRadius: '8px',
                      marginBottom: '12px'
                    }}>
                      <h4 style={{ margin: '0 0 8px 0', fontSize: '1rem' }}>📊 农场状态</h4>
                      <div style={{ fontSize: '0.9rem', lineHeight: '1.4' }}>
                        <div>🏭 农场类型: {selectedFarmType?.name}</div>
                        <div>💰 资金: {livestockData?.coins || 0} 金币</div>
                        <div>🌾 饲料: {livestockData?.feed || 0} 单位</div>
                        <div>🏠 建筑数量: {livestockData?.buildings.length || 0}</div>
                        <div>🐾 动物数量: {livestockData?.animals.length || 0}</div>
                        <div>📦 鸡蛋库存: {livestockData?.products.egg || 0}</div>
                        <div>🥩 猪肉库存: {livestockData?.products.pork || 0}</div>
                      </div>
                    </div>

                    {/* 建设新建筑 */}
                    <div style={{
                      background: '#e8f5e8',
                      padding: '12px',
                      borderRadius: '8px',
                      border: '2px solid #4CAF50',
                      marginBottom: '12px'
                    }}>
                      <h4 style={{ margin: '0 0 10px 0', fontSize: '1rem' }}>🔨 建设新建筑</h4>
                      {Object.entries(BUILDING_TYPES).map(([key, building]) => (
                        <button
                          key={key}
                          onClick={() => {
                            // 简化建设：选择第一个空位
                            const emptySpot = Array.from({ length: 36 }, (_, index) => ({
                              x: index % 6,
                              y: Math.floor(index / 6)
                            })).find(pos => {
                              return !livestockData?.buildings.some(b => 
                                pos.x >= b.position.x && 
                                pos.x < b.position.x + b.size.width &&
                                pos.y >= b.position.y && 
                                pos.y < b.position.y + b.size.height
                              )
                            })
                            if (emptySpot) {
                              handleBuildStructure(key as keyof typeof BUILDING_TYPES, emptySpot)
                            } else {
                              alert('❌ 没有足够的空间建设!')
                            }
                          }}
                          disabled={!livestockData || livestockData.coins < building.cost}
                          style={{
                            display: 'block',
                            width: '100%',
                            margin: '4px 0',
                            padding: '8px',
                            background: !livestockData || livestockData.coins < building.cost ? '#ccc' : '#4CAF50',
                            color: 'white',
                            border: 'none',
                            borderRadius: '6px',
                            cursor: !livestockData || livestockData.coins < building.cost ? 'not-allowed' : 'pointer',
                            fontSize: '0.9rem',
                            textAlign: 'left'
                          }}
                        >
                          {building.icon} 建设{building.name} - 💰{building.cost}
                        </button>
                      ))}
                    </div>

                    {/* 已有建筑列表 */}
                    {livestockData && livestockData.buildings.length > 0 && (
                      <div style={{
                        background: '#f0f8ff',
                        padding: '12px',
                        borderRadius: '8px',
                        marginBottom: '12px'
                      }}>
                        <h4 style={{ margin: '0 0 10px 0', fontSize: '1rem' }}>🏠 已有建筑</h4>
                        {livestockData.buildings.map(building => (
                          <div key={building.id} style={{
                            background: 'white',
                            padding: '8px',
                            borderRadius: '6px',
                            marginBottom: '6px',
                            border: selectedBuilding === building.id ? '2px solid #FF6B35' : '1px solid #ddd'
                          }}>
                            <div style={{ fontWeight: 'bold', fontSize: '0.9rem' }}>
                              {BUILDING_TYPES[building.type].icon} {BUILDING_TYPES[building.type].name}
                            </div>
                            <div style={{ fontSize: '0.8rem', color: '#666' }}>
                              位置: ({building.position.x}, {building.position.y}) | 
                              等级: {building.level} | 
                              耐久: {Math.round(building.durability)}%
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </>
                )}

                {/* 动物标签页 */}
                {livestockTab === 'animals' && (
                  <>
                    <h3 style={{ margin: '0 0 12px 0', fontSize: '1.1rem' }}>🐾 动物管理</h3>
                    
                    {/* 购买动物 */}
                    <div style={{
                      background: '#e8f5e8',
                      padding: '12px',
                      borderRadius: '8px',
                      border: '2px solid #4CAF50',
                      marginBottom: '12px'
                    }}>
                      <h4 style={{ margin: '0 0 10px 0', fontSize: '1rem' }}>🛒 购买动物</h4>
                      {Object.entries(ANIMAL_TYPES).map(([key, animal]) => (
                        <button
                          key={key}
                          onClick={() => {
                            // 简化购买：选择第一个空位
                            const emptySpot = Array.from({ length: 36 }, (_, index) => ({
                              x: index % 6,
                              y: Math.floor(index / 6)
                            })).find(pos => {
                              return !livestockData?.buildings.some(b => 
                                pos.x >= b.position.x && 
                                pos.x < b.position.x + b.size.width &&
                                pos.y >= b.position.y && 
                                pos.y < b.position.y + b.size.height
                              ) && !livestockData?.animals.some(a => 
                                a.position.x === pos.x && a.position.y === pos.y
                              )
                            })
                            if (emptySpot) {
                              handleBuyAnimal(key as 'chicken' | 'pig', emptySpot)
                            } else {
                              alert('❌ 没有空间或没有合适的建筑!')
                            }
                          }}
                          disabled={!livestockData || livestockData.coins < animal.cost}
                          style={{
                            display: 'block',
                            width: '100%',
                            margin: '4px 0',
                            padding: '8px',
                            background: !livestockData || livestockData.coins < animal.cost ? '#ccc' : '#4CAF50',
                            color: 'white',
                            border: 'none',
                            borderRadius: '6px',
                            cursor: !livestockData || livestockData.coins < animal.cost ? 'not-allowed' : 'pointer',
                            fontSize: '0.9rem',
                            textAlign: 'left'
                          }}
                        >
                          {animal.icon} 购买{animal.name} - 💰{animal.cost}
                        </button>
                      ))}
                    </div>

                    {/* 动物管理操作 */}
                    <div style={{
                      background: '#fff3e0',
                      padding: '12px',
                      borderRadius: '8px',
                      marginBottom: '12px'
                    }}>
                      <h4 style={{ margin: '0 0 10px 0', fontSize: '1rem' }}>🍽️ 动物管理</h4>
                      <button
                        onClick={handleFeedAnimals}
                        disabled={!livestockData || livestockData.feed < 10}
                        style={{
                          width: '100%',
                          padding: '10px',
                          background: !livestockData || livestockData.feed < 10 ? '#ccc' : '#FF9800',
                          color: 'white',
                          border: 'none',
                          borderRadius: '6px',
                          cursor: !livestockData || livestockData.feed < 10 ? 'not-allowed' : 'pointer',
                          fontSize: '1rem',
                          fontWeight: 'bold',
                          marginBottom: '8px'
                        }}
                      >
                        🍽️ 喂食所有动物 (-10饲料)
                      </button>
                      <button
                        onClick={handleCollectProducts}
                        style={{
                          width: '100%',
                          padding: '10px',
                          background: '#4CAF50',
                          color: 'white',
                          border: 'none',
                          borderRadius: '6px',
                          cursor: 'pointer',
                          fontSize: '1rem',
                          fontWeight: 'bold'
                        }}
                      >
                        📦 收集产品
                      </button>
                    </div>

                    {/* 动物列表 */}
                    {livestockData && livestockData.animals.length > 0 && (
                      <div style={{
                        background: '#f0f8ff',
                        padding: '12px',
                        borderRadius: '8px',
                        marginBottom: '12px'
                      }}>
                        <h4 style={{ margin: '0 0 10px 0', fontSize: '1rem' }}>🐾 动物列表</h4>
                        {livestockData.animals.map(animal => (
                          <div key={animal.id} style={{
                            background: 'white',
                            padding: '8px',
                            borderRadius: '6px',
                            marginBottom: '6px',
                            border: selectedAnimal === animal.id ? '2px solid #FF6B35' : '1px solid #ddd'
                          }}>
                            <div style={{ fontWeight: 'bold', fontSize: '0.9rem' }}>
                              {ANIMAL_TYPES[animal.type].icon} {ANIMAL_TYPES[animal.type].name}
                            </div>
                            <div style={{ fontSize: '0.8rem', color: '#666' }}>
                              年龄: {animal.age}天 | 健康: {Math.round(animal.health)}% | 
                              饱食: {Math.round(animal.feedLevel)}% | 生产力: {Math.round(animal.productivity)}%
                            </div>
                            <div style={{ fontSize: '0.7rem', color: '#999' }}>
                              位置: ({animal.position.x}, {animal.position.y})
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </>
                )}

                {/* 商店标签页 */}
                {livestockTab === 'shop' && (
                  <>
                    <h3 style={{ margin: '0 0 12px 0', fontSize: '1.1rem' }}>🛒 畜牧商店</h3>
                    
                    {/* 饲料商店 */}
                    <div style={{
                      background: '#e8f5e8',
                      padding: '12px',
                      borderRadius: '8px',
                      border: '2px solid #4CAF50',
                      marginBottom: '12px'
                    }}>
                      <h4 style={{ margin: '0 0 10px 0', fontSize: '1rem' }}>🌾 饲料商店</h4>
                      
                      {[
                        { name: '小包饲料', amount: 50, cost: 100, description: '50单位饲料' },
                        { name: '中包饲料', amount: 150, cost: 250, description: '150单位饲料，更实惠' },
                        { name: '大包饲料', amount: 300, cost: 450, description: '300单位饲料，最划算' }
                      ].map((feed, index) => (
                        <button
                          key={index}
                          onClick={() => {
                            if (livestockData && livestockData.coins >= feed.cost) {
                              const updatedData = {
                                ...livestockData,
                                feed: livestockData.feed + feed.amount,
                                coins: livestockData.coins - feed.cost
                              }
                              setLivestockData(updatedData)
                              saveLivestockDataToStorage(updatedData, selectedFarmType!.id)
                              alert(`🎉 购买成功！获得 ${feed.amount} 单位饲料`)
                            }
                          }}
                          disabled={!livestockData || livestockData.coins < feed.cost}
                          style={{
                            display: 'block',
                            width: '100%',
                            margin: '4px 0',
                            padding: '10px',
                            background: !livestockData || livestockData.coins < feed.cost ? '#ccc' : '#4CAF50',
                            color: 'white',
                            border: 'none',
                            borderRadius: '6px',
                            cursor: !livestockData || livestockData.coins < feed.cost ? 'not-allowed' : 'pointer',
                            fontSize: '0.9rem',
                            textAlign: 'left'
                          }}
                        >
                          <div style={{ fontWeight: 'bold' }}>{feed.name} - 💰{feed.cost}</div>
                          <div style={{ fontSize: '0.8rem', opacity: 0.9 }}>{feed.description}</div>
                        </button>
                      ))}
                    </div>

                    {/* 建筑升级 */}
                    {livestockData && livestockData.buildings.length > 0 && (
                      <div style={{
                        background: '#fff3e0',
                        padding: '12px',
                        borderRadius: '8px',
                        marginBottom: '12px'
                      }}>
                        <h4 style={{ margin: '0 0 10px 0', fontSize: '1rem' }}>⬆️ 建筑升级</h4>
                        <p style={{ fontSize: '0.8rem', color: '#666', margin: '0 0 8px 0' }}>
                          升级建筑可以增加容量和效率
                        </p>
                        {livestockData.buildings.map(building => (
                          <div key={building.id} style={{
                            background: 'white',
                            padding: '8px',
                            borderRadius: '6px',
                            marginBottom: '6px',
                            border: '1px solid #ddd'
                          }}>
                            <div style={{ fontWeight: 'bold', fontSize: '0.9rem' }}>
                              {BUILDING_TYPES[building.type].icon} {BUILDING_TYPES[building.type].name} Lv.{building.level}
                            </div>
                            <div style={{ fontSize: '0.8rem', color: '#666' }}>
                              升级费用: 💰{100 * building.level} | 
                              容量: {building.capacity} → {building.capacity + 2}
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </>
                )}

                {/* 生产标签页 */}
                {livestockTab === 'production' && (
                  <>
                    <h3 style={{ margin: '0 0 12px 0', fontSize: '1.1rem' }}>📦 生产管理</h3>
                    
                    {/* 产品库存 */}
                    <div style={{
                      background: '#f8f9fa',
                      padding: '12px',
                      borderRadius: '8px',
                      marginBottom: '12px'
                    }}>
                      <h4 style={{ margin: '0 0 8px 0', fontSize: '1rem' }}>📦 产品库存</h4>
                      <div style={{ fontSize: '0.9rem', lineHeight: '1.4' }}>
                        <div>🥚 鸡蛋: {livestockData?.products.egg || 0} 个</div>
                        <div>🥩 猪肉: {livestockData?.products.pork || 0} 斤</div>
                      </div>
                    </div>

                    {/* 销售产品 */}
                    <div style={{
                      background: '#e8f5e8',
                      padding: '12px',
                      borderRadius: '8px',
                      border: '2px solid #4CAF50',
                      marginBottom: '12px'
                    }}>
                      <h4 style={{ margin: '0 0 10px 0', fontSize: '1rem' }}>💰 销售产品</h4>
                      
                      {/* 鸡蛋销售 */}
                      {livestockData && livestockData.products.egg > 0 && (
                        <div style={{ marginBottom: '10px' }}>
                          <div style={{ fontWeight: 'bold', marginBottom: '6px' }}>🥚 鸡蛋销售 (每个2金币)</div>
                          {[10, 50, livestockData.products.egg].filter(amount => amount <= livestockData.products.egg).map((amount, index) => (
                            <button
                              key={index}
                              onClick={() => handleSellProducts('egg', amount)}
                              style={{
                                margin: '2px 4px 2px 0',
                                padding: '6px 12px',
                                background: '#4CAF50',
                                color: 'white',
                                border: 'none',
                                borderRadius: '6px',
                                cursor: 'pointer',
                                fontSize: '0.8rem'
                              }}
                            >
                              卖出{amount}个 (💰{amount * 2})
                            </button>
                          ))}
                        </div>
                      )}

                      {/* 猪肉销售 */}
                      {livestockData && livestockData.products.pork > 0 && (
                        <div style={{ marginBottom: '10px' }}>
                          <div style={{ fontWeight: 'bold', marginBottom: '6px' }}>🥩 猪肉销售 (每斤5金币)</div>
                          {[5, 20, livestockData.products.pork].filter(amount => amount <= livestockData.products.pork).map((amount, index) => (
                            <button
                              key={index}
                              onClick={() => handleSellProducts('pork', amount)}
                              style={{
                                margin: '2px 4px 2px 0',
                                padding: '6px 12px',
                                background: '#4CAF50',
                                color: 'white',
                                border: 'none',
                                borderRadius: '6px',
                                cursor: 'pointer',
                                fontSize: '0.8rem'
                              }}
                            >
                              卖出{amount}斤 (💰{amount * 5})
                            </button>
                          ))}
                        </div>
                      )}

                      {(!livestockData || (livestockData.products.egg === 0 && livestockData.products.pork === 0)) && (
                        <div style={{ color: '#666', fontStyle: 'italic' }}>
                          暂无产品可销售，先收集产品吧！
                        </div>
                      )}
                    </div>

                    {/* 生产统计 */}
                    <div style={{
                      background: '#f0f8ff',
                      padding: '12px',
                      borderRadius: '8px',
                      marginBottom: '12px'
                    }}>
                      <h4 style={{ margin: '0 0 8px 0', fontSize: '1rem' }}>📊 生产统计</h4>
                      <div style={{ fontSize: '0.9rem', lineHeight: '1.4' }}>
                        <div>🐔 鸡的数量: {livestockData?.animals.filter(a => a.type === 'chicken').length || 0}</div>
                        <div>🐷 猪的数量: {livestockData?.animals.filter(a => a.type === 'pig').length || 0}</div>
                        <div>🏠 建筑数量: {livestockData?.buildings.length || 0}</div>
                        <div>⭐ 农场等级: {livestockData?.level || 1}</div>
                        <div>💎 经验值: {livestockData?.experience || 0}</div>
                      </div>
                    </div>
                  </>
                )}
              </>
            )}
          </div>

          {/* 农场网格 */}
          <div style={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'flex-start'
          }}>
            {/* 🐷 畜牧业农场网格 */}
            {isLivestockFarm ? (
              <div style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(6, 1fr)',
                gap: '6px',
                background: 'rgba(255,255,255,0.95)',
                padding: '15px',
                borderRadius: '15px',
                maxWidth: '700px'
              }}>
                {Array.from({ length: 36 }, (_, index) => {
                  const x = index % 6
                  const y = Math.floor(index / 6)
                  const gridId = `grid_${x}_${y}`
                  
                  // 查找该位置是否有建筑
                  const building = livestockData?.buildings.find(b => 
                    x >= b.position.x && 
                    x < b.position.x + b.size.width &&
                    y >= b.position.y && 
                    y < b.position.y + b.size.height
                  )
                  
                  // 查找该位置是否有动物
                  const animal = livestockData?.animals.find(a => 
                    a.position.x === x && a.position.y === y
                  )
                  
                  return (
                    <div
                      key={gridId}
                      onClick={() => {
                        if (building) {
                          setSelectedBuilding(building.id)
                          setSelectedAnimal(null)
                        } else if (animal) {
                          setSelectedAnimal(animal.id)
                          setSelectedBuilding(null)
                        } else {
                          // 空地，可以建设或购买动物
                          setSelectedBuilding(null)
                          setSelectedAnimal(null)
                        }
                      }}
                      style={{
                        width: '65px',
                        height: '65px',
                        background: building ? 
                          (building.type === 'chicken_coop' ? '#FFE5B4' :
                           building.type === 'pig_pen' ? '#DDA0DD' :
                           building.type === 'feed_storage' ? '#98FB98' :
                           building.type === 'processing_plant' ? '#B0C4DE' : '#F0F0F0') :
                          animal ? '#90EE90' : '#F5F5DC',
                        border: (selectedBuilding && building?.id === selectedBuilding) || 
                               (selectedAnimal && animal?.id === selectedAnimal) ? 
                               '3px solid #FF6B35' : '2px solid #4CAF50',
                        borderRadius: '8px',
                        cursor: 'pointer',
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                        justifyContent: 'center',
                        position: 'relative',
                        transition: 'all 0.2s ease'
                      }}
                    >
                      <div style={{ fontSize: '8px', fontWeight: 'bold', color: '#666' }}>
                        {x},{y}
                      </div>
                      
                      {building ? (
                        <div style={{ textAlign: 'center' }}>
                          <div style={{ fontSize: '24px' }}>
                            {BUILDING_TYPES[building.type].icon}
                          </div>
                          <div style={{ fontSize: '7px', fontWeight: 'bold' }}>
                            Lv.{building.level}
                          </div>
                          <div style={{ fontSize: '6px', color: '#666' }}>
                            {Math.round(building.durability)}%
                          </div>
                        </div>
                      ) : animal ? (
                        <div style={{ textAlign: 'center' }}>
                          <div style={{ fontSize: '20px' }}>
                            {ANIMAL_TYPES[animal.type].icon}
                          </div>
                          <div style={{ fontSize: '6px', color: '#666' }}>
                            {animal.age}天 💚{Math.round(animal.health)}%
                          </div>
                          <div style={{ fontSize: '6px', color: '#4CAF50' }}>
                            🍽️{Math.round(animal.feedLevel)}%
                          </div>
                        </div>
                      ) : (
                        <div style={{ textAlign: 'center', opacity: 0.5 }}>
                          <div style={{ fontSize: '16px' }}>➕</div>
                          <div style={{ fontSize: '6px' }}>空地</div>
                        </div>
                      )}
                    </div>
                  )
                })}
              </div>
            ) : (
              // 🌱 传统种植业农场网格
              farmData && (
                <div style={{
                  display: 'grid',
                  gridTemplateColumns: 'repeat(6, 1fr)',
                  gap: '6px',
                  background: 'rgba(255,255,255,0.95)',
                  padding: '15px',
                  borderRadius: '15px',
                  maxWidth: '700px'
                }}>
                  {farmData.plots.map((plot) => {
                    const isOwned = ownedPlots.has(plot.id)
                    const isRecentlyPurchased = recentlyPurchased === plot.id
                    const [, xStr, yStr] = plot.id.split('_')
                    const x = parseInt(xStr), y = parseInt(yStr)
                    const plotPrice = getPlotPrice(x, y)
                    
                    return (
                      <div
                        key={plot.id}
                        onClick={() => {
                          if (isOwned) {
                            setSelectedPlot(plot.id)
                          } else {
                            handlePurchasePlot(plot.id)
                          }
                        }}
                        style={{
                          width: '65px',
                          height: '65px',
                          background: !isOwned ? 'rgba(200,200,200,0.3)' : 
                                     plot.currentCrop ? '#90EE90' : (
                            plot.terrain === TerrainType.PLAINS ? '#DEB887' :
                            plot.terrain === TerrainType.HILLS ? '#D2B48C' :
                            plot.terrain === TerrainType.WETLANDS ? '#87CEEB' :
                            plot.terrain === TerrainType.FERTILE_VALLEY ? '#98FB98' :
                            plot.terrain === TerrainType.MOUNTAINSIDE ? '#A0A0A0' :
                            '#DEB887'
                          ),
                          border: !isOwned ? '2px dashed #999' :
                                 isRecentlyPurchased ? '3px solid #00FF00' : // 🎉 最近购买的地块显示绿色边框
                                 selectedPlot === plot.id ? '3px solid #FF6B35' : '2px solid #4CAF50',
                          borderRadius: '8px',
                          cursor: 'pointer',
                          display: 'flex',
                          flexDirection: 'column',
                          alignItems: 'center',
                          justifyContent: 'center',
                          position: 'relative',
                          transition: 'all 0.2s ease',
                          opacity: !isOwned ? 0.6 : 1,
                          // 🎉 最近购买地块的闪烁动画
                          ...(isRecentlyPurchased && {
                            animation: 'purchaseGlow 0.5s ease-in-out infinite alternate',
                            boxShadow: '0 0 15px #00FF00'
                          })
                        }}
                      >
                        {/* 🎉 购买成功的浮动提示 */}
                        {isRecentlyPurchased && (
                          <div style={{
                            position: 'absolute',
                            top: '-25px',
                            left: '50%',
                            transform: 'translateX(-50%)',
                            background: '#4CAF50',
                            color: 'white',
                            padding: '2px 6px',
                            borderRadius: '12px',
                            fontSize: '8px',
                            fontWeight: 'bold',
                            zIndex: 10,
                            animation: 'slideUp 2s ease-out'
                          }}>
                            🎉 购买成功!
                          </div>
                        )}
                        
                        {!isOwned ? (
                          <div style={{ textAlign: 'center' }}>
                            <div style={{ fontSize: '20px' }}>🔒</div>
                            <div style={{ fontSize: '8px', fontWeight: 'bold' }}>
                              💰{plotPrice}
                            </div>
                          </div>
                        ) : (
                          <>
                            <div style={{ fontSize: '8px', fontWeight: 'bold' }}>
                              {plot.id.replace('plot_', '')}
                              <span style={{ marginLeft: '2px' }}>
                                {plot.terrain === TerrainType.PLAINS && '🌾' ||
                                 plot.terrain === TerrainType.HILLS && '⛰️' ||
                                 plot.terrain === TerrainType.WETLANDS && '🌿' ||
                                 plot.terrain === TerrainType.FERTILE_VALLEY && '🌺' ||
                                 plot.terrain === TerrainType.MOUNTAINSIDE && '🏔️' ||
                                 '🌍'}
                              </span>
                            </div>

                            {plot.currentCrop ? (
                              <div style={{ textAlign: 'center' }}>
                                <div style={{ fontSize: '16px' }}>
                                  {availableCrops.find(c => c.id === plot.currentCrop?.cropType)?.name?.split(' ')[0] || '🌱'}
                                </div>
                                <div style={{ fontSize: '7px' }}>
                                  {Math.round(plot.currentCrop.growthStage)}%
                                </div>
                                <div style={{
                                  width: '40px',
                                  height: '2px',
                                  background: '#ddd',
                                  borderRadius: '1px',
                                  margin: '1px auto'
                                }}>
                                  <div style={{
                                    width: `${plot.currentCrop.growthStage}%`,
                                    height: '100%',
                                    background: plot.currentCrop.growthStage >= 100 ? '#4CAF50' : '#FFA000'
                                  }} />
                                </div>
                              </div>
                            ) : (
                              <div style={{ fontSize: '20px', opacity: 0.6 }}>🌍</div>
                            )}

                            <div style={{
                              position: 'absolute',
                              bottom: '1px',
                              fontSize: '7px',
                              color: '#666'
                            }}>
                              🌱{Math.round(plot.soilFertility)}%
                            </div>

                            {(plot.diseases.length > 0 || plot.pests.length > 0) && (
                              <div style={{
                                position: 'absolute',
                                top: '1px',
                                right: '1px',
                                display: 'flex',
                                gap: '1px'
                              }}>
                                {plot.diseases.length > 0 && <span style={{ fontSize: '8px' }}>🦠</span>}
                                {plot.pests.length > 0 && <span style={{ fontSize: '8px' }}>🐛</span>}
                              </div>
                            )}
                          </>
                        )}
                      </div>
                    )
                  })}
                </div>
              )
            )}
          </div>
        </div>

        {/* 说明 */}
        <div style={{
          textAlign: 'center',
          marginTop: '15px',
          background: 'rgba(255,255,255,0.9)',
          padding: '12px',
          borderRadius: '12px',
          maxWidth: '550px',
          margin: '15px auto 0',
          flexShrink: 0
        }}>
          <h4 style={{ margin: '0 0 8px 0', fontSize: '1rem' }}>
            {selectedFarmType ? `🏭 ${selectedFarmType.product.icon} ${selectedFarmType.name}` : '增强功能特色'}
          </h4>
          
          {selectedFarmType ? (
            <>
              <p style={{ margin: '0 0 8px 0', fontSize: '0.9rem', lineHeight: '1.4' }}>
                {selectedFarmType.description}
              </p>
              <div style={{ 
                fontSize: '0.8rem', 
                textAlign: 'left', 
                background: '#f8f9fa', 
                padding: '8px', 
                borderRadius: '8px',
                marginTop: '8px'
              }}>
                <strong>🎯 专业加成:</strong> {selectedFarmType.specialBonus}<br/>
                <strong>🌍 最佳地形:</strong> {selectedFarmType.optimalTerrain.map(terrain => {
                  const names = {
                    [TerrainType.PLAINS]: '🌾 平原',
                    [TerrainType.HILLS]: '⛰️ 山地',
                    [TerrainType.WETLANDS]: '🌿 湿地',
                    [TerrainType.FERTILE_VALLEY]: '🌺 富饶山谷',
                    [TerrainType.MOUNTAINSIDE]: '🏔️ 山腰'
                  }
                  return names[terrain]
                }).join(', ')}<br/>
                <strong>💰 基础价格:</strong> ¥{selectedFarmType.product.basePrice}/吨 ({selectedFarmType.product.exchange})<br/>
                <strong>📊 产量范围:</strong> {selectedFarmType.product.yieldRanges.gray.min}-{selectedFarmType.product.yieldRanges.gold_red.max} 公斤/亩
              </div>
            </>
          ) : (
            <>
              <p style={{ margin: '0 0 8px 0', fontSize: '0.9rem', lineHeight: '1.4' }}>
                请选择一个专业农场类型开始您的农业之旅！
              </p>
              <div style={{ 
                fontSize: '0.8rem', 
                textAlign: 'left', 
                background: '#f8f9fa', 
                padding: '8px', 
                borderRadius: '8px',
                marginTop: '8px'
              }}>
                <strong>🏭 专业农场系统:</strong><br/>
                选择专业农场类型以获得针对特定农产品的优化生产环境！<br/>
                每种专业农场都有独特的加成效果和最佳地形配置。<br/>
                🌾 玉米农场: 产量+60%, 抗病+50%<br/>
                🌾 小麦农场: 产量+50%, 病虫害抗性+40%<br/>
                🫘 大豆农场: 产量+45%, 土壤肥力自然恢复+100%<br/>
                🍎 苹果果园: 产量+70%, 果实品质+60%<br/>
                🥚 鸡蛋养殖场: 产量+65%, 蛋品质量+45%<br/>
                🐷 生猪养殖场: 出栏重量+50%, 饲料转化率+40%<br/>
                🧄 红枣果园: 产量+65%, 糖分含量+40%
              </div>
            </>
          )}
        </div>
      </div>
    </>
  )
}

export default EnhancedFarmUI 