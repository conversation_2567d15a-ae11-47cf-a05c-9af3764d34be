.error-boundary {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: 40px 20px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 12px;
  margin: 20px;
}

.error-content {
  text-align: center;
  background: white;
  padding: 40px;
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  max-width: 600px;
  width: 100%;
}

.error-icon {
  font-size: 64px;
  margin-bottom: 20px;
  animation: shake 0.5s ease-in-out infinite alternate;
}

@keyframes shake {
  0% { transform: translateX(0); }
  100% { transform: translateX(4px); }
}

.error-content h2 {
  color: #2c3e50;
  font-size: 28px;
  margin-bottom: 16px;
  font-weight: 600;
}

.error-content p {
  color: #6c757d;
  font-size: 16px;
  line-height: 1.6;
  margin-bottom: 30px;
}

.error-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
  margin-bottom: 30px;
}

.retry-button,
.reload-button {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 100px;
}

.retry-button {
  background: #007bff;
  color: white;
}

.retry-button:hover {
  background: #0056b3;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

.reload-button {
  background: #6c757d;
  color: white;
}

.reload-button:hover {
  background: #545b62;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
}

.error-details {
  text-align: left;
  margin-top: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #dee2e6;
}

.error-details summary {
  cursor: pointer;
  font-weight: 600;
  color: #495057;
  margin-bottom: 10px;
  padding: 8px;
  background: #e9ecef;
  border-radius: 4px;
}

.error-details summary:hover {
  background: #dee2e6;
}

.error-stack {
  margin-top: 10px;
}

.error-stack h4 {
  color: #343a40;
  font-size: 14px;
  margin: 15px 0 8px 0;
  font-weight: 600;
}

.error-stack pre {
  background: #2d3748;
  color: #e2e8f0;
  padding: 12px;
  border-radius: 6px;
  font-family: 'Monaco', 'Consolas', 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
  overflow-x: auto;
  white-space: pre-wrap;
  word-break: break-word;
  max-height: 200px;
  overflow-y: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .error-boundary {
    padding: 20px 10px;
    margin: 10px;
  }

  .error-content {
    padding: 30px 20px;
  }

  .error-content h2 {
    font-size: 24px;
  }

  .error-actions {
    flex-direction: column;
    align-items: center;
  }

  .retry-button,
  .reload-button {
    width: 200px;
  }

  .error-details {
    padding: 15px;
  }

  .error-stack pre {
    font-size: 11px;
    padding: 8px;
  }
}

/* 暗色主题支持 */
@media (prefers-color-scheme: dark) {
  .error-boundary {
    background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
  }

  .error-content {
    background: #2d3748;
    color: #e2e8f0;
  }

  .error-content h2 {
    color: #f7fafc;
  }

  .error-content p {
    color: #a0aec0;
  }

  .error-details {
    background: #1a202c;
    border-color: #4a5568;
  }

  .error-details summary {
    background: #4a5568;
    color: #e2e8f0;
  }

  .error-details summary:hover {
    background: #718096;
  }

  .error-stack h4 {
    color: #f7fafc;
  }
} 