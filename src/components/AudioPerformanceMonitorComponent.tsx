import React, { useState, useEffect } from 'react'
import { AudioManager } from '../audio/AudioManager'

interface PerformanceMetrics {
  averageLatency: number
  minLatency: number
  maxLatency: number
  totalPlays: number
  successfulPlays: number
  failedPlays: number
  successRate: number
  activeInstances: number
  peakInstances: number
  memoryUsage: number
  audioContextState: string
  sampleRate: number
  baseLatency: number
  outputLatency: number
  cacheHitRate: number
  cacheSize: number
  preloadedCount: number
}

interface CacheItem {
  url: string
  size: string
  lastAccessed: string
  accessCount: number
  preloaded: boolean
}

const AudioPerformanceMonitorComponent: React.FC = () => {
  const [isVisible, setIsVisible] = useState(false)
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null)
  const [cacheDetails, setCacheDetails] = useState<CacheItem[]>([])
  const [warnings, setWarnings] = useState<string[]>([])
  const [recommendations, setRecommendations] = useState<string[]>([])

  // 只在localhost显示
  const isDevelopment = window.location.hostname === 'localhost' || 
                       window.location.hostname === '127.0.0.1'

  useEffect(() => {
    if (!isDevelopment) return

    const interval = setInterval(() => {
      updateMetrics()
    }, 2000) // 每2秒更新

    return () => clearInterval(interval)
  }, [isDevelopment])

  const updateMetrics = (): void => {
    try {
      const audioManager = AudioManager.getInstance()
      const performanceData = (audioManager as any).getPerformanceReport?.()
      
      if (performanceData) {
        setMetrics(performanceData.metrics)
        setWarnings(performanceData.warnings)
        setRecommendations(performanceData.recommendations)
      }

      const cacheData = (audioManager as any).getCacheDetails?.()
      if (cacheData) {
        setCacheDetails(cacheData)
      }
    } catch (error) {
      console.warn('获取音频性能数据失败:', error)
    }
  }

  const formatLatency = (latency: number): string => {
    return `${latency.toFixed(1)}ms`
  }

  const formatMemory = (bytes: number): string => {
    return `${bytes.toFixed(1)}MB`
  }

  const formatPercentage = (value: number): string => {
    return `${value.toFixed(1)}%`
  }

  const getLatencyColor = (latency: number): string => {
    if (latency > 200) return 'text-red-500'
    if (latency > 100) return 'text-yellow-500'
    return 'text-green-500'
  }

  const getSuccessRateColor = (rate: number): string => {
    if (rate < 90) return 'text-red-500'
    if (rate < 95) return 'text-yellow-500'
    return 'text-green-500'
  }

  if (!isDevelopment) {
    return null
  }

  return (
    <div className="fixed bottom-4 right-4 z-50">
      {/* 切换按钮 */}
      <button
        onClick={() => setIsVisible(!isVisible)}
        className="bg-blue-600 text-white px-3 py-2 rounded-lg shadow-lg hover:bg-blue-700 transition-colors text-sm font-medium"
      >
        🎵 性能监控
      </button>

      {/* 性能监控面板 */}
      {isVisible && (
        <div className="absolute bottom-12 right-0 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-xl p-4 w-96 max-h-96 overflow-y-auto">
          <div className="flex justify-between items-center mb-3">
            <h3 className="text-lg font-semibold text-gray-800 dark:text-white">
              音频性能监控
            </h3>
            <button
              onClick={() => setIsVisible(false)}
              className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
            >
              ×
            </button>
          </div>

          {metrics ? (
            <div className="space-y-4">
              {/* 延迟指标 */}
              <div>
                <h4 className="font-medium text-gray-700 dark:text-gray-300 mb-2">延迟指标</h4>
                <div className="grid grid-cols-3 gap-2 text-sm">
                  <div>
                    <span className="text-gray-600 dark:text-gray-400">平均:</span>
                    <span className={`ml-1 font-mono ${getLatencyColor(metrics.averageLatency)}`}>
                      {formatLatency(metrics.averageLatency)}
                    </span>
                  </div>
                  <div>
                    <span className="text-gray-600 dark:text-gray-400">最小:</span>
                    <span className="ml-1 font-mono text-green-500">
                      {formatLatency(metrics.minLatency)}
                    </span>
                  </div>
                  <div>
                    <span className="text-gray-600 dark:text-gray-400">最大:</span>
                    <span className={`ml-1 font-mono ${getLatencyColor(metrics.maxLatency)}`}>
                      {formatLatency(metrics.maxLatency)}
                    </span>
                  </div>
                </div>
              </div>

              {/* 播放统计 */}
              <div>
                <h4 className="font-medium text-gray-700 dark:text-gray-300 mb-2">播放统计</h4>
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div>
                    <span className="text-gray-600 dark:text-gray-400">总次数:</span>
                    <span className="ml-1 font-mono">{metrics.totalPlays}</span>
                  </div>
                  <div>
                    <span className="text-gray-600 dark:text-gray-400">成功率:</span>
                    <span className={`ml-1 font-mono ${getSuccessRateColor(metrics.successRate)}`}>
                      {formatPercentage(metrics.successRate)}
                    </span>
                  </div>
                  <div>
                    <span className="text-gray-600 dark:text-gray-400">成功:</span>
                    <span className="ml-1 font-mono text-green-500">{metrics.successfulPlays}</span>
                  </div>
                  <div>
                    <span className="text-gray-600 dark:text-gray-400">失败:</span>
                    <span className="ml-1 font-mono text-red-500">{metrics.failedPlays}</span>
                  </div>
                </div>
              </div>

              {/* 资源使用 */}
              <div>
                <h4 className="font-medium text-gray-700 dark:text-gray-300 mb-2">资源使用</h4>
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div>
                    <span className="text-gray-600 dark:text-gray-400">活跃实例:</span>
                    <span className="ml-1 font-mono">{metrics.activeInstances}</span>
                  </div>
                  <div>
                    <span className="text-gray-600 dark:text-gray-400">峰值实例:</span>
                    <span className="ml-1 font-mono">{metrics.peakInstances}</span>
                  </div>
                  <div>
                    <span className="text-gray-600 dark:text-gray-400">内存使用:</span>
                    <span className="ml-1 font-mono">{formatMemory(metrics.memoryUsage)}</span>
                  </div>
                  <div>
                    <span className="text-gray-600 dark:text-gray-400">缓存命中:</span>
                    <span className="ml-1 font-mono">{formatPercentage(metrics.cacheHitRate)}</span>
                  </div>
                </div>
              </div>

              {/* 系统状态 */}
              <div>
                <h4 className="font-medium text-gray-700 dark:text-gray-300 mb-2">系统状态</h4>
                <div className="text-sm space-y-1">
                  <div>
                    <span className="text-gray-600 dark:text-gray-400">音频上下文:</span>
                    <span className={`ml-1 font-mono ${
                      metrics.audioContextState === 'running' ? 'text-green-500' :
                      metrics.audioContextState === 'suspended' ? 'text-yellow-500' : 'text-red-500'
                    }`}>
                      {metrics.audioContextState}
                    </span>
                  </div>
                  <div>
                    <span className="text-gray-600 dark:text-gray-400">采样率:</span>
                    <span className="ml-1 font-mono">{metrics.sampleRate}Hz</span>
                  </div>
                  <div>
                    <span className="text-gray-600 dark:text-gray-400">基础延迟:</span>
                    <span className="ml-1 font-mono">{formatLatency(metrics.baseLatency)}</span>
                  </div>
                </div>
              </div>

              {/* 警告和建议 */}
              {warnings.length > 0 && (
                <div>
                  <h4 className="font-medium text-red-600 dark:text-red-400 mb-2">⚠️ 警告</h4>
                  <div className="text-sm space-y-1">
                    {warnings.map((warning, index) => (
                      <div key={index} className="text-red-600 dark:text-red-400">
                        • {warning}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {recommendations.length > 0 && (
                <div>
                  <h4 className="font-medium text-blue-600 dark:text-blue-400 mb-2">💡 建议</h4>
                  <div className="text-sm space-y-1">
                    {recommendations.map((rec, index) => (
                      <div key={index} className="text-blue-600 dark:text-blue-400">
                        • {rec}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* 缓存详情 */}
              {cacheDetails.length > 0 && (
                <div>
                  <h4 className="font-medium text-gray-700 dark:text-gray-300 mb-2">缓存详情</h4>
                  <div className="text-xs space-y-1 max-h-32 overflow-y-auto">
                    {cacheDetails.map((item, index) => (
                      <div key={index} className="flex justify-between items-center">
                        <span className="text-gray-600 dark:text-gray-400 truncate flex-1">
                          {item.url}
                        </span>
                        <div className="flex space-x-2 text-gray-500">
                          <span>{item.size}</span>
                          <span>({item.accessCount})</span>
                          {item.preloaded && <span className="text-blue-500">P</span>}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div className="text-center text-gray-500 dark:text-gray-400 py-4">
              正在加载性能数据...
            </div>
          )}
        </div>
      )}
    </div>
  )
}

export default AudioPerformanceMonitorComponent 