import React, { useState, useEffect, useMemo } from 'react'
import { 
  DecorationItem, 
  FarmTheme,
  DecorationType,
  DecorationShopCategory 
} from '../types/decoration'
import { DecorationSystem } from '../systems/DecorationSystem'
import { ItemRarity } from '../types/lootbox'
import './DecorationShop.css'

interface DecorationShopProps {
  decorationSystem: DecorationSystem
  onClose: () => void
  onPurchase?: (item: DecorationItem, quantity: number) => void
}

const DecorationShop: React.FC<DecorationShopProps> = ({
  decorationSystem,
  onClose,
  onPurchase
}) => {
  const [activeTab, setActiveTab] = useState<'decorations' | 'themes' | 'owned'>('decorations')
  const [selectedCategory, setSelectedCategory] = useState<DecorationType | 'all'>('all')
  const [availableDecorations, setAvailableDecorations] = useState<DecorationItem[]>([])
  const [availableThemes, setAvailableThemes] = useState<FarmTheme[]>([])
  const [ownedDecorations, setOwnedDecorations] = useState<{ item: DecorationItem, quantity: number }[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [sortBy, setSortBy] = useState<'name' | 'price' | 'rarity'>('name')
  const [purchaseProcessing, setPurchaseProcessing] = useState<string | null>(null)

  // 装饰道具分类配置
  const categories: DecorationShopCategory[] = [
    {
      id: 'all',
      name: '全部',
      icon: '🏷️',
      decorationTypes: [],
      sortOrder: 0,
      isNew: false,
      isPopular: false
    },
    {
      id: 'fence',
      name: '围栏',
      icon: '🚪',
      decorationTypes: [DecorationType.FENCE, DecorationType.HEDGE, DecorationType.WALL],
      sortOrder: 1,
      isNew: false,
      isPopular: true
    },
    {
      id: 'plants',
      name: '植物',
      icon: '🌸',
      decorationTypes: [DecorationType.ORNAMENTAL_PLANT, DecorationType.FLOWER_BED, DecorationType.TREE, DecorationType.BUSH],
      sortOrder: 2,
      isNew: false,
      isPopular: true
    },
    {
      id: 'functional',
      name: '功能',
      icon: '⛲',
      decorationTypes: [DecorationType.LAMP, DecorationType.FOUNTAIN, DecorationType.STATUE, DecorationType.BENCH],
      sortOrder: 3,
      isNew: false,
      isPopular: false
    },
    {
      id: 'paths',
      name: '道路',
      icon: '🛤️',
      decorationTypes: [DecorationType.STONE_PATH, DecorationType.WOODEN_PATH, DecorationType.BRICK_PATH],
      sortOrder: 4,
      isNew: false,
      isPopular: false
    },
    {
      id: 'seasonal',
      name: '季节',
      icon: '🎄',
      decorationTypes: [DecorationType.SEASONAL, DecorationType.HOLIDAY],
      sortOrder: 5,
      isNew: true,
      isPopular: false
    },
    {
      id: 'cultural',
      name: '文化',
      icon: '🏮',
      decorationTypes: [DecorationType.CHINESE_STYLE, DecorationType.WESTERN_STYLE, DecorationType.MODERN_STYLE, DecorationType.RUSTIC_STYLE],
      sortOrder: 6,
      isNew: false,
      isPopular: false
    }
  ]

  // 品质颜色映射
  const rarityColors: Record<ItemRarity, string> = {
    [ItemRarity.GRAY]: '#9E9E9E',
    [ItemRarity.GREEN]: '#4CAF50',
    [ItemRarity.BLUE]: '#2196F3',
    [ItemRarity.ORANGE]: '#FF9800',
    [ItemRarity.GOLD]: '#FFD700',
    [ItemRarity.GOLD_RED]: '#FF6B6B'
  }

  // 初始化数据
  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    setLoading(true)
    try {
      const [decorations, themes, owned] = await Promise.all([
        decorationSystem.getAvailableDecorations(),
        decorationSystem.getAvailableThemes(),
        Promise.resolve(decorationSystem.getOwnedDecorations())
      ])
      
      setAvailableDecorations(decorations)
      setAvailableThemes(themes)
      setOwnedDecorations(owned)
    } catch (error) {
      console.error('加载装饰商店数据失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 过滤和排序装饰道具
  const filteredDecorations = useMemo(() => {
    let items = availableDecorations

    // 按分类过滤
    if (selectedCategory !== 'all') {
      const category = categories.find(cat => cat.id === selectedCategory)
      if (category && category.decorationTypes.length > 0) {
        items = items.filter(item => category.decorationTypes.includes(item.type))
      }
    }

    // 按搜索词过滤
    if (searchTerm) {
      items = items.filter(item => 
        item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.description.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    // 排序
    items.sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name)
        case 'price':
          return a.economy.basePrice - b.economy.basePrice
        case 'rarity':
          const rarityOrder = {
            [ItemRarity.GRAY]: 1,
            [ItemRarity.GREEN]: 2,
            [ItemRarity.BLUE]: 3,
            [ItemRarity.ORANGE]: 4,
            [ItemRarity.GOLD]: 5,
            [ItemRarity.GOLD_RED]: 6
          }
          return rarityOrder[b.rarity] - rarityOrder[a.rarity]
        default:
          return 0
      }
    })

    return items
  }, [availableDecorations, selectedCategory, searchTerm, sortBy, categories])

  // 处理购买装饰道具
  const handlePurchaseDecoration = async (item: DecorationItem, quantity: number = 1) => {
    setPurchaseProcessing(item.id)
    try {
      const result = await decorationSystem.purchaseDecoration(item.id, quantity)
      
      if (result.success) {
        // 刷新数据
        await loadData()
        
        // 触发回调
        if (onPurchase) {
          onPurchase(item, quantity)
        }
        
        // 显示成功消息
        alert(result.message)
      } else {
        alert(result.message)
      }
    } catch (error) {
      console.error('购买失败:', error)
      alert('购买失败，请稍后重试')
    } finally {
      setPurchaseProcessing(null)
    }
  }

  // 处理购买主题
  const handlePurchaseTheme = async (theme: FarmTheme) => {
    setPurchaseProcessing(theme.id)
    try {
      const result = await decorationSystem.purchaseTheme(theme.id)
      
      if (result.success) {
        await loadData()
        alert(result.message)
      } else {
        alert(result.message)
      }
    } catch (error) {
      console.error('购买主题失败:', error)
      alert('购买主题失败，请稍后重试')
    } finally {
      setPurchaseProcessing(null)
    }
  }

  // 处理应用主题
  const handleApplyTheme = (themeId: string) => {
    const result = decorationSystem.applyTheme(themeId)
    if (result.success) {
      alert(result.message)
    } else {
      alert(result.message)
    }
  }

  // 渲染装饰道具卡片
  const renderDecorationCard = (item: DecorationItem) => (
    <div key={item.id} className="decoration-card" style={{ borderColor: rarityColors[item.rarity] }}>
      <div className="decoration-image">
        <img src={item.visual.sprite} alt={item.name} onError={(e) => {
          (e.target as HTMLImageElement).src = '/placeholder-decoration.png'
        }} />
        <div className="rarity-badge" style={{ backgroundColor: rarityColors[item.rarity] }}>
          {item.rarity.toUpperCase()}
        </div>
      </div>
      
      <div className="decoration-info">
        <h3 className="decoration-name">{item.name}</h3>
        <p className="decoration-description">{item.description}</p>
        
        <div className="decoration-stats">
          <div className="stat">
            <span className="stat-label">美观度:</span>
            <span className="stat-value">+{item.beautyValue}</span>
          </div>
          <div className="stat">
            <span className="stat-label">等级:</span>
            <span className="stat-value">{item.level}/{item.maxLevel}</span>
          </div>
          {item.effects && item.effects.length > 0 && (
            <div className="effects">
              <span className="effects-label">特殊效果:</span>
              <ul className="effects-list">
                {item.effects.map((effect, index) => (
                  <li key={index} className="effect-item">
                    {effect.description}
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>

        <div className="decoration-price">
          <div className="price-info">
            <span className="price-amount">{item.economy.basePrice}</span>
            <span className="price-currency">专注代币</span>
          </div>
          <button 
            className="purchase-btn"
            onClick={() => handlePurchaseDecoration(item)}
            disabled={purchaseProcessing === item.id}
          >
            {purchaseProcessing === item.id ? '购买中...' : '购买'}
          </button>
        </div>

        {item.economy.unlockLevel > 1 && (
          <div className="unlock-requirement">
            需要农场等级 {item.economy.unlockLevel}
          </div>
        )}
      </div>
    </div>
  )

  // 渲染主题卡片
  const renderThemeCard = (theme: FarmTheme) => {
    const manager = decorationSystem.getManager()
    const isUnlocked = manager.unlockedThemes.includes(theme.id)
    const isActive = manager.currentTheme === theme.id
    
    return (
      <div key={theme.id} className={`theme-card ${isActive ? 'active' : ''}`}>
        <div className="theme-preview">
          <img src={theme.visual.backgroundTexture} alt={theme.name} onError={(e) => {
            (e.target as HTMLImageElement).src = '/placeholder-theme.png'
          }} />
          {isActive && <div className="active-badge">当前主题</div>}
        </div>
        
        <div className="theme-info">
          <h3 className="theme-name">{theme.name}</h3>
          <p className="theme-description">{theme.description}</p>
          
          <div className="theme-effects">
            {theme.themeEffects && theme.themeEffects.length > 0 && (
              <div className="effects">
                <span className="effects-label">主题效果:</span>
                <ul className="effects-list">
                  {theme.themeEffects.map((effect, index) => (
                    <li key={index} className="effect-item">
                      {effect.description}
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>

          <div className="theme-actions">
            {!isUnlocked ? (
              <div className="theme-purchase">
                <div className="theme-price">
                  {theme.price > 0 ? (
                    <>
                      <span className="price-amount">{theme.price}</span>
                      <span className="price-currency">专注代币</span>
                    </>
                  ) : (
                    <span className="price-free">免费</span>
                  )}
                </div>
                <button 
                  className="purchase-btn"
                  onClick={() => handlePurchaseTheme(theme)}
                  disabled={purchaseProcessing === theme.id}
                >
                  {purchaseProcessing === theme.id ? '解锁中...' : '解锁主题'}
                </button>
              </div>
            ) : !isActive ? (
              <button 
                className="apply-btn"
                onClick={() => handleApplyTheme(theme.id)}
              >
                应用主题
              </button>
            ) : (
              <div className="active-indicator">正在使用</div>
            )}
          </div>
        </div>
      </div>
    )
  }

  // 渲染已拥有的装饰道具
  const renderOwnedItem = (owned: { item: DecorationItem, quantity: number }) => (
    <div key={owned.item.id} className="owned-item">
      <div className="owned-image">
        <img src={owned.item.visual.sprite} alt={owned.item.name} onError={(e) => {
          (e.target as HTMLImageElement).src = '/placeholder-decoration.png'
        }} />
        <div className="quantity-badge">{owned.quantity}</div>
      </div>
      <div className="owned-info">
        <h4 className="owned-name">{owned.item.name}</h4>
        <p className="owned-description">{owned.item.description}</p>
        <div className="owned-actions">
          <button 
            className="use-btn"
            onClick={() => {
              // 这里可以触发放置模式
              alert(`点击农场空地放置 ${owned.item.name}`)
            }}
          >
            使用
          </button>
        </div>
      </div>
    </div>
  )

  if (loading) {
    return (
      <div className="decoration-shop loading">
        <div className="loading-content">
          <div className="loading-spinner"></div>
          <p>加载装饰商店中...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="decoration-shop">
      <div className="shop-header">
        <h2 className="shop-title">🏪 装饰商店</h2>
        <button className="close-btn" onClick={onClose}>×</button>
      </div>

      <div className="shop-tabs">
        <button 
          className={`tab ${activeTab === 'decorations' ? 'active' : ''}`}
          onClick={() => setActiveTab('decorations')}
        >
          装饰道具
        </button>
        <button 
          className={`tab ${activeTab === 'themes' ? 'active' : ''}`}
          onClick={() => setActiveTab('themes')}
        >
          农场主题
        </button>
        <button 
          className={`tab ${activeTab === 'owned' ? 'active' : ''}`}
          onClick={() => setActiveTab('owned')}
        >
          已拥有 ({ownedDecorations.length})
        </button>
      </div>

      {activeTab === 'decorations' && (
        <div className="decorations-tab">
          <div className="decorations-controls">
            <div className="search-bar">
              <input
                type="text"
                placeholder="搜索装饰道具..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="search-input"
              />
            </div>
            
            <div className="filter-controls">
              <select 
                value={sortBy} 
                onChange={(e) => setSortBy(e.target.value as 'name' | 'price' | 'rarity')}
                className="sort-select"
              >
                <option value="name">按名称排序</option>
                <option value="price">按价格排序</option>
                <option value="rarity">按品质排序</option>
              </select>
            </div>
          </div>

          <div className="categories">
            {categories.map(category => (
              <button
                key={category.id}
                className={`category-btn ${selectedCategory === category.id ? 'active' : ''} ${category.isNew ? 'new' : ''} ${category.isPopular ? 'popular' : ''}`}
                onClick={() => setSelectedCategory(category.id as DecorationType | 'all')}
              >
                <span className="category-icon">{category.icon}</span>
                <span className="category-name">{category.name}</span>
                {category.isNew && <span className="new-badge">新</span>}
                {category.isPopular && <span className="popular-badge">热</span>}
              </button>
            ))}
          </div>

          <div className="decorations-grid">
            {filteredDecorations.length === 0 ? (
              <div className="empty-state">
                <p>没有找到符合条件的装饰道具</p>
              </div>
            ) : (
              filteredDecorations.map(renderDecorationCard)
            )}
          </div>
        </div>
      )}

      {activeTab === 'themes' && (
        <div className="themes-tab">
          <div className="themes-grid">
            {availableThemes.length === 0 ? (
              <div className="empty-state">
                <p>暂无可用主题</p>
              </div>
            ) : (
              availableThemes.map(renderThemeCard)
            )}
          </div>
        </div>
      )}

      {activeTab === 'owned' && (
        <div className="owned-tab">
          <div className="owned-list">
            {ownedDecorations.length === 0 ? (
              <div className="empty-state">
                <p>还没有拥有任何装饰道具</p>
                <button onClick={() => setActiveTab('decorations')}>
                  去购买装饰道具
                </button>
              </div>
            ) : (
              ownedDecorations.map(renderOwnedItem)
            )}
          </div>
        </div>
      )}
    </div>
  )
}

export default DecorationShop 