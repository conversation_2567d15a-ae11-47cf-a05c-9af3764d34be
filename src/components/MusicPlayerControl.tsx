import React, { useState, useCallback } from 'react'
import { useAudioManager } from '../hooks/useAudioManager'
import { BACKGROUND_MUSIC } from '../audio/audioConfig'

interface MusicPlayerControlProps {
  className?: string
  showMinimal?: boolean
}

/**
 * 音乐播放控制组件
 * 提供背景音乐的播放控制界面
 */
export const MusicPlayerControl: React.FC<MusicPlayerControlProps> = ({ 
  className = '', 
  showMinimal = false 
}) => {
  const { 
    playMusic, 
    stopMusic, 
    toggleMusic, 
    setVolume, 
    setEnabled,
    audioStatus, 
    audioSettings 
  } = useAudioManager()

  const [isExpanded, setIsExpanded] = useState(!showMinimal)
  const [selectedTrack, setSelectedTrack] = useState<string>(audioSettings.currentMusic || 'farm_ambient')

  // 播放选中的音乐
  const handlePlayTrack = useCallback(async (trackId: string) => {
    const success = await playMusic(trackId, true)
    if (success) {
      setSelectedTrack(trackId)
    }
  }, [playMusic])

  // 切换播放/暂停
  const handleTogglePlayback = useCallback(() => {
    if (audioStatus.isPlaying) {
      // 如果正在播放，暂停/恢复
      toggleMusic()
    } else {
      // 如果没有播放，开始播放选中的音乐
      handlePlayTrack(selectedTrack)
    }
  }, [audioStatus.isPlaying, toggleMusic, handlePlayTrack, selectedTrack])

  // 停止播放
  const handleStop = useCallback(() => {
    stopMusic()
  }, [stopMusic])

  // 音量调节
  const handleVolumeChange = useCallback((value: number) => {
    setVolume('music', value / 100)
  }, [setVolume])

  // 主音量调节
  const handleMasterVolumeChange = useCallback((value: number) => {
    setVolume('master', value / 100)
  }, [setVolume])

  // 启用/禁用音乐
  const handleToggleEnabled = useCallback(() => {
    setEnabled('music', !audioSettings.musicEnabled)
  }, [setEnabled, audioSettings.musicEnabled])

  // 获取当前播放的音轨信息
  const getCurrentTrack = useCallback(() => {
    const currentId = audioStatus.currentMusic || selectedTrack
    return BACKGROUND_MUSIC.find(track => track.id === currentId)
  }, [audioStatus.currentMusic, selectedTrack])

  const currentTrack = getCurrentTrack()

  const buttonStyle: React.CSSProperties = {
    background: '#3498db',
    border: 'none',
    color: 'white',
    padding: '8px 16px',
    borderRadius: '6px',
    cursor: 'pointer',
    fontSize: '0.9em',
    transition: 'all 0.2s',
    flex: 1,
    minWidth: '80px'
  }

  const controlPanelStyle: React.CSSProperties = {
    background: 'linear-gradient(135deg, #2c3e50, #34495e)',
    borderRadius: '12px',
    padding: '20px',
    margin: '16px',
    color: 'white',
    fontFamily: "'Segoe UI', system-ui, sans-serif",
    boxShadow: '0 4px 12px rgba(0,0,0,0.3)',
    maxWidth: '400px'
  }

  // 最小化模式
  if (showMinimal && !isExpanded) {
    return (
      <div className={className} style={{ display: 'flex', gap: '8px', alignItems: 'center' }}>
        <button
          onClick={handleTogglePlayback}
          disabled={!audioStatus.isInitialized}
          title={audioStatus.isPlaying ? '暂停音乐' : '播放音乐'}
          style={{
            width: '32px',
            height: '32px',
            border: 'none',
            borderRadius: '50%',
            background: 'rgba(0, 0, 0, 0.7)',
            color: 'white',
            cursor: 'pointer',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            opacity: audioStatus.isInitialized ? 1 : 0.5
          }}
        >
          {audioStatus.isPlaying ? '⏸️' : '▶️'}
        </button>
        <button
          onClick={() => setIsExpanded(true)}
          title="展开音乐控制"
          style={{
            width: '32px',
            height: '32px',
            border: 'none',
            borderRadius: '50%',
            background: 'rgba(0, 0, 0, 0.7)',
            color: 'white',
            cursor: 'pointer',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}
        >
          🎵
        </button>
      </div>
    )
  }

  // 完整模式
  return (
    <div className={className} style={controlPanelStyle}>
      {/* 标题和状态 */}
      <div style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center', 
        marginBottom: '16px',
        borderBottom: '1px solid rgba(255,255,255,0.2)',
        paddingBottom: '12px'
      }}>
        <h3 style={{ margin: 0, fontSize: '1.2em', fontWeight: 600 }}>🎵 背景音乐</h3>
        {showMinimal && (
          <button
            onClick={() => setIsExpanded(false)}
            title="最小化"
            style={{
              background: 'none',
              border: 'none',
              color: 'white',
              cursor: 'pointer',
              fontSize: '16px',
              padding: '4px',
              borderRadius: '4px'
            }}
          >
            ➖
          </button>
        )}
      </div>

      {/* 系统状态 */}
      <div style={{ 
        marginBottom: '16px', 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center', 
        fontSize: '0.9em' 
      }}>
        <div style={{ color: audioStatus.isInitialized ? '#2ecc71' : '#f39c12' }}>
          {audioStatus.isInitialized ? '🟢 就绪' : '🔄 初始化中...'}
        </div>
        <div style={{ color: '#bdc3c7' }}>
          音频状态: {audioStatus.audioContextState || '未知'}
        </div>
      </div>

      {/* 当前播放信息 */}
      {currentTrack && (
        <div style={{ 
          background: 'rgba(255,255,255,0.1)', 
          padding: '12px', 
          borderRadius: '8px', 
          marginBottom: '16px' 
        }}>
          <div style={{ fontWeight: 600, marginBottom: '4px' }}>{currentTrack.name}</div>
          <div style={{ fontSize: '0.9em', color: '#bdc3c7', marginBottom: '8px' }}>
            {currentTrack.description}
          </div>
          <div style={{ fontSize: '0.9em', color: '#3498db' }}>
            {audioStatus.isPlaying ? '🎵 播放中' : '⏸️ 已暂停'}
          </div>
        </div>
      )}

      {/* 播放控制 */}
      <div style={{ display: 'flex', gap: '8px', marginBottom: '16px', flexWrap: 'wrap' }}>
        <button
          onClick={handleTogglePlayback}
          disabled={!audioStatus.isInitialized}
          title={audioStatus.isPlaying ? '暂停' : '播放'}
          style={{
            ...buttonStyle,
            backgroundColor: audioStatus.isInitialized ? '#3498db' : '#7f8c8d'
          }}
        >
          {audioStatus.isPlaying ? '⏸️ 暂停' : '▶️ 播放'}
        </button>
        
        <button
          onClick={handleStop}
          disabled={!audioStatus.isInitialized || !audioStatus.isPlaying}
          title="停止"
          style={{
            ...buttonStyle,
            backgroundColor: (!audioStatus.isInitialized || !audioStatus.isPlaying) ? '#7f8c8d' : '#e74c3c'
          }}
        >
          ⏹️ 停止
        </button>

        <button
          onClick={handleToggleEnabled}
          title={audioSettings.musicEnabled ? '禁用音乐' : '启用音乐'}
          style={{
            ...buttonStyle,
            backgroundColor: audioSettings.musicEnabled ? '#27ae60' : '#e67e22'
          }}
        >
          {audioSettings.musicEnabled ? '🔊 启用' : '🔇 禁用'}
        </button>
      </div>

      {/* 音乐选择 */}
      <div style={{ marginBottom: '16px' }}>
        <label style={{ display: 'block', marginBottom: '8px', fontWeight: 500 }}>
          选择音乐:
        </label>
        <select
          value={selectedTrack}
          onChange={(e) => setSelectedTrack(e.target.value)}
          style={{
            width: '100%',
            padding: '8px',
            border: 'none',
            borderRadius: '4px',
            background: 'rgba(255,255,255,0.1)',
            color: 'white',
            marginBottom: '8px'
          }}
        >
          {BACKGROUND_MUSIC.map(track => (
            <option key={track.id} value={track.id} style={{ background: '#34495e', color: 'white' }}>
              {track.name}
            </option>
          ))}
        </select>
        <button
          onClick={() => handlePlayTrack(selectedTrack)}
          disabled={!audioStatus.isInitialized || selectedTrack === audioStatus.currentMusic}
          title="播放选中的音乐"
          style={{
            ...buttonStyle,
            backgroundColor: (!audioStatus.isInitialized || selectedTrack === audioStatus.currentMusic) ? '#7f8c8d' : '#3498db'
          }}
        >
          播放
        </button>
      </div>

      {/* 音量控制 */}
      <div style={{ marginBottom: '16px' }}>
        <div style={{ marginBottom: '12px' }}>
          <label style={{ display: 'block', marginBottom: '6px', fontSize: '0.9em', fontWeight: 500 }}>
            🎵 音乐音量: {Math.round(audioSettings.musicVolume * 100)}%
          </label>
          <input
            type="range"
            min="0"
            max="100"
            value={Math.round(audioSettings.musicVolume * 100)}
            onChange={(e) => handleVolumeChange(Number(e.target.value))}
            disabled={!audioSettings.musicEnabled}
            style={{
              width: '100%',
              height: '6px',
              borderRadius: '3px',
              background: 'rgba(255,255,255,0.2)',
              outline: 'none',
              opacity: audioSettings.musicEnabled ? 1 : 0.5
            }}
          />
        </div>

        <div style={{ marginBottom: '12px' }}>
          <label style={{ display: 'block', marginBottom: '6px', fontSize: '0.9em', fontWeight: 500 }}>
            🔊 主音量: {Math.round(audioSettings.masterVolume * 100)}%
          </label>
          <input
            type="range"
            min="0"
            max="100"
            value={Math.round(audioSettings.masterVolume * 100)}
            onChange={(e) => handleMasterVolumeChange(Number(e.target.value))}
            style={{
              width: '100%',
              height: '6px',
              borderRadius: '3px',
              background: 'rgba(255,255,255,0.2)',
              outline: 'none'
            }}
          />
        </div>
      </div>

      {/* 音乐列表 */}
      <div>
        <h4 style={{ margin: '0 0 12px 0', fontSize: '1em', fontWeight: 600 }}>可用音乐:</h4>
        <div style={{ maxHeight: '200px', overflowY: 'auto' }}>
          {BACKGROUND_MUSIC.map(track => (
            <div 
              key={track.id} 
              style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                padding: '8px',
                marginBottom: '4px',
                background: track.id === audioStatus.currentMusic ? 'rgba(52, 152, 219, 0.3)' : 'rgba(255,255,255,0.05)',
                borderRadius: '6px',
                borderLeft: track.id === audioStatus.currentMusic ? '3px solid #3498db' : 'none'
              }}
            >
              <div style={{ flex: 1 }}>
                <div style={{ fontWeight: 500, marginBottom: '2px' }}>{track.name}</div>
                <div style={{ fontSize: '0.8em', color: '#bdc3c7' }}>{track.description}</div>
              </div>
              <button
                onClick={() => handlePlayTrack(track.id)}
                disabled={!audioStatus.isInitialized}
                title={`播放 ${track.name}`}
                style={{
                  background: audioStatus.isInitialized ? '#3498db' : '#7f8c8d',
                  border: 'none',
                  color: 'white',
                  width: '32px',
                  height: '32px',
                  borderRadius: '50%',
                  cursor: audioStatus.isInitialized ? 'pointer' : 'not-allowed',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}
              >
                {track.id === audioStatus.currentMusic && audioStatus.isPlaying ? '⏸️' : '▶️'}
              </button>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

export default MusicPlayerControl 