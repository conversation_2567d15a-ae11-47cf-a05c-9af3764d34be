import React from 'react'

interface LoadingSpinnerProps {
  size?: 'small' | 'medium' | 'large'
  message?: string
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({ 
  size = 'medium', 
  message = '加载中...' 
}) => {
  const getSize = () => {
    switch (size) {
      case 'small': return '24px'
      case 'large': return '64px'
      default: return '40px'
    }
  }

  return (
    <div className="loading-spinner-container">
      <div 
        className="loading-spinner"
        style={{
          width: getSize(),
          height: getSize(),
          border: `4px solid #f3f3f3`,
          borderTop: `4px solid #4CAF50`,
          borderRadius: '50%',
          animation: 'spin 1s linear infinite'
        }}
      />
      {message && <p className="loading-message">{message}</p>}
      <style>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
        .loading-spinner-container {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          padding: 20px;
        }
        .loading-message {
          margin-top: 12px;
          color: #666;
          font-size: 14px;
        }
      `}</style>
    </div>
  )
}

export default LoadingSpinner 