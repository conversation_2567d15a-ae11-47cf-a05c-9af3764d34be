import React, { useEffect, useRef, useState } from 'react'
import Phaser from 'phaser'
import { EnhancedFarmScene } from '../game/scenes/EnhancedFarmScene'
import { UnifiedAgriculturalScene } from '../game/scenes/UnifiedAgriculturalScene'
import { gameConfig } from '../game/GameConfig'

import { CameraView } from '../components/CameraView'
import { ApplicationMonitor } from '../components/monitoring/ApplicationMonitor'
import { FocusMode } from '../components/focus/FocusMode'
import { UserTesting } from '../components/testing/UserTesting'
import { TestPlan } from '../components/testing/TestPlan'
import FeedbackAnalysis from '../components/FeedbackAnalysis'
import { LootboxTester } from '../components/LootboxTester'
import { InventorySystem } from '../systems/InventorySystem'
import { InventoryPanel } from '../components/InventoryPanel'
import { ItemRarity, ItemCategory, ItemType } from '../types/lootbox'
import { useGame } from '../contexts/GameContext'
import { useTutorial } from '../contexts/TutorialContext'
import { PoseResults, PostureAnalysis } from '../types/pose'
import { 
  Container, 
  Flex, 
  Grid,
  Show,
  Breakpoint,
  ResponsiveGameCanvas,
  ResponsiveSidebar
} from '../components/ResponsiveLayout'
import { useDeviceType, useOrientation, isTouchDevice } from '../utils/responsive'
import { fullTutorialSteps } from '../config/tutorialSteps'
import UnifiedGameSystem from '../pages/UnifiedGameSystem'

// 主游戏界面组件
const MainGameInterface: React.FC = () => {
  const gameRef = useRef<Phaser.Game | null>(null)
  const phaserRef = useRef<HTMLDivElement>(null)
  const [cameraStatus, setCameraStatus] = useState<string>('idle')
  const [showCamera, setShowCamera] = useState(false)
  const [showAppMonitor, setShowAppMonitor] = useState(false)
  const [showFocusMode, setShowFocusMode] = useState(false)
  const [showUserTesting, setShowUserTesting] = useState(false)
  const [showTestPlan, setShowTestPlan] = useState(false)
  const [showFeedbackAnalysis, setShowFeedbackAnalysis] = useState(false)
  const [showLootboxTester, setShowLootboxTester] = useState(false)
  const [showInventory, setShowInventory] = useState(false)
  const [showUnifiedGame, setShowUnifiedGame] = useState(false)
  
  // 创建背包系统实例
  const [inventorySystem] = useState(() => {
    const system = new InventorySystem(200)
    
    // 添加一些测试物品
    console.log('开始添加测试物品到库存系统...')
    
    // 灰色品质物品
    system.addItem({
      id: 'wheat-common',
      name: '普通小麦',
      icon: '🌾',
      rarity: ItemRarity.GRAY,
      category: ItemCategory.AGRICULTURAL,
      type: ItemType.CROP,
      description: '常见的农产品',
      value: 10,
      stackable: true,
      tradeable: true,
      synthesizable: true,
      metadata: {
        yieldMultiplier: 1.0,
        futuresPrice: 2850
      }
    }, 15)
    
    // 绿色品质物品
    system.addItem({
      id: 'rice-green',
      name: '优质稻米',
      icon: '🍚',
      rarity: ItemRarity.GREEN,
      category: ItemCategory.AGRICULTURAL,
      type: ItemType.CROP,
      description: '绿色有机稻米',
      value: 25,
      stackable: true,
      tradeable: true,
      synthesizable: true,
      metadata: {
        yieldMultiplier: 1.2,
        futuresPrice: 2950
      }
    }, 12)
    
    console.log('测试物品添加完成')
    return system
  })
  
  // 响应式状态
  const deviceType = useDeviceType()
  const orientation = useOrientation()
  const isTouch = isTouchDevice()
  
  // 使用游戏上下文
  const { 
    state, 
    updatePosture, 
    startFocusSession, 
    endFocusSession,
    getFocusTimeFormatted,
    getCurrentStreakFormatted,
    shouldTriggerGrowth,
    growPlant
  } = useGame()

  // 使用引导上下文
  const { 
    state: tutorialState, 
    startTutorial, 
    updateProgress,
    finishTutorial
  } = useTutorial()

  // 启动新手引导
  useEffect(() => {
    if (tutorialState.isFirstTimeUser && !tutorialState.tutorialProgress.completed) {
      const timer = setTimeout(() => {
        startTutorial(fullTutorialSteps)
      }, 1000)
      
      return () => clearTimeout(timer)
    }
  }, [tutorialState.isFirstTimeUser, tutorialState.tutorialProgress.completed, startTutorial])

  useEffect(() => {
    if (phaserRef.current && !gameRef.current) {
      const getGameSize = () => {
        const baseWidth = 800
        const baseHeight = 600
        
        if (deviceType === 'mobile') {
          const scale = Math.min(window.innerWidth / baseWidth, 0.9)
          return {
            width: Math.floor(baseWidth * scale),
            height: Math.floor(baseHeight * scale)
          }
        }
        
        if (deviceType === 'tablet') {
          return {
            width: Math.min(baseWidth, window.innerWidth * 0.8),
            height: Math.min(baseHeight, window.innerHeight * 0.7)
          }
        }
        
        return { width: baseWidth, height: baseHeight }
      }
      
      const { width, height } = getGameSize()
      
      const config: Phaser.Types.Core.GameConfig = {
        type: Phaser.AUTO,
        width,
        height,
        parent: phaserRef.current,
        backgroundColor: '#87CEEB',
        scene: [EnhancedFarmScene],
        scale: {
          mode: Phaser.Scale.FIT,
          autoCenter: Phaser.Scale.CENTER_BOTH,
          width,
          height
        },
        physics: {
          default: 'arcade',
          arcade: {
            gravity: { x: 0, y: 0 },
            debug: false
          }
        }
      }

      gameRef.current = new Phaser.Game(config)
      
      if (gameRef.current.scene.scenes[0]) {
        const farmScene = gameRef.current.scene.scenes[0] as EnhancedFarmScene
        
        farmScene.events.on('plantGrown', (data: { type: string; position: { x: number; y: number } }) => {
          growPlant(data.type)
        })
      }
    }

    return () => {
      if (gameRef.current) {
        gameRef.current.destroy(true)
        gameRef.current = null
      }
    }
  }, [])

  useEffect(() => {
    if (gameRef.current?.scene.scenes[0]) {
      const farmScene = gameRef.current.scene.scenes[0] as EnhancedFarmScene
      
      if (shouldTriggerGrowth()) {
        // farmScene.triggerPlantGrowth?.()
      }
    }
  }, [state, shouldTriggerGrowth])

  const handleCameraStatusChange = (status: string) => {
    setCameraStatus(status)
    
    if (status === 'granted' && !state.focusSession.isActive) {
      startFocusSession()
    } else if (status !== 'granted' && state.focusSession.isActive) {
      endFocusSession()
    }
  }

  const handlePoseDetected = (_results: PoseResults, analysis: PostureAnalysis) => {
    updatePosture(analysis)
  }

  const launchAgriculturalSystem = () => {
    if (gameRef.current) {
      gameRef.current.destroy(true)
      gameRef.current = null
    }

    if (phaserRef.current) {
      const config: Phaser.Types.Core.GameConfig = {
        ...gameConfig,
        parent: phaserRef.current,
        scene: [UnifiedAgriculturalScene]
      }

      gameRef.current = new Phaser.Game(config)
      console.log('🌾 农产品系统已启动')
    }
  }

  // 如果显示统一游戏系统，直接返回该页面
  if (showUnifiedGame) {
    return (
      <div style={{ position: 'relative' }}>
        <button
          onClick={() => setShowUnifiedGame(false)}
          style={{
            position: 'fixed',
            top: '20px',
            left: '20px',
            zIndex: 10000,
            padding: '10px 20px',
            backgroundColor: '#4CAF50',
            color: 'white',
            border: 'none',
            borderRadius: '6px',
            cursor: 'pointer',
            fontSize: '1rem',
            fontWeight: 'bold',
            boxShadow: '0 2px 8px rgba(0,0,0,0.2)'
          }}
        >
          ← 返回主页
        </button>
        <UnifiedGameSystem />
      </div>
    )
  }

  return (
    <>
      <Container>
        <header className="app-header">
          <Breakpoint
            mobile={
              <div>
                <h1 style={{ fontSize: '1.8rem' }}>🌱 自律农场</h1>
                <p style={{ fontSize: '1rem' }}>让自律变成快乐的游戏体验！</p>
              </div>
            }
            tablet={
              <div>
                <h1 style={{ fontSize: '2.2rem' }}>🌱 自律农场 - 习惯养成</h1>
                <p style={{ fontSize: '1.1rem' }}>通过摄像头监测，让自律变成快乐的游戏体验！</p>
              </div>
            }
            desktop={
              <div>
                <h1>🌱 自律农场 - 习惯养成游戏</h1>
                <p>通过摄像头监测，让自律变成快乐的游戏体验！</p>
              </div>
            }
          >
            <div>
              <h1>🌱 自律农场 - 习惯养成游戏</h1>
              <p>通过摄像头监测，让自律变成快乐的游戏体验！</p>
            </div>
          </Breakpoint>
          
          <Flex 
            className="camera-status-bar"
            direction={deviceType === 'mobile' ? 'column' : 'row'}
            align="center"
            justify={deviceType === 'mobile' ? 'center' : 'between'}
            gap={deviceType === 'mobile' ? 8 : 16}
          >
            <Flex align="center" gap={8}>
              <span className={`status-indicator status-${cameraStatus}`}>
                {cameraStatus === 'granted' ? '🟢' : 
                 cameraStatus === 'requesting' ? '🟡' : 
                 cameraStatus === 'denied' || cameraStatus === 'error' ? '🔴' : '⚪'}
              </span>
              <span style={{ fontSize: deviceType === 'mobile' ? '0.9rem' : '1rem' }}>
                摄像头状态: {cameraStatus === 'idle' ? '未启动' :
                            cameraStatus === 'requesting' ? '请求中...' :
                            cameraStatus === 'granted' ? '已连接' :
                            cameraStatus === 'denied' ? '权限被拒绝' :
                            cameraStatus === 'error' ? '错误' : '不可用'}
              </span>
            </Flex>
            <Flex gap={8}>
              <button 
                className="toggle-camera-btn"
                onClick={() => setShowCamera(!showCamera)}
                style={{ 
                  padding: isTouch ? '12px 16px' : '8px 16px',
                  fontSize: deviceType === 'mobile' ? '0.9rem' : '1rem'
                }}
              >
                {showCamera ? '隐藏摄像头' : '显示摄像头'}
              </button>
              <button 
                onClick={() => setShowUnifiedGame(true)}
                style={{ 
                  padding: isTouch ? '12px 16px' : '8px 16px',
                  fontSize: deviceType === 'mobile' ? '0.9rem' : '1rem',
                  backgroundColor: '#FF9800',
                  color: 'white',
                  border: 'none',
                  borderRadius: '4px',
                  cursor: 'pointer',
                  fontWeight: 'bold'
                }}
              >
                🎮 期货游戏系统
              </button>
            </Flex>
          </Flex>
        </header>
        
        <main className="app-main">
          {/* 应用监控面板 */}
          {showAppMonitor && (
            <div className="app-monitor-overlay">
              <div className="monitor-header">
                <h2>🖥️ 应用监控系统</h2>
                <button 
                  className="close-monitor-btn"
                  onClick={() => setShowAppMonitor(false)}
                >
                  ✕ 关闭
                </button>
              </div>
              <ApplicationMonitor />
            </div>
          )}
          
          {/* 专注模式面板 */}
          {showFocusMode && (
            <div className="focus-mode-overlay">
              <div className="focus-mode-header">
                <h2>🎯 手机专注模式</h2>
                <button 
                  className="close-focus-btn"
                  onClick={() => setShowFocusMode(false)}
                >
                  ✕ 关闭
                </button>
              </div>
              <FocusMode />
            </div>
          )}

          {/* 其他面板... */}
          {showUserTesting && (
            <div className="user-testing-overlay">
              <div className="testing-header">
                <h2>👥 用户测试中心</h2>
                <button onClick={() => setShowUserTesting(false)}>✕ 关闭</button>
              </div>
              <UserTesting />
            </div>
          )}

          {showTestPlan && (
            <div className="test-plan-overlay">
              <div className="plan-header">
                <h2>📋 测试计划管理</h2>
                <button onClick={() => setShowTestPlan(false)}>✕ 关闭</button>
              </div>
              <TestPlan />
            </div>
          )}

          {showFeedbackAnalysis && (
            <div className="feedback-analysis-overlay">
              <div className="analysis-header">
                <h2>📊 反馈分析报告</h2>
                <button onClick={() => setShowFeedbackAnalysis(false)}>✕ 关闭</button>
              </div>
              <FeedbackAnalysis />
            </div>
          )}

          {showLootboxTester && (
            <div className="lootbox-tester-overlay">
              <div className="tester-header">
                <h2>🎁 期货农产品盲盒测试</h2>
                <button onClick={() => setShowLootboxTester(false)}>✕ 关闭</button>
              </div>
              <LootboxTester />
            </div>
          )}
          
          <Flex 
            direction={deviceType === 'desktop' ? 'row' : 'column'}
            gap={deviceType === 'mobile' ? 16 : deviceType === 'tablet' ? 24 : 32}
            align="start"
          >
            {/* 摄像头区域 */}
            <Show on={showCamera ? ['mobile', 'tablet', 'desktop'] : []}>
              <section className="camera-section" style={{ flex: deviceType === 'desktop' ? '0 0 300px' : '1' }}>
                <h2 style={{ fontSize: deviceType === 'mobile' ? '1.2rem' : '1.5rem' }}>
                  📹 行为监测
                </h2>
                <CameraView 
                  width={deviceType === 'mobile' ? 280 : 320}
                  height={deviceType === 'mobile' ? 210 : 240}
                  onStatusChange={handleCameraStatusChange}
                  onPoseDetected={handlePoseDetected}
                  showControls={true}
                  className="main-camera"
                />
              </section>
            </Show>

            {/* 游戏区域 */}
            <section className="game-section" style={{ flex: '1', minWidth: 0 }}>
              <h2 style={{ fontSize: deviceType === 'mobile' ? '1.2rem' : '1.5rem' }}>
                🎮 农场管理
              </h2>
              <ResponsiveGameCanvas
                baseWidth={800}
                baseHeight={600}
                maintainAspectRatio={true}
              >
                <div ref={phaserRef} className="phaser-container" />
              </ResponsiveGameCanvas>
            </section>

            {/* 控制面板 */}
            <ResponsiveSidebar 
              collapsible={true}
              defaultCollapsed={deviceType === 'mobile'}
              className="control-panel"
            >
              <div className="panel-section">
                <h3>🎯 专注状态</h3>
                <div className="focus-indicator">
                  <div className={`focus-status ${state.isPostureGood ? 'good' : 'poor'}`}>
                    <div className="focus-score">
                      专注度: {Math.round(state.currentFocusScore)}%
                    </div>
                    <div className="focus-streak">
                      连续专注: {getCurrentStreakFormatted()}
                    </div>
                    <div className="focus-average">
                      平均分数: {Math.round(state.averageFocusScore)}%
                    </div>
                  </div>
                  {state.focusSession.isActive && (
                    <div className="session-info">
                      <span className="session-indicator">🔥</span>
                      <span>专注会话进行中</span>
                    </div>
                  )}
                </div>
              </div>

              <div className="panel-section">
                <h3>📊 农场统计</h3>
                <div className="stats-grid">
                  <div className="stat-item">
                    <span className="stat-label">知识花</span>
                    <span className="stat-value">{state.farmStats.knowledgeFlowers}</span>
                  </div>
                  <div className="stat-item">
                    <span className="stat-label">力量树</span>
                    <span className="stat-value">{state.farmStats.strengthTrees}</span>
                  </div>
                </div>
                <div className="growth-points">
                  <span>成长积分: {state.farmStats.totalGrowthPoints}</span>
                </div>
              </div>

              <div className="panel-section">
                <h3>🛠️ 操作中心</h3>
                <div className="action-buttons">
                  <button 
                    className={`action-btn focus-btn ${state.focusSession.isActive ? 'active' : ''}`}
                    onClick={state.focusSession.isActive ? endFocusSession : startFocusSession}
                  >
                    💡 {state.focusSession.isActive ? '结束专注' : '开始专注学习'}
                  </button>
                  <button 
                    className={`action-btn monitor-btn ${showAppMonitor ? 'active' : ''}`}
                    onClick={() => setShowAppMonitor(!showAppMonitor)}
                  >
                    🖥️ {showAppMonitor ? '关闭应用监控' : '打开应用监控'}
                  </button>
                  <button 
                    className={`action-btn inventory-btn ${showInventory ? 'active' : ''}`}
                    onClick={() => setShowInventory(!showInventory)}
                  >
                    🎒 {showInventory ? '关闭背包' : '物品背包'}
                  </button>
                </div>
              </div>
            </ResponsiveSidebar>
          </Flex>
        </main>

        <footer className="app-footer">
          <p>🌟 坚持每一天，收获更好的自己！</p>
        </footer>
      </Container>
      
      {/* 背包界面 */}
      {showInventory && (
        <InventoryPanel 
          inventorySystem={inventorySystem} 
          onClose={() => setShowInventory(false)} 
        />
      )}
    </>
  )
}

export default MainGameInterface 