import React from 'react'
import { PrivacySettings as PrivacySettingsType, EncryptionLevel } from '../../types/settings.types'

interface PrivacySettingsProps {
  settings: PrivacySettingsType
  onSettingChange: (key: string, value: any) => void
  isLoading: boolean
}

export const PrivacySettings: React.FC<PrivacySettingsProps> = ({ 
  settings, 
  onSettingChange, 
  isLoading 
}) => {
  return (
    <div className="settings-section">
      <div className="section-header">
        <h3>隐私设置</h3>
        <p>配置数据收集和隐私保护</p>
      </div>

      <div className="settings-grid">
        {/* 数据收集设置 */}
        <div className="subsection">
          <h4>数据收集</h4>
          <div className="setting-group">
            <label className="setting-label">
              <span className="label-text">行为分析</span>
              <span className="label-description">收集使用行为数据以改进应用</span>
            </label>
            <div className="toggle-switch">
              <input
                type="checkbox"
                id="behaviorAnalytics"
                checked={settings.dataCollection.behaviorAnalytics}
                onChange={(e) => onSettingChange('dataCollection.behaviorAnalytics', e.target.checked)}
                disabled={isLoading}
              />
              <label htmlFor="behaviorAnalytics" className="toggle-label">
                <span className="toggle-slider"></span>
              </label>
            </div>
          </div>

          <div className="setting-group">
            <label className="setting-label">
              <span className="label-text">性能指标</span>
              <span className="label-description">收集应用性能和稳定性数据</span>
            </label>
            <div className="toggle-switch">
              <input
                type="checkbox"
                id="performanceMetrics"
                checked={settings.dataCollection.performanceMetrics}
                onChange={(e) => onSettingChange('dataCollection.performanceMetrics', e.target.checked)}
                disabled={isLoading}
              />
              <label htmlFor="performanceMetrics" className="toggle-label">
                <span className="toggle-slider"></span>
              </label>
            </div>
          </div>

          <div className="setting-group">
            <label className="setting-label">
              <span className="label-text">错误报告</span>
              <span className="label-description">自动发送崩溃和错误报告</span>
            </label>
            <div className="toggle-switch">
              <input
                type="checkbox"
                id="errorReporting"
                checked={settings.dataCollection.errorReporting}
                onChange={(e) => onSettingChange('dataCollection.errorReporting', e.target.checked)}
                disabled={isLoading}
              />
              <label htmlFor="errorReporting" className="toggle-label">
                <span className="toggle-slider"></span>
              </label>
            </div>
          </div>

          <div className="setting-group">
            <label className="setting-label">
              <span className="label-text">摄像头数据</span>
              <span className="label-description">允许处理摄像头图像数据</span>
            </label>
            <div className="toggle-switch">
              <input
                type="checkbox"
                id="cameraData"
                checked={settings.dataCollection.cameraData}
                onChange={(e) => onSettingChange('dataCollection.cameraData', e.target.checked)}
                disabled={isLoading}
              />
              <label htmlFor="cameraData" className="toggle-label">
                <span className="toggle-slider"></span>
              </label>
            </div>
          </div>

          <div className="setting-group">
            <label className="setting-label">
              <span className="label-text">音频数据</span>
              <span className="label-description">允许处理音频输入数据</span>
            </label>
            <div className="toggle-switch">
              <input
                type="checkbox"
                id="audioData"
                checked={settings.dataCollection.audioData}
                onChange={(e) => onSettingChange('dataCollection.audioData', e.target.checked)}
                disabled={isLoading}
              />
              <label htmlFor="audioData" className="toggle-label">
                <span className="toggle-slider"></span>
              </label>
            </div>
          </div>
        </div>

        {/* 数据共享设置 */}
        <div className="subsection">
          <h4>数据共享</h4>
          <div className="setting-group">
            <label className="setting-label">
              <span className="label-text">分析数据</span>
              <span className="label-description">与分析服务共享匿名数据</span>
            </label>
            <div className="toggle-switch">
              <input
                type="checkbox"
                id="analytics"
                checked={settings.sharing.analytics}
                onChange={(e) => onSettingChange('sharing.analytics', e.target.checked)}
                disabled={isLoading}
              />
              <label htmlFor="analytics" className="toggle-label">
                <span className="toggle-slider"></span>
              </label>
            </div>
          </div>

          <div className="setting-group">
            <label className="setting-label">
              <span className="label-text">研究数据</span>
              <span className="label-description">为学术研究贡献匿名数据</span>
            </label>
            <div className="toggle-switch">
              <input
                type="checkbox"
                id="research"
                checked={settings.sharing.research}
                onChange={(e) => onSettingChange('sharing.research', e.target.checked)}
                disabled={isLoading}
              />
              <label htmlFor="research" className="toggle-label">
                <span className="toggle-slider"></span>
              </label>
            </div>
          </div>

          <div className="setting-group">
            <label className="setting-label">
              <span className="label-text">聚合数据</span>
              <span className="label-description">共享统计汇总数据</span>
            </label>
            <div className="toggle-switch">
              <input
                type="checkbox"
                id="aggregatedData"
                checked={settings.sharing.aggregatedData}
                onChange={(e) => onSettingChange('sharing.aggregatedData', e.target.checked)}
                disabled={isLoading}
              />
              <label htmlFor="aggregatedData" className="toggle-label">
                <span className="toggle-slider"></span>
              </label>
            </div>
          </div>
        </div>

        {/* 数据保留设置 */}
        <div className="subsection">
          <h4>数据保留</h4>
          <div className="setting-group">
            <label className="setting-label">
              <span className="label-text">自动删除</span>
              <span className="label-description">自动删除过期数据</span>
            </label>
            <div className="toggle-switch">
              <input
                type="checkbox"
                id="autoDelete"
                checked={settings.retention.autoDelete}
                onChange={(e) => onSettingChange('retention.autoDelete', e.target.checked)}
                disabled={isLoading}
              />
              <label htmlFor="autoDelete" className="toggle-label">
                <span className="toggle-slider"></span>
              </label>
            </div>
          </div>

          <div className="setting-group">
            <label className="setting-label">
              <span className="label-text">行为数据保留（天）</span>
              <span className="label-description">行为数据保留时长</span>
            </label>
            <div className="number-input-wrapper">
              <input
                type="number"
                min="1"
                max="365"
                value={settings.retention.behaviorData}
                onChange={(e) => onSettingChange('retention.behaviorData', parseInt(e.target.value))}
                disabled={isLoading}
              />
              <span className="input-unit">天</span>
            </div>
          </div>

          <div className="setting-group">
            <label className="setting-label">
              <span className="label-text">性能数据保留（天）</span>
              <span className="label-description">性能数据保留时长</span>
            </label>
            <div className="number-input-wrapper">
              <input
                type="number"
                min="1"
                max="90"
                value={settings.retention.performanceData}
                onChange={(e) => onSettingChange('retention.performanceData', parseInt(e.target.value))}
                disabled={isLoading}
              />
              <span className="input-unit">天</span>
            </div>
          </div>
        </div>

        {/* 加密设置 */}
        <div className="subsection">
          <h4>数据加密</h4>
          <div className="setting-group">
            <label className="setting-label">
              <span className="label-text">启用加密</span>
              <span className="label-description">加密本地存储的敏感数据</span>
            </label>
            <div className="toggle-switch">
              <input
                type="checkbox"
                id="encryptionEnabled"
                checked={settings.encryption.enabled}
                onChange={(e) => onSettingChange('encryption.enabled', e.target.checked)}
                disabled={isLoading}
              />
              <label htmlFor="encryptionEnabled" className="toggle-label">
                <span className="toggle-slider"></span>
              </label>
            </div>
          </div>

          <div className="setting-group">
            <label className="setting-label">
              <span className="label-text">加密级别</span>
              <span className="label-description">选择数据加密强度</span>
            </label>
            <select
              className="setting-input"
              value={settings.encryption.level}
              onChange={(e) => onSettingChange('encryption.level', e.target.value)}
              disabled={isLoading || !settings.encryption.enabled}
            >
              <option value={EncryptionLevel.BASIC}>基础加密</option>
              <option value={EncryptionLevel.STANDARD}>标准加密</option>
              <option value={EncryptionLevel.ADVANCED}>高级加密</option>
            </select>
          </div>

          <div className="setting-group">
            <label className="setting-label">
              <span className="label-text">本地加密</span>
              <span className="label-description">加密本地存储文件</span>
            </label>
            <div className="toggle-switch">
              <input
                type="checkbox"
                id="localEncryption"
                checked={settings.encryption.localEncryption}
                onChange={(e) => onSettingChange('encryption.localEncryption', e.target.checked)}
                disabled={isLoading || !settings.encryption.enabled}
              />
              <label htmlFor="localEncryption" className="toggle-label">
                <span className="toggle-slider"></span>
              </label>
            </div>
          </div>
        </div>

        {/* 匿名化 */}
        <div className="setting-group">
          <label className="setting-label">
            <span className="label-text">数据匿名化</span>
            <span className="label-description">自动移除个人身份信息</span>
          </label>
          <div className="toggle-switch">
            <input
              type="checkbox"
              id="anonymization"
              checked={settings.anonymization}
              onChange={(e) => onSettingChange('anonymization', e.target.checked)}
              disabled={isLoading}
            />
            <label htmlFor="anonymization" className="toggle-label">
              <span className="toggle-slider"></span>
            </label>
          </div>
        </div>
      </div>

      <div className="section-footer">
        <div className="footer-info">
          <span className="info-icon">ℹ️</span>
          <span>隐私设置更改会立即生效。部分设置可能需要重启应用</span>
        </div>
      </div>
    </div>
  )
}

export default PrivacySettings 