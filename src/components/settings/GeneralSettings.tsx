import React from 'react'
import { GeneralSettings as GeneralSettingsType, PerformanceMode } from '../../types/settings.types'

interface GeneralSettingsProps {
  settings: GeneralSettingsType
  onSettingChange: (key: string, value: any) => void
  isLoading: boolean
}

/**
 * 通用设置组件
 */
export const GeneralSettings: React.FC<GeneralSettingsProps> = ({
  settings,
  onSettingChange,
  isLoading
}) => {
  return (
    <div className="settings-section">
      <div className="section-header">
        <h3>通用设置</h3>
        <p>配置应用的基本行为和偏好</p>
      </div>

      <div className="settings-grid">
        <div className="setting-group">
          <label className="setting-label">
            <span className="label-text">界面语言</span>
          </label>
          <select
            className="setting-input"
            value={settings.language}
            onChange={(e) => onSettingChange('language', e.target.value)}
            disabled={isLoading}
          >
            <option value="zh-CN">简体中文</option>
            <option value="en-US">English</option>
          </select>
        </div>

        <div className="setting-group">
          <label className="setting-label">
            <span className="label-text">性能模式</span>
          </label>
          <select
            className="setting-input"
            value={settings.performanceMode}
            onChange={(e) => onSettingChange('performanceMode', e.target.value)}
            disabled={isLoading}
          >
            <option value={PerformanceMode.HIGH}>高性能</option>
            <option value={PerformanceMode.BALANCED}>平衡</option>
            <option value={PerformanceMode.POWER_SAVER}>省电</option>
          </select>
        </div>

        <div className="setting-group">
          <label className="setting-label">
            <span className="label-text">开机自启动</span>
          </label>
          <input
            type="checkbox"
            checked={settings.autoStart}
            onChange={(e) => onSettingChange('autoStart', e.target.checked)}
            disabled={isLoading}
          />
        </div>
      </div>
    </div>
  )
}

export default GeneralSettings 