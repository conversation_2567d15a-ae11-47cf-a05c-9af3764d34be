import React, { useState, useEffect, useRef } from 'react'
import { AudioSettings as AudioSettingsType, AudioQuality } from '../../types/settings.types'
import { audioService, AudioDeviceInfo, SoundEffect, AudioAnalyzerData } from '../../services/AudioService'

interface AudioSettingsProps {
  settings: AudioSettingsType
  onSettingChange: (key: string, value: any) => void
  isLoading: boolean
}

/**
 * 音频设置组件
 */
export const AudioSettings: React.FC<AudioSettingsProps> = ({
  settings,
  onSettingChange,
  isLoading
}) => {
  const [inputDevices, setInputDevices] = useState<AudioDeviceInfo[]>([])
  const [outputDevices, setOutputDevices] = useState<AudioDeviceInfo[]>([])
  const [soundEffects, setSoundEffects] = useState<SoundEffect[]>([])
  const [isServiceReady, setIsServiceReady] = useState(false)
  const [isMicActive, setIsMicActive] = useState(false)
  const [audioAnalysis, setAudioAnalysis] = useState<AudioAnalyzerData | null>(null)
  const [testingSound, setTestingSound] = useState<string | null>(null)
  const [error, setError] = useState<string | null>(null)
  const analysisIntervalRef = useRef<NodeJS.Timeout | null>(null)

  // 初始化音频服务
  useEffect(() => {
    const initializeService = async () => {
      try {
        if (!audioService.isReady()) {
          await audioService.initialize()
        }
        setIsServiceReady(true)
        setInputDevices(audioService.getInputDevices())
        setOutputDevices(audioService.getOutputDevices())
        setSoundEffects(audioService.getSoundEffects())
        
        // 更新服务设置
        audioService.updateSettings(settings)
      } catch (err) {
        setError(err instanceof Error ? err.message : '音频服务初始化失败')
      }
    }

    initializeService()

    // 监听服务事件
    const handleDevicesDetected = (data: { input: AudioDeviceInfo[]; output: AudioDeviceInfo[] }) => {
      setInputDevices(data.input)
      setOutputDevices(data.output)
    }

    const handleSoundPlayed = (data: { soundId: string }) => {
      // 可以在这里添加播放反馈
    }

    audioService.addEventListener('devicesDetected', handleDevicesDetected)
    audioService.addEventListener('soundPlayed', handleSoundPlayed)

    return () => {
      audioService.removeEventListener('devicesDetected', handleDevicesDetected)
      audioService.removeEventListener('soundPlayed', handleSoundPlayed)
      stopMicrophone()
    }
  }, [])

  // 当设置更改时，更新音频服务
  useEffect(() => {
    if (isServiceReady) {
      audioService.updateSettings(settings)
    }
  }, [settings, isServiceReady])

  // 播放测试音效
  const playTestSound = async (soundId: string) => {
    try {
      setTestingSound(soundId)
      setError(null)
      await audioService.playSound(soundId, { volume: 0.8 })
    } catch (err) {
      setError(err instanceof Error ? err.message : '音效播放失败')
    } finally {
      setTimeout(() => setTestingSound(null), 1000)
    }
  }

  // 开始麦克风测试
  const startMicrophone = async () => {
    try {
      setError(null)
      await audioService.startMicrophone(settings.devices.inputDeviceId)
      setIsMicActive(true)
      
      // 开始音频分析
      analysisIntervalRef.current = setInterval(() => {
        const analysis = audioService.getAudioAnalysis()
        setAudioAnalysis(analysis)
      }, 100)
      
    } catch (err) {
      setError(err instanceof Error ? err.message : '麦克风启动失败')
    }
  }

  // 停止麦克风测试
  const stopMicrophone = () => {
    audioService.stopMicrophone()
    setIsMicActive(false)
    setAudioAnalysis(null)
    
    if (analysisIntervalRef.current) {
      clearInterval(analysisIntervalRef.current)
      analysisIntervalRef.current = null
    }
  }

  // 停止所有音效
  const stopAllSounds = () => {
    audioService.stopAllSounds()
    setTestingSound(null)
  }

  // 音量条组件
  const VolumeSlider: React.FC<{
    label: string
    description?: string
    value: number
    onChange: (value: number) => void
    disabled?: boolean
  }> = ({ label, description, value, onChange, disabled }) => (
    <div className="setting-group">
      <label className="setting-label">
        <span className="label-text">{label}</span>
        {description && <span className="label-description">{description}</span>}
      </label>
      <div className="range-input-wrapper">
        <input
          type="range"
          min="0"
          max="100"
          value={value}
          onChange={(e) => onChange(parseInt(e.target.value))}
          disabled={disabled || isLoading || !settings.enabled}
        />
        <span className="range-value">{value}%</span>
      </div>
    </div>
  )

  return (
    <div className="settings-section">
      <div className="section-header">
        <h3>音频设置</h3>
        <p>配置音量、音效和设备</p>
      </div>

      {error && (
        <div className="error-message">
          <span className="error-icon">⚠️</span>
          <span>{error}</span>
          <button onClick={() => setError(null)}>×</button>
        </div>
      )}

      <div className="settings-grid">
        {/* 基本设置 */}
        <div className="setting-group">
          <label className="setting-label">
            <span className="label-text">启用音频</span>
            <span className="label-description">开启或关闭所有音频功能</span>
          </label>
          <div className="toggle-switch">
            <input
              type="checkbox"
              id="audioEnabled"
              checked={settings.enabled}
              onChange={(e) => onSettingChange('enabled', e.target.checked)}
              disabled={isLoading}
            />
            <label htmlFor="audioEnabled" className="toggle-label">
              <span className="toggle-slider"></span>
            </label>
          </div>
        </div>

        {/* 主静音 */}
        <div className="setting-group">
          <label className="setting-label">
            <span className="label-text">静音</span>
            <span className="label-description">临时关闭所有声音</span>
          </label>
          <div className="toggle-switch">
            <input
              type="checkbox"
              id="audioMuted"
              checked={settings.volume.muted}
              onChange={(e) => onSettingChange('volume.muted', e.target.checked)}
              disabled={isLoading || !settings.enabled}
            />
            <label htmlFor="audioMuted" className="toggle-label">
              <span className="toggle-slider"></span>
            </label>
          </div>
        </div>

        {/* 音量控制 */}
        <VolumeSlider
          label="主音量"
          description="控制所有音频的总音量"
          value={settings.volume.master}
          onChange={(value) => onSettingChange('volume.master', value)}
          disabled={settings.volume.muted}
        />

        <VolumeSlider
          label="音效音量"
          description="按钮点击、提示音等界面音效"
          value={settings.volume.effects}
          onChange={(value) => onSettingChange('volume.effects', value)}
          disabled={settings.volume.muted}
        />

        <VolumeSlider
          label="背景音乐"
          description="环境音乐和背景声音"
          value={settings.volume.background}
          onChange={(value) => onSettingChange('volume.background', value)}
          disabled={settings.volume.muted}
        />

        <VolumeSlider
          label="语音音量"
          description="语音提示和语音反馈"
          value={settings.volume.voice}
          onChange={(value) => onSettingChange('volume.voice', value)}
          disabled={settings.volume.muted}
        />

        <VolumeSlider
          label="通知音量"
          description="系统通知和提醒音"
          value={settings.volume.notifications}
          onChange={(value) => onSettingChange('volume.notifications', value)}
          disabled={settings.volume.muted}
        />

        {/* 设备选择 */}
        <div className="setting-group">
          <label className="setting-label">
            <span className="label-text">输出设备</span>
            <span className="label-description">选择音频输出设备（扬声器/耳机）</span>
          </label>
          <select
            className="setting-input"
            value={settings.devices.outputDeviceId}
            onChange={(e) => onSettingChange('devices.outputDeviceId', e.target.value)}
            disabled={isLoading || !settings.enabled}
          >
            <option value="">默认设备</option>
            {outputDevices.map((device) => (
              <option key={device.deviceId} value={device.deviceId}>
                {device.label || `音频设备 ${device.deviceId.slice(0, 8)}`}
              </option>
            ))}
          </select>
        </div>

        <div className="setting-group">
          <label className="setting-label">
            <span className="label-text">输入设备</span>
            <span className="label-description">选择音频输入设备（麦克风）</span>
          </label>
          <select
            className="setting-input"
            value={settings.devices.inputDeviceId}
            onChange={(e) => onSettingChange('devices.inputDeviceId', e.target.value)}
            disabled={isLoading || !settings.enabled}
          >
            <option value="">默认设备</option>
            {inputDevices.map((device) => (
              <option key={device.deviceId} value={device.deviceId}>
                {device.label || `麦克风 ${device.deviceId.slice(0, 8)}`}
              </option>
            ))}
          </select>
        </div>

        {/* 音频质量 */}
        <div className="setting-group">
          <label className="setting-label">
            <span className="label-text">音频质量</span>
            <span className="label-description">设置音频处理质量</span>
          </label>
          <select
            className="setting-input"
            value={settings.quality}
            onChange={(e) => onSettingChange('quality', e.target.value)}
            disabled={isLoading || !settings.enabled}
          >
            <option value={AudioQuality.LOW}>低质量（节省CPU）</option>
            <option value={AudioQuality.MEDIUM}>中等质量</option>
            <option value={AudioQuality.HIGH}>高质量</option>
            <option value={AudioQuality.LOSSLESS}>无损质量</option>
          </select>
        </div>
      </div>

      {/* 音效设置 */}
      <div className="subsection">
        <h4>音效增强</h4>
        <div className="settings-grid">
          <div className="setting-group">
            <label className="setting-label">
              <span className="label-text">启用音效处理</span>
              <span className="label-description">启用音频增强效果</span>
            </label>
            <div className="toggle-switch">
              <input
                type="checkbox"
                id="effectsEnabled"
                checked={settings.effects.enabled}
                onChange={(e) => onSettingChange('effects.enabled', e.target.checked)}
                disabled={isLoading || !settings.enabled}
              />
              <label htmlFor="effectsEnabled" className="toggle-label">
                <span className="toggle-slider"></span>
              </label>
            </div>
          </div>

          <VolumeSlider
            label="混响"
            description="添加空间感和深度"
            value={settings.effects.reverb}
            onChange={(value) => onSettingChange('effects.reverb', value)}
            disabled={!settings.effects.enabled}
          />

          <VolumeSlider
            label="低音"
            description="增强低频响应"
            value={settings.effects.bass}
            onChange={(value) => onSettingChange('effects.bass', value)}
            disabled={!settings.effects.enabled}
          />

          <VolumeSlider
            label="高音"
            description="增强高频清晰度"
            value={settings.effects.treble}
            onChange={(value) => onSettingChange('effects.treble', value)}
            disabled={!settings.effects.enabled}
          />

          <div className="setting-group">
            <label className="setting-label">
              <span className="label-text">空间音频</span>
              <span className="label-description">模拟3D环绕声效果</span>
            </label>
            <div className="toggle-switch">
              <input
                type="checkbox"
                id="spatialAudio"
                checked={settings.effects.spatialAudio}
                onChange={(e) => onSettingChange('effects.spatialAudio', e.target.checked)}
                disabled={isLoading || !settings.enabled || !settings.effects.enabled}
              />
              <label htmlFor="spatialAudio" className="toggle-label">
                <span className="toggle-slider"></span>
              </label>
            </div>
          </div>

          <div className="setting-group">
            <label className="setting-label">
              <span className="label-text">噪声抑制</span>
              <span className="label-description">减少背景噪音</span>
            </label>
            <div className="toggle-switch">
              <input
                type="checkbox"
                id="noiseReduction"
                checked={settings.effects.noiseReduction}
                onChange={(e) => onSettingChange('effects.noiseReduction', e.target.checked)}
                disabled={isLoading || !settings.enabled}
              />
              <label htmlFor="noiseReduction" className="toggle-label">
                <span className="toggle-slider"></span>
              </label>
            </div>
          </div>
        </div>
      </div>

      {/* 通知音效 */}
      <div className="subsection">
        <h4>通知音效</h4>
        <div className="settings-grid">
          <div className="setting-group">
            <label className="setting-label">
              <span className="label-text">专注丢失提示</span>
              <span className="label-description">当失去专注时播放提示音</span>
            </label>
            <div className="toggle-switch">
              <input
                type="checkbox"
                id="playOnFocusLoss"
                checked={settings.notifications.playOnFocusLoss}
                onChange={(e) => onSettingChange('notifications.playOnFocusLoss', e.target.checked)}
                disabled={isLoading || !settings.enabled}
              />
              <label htmlFor="playOnFocusLoss" className="toggle-label">
                <span className="toggle-slider"></span>
              </label>
            </div>
          </div>

          <div className="setting-group">
            <label className="setting-label">
              <span className="label-text">成就解锁音效</span>
              <span className="label-description">获得成就时播放庆祝音效</span>
            </label>
            <div className="toggle-switch">
              <input
                type="checkbox"
                id="playOnAchievement"
                checked={settings.notifications.playOnAchievement}
                onChange={(e) => onSettingChange('notifications.playOnAchievement', e.target.checked)}
                disabled={isLoading || !settings.enabled}
              />
              <label htmlFor="playOnAchievement" className="toggle-label">
                <span className="toggle-slider"></span>
              </label>
            </div>
          </div>

          <div className="setting-group">
            <label className="setting-label">
              <span className="label-text">提醒音效</span>
              <span className="label-description">定时提醒时播放提示音</span>
            </label>
            <div className="toggle-switch">
              <input
                type="checkbox"
                id="playOnReminder"
                checked={settings.notifications.playOnReminder}
                onChange={(e) => onSettingChange('notifications.playOnReminder', e.target.checked)}
                disabled={isLoading || !settings.enabled}
              />
              <label htmlFor="playOnReminder" className="toggle-label">
                <span className="toggle-slider"></span>
              </label>
            </div>
          </div>

          <div className="setting-group">
            <label className="setting-label">
              <span className="label-text">错误提示音</span>
              <span className="label-description">发生错误时播放警告音</span>
            </label>
            <div className="toggle-switch">
              <input
                type="checkbox"
                id="playOnError"
                checked={settings.notifications.playOnError}
                onChange={(e) => onSettingChange('notifications.playOnError', e.target.checked)}
                disabled={isLoading || !settings.enabled}
              />
              <label htmlFor="playOnError" className="toggle-label">
                <span className="toggle-slider"></span>
              </label>
            </div>
          </div>
        </div>
      </div>

      {/* 音效测试和麦克风测试 */}
      {settings.enabled && isServiceReady && (
        <div className="audio-controls">
          <div className="controls-header">
            <h4>音频测试</h4>
            <div className="control-buttons">
              <button
                className="btn btn-secondary"
                onClick={stopAllSounds}
                disabled={isLoading}
              >
                🔇 停止所有音效
              </button>
            </div>
          </div>

          {/* 音效测试 */}
          <div className="sound-testing">
            <h5>音效测试</h5>
            <div className="sound-list">
              {soundEffects.map((sound) => (
                <div key={sound.id} className="sound-item">
                  <div className="sound-info">
                    <span className="sound-name">{sound.name}</span>
                    <span className="sound-description">{sound.description}</span>
                    <span className="sound-category">{sound.category}</span>
                  </div>
                  <button
                    className={`btn btn-small ${testingSound === sound.id ? 'btn-primary' : 'btn-secondary'}`}
                    onClick={() => playTestSound(sound.id)}
                    disabled={isLoading || testingSound === sound.id}
                  >
                    {testingSound === sound.id ? '播放中...' : '🔊 测试'}
                  </button>
                </div>
              ))}
            </div>
          </div>

          {/* 麦克风测试 */}
          <div className="microphone-testing">
            <h5>麦克风测试</h5>
            <div className="mic-controls">
              {!isMicActive ? (
                <button
                  className="btn btn-primary"
                  onClick={startMicrophone}
                  disabled={isLoading}
                >
                  🎤 开始录音测试
                </button>
              ) : (
                <button
                  className="btn btn-secondary"
                  onClick={stopMicrophone}
                  disabled={isLoading}
                >
                  ⏹️ 停止录音测试
                </button>
              )}
            </div>

            {/* 音频分析显示 */}
            {audioAnalysis && (
              <div className="audio-analysis">
                <div className="analysis-item">
                  <span className="analysis-label">音量:</span>
                  <div className="volume-meter">
                    <div 
                      className="volume-bar" 
                      style={{ width: `${audioAnalysis.volume * 100}%` }}
                    ></div>
                  </div>
                  <span className="analysis-value">{Math.round(audioAnalysis.volume * 100)}%</span>
                </div>
                
                <div className="analysis-item">
                  <span className="analysis-label">主频:</span>
                  <span className="analysis-value">{Math.round(audioAnalysis.frequency)} Hz</span>
                </div>

                <div className="waveform-display">
                  <svg width="300" height="60" viewBox="0 0 300 60">
                    <polyline
                      points={audioAnalysis.waveform
                        .slice(0, 150)
                        .map((value, index) => `${index * 2},${30 + value * 25}`)
                        .join(' ')}
                      fill="none"
                      stroke="#4299e1"
                      strokeWidth="2"
                    />
                  </svg>
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      <div className="section-footer">
        <div className="footer-info">
          <span className="info-icon">ℹ️</span>
          <span>音频设置更改会立即生效。某些效果可能需要重启音频设备</span>
        </div>
      </div>
    </div>
  )
}

export default AudioSettings 