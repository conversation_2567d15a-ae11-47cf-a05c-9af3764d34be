import React, { useState, useEffect } from 'react'
import { DataSettings as DataSettingsType, BackupFrequency, BackupLocation, SyncFrequency, ConflictResolution, ExportFormat, MergeStrategy } from '../../types/settings.types'
import { SyncService } from '../../services/SyncService'
import { DatabaseManager } from '../../storage/DatabaseManager'

interface DataSettingsProps {
  settings: DataSettingsType
  onSettingChange: (key: string, value: any) => void
  isLoading: boolean
}

export const DataSettings: React.FC<DataSettingsProps> = ({ 
  settings, 
  onSettingChange, 
  isLoading 
}) => {
  const [isOperationRunning, setIsOperationRunning] = useState(false)
  const [operationStatus, setOperationStatus] = useState<string | null>(null)
  const [syncService] = useState(() => SyncService.getInstance())
  const [databaseManager] = useState(() => new DatabaseManager())

  // 初始化数据库管理器
  useEffect(() => {
    const initializeDatabase = async () => {
      try {
        await databaseManager.initialize()
      } catch (error) {
        console.error('数据库管理器初始化失败:', error)
      }
    }
    
    initializeDatabase()

    return () => {
      databaseManager.close()
    }
  }, [databaseManager])

  // 显示操作状态
  const showStatus = (message: string, type: 'success' | 'error' | 'info' = 'info') => {
    setOperationStatus(message)
    setTimeout(() => setOperationStatus(null), 3000)
  }

  // 导出所有数据
  const handleExportAllData = async () => {
    if (isOperationRunning) return
    
    try {
      setIsOperationRunning(true)
      showStatus('正在导出数据...', 'info')

      const exportData = await databaseManager.exportAllData()
      const blob = new Blob([exportData], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `selfgame-data-export-${new Date().toISOString().split('T')[0]}.json`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)

      showStatus('数据导出成功！', 'success')
    } catch (error) {
      console.error('导出数据失败:', error)
      showStatus('数据导出失败', 'error')
    } finally {
      setIsOperationRunning(false)
    }
  }

  // 导入数据
  const handleImportData = () => {
    const input = document.createElement('input')
    input.type = 'file'
    input.accept = '.json'
    input.onchange = async (event) => {
      const file = (event.target as HTMLInputElement).files?.[0]
      if (!file) return

      try {
        setIsOperationRunning(true)
        showStatus('正在导入数据...', 'info')

        const text = await file.text()
        const success = await databaseManager.importData(text)
        
        if (success) {
          showStatus('数据导入成功！', 'success')
        } else {
          showStatus('数据导入失败', 'error')
        }
      } catch (error) {
        console.error('导入数据失败:', error)
        showStatus('数据导入失败', 'error')
      } finally {
        setIsOperationRunning(false)
      }
    }
    input.click()
  }

  // 立即备份
  const handleCreateBackup = async () => {
    if (isOperationRunning) return
    
    try {
      setIsOperationRunning(true)
      showStatus('正在创建备份...', 'info')

      const backupInfo = await databaseManager.createBackup('手动备份')
      
      if (backupInfo) {
        showStatus(`备份创建成功！备份ID: ${backupInfo.id.slice(-8)}`, 'success')
      } else {
        showStatus('备份创建失败', 'error')
      }
    } catch (error) {
      console.error('创建备份失败:', error)
      showStatus('备份创建失败', 'error')
    } finally {
      setIsOperationRunning(false)
    }
  }

  // 手动同步
  const handleManualSync = async () => {
    if (isOperationRunning) return
    
    try {
      setIsOperationRunning(true)
      showStatus('正在同步数据...', 'info')

      await syncService.forceSync()
      showStatus('数据同步成功！', 'success')
    } catch (error) {
      console.error('数据同步失败:', error)
      showStatus('数据同步失败', 'error')
    } finally {
      setIsOperationRunning(false)
    }
  }

  // 清理缓存
  const handleClearCache = async () => {
    if (isOperationRunning) return
    
    try {
      setIsOperationRunning(true)
      showStatus('正在清理缓存...', 'info')

      // 清理过期数据
      await databaseManager.cleanupExpiredData()
      
      // 清理浏览器缓存
      if ('caches' in window) {
        const cacheNames = await caches.keys()
        await Promise.all(
          cacheNames.map(cacheName => caches.delete(cacheName))
        )
      }

      showStatus('缓存清理完成！', 'success')
    } catch (error) {
      console.error('清理缓存失败:', error)
      showStatus('缓存清理失败', 'error')
    } finally {
      setIsOperationRunning(false)
    }
  }

  // 获取存储使用情况
  const [storageUsage, setStorageUsage] = useState<{ totalSize: number; availableSpace: number } | null>(null)

  useEffect(() => {
    const updateStorageUsage = async () => {
      try {
        const usage = await databaseManager.getStorageUsage()
        setStorageUsage(usage)
      } catch (error) {
        console.error('获取存储使用情况失败:', error)
      }
    }

    updateStorageUsage()
    // 每30秒更新一次存储使用情况
    const interval = setInterval(updateStorageUsage, 30000)
    
    return () => clearInterval(interval)
  }, [databaseManager])

  // 格式化存储大小
  const formatSize = (bytes: number): string => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
  }

  return (
    <div className="settings-section">
      <div className="section-header">
        <h3>数据设置</h3>
        <p>配置存储、备份和同步</p>
      </div>

      {/* 操作状态提示 */}
      {operationStatus && (
        <div className={`operation-status ${operationStatus.includes('成功') ? 'success' : operationStatus.includes('失败') ? 'error' : 'info'}`}>
          <span className="status-message">{operationStatus}</span>
        </div>
      )}

      {/* 存储使用情况 */}
      {storageUsage && (
        <div className="storage-usage-panel">
          <h4>存储使用情况</h4>
          <div className="usage-info">
            <div className="usage-item">
              <span className="usage-label">已使用:</span>
              <span className="usage-value">{formatSize(storageUsage.totalSize)}</span>
            </div>
            <div className="usage-item">
              <span className="usage-label">可用空间:</span>
              <span className="usage-value">{formatSize(storageUsage.availableSpace)}</span>
            </div>
          </div>
          <div className="usage-bar">
            <div 
              className="usage-fill" 
              style={{ 
                width: `${Math.min(100, (storageUsage.totalSize / (storageUsage.totalSize + storageUsage.availableSpace)) * 100)}%` 
              }}
            ></div>
          </div>
        </div>
      )}

      <div className="settings-grid">
        {/* 存储设置 */}
        <div className="subsection">
          <h4>本地存储</h4>
          <div className="setting-group">
            <label className="setting-label">
              <span className="label-text">存储位置</span>
              <span className="label-description">本地数据存储路径</span>
            </label>
            <div className="file-input-wrapper">
              <input
                type="text"
                className="setting-input"
                value={settings.storage.location}
                onChange={(e) => onSettingChange('storage.location', e.target.value)}
                disabled={isLoading}
                placeholder="选择存储目录"
              />
              <button className="btn btn-small btn-secondary">浏览</button>
            </div>
          </div>

          <div className="setting-group">
            <label className="setting-label">
              <span className="label-text">最大存储空间 (MB)</span>
              <span className="label-description">限制本地数据使用的磁盘空间</span>
            </label>
            <div className="number-input-wrapper">
              <input
                type="number"
                min="100"
                max="10000"
                value={settings.storage.maxSize}
                onChange={(e) => onSettingChange('storage.maxSize', parseInt(e.target.value))}
                disabled={isLoading}
              />
              <span className="input-unit">MB</span>
            </div>
          </div>

          <div className="setting-group">
            <label className="setting-label">
              <span className="label-text">数据压缩</span>
              <span className="label-description">压缩存储数据以节省空间</span>
            </label>
            <div className="toggle-switch">
              <input
                type="checkbox"
                id="storageCompression"
                checked={settings.storage.compression}
                onChange={(e) => onSettingChange('storage.compression', e.target.checked)}
                disabled={isLoading}
              />
              <label htmlFor="storageCompression" className="toggle-label">
                <span className="toggle-slider"></span>
              </label>
            </div>
          </div>

          <div className="setting-group">
            <label className="setting-label">
              <span className="label-text">自动清理</span>
              <span className="label-description">自动清理过期和临时数据</span>
            </label>
            <div className="toggle-switch">
              <input
                type="checkbox"
                id="autoCleanup"
                checked={settings.storage.autoCleanup}
                onChange={(e) => onSettingChange('storage.autoCleanup', e.target.checked)}
                disabled={isLoading}
              />
              <label htmlFor="autoCleanup" className="toggle-label">
                <span className="toggle-slider"></span>
              </label>
            </div>
          </div>
        </div>

        {/* 备份设置 */}
        <div className="subsection">
          <h4>数据备份</h4>
          <div className="setting-group">
            <label className="setting-label">
              <span className="label-text">启用备份</span>
              <span className="label-description">自动备份重要数据</span>
            </label>
            <div className="toggle-switch">
              <input
                type="checkbox"
                id="backupEnabled"
                checked={settings.backup.enabled}
                onChange={(e) => onSettingChange('backup.enabled', e.target.checked)}
                disabled={isLoading}
              />
              <label htmlFor="backupEnabled" className="toggle-label">
                <span className="toggle-slider"></span>
              </label>
            </div>
          </div>

          <div className="setting-group">
            <label className="setting-label">
              <span className="label-text">备份频率</span>
              <span className="label-description">自动备份的时间间隔</span>
            </label>
            <select
              className="setting-input"
              value={settings.backup.frequency}
              onChange={(e) => onSettingChange('backup.frequency', e.target.value)}
              disabled={isLoading || !settings.backup.enabled}
            >
              <option value={BackupFrequency.HOURLY}>每小时</option>
              <option value={BackupFrequency.DAILY}>每天</option>
              <option value={BackupFrequency.WEEKLY}>每周</option>
              <option value={BackupFrequency.MONTHLY}>每月</option>
            </select>
          </div>

          <div className="setting-group">
            <label className="setting-label">
              <span className="label-text">备份位置</span>
              <span className="label-description">选择备份存储位置</span>
            </label>
            <select
              className="setting-input"
              value={settings.backup.location}
              onChange={(e) => onSettingChange('backup.location', e.target.value)}
              disabled={isLoading || !settings.backup.enabled}
            >
              <option value={BackupLocation.LOCAL}>本地存储</option>
              <option value={BackupLocation.CLOUD}>云端存储</option>
              <option value={BackupLocation.BOTH}>本地 + 云端</option>
            </select>
          </div>

          <div className="setting-group">
            <label className="setting-label">
              <span className="label-text">备份保留天数</span>
              <span className="label-description">备份文件保留时间</span>
            </label>
            <div className="number-input-wrapper">
              <input
                type="number"
                min="1"
                max="365"
                value={settings.backup.retention}
                onChange={(e) => onSettingChange('backup.retention', parseInt(e.target.value))}
                disabled={isLoading || !settings.backup.enabled}
              />
              <span className="input-unit">天</span>
            </div>
          </div>

          <div className="setting-group">
            <label className="setting-label">
              <span className="label-text">备份加密</span>
              <span className="label-description">加密备份文件</span>
            </label>
            <div className="toggle-switch">
              <input
                type="checkbox"
                id="backupEncryption"
                checked={settings.backup.encryption}
                onChange={(e) => onSettingChange('backup.encryption', e.target.checked)}
                disabled={isLoading || !settings.backup.enabled}
              />
              <label htmlFor="backupEncryption" className="toggle-label">
                <span className="toggle-slider"></span>
              </label>
            </div>
          </div>
        </div>

        {/* 同步设置 */}
        <div className="subsection">
          <h4>数据同步</h4>
          <div className="setting-group">
            <label className="setting-label">
              <span className="label-text">启用同步</span>
              <span className="label-description">在设备间同步数据</span>
            </label>
            <div className="toggle-switch">
              <input
                type="checkbox"
                id="syncEnabled"
                checked={settings.sync.enabled}
                onChange={(e) => onSettingChange('sync.enabled', e.target.checked)}
                disabled={isLoading}
              />
              <label htmlFor="syncEnabled" className="toggle-label">
                <span className="toggle-slider"></span>
              </label>
            </div>
          </div>

          <div className="setting-group">
            <label className="setting-label">
              <span className="label-text">同步频率</span>
              <span className="label-description">数据同步的时间间隔</span>
            </label>
            <select
              className="setting-input"
              value={settings.sync.frequency}
              onChange={(e) => onSettingChange('sync.frequency', e.target.value)}
              disabled={isLoading || !settings.sync.enabled}
            >
              <option value={SyncFrequency.REALTIME}>实时同步</option>
              <option value={SyncFrequency.EVERY_MINUTE}>每分钟</option>
              <option value={SyncFrequency.EVERY_HOUR}>每小时</option>
              <option value={SyncFrequency.MANUAL}>手动同步</option>
            </select>
          </div>

          <div className="setting-group">
            <label className="setting-label">
              <span className="label-text">冲突解决策略</span>
              <span className="label-description">当数据冲突时的处理方式</span>
            </label>
            <select
              className="setting-input"
              value={settings.sync.conflicts}
              onChange={(e) => onSettingChange('sync.conflicts', e.target.value)}
              disabled={isLoading || !settings.sync.enabled}
            >
              <option value={ConflictResolution.SERVER_WINS}>服务器优先</option>
              <option value={ConflictResolution.CLIENT_WINS}>本地优先</option>
              <option value={ConflictResolution.MERGE}>智能合并</option>
              <option value={ConflictResolution.ASK_USER}>询问用户</option>
            </select>
          </div>
        </div>

        {/* 导出设置 */}
        <div className="subsection">
          <h4>数据导出</h4>
          <div className="setting-group">
            <label className="setting-label">
              <span className="label-text">导出格式</span>
              <span className="label-description">选择数据导出格式</span>
            </label>
            <select
              className="setting-input"
              value={settings.export.format}
              onChange={(e) => onSettingChange('export.format', e.target.value)}
              disabled={isLoading}
            >
              <option value={ExportFormat.JSON}>JSON 格式</option>
              <option value={ExportFormat.CSV}>CSV 格式</option>
              <option value={ExportFormat.XML}>XML 格式</option>
              <option value={ExportFormat.PDF}>PDF 报告</option>
            </select>
          </div>

          <div className="setting-group">
            <label className="setting-label">
              <span className="label-text">包含个人数据</span>
              <span className="label-description">导出时包含个人敏感信息</span>
            </label>
            <div className="toggle-switch">
              <input
                type="checkbox"
                id="includePersonalData"
                checked={settings.export.includePersonalData}
                onChange={(e) => onSettingChange('export.includePersonalData', e.target.checked)}
                disabled={isLoading}
              />
              <label htmlFor="includePersonalData" className="toggle-label">
                <span className="toggle-slider"></span>
              </label>
            </div>
          </div>

          <div className="setting-group">
            <label className="setting-label">
              <span className="label-text">包含分析数据</span>
              <span className="label-description">导出时包含统计和分析数据</span>
            </label>
            <div className="toggle-switch">
              <input
                type="checkbox"
                id="includeAnalytics"
                checked={settings.export.includeAnalytics}
                onChange={(e) => onSettingChange('export.includeAnalytics', e.target.checked)}
                disabled={isLoading}
              />
              <label htmlFor="includeAnalytics" className="toggle-label">
                <span className="toggle-slider"></span>
              </label>
            </div>
          </div>

          <div className="setting-group">
            <label className="setting-label">
              <span className="label-text">导出加密</span>
              <span className="label-description">加密导出的数据文件</span>
            </label>
            <div className="toggle-switch">
              <input
                type="checkbox"
                id="exportEncryption"
                checked={settings.export.encryption}
                onChange={(e) => onSettingChange('export.encryption', e.target.checked)}
                disabled={isLoading}
              />
              <label htmlFor="exportEncryption" className="toggle-label">
                <span className="toggle-slider"></span>
              </label>
            </div>
          </div>
        </div>

        {/* 导入设置 */}
        <div className="subsection">
          <h4>数据导入</h4>
          <div className="setting-group">
            <label className="setting-label">
              <span className="label-text">数据验证</span>
              <span className="label-description">导入前验证数据完整性</span>
            </label>
            <div className="toggle-switch">
              <input
                type="checkbox"
                id="validateData"
                checked={settings.import.validateData}
                onChange={(e) => onSettingChange('import.validateData', e.target.checked)}
                disabled={isLoading}
              />
              <label htmlFor="validateData" className="toggle-label">
                <span className="toggle-slider"></span>
              </label>
            </div>
          </div>

          <div className="setting-group">
            <label className="setting-label">
              <span className="label-text">合并策略</span>
              <span className="label-description">导入数据时的合并策略</span>
            </label>
            <select
              className="setting-input"
              value={settings.import.mergeStrategy}
              onChange={(e) => onSettingChange('import.mergeStrategy', e.target.value)}
              disabled={isLoading}
            >
              <option value={MergeStrategy.REPLACE}>替换现有数据</option>
              <option value={MergeStrategy.MERGE}>智能合并</option>
              <option value={MergeStrategy.SKIP}>跳过重复数据</option>
            </select>
          </div>

          <div className="setting-group">
            <label className="setting-label">
              <span className="label-text">导入前备份</span>
              <span className="label-description">导入前自动备份现有数据</span>
            </label>
            <div className="toggle-switch">
              <input
                type="checkbox"
                id="backupBeforeImport"
                checked={settings.import.backupBeforeImport}
                onChange={(e) => onSettingChange('import.backupBeforeImport', e.target.checked)}
                disabled={isLoading}
              />
              <label htmlFor="backupBeforeImport" className="toggle-label">
                <span className="toggle-slider"></span>
              </label>
            </div>
          </div>
        </div>

        {/* 缓存设置 */}
        <div className="subsection">
          <h4>缓存管理</h4>
          <div className="setting-group">
            <label className="setting-label">
              <span className="label-text">启用缓存</span>
              <span className="label-description">缓存数据以提升性能</span>
            </label>
            <div className="toggle-switch">
              <input
                type="checkbox"
                id="cacheEnabled"
                checked={settings.cache.enabled}
                onChange={(e) => onSettingChange('cache.enabled', e.target.checked)}
                disabled={isLoading}
              />
              <label htmlFor="cacheEnabled" className="toggle-label">
                <span className="toggle-slider"></span>
              </label>
            </div>
          </div>

          <div className="setting-group">
            <label className="setting-label">
              <span className="label-text">缓存大小 (MB)</span>
              <span className="label-description">限制缓存使用的内存</span>
            </label>
            <div className="number-input-wrapper">
              <input
                type="number"
                min="10"
                max="1000"
                value={settings.cache.maxSize}
                onChange={(e) => onSettingChange('cache.maxSize', parseInt(e.target.value))}
                disabled={isLoading || !settings.cache.enabled}
              />
              <span className="input-unit">MB</span>
            </div>
          </div>

          <div className="setting-group">
            <label className="setting-label">
              <span className="label-text">缓存过期时间 (小时)</span>
              <span className="label-description">缓存数据的有效期</span>
            </label>
            <div className="number-input-wrapper">
              <input
                type="number"
                min="1"
                max="168"
                value={settings.cache.maxAge}
                onChange={(e) => onSettingChange('cache.maxAge', parseInt(e.target.value))}
                disabled={isLoading || !settings.cache.enabled}
              />
              <span className="input-unit">小时</span>
            </div>
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="subsection">
          <h4>数据操作</h4>
          <div className="action-buttons">
            <button 
              className="btn btn-secondary" 
              onClick={handleExportAllData}
              disabled={isLoading || isOperationRunning}
            >
              🗂️ 导出所有数据
            </button>
            <button 
              className="btn btn-secondary" 
              onClick={handleImportData}
              disabled={isLoading || isOperationRunning}
            >
              📁 导入数据
            </button>
            <button 
              className="btn btn-primary" 
              onClick={handleCreateBackup}
              disabled={isLoading || isOperationRunning}
            >
              💾 立即备份
            </button>
            <button 
              className="btn btn-secondary" 
              onClick={handleManualSync}
              disabled={isLoading || isOperationRunning || !settings.sync.enabled}
            >
              🔄 手动同步
            </button>
            <button 
              className="btn btn-warning" 
              onClick={handleClearCache}
              disabled={isLoading || isOperationRunning}
            >
              🗑️ 清理缓存
            </button>
          </div>
        </div>
      </div>

      <div className="section-footer">
        <div className="footer-info">
          <span className="info-icon">ℹ️</span>
          <span>数据设置更改可能需要重启应用才能完全生效</span>
        </div>
      </div>
    </div>
  )
}

export default DataSettings 