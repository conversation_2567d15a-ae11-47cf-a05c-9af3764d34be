import React from 'react'
import { FocusSettings as FocusSettingsType } from '../../types/settings.types'

interface FocusSettingsProps {
  settings: FocusSettingsType
  onSettingChange: (key: string, value: any) => void
  isLoading: boolean
}

export const FocusSettings: React.FC<FocusSettingsProps> = ({
  settings,
  onSettingChange,
  isLoading
}) => {
  return (
    <div className="settings-section">
      <div className="section-header">
        <h3>专注设置</h3>
        <p>配置专注目标和检测敏感度</p>
      </div>

      <div className="settings-grid">
        <div className="setting-group">
          <label className="setting-label">
            <span className="label-text">启用专注检测</span>
          </label>
          <input
            type="checkbox"
            checked={settings.enabled}
            onChange={(e) => onSettingChange('enabled', e.target.checked)}
            disabled={isLoading}
          />
        </div>

        <div className="setting-group">
          <label className="setting-label">
            <span className="label-text">运动敏感度</span>
          </label>
          <input
            type="range"
            min="0"
            max="100"
            value={settings.sensitivity.movement}
            onChange={(e) => onSettingChange('sensitivity.movement', parseInt(e.target.value))}
            disabled={isLoading}
          />
          <span>{settings.sensitivity.movement}%</span>
        </div>

        <div className="setting-group">
          <label className="setting-label">
            <span className="label-text">每日专注时间（分钟）</span>
          </label>
          <input
            type="number"
            min="30"
            max="480"
            value={settings.targets.dailyGoals.focusTime}
            onChange={(e) => onSettingChange('targets.dailyGoals.focusTime', parseInt(e.target.value))}
            disabled={isLoading}
          />
        </div>
      </div>
    </div>
  )
}

export default FocusSettings 