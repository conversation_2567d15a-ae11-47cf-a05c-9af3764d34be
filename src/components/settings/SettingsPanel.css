/* 设置面板主容器 */
.settings-panel {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  color: #333;
}

/* 设置头部 */
.settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 32px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.settings-title h1 {
  margin: 0 0 4px 0;
  font-size: 28px;
  font-weight: 700;
  color: #2d3748;
}

.settings-title p {
  margin: 0;
  font-size: 16px;
  color: #718096;
}

.settings-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.unsaved-indicator {
  padding: 6px 12px;
  background: #fed7d7;
  color: #c53030;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
}

.settings-buttons {
  display: flex;
  gap: 12px;
}

/* 按钮样式 */
.btn {
  padding: 10px 16px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: #4299e1;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #3182ce;
  transform: translateY(-1px);
}

.btn-secondary {
  background: #e2e8f0;
  color: #4a5568;
}

.btn-secondary:hover:not(:disabled) {
  background: #cbd5e0;
  transform: translateY(-1px);
}

.btn-warning {
  background: #fed7d7;
  color: #c53030;
}

.btn-warning:hover:not(:disabled) {
  background: #feb2b2;
  transform: translateY(-1px);
}

/* 错误消息 */
.settings-error {
  padding: 16px 32px;
  background: #fed7d7;
  border-bottom: 1px solid #feb2b2;
}

.error-message {
  display: flex;
  align-items: center;
  gap: 12px;
  color: #c53030;
  font-weight: 500;
}

.error-close {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: #c53030;
  margin-left: auto;
}

/* 设置内容区域 */
.settings-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

/* 设置导航 */
.settings-navigation {
  width: 280px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-right: 1px solid rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
}

.navigation-header {
  padding: 24px 20px 16px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 8px;
}

.navigation-header h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #2d3748;
}

.unsaved-badge {
  width: 8px;
  height: 8px;
  background: #f56565;
  border-radius: 50%;
  color: transparent;
}

.navigation-list {
  flex: 1;
  list-style: none;
  margin: 0;
  padding: 8px 0;
  overflow-y: auto;
}

.navigation-item {
  margin: 0;
}

.navigation-button {
  width: 100%;
  padding: 16px 20px;
  border: none;
  background: transparent;
  text-align: left;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 12px;
  transition: all 0.2s ease;
  color: #4a5568;
}

.navigation-button:hover {
  background: rgba(79, 172, 254, 0.1);
  color: #2b6cb0;
}

.navigation-button.active {
  background: rgba(79, 172, 254, 0.15);
  color: #2b6cb0;
  font-weight: 600;
  border-right: 3px solid #4299e1;
}

.navigation-icon {
  font-size: 20px;
  width: 24px;
  text-align: center;
}

.navigation-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.navigation-label {
  font-size: 15px;
  font-weight: 500;
}

.navigation-description {
  font-size: 13px;
  opacity: 0.7;
}

.navigation-indicator {
  margin-left: auto;
  opacity: 0.6;
}

.navigation-footer {
  padding: 16px 20px;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.navigation-tip {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  font-size: 13px;
  color: #718096;
  line-height: 1.4;
}

.tip-icon {
  font-size: 14px;
  margin-top: 1px;
}

/* 设置主要内容 */
.settings-main {
  flex: 1;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  overflow-y: auto;
}

.settings-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  gap: 16px;
  color: #718096;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(79, 172, 254, 0.2);
  border-top: 3px solid #4299e1;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.settings-content-area {
  padding: 32px;
}

/* 设置节 */
.settings-section {
  max-width: 800px;
  margin: 0 auto;
}

.section-header {
  margin-bottom: 32px;
}

.section-header h3 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 700;
  color: #2d3748;
}

.section-header p {
  margin: 0;
  font-size: 16px;
  color: #718096;
}

/* 设置网格 */
.settings-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.setting-group {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
}

.setting-group:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.setting-group.full-width {
  grid-column: 1 / -1;
}

/* 设置标签 */
.setting-label {
  display: block;
  margin-bottom: 12px;
}

.label-text {
  display: block;
  font-size: 16px;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 4px;
}

.label-description {
  display: block;
  font-size: 14px;
  color: #718096;
  line-height: 1.4;
}

/* 设置输入控件 */
.setting-input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.2s ease;
  background: white;
}

.setting-input:focus {
  outline: none;
  border-color: #4299e1;
  box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
}

.setting-input:disabled {
  background: #f7fafc;
  color: #a0aec0;
  cursor: not-allowed;
}

/* 数字输入包装器 */
.number-input-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
}

.input-unit {
  font-size: 14px;
  color: #718096;
  font-weight: 500;
}

/* 设置提示 */
.setting-hint {
  margin-top: 8px;
  font-size: 13px;
  color: #718096;
  font-style: italic;
}

/* 单选按钮组 */
.radio-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.radio-item {
  position: relative;
}

.radio-label {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  cursor: pointer;
  padding: 16px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.radio-label:hover {
  border-color: #cbd5e0;
}

.radio-label:has(input:checked) {
  border-color: #4299e1;
  background: rgba(66, 153, 225, 0.05);
}

.radio-label input[type="radio"] {
  display: none;
}

.radio-custom {
  width: 20px;
  height: 20px;
  border: 2px solid #e2e8f0;
  border-radius: 50%;
  position: relative;
  transition: all 0.2s ease;
  flex-shrink: 0;
  margin-top: 2px;
}

.radio-label:has(input:checked) .radio-custom {
  border-color: #4299e1;
}

.radio-label:has(input:checked) .radio-custom::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 8px;
  height: 8px;
  background: #4299e1;
  border-radius: 50%;
}

.radio-content {
  flex: 1;
}

.radio-title {
  display: block;
  font-size: 16px;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 4px;
}

.radio-description {
  display: block;
  font-size: 14px;
  color: #718096;
  line-height: 1.4;
}

/* 切换开关 */
.toggle-switch {
  position: relative;
}

.toggle-switch input[type="checkbox"] {
  display: none;
}

.toggle-label {
  display: block;
  width: 52px;
  height: 28px;
  background: #e2e8f0;
  border-radius: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.toggle-label:has(input:checked) {
  background: #4299e1;
}

.toggle-slider {
  position: absolute;
  top: 2px;
  left: 2px;
  width: 24px;
  height: 24px;
  background: white;
  border-radius: 12px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.toggle-label:has(input:checked) .toggle-slider {
  transform: translateX(24px);
}

/* 范围滑块 */
input[type="range"] {
  -webkit-appearance: none;
  appearance: none;
  width: 100%;
  height: 6px;
  background: #e2e8f0;
  border-radius: 3px;
  outline: none;
}

input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  background: #4299e1;
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
}

input[type="range"]::-webkit-slider-thumb:hover {
  transform: scale(1.1);
}

input[type="range"]::-moz-range-thumb {
  width: 20px;
  height: 20px;
  background: #4299e1;
  border-radius: 50%;
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* 节底部 */
.section-footer {
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.footer-info {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #718096;
}

/* 占位符样式 */
.settings-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 400px;
  color: #718096;
  font-size: 18px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .settings-panel {
    height: 100vh;
  }
  
  .settings-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
    padding: 16px 20px;
  }
  
  .settings-content {
    flex-direction: column;
  }
  
  .settings-navigation {
    width: 100%;
    max-height: 200px;
    overflow-y: auto;
  }
  
  .navigation-list {
    display: flex;
    overflow-x: auto;
    padding: 8px;
    gap: 8px;
  }
  
  .navigation-item {
    flex-shrink: 0;
  }
  
  .navigation-button {
    min-width: 140px;
    padding: 12px 16px;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
  }
  
  .navigation-button.active {
    border-color: #4299e1;
    border-right: 1px solid #4299e1;
  }
  
  .settings-content-area {
    padding: 20px;
  }
  
  .settings-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .setting-group {
    padding: 20px;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .settings-panel {
    background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
    color: #e2e8f0;
  }
  
  .settings-header,
  .settings-navigation,
  .settings-main {
    background: rgba(45, 55, 72, 0.95);
    color: #e2e8f0;
  }
  
  .setting-group {
    background: rgba(45, 55, 72, 0.8);
    border-color: rgba(255, 255, 255, 0.1);
  }
  
  .setting-input {
    background: rgba(45, 55, 72, 0.6);
    border-color: rgba(255, 255, 255, 0.2);
    color: #e2e8f0;
  }
  
  .navigation-button {
    color: #cbd5e0;
  }
  
  .navigation-button:hover {
    background: rgba(79, 172, 254, 0.2);
    color: #90cdf4;
  }
  
  .navigation-button.active {
    background: rgba(79, 172, 254, 0.3);
    color: #90cdf4;
  }
}

/* 操作状态提示 */
.operation-status {
  margin-bottom: 24px;
  padding: 12px 16px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  animation: slideIn 0.3s ease-out;
}

.operation-status.success {
  background: rgba(72, 187, 120, 0.1);
  border: 1px solid rgba(72, 187, 120, 0.3);
  color: #2f855a;
}

.operation-status.error {
  background: rgba(245, 101, 101, 0.1);
  border: 1px solid rgba(245, 101, 101, 0.3);
  color: #c53030;
}

.operation-status.info {
  background: rgba(66, 153, 225, 0.1);
  border: 1px solid rgba(66, 153, 225, 0.3);
  color: #2b6cb0;
}

.operation-status::before {
  content: '';
  width: 4px;
  height: 20px;
  border-radius: 2px;
}

.operation-status.success::before {
  background: #48bb78;
}

.operation-status.error::before {
  background: #f56565;
}

.operation-status.info::before {
  background: #4299e1;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 存储使用情况面板 */
.storage-usage-panel {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.05);
  margin-bottom: 24px;
  transition: all 0.2s ease;
}

.storage-usage-panel:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.storage-usage-panel h4 {
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 600;
  color: #2d3748;
}

.usage-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
}

.usage-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
  text-align: center;
}

.usage-label {
  font-size: 12px;
  color: #718096;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.usage-value {
  font-size: 16px;
  font-weight: 700;
  color: #2d3748;
}

.usage-bar {
  width: 100%;
  height: 8px;
  background: #e2e8f0;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

.usage-fill {
  height: 100%;
  background: linear-gradient(90deg, #48bb78 0%, #4299e1 50%, #ed8936 100%);
  border-radius: 4px;
  transition: width 0.3s ease;
  position: relative;
}

.usage-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* 子节样式 */
.subsection {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
}

.subsection:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.subsection h4 {
  margin: 0 0 20px 0;
  font-size: 18px;
  font-weight: 600;
  color: #2d3748;
  padding-bottom: 12px;
  border-bottom: 2px solid #e2e8f0;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.action-buttons .btn {
  flex: 1;
  min-width: 160px;
  padding: 12px 20px;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.action-buttons .btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.action-buttons .btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* 文件输入包装器 */
.file-input-wrapper {
  display: flex;
  gap: 8px;
  align-items: center;
}

.file-input-wrapper .setting-input {
  flex: 1;
}

.file-input-wrapper .btn {
  flex-shrink: 0;
  padding: 12px 16px;
  font-size: 14px;
}

.btn-small {
  padding: 8px 12px;
  font-size: 13px;
}

/* 深色模式下的额外样式 */
@media (prefers-color-scheme: dark) {
  .storage-usage-panel,
  .subsection {
    background: rgba(45, 55, 72, 0.8);
    border-color: rgba(255, 255, 255, 0.1);
  }
  
  .storage-usage-panel h4,
  .subsection h4 {
    color: #e2e8f0;
  }
  
  .usage-value {
    color: #e2e8f0;
  }
  
  .usage-bar {
    background: rgba(255, 255, 255, 0.1);
  }
  
  .operation-status.success {
    background: rgba(72, 187, 120, 0.2);
    color: #68d391;
  }
  
  .operation-status.error {
    background: rgba(245, 101, 101, 0.2);
    color: #fc8181;
  }
  
  .operation-status.info {
    background: rgba(66, 153, 225, 0.2);
    color: #90cdf4;
  }
}

/* 响应式设计 - 移动端适配 */
@media (max-width: 768px) {
  .storage-usage-panel,
  .subsection {
    padding: 16px;
  }
  
  .usage-info {
    flex-direction: column;
    gap: 16px;
  }
  
  .usage-item {
    flex-direction: row;
    justify-content: space-between;
    text-align: left;
  }
  
  .action-buttons {
    flex-direction: column;
  }
  
  .action-buttons .btn {
    min-width: auto;
    width: 100%;
  }
  
  .file-input-wrapper {
    flex-direction: column;
  }
  
  .file-input-wrapper .btn {
    width: 100%;
  }
} 