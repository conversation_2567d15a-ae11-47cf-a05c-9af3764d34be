import React from 'react'
import { NotificationSettings as NotificationSettingsType } from '../../types/settings.types'

interface NotificationSettingsProps {
  settings: NotificationSettingsType
  onSettingChange: (key: string, value: any) => void
  isLoading: boolean
}

export const NotificationSettings: React.FC<NotificationSettingsProps> = ({ settings, onSettingChange, isLoading }) => {
  return (
    <div className="settings-section">
      <div className="section-header">
        <h3>通知设置</h3>
        <p>配置提醒和通知偏好</p>
      </div>
      <div className="settings-grid">
        <div className="setting-group">
          <label className="setting-label">
            <span className="label-text">启用通知</span>
          </label>
          <input
            type="checkbox"
            checked={settings.enabled}
            onChange={(e) => onSettingChange('enabled', e.target.checked)}
            disabled={isLoading}
          />
        </div>
      </div>
    </div>
  )
}

export default NotificationSettings 