/* 摄像头设置组件样式 */

/* 权限请求界面 */
.permission-request {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 48px 24px;
  text-align: center;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.permission-icon {
  font-size: 64px;
  margin-bottom: 16px;
  opacity: 0.7;
}

.permission-request h4 {
  margin: 0 0 12px 0;
  font-size: 24px;
  font-weight: 600;
  color: #2d3748;
}

.permission-request p {
  margin: 0 0 24px 0;
  font-size: 16px;
  color: #718096;
  line-height: 1.5;
  max-width: 400px;
}

/* 错误消息 */
.error-message {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: #fed7d7;
  color: #c53030;
  border-radius: 8px;
  margin-bottom: 24px;
  font-weight: 500;
}

.error-icon {
  font-size: 18px;
}

.error-message button {
  background: none;
  border: none;
  color: #c53030;
  font-size: 18px;
  cursor: pointer;
  margin-left: auto;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
}

.error-message button:hover {
  background: rgba(197, 48, 48, 0.1);
}

/* 范围输入包装器 */
.range-input-wrapper {
  display: flex;
  align-items: center;
  gap: 12px;
}

.range-input-wrapper input[type="range"] {
  flex: 1;
}

.range-value {
  min-width: 40px;
  text-align: right;
  font-weight: 500;
  color: #4a5568;
  font-size: 14px;
}

/* 摄像头控制区域 */
.camera-controls {
  margin-top: 32px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.controls-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  background: #f7fafc;
}

.controls-header h4 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #2d3748;
}

.control-buttons {
  display: flex;
  gap: 12px;
}

.btn-small {
  padding: 6px 12px;
  font-size: 13px;
}

/* 摄像头预览 */
.camera-preview {
  position: relative;
  padding: 24px;
  background: #000;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.preview-video {
  width: 100%;
  max-width: 640px;
  height: auto;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  display: block;
  margin: 0 auto;
}

.preview-info {
  display: flex;
  justify-content: center;
  gap: 24px;
  margin-top: 16px;
  font-size: 14px;
  color: #a0aec0;
}

/* 设备测试 */
.device-testing {
  padding: 24px;
}

.device-testing h5 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #2d3748;
}

.device-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.device-item {
  padding: 20px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  background: #fafafa;
  transition: all 0.2s ease;
}

.device-item:hover {
  border-color: #cbd5e0;
  background: white;
}

.device-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-bottom: 12px;
}

.device-name {
  font-weight: 600;
  color: #2d3748;
  font-size: 15px;
}

.device-id {
  font-size: 13px;
  color: #718096;
  font-family: 'Courier New', monospace;
}

.device-actions {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
}

/* 测试结果 */
.test-result {
  padding: 16px;
  border-radius: 8px;
  border: 1px solid;
  margin-top: 12px;
}

.test-result.success {
  background: #f0fff4;
  border-color: #68d391;
  color: #22543d;
}

.test-result.error {
  background: #fed7d7;
  border-color: #fc8181;
  color: #c53030;
}

.result-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  font-weight: 600;
}

.result-icon {
  font-size: 16px;
}

.result-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 8px;
}

.result-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
  font-size: 14px;
}

.result-row span:first-child {
  color: #4a5568;
}

.result-row span:last-child {
  font-weight: 600;
  color: #2d3748;
}

.result-errors {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.error-item {
  padding: 4px 8px;
  background: rgba(197, 48, 48, 0.1);
  border-radius: 4px;
  font-size: 13px;
  font-family: 'Courier New', monospace;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .permission-request {
    padding: 32px 20px;
  }
  
  .permission-icon {
    font-size: 48px;
  }
  
  .controls-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .control-buttons {
    justify-content: center;
  }
  
  .camera-preview {
    padding: 16px;
  }
  
  .preview-info {
    flex-direction: column;
    gap: 8px;
    text-align: center;
  }
  
  .device-testing {
    padding: 16px;
  }
  
  .device-item {
    padding: 16px;
  }
  
  .result-details {
    grid-template-columns: 1fr;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .permission-request {
    background: rgba(45, 55, 72, 0.8);
    border-color: rgba(255, 255, 255, 0.1);
    color: #e2e8f0;
  }
  
  .permission-request h4 {
    color: #e2e8f0;
  }
  
  .permission-request p {
    color: #cbd5e0;
  }
  
  .camera-controls {
    background: rgba(45, 55, 72, 0.8);
    border-color: rgba(255, 255, 255, 0.1);
  }
  
  .controls-header {
    background: rgba(26, 32, 44, 0.6);
    color: #e2e8f0;
  }
  
  .controls-header h4 {
    color: #e2e8f0;
  }
  
  .device-testing h5 {
    color: #e2e8f0;
  }
  
  .device-item {
    background: rgba(26, 32, 44, 0.4);
    border-color: rgba(255, 255, 255, 0.2);
    color: #e2e8f0;
  }
  
  .device-item:hover {
    background: rgba(45, 55, 72, 0.6);
    border-color: rgba(255, 255, 255, 0.3);
  }
  
  .device-name {
    color: #e2e8f0;
  }
  
  .device-id {
    color: #cbd5e0;
  }
  
  .test-result.success {
    background: rgba(34, 84, 61, 0.2);
    border-color: rgba(104, 211, 145, 0.4);
    color: #68d391;
  }
  
  .test-result.error {
    background: rgba(197, 48, 48, 0.2);
    border-color: rgba(252, 129, 129, 0.4);
    color: #fc8181;
  }
  
  .result-row span:first-child {
    color: #cbd5e0;
  }
  
  .result-row span:last-child {
    color: #e2e8f0;
  }
  
  .error-item {
    background: rgba(197, 48, 48, 0.2);
    color: #fc8181;
  }
} 