import React, { useState, useEffect } from 'react'
import { settingsService } from '../../services/SettingsService'
import { UserSettings, SettingsCategory, SettingsEventType } from '../../types/settings.types'
import { SettingsNavigation } from './SettingsNavigation'
import { GeneralSettings } from './GeneralSettings'
import { CameraSettings } from './CameraSettings'
import { AudioSettings } from './AudioSettings'
import { FocusSettings } from './FocusSettings'
import { PrivacySettings } from './PrivacySettings'
import { NotificationSettings } from './NotificationSettings'
import { AppearanceSettings } from './AppearanceSettings'
import { DataSettings } from './DataSettings'
import './SettingsPanel.css'

interface SettingsPanelProps {
  isVisible?: boolean
  onClose?: () => void
  initialCategory?: SettingsCategory
}

/**
 * 主设置面板组件
 */
export const SettingsPanel: React.FC<SettingsPanelProps> = ({
  isVisible = true,
  onClose,
  initialCategory = SettingsCategory.GENERAL
}) => {
  const [settings, setSettings] = useState<UserSettings | null>(null)
  const [activeCategory, setActiveCategory] = useState<SettingsCategory>(initialCategory)
  const [isLoading, setIsLoading] = useState(true)
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // 初始化设置服务
  useEffect(() => {
    const initializeSettings = async () => {
      try {
        setIsLoading(true)
        setError(null)
        
        if (!settingsService.isReady()) {
          await settingsService.initialize()
        }
        
        const userSettings = settingsService.getSettings()
        setSettings(userSettings)
      } catch (err) {
        setError(err instanceof Error ? err.message : '加载设置失败')
        console.error('设置初始化失败:', err)
      } finally {
        setIsLoading(false)
      }
    }

    initializeSettings()
  }, [])

  // 监听设置变更
  useEffect(() => {
    const handleSettingsChange = () => {
      const updatedSettings = settingsService.getSettings()
      setSettings(updatedSettings)
      setHasUnsavedChanges(false)
    }

    const handleSettingsSaved = () => {
      setHasUnsavedChanges(false)
    }

    settingsService.addEventListener(SettingsEventType.CHANGED, handleSettingsChange)
    settingsService.addEventListener(SettingsEventType.SAVED, handleSettingsSaved)

    return () => {
      settingsService.removeEventListener(SettingsEventType.CHANGED, handleSettingsChange)
      settingsService.removeEventListener(SettingsEventType.SAVED, handleSettingsSaved)
    }
  }, [])

  // 处理设置更新
  const handleSettingChange = async (category: SettingsCategory, key: string, value: any) => {
    try {
      setError(null)
      setHasUnsavedChanges(true)
      await settingsService.updateSettings(category, key, value)
    } catch (err) {
      setError(err instanceof Error ? err.message : '更新设置失败')
      console.error('更新设置失败:', err)
    }
  }

  // 处理类别切换
  const handleCategoryChange = (category: SettingsCategory) => {
    if (hasUnsavedChanges) {
      const confirmed = window.confirm('您有未保存的更改，确定要切换到其他设置页面吗？')
      if (!confirmed) return
    }
    setActiveCategory(category)
    setError(null)
  }

  // 处理重置设置
  const handleResetCategory = async () => {
    try {
      const confirmed = window.confirm('确定要重置此类别的所有设置到默认值吗？此操作不可撤销。')
      if (!confirmed) return

      setError(null)
      await settingsService.resetSettings(activeCategory)
    } catch (err) {
      setError(err instanceof Error ? err.message : '重置设置失败')
      console.error('重置设置失败:', err)
    }
  }

  // 处理导出设置
  const handleExportSettings = async () => {
    try {
      setError(null)
      const exportData = await settingsService.exportSettings()
      const blob = new Blob([exportData], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `selfgame-settings-${new Date().toISOString().split('T')[0]}.json`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    } catch (err) {
      setError(err instanceof Error ? err.message : '导出设置失败')
      console.error('导出设置失败:', err)
    }
  }

  // 处理导入设置
  const handleImportSettings = async (event: React.ChangeEvent<HTMLInputElement>) => {
    try {
      const file = event.target.files?.[0]
      if (!file) return

      setError(null)
      const text = await file.text()
      await settingsService.importSettings(text)
      
      // 重置文件输入
      event.target.value = ''
    } catch (err) {
      setError(err instanceof Error ? err.message : '导入设置失败')
      console.error('导入设置失败:', err)
    }
  }

  // 处理关闭
  const handleClose = () => {
    if (hasUnsavedChanges) {
      const confirmed = window.confirm('您有未保存的更改，确定要关闭设置面板吗？')
      if (!confirmed) return
    }
    onClose?.()
  }

  // 渲染当前设置内容
  const renderSettingsContent = () => {
    if (!settings) return null

    const commonProps = {
      settings: settings[activeCategory],
      onSettingChange: (key: string, value: any) => handleSettingChange(activeCategory, key, value),
      isLoading
    }

    switch (activeCategory) {
      case SettingsCategory.GENERAL:
        return <GeneralSettings {...commonProps} />
      case SettingsCategory.CAMERA:
        return <CameraSettings {...commonProps} />
      case SettingsCategory.AUDIO:
        return <AudioSettings {...commonProps} />
      case SettingsCategory.FOCUS:
        return <FocusSettings {...commonProps} />
      case SettingsCategory.PRIVACY:
        return <PrivacySettings {...commonProps} />
      case SettingsCategory.NOTIFICATIONS:
        return <NotificationSettings {...commonProps} />
      case SettingsCategory.APPEARANCE:
        return <AppearanceSettings {...commonProps} />
      case SettingsCategory.DATA:
        return <DataSettings {...commonProps} />
      default:
        return <div className="settings-placeholder">设置类别未找到</div>
    }
  }

  if (!isVisible) return null

  return (
    <div className="settings-panel">
      <div className="settings-header">
        <div className="settings-title">
          <h1>应用设置</h1>
          <p>个性化您的自律农场体验</p>
        </div>
        
        <div className="settings-actions">
          {hasUnsavedChanges && (
            <span className="unsaved-indicator">有未保存的更改</span>
          )}
          
          <div className="settings-buttons">
            <input
              type="file"
              accept=".json"
              onChange={handleImportSettings}
              style={{ display: 'none' }}
              id="import-settings"
            />
            <label htmlFor="import-settings" className="btn btn-secondary">
              导入设置
            </label>
            
            <button 
              className="btn btn-secondary" 
              onClick={handleExportSettings}
              disabled={isLoading}
            >
              导出设置
            </button>
            
            <button 
              className="btn btn-warning" 
              onClick={handleResetCategory}
              disabled={isLoading}
            >
              重置当前类别
            </button>
            
            {onClose && (
              <button 
                className="btn btn-primary" 
                onClick={handleClose}
              >
                关闭
              </button>
            )}
          </div>
        </div>
      </div>

      {error && (
        <div className="settings-error">
          <div className="error-message">
            <span className="error-icon">⚠️</span>
            <span>{error}</span>
            <button 
              className="error-close" 
              onClick={() => setError(null)}
              aria-label="关闭错误消息"
            >
              ×
            </button>
          </div>
        </div>
      )}

      <div className="settings-content">
        <SettingsNavigation
          activeCategory={activeCategory}
          onCategoryChange={handleCategoryChange}
          hasUnsavedChanges={hasUnsavedChanges}
        />
        
        <div className="settings-main">
          {isLoading ? (
            <div className="settings-loading">
              <div className="loading-spinner"></div>
              <p>加载设置中...</p>
            </div>
          ) : (
            <div className="settings-content-area">
              {renderSettingsContent()}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default SettingsPanel 