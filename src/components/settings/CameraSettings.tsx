import React, { useState, useEffect, useRef } from 'react'
import { CameraSettings as CameraSettingsType, ExposureMode, FocusMode } from '../../types/settings.types'
import { cameraDeviceService, CameraDeviceInfo, CameraTestResult } from '../../services/CameraDeviceService'
import './CameraSettings.css'

interface CameraSettingsProps {
  settings: CameraSettingsType
  onSettingChange: (key: string, value: any) => void
  isLoading: boolean
}

/**
 * 摄像头设置组件
 */
export const CameraSettings: React.FC<CameraSettingsProps> = ({
  settings,
  onSettingChange,
  isLoading
}) => {
  const [devices, setDevices] = useState<CameraDeviceInfo[]>([])
  const [isServiceReady, setIsServiceReady] = useState(false)
  const [permissionGranted, setPermissionGranted] = useState(false)
  const [isPreviewActive, setIsPreviewActive] = useState(false)
  const [testResults, setTestResults] = useState<CameraTestResult[]>([])
  const [isTesting, setIsTesting] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const videoRef = useRef<HTMLVideoElement>(null)
  const [currentStream, setCurrentStream] = useState<MediaStream | null>(null)

  // 初始化摄像头服务
  useEffect(() => {
    const initializeService = async () => {
      try {
        if (!cameraDeviceService.isReady()) {
          await cameraDeviceService.initialize()
        }
        setIsServiceReady(true)
        setDevices(cameraDeviceService.getDevices())
        setPermissionGranted(true)
      } catch (err) {
        setError(err instanceof Error ? err.message : '摄像头服务初始化失败')
        setPermissionGranted(false)
      }
    }

    initializeService()

    // 监听服务事件
    const handleDevicesDetected = (data: { devices: CameraDeviceInfo[] }) => {
      setDevices(data.devices)
    }

    const handlePermissionGranted = () => {
      setPermissionGranted(true)
      setError(null)
    }

    const handlePermissionDenied = (data: { error: string }) => {
      setPermissionGranted(false)
      setError(`摄像头权限被拒绝: ${data.error}`)
    }

    cameraDeviceService.addEventListener('devicesDetected', handleDevicesDetected)
    cameraDeviceService.addEventListener('permissionGranted', handlePermissionGranted)
    cameraDeviceService.addEventListener('permissionDenied', handlePermissionDenied)

    return () => {
      cameraDeviceService.removeEventListener('devicesDetected', handleDevicesDetected)
      cameraDeviceService.removeEventListener('permissionGranted', handlePermissionGranted)
      cameraDeviceService.removeEventListener('permissionDenied', handlePermissionDenied)
      stopPreview()
    }
  }, [])

  // 请求摄像头权限
  const requestPermission = async () => {
    try {
      setError(null)
      const granted = await cameraDeviceService.requestPermissions()
      if (granted) {
        await cameraDeviceService.detectDevices()
        setDevices(cameraDeviceService.getDevices())
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '权限请求失败')
    }
  }

  // 开始预览
  const startPreview = async () => {
    try {
      setError(null)
      const streamInfo = await cameraDeviceService.startCamera(settings)
      setCurrentStream(streamInfo.stream)
      
      if (videoRef.current) {
        videoRef.current.srcObject = streamInfo.stream
        await videoRef.current.play()
      }
      
      setIsPreviewActive(true)
    } catch (err) {
      setError(err instanceof Error ? err.message : '预览启动失败')
    }
  }

  // 停止预览
  const stopPreview = () => {
    if (currentStream) {
      currentStream.getTracks().forEach(track => track.stop())
      setCurrentStream(null)
    }
    
    if (videoRef.current) {
      videoRef.current.srcObject = null
    }
    
    setIsPreviewActive(false)
    cameraDeviceService.stopCamera()
  }

  // 测试摄像头设备
  const testDevice = async (deviceId: string) => {
    try {
      setIsTesting(true)
      setError(null)
      
      const result = await cameraDeviceService.testCamera(deviceId, settings.resolution)
      setTestResults(prev => [...prev.filter(r => r.deviceId !== deviceId), result])
    } catch (err) {
      setError(err instanceof Error ? err.message : '设备测试失败')
    } finally {
      setIsTesting(false)
    }
  }

  // 获取设备的测试结果
  const getTestResult = (deviceId: string): CameraTestResult | undefined => {
    return testResults.find(r => r.deviceId === deviceId)
  }

  // 截图
  const capturePhoto = async () => {
    try {
      setError(null)
      const photoData = await cameraDeviceService.capturePhoto()
      if (photoData) {
        // 创建下载链接
        const link = document.createElement('a')
        link.href = photoData
        link.download = `camera-capture-${Date.now()}.jpg`
        link.click()
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '截图失败')
    }
  }

  // 如果没有权限，显示权限请求界面
  if (!permissionGranted) {
    return (
      <div className="settings-section">
        <div className="section-header">
          <h3>摄像头设置</h3>
          <p>配置摄像头参数和行为检测</p>
        </div>

        <div className="permission-request">
          <div className="permission-icon">📷</div>
          <h4>需要摄像头访问权限</h4>
          <p>为了使用摄像头功能，请授予应用访问摄像头的权限。</p>
          <button 
            className="btn btn-primary" 
            onClick={requestPermission}
            disabled={isLoading}
          >
            请求权限
          </button>
          {error && (
            <div className="error-message">
              <span className="error-icon">⚠️</span>
              <span>{error}</span>
            </div>
          )}
        </div>
      </div>
    )
  }

  return (
    <div className="settings-section">
      <div className="section-header">
        <h3>摄像头设置</h3>
        <p>配置摄像头参数和行为检测</p>
      </div>

      {error && (
        <div className="error-message">
          <span className="error-icon">⚠️</span>
          <span>{error}</span>
          <button onClick={() => setError(null)}>×</button>
        </div>
      )}

      <div className="settings-grid">
        {/* 基本设置 */}
        <div className="setting-group">
          <label className="setting-label">
            <span className="label-text">启用摄像头</span>
            <span className="label-description">开启或关闭摄像头功能</span>
          </label>
          <div className="toggle-switch">
            <input
              type="checkbox"
              id="cameraEnabled"
              checked={settings.enabled}
              onChange={(e) => onSettingChange('enabled', e.target.checked)}
              disabled={isLoading}
            />
            <label htmlFor="cameraEnabled" className="toggle-label">
              <span className="toggle-slider"></span>
            </label>
          </div>
        </div>

        {/* 设备选择 */}
        <div className="setting-group">
          <label className="setting-label">
            <span className="label-text">摄像头设备</span>
            <span className="label-description">选择要使用的摄像头设备</span>
          </label>
          <select
            className="setting-input"
            value={settings.deviceId}
            onChange={(e) => onSettingChange('deviceId', e.target.value)}
            disabled={isLoading || !settings.enabled}
          >
            <option value="">自动选择</option>
            {devices.map((device) => (
              <option key={device.deviceId} value={device.deviceId}>
                {device.label || `摄像头 ${device.deviceId.slice(0, 8)}`}
              </option>
            ))}
          </select>
        </div>

        {/* 分辨率设置 */}
        <div className="setting-group">
          <label className="setting-label">
            <span className="label-text">分辨率</span>
            <span className="label-description">设置摄像头采集分辨率</span>
          </label>
          <select
            className="setting-input"
            value={`${settings.resolution.width}x${settings.resolution.height}`}
            onChange={(e) => {
              const [width, height] = e.target.value.split('x').map(Number)
              const label = e.target.selectedOptions[0].text
              onSettingChange('resolution', { width, height, label })
            }}
            disabled={isLoading || !settings.enabled}
          >
            <option value="640x480">640x480 (480p)</option>
            <option value="1280x720">1280x720 (720p HD)</option>
            <option value="1920x1080">1920x1080 (1080p Full HD)</option>
            <option value="2560x1440">2560x1440 (1440p 2K)</option>
            <option value="3840x2160">3840x2160 (2160p 4K)</option>
          </select>
        </div>

        {/* 帧率设置 */}
        <div className="setting-group">
          <label className="setting-label">
            <span className="label-text">帧率</span>
            <span className="label-description">设置视频帧率 (FPS)</span>
          </label>
          <div className="number-input-wrapper">
            <input
              type="number"
              className="setting-input"
              value={settings.fps}
              onChange={(e) => onSettingChange('fps', parseInt(e.target.value))}
              min="15"
              max="60"
              step="5"
              disabled={isLoading || !settings.enabled}
            />
            <span className="input-unit">FPS</span>
          </div>
        </div>

        {/* 亮度设置 */}
        <div className="setting-group">
          <label className="setting-label">
            <span className="label-text">亮度</span>
            <span className="label-description">调整摄像头亮度</span>
          </label>
          <div className="range-input-wrapper">
            <input
              type="range"
              min="0"
              max="100"
              value={settings.brightness}
              onChange={(e) => onSettingChange('brightness', parseInt(e.target.value))}
              disabled={isLoading || !settings.enabled}
            />
            <span className="range-value">{settings.brightness}%</span>
          </div>
        </div>

        {/* 对比度设置 */}
        <div className="setting-group">
          <label className="setting-label">
            <span className="label-text">对比度</span>
            <span className="label-description">调整摄像头对比度</span>
          </label>
          <div className="range-input-wrapper">
            <input
              type="range"
              min="0"
              max="100"
              value={settings.contrast}
              onChange={(e) => onSettingChange('contrast', parseInt(e.target.value))}
              disabled={isLoading || !settings.enabled}
            />
            <span className="range-value">{settings.contrast}%</span>
          </div>
        </div>

        {/* 饱和度设置 */}
        <div className="setting-group">
          <label className="setting-label">
            <span className="label-text">饱和度</span>
            <span className="label-description">调整摄像头饱和度</span>
          </label>
          <div className="range-input-wrapper">
            <input
              type="range"
              min="0"
              max="100"
              value={settings.saturation}
              onChange={(e) => onSettingChange('saturation', parseInt(e.target.value))}
              disabled={isLoading || !settings.enabled}
            />
            <span className="range-value">{settings.saturation}%</span>
          </div>
        </div>

        {/* 曝光模式 */}
        <div className="setting-group">
          <label className="setting-label">
            <span className="label-text">曝光模式</span>
            <span className="label-description">设置摄像头曝光模式</span>
          </label>
          <select
            className="setting-input"
            value={settings.exposureMode}
            onChange={(e) => onSettingChange('exposureMode', e.target.value)}
            disabled={isLoading || !settings.enabled}
          >
            <option value={ExposureMode.AUTO}>自动曝光</option>
            <option value={ExposureMode.MANUAL}>手动曝光</option>
          </select>
        </div>

        {/* 对焦模式 */}
        <div className="setting-group">
          <label className="setting-label">
            <span className="label-text">对焦模式</span>
            <span className="label-description">设置摄像头对焦模式</span>
          </label>
          <select
            className="setting-input"
            value={settings.focusMode}
            onChange={(e) => onSettingChange('focusMode', e.target.value)}
            disabled={isLoading || !settings.enabled}
          >
            <option value={FocusMode.AUTO}>自动对焦</option>
            <option value={FocusMode.MANUAL}>手动对焦</option>
            <option value={FocusMode.CONTINUOUS}>连续对焦</option>
          </select>
        </div>

        {/* 镜像翻转 */}
        <div className="setting-group">
          <label className="setting-label">
            <span className="label-text">水平翻转</span>
            <span className="label-description">水平镜像摄像头画面</span>
          </label>
          <div className="toggle-switch">
            <input
              type="checkbox"
              id="flipHorizontal"
              checked={settings.flipHorizontal}
              onChange={(e) => onSettingChange('flipHorizontal', e.target.checked)}
              disabled={isLoading || !settings.enabled}
            />
            <label htmlFor="flipHorizontal" className="toggle-label">
              <span className="toggle-slider"></span>
            </label>
          </div>
        </div>

        <div className="setting-group">
          <label className="setting-label">
            <span className="label-text">垂直翻转</span>
            <span className="label-description">垂直镜像摄像头画面</span>
          </label>
          <div className="toggle-switch">
            <input
              type="checkbox"
              id="flipVertical"
              checked={settings.flipVertical}
              onChange={(e) => onSettingChange('flipVertical', e.target.checked)}
              disabled={isLoading || !settings.enabled}
            />
            <label htmlFor="flipVertical" className="toggle-label">
              <span className="toggle-slider"></span>
            </label>
          </div>
        </div>

        {/* 夜视模式 */}
        <div className="setting-group">
          <label className="setting-label">
            <span className="label-text">夜视模式</span>
            <span className="label-description">低光环境下的增强模式</span>
          </label>
          <div className="toggle-switch">
            <input
              type="checkbox"
              id="nightVision"
              checked={settings.nightVision}
              onChange={(e) => onSettingChange('nightVision', e.target.checked)}
              disabled={isLoading || !settings.enabled}
            />
            <label htmlFor="nightVision" className="toggle-label">
              <span className="toggle-slider"></span>
            </label>
          </div>
        </div>

        {/* 运动检测 */}
        <div className="setting-group">
          <label className="setting-label">
            <span className="label-text">运动检测</span>
            <span className="label-description">检测画面中的运动变化</span>
          </label>
          <div className="toggle-switch">
            <input
              type="checkbox"
              id="motionDetection"
              checked={settings.motionDetection}
              onChange={(e) => onSettingChange('motionDetection', e.target.checked)}
              disabled={isLoading || !settings.enabled}
            />
            <label htmlFor="motionDetection" className="toggle-label">
              <span className="toggle-slider"></span>
            </label>
          </div>
        </div>
      </div>

      {/* 预览和测试区域 */}
      {settings.enabled && isServiceReady && (
        <div className="camera-controls">
          <div className="controls-header">
            <h4>摄像头预览和测试</h4>
            <div className="control-buttons">
              {!isPreviewActive ? (
                <button
                  className="btn btn-primary"
                  onClick={startPreview}
                  disabled={isLoading}
                >
                  📹 开始预览
                </button>
              ) : (
                <>
                  <button
                    className="btn btn-secondary"
                    onClick={stopPreview}
                    disabled={isLoading}
                  >
                    ⏹️ 停止预览
                  </button>
                  <button
                    className="btn btn-secondary"
                    onClick={capturePhoto}
                    disabled={isLoading}
                  >
                    📸 截图
                  </button>
                </>
              )}
            </div>
          </div>

          {/* 视频预览 */}
          {isPreviewActive && (
            <div className="camera-preview">
              <video
                ref={videoRef}
                autoPlay
                muted
                playsInline
                className="preview-video"
                style={{
                  transform: `scale(${settings.flipHorizontal ? -1 : 1}, ${settings.flipVertical ? -1 : 1})`
                }}
              />
              <div className="preview-info">
                <span>分辨率: {settings.resolution.width}x{settings.resolution.height}</span>
                <span>帧率: {settings.fps} FPS</span>
              </div>
            </div>
          )}

          {/* 设备测试 */}
          <div className="device-testing">
            <h5>设备测试</h5>
            <div className="device-list">
              {devices.map((device) => {
                const testResult = getTestResult(device.deviceId)
                return (
                  <div key={device.deviceId} className="device-item">
                    <div className="device-info">
                      <span className="device-name">
                        {device.label || `摄像头 ${device.deviceId.slice(0, 8)}`}
                      </span>
                      <span className="device-id">{device.deviceId}</span>
                    </div>
                    
                    <div className="device-actions">
                      <button
                        className="btn btn-small btn-secondary"
                        onClick={() => testDevice(device.deviceId)}
                        disabled={isTesting || isLoading}
                      >
                        {isTesting ? '测试中...' : '测试设备'}
                      </button>
                    </div>

                    {testResult && (
                      <div className={`test-result ${testResult.success ? 'success' : 'error'}`}>
                        <div className="result-header">
                          <span className={`result-icon ${testResult.success ? 'success' : 'error'}`}>
                            {testResult.success ? '✅' : '❌'}
                          </span>
                          <span>测试结果</span>
                        </div>
                        
                        {testResult.success ? (
                          <div className="result-details">
                            <div className="result-row">
                              <span>初始化时间:</span>
                              <span>{Math.round(testResult.performance.initTime)}ms</span>
                            </div>
                            <div className="result-row">
                              <span>首帧时间:</span>
                              <span>{Math.round(testResult.performance.firstFrameTime)}ms</span>
                            </div>
                            <div className="result-row">
                              <span>实际帧率:</span>
                              <span>{testResult.frameRate} FPS</span>
                            </div>
                            <div className="result-row">
                              <span>画面亮度:</span>
                              <span>{testResult.quality.brightness}</span>
                            </div>
                            <div className="result-row">
                              <span>画面对比度:</span>
                              <span>{testResult.quality.contrast}</span>
                            </div>
                          </div>
                        ) : (
                          <div className="result-errors">
                            {testResult.errors.map((error, index) => (
                              <div key={index} className="error-item">{error}</div>
                            ))}
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                )
              })}
            </div>
          </div>
        </div>
      )}

      <div className="section-footer">
        <div className="footer-info">
          <span className="info-icon">ℹ️</span>
          <span>摄像头设置更改将在下次启动摄像头时生效</span>
        </div>
      </div>
    </div>
  )
}

export default CameraSettings 