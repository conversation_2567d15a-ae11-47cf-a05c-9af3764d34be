import React from 'react'
import { SettingsCategory } from '../../types/settings.types'

interface SettingsNavigationProps {
  activeCategory: SettingsCategory
  onCategoryChange: (category: SettingsCategory) => void
  hasUnsavedChanges: boolean
}

interface CategoryItem {
  category: SettingsCategory
  label: string
  icon: string
  description: string
}

const settingsCategories: CategoryItem[] = [
  {
    category: SettingsCategory.GENERAL,
    label: '通用设置',
    icon: '⚙️',
    description: '语言、时区和基本配置'
  },
  {
    category: SettingsCategory.CAMERA,
    label: '摄像头设置',
    icon: '📷',
    description: '摄像头参数和行为检测'
  },
  {
    category: SettingsCategory.AUDIO,
    label: '音频设置',
    icon: '🔊',
    description: '音量、音效和设备配置'
  },
  {
    category: SettingsCategory.FOCUS,
    label: '专注设置',
    icon: '🎯',
    description: '专注目标和检测敏感度'
  },
  {
    category: SettingsCategory.PRIVACY,
    label: '隐私设置',
    icon: '🔐',
    description: '数据收集和隐私保护'
  },
  {
    category: SettingsCategory.NOTIFICATIONS,
    label: '通知设置',
    icon: '🔔',
    description: '提醒和通知偏好'
  },
  {
    category: SettingsCategory.APPEARANCE,
    label: '外观设置',
    icon: '🎨',
    description: '主题、布局和可访问性'
  },
  {
    category: SettingsCategory.DATA,
    label: '数据设置',
    icon: '💾',
    description: '存储、备份和同步'
  }
]

/**
 * 设置导航组件
 */
export const SettingsNavigation: React.FC<SettingsNavigationProps> = ({
  activeCategory,
  onCategoryChange,
  hasUnsavedChanges
}) => {
  return (
    <nav className="settings-navigation">
      <div className="navigation-header">
        <h2>设置类别</h2>
        {hasUnsavedChanges && (
          <span className="unsaved-badge" title="有未保存的更改">
            •
          </span>
        )}
      </div>
      
      <ul className="navigation-list">
        {settingsCategories.map((item) => (
          <li key={item.category} className="navigation-item">
            <button
              className={`navigation-button ${
                activeCategory === item.category ? 'active' : ''
              }`}
              onClick={() => onCategoryChange(item.category)}
              title={item.description}
            >
              <span className="navigation-icon">{item.icon}</span>
              <div className="navigation-content">
                <span className="navigation-label">{item.label}</span>
                <span className="navigation-description">{item.description}</span>
              </div>
              {activeCategory === item.category && (
                <span className="navigation-indicator">
                  <svg width="8" height="12" viewBox="0 0 8 12" fill="none">
                    <path
                      d="M2 2L6 6L2 10"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                </span>
              )}
            </button>
          </li>
        ))}
      </ul>
      
      <div className="navigation-footer">
        <div className="navigation-tip">
          <span className="tip-icon">💡</span>
          <span className="tip-text">
            设置会自动保存，无需手动保存。
          </span>
        </div>
      </div>
    </nav>
  )
}

export default SettingsNavigation 