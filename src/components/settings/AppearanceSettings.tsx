import React from 'react'
import { AppearanceSettings as AppearanceSettingsType } from '../../types/settings.types'

interface AppearanceSettingsProps {
  settings: AppearanceSettingsType
  onSettingChange: (key: string, value: any) => void
  isLoading: boolean
}

export const AppearanceSettings: React.FC<AppearanceSettingsProps> = ({ settings, onSettingChange, isLoading }) => {
  return (
    <div className="settings-section">
      <div className="section-header">
        <h3>外观设置</h3>
        <p>配置主题、布局和可访问性</p>
      </div>
      <div className="settings-grid">
        <div className="setting-group">
          <label className="setting-label">
            <span className="label-text">深色模式</span>
          </label>
          <input
            type="checkbox"
            checked={settings.theme.darkMode}
            onChange={(e) => onSettingChange('theme.darkMode', e.target.checked)}
            disabled={isLoading}
          />
        </div>
      </div>
    </div>
  )
}

export default AppearanceSettings 