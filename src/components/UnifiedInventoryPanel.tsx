import React, { useState, useEffect, useMemo } from 'react'
import { ItemIntegrationManager, IntegratedItem } from '../managers/ItemIntegrationManager'
import { ItemRarity, ItemCategory, ItemType, LootboxType } from '../types/lootbox'
import FuturesProductTooltip from './FuturesProductTooltip'

interface UnifiedInventoryPanelProps {
  itemManager: ItemIntegrationManager
  className?: string
}

// 品质颜色映射
const RARITY_COLORS = {
  [ItemRarity.GRAY]: '#9E9E9E',
  [ItemRarity.GREEN]: '#4CAF50',
  [ItemRarity.BLUE]: '#2196F3',
  [ItemRarity.ORANGE]: '#FF9800',
  [ItemRarity.GOLD]: '#FFD700',
  [ItemRarity.GOLD_RED]: '#FF6B6B'
}

// 品质名称映射
const RARITY_NAMES = {
  [ItemRarity.GRAY]: '普通',
  [ItemRarity.GREEN]: '优质',
  [ItemRarity.BLUE]: '稀有',
  [ItemRarity.ORANGE]: '史诗',
  [ItemRarity.GOLD]: '传说',
  [ItemRarity.GOLD_RED]: '神话'
}

// 分类标签映射
const CATEGORY_LABELS = {
  [ItemCategory.AGRICULTURAL]: '农产品',
  [ItemCategory.INDUSTRIAL]: '工业品'
}

// 类型标签映射
const TYPE_LABELS = {
  [ItemType.SEED]: '种子',
  [ItemType.CROP]: '作物',
  [ItemType.LIVESTOCK]: '牲畜',
  [ItemType.FARM_TOOL]: '农具',
  [ItemType.RAW_MATERIAL]: '原材料',
  [ItemType.MACHINERY]: '机械',
  [ItemType.CURRENCY]: '货币',
  [ItemType.BOOST]: '增益'
}

// 盲盒类型标签
const LOOTBOX_LABELS = {
  [LootboxType.BASIC_FARM]: '基础农场盒',
  [LootboxType.PREMIUM_FARM]: '高级农场盒',
  [LootboxType.LEGENDARY_FARM]: '传说农场盒',
  [LootboxType.BASIC_INDUSTRIAL]: '基础工业盒',
  [LootboxType.PREMIUM_INDUSTRIAL]: '高级工业盒',
  [LootboxType.LEGENDARY_INDUSTRIAL]: '传说工业盒',
  [LootboxType.FUTURES_MYSTERY]: '期货神秘盒',
  [LootboxType.GOLDEN_TREASURE]: '金色宝藏盒',
  [LootboxType.SYNTHESIS_BOX]: '合成专用盒'
}

export const UnifiedInventoryPanel: React.FC<UnifiedInventoryPanelProps> = ({ 
  itemManager, 
  className = '' 
}) => {
  const [items, setItems] = useState<IntegratedItem[]>([])
  const [selectedCategory, setSelectedCategory] = useState<ItemCategory | 'all'>('all')
  const [selectedType, setSelectedType] = useState<ItemType | 'all'>('all')
  const [selectedRarity, setSelectedRarity] = useState<ItemRarity | 'all'>('all')
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedItems, setSelectedItems] = useState<string[]>([])
  const [showLootboxPanel, setShowLootboxPanel] = useState(false)
  const [synthesisPairs, setSynthesisPairs] = useState<string[]>([])

  // 更新物品列表
  useEffect(() => {
    const updateItems = () => {
      setItems(itemManager.getAllItems())
    }

    // 初始加载
    updateItems()

    // 监听物品变化
    itemManager.onItemAdded(updateItems)
    itemManager.onItemUpdated(updateItems)
    itemManager.onItemRemoved(updateItems)

    return () => {
      itemManager.removeAllListeners()
    }
  }, [itemManager])

  // 过滤后的物品
  const filteredItems = useMemo(() => {
    let filtered = items

    // 分类过滤
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(item => item.category === selectedCategory)
    }

    // 类型过滤
    if (selectedType !== 'all') {
      filtered = filtered.filter(item => item.type === selectedType)
    }

    // 品质过滤
    if (selectedRarity !== 'all') {
      filtered = filtered.filter(item => item.rarity === selectedRarity)
    }

    // 搜索过滤
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase()
      filtered = filtered.filter(item => 
        item.name.toLowerCase().includes(query) ||
        item.description.toLowerCase().includes(query)
      )
    }

    // 按品质和名称排序
    return filtered.sort((a, b) => {
      const rarityOrder = [
        ItemRarity.GOLD_RED, ItemRarity.GOLD, ItemRarity.ORANGE,
        ItemRarity.BLUE, ItemRarity.GREEN, ItemRarity.GRAY
      ]
      const aRarityIndex = rarityOrder.indexOf(a.rarity)
      const bRarityIndex = rarityOrder.indexOf(b.rarity)
      
      if (aRarityIndex !== bRarityIndex) {
        return aRarityIndex - bRarityIndex
      }
      
      return a.name.localeCompare(b.name)
    })
  }, [items, selectedCategory, selectedType, selectedRarity, searchQuery])

  // 统计信息
  const statistics = useMemo(() => {
    const stats = {
      total: items.length,
      byCategory: {} as Record<ItemCategory, number>,
      byRarity: {} as Record<ItemRarity, number>,
      totalValue: 0
    }

    items.forEach(item => {
      // 分类统计
      stats.byCategory[item.category] = (stats.byCategory[item.category] || 0) + item.quantity
      
      // 品质统计
      stats.byRarity[item.rarity] = (stats.byRarity[item.rarity] || 0) + item.quantity
      
      // 总价值
      stats.totalValue += item.value * item.quantity
    })

    return stats
  }, [items])

  // 处理物品选择
  const handleItemSelect = (itemId: string) => {
    setSelectedItems(prev => {
      if (prev.includes(itemId)) {
        return prev.filter(id => id !== itemId)
      } else if (prev.length < 2) {
        return [...prev, itemId]
      } else {
        return [prev[1], itemId] // 替换第一个选择
      }
    })
  }

  // 处理种植
  const handlePlant = async (itemId: string) => {
    // 这里需要集成农场UI来选择种植位置
    // 暂时使用默认位置
    const result = await itemManager.plantItem(itemId, 'slot_0_0')
    
    if (result.success) {
      alert(`种植成功！`)
    } else {
      alert(`种植失败：${result.message}`)
    }
  }

  // 处理合成
  const handleSynthesize = async () => {
    if (selectedItems.length !== 2) {
      alert('请选择2个物品进行合成')
      return
    }

    const result = await itemManager.synthesizeItems(selectedItems)
    
    if (result.success) {
      alert(`合成成功！获得了${result.resultItem?.name}`)
      setSelectedItems([])
    } else {
      alert(`合成失败：${result.error}`)
    }
  }

  // 处理盲盒开启
  const handleOpenLootbox = async (lootboxType: LootboxType) => {
    const result = await itemManager.openLootbox(lootboxType)
    
    if (result.success) {
      alert(`开启成功！获得了${result.items.length}个物品`)
    } else {
      alert(`开启失败：${result.error}`)
    }
  }

  return (
    <div className={`unified-inventory-panel ${className}`}>
      {/* 标题栏 */}
      <div className="inventory-header">
        <h2>🎒 统一物品系统</h2>
        <div className="header-actions">
          <button 
            onClick={() => setShowLootboxPanel(!showLootboxPanel)}
            className="btn btn-primary"
          >
            📦 盲盒商店
          </button>
        </div>
      </div>

      {/* 统计信息 */}
      <div className="inventory-stats">
        <div className="stat-card">
          <h4>物品总数</h4>
          <span className="stat-value">{statistics.total}</span>
        </div>
        <div className="stat-card">
          <h4>总价值</h4>
          <span className="stat-value">{statistics.totalValue.toLocaleString()}</span>
        </div>
        <div className="stat-card">
          <h4>农产品</h4>
          <span className="stat-value">{statistics.byCategory[ItemCategory.AGRICULTURAL] || 0}</span>
        </div>
        <div className="stat-card">
          <h4>工业品</h4>
          <span className="stat-value">{statistics.byCategory[ItemCategory.INDUSTRIAL] || 0}</span>
        </div>
      </div>

      {/* 盲盒面板 */}
      {showLootboxPanel && (
        <div className="lootbox-panel">
          <h3>🎰 盲盒商店</h3>
          <div className="lootbox-grid">
            {Object.values(LootboxType).map(lootboxType => (
              <div key={lootboxType} className="lootbox-card">
                <h4>{LOOTBOX_LABELS[lootboxType]}</h4>
                <button 
                  onClick={() => handleOpenLootbox(lootboxType)}
                  className="btn btn-accent"
                >
                  开启
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* 过滤器 */}
      <div className="inventory-filters">
        <div className="filter-group">
          <label>搜索：</label>
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="搜索物品名称或描述..."
            className="search-input"
          />
        </div>

        <div className="filter-group">
          <label>分类：</label>
          <select 
            value={selectedCategory} 
            onChange={(e) => setSelectedCategory(e.target.value as ItemCategory | 'all')}
            className="filter-select"
          >
            <option value="all">全部</option>
            {Object.entries(CATEGORY_LABELS).map(([value, label]) => (
              <option key={value} value={value}>{label}</option>
            ))}
          </select>
        </div>

        <div className="filter-group">
          <label>类型：</label>
          <select 
            value={selectedType} 
            onChange={(e) => setSelectedType(e.target.value as ItemType | 'all')}
            className="filter-select"
          >
            <option value="all">全部</option>
            {Object.entries(TYPE_LABELS).map(([value, label]) => (
              <option key={value} value={value}>{label}</option>
            ))}
          </select>
        </div>

        <div className="filter-group">
          <label>品质：</label>
          <select 
            value={selectedRarity} 
            onChange={(e) => setSelectedRarity(e.target.value as ItemRarity | 'all')}
            className="filter-select"
          >
            <option value="all">全部</option>
            {Object.entries(RARITY_NAMES).map(([value, label]) => (
              <option key={value} value={value}>{label}</option>
            ))}
          </select>
        </div>
      </div>

      {/* 操作栏 */}
      <div className="inventory-actions">
        <div className="selected-info">
          {selectedItems.length > 0 && (
            <span>已选择 {selectedItems.length}/2 个物品</span>
          )}
        </div>
        <div className="action-buttons">
          <button 
            onClick={handleSynthesize}
            disabled={selectedItems.length !== 2}
            className="btn btn-warning"
          >
            🧪 合成选中物品
          </button>
          <button 
            onClick={() => setSelectedItems([])}
            disabled={selectedItems.length === 0}
            className="btn btn-secondary"
          >
            清除选择
          </button>
        </div>
      </div>

      {/* 物品网格 */}
      <div className="inventory-grid">
        {(() => {
          // 展开物品：每个物品根据数量分别显示
          const expandedItems: Array<{item: any, index: number}> = [];
          filteredItems.forEach((item) => {
            for (let i = 0; i < item.quantity; i++) {
              expandedItems.push({ item, index: i });
            }
          });
          
          return expandedItems.map(({item, index}, globalIndex) => (
            <FuturesProductTooltip key={`${item.id}-${index}-${globalIndex}`} item={item}>
              <div 
                className={`item-card ${selectedItems.includes(item.id) ? 'selected' : ''}`}
                onClick={() => handleItemSelect(item.id)}
                style={{ borderColor: RARITY_COLORS[item.rarity], position: 'relative' }}
                title={`${item.name} (${RARITY_NAMES[item.rarity]})`}
              >
                {/* 物品图标和基本信息 */}
                <div className="item-header">
                  <span className="item-icon">{item.icon}</span>
                  <div className="item-rarity-badge" style={{ 
                    backgroundColor: RARITY_COLORS[item.rarity],
                    color: 'white',
                    fontSize: '0.7rem',
                    fontWeight: 'bold',
                    padding: '2px 6px',
                    borderRadius: '8px'
                  }}>
                    {RARITY_NAMES[item.rarity]}
                  </div>
                </div>

                {/* 物品名称 */}
                <h4 className="item-name" style={{ color: RARITY_COLORS[item.rarity] }}>
                  {item.name}
                </h4>

                {/* 物品描述 */}
                <p className="item-description">{item.description}</p>

                {/* 物品标签 */}
                <div className="item-tags">
                  <span className="tag category-tag">
                    {CATEGORY_LABELS[item.category]}
                  </span>
                  <span className="tag type-tag">
                    {TYPE_LABELS[item.type]}
                  </span>
                  <span 
                    className="tag rarity-tag"
                    style={{ backgroundColor: RARITY_COLORS[item.rarity] }}
                  >
                    {RARITY_NAMES[item.rarity]}
                  </span>
                </div>

                {/* 物品属性 */}
                <div className="item-properties">
                  <div className="property">
                    <span>价值: </span>
                    <span className="property-value">{item.value.toLocaleString()}</span>
                  </div>
                  
                  {item.metadata.yieldMultiplier && (
                    <div className="property">
                      <span>产量倍数: </span>
                      <span className="property-value">{item.metadata.yieldMultiplier.toFixed(1)}x</span>
                    </div>
                  )}
                  
                  {item.metadata.qualityBonus && (
                    <div className="property">
                      <span>品质加成: </span>
                      <span className="property-value">+{item.metadata.qualityBonus}</span>
                    </div>
                  )}
                  
                  {item.metadata.futuresPrice && (
                    <div className="property">
                      <span>期货价格: </span>
                      <span className="property-value">{item.metadata.futuresPrice.toLocaleString()}</span>
                    </div>
                  )}
                </div>

                {/* 物品状态 */}
                {Object.keys(item.status).length > 0 && (
                  <div className="item-status">
                    {item.status.isPlanted && <span className="status-tag planted">已种植</span>}
                    {item.status.isGrowing && <span className="status-tag growing">生长中</span>}
                    {item.status.isReady && <span className="status-tag ready">可收获</span>}
                  </div>
                )}

                {/* 物品操作 */}
                <div className="item-actions">
                  {item.type === ItemType.SEED && item.category === ItemCategory.AGRICULTURAL && (
                    <button 
                      onClick={(e) => {
                        e.stopPropagation()
                        handlePlant(item.id)
                      }}
                      className="btn btn-sm btn-success"
                    >
                      🌱 种植
                    </button>
                  )}
                  
                  {item.synthesizable && (
                    <button 
                      onClick={(e) => {
                        e.stopPropagation()
                        handleItemSelect(item.id)
                      }}
                      className="btn btn-sm btn-warning"
                    >
                      🧪 选择合成
                    </button>
                  )}
                </div>

                {/* 来源信息 */}
                <div className="item-source">
                  <small>
                    来源: {item.source.type === 'lootbox' ? '盲盒' : 
                          item.source.type === 'farm' ? '农场' : 
                          item.source.type === 'synthesis' ? '合成' : '手动添加'}
                  </small>
                </div>

                {/* 产量指示器 - 仅农业产品显示 */}
                {item.category === ItemCategory.AGRICULTURAL && (
                  <div className="production-indicator" style={{
                    position: 'absolute',
                    top: '5px',
                    right: '5px',
                    fontSize: '12px',
                    opacity: '0.8',
                    animation: 'pulse 2s infinite'
                  }}>
                    📈
                  </div>
                )}
              </div>
            </FuturesProductTooltip>
          ))
        })()}
      </div>

      {filteredItems.length === 0 && (
        <div className="empty-state">
          <p>没有找到符合条件的物品</p>
          <button 
            onClick={() => setShowLootboxPanel(true)}
            className="btn btn-primary"
          >
            📦 开启盲盒获取物品
          </button>
        </div>
      )}

      <style>{`
        .unified-inventory-panel {
          max-width: 1200px;
          margin: 0 auto;
          padding: 20px;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          border-radius: 15px;
          color: white;
          min-height: 600px;
        }

        .inventory-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 20px;
          padding-bottom: 15px;
          border-bottom: 2px solid rgba(255, 255, 255, 0.2);
        }

        .inventory-header h2 {
          margin: 0;
          font-size: 1.8rem;
          font-weight: bold;
        }

        .inventory-stats {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
          gap: 15px;
          margin-bottom: 20px;
        }

        .stat-card {
          background: rgba(255, 255, 255, 0.1);
          padding: 15px;
          border-radius: 10px;
          text-align: center;
          backdrop-filter: blur(10px);
        }

        .stat-card h4 {
          margin: 0 0 8px 0;
          font-size: 0.9rem;
          opacity: 0.8;
        }

        .stat-value {
          font-size: 1.5rem;
          font-weight: bold;
          color: #FFD700;
        }

        .lootbox-panel {
          background: rgba(0, 0, 0, 0.3);
          padding: 20px;
          border-radius: 10px;
          margin-bottom: 20px;
        }

        .lootbox-grid {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
          gap: 15px;
          margin-top: 15px;
        }

        .lootbox-card {
          background: rgba(255, 255, 255, 0.1);
          padding: 15px;
          border-radius: 8px;
          text-align: center;
        }

        .lootbox-card h4 {
          margin: 0 0 10px 0;
          font-size: 1rem;
        }

        .inventory-filters {
          display: grid;
          grid-template-columns: 2fr 1fr 1fr 1fr;
          gap: 15px;
          margin-bottom: 20px;
          padding: 15px;
          background: rgba(255, 255, 255, 0.1);
          border-radius: 10px;
        }

        .filter-group {
          display: flex;
          flex-direction: column;
          gap: 5px;
        }

        .filter-group label {
          font-size: 0.9rem;
          font-weight: 500;
          opacity: 0.9;
        }

        .search-input, .filter-select {
          padding: 8px 12px;
          border: none;
          border-radius: 5px;
          background: rgba(255, 255, 255, 0.9);
          color: #333;
          font-size: 0.9rem;
        }

        .inventory-actions {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 20px;
          padding: 15px;
          background: rgba(255, 255, 255, 0.1);
          border-radius: 10px;
        }

        .selected-info {
          font-weight: 500;
        }

        .action-buttons {
          display: flex;
          gap: 10px;
        }

        .inventory-grid {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
          gap: 20px;
        }

        .item-card {
          background: rgba(255, 255, 255, 0.95);
          color: #333;
          padding: 15px;
          border-radius: 12px;
          border: 3px solid transparent;
          cursor: pointer;
          transition: all 0.3s ease;
          position: relative;
        }

        .item-card:hover {
          transform: translateY(-5px);
          box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }

        .item-card.selected {
          box-shadow: 0 0 20px rgba(255, 215, 0, 0.6);
          transform: translateY(-3px);
        }

        .item-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 10px;
        }

        .item-icon {
          font-size: 2rem;
        }

        .item-quantity {
          background: #333;
          color: white;
          padding: 4px 8px;
          border-radius: 20px;
          font-size: 0.8rem;
          font-weight: bold;
          min-width: 25px;
          text-align: center;
        }

        .item-name {
          margin: 0 0 8px 0;
          font-size: 1.1rem;
          font-weight: bold;
        }

        .item-description {
          margin: 0 0 12px 0;
          font-size: 0.85rem;
          opacity: 0.8;
          line-height: 1.4;
        }

        .item-tags {
          display: flex;
          flex-wrap: wrap;
          gap: 5px;
          margin-bottom: 12px;
        }

        .tag {
          padding: 3px 8px;
          border-radius: 12px;
          font-size: 0.7rem;
          font-weight: 500;
          text-transform: uppercase;
        }

        .category-tag {
          background: #E3F2FD;
          color: #1976D2;
        }

        .type-tag {
          background: #F3E5F5;
          color: #7B1FA2;
        }

        .rarity-tag {
          color: white;
          font-weight: bold;
        }

        .item-properties {
          margin-bottom: 12px;
        }

        .property {
          display: flex;
          justify-content: space-between;
          font-size: 0.85rem;
          margin-bottom: 4px;
        }

        .property-value {
          font-weight: bold;
          color: #1976D2;
        }

        .item-status {
          display: flex;
          gap: 5px;
          margin-bottom: 12px;
        }

        .status-tag {
          padding: 2px 6px;
          border-radius: 8px;
          font-size: 0.7rem;
          font-weight: bold;
        }

        .status-tag.planted {
          background: #C8E6C9;
          color: #2E7D32;
        }

        .status-tag.growing {
          background: #FFF3E0;
          color: #F57C00;
        }

        .status-tag.ready {
          background: #E8F5E8;
          color: #4CAF50;
        }

        .item-actions {
          display: flex;
          gap: 8px;
          margin-bottom: 8px;
        }

        .item-source {
          font-size: 0.75rem;
          opacity: 0.6;
          text-align: right;
        }

        .btn {
          padding: 8px 16px;
          border: none;
          border-radius: 6px;
          cursor: pointer;
          font-weight: 500;
          transition: all 0.2s ease;
          text-decoration: none;
          display: inline-block;
          text-align: center;
        }

        .btn:hover {
          transform: translateY(-1px);
        }

        .btn:disabled {
          opacity: 0.5;
          cursor: not-allowed;
          transform: none;
        }

        .btn-primary {
          background: linear-gradient(45deg, #667eea, #764ba2);
          color: white;
        }

        .btn-secondary {
          background: #6C757D;
          color: white;
        }

        .btn-success {
          background: #28A745;
          color: white;
        }

        .btn-warning {
          background: #FFC107;
          color: #333;
        }

        .btn-accent {
          background: linear-gradient(45deg, #FF6B6B, #FF8E53);
          color: white;
        }

        .btn-sm {
          padding: 4px 8px;
          font-size: 0.8rem;
        }

        .empty-state {
          text-align: center;
          padding: 60px 20px;
          opacity: 0.8;
        }

        .empty-state p {
          font-size: 1.1rem;
          margin-bottom: 20px;
        }

        .production-indicator {
          font-size: 0.75rem;
          color: #4CAF50;
          background: rgba(76, 175, 80, 0.1);
          padding: 4px 6px;
          border-radius: 4px;
          margin: 4px 0;
          display: flex;
          align-items: center;
          gap: 4px;
          z-index: 5;
          font-weight: bold;
          text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }

        @keyframes pulse {
          0%, 100% { 
            transform: scale(1); 
            opacity: 0.7; 
          }
          50% { 
            transform: scale(1.2); 
            opacity: 1; 
          }
        }

        @media (max-width: 768px) {
          .inventory-filters {
            grid-template-columns: 1fr;
          }
          
          .inventory-grid {
            grid-template-columns: 1fr;
          }
          
          .inventory-actions {
            flex-direction: column;
            gap: 10px;
          }
        }
      `}</style>
    </div>
  )
} 