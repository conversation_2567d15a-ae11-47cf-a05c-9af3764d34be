/* 摄像头视图容器 */
.camera-view {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.camera-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  width: 100%;
}

/* 视频包装器 */
.video-wrapper {
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  background: linear-gradient(45deg, #e8f5e8, #f0f8ff);
}

/* 摄像头视频 */
.camera-video {
  display: block;
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 12px;
}

/* 视频占位符 */
.video-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border: 2px dashed #cbd5e0;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.video-placeholder:hover {
  background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e0 100%);
  border-color: #a0aec0;
}

.placeholder-content {
  text-align: center;
  color: #64748b;
}

.placeholder-icon {
  font-size: 3rem;
  margin-bottom: 0.5rem;
  opacity: 0.7;
}

.placeholder-content p {
  font-size: 1rem;
  margin: 0;
  font-weight: 500;
}

/* 视频覆盖层 */
.video-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  border-radius: 12px;
}

.video-overlay p {
  margin: 0.5rem 0 0 0;
  font-size: 1rem;
}

/* 加载动画 */
.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-left: 4px solid #ffffff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 控制面板 */
.camera-controls {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  width: 100%;
  max-width: 600px;
}

.control-row {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
  justify-content: center;
}

/* 控制按钮 */
.control-btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  min-width: 140px;
  justify-content: center;
}

.control-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.control-btn.primary {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
}

.control-btn.primary:hover:not(:disabled) {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
  transform: translateY(-2px);
}

.control-btn.secondary {
  background: linear-gradient(135deg, #64748b 0%, #475569 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(100, 116, 139, 0.3);
}

.control-btn.secondary:hover {
  background: linear-gradient(135deg, #475569 0%, #334155 100%);
  box-shadow: 0 6px 20px rgba(100, 116, 139, 0.4);
  transform: translateY(-2px);
}

/* 设备选择器 */
.control-row label {
  font-weight: 600;
  color: #374151;
  white-space: nowrap;
}

.control-row select {
  padding: 0.5rem 1rem;
  border: 2px solid #e5e7eb;
  border-radius: 6px;
  background: white;
  font-size: 0.875rem;
  color: #374151;
  cursor: pointer;
  transition: border-color 0.3s ease;
  min-width: 200px;
}

.control-row select:focus {
  outline: none;
  border-color: #10b981;
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.control-row select:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 状态指示器 */
.camera-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 20px;
  font-weight: 600;
  font-size: 0.875rem;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.status-icon {
  font-size: 1rem;
}

.status-text {
  white-space: nowrap;
}

/* 不支持警告 */
.unsupported-warning {
  background: linear-gradient(135deg, #fef3c7, #fde68a);
  color: #92400e;
  padding: 0.75rem 1rem;
  border-radius: 8px;
  font-weight: 500;
  border: 1px solid #f59e0b;
  text-align: center;
  width: 100%;
}

/* 错误提示 */
.camera-error {
  background: linear-gradient(135deg, #fef2f2, #fee2e2);
  border: 1px solid #f87171;
  border-radius: 12px;
  padding: 1.5rem;
  width: 100%;
  max-width: 600px;
  margin-top: 1rem;
}

.camera-error .error-icon {
  font-size: 2rem;
  text-align: center;
  margin-bottom: 1rem;
}

.camera-error .error-content h4 {
  color: #dc2626;
  margin: 0 0 0.5rem 0;
  font-size: 1.125rem;
  font-weight: 700;
}

.camera-error .error-content p {
  color: #7f1d1d;
  margin: 0 0 1rem 0;
  font-weight: 500;
}

.camera-error .error-help {
  background: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
  padding: 1rem;
  margin-top: 1rem;
}

.camera-error .error-help p {
  margin: 0 0 0.5rem 0;
  font-weight: 600;
  color: #991b1b;
}

.camera-error .error-help ul {
  margin: 0;
  padding-left: 1.5rem;
  color: #7f1d1d;
}

.camera-error .error-help li {
  margin-bottom: 0.25rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .camera-view {
    padding: 0.75rem;
  }
  
  .video-wrapper {
    width: 100% !important;
    height: auto !important;
    aspect-ratio: 4/3;
  }
  
  .control-row {
    flex-direction: column;
    align-items: stretch;
  }
  
  .control-btn {
    min-width: auto;
    width: 100%;
  }
  
  .control-row select {
    min-width: auto;
    width: 100%;
  }
  
  .placeholder-icon {
    font-size: 2rem;
  }
} 