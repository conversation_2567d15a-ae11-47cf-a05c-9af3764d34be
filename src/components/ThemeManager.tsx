import React, { useState, useEffect } from 'react'
import './ThemeManager.css'
import { FarmTheme, ThemeStyle, UnlockConditionType } from '../types/decoration'
import { FARM_THEMES } from '../data/decorationItems'

interface ThemeManagerProps {
  currentTheme: string
  unlockedThemes: string[]
  playerLevel: number
  focusTokens: number
  achievements: string[]
  onThemeChange: (themeId: string) => void
  onThemePurchase: (themeId: string, price: number) => void
}

interface FilterOption {
  key: string
  label: string
  value: ThemeStyle | 'all'
}

export const ThemeManager: React.FC<ThemeManagerProps> = ({
  currentTheme,
  unlockedThemes,
  playerLevel,
  focusTokens,
  achievements,
  onThemeChange,
  onThemePurchase
}) => {
  const [selectedTheme, setSelectedTheme] = useState<string>(currentTheme)
  const [previewMode, setPreviewMode] = useState<boolean>(false)
  const [filterCategory, setFilterCategory] = useState<ThemeStyle | 'all'>('all')
  const [searchQuery, setSearchQuery] = useState<string>('')
  const [sortBy, setSortBy] = useState<'name' | 'price' | 'style'>('name')

  // 过滤选项
  const filterOptions: FilterOption[] = [
    { key: 'all', label: '全部风格', value: 'all' },
    { key: 'natural', label: '自然风格', value: ThemeStyle.NATURAL },
    { key: 'eastern', label: '东方风格', value: ThemeStyle.EASTERN },
    { key: 'futuristic', label: '科技风格', value: ThemeStyle.FUTURISTIC },
    { key: 'rustic', label: '田园风格', value: ThemeStyle.RUSTIC },
    { key: 'tropical', label: '热带风格', value: ThemeStyle.TROPICAL },
    { key: 'seasonal', label: '季节主题', value: ThemeStyle.SEASONAL },
    { key: 'exotic', label: '异域风情', value: ThemeStyle.EXOTIC }
  ]

  // 检查主题解锁条件
  const checkThemeUnlocked = (theme: FarmTheme): boolean => {
    if (theme.isDefault || unlockedThemes.includes(theme.id)) {
      return true
    }

    return theme.unlockConditions.every(condition => {
      switch (condition.type) {
        case UnlockConditionType.FARM_LEVEL:
          return playerLevel >= (condition.value as number)
        case UnlockConditionType.FOCUS_TOKENS:
          return focusTokens >= (condition.value as number)
        case UnlockConditionType.ACHIEVEMENT:
          return achievements.includes(condition.value as string)
        case UnlockConditionType.ITEM_COUNT:
          // 这里需要传入装饰道具数量，暂时返回true
          return true
        default:
          return false
      }
    })
  }

  // 检查是否可以购买主题
  const canPurchaseTheme = (theme: FarmTheme): boolean => {
    if (unlockedThemes.includes(theme.id) || theme.isDefault) {
      return false
    }
    return checkThemeUnlocked(theme) && focusTokens >= theme.price
  }

  // 获取主题状态文本
  const getThemeStatusText = (theme: FarmTheme): string => {
    if (theme.isDefault) return '默认主题'
    if (unlockedThemes.includes(theme.id)) return '已拥有'
    if (checkThemeUnlocked(theme)) return `${theme.price} 专注代币`
    
    // 显示解锁条件
    const unmetConditions = theme.unlockConditions.filter(condition => {
      switch (condition.type) {
        case UnlockConditionType.FARM_LEVEL:
          return playerLevel < (condition.value as number)
        case UnlockConditionType.FOCUS_TOKENS:
          return focusTokens < (condition.value as number)
        case UnlockConditionType.ACHIEVEMENT:
          return !achievements.includes(condition.value as string)
        default:
          return false
      }
    })

    if (unmetConditions.length > 0) {
      return unmetConditions[0].description
    }

    return '未解锁'
  }

  // 过滤和排序主题
  const filteredThemes = FARM_THEMES
    .filter(theme => {
      // 分类过滤
      if (filterCategory !== 'all' && theme.category !== filterCategory) {
        return false
      }
      
      // 搜索过滤
      if (searchQuery && !theme.name.toLowerCase().includes(searchQuery.toLowerCase()) &&
          !theme.description.toLowerCase().includes(searchQuery.toLowerCase())) {
        return false
      }
      
      return true
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'price':
          return a.price - b.price
        case 'style':
          return a.category.localeCompare(b.category)
        case 'name':
        default:
          return a.name.localeCompare(b.name)
      }
    })

  // 预览主题
  const handlePreviewTheme = (themeId: string) => {
    setSelectedTheme(themeId)
    setPreviewMode(true)
  }

  // 应用主题
  const handleApplyTheme = () => {
    if (selectedTheme !== currentTheme) {
      onThemeChange(selectedTheme)
    }
    setPreviewMode(false)
  }

  // 取消预览
  const handleCancelPreview = () => {
    setSelectedTheme(currentTheme)
    setPreviewMode(false)
  }

  // 购买主题
  const handlePurchaseTheme = (theme: FarmTheme) => {
    if (canPurchaseTheme(theme)) {
      onThemePurchase(theme.id, theme.price)
    }
  }

  useEffect(() => {
    setSelectedTheme(currentTheme)
  }, [currentTheme])

  return (
    <div className="theme-manager">
      <div className="theme-manager__header">
        <h2 className="theme-manager__title">农场主题</h2>
        <p className="theme-manager__subtitle">选择您的农场风格，展现独特个性</p>
        
        {/* 当前主题显示 */}
        <div className="theme-manager__current">
          <span className="current-theme__label">当前主题：</span>
          <span className="current-theme__name">
            {FARM_THEMES.find(t => t.id === currentTheme)?.name || '未知主题'}
          </span>
        </div>
      </div>

      {/* 预览模式提示 */}
      {previewMode && (
        <div className="theme-manager__preview-bar">
          <div className="preview-bar__content">
            <span className="preview-bar__text">
              正在预览：{FARM_THEMES.find(t => t.id === selectedTheme)?.name}
            </span>
            <div className="preview-bar__actions">
              <button 
                className="btn btn--primary btn--small"
                onClick={handleApplyTheme}
              >
                应用主题
              </button>
              <button 
                className="btn btn--secondary btn--small"
                onClick={handleCancelPreview}
              >
                取消预览
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 筛选和搜索 */}
      <div className="theme-manager__filters">
        <div className="filters__row">
          <div className="filter-group">
            <label className="filter-label">分类筛选：</label>
            <select 
              value={filterCategory} 
              onChange={(e) => setFilterCategory(e.target.value as ThemeStyle | 'all')}
              className="filter-select"
            >
              {filterOptions.map(option => (
                <option key={option.key} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          <div className="filter-group">
            <label className="filter-label">排序方式：</label>
            <select 
              value={sortBy} 
              onChange={(e) => setSortBy(e.target.value as 'name' | 'price' | 'style')}
              className="filter-select"
            >
              <option value="name">按名称</option>
              <option value="price">按价格</option>
              <option value="style">按风格</option>
            </select>
          </div>

          <div className="filter-group filter-group--search">
            <input
              type="text"
              placeholder="搜索主题..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="search-input"
            />
          </div>
        </div>
      </div>

      {/* 主题网格 */}
      <div className="theme-manager__grid">
        {filteredThemes.map(theme => {
          const isUnlocked = checkThemeUnlocked(theme)
          const isOwned = unlockedThemes.includes(theme.id) || theme.isDefault
          const isCurrent = theme.id === currentTheme
          const isSelected = theme.id === selectedTheme
          const canPurchase = canPurchaseTheme(theme)

          return (
            <div 
              key={theme.id}
              className={`theme-card ${isCurrent ? 'theme-card--current' : ''} ${isSelected ? 'theme-card--selected' : ''} ${!isUnlocked ? 'theme-card--locked' : ''}`}
            >
              {/* 主题预览图 */}
              <div className="theme-card__preview">
                <div 
                  className="theme-preview"
                  style={{
                    backgroundImage: `url(${theme.visual.backgroundTexture})`,
                    borderColor: theme.visual.colorPalette?.primary
                  }}
                >
                  <div className="theme-preview__overlay">
                    {/* 状态标识 */}
                    {isCurrent && <span className="status-badge status-badge--current">当前</span>}
                    {isSelected && !isCurrent && <span className="status-badge status-badge--selected">已选</span>}
                    {!isUnlocked && <span className="status-badge status-badge--locked">🔒</span>}
                  </div>
                </div>
              </div>

              {/* 主题信息 */}
              <div className="theme-card__info">
                <div className="theme-info__header">
                  <h3 className="theme-info__name">{theme.name}</h3>
                  <span className="theme-info__category">{theme.category}</span>
                </div>
                
                <p className="theme-info__description">{theme.description}</p>

                {/* 主题标签 */}
                {theme.tags && theme.tags.length > 0 && (
                  <div className="theme-info__tags">
                    {theme.tags.slice(0, 3).map(tag => (
                      <span key={tag} className="theme-tag">{tag}</span>
                    ))}
                  </div>
                )}

                {/* 主题效果 */}
                {theme.themeEffects && theme.themeEffects.length > 0 && (
                  <div className="theme-info__effects">
                    <h4 className="effects__title">主题效果：</h4>
                    <ul className="effects__list">
                      {theme.themeEffects.slice(0, 2).map((effect, index) => (
                        <li key={index} className="effect-item">
                          {effect.description}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>

              {/* 主题操作 */}
              <div className="theme-card__actions">
                <div className="theme-status">
                  <span className="theme-status__text">
                    {getThemeStatusText(theme)}
                  </span>
                </div>

                <div className="theme-actions">
                  {/* 预览按钮 */}
                  {isUnlocked && !isCurrent && (
                    <button
                      className="btn btn--secondary btn--small"
                      onClick={() => handlePreviewTheme(theme.id)}
                    >
                      预览
                    </button>
                  )}

                  {/* 应用按钮 */}
                  {isOwned && !isCurrent && (
                    <button
                      className="btn btn--primary btn--small"
                      onClick={() => onThemeChange(theme.id)}
                    >
                      应用
                    </button>
                  )}

                  {/* 购买按钮 */}
                  {!isOwned && canPurchase && (
                    <button
                      className="btn btn--purchase btn--small"
                      onClick={() => handlePurchaseTheme(theme)}
                    >
                      购买
                    </button>
                  )}

                  {/* 当前主题标识 */}
                  {isCurrent && (
                    <span className="current-indicator">当前使用</span>
                  )}
                </div>
              </div>
            </div>
          )
        })}
      </div>

      {/* 空状态 */}
      {filteredThemes.length === 0 && (
        <div className="theme-manager__empty">
          <p>没有找到符合条件的主题</p>
          <button 
            className="btn btn--secondary"
            onClick={() => {
              setFilterCategory('all')
              setSearchQuery('')
            }}
          >
            清除筛选
          </button>
        </div>
      )}
    </div>
  )
}

export default ThemeManager 