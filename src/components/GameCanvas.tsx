import React, { useEffect, useRef } from 'react'
import Phaser from 'phaser'
import { EnhancedFarmScene } from '../game/scenes/EnhancedFarmScene'

export const GameCanvas: React.FC = () => {
  const gameRef = useRef<Phaser.Game | null>(null)
  const phaserRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (phaserRef.current && !gameRef.current) {
      const config: Phaser.Types.Core.GameConfig = {
        type: Phaser.AUTO,
        width: 800,
        height: 600,
        parent: phaserRef.current,
        backgroundColor: '#87CEEB',
        scene: [EnhancedFarmScene],
        scale: {
          mode: Phaser.Scale.NONE,
          autoCenter: Phaser.Scale.CENTER_BOTH
        },
        physics: {
          default: 'arcade',
          arcade: {
            gravity: { y: 0, x: 0 },
            debug: false
          }
        }
      }

      gameRef.current = new Phaser.Game(config)
    }

    return () => {
      if (gameRef.current) {
        gameRef.current.destroy(true)
        gameRef.current = null
      }
    }
  }, [])

  return <div ref={phaserRef} className="phaser-container" />
} 