import React, { useState, useEffect } from 'react'
import { EnhancedFarmSystem } from '../systems/EnhancedFarmSystem'
import { 
  ExtendedFarmData, 
  FarmPlot, 
  TerrainType, 
  Season, 
  WeatherType 
} from '../types/gameModels'
import { ItemRarity } from '../types/lootbox'

// 增强农场系统界面组件
export const EnhancedPlantingSystem: React.FC = () => {
  const [farmSystem, setFarmSystem] = useState<EnhancedFarmSystem | null>(null)
  const [farmData, setFarmData] = useState<ExtendedFarmData | null>(null)
  const [selectedPlot, setSelectedPlot] = useState<string | null>(null)
  const [selectedCrop, setSelectedCrop] = useState<string>('corn')

  // 可用作物列表
  const availableCrops = [
    { id: 'corn', name: '🌽 玉米', description: '平原地形+20%产量' },
    { id: 'wheat', name: '🌾 小麦', description: '春季+40%产量' },
    { id: 'soybean', name: '🫘 大豆', description: '夏季+20%产量' },
    { id: 'cotton', name: '🌾 棉花', description: '山地+10%产量' },
    { id: 'apple', name: '🍎 苹果', description: '山腰+20%产量' },
    { id: 'rice', name: '🌾 稻米', description: '湿地+30%产量' }
  ]

  // 地形信息
  const terrainInfo = {
    [TerrainType.PLAINS]: { name: '🌾 平原', color: '#90EE90' },
    [TerrainType.HILLS]: { name: '⛰️ 山地', color: '#DEB887' },
    [TerrainType.WETLANDS]: { name: '🌿 湿地', color: '#87CEEB' },
    [TerrainType.FERTILE_VALLEY]: { name: '🌺 富饶山谷', color: '#98FB98' },
    [TerrainType.MOUNTAINSIDE]: { name: '🏔️ 山腰', color: '#D2B48C' }
  }

  // 季节信息
  const seasonInfo = {
    [Season.SPRING]: { name: '🌸 春季', color: '#FFB6C1' },
    [Season.SUMMER]: { name: '☀️ 夏季', color: '#FFD700' },
    [Season.AUTUMN]: { name: '🍂 秋季', color: '#FF8C00' },
    [Season.WINTER]: { name: '❄️ 冬季', color: '#87CEEB' }
  }

  // 天气信息
  const weatherInfo = {
    [WeatherType.SUNNY]: { name: '☀️ 晴天', color: '#FFD700' },
    [WeatherType.RAINY]: { name: '🌧️ 雨天', color: '#4682B4' },
    [WeatherType.CLOUDY]: { name: '☁️ 多云', color: '#A9A9A9' },
    [WeatherType.STORMY]: { name: '⛈️ 暴风雨', color: '#696969' },
    [WeatherType.DROUGHT]: { name: '🌵 干旱', color: '#D2691E' },
    [WeatherType.SNOW]: { name: '🌨️ 下雪', color: '#B0C4DE' }
  }

  // 初始化农场系统
  useEffect(() => {
    const initialFarmData: ExtendedFarmData = {
      id: 'farm_1',
      ownerId: 'player_1',
      name: '我的农场',
      level: 1,
      size: {
        totalPlots: 9, // 3x3网格
        usedPlots: 0
      },
      terrain: TerrainType.PLAINS,
      plots: [],
      currentSeason: Season.SPRING,
      currentWeather: WeatherType.SUNNY,
      soilHealth: 75,
      waterLevel: 80,
      buildings: [],
      equipment: [],
      storage: {},
      decorations: [],
      totalValue: 0,
      income: 0,
      expenses: 0,
      theme: 'default'
    }

    const system = new EnhancedFarmSystem(initialFarmData)
    setFarmSystem(system)
    setFarmData(system.getFarmData())

    // 监听更新
    const updateCallback = () => {
      setFarmData(system.getFarmData())
    }
    system.addUpdateCallback(updateCallback)

    return () => {
      system.removeUpdateCallback(updateCallback)
      system.destroy()
    }
  }, [])

  // 种植作物
  const handlePlantCrop = (plotId: string) => {
    if (farmSystem) {
      const success = farmSystem.plantCrop(plotId, selectedCrop)
      if (success) {
        console.log(`✅ 种植成功: ${selectedCrop} 在 ${plotId}`)
      } else {
        console.log(`❌ 种植失败: ${plotId}`)
      }
    }
  }

  // 收获作物
  const handleHarvestCrop = (plotId: string) => {
    if (farmSystem) {
      const harvestedCrop = farmSystem.harvestCrop(plotId)
      if (harvestedCrop) {
        console.log(`🎉 收获成功:`, harvestedCrop)
      } else {
        console.log(`❌ 收获失败: ${plotId}`)
      }
    }
  }

  // 治疗疾病
  const handleTreatDisease = (plotId: string, diseaseIndex: number) => {
    if (farmSystem) {
      const success = farmSystem.treatDisease(plotId, diseaseIndex)
      if (success) {
        console.log(`💊 疾病治疗成功`)
      }
    }
  }

  // 控制虫害
  const handleControlPest = (plotId: string, pestIndex: number) => {
    if (farmSystem) {
      const success = farmSystem.controlPest(plotId, pestIndex)
      if (success) {
        console.log(`🔫 虫害控制成功`)
      }
    }
  }

  // 改良土壤
  const handleImproveSoil = (plotId: string, method: 'fertilizer' | 'compost' | 'lime') => {
    if (farmSystem) {
      const success = farmSystem.improveSoil(plotId, method)
      if (success) {
        console.log(`🌱 土壤改良成功`)
      }
    }
  }

  if (!farmData) {
    return (
      <div style={{ 
        padding: '20px', 
        textAlign: 'center',
        background: 'linear-gradient(135deg, #87CEEB 0%, #98FB98 100%)',
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <div style={{
          background: 'white',
          padding: '30px',
          borderRadius: '15px',
          boxShadow: '0 8px 25px rgba(0,0,0,0.1)'
        }}>
          <h2>🌱 正在初始化增强农场系统...</h2>
        </div>
      </div>
    )
  }

  const environment = farmSystem?.getCurrentEnvironment()

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #87CEEB 0%, #98FB98 100%)',
      padding: '20px'
    }}>
      {/* 标题和环境信息 */}
      <div style={{
        textAlign: 'center',
        marginBottom: '20px',
        color: '#2C5530'
      }}>
        <h1 style={{ 
          fontSize: '2.5rem', 
          marginBottom: '10px',
          textShadow: '2px 2px 4px rgba(0,0,0,0.1)'
        }}>
          🌾 增强农场收集系统
        </h1>
        <div style={{
          display: 'flex',
          justifyContent: 'center',
          gap: '20px',
          flexWrap: 'wrap'
        }}>
          {environment && (
            <>
              <div style={{
                background: seasonInfo[environment.season].color,
                padding: '8px 15px',
                borderRadius: '20px',
                border: '2px solid white',
                fontWeight: 'bold'
              }}>
                {seasonInfo[environment.season].name}
              </div>
              <div style={{
                background: weatherInfo[environment.weather].color,
                padding: '8px 15px',
                borderRadius: '20px',
                border: '2px solid white',
                fontWeight: 'bold'
              }}>
                {weatherInfo[environment.weather].name}
              </div>
              <div style={{
                background: terrainInfo[farmData.terrain].color,
                padding: '8px 15px',
                borderRadius: '20px',
                border: '2px solid white',
                fontWeight: 'bold'
              }}>
                {terrainInfo[farmData.terrain].name}
              </div>
            </>
          )}
        </div>
      </div>

      <div style={{
        display: 'grid',
        gridTemplateColumns: '300px 1fr 300px',
        gap: '20px',
        maxWidth: '1400px',
        margin: '0 auto'
      }}>
        {/* 左侧控制面板 */}
        <div style={{
          background: 'rgba(255,255,255,0.95)',
          borderRadius: '15px',
          padding: '20px',
          boxShadow: '0 8px 25px rgba(0,0,0,0.1)',
          height: 'fit-content'
        }}>
          <h3 style={{ marginBottom: '15px', color: '#2C5530' }}>🌱 种植控制</h3>
          
          {/* 作物选择 */}
          <div style={{ marginBottom: '20px' }}>
            <label style={{ fontWeight: 'bold', display: 'block', marginBottom: '8px' }}>
              选择作物：
            </label>
            <select 
              value={selectedCrop}
              onChange={(e) => setSelectedCrop(e.target.value)}
              style={{
                width: '100%',
                padding: '8px',
                borderRadius: '8px',
                border: '2px solid #4CAF50',
                fontSize: '14px'
              }}
            >
              {availableCrops.map(crop => (
                <option key={crop.id} value={crop.id}>
                  {crop.name}
                </option>
              ))}
            </select>
            <div style={{
              fontSize: '12px',
              color: '#666',
              marginTop: '5px',
              fontStyle: 'italic'
            }}>
              {availableCrops.find(c => c.id === selectedCrop)?.description}
            </div>
          </div>

          {/* 农场统计 */}
          <div style={{
            background: '#f8f9fa',
            padding: '15px',
            borderRadius: '10px',
            marginBottom: '15px'
          }}>
            <h4 style={{ margin: '0 0 10px 0', color: '#2C5530' }}>📊 农场统计</h4>
            <div style={{ fontSize: '14px', lineHeight: '1.5' }}>
              <div>🏠 农场等级: {farmData.level}</div>
              <div>📏 使用地块: {farmData.size.usedPlots}/{farmData.size.totalPlots}</div>
              <div>🌱 土壤健康: {farmData.soilHealth}%</div>
              <div>💧 水位: {farmData.waterLevel}%</div>
            </div>
          </div>

          {/* 操作按钮 */}
          {selectedPlot && (
            <div style={{
              background: '#e8f5e8',
              padding: '15px',
              borderRadius: '10px',
              border: '2px solid #4CAF50'
            }}>
              <h4 style={{ margin: '0 0 10px 0', color: '#2C5530' }}>
                🎯 地块操作: {selectedPlot}
              </h4>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                <button
                  onClick={() => handlePlantCrop(selectedPlot)}
                  style={{
                    background: '#4CAF50',
                    color: 'white',
                    border: 'none',
                    padding: '10px',
                    borderRadius: '8px',
                    cursor: 'pointer',
                    fontWeight: 'bold'
                  }}
                >
                  🌱 种植 {availableCrops.find(c => c.id === selectedCrop)?.name}
                </button>
                
                <button
                  onClick={() => handleHarvestCrop(selectedPlot)}
                  style={{
                    background: '#FF6B35',
                    color: 'white',
                    border: 'none',
                    padding: '10px',
                    borderRadius: '8px',
                    cursor: 'pointer',
                    fontWeight: 'bold'
                  }}
                >
                  🎉 收获作物
                </button>

                <div style={{ display: 'flex', gap: '5px' }}>
                  <button
                    onClick={() => handleImproveSoil(selectedPlot, 'fertilizer')}
                    style={{
                      background: '#8BC34A',
                      color: 'white',
                      border: 'none',
                      padding: '8px',
                      borderRadius: '6px',
                      cursor: 'pointer',
                      fontSize: '12px',
                      flex: 1
                    }}
                  >
                    💩 施肥
                  </button>
                  <button
                    onClick={() => handleImproveSoil(selectedPlot, 'compost')}
                    style={{
                      background: '#795548',
                      color: 'white',
                      border: 'none',
                      padding: '8px',
                      borderRadius: '6px',
                      cursor: 'pointer',
                      fontSize: '12px',
                      flex: 1
                    }}
                  >
                    🍂 堆肥
                  </button>
                  <button
                    onClick={() => handleImproveSoil(selectedPlot, 'lime')}
                    style={{
                      background: '#9E9E9E',
                      color: 'white',
                      border: 'none',
                      padding: '8px',
                      borderRadius: '6px',
                      cursor: 'pointer',
                      fontSize: '12px',
                      flex: 1
                    }}
                  >
                    🧂 石灰
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* 中央农场网格 */}
        <div style={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center'
        }}>
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(3, 1fr)',
            gap: '15px',
            background: 'rgba(255,255,255,0.95)',
            padding: '25px',
            borderRadius: '20px',
            boxShadow: '0 8px 25px rgba(0,0,0,0.1)'
          }}>
            {farmData.plots.map((plot, index) => (
              <div
                key={plot.id}
                onClick={() => setSelectedPlot(plot.id)}
                style={{
                  width: '150px',
                  height: '150px',
                  background: plot.currentCrop 
                    ? `linear-gradient(135deg, ${terrainInfo[plot.terrain].color} 0%, #90EE90 100%)`
                    : terrainInfo[plot.terrain].color,
                  border: selectedPlot === plot.id ? '4px solid #FF6B35' : '3px solid #4CAF50',
                  borderRadius: '15px',
                  cursor: 'pointer',
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  justifyContent: 'center',
                  position: 'relative',
                  transition: 'all 0.3s ease',
                  boxShadow: selectedPlot === plot.id 
                    ? '0 8px 20px rgba(255,107,53,0.3)' 
                    : '0 4px 10px rgba(0,0,0,0.1)'
                }}
                onMouseEnter={(e) => {
                  if (selectedPlot !== plot.id) {
                    e.currentTarget.style.transform = 'scale(1.05)'
                  }
                }}
                onMouseLeave={(e) => {
                  if (selectedPlot !== plot.id) {
                    e.currentTarget.style.transform = 'scale(1)'
                  }
                }}
              >
                {/* 地块信息 */}
                <div style={{
                  fontSize: '12px',
                  fontWeight: 'bold',
                  color: '#2C5530',
                  marginBottom: '5px'
                }}>
                  {plot.id}
                </div>

                {/* 作物状态 */}
                {plot.currentCrop ? (
                  <div style={{ textAlign: 'center' }}>
                    <div style={{ fontSize: '24px', marginBottom: '5px' }}>
                      {availableCrops.find(c => c.id === plot.currentCrop?.cropType)?.name?.split(' ')[0] || '🌱'}
                    </div>
                    <div style={{
                      fontSize: '11px',
                      fontWeight: 'bold',
                      color: '#2C5530'
                    }}>
                      成长: {Math.round(plot.currentCrop.growthStage)}%
                    </div>
                    <div style={{
                      width: '100px',
                      height: '4px',
                      background: '#ddd',
                      borderRadius: '2px',
                      margin: '3px auto',
                      overflow: 'hidden'
                    }}>
                      <div style={{
                        width: `${plot.currentCrop.growthStage}%`,
                        height: '100%',
                        background: plot.currentCrop.growthStage >= 100 
                          ? '#4CAF50' 
                          : `linear-gradient(90deg, #FFA000 0%, #4CAF50 ${plot.currentCrop.growthStage}%)`,
                        transition: 'width 0.3s ease'
                      }} />
                    </div>
                    <div style={{
                      fontSize: '10px',
                      color: plot.currentCrop.health < 50 ? '#FF5722' : '#2C5530'
                    }}>
                      健康: {Math.round(plot.currentCrop.health)}%
                    </div>
                  </div>
                ) : (
                  <div style={{
                    fontSize: '40px',
                    opacity: 0.6
                  }}>
                    🌍
                  </div>
                )}

                {/* 土壤肥力指示器 */}
                <div style={{
                  position: 'absolute',
                  bottom: '8px',
                  left: '8px',
                  right: '8px',
                  fontSize: '10px',
                  textAlign: 'center',
                  color: '#666'
                }}>
                  🌱 {Math.round(plot.soilFertility)}%
                </div>

                {/* 问题指示器 */}
                {(plot.diseases.length > 0 || plot.pests.length > 0) && (
                  <div style={{
                    position: 'absolute',
                    top: '8px',
                    right: '8px',
                    display: 'flex',
                    gap: '2px'
                  }}>
                    {plot.diseases.length > 0 && (
                      <span style={{ fontSize: '16px' }}>🦠</span>
                    )}
                    {plot.pests.length > 0 && (
                      <span style={{ fontSize: '16px' }}>🐛</span>
                    )}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* 右侧信息面板 */}
        <div style={{
          background: 'rgba(255,255,255,0.95)',
          borderRadius: '15px',
          padding: '20px',
          boxShadow: '0 8px 25px rgba(0,0,0,0.1)',
          height: 'fit-content'
        }}>
          <h3 style={{ marginBottom: '15px', color: '#2C5530' }}>📊 地块详情</h3>
          
          {selectedPlot ? (
            <div>
              {(() => {
                const plot = farmData.plots.find(p => p.id === selectedPlot)
                if (!plot) return <div>未找到地块</div>
                
                return (
                  <div>
                    <div style={{
                      background: '#f8f9fa',
                      padding: '15px',
                      borderRadius: '10px',
                      marginBottom: '15px'
                    }}>
                      <h4 style={{ margin: '0 0 10px 0', color: '#2C5530' }}>
                        {plot.id} - {terrainInfo[plot.terrain].name}
                      </h4>
                      <div style={{ fontSize: '14px', lineHeight: '1.5' }}>
                        <div>🌱 土壤肥力: {Math.round(plot.soilFertility)}%</div>
                        <div>📊 轮作历史: {plot.rotationHistory.slice(-3).join(' → ') || '无'}</div>
                      </div>
                    </div>

                    {/* 当前作物信息 */}
                    {plot.currentCrop && (
                      <div style={{
                        background: '#e8f5e8',
                        padding: '15px',
                        borderRadius: '10px',
                        marginBottom: '15px',
                        border: '2px solid #4CAF50'
                      }}>
                        <h4 style={{ margin: '0 0 10px 0', color: '#2C5530' }}>
                          🌱 当前作物
                        </h4>
                        <div style={{ fontSize: '14px', lineHeight: '1.5' }}>
                          <div>品种: {availableCrops.find(c => c.id === plot.currentCrop?.cropType)?.name}</div>
                          <div>成长: {Math.round(plot.currentCrop.growthStage)}%</div>
                          <div>健康: {Math.round(plot.currentCrop.health)}%</div>
                          <div>种植时间: {new Date(plot.currentCrop.plantedAt).toLocaleTimeString()}</div>
                          {plot.currentCrop.growthStage >= 100 && (
                            <div style={{ 
                              color: '#4CAF50', 
                              fontWeight: 'bold',
                              marginTop: '5px'
                            }}>
                              ✅ 可以收获！
                            </div>
                          )}
                        </div>
                      </div>
                    )}

                    {/* 疾病信息 */}
                    {plot.diseases.length > 0 && (
                      <div style={{
                        background: '#ffebee',
                        padding: '15px',
                        borderRadius: '10px',
                        marginBottom: '15px',
                        border: '2px solid #f44336'
                      }}>
                        <h4 style={{ margin: '0 0 10px 0', color: '#d32f2f' }}>
                          🦠 疾病问题
                        </h4>
                        {plot.diseases.map((disease, index) => (
                          <div key={index} style={{
                            display: 'flex',
                            justifyContent: 'space-between',
                            alignItems: 'center',
                            marginBottom: '8px',
                            fontSize: '14px'
                          }}>
                            <div>
                              <div>{disease.type}</div>
                              <div style={{ fontSize: '12px', color: '#666' }}>
                                严重度: {Math.round(disease.severity)}%
                              </div>
                            </div>
                            {!disease.treated && (
                              <button
                                onClick={() => handleTreatDisease(plot.id, index)}
                                style={{
                                  background: '#4CAF50',
                                  color: 'white',
                                  border: 'none',
                                  padding: '5px 10px',
                                  borderRadius: '5px',
                                  cursor: 'pointer',
                                  fontSize: '12px'
                                }}
                              >
                                💊 治疗
                              </button>
                            )}
                          </div>
                        ))}
                      </div>
                    )}

                    {/* 虫害信息 */}
                    {plot.pests.length > 0 && (
                      <div style={{
                        background: '#fff3e0',
                        padding: '15px',
                        borderRadius: '10px',
                        marginBottom: '15px',
                        border: '2px solid #ff9800'
                      }}>
                        <h4 style={{ margin: '0 0 10px 0', color: '#f57c00' }}>
                          🐛 虫害问题
                        </h4>
                        {plot.pests.map((pest, index) => (
                          <div key={index} style={{
                            display: 'flex',
                            justifyContent: 'space-between',
                            alignItems: 'center',
                            marginBottom: '8px',
                            fontSize: '14px'
                          }}>
                            <div>
                              <div>{pest.type}</div>
                              <div style={{ fontSize: '12px', color: '#666' }}>
                                数量: {Math.round(pest.population)} | 损害: {Math.round(pest.damage)}%
                              </div>
                            </div>
                            {!pest.controlled && (
                              <button
                                onClick={() => handleControlPest(plot.id, index)}
                                style={{
                                  background: '#4CAF50',
                                  color: 'white',
                                  border: 'none',
                                  padding: '5px 10px',
                                  borderRadius: '5px',
                                  cursor: 'pointer',
                                  fontSize: '12px'
                                }}
                              >
                                🔫 防治
                              </button>
                            )}
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                )
              })()}
            </div>
          ) : (
            <div style={{
              textAlign: 'center',
              color: '#666',
              fontStyle: 'italic',
              padding: '20px'
            }}>
              点击农场网格中的地块查看详细信息
            </div>
          )}
        </div>
      </div>

      {/* 底部说明 */}
      <div style={{
        textAlign: 'center',
        marginTop: '30px',
        background: 'rgba(255,255,255,0.9)',
        padding: '20px',
        borderRadius: '15px',
        maxWidth: '800px',
        margin: '30px auto 0',
        boxShadow: '0 4px 15px rgba(0,0,0,0.1)'
      }}>
        <h4 style={{ marginBottom: '15px', color: '#2C5530' }}>🎮 可玩性特色</h4>
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
          gap: '15px',
          fontSize: '14px',
          textAlign: 'left'
        }}>
          <div>
            <strong>🌍 地形系统:</strong><br />
            不同地形适合不同作物，影响产量
          </div>
          <div>
            <strong>🔄 作物轮作:</strong><br />
            连续种植同种作物会降低土壤肥力
          </div>
          <div>
            <strong>🌤️ 环境变化:</strong><br />
            季节和天气实时变化，影响生长
          </div>
          <div>
            <strong>🦠 病虫害:</strong><br />
            随机发生的疾病和虫害需要及时处理
          </div>
        </div>
      </div>
    </div>
  )
}

export default EnhancedPlantingSystem 