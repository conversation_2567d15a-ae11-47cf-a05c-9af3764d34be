import React, { useState } from 'react'

interface UserTestingProps {
  className?: string
}

interface TestResult {
  id: string
  timestamp: number
  testType: string
  duration: number
  result: 'pass' | 'fail' | 'pending'
  details: string
}

export const UserTesting: React.FC<UserTestingProps> = ({ className = '' }) => {
  const [isVisible, setIsVisible] = useState(false)
  const [testResults, setTestResults] = useState<TestResult[]>([])
  const [isRunning, setIsRunning] = useState(false)

  const runTest = async (testType: string) => {
    setIsRunning(true)
    
    const testId = `test_${Date.now()}`
    const startTime = Date.now()
    
    // 模拟测试运行
    await new Promise(resolve => setTimeout(resolve, 2000 + Math.random() * 3000))
    
    const duration = Date.now() - startTime
    const result: TestResult = {
      id: testId,
      timestamp: Date.now(),
      testType,
      duration,
      result: Math.random() > 0.3 ? 'pass' : 'fail',
      details: `测试完成，耗时 ${duration}ms`
    }
    
    setTestResults(prev => [result, ...prev.slice(0, 9)]) // 保持最新10条记录
    setIsRunning(false)
  }

  if (!isVisible) {
    return (
      <button
        onClick={() => setIsVisible(true)}
        className={`user-testing-toggle ${className}`}
        style={{
          position: 'fixed',
          top: '120px',
          right: '10px',
          zIndex: 9999,
          background: 'rgba(156, 39, 176, 0.8)',
          color: 'white',
          border: 'none',
          borderRadius: '4px',
          padding: '4px 8px',
          fontSize: '12px',
          cursor: 'pointer'
        }}
      >
        🧪 测试
      </button>
    )
  }

  return (
    <div className={`user-testing ${className}`} style={{
      position: 'fixed',
      top: '120px',
      right: '10px',
      zIndex: 9999,
      background: 'rgba(156, 39, 176, 0.9)',
      color: 'white',
      padding: '12px',
      borderRadius: '8px',
      fontSize: '12px',
      minWidth: '250px',
      maxHeight: '400px',
      overflow: 'auto'
    }}>
      <div style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        marginBottom: '10px'
      }}>
        <span style={{ fontWeight: 'bold' }}>🧪 用户测试</span>
        <button
          onClick={() => setIsVisible(false)}
          style={{
            background: 'none',
            border: 'none',
            color: 'white',
            cursor: 'pointer',
            fontSize: '12px'
          }}
        >
          ✕
        </button>
      </div>
      
      <div style={{ marginBottom: '10px' }}>
        <button
          onClick={() => runTest('UI交互测试')}
          disabled={isRunning}
          style={{
            background: '#4CAF50',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            padding: '4px 8px',
            fontSize: '11px',
            cursor: 'pointer',
            marginRight: '4px',
            marginBottom: '4px'
          }}
        >
          UI测试
        </button>
        
        <button
          onClick={() => runTest('性能测试')}
          disabled={isRunning}
          style={{
            background: '#FF9800',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            padding: '4px 8px',
            fontSize: '11px',
            cursor: 'pointer',
            marginRight: '4px',
            marginBottom: '4px'
          }}
        >
          性能测试
        </button>
        
        <button
          onClick={() => runTest('功能测试')}
          disabled={isRunning}
          style={{
            background: '#2196F3',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            padding: '4px 8px',
            fontSize: '11px',
            cursor: 'pointer',
            marginBottom: '4px'
          }}
        >
          功能测试
        </button>
      </div>
      
      {isRunning && (
        <div style={{ 
          textAlign: 'center', 
          padding: '8px',
          background: 'rgba(255, 255, 255, 0.1)',
          borderRadius: '4px',
          marginBottom: '10px'
        }}>
          ⏳ 测试运行中...
        </div>
      )}
      
      <div style={{ fontSize: '11px' }}>
        <div style={{ fontWeight: 'bold', marginBottom: '5px' }}>
          测试结果 ({testResults.length}/10)
        </div>
        
        {testResults.map(result => (
          <div
            key={result.id}
            style={{
              background: 'rgba(255, 255, 255, 0.1)',
              padding: '6px',
              borderRadius: '4px',
              marginBottom: '4px',
              borderLeft: `3px solid ${result.result === 'pass' ? '#4CAF50' : '#f44336'}`
            }}
          >
            <div style={{ fontWeight: 'bold' }}>
              {result.result === 'pass' ? '✅' : '❌'} {result.testType}
            </div>
            <div style={{ opacity: 0.8 }}>
              {new Date(result.timestamp).toLocaleTimeString()}
            </div>
            <div style={{ opacity: 0.7, fontSize: '10px' }}>
              {result.details}
            </div>
          </div>
        ))}
        
        {testResults.length === 0 && (
          <div style={{ 
            textAlign: 'center', 
            padding: '20px',
            opacity: 0.6
          }}>
            暂无测试结果
          </div>
        )}
      </div>
    </div>
  )
}

export default UserTesting 