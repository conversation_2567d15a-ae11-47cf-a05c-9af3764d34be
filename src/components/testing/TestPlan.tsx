import React, { useState } from 'react'

interface TestPlanProps {
  className?: string
}

interface TestCase {
  id: string
  title: string
  description: string
  priority: 'high' | 'medium' | 'low'
  status: 'pending' | 'running' | 'passed' | 'failed'
  estimatedTime: number
}

export const TestPlan: React.FC<TestPlanProps> = ({ className = '' }) => {
  const [isVisible, setIsVisible] = useState(false)
  const [testCases] = useState<TestCase[]>([
    {
      id: 'tc-001',
      title: '盲盒开启功能测试',
      description: '验证用户能够正常开启各种类型的盲盒并获得物品',
      priority: 'high',
      status: 'pending',
      estimatedTime: 15
    },
    {
      id: 'tc-002',
      title: '物品背包显示测试',
      description: '验证获得的物品能够正确显示在背包中',
      priority: 'high',
      status: 'pending',
      estimatedTime: 10
    },
    {
      id: 'tc-003',
      title: '工具提示功能测试',
      description: '验证鼠标悬停在物品上时能够显示正确的产量信息',
      priority: 'medium',
      status: 'pending',
      estimatedTime: 8
    },
    {
      id: 'tc-004',
      title: '物品合成功能测试',
      description: '验证用户能够拖拽物品进行合成操作',
      priority: 'medium',
      status: 'pending',
      estimatedTime: 20
    },
    {
      id: 'tc-005',
      title: '响应式布局测试',
      description: '验证在不同屏幕尺寸下界面显示正常',
      priority: 'low',
      status: 'pending',
      estimatedTime: 12
    }
  ])

  const getPriorityColor = (priority: TestCase['priority']) => {
    switch (priority) {
      case 'high': return '#f44336'
      case 'medium': return '#ff9800'
      case 'low': return '#4caf50'
    }
  }

  const getStatusColor = (status: TestCase['status']) => {
    switch (status) {
      case 'pending': return '#9e9e9e'
      case 'running': return '#2196f3'
      case 'passed': return '#4caf50'
      case 'failed': return '#f44336'
    }
  }

  const getStatusIcon = (status: TestCase['status']) => {
    switch (status) {
      case 'pending': return '⏳'
      case 'running': return '🔄'
      case 'passed': return '✅'
      case 'failed': return '❌'
    }
  }

  const totalEstimatedTime = testCases.reduce((sum, tc) => sum + tc.estimatedTime, 0)
  const passedCount = testCases.filter(tc => tc.status === 'passed').length
  const failedCount = testCases.filter(tc => tc.status === 'failed').length

  if (!isVisible) {
    return (
      <button
        onClick={() => setIsVisible(true)}
        className={`test-plan-toggle ${className}`}
        style={{
          position: 'fixed',
          top: '170px',
          right: '10px',
          zIndex: 9999,
          background: 'rgba(63, 81, 181, 0.8)',
          color: 'white',
          border: 'none',
          borderRadius: '4px',
          padding: '4px 8px',
          fontSize: '12px',
          cursor: 'pointer'
        }}
      >
        📋 测试计划
      </button>
    )
  }

  return (
    <div className={`test-plan ${className}`} style={{
      position: 'fixed',
      top: '170px',
      right: '10px',
      zIndex: 9999,
      background: 'rgba(63, 81, 181, 0.9)',
      color: 'white',
      padding: '12px',
      borderRadius: '8px',
      fontSize: '12px',
      minWidth: '300px',
      maxHeight: '500px',
      overflow: 'auto'
    }}>
      <div style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        marginBottom: '12px'
      }}>
        <span style={{ fontWeight: 'bold' }}>📋 测试计划</span>
        <button
          onClick={() => setIsVisible(false)}
          style={{
            background: 'none',
            border: 'none',
            color: 'white',
            cursor: 'pointer',
            fontSize: '12px'
          }}
        >
          ✕
        </button>
      </div>
      
      {/* 测试概要 */}
      <div style={{
        background: 'rgba(255, 255, 255, 0.1)',
        padding: '8px',
        borderRadius: '4px',
        marginBottom: '12px'
      }}>
        <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>
          测试概要
        </div>
        <div style={{ fontSize: '11px' }}>
          <div>总用例: {testCases.length}</div>
          <div>通过: {passedCount} | 失败: {failedCount}</div>
          <div>预计时间: {totalEstimatedTime}分钟</div>
        </div>
      </div>
      
      {/* 测试用例列表 */}
      <div style={{ fontSize: '11px' }}>
        <div style={{ fontWeight: 'bold', marginBottom: '8px' }}>
          测试用例
        </div>
        
        {testCases.map(testCase => (
          <div
            key={testCase.id}
            style={{
              background: 'rgba(255, 255, 255, 0.1)',
              padding: '8px',
              borderRadius: '4px',
              marginBottom: '6px',
              borderLeft: `3px solid ${getPriorityColor(testCase.priority)}`
            }}
          >
            <div style={{ 
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginBottom: '4px'
            }}>
              <span style={{ fontWeight: 'bold' }}>
                {getStatusIcon(testCase.status)} {testCase.title}
              </span>
              <span style={{ 
                background: getPriorityColor(testCase.priority),
                padding: '2px 6px',
                borderRadius: '8px',
                fontSize: '10px'
              }}>
                {testCase.priority.toUpperCase()}
              </span>
            </div>
            
            <div style={{ 
              opacity: 0.8, 
              marginBottom: '4px',
              lineHeight: '1.3'
            }}>
              {testCase.description}
            </div>
            
            <div style={{ 
              display: 'flex',
              justifyContent: 'space-between',
              fontSize: '10px',
              opacity: 0.7
            }}>
              <span>ID: {testCase.id}</span>
              <span>预计: {testCase.estimatedTime}分钟</span>
            </div>
          </div>
        ))}
      </div>
      
      {/* 操作按钮 */}
      <div style={{ 
        marginTop: '12px',
        display: 'flex',
        gap: '8px'
      }}>
        <button
          style={{
            background: '#4CAF50',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            padding: '6px 12px',
            fontSize: '11px',
            cursor: 'pointer',
            flex: 1
          }}
        >
          运行全部
        </button>
        
        <button
          style={{
            background: '#FF9800',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            padding: '6px 12px',
            fontSize: '11px',
            cursor: 'pointer',
            flex: 1
          }}
        >
          导出报告
        </button>
      </div>
    </div>
  )
}

export default TestPlan 