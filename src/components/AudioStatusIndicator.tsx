import React from 'react'
import { AudioSettings } from '../audio/audioConfig'

interface AudioStatus {
  isInitialized: boolean
  isPlaying: boolean
  currentMusic?: string
  audioContextState?: string
  loadedAudios: number
}

interface AudioStatusIndicatorProps {
  status: AudioStatus
  settings: AudioSettings
  className?: string
}

/**
 * 音频状态指示器组件
 * 用于开发调试，显示音频系统的实时状态
 */
export const AudioStatusIndicator: React.FC<AudioStatusIndicatorProps> = ({ 
  status, 
  settings, 
  className = '' 
}) => {
  const containerStyle: React.CSSProperties = {
    position: 'fixed',
    top: '10px',
    right: '10px',
    background: 'rgba(0, 0, 0, 0.8)',
    color: 'white',
    padding: '12px',
    borderRadius: '8px',
    fontSize: '12px',
    fontFamily: 'monospace',
    zIndex: 9999,
    minWidth: '200px',
    backdropFilter: 'blur(4px)',
    border: '1px solid rgba(255, 255, 255, 0.2)'
  }

  const statusItemStyle: React.CSSProperties = {
    display: 'flex',
    justifyContent: 'space-between',
    marginBottom: '4px',
    padding: '2px 0'
  }

  const indicatorStyle = (isActive: boolean): React.CSSProperties => ({
    display: 'inline-block',
    width: '8px',
    height: '8px',
    borderRadius: '50%',
    marginRight: '6px',
    backgroundColor: isActive ? '#2ecc71' : '#e74c3c'
  })

  const sectionStyle: React.CSSProperties = {
    borderTop: '1px solid rgba(255, 255, 255, 0.3)',
    paddingTop: '8px',
    marginTop: '8px'
  }

  return (
    <div className={className} style={containerStyle}>
      <div style={{ fontWeight: 'bold', marginBottom: '8px', fontSize: '13px' }}>
        🔧 音频系统状态
      </div>

      {/* 系统状态 */}
      <div style={statusItemStyle}>
        <span>
          <span style={indicatorStyle(status.isInitialized)}></span>
          初始化
        </span>
        <span>{status.isInitialized ? '✅' : '❌'}</span>
      </div>

      <div style={statusItemStyle}>
        <span>
          <span style={indicatorStyle(status.isPlaying)}></span>
          播放中
        </span>
        <span>{status.isPlaying ? '🎵' : '⏸️'}</span>
      </div>

      <div style={statusItemStyle}>
        <span>
          <span style={indicatorStyle(!!status.currentMusic)}></span>
          当前音乐
        </span>
        <span>{status.currentMusic || '无'}</span>
      </div>

      <div style={statusItemStyle}>
        <span>音频上下文</span>
        <span>{status.audioContextState || '未知'}</span>
      </div>

      <div style={statusItemStyle}>
        <span>已加载音频</span>
        <span>{status.loadedAudios}</span>
      </div>

      {/* 设置状态 */}
      <div style={sectionStyle}>
        <div style={{ fontWeight: 'bold', marginBottom: '6px', fontSize: '11px' }}>
          设置
        </div>

        <div style={statusItemStyle}>
          <span>
            <span style={indicatorStyle(settings.musicEnabled)}></span>
            音乐启用
          </span>
          <span>{settings.musicEnabled ? '开' : '关'}</span>
        </div>

        <div style={statusItemStyle}>
          <span>
            <span style={indicatorStyle(settings.effectsEnabled)}></span>
            音效启用
          </span>
          <span>{settings.effectsEnabled ? '开' : '关'}</span>
        </div>

        <div style={statusItemStyle}>
          <span>主音量</span>
          <span>{Math.round(settings.masterVolume * 100)}%</span>
        </div>

        <div style={statusItemStyle}>
          <span>音乐音量</span>
          <span>{Math.round(settings.musicVolume * 100)}%</span>
        </div>

        <div style={statusItemStyle}>
          <span>音效音量</span>
          <span>{Math.round(settings.effectsVolume * 100)}%</span>
        </div>
      </div>

      {/* 调试信息 */}
      <div style={sectionStyle}>
        <div style={{ fontWeight: 'bold', marginBottom: '6px', fontSize: '11px' }}>
          调试信息
        </div>

        <div style={{ fontSize: '10px', color: '#bdc3c7' }}>
          <div>User Agent: {navigator.userAgent.split(' ').pop()}</div>
          <div>支持Web Audio: {window.AudioContext ? '是' : '否'}</div>
          <div>当前时间: {new Date().toLocaleTimeString()}</div>
        </div>
      </div>

      {/* 操作提示 */}
      <div style={sectionStyle}>
        <div style={{ fontSize: '10px', color: '#95a5a6', fontStyle: 'italic' }}>
          💡 此面板仅在开发环境显示
        </div>
      </div>
    </div>
  )
}

export default AudioStatusIndicator 