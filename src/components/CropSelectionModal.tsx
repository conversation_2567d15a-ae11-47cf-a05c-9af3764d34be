import React, { useState, useEffect } from 'react'
import { CropType, CropQuality } from '../types/crop'
import { CropPreview } from './CropVisualizer'
import { CROP_DETECTION_CONFIGS, SelfDisciplineType } from '../data/cropSpecifications'

interface CropSelectionModalProps {
  isOpen: boolean
  onClose: () => void
  onCropSelect: (cropType: CropType) => void
  unlockedCropTypes: CropType[]
  gridPosition: { x: number, y: number }
  userLevel?: number
  userExperience?: number
}

interface CropInfo {
  type: CropType
  name: string
  description: string
  behaviorType: SelfDisciplineType
  difficulty: string
  unlockLevel: number
  isUnlocked: boolean
  previewQuality: CropQuality
}

/**
 * 作物选择模态框组件
 * 允许用户选择要种植的作物类型
 */
export const CropSelectionModal: React.FC<CropSelectionModalProps> = ({
  isOpen,
  onClose,
  onCropSelect,
  unlockedCropTypes,
  gridPosition,
  userLevel = 1,
  userExperience = 0
}) => {
  const [selectedCrop, setSelectedCrop] = useState<CropType | null>(null)
  const [cropInfos, setCropInfos] = useState<CropInfo[]>([])

  // 作物基础信息
  const cropBaseInfo: Record<CropType, { name: string; description: string }> = {
    [CropType.KNOWLEDGE_FLOWER]: {
      name: '知识花',
      description: '通过学习活动茁壮成长的花朵，象征智慧的积累。学习时间越长，花朵越美丽。'
    },
    [CropType.STRENGTH_TREE]: {
      name: '力量树',
      description: '需要体力锻炼来生长的强壮大树。运动时间为它提供养分，让它变得更加粗壮。'
    },
    [CropType.TIME_VEGGIE]: {
      name: '时间菜',
      description: '时间管理专家的最爱！通过番茄工作法等时间管理技巧获得成长。'
    },
    [CropType.MEDITATION_LOTUS]: {
      name: '冥想莲',
      description: '在宁静的冥想中绽放的莲花。需要专注的冥想练习来达到完美的平衡。'
    },
    [CropType.FOCUS_FLOWER]: {
      name: '专注花',
      description: '深度专注工作时才会盛开的稀有花朵。要求极高的专注度和持续时间。'
    },
    [CropType.READING_VINE]: {
      name: '读书藤',
      description: '在书香中蔓延生长的藤蔓。阅读时间越多，藤蔓攀爬得越高，结出智慧果实。'
    },
    [CropType.SOCIAL_FRUIT]: {
      name: '社交果',
      description: '在社交互动中成熟的甜美果实。需要真实的社交活动来获得成长。'
    }
  }

  // 行为类型的中文名称
  const behaviorTypeNames: Record<SelfDisciplineType, string> = {
    [SelfDisciplineType.LEARNING]: '学习活动',
    [SelfDisciplineType.EXERCISE]: '体力锻炼',
    [SelfDisciplineType.TIME_MANAGEMENT]: '时间管理',
    [SelfDisciplineType.MEDITATION]: '冥想练习',
    [SelfDisciplineType.DEEP_FOCUS]: '深度专注',
    [SelfDisciplineType.READING]: '阅读习惯',
    [SelfDisciplineType.SOCIAL_INTERACTION]: '社交互动'
  }

  // 难度的中文名称
  const difficultyNames: Record<string, string> = {
    'easy': '简单',
    'medium': '中等',
    'hard': '困难',
    'expert': '专家'
  }

  // 根据难度获取预览品质
  const getPreviewQuality = (difficulty: string): CropQuality => {
    switch (difficulty) {
      case 'easy': return CropQuality.COMMON
      case 'medium': return CropQuality.UNCOMMON
      case 'hard': return CropQuality.RARE
      case 'expert': return CropQuality.EPIC
      default: return CropQuality.COMMON
    }
  }

  // 初始化作物信息
  useEffect(() => {
    const infos: CropInfo[] = Object.values(CropType).map(cropType => {
      const config = CROP_DETECTION_CONFIGS[cropType]
      const baseInfo = cropBaseInfo[cropType]
      const isUnlocked = unlockedCropTypes.includes(cropType) || userLevel >= config.balanceFactors.unlockLevel

      return {
        type: cropType,
        name: baseInfo.name,
        description: baseInfo.description,
        behaviorType: config.behaviorType,
        difficulty: config.balanceFactors.difficulty,
        unlockLevel: config.balanceFactors.unlockLevel,
        isUnlocked,
        previewQuality: getPreviewQuality(config.balanceFactors.difficulty)
      }
    })

    // 按解锁状态和难度排序
    infos.sort((a, b) => {
      if (a.isUnlocked !== b.isUnlocked) {
        return a.isUnlocked ? -1 : 1
      }
      return a.unlockLevel - b.unlockLevel
    })

    setCropInfos(infos)
  }, [unlockedCropTypes, userLevel])

  // 处理作物选择
  const handleCropSelect = (cropType: CropType) => {
    setSelectedCrop(cropType)
  }

  // 确认种植
  const handlePlantConfirm = () => {
    if (selectedCrop) {
      onCropSelect(selectedCrop)
      onClose()
      setSelectedCrop(null)
    }
  }

  // 关闭模态框
  const handleClose = () => {
    onClose()
    setSelectedCrop(null)
  }

  if (!isOpen) return null

  const selectedCropInfo = cropInfos.find(info => info.type === selectedCrop)

  return (
    <div className="crop-selection-modal-overlay" onClick={handleClose} style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 1000
    }}>
      <div 
        className="crop-selection-modal"
        onClick={(e) => e.stopPropagation()}
        style={{
          backgroundColor: 'rgba(20, 25, 40, 0.95)',
          border: '2px solid rgba(255, 255, 255, 0.2)',
          borderRadius: '16px',
          padding: '24px',
          maxWidth: '800px',
          maxHeight: '90vh',
          width: '90%',
          overflowY: 'auto',
          backdropFilter: 'blur(10px)'
        }}
      >
        {/* 模态框标题 */}
        <div className="modal-header" style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '24px',
          borderBottom: '1px solid rgba(255, 255, 255, 0.1)',
          paddingBottom: '16px'
        }}>
          <h2 style={{
            color: 'white',
            margin: 0,
            fontSize: '24px',
            fontWeight: 'bold'
          }}>
            选择作物类型
          </h2>
          <p style={{
            color: 'rgba(255, 255, 255, 0.7)',
            margin: 0,
            fontSize: '14px'
          }}>
            位置: ({gridPosition.x}, {gridPosition.y})
          </p>
          <button
            onClick={handleClose}
            style={{
              background: 'none',
              border: 'none',
              color: 'rgba(255, 255, 255, 0.7)',
              fontSize: '24px',
              cursor: 'pointer',
              padding: '4px'
            }}
          >
            ×
          </button>
        </div>

        {/* 作物网格 */}
        <div className="crop-grid" style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
          gap: '16px',
          marginBottom: '24px'
        }}>
          {cropInfos.map((cropInfo) => (
            <div
              key={cropInfo.type}
              className={`crop-card ${selectedCrop === cropInfo.type ? 'selected' : ''} ${!cropInfo.isUnlocked ? 'locked' : ''}`}
              onClick={() => cropInfo.isUnlocked && handleCropSelect(cropInfo.type)}
              style={{
                border: `2px solid ${selectedCrop === cropInfo.type ? '#4CAF50' : 'rgba(255, 255, 255, 0.2)'}`,
                borderRadius: '12px',
                padding: '16px',
                backgroundColor: cropInfo.isUnlocked ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.3)',
                cursor: cropInfo.isUnlocked ? 'pointer' : 'not-allowed',
                transition: 'all 0.3s ease',
                position: 'relative',
                filter: cropInfo.isUnlocked ? 'none' : 'grayscale(1) brightness(0.5)'
              }}
            >
              {/* 锁定图标 */}
              {!cropInfo.isUnlocked && (
                <div style={{
                  position: 'absolute',
                  top: '8px',
                  right: '8px',
                  color: 'rgba(255, 255, 255, 0.6)',
                  fontSize: '16px'
                }}>
                  🔒
                </div>
              )}

              {/* 作物预览 */}
              <div style={{ display: 'flex', justifyContent: 'center', marginBottom: '12px' }}>
                <CropPreview
                  cropType={cropInfo.type}
                  quality={cropInfo.previewQuality}
                  size="medium"
                  showLabel={false}
                />
              </div>

              {/* 作物信息 */}
              <div style={{ textAlign: 'center' }}>
                <h3 style={{
                  color: 'white',
                  margin: '0 0 8px 0',
                  fontSize: '18px',
                  fontWeight: 'bold'
                }}>
                  {cropInfo.name}
                </h3>
                
                <div style={{
                  color: 'rgba(255, 255, 255, 0.8)',
                  fontSize: '12px',
                  marginBottom: '8px'
                }}>
                  {behaviorTypeNames[cropInfo.behaviorType]}
                </div>

                <div style={{
                  display: 'flex',
                  justifyContent: 'center',
                  gap: '8px',
                  marginBottom: '8px'
                }}>
                  <span style={{
                    background: getDifficultyColor(cropInfo.difficulty),
                    color: 'white',
                    padding: '2px 8px',
                    borderRadius: '12px',
                    fontSize: '10px'
                  }}>
                    {difficultyNames[cropInfo.difficulty]}
                  </span>
                  
                  {!cropInfo.isUnlocked && (
                    <span style={{
                      background: 'rgba(255, 193, 7, 0.8)',
                      color: 'white',
                      padding: '2px 8px',
                      borderRadius: '12px',
                      fontSize: '10px'
                    }}>
                      Lv.{cropInfo.unlockLevel}
                    </span>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* 选中作物的详细信息 */}
        {selectedCropInfo && (
          <div className="selected-crop-details" style={{
            backgroundColor: 'rgba(255, 255, 255, 0.1)',
            borderRadius: '12px',
            padding: '20px',
            marginBottom: '24px'
          }}>
            <h3 style={{
              color: 'white',
              margin: '0 0 12px 0',
              fontSize: '20px'
            }}>
              {selectedCropInfo.name}
            </h3>
            
            <p style={{
              color: 'rgba(255, 255, 255, 0.8)',
              lineHeight: '1.5',
              margin: '0 0 16px 0'
            }}>
              {selectedCropInfo.description}
            </p>

            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))',
              gap: '12px',
              fontSize: '14px'
            }}>
              <div>
                <strong style={{ color: 'white' }}>行为类型:</strong>
                <br />
                <span style={{ color: 'rgba(255, 255, 255, 0.8)' }}>
                  {behaviorTypeNames[selectedCropInfo.behaviorType]}
                </span>
              </div>
              
              <div>
                <strong style={{ color: 'white' }}>难度等级:</strong>
                <br />
                <span style={{ color: 'rgba(255, 255, 255, 0.8)' }}>
                  {difficultyNames[selectedCropInfo.difficulty]}
                </span>
              </div>
              
              <div>
                <strong style={{ color: 'white' }}>解锁等级:</strong>
                <br />
                <span style={{ color: 'rgba(255, 255, 255, 0.8)' }}>
                  Lv.{selectedCropInfo.unlockLevel}
                </span>
              </div>
            </div>
          </div>
        )}

        {/* 操作按钮 */}
        <div className="modal-actions" style={{
          display: 'flex',
          justifyContent: 'flex-end',
          gap: '12px'
        }}>
          <button
            onClick={handleClose}
            style={{
              padding: '12px 24px',
              borderRadius: '8px',
              border: '1px solid rgba(255, 255, 255, 0.3)',
              backgroundColor: 'transparent',
              color: 'rgba(255, 255, 255, 0.8)',
              cursor: 'pointer',
              fontSize: '14px',
              transition: 'all 0.2s ease'
            }}
          >
            取消
          </button>
          
          <button
            onClick={handlePlantConfirm}
            disabled={!selectedCrop}
            style={{
              padding: '12px 24px',
              borderRadius: '8px',
              border: 'none',
              backgroundColor: selectedCrop ? '#4CAF50' : 'rgba(76, 175, 80, 0.3)',
              color: 'white',
              cursor: selectedCrop ? 'pointer' : 'not-allowed',
              fontSize: '14px',
              fontWeight: 'bold',
              transition: 'all 0.2s ease'
            }}
          >
            种植 {selectedCropInfo?.name || ''}
          </button>
        </div>
      </div>
    </div>
  )
}

// 获取难度颜色
function getDifficultyColor(difficulty: string): string {
  switch (difficulty) {
    case 'easy': return 'rgba(76, 175, 80, 0.8)'    // 绿色
    case 'medium': return 'rgba(255, 193, 7, 0.8)'  // 黄色
    case 'hard': return 'rgba(255, 152, 0, 0.8)'    // 橙色
    case 'expert': return 'rgba(244, 67, 54, 0.8)'  // 红色
    default: return 'rgba(158, 158, 158, 0.8)'      // 灰色
  }
} 