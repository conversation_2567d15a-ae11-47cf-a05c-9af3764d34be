import React, { useState, useEffect } from 'react'

interface ApplicationMonitorProps {
  className?: string
}

interface MonitorData {
  timestamp: number
  memory: number
  performance: number
  errors: number
}

export const ApplicationMonitor: React.FC<ApplicationMonitorProps> = ({ 
  className = '' 
}) => {
  const [isVisible, setIsVisible] = useState(false)
  const [monitorData, setMonitorData] = useState<MonitorData>({
    timestamp: Date.now(),
    memory: 0,
    performance: 100,
    errors: 0
  })

  useEffect(() => {
    // 模拟监控数据更新
    const interval = setInterval(() => {
      setMonitorData({
        timestamp: Date.now(),
        memory: Math.random() * 100,
        performance: 90 + Math.random() * 10,
        errors: Math.floor(Math.random() * 5)
      })
    }, 5000)

    return () => clearInterval(interval)
  }, [])

  if (!isVisible) {
    return (
      <button
        onClick={() => setIsVisible(true)}
        className={`monitor-toggle ${className}`}
        style={{
          position: 'fixed',
          top: '10px',
          right: '10px',
          zIndex: 9999,
          background: 'rgba(0, 0, 0, 0.7)',
          color: 'white',
          border: 'none',
          borderRadius: '4px',
          padding: '4px 8px',
          fontSize: '12px',
          cursor: 'pointer'
        }}
      >
        📊 监控
      </button>
    )
  }

  return (
    <div className={`application-monitor ${className}`} style={{
      position: 'fixed',
      top: '10px',
      right: '10px',
      zIndex: 9999,
      background: 'rgba(0, 0, 0, 0.8)',
      color: 'white',
      padding: '10px',
      borderRadius: '8px',
      fontSize: '12px',
      minWidth: '200px',
      fontFamily: 'monospace'
    }}>
      <div style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        marginBottom: '8px'
      }}>
        <span>📊 应用监控</span>
        <button
          onClick={() => setIsVisible(false)}
          style={{
            background: 'none',
            border: 'none',
            color: 'white',
            cursor: 'pointer',
            fontSize: '12px'
          }}
        >
          ✕
        </button>
      </div>
      
      <div style={{ marginBottom: '4px' }}>
        内存使用: {monitorData.memory.toFixed(1)}%
      </div>
      
      <div style={{ marginBottom: '4px' }}>
        性能评分: {monitorData.performance.toFixed(0)}%
      </div>
      
      <div style={{ marginBottom: '4px' }}>
        错误计数: {monitorData.errors}
      </div>
      
      <div style={{ fontSize: '10px', opacity: 0.7 }}>
        更新时间: {new Date(monitorData.timestamp).toLocaleTimeString()}
      </div>
    </div>
  )
}

export default ApplicationMonitor 