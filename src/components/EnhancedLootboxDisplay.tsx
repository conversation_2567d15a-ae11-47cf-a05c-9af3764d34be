import React, { useState, useEffect } from 'react'
import { LootboxType, RARITY_COLORS } from '../types/lootbox'
import { LOOTBOX_CONFIGS } from '../data/lootboxConfigs'
import { RARITY_NAMES, CURRENCY_NAMES } from '../utils/lootboxGenerator'

interface EnhancedLootboxDisplayProps {
  selectedLootbox: LootboxType
  onLootboxSelect: (lootboxType: LootboxType) => void
  userCurrency: Record<string, number>
  className?: string
}

export const EnhancedLootboxDisplay: React.FC<EnhancedLootboxDisplayProps> = ({
  selectedLootbox,
  onLootboxSelect,
  userCurrency,
  className = ''
}) => {
  const [hoveredCard, setHoveredCard] = useState<LootboxType | null>(null)
  const [windowWidth, setWindowWidth] = useState(window.innerWidth)

  useEffect(() => {
    const handleResize = () => setWindowWidth(window.innerWidth)
    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  // 按类别分组盲盒
  const groupedLootboxes = Object.values(LootboxType).reduce((groups, lootboxType) => {
    const config = LOOTBOX_CONFIGS[lootboxType]
    const category = config.category || 'mixed'
    
    if (!groups[category]) {
      groups[category] = []
    }
    groups[category].push(lootboxType)
    return groups
  }, {} as Record<string, LootboxType[]>)

  const categoryInfo = {
    agricultural: { name: '🌾 农业期货', color: '#22c55e', description: '玉米、小麦、大豆等农产品期货' },
    industrial: { name: '🏭 工业金属', color: '#3b82f6', description: '铜、铝、锌等工业金属期货' },
    energy: { name: '⚡ 能源化工', color: '#f59e0b', description: '原油、天然气等能源期货' },
    mixed: { name: '🎁 综合宝盒', color: '#8b5cf6', description: '包含多种类型的综合盲盒' }
  }

  return (
    <div className={`enhanced-lootbox-display ${className}`} style={{
      padding: windowWidth >= 768 ? '20px' : '16px',
      maxWidth: '1400px',
      margin: '0 auto'
    }}>
      {/* 标题区域 */}
      <div style={{
        textAlign: 'center',
        marginBottom: '40px',
        padding: '32px',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        borderRadius: '24px',
        color: 'white',
        position: 'relative',
        overflow: 'hidden'
      }}>
        <h2 style={{
          fontSize: '2.2rem',
          fontWeight: '800',
          margin: '0 0 12px 0',
          textShadow: '0 2px 4px rgba(0, 0, 0, 0.3)'
        }}>🎰 期货盲盒精品商店</h2>
        <p style={{
          fontSize: '1.1rem',
          margin: '0',
          opacity: '0.9'
        }}>精选优质期货品种，开启您的投资传奇</p>
      </div>

      {/* 分类展示 */}
      {Object.entries(groupedLootboxes).map(([category, lootboxes]) => {
        const info = categoryInfo[category as keyof typeof categoryInfo]
        if (!info || lootboxes.length === 0) return null

        return (
          <div key={category} style={{ marginBottom: '48px' }}>
            <div style={{
              marginBottom: '24px',
              padding: '20px 24px',
              background: 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)',
              borderRadius: '16px',
              borderLeft: `6px solid ${info.color}`
            }}>
              <h3 style={{
                fontSize: '1.5rem',
                fontWeight: '700',
                margin: '0 0 8px 0',
                color: '#1e293b'
              }}>{info.name}</h3>
              <p style={{
                margin: '0',
                color: '#64748b',
                fontSize: '1rem'
              }}>{info.description}</p>
            </div>

            <div style={{
              display: 'grid',
              gridTemplateColumns: windowWidth >= 1200 ? 'repeat(3, 1fr)' :
                                 windowWidth >= 900 ? 'repeat(2, 1fr)' :
                                 windowWidth >= 600 ? 'repeat(2, 1fr)' :
                                 '1fr',
              gap: windowWidth >= 768 ? '24px' : '16px',
              maxWidth: '1200px',
              margin: '0 auto',
              justifyItems: 'stretch'
            }}>
              {lootboxes.map((lootboxType, index) => {
                const config = LOOTBOX_CONFIGS[lootboxType]
                const isSelected = selectedLootbox === lootboxType
                const isAffordable = userCurrency[config.price.currency] >= config.price.amount

                return (
                  <div
                    key={lootboxType}
                    onClick={() => onLootboxSelect(lootboxType)}
                    onMouseEnter={() => setHoveredCard(lootboxType)}
                    onMouseLeave={() => setHoveredCard(null)}
                    style={{
                      background: 'linear-gradient(145deg, #ffffff, #f8fafc)',
                      border: isSelected ? `3px solid ${info.color}` : '3px solid transparent',
                      borderRadius: '24px',
                      padding: '0',
                      cursor: isAffordable ? 'pointer' : 'not-allowed',
                      transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
                      position: 'relative',
                      overflow: 'hidden',
                      minHeight: windowWidth >= 768 ? '240px' : '220px',
                      width: '100%',
                      maxWidth: '400px',
                      margin: '0 auto',
                      opacity: isAffordable ? 1 : 0.6,
                      filter: isAffordable ? 'none' : 'grayscale(0.4)',
                      transform: isSelected ? 'translateY(-4px) scale(1.02)' : hoveredCard === lootboxType ? 'translateY(-8px) scale(1.02)' : 'translateY(0)',
                      boxShadow: isSelected 
                        ? `0 12px 32px ${info.color}40, 0 4px 8px ${info.color}25`
                        : hoveredCard === lootboxType
                        ? `0 20px 40px rgba(0, 0, 0, 0.15), 0 8px 16px ${info.color}33`
                        : '0 4px 20px rgba(0, 0, 0, 0.08), 0 1px 3px rgba(0, 0, 0, 0.1)'
                    }}
                  >
                    {/* 品质指示器 */}
                    <div style={{
                      position: 'absolute',
                      top: '16px',
                      right: '16px',
                      width: '20px',
                      height: '20px',
                      borderRadius: '50%',
                      zIndex: 3,
                      boxShadow: '0 2px 8px rgba(0, 0, 0, 0.3)',
                      backgroundColor: RARITY_COLORS[config.guaranteedRarity || 'gray']
                    }} />

                    {/* 类别徽章 */}
                    <div style={{
                      position: 'absolute',
                      top: '16px',
                      left: '16px',
                      color: 'white',
                      padding: '6px 12px',
                      borderRadius: '16px',
                      fontSize: '0.8rem',
                      fontWeight: '700',
                      zIndex: 3,
                      textShadow: '0 1px 2px rgba(0, 0, 0, 0.3)',
                      backgroundColor: info.color
                    }}>
                      {info.name.split(' ')[0]}
                    </div>

                    {/* 主要内容 */}
                    <div style={{
                      position: 'relative',
                      zIndex: 2,
                      padding: '24px',
                      height: '100%',
                      display: 'flex',
                      flexDirection: 'column'
                    }}>
                      <div style={{
                        textAlign: 'center',
                        marginBottom: '20px'
                      }}>
                        <div style={{
                          fontSize: '4rem',
                          filter: 'drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1))',
                          transition: 'transform 0.3s ease',
                          transform: hoveredCard === lootboxType ? 'scale(1.1) rotate(5deg)' : 'scale(1)'
                        }}>{config.icon}</div>
                      </div>

                      <div style={{
                        flex: 1,
                        display: 'flex',
                        flexDirection: 'column'
                      }}>
                        <h4 style={{
                          fontSize: '1.4rem',
                          fontWeight: '800',
                          margin: '0 0 12px 0',
                          color: '#1e293b',
                          textAlign: 'center'
                        }}>{config.name}</h4>
                        
                        <p style={{
                          fontSize: '0.95rem',
                          color: '#64748b',
                          lineHeight: '1.5',
                          margin: '0 0 20px 0',
                          textAlign: 'center',
                          flex: 1
                        }}>{config.description}</p>
                        
                        <div style={{ marginBottom: '16px' }}>
                          <div style={{
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            gap: '8px',
                            padding: '12px 20px',
                            background: 'linear-gradient(135deg, #f8fafc, #e2e8f0)',
                            border: `2px solid ${info.color}`,
                            borderRadius: '20px',
                            fontWeight: '700'
                          }}>
                            <span style={{ fontSize: '1.2rem' }}>💰</span>
                            <span style={{ fontSize: '1.3rem', color: '#059669' }}>{config.price.amount}</span>
                            <span style={{ fontSize: '0.9rem', color: '#64748b' }}>{CURRENCY_NAMES[config.price.currency]}</span>
                          </div>
                        </div>

                        {/* 保底提示 */}
                        {config.guaranteedRarity && config.guaranteedRarity !== 'gray' && (
                          <div style={{
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            gap: '6px',
                            padding: '8px 16px',
                            background: 'linear-gradient(135deg, #fbbf24, #f59e0b)',
                            color: 'white',
                            borderRadius: '16px',
                            fontSize: '0.85rem',
                            fontWeight: '700',
                            textShadow: '0 1px 2px rgba(0, 0, 0, 0.2)'
                          }}>
                            <span>✨</span>
                            <span>保底 {RARITY_NAMES[config.guaranteedRarity]}</span>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* 选中指示器 */}
                    {isSelected && (
                      <div style={{
                        position: 'absolute',
                        top: '50%',
                        left: '50%',
                        transform: 'translate(-50%, -50%)',
                        zIndex: 4,
                        pointerEvents: 'none'
                      }}>
                        <div style={{
                          width: '60px',
                          height: '60px',
                          border: `4px solid ${info.color}`,
                          borderRadius: '50%',
                          position: 'relative',
                          animation: 'selection-pulse 1.5s ease-in-out infinite'
                        }}>
                          <div style={{
                            position: 'absolute',
                            top: '50%',
                            left: '50%',
                            transform: 'translate(-50%, -50%)',
                            fontSize: '1.5rem',
                            fontWeight: 'bold',
                            color: 'white',
                            background: info.color,
                            width: '40px',
                            height: '40px',
                            borderRadius: '50%',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.3)'
                          }}>✓</div>
                        </div>
                      </div>
                    )}
                  </div>
                )
              })}
            </div>
          </div>
        )
      })}
    </div>
  )
}

export default EnhancedLootboxDisplay
