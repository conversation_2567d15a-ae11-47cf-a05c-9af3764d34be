// 隐私设置界面组件

import React, { useState, useEffect } from 'react'
import { Card } from '../ui/Card'
import { Button } from '../ui/Button'
import { PrivacyManager, PrivacySettings as PrivacySettingsType, PrivacyReport } from '../../services/PrivacyManager'

// 简单的开关组件
interface SwitchProps {
  checked: boolean
  onChange: (checked: boolean) => void
  disabled?: boolean
}

const Switch: React.FC<SwitchProps> = ({ checked, onChange, disabled = false }) => (
  <button
    onClick={() => !disabled && onChange(!checked)}
    disabled={disabled}
    className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
      checked ? 'bg-blue-600' : 'bg-gray-200'
    } ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
  >
    <span
      className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
        checked ? 'translate-x-6' : 'translate-x-1'
      }`}
    />
  </button>
)

// 简单的标签组件
interface BadgeProps {
  children: React.ReactNode
  variant: 'green' | 'blue' | 'yellow' | 'red'
  className?: string
}

const Badge: React.FC<BadgeProps> = ({ children, variant, className = '' }) => {
  const colors = {
    green: 'bg-green-100 text-green-800',
    blue: 'bg-blue-100 text-blue-800',
    yellow: 'bg-yellow-100 text-yellow-800',
    red: 'bg-red-100 text-red-800'
  }
  
  return (
    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${colors[variant]} ${className}`}>
      {children}
    </span>
  )
}

interface PrivacySettingsProps {
  privacyManager: PrivacyManager
  onSettingsChange?: (settings: PrivacySettingsType) => void
  className?: string
}

interface SettingItem {
  key: keyof PrivacySettingsType
  label: string
  description: string
  category: 'essential' | 'functional' | 'analytics' | 'marketing'
  impact: 'none' | 'low' | 'medium' | 'high'
  required?: boolean
}

const SETTING_ITEMS: SettingItem[] = [
  {
    key: 'dataCollection',
    label: '数据收集',
    description: '允许应用收集和存储使用数据以改善体验',
    category: 'functional',
    impact: 'medium'
  },
  {
    key: 'analytics',
    label: '分析统计',
    description: '收集匿名使用统计以帮助改进应用功能',
    category: 'analytics',
    impact: 'low'
  },
  {
    key: 'poseTracking',
    label: '姿态追踪',
    description: '使用摄像头进行姿态检测和专注度分析',
    category: 'functional',
    impact: 'high'
  },
  {
    key: 'localProcessingOnly',
    label: '仅本地处理',
    description: '确保所有数据处理都在您的设备上进行',
    category: 'essential',
    impact: 'none',
    required: true
  },
  {
    key: 'shareUsageStats',
    label: '共享使用统计',
    description: '与开发者共享匿名化的使用统计信息',
    category: 'marketing',
    impact: 'low'
  }
]

const CATEGORY_LABELS = {
  essential: '必需功能',
  functional: '功能性',
  analytics: '分析统计',
  marketing: '营销推广'
}

const IMPACT_COLORS = {
  none: 'green',
  low: 'blue',
  medium: 'yellow',
  high: 'red'
} as const

export const PrivacySettings: React.FC<PrivacySettingsProps> = ({
  privacyManager,
  onSettingsChange,
  className = ''
}) => {
  const [settings, setSettings] = useState<PrivacySettingsType>()
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [report, setReport] = useState<PrivacyReport | null>(null)
  const [showAdvanced, setShowAdvanced] = useState(false)
  const [showReport, setShowReport] = useState(false)

  // 加载当前设置
  useEffect(() => {
    loadSettings()
  }, [privacyManager])

  const loadSettings = async () => {
    try {
      const currentSettings = privacyManager.getPrivacySettings()
      setSettings(currentSettings)
    } catch (error) {
      console.error('Failed to load privacy settings:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSettingChange = async (key: keyof PrivacySettingsType, value: any) => {
    if (!settings) return

    const newSettings = { ...settings, [key]: value }
    setSettings(newSettings)

    // 立即保存设置
    try {
      setSaving(true)
      await privacyManager.updatePrivacySettings({ [key]: value })
      onSettingsChange?.(newSettings)
    } catch (error) {
      console.error('Failed to update privacy setting:', error)
      // 回滚设置
      setSettings(settings)
    } finally {
      setSaving(false)
    }
  }

  const handleAutoDeleteChange = async (field: 'enabled' | 'retentionDays', value: boolean | number) => {
    if (!settings) return

    const newAutoDelete = { ...settings.autoDelete, [field]: value }
    const newSettings = { ...settings, autoDelete: newAutoDelete }
    setSettings(newSettings)

    try {
      setSaving(true)
      await privacyManager.updatePrivacySettings({ autoDelete: newAutoDelete })
      onSettingsChange?.(newSettings)
    } catch (error) {
      console.error('Failed to update auto-delete setting:', error)
      setSettings(settings)
    } finally {
      setSaving(false)
    }
  }

  const generateReport = async () => {
    try {
      setLoading(true)
      const privacyReport = await privacyManager.generatePrivacyReport()
      setReport(privacyReport)
      setShowReport(true)
    } catch (error) {
      console.error('Failed to generate privacy report:', error)
    } finally {
      setLoading(false)
    }
  }

  const deleteAllData = async () => {
    if (!confirm('确定要删除所有用户数据吗？此操作不可恢复。')) {
      return
    }

    try {
      setLoading(true)
      await privacyManager.deleteAllUserData()
      alert('所有用户数据已成功删除')
      // 重新加载设置
      await loadSettings()
    } catch (error) {
      console.error('Failed to delete user data:', error)
      alert('删除数据时发生错误')
    } finally {
      setLoading(false)
    }
  }

  const renderSettingItem = (item: SettingItem) => {
    if (!settings) return null

    const value = settings[item.key]
    const isBoolean = typeof value === 'boolean'

    return (
      <div key={item.key} className="flex items-center justify-between p-4 border-b border-gray-200">
        <div className="flex-1">
          <div className="flex items-center gap-2 mb-1">
            <h4 className="font-medium text-gray-900">{item.label}</h4>
            <Badge 
              variant={IMPACT_COLORS[item.impact]}
              className="text-xs"
            >
              影响: {item.impact === 'none' ? '无' : item.impact === 'low' ? '低' : item.impact === 'medium' ? '中' : '高'}
            </Badge>
            {item.required && (
              <Badge variant="red" className="text-xs">必需</Badge>
            )}
          </div>
          <p className="text-sm text-gray-600">{item.description}</p>
        </div>
        <div className="ml-4">
          {isBoolean ? (
            <Switch
              checked={value as boolean}
              onChange={(checked) => handleSettingChange(item.key, checked)}
              disabled={item.required || saving}
            />
          ) : (
            <span className="text-sm text-gray-500">配置中</span>
          )}
        </div>
      </div>
    )
  }

  const renderCategorySection = (category: string) => {
    const categoryItems = SETTING_ITEMS.filter(item => item.category === category)
    if (categoryItems.length === 0) return null

    return (
      <Card key={category} className="mb-4">
        <div className="p-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">
            {CATEGORY_LABELS[category as keyof typeof CATEGORY_LABELS]}
          </h3>
        </div>
        <div>
          {categoryItems.map(renderSettingItem)}
        </div>
      </Card>
    )
  }

  const renderAdvancedSettings = () => (
    <Card className="mb-4">
      <div className="p-4 border-b border-gray-200">
        <h3 className="text-lg font-semibold text-gray-900">高级设置</h3>
      </div>
      <div className="p-4">
        <div className="mb-4">
          <label className="flex items-center justify-between">
            <div>
              <h4 className="font-medium text-gray-900">自动删除数据</h4>
              <p className="text-sm text-gray-600">定期自动删除过期的用户数据</p>
            </div>
            <Switch
              checked={settings?.autoDelete.enabled || false}
              onChange={(checked) => handleAutoDeleteChange('enabled', checked)}
              disabled={saving}
            />
          </label>
        </div>
        
        {settings?.autoDelete.enabled && (
          <div className="ml-4">
            <label className="block">
              <span className="text-sm font-medium text-gray-700">数据保留天数</span>
              <select
                value={settings.autoDelete.retentionDays}
                onChange={(e) => handleAutoDeleteChange('retentionDays', Number(e.target.value))}
                disabled={saving}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              >
                <option value={7}>7天</option>
                <option value={30}>30天</option>
                <option value={90}>90天</option>
                <option value={365}>1年</option>
              </select>
            </label>
          </div>
        )}
      </div>
    </Card>
  )

  const renderPrivacyReport = () => {
    if (!report) return null

    return (
      <Card className="mb-4">
        <div className="p-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">隐私报告</h3>
        </div>
        <div className="p-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{report.dataCategories.length}</div>
              <div className="text-sm text-gray-600">数据分类</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{report.consentHistory.length}</div>
              <div className="text-sm text-gray-600">同意记录</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {Object.values(report.complianceStatus).filter(Boolean).length}/3
              </div>
              <div className="text-sm text-gray-600">合规标准</div>
            </div>
          </div>

          <div className="mb-4">
            <h4 className="font-medium text-gray-900 mb-2">合规状态</h4>
            <div className="flex gap-2">
              <Badge variant={report.complianceStatus.gdpr ? 'green' : 'red'}>
                GDPR: {report.complianceStatus.gdpr ? '合规' : '不合规'}
              </Badge>
              <Badge variant={report.complianceStatus.ccpa ? 'green' : 'red'}>
                CCPA: {report.complianceStatus.ccpa ? '合规' : '不合规'}
              </Badge>
              <Badge variant={report.complianceStatus.pipeda ? 'green' : 'red'}>
                PIPEDA: {report.complianceStatus.pipeda ? '合规' : '不合规'}
              </Badge>
            </div>
          </div>

          <div>
            <h4 className="font-medium text-gray-900 mb-2">数据保留情况</h4>
            <div className="space-y-2">
              {report.dataRetention.map((retention, index) => (
                <div key={index} className="flex justify-between items-center">
                  <span className="text-sm text-gray-700">{retention.category}</span>
                  <span className="text-sm text-gray-500">{retention.itemCount} 项</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </Card>
    )
  }

  if (loading && !settings) {
    return (
      <Card className={className}>
        <div className="p-8 text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">加载隐私设置...</p>
        </div>
      </Card>
    )
  }

  return (
    <div className={className}>
      <Card className="mb-4">
        <div className="p-4 border-b border-gray-200">
          <h2 className="text-xl font-bold text-gray-900">隐私与数据保护</h2>
          <p className="text-sm text-gray-600 mt-1">
            管理您的数据隐私设置和个人信息保护选项
          </p>
        </div>
        <div className="p-4">
          <div className="flex gap-2 mb-4">
            <Button
              onClick={() => setShowAdvanced(!showAdvanced)}
              variant="secondary"
              size="sm"
            >
              {showAdvanced ? '隐藏' : '显示'}高级设置
            </Button>
            <Button
              onClick={generateReport}
              variant="secondary"
              size="sm"
              disabled={loading}
            >
              生成隐私报告
            </Button>
            <Button
              onClick={deleteAllData}
              variant="error"
              size="sm"
              disabled={loading}
              className="ml-auto"
            >
              删除所有数据
            </Button>
          </div>
        </div>
      </Card>

      {/* 基础设置 */}
      {['essential', 'functional', 'analytics', 'marketing'].map(renderCategorySection)}

      {/* 高级设置 */}
      {showAdvanced && renderAdvancedSettings()}

      {/* 隐私报告 */}
      {showReport && renderPrivacyReport()}

      {/* 状态指示器 */}
      {saving && (
        <div className="fixed bottom-4 right-4 bg-blue-600 text-white px-4 py-2 rounded-lg shadow-lg">
          <div className="flex items-center gap-2">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            <span>保存中...</span>
          </div>
        </div>
      )}
    </div>
  )
} 