import React, { useState, useEffect } from 'react'
import { useGameStore } from '../stores/gameStore'
import { GameItem, QUALITY_CONFIGS } from '../types/enhanced-items'
import { SimpleItemCard } from './ItemCard'

interface SynthesisPanelProps {
  isOpen: boolean
  onClose: () => void
}

export const SynthesisPanel: React.FC<SynthesisPanelProps> = ({ isOpen, onClose }) => {
  const {
    synthesis,
    addMaterial,
    removeMaterial,
    clearMaterials,
    performSynthesis,
    canSynthesize,
    focusState
  } = useGameStore()

  const [isAnimating, setIsAnimating] = useState(false)
  const [showResult, setShowResult] = useState(false)

  // 计算成功率 (简化版，实际应该从SynthesisSystem获取)
  const calculateSuccessRate = () => {
    if (synthesis.selectedMaterials.length !== 2) return 0
    
    const [item1, item2] = synthesis.selectedMaterials
    if (item1.quality !== item2.quality) return 0
    
    const qualityConfig = QUALITY_CONFIGS[item1.quality]
    let baseRate = qualityConfig.synthesisSuccessRate
    
    // 专注时间加成
    const minutes = focusState.dailyFocusMinutes
    let focusBonus = 0
    if (minutes >= 120) focusBonus = 20
    else if (minutes >= 90) focusBonus = 15
    else if (minutes >= 60) focusBonus = 10
    else if (minutes >= 30) focusBonus = 5
    
    return Math.min(baseRate + focusBonus, 95)
  }

  const handleSynthesize = async () => {
    if (!canSynthesize()) return
    
    setIsAnimating(true)
    setShowResult(false)
    
    // 延迟执行合成以显示动画
    setTimeout(async () => {
      await performSynthesis()
      setIsAnimating(false)
      setShowResult(true)
      
      // 3秒后自动隐藏结果
      setTimeout(() => {
        setShowResult(false)
      }, 3000)
    }, 2000)
  }

  const getResultDisplay = () => {
    if (!synthesis.lastResult) return null
    
    const result = synthesis.lastResult
    
    return (
      <div 
        className={`
          p-4 rounded-lg border-2 text-center transition-all duration-500
          ${result.success 
            ? 'bg-green-50 border-green-200 text-green-800' 
            : 'bg-red-50 border-red-200 text-red-800'
          }
          ${showResult ? 'opacity-100 transform scale-100' : 'opacity-0 transform scale-95'}
        `}
      >
        <div className="text-lg font-bold mb-2">
          {result.success ? '🎉 合成成功！' : '💥 合成失败'}
        </div>
        
        <div className="text-sm mb-2">{result.message}</div>
        
        {result.success && result.resultItem && (
          <div className="flex items-center justify-center gap-2">
            <span>获得:</span>
            <div className="flex items-center gap-1">
              <span className="text-xl">{result.resultItem.icon}</span>
              <span className="font-semibold">{result.resultItem.name}</span>
              <span 
                className="px-2 py-1 rounded text-xs text-white"
                style={{ backgroundColor: QUALITY_CONFIGS[result.resultItem.quality].color }}
              >
                {QUALITY_CONFIGS[result.resultItem.quality].name}
              </span>
            </div>
          </div>
        )}
        
        {result.appliedBonuses.length > 0 && (
          <div className="text-xs text-gray-600 mt-2">
            <div>应用的加成:</div>
            {result.appliedBonuses.map((bonus, index) => (
              <div key={index}>• {bonus}</div>
            ))}
          </div>
        )}
      </div>
    )
  }

  const getCooldownDisplay = () => {
    const now = Date.now()
    if (now < synthesis.cooldownEndTime) {
      const remaining = Math.ceil((synthesis.cooldownEndTime - now) / 1000)
      return (
        <div className="text-sm text-orange-600 text-center">
          冷却中... {remaining}秒
        </div>
      )
    }
    return null
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-96 max-w-[90vw] max-h-[90vh] overflow-y-auto">
        {/* 标题栏 */}
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-bold text-gray-800">道具合成</h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 text-xl"
          >
            ×
          </button>
        </div>

        {/* 专注状态显示 */}
        <div className="bg-blue-50 rounded-lg p-3 mb-6">
          <div className="text-sm font-semibold text-blue-800 mb-1">专注状态</div>
          <div className="text-xs text-blue-600">
            今日专注: {focusState.dailyFocusMinutes} 分钟
          </div>
          <div className="text-xs text-blue-600">
            连续专注: {focusState.focusStreak} 天
          </div>
        </div>

        {/* 合成区域 */}
        <div className="mb-6">
          <div className="text-sm font-semibold text-gray-700 mb-3">合成材料</div>
          
          <div className="flex items-center justify-center gap-4 mb-4">
            {/* 材料槽1 */}
            <SimpleItemCard
              item={synthesis.selectedMaterials[0]}
              placeholder="材料1"
              onRemove={() => synthesis.selectedMaterials[0] && removeMaterial(synthesis.selectedMaterials[0].id)}
            />
            
            {/* 加号 */}
            <div className="text-2xl text-gray-400">+</div>
            
            {/* 材料槽2 */}
            <SimpleItemCard
              item={synthesis.selectedMaterials[1]}
              placeholder="材料2"
              onRemove={() => synthesis.selectedMaterials[1] && removeMaterial(synthesis.selectedMaterials[1].id)}
            />
            
            {/* 箭头 */}
            <div className="text-2xl text-gray-400">→</div>
            
            {/* 结果预览 */}
            <div className="w-20 h-20 border-2 border-dashed border-green-300 rounded-lg flex items-center justify-center bg-green-50">
              {synthesis.selectedMaterials.length === 2 && synthesis.selectedMaterials[0].quality === synthesis.selectedMaterials[1].quality ? (
                <div className="text-center">
                  <div className="text-xl">{synthesis.selectedMaterials[0].icon}</div>
                  <div 
                    className="text-xs px-1 py-0.5 rounded text-white mt-1"
                    style={{ 
                      backgroundColor: synthesis.selectedMaterials[0].quality === 'legendary' 
                        ? QUALITY_CONFIGS['legendary'].color 
                        : QUALITY_CONFIGS[
                            ['common', 'good', 'rare', 'epic', 'legendary']
                            [['common', 'good', 'rare', 'epic', 'legendary'].indexOf(synthesis.selectedMaterials[0].quality) + 1] || 'legendary'
                          ].color 
                    }}
                  >
                    ?
                  </div>
                </div>
              ) : (
                <div className="text-xs text-gray-500 text-center">结果预览</div>
              )}
            </div>
          </div>

          {/* 清空材料按钮 */}
          {synthesis.selectedMaterials.length > 0 && (
            <div className="text-center mb-4">
              <button
                onClick={clearMaterials}
                className="text-sm text-gray-500 hover:text-gray-700 underline"
              >
                清空材料
              </button>
            </div>
          )}
        </div>

        {/* 成功率显示 */}
        <div className="mb-6">
          <div className="bg-gray-50 rounded-lg p-3">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-semibold text-gray-700">合成成功率</span>
              <span className="text-lg font-bold text-blue-600">
                {calculateSuccessRate()}%
              </span>
            </div>
            
            {/* 成功率条 */}
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                style={{ width: `${calculateSuccessRate()}%` }}
              />
            </div>
            
            {/* 成功率说明 */}
            <div className="text-xs text-gray-600 mt-2">
              <div>基础成功率: {synthesis.selectedMaterials.length === 2 ? QUALITY_CONFIGS[synthesis.selectedMaterials[0].quality]?.synthesisSuccessRate || 0 : 0}%</div>
              {focusState.dailyFocusMinutes >= 30 && (
                <div className="text-blue-600">
                  • 专注时间加成: +{focusState.dailyFocusMinutes >= 120 ? 20 : focusState.dailyFocusMinutes >= 90 ? 15 : focusState.dailyFocusMinutes >= 60 ? 10 : 5}%
                </div>
              )}
              {focusState.focusStreak >= 3 && (
                <div className="text-green-600">
                  • 连续专注加成: +{focusState.focusStreak >= 30 ? 10 : focusState.focusStreak >= 14 ? 7 : focusState.focusStreak >= 7 ? 5 : 3}%
                </div>
              )}
            </div>
          </div>
        </div>

        {/* 合成按钮 */}
        <div className="mb-6">
          {getCooldownDisplay()}
          
          <button
            onClick={handleSynthesize}
            disabled={!canSynthesize() || isAnimating || Date.now() < synthesis.cooldownEndTime}
            className={`
              w-full py-3 rounded-lg font-semibold text-white transition-all duration-200
              ${canSynthesize() && Date.now() >= synthesis.cooldownEndTime && !isAnimating
                ? 'bg-blue-500 hover:bg-blue-600 shadow-lg hover:shadow-xl transform hover:scale-105' 
                : 'bg-gray-400 cursor-not-allowed'
              }
              ${isAnimating ? 'animate-pulse' : ''}
            `}
          >
            {isAnimating ? '🔥 合成中...' : synthesis.isProcessing ? '处理中...' : '开始合成'}
          </button>
        </div>

        {/* 合成动画 */}
        {isAnimating && (
          <div className="text-center mb-6">
            <div className="inline-flex items-center gap-2 text-orange-600">
              <div className="animate-spin text-2xl">⚗️</div>
              <span className="font-semibold">炼金炉正在运转...</span>
            </div>
            <div className="mt-2 text-sm text-gray-600">
              材料正在融合，请稍候...
            </div>
          </div>
        )}

        {/* 合成结果 */}
        {synthesis.lastResult && getResultDisplay()}

        {/* 使用说明 */}
        <div className="text-xs text-gray-500 mt-4 p-3 bg-gray-50 rounded">
          <div className="font-semibold mb-1">使用说明:</div>
          <div>• 将两个相同品质的同类道具放入材料槽</div>
          <div>• 专注时间越长，合成成功率越高</div>
          <div>• 连续专注天数可提供额外加成</div>
          <div>• 合成成功可获得更高品质的道具</div>
        </div>
      </div>
    </div>
  )
} 