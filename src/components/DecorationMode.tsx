import React, { useState, useEffect, useRef, useCallback } from 'react'
import { 
  DecorationItem, 
  PlacedDecoration, 
  DecorationType 
} from '../types/decoration'
import { DecorationSystem } from '../systems/DecorationSystem'
import './DecorationMode.css'

interface DecorationModeProps {
  decorationSystem: DecorationSystem
  farmWidth: number
  farmHeight: number
  gridSize: number
  isActive: boolean
  onExit: () => void
  onSelectionChange?: (decoration: PlacedDecoration | null) => void
}

interface GridPosition {
  x: number
  y: number
}

interface DragState {
  isDragging: boolean
  draggedItem: DecorationItem | null
  draggedDecoration: PlacedDecoration | null
  startPosition: GridPosition | null
  currentPosition: GridPosition | null
  dragType: 'new' | 'existing' | null
}

const DecorationMode: React.FC<DecorationModeProps> = ({
  decorationSystem,
  farmWidth,
  farmHeight,
  gridSize,
  isActive,
  onExit,
  onSelectionChange
}) => {
  const [selectedDecoration, setSelectedDecoration] = useState<PlacedDecoration | null>(null)
  const [placedDecorations, setPlacedDecorations] = useState<PlacedDecoration[]>([])
  const [ownedDecorations, setOwnedDecorations] = useState<{ item: DecorationItem, quantity: number }[]>([])
  const [dragState, setDragState] = useState<DragState>({
    isDragging: false,
    draggedItem: null,
    draggedDecoration: null,
    startPosition: null,
    currentPosition: null,
    dragType: null
  })
  const [showGrid, setShowGrid] = useState(true)
  const [previewPosition, setPreviewPosition] = useState<GridPosition | null>(null)
  const [totalBeauty, setTotalBeauty] = useState(0)

  const farmRef = useRef<HTMLDivElement>(null)

  // 加载装饰数据
  useEffect(() => {
    if (isActive) {
      loadDecorationData()
    }
  }, [isActive])

  // 计算美观度
  useEffect(() => {
    const { totalBeauty } = decorationSystem.calculateTotalEffects()
    setTotalBeauty(totalBeauty)
  }, [placedDecorations])

  const loadDecorationData = () => {
    const placed = decorationSystem.getPlacedDecorations()
    const owned = decorationSystem.getOwnedDecorations()
    
    setPlacedDecorations(placed.map(p => p.decoration))
    setOwnedDecorations(owned)
  }

  // 将像素坐标转换为网格坐标
  const pixelToGrid = useCallback((pixelX: number, pixelY: number): GridPosition => {
    return {
      x: Math.floor(pixelX / gridSize),
      y: Math.floor(pixelY / gridSize)
    }
  }, [gridSize])

  // 将网格坐标转换为像素坐标
  const gridToPixel = useCallback((gridX: number, gridY: number): GridPosition => {
    return {
      x: gridX * gridSize,
      y: gridY * gridSize
    }
  }, [gridSize])

  // 获取鼠标在农场内的坐标
  const getMousePosition = useCallback((event: React.MouseEvent): GridPosition | null => {
    if (!farmRef.current) return null
    
    const rect = farmRef.current.getBoundingClientRect()
    const pixelX = event.clientX - rect.left
    const pixelY = event.clientY - rect.top
    
    if (pixelX < 0 || pixelY < 0 || pixelX > farmWidth || pixelY > farmHeight) {
      return null
    }
    
    return pixelToGrid(pixelX, pixelY)
  }, [farmWidth, farmHeight, pixelToGrid])

  // 检查位置是否可放置
  const canPlaceAt = useCallback((item: DecorationItem, gridX: number, gridY: number, excludeId?: string): boolean => {
    // 检查边界
    if (gridX < 0 || gridY < 0 || 
        gridX + item.size.width > farmWidth / gridSize || 
        gridY + item.size.height > farmHeight / gridSize) {
      return false
    }

    // 检查与其他装饰的重叠
    if (!item.placement.canOverlap) {
      for (const placed of placedDecorations) {
        if (excludeId && placed.instanceId === excludeId) continue
        
        const placedItem = decorationSystem.getPlacedDecorations()
          .find(p => p.decoration.instanceId === placed.instanceId)?.item
        
        if (!placedItem) continue

        // 检查重叠
        const overlapX = !(placed.x + placedItem.size.width <= gridX || placed.x >= gridX + item.size.width)
        const overlapY = !(placed.y + placedItem.size.height <= gridY || placed.y >= gridY + item.size.height)
        
        if (overlapX && overlapY) {
          return false
        }
      }
    }

    return true
  }, [placedDecorations, farmWidth, farmHeight, gridSize, decorationSystem])

  // 开始拖拽新装饰道具
  const startDragNewItem = (item: DecorationItem, event: React.MouseEvent) => {
    event.preventDefault()
    
    const position = getMousePosition(event)
    if (!position) return

    setDragState({
      isDragging: true,
      draggedItem: item,
      draggedDecoration: null,
      startPosition: position,
      currentPosition: position,
      dragType: 'new'
    })
  }

  // 开始拖拽已放置的装饰
  const startDragExistingDecoration = (decoration: PlacedDecoration, event: React.MouseEvent) => {
    event.preventDefault()
    event.stopPropagation()
    
    const position = getMousePosition(event)
    if (!position) return

    setSelectedDecoration(decoration)
    setDragState({
      isDragging: true,
      draggedItem: null,
      draggedDecoration: decoration,
      startPosition: { x: decoration.x, y: decoration.y },
      currentPosition: position,
      dragType: 'existing'
    })

    if (onSelectionChange) {
      onSelectionChange(decoration)
    }
  }

  // 处理鼠标移动
  const handleMouseMove = useCallback((event: React.MouseEvent) => {
    if (!dragState.isDragging) {
      // 预览模式
      if (dragState.draggedItem) {
        const position = getMousePosition(event)
        setPreviewPosition(position)
      }
      return
    }

    const position = getMousePosition(event)
    if (!position) return

    setDragState(prev => ({
      ...prev,
      currentPosition: position
    }))
  }, [dragState, getMousePosition])

  // 结束拖拽
  const handleMouseUp = useCallback((event: React.MouseEvent) => {
    if (!dragState.isDragging) return

    const position = getMousePosition(event)
    if (!position) {
      resetDragState()
      return
    }

    if (dragState.dragType === 'new' && dragState.draggedItem) {
      // 放置新装饰道具
      const canPlace = canPlaceAt(dragState.draggedItem, position.x, position.y)
      if (canPlace) {
        const result = decorationSystem.placeDecoration(
          dragState.draggedItem.id,
          position.x,
          position.y
        )
        
        if (result.success) {
          loadDecorationData()
          if (result.placedDecoration) {
            setSelectedDecoration(result.placedDecoration)
            if (onSelectionChange) {
              onSelectionChange(result.placedDecoration)
            }
          }
        } else {
          alert(result.message)
        }
      } else {
        alert('无法在此位置放置装饰道具')
      }
    } else if (dragState.dragType === 'existing' && dragState.draggedDecoration) {
      // 移动已有装饰道具
      const item = decorationSystem.getPlacedDecorations()
        .find(p => p.decoration.instanceId === dragState.draggedDecoration!.instanceId)?.item

      if (item) {
        const canPlace = canPlaceAt(item, position.x, position.y, dragState.draggedDecoration.instanceId)
        if (canPlace) {
          const result = decorationSystem.moveDecoration(
            dragState.draggedDecoration.instanceId,
            position.x,
            position.y
          )
          
          if (result.success) {
            loadDecorationData()
          } else {
            alert(result.message)
          }
        } else {
          alert('无法移动到此位置')
        }
      }
    }

    resetDragState()
  }, [dragState, getMousePosition, canPlaceAt, decorationSystem, loadDecorationData, onSelectionChange])

  const resetDragState = () => {
    setDragState({
      isDragging: false,
      draggedItem: null,
      draggedDecoration: null,
      startPosition: null,
      currentPosition: null,
      dragType: null
    })
    setPreviewPosition(null)
  }

  // 选择装饰道具
  const selectDecoration = (decoration: PlacedDecoration) => {
    setSelectedDecoration(decoration)
    if (onSelectionChange) {
      onSelectionChange(decoration)
    }
  }

  // 删除选中的装饰道具
  const deleteSelectedDecoration = () => {
    if (!selectedDecoration) return

    const result = decorationSystem.removeDecoration(selectedDecoration.instanceId)
    if (result.success) {
      setSelectedDecoration(null)
      loadDecorationData()
      if (onSelectionChange) {
        onSelectionChange(null)
      }
    } else {
      alert(result.message)
    }
  }

  // 旋转选中的装饰道具
  const rotateSelectedDecoration = () => {
    if (!selectedDecoration) return
    
    // 这里可以实现旋转逻辑
    alert('旋转功能将在后续版本中实现')
  }

  // 渲染网格
  const renderGrid = () => {
    if (!showGrid) return null

    const lines = []
    const cols = Math.ceil(farmWidth / gridSize)
    const rows = Math.ceil(farmHeight / gridSize)

    // 垂直线
    for (let i = 0; i <= cols; i++) {
      lines.push(
        <line
          key={`v-${i}`}
          x1={i * gridSize}
          y1={0}
          x2={i * gridSize}
          y2={farmHeight}
          stroke="rgba(0, 0, 0, 0.1)"
          strokeWidth="1"
        />
      )
    }

    // 水平线
    for (let i = 0; i <= rows; i++) {
      lines.push(
        <line
          key={`h-${i}`}
          x1={0}
          y1={i * gridSize}
          x2={farmWidth}
          y2={i * gridSize}
          stroke="rgba(0, 0, 0, 0.1)"
          strokeWidth="1"
        />
      )
    }

    return (
      <svg
        className="decoration-grid"
        width={farmWidth}
        height={farmHeight}
        style={{ position: 'absolute', top: 0, left: 0, pointerEvents: 'none' }}
      >
        {lines}
      </svg>
    )
  }

  // 渲染已放置的装饰道具
  const renderPlacedDecorations = () => {
    return placedDecorations.map(decoration => {
      const item = decorationSystem.getPlacedDecorations()
        .find(p => p.decoration.instanceId === decoration.instanceId)?.item
      
      if (!item) return null

      const pixel = gridToPixel(decoration.x, decoration.y)
      const isSelected = selectedDecoration?.instanceId === decoration.instanceId
      const isDragging = dragState.isDragging && 
                        dragState.draggedDecoration?.instanceId === decoration.instanceId

      return (
        <div
          key={decoration.instanceId}
          className={`placed-decoration ${isSelected ? 'selected' : ''} ${isDragging ? 'dragging' : ''}`}
          style={{
            left: isDragging && dragState.currentPosition ? 
                  gridToPixel(dragState.currentPosition.x, dragState.currentPosition.y).x : pixel.x,
            top: isDragging && dragState.currentPosition ? 
                 gridToPixel(dragState.currentPosition.x, dragState.currentPosition.y).y : pixel.y,
            width: item.size.width * gridSize,
            height: item.size.height * gridSize,
            transform: `rotate(${decoration.rotation}deg)`
          }}
          onClick={(e) => {
            e.stopPropagation()
            selectDecoration(decoration)
          }}
          onMouseDown={(e) => startDragExistingDecoration(decoration, e)}
        >
          <img
            src={item.visual.sprite}
            alt={item.name}
            style={{ width: '100%', height: '100%', objectFit: 'cover' }}
            onError={(e) => {
              (e.target as HTMLImageElement).src = '/placeholder-decoration.png'
            }}
          />
          {isSelected && (
            <div className="decoration-selection-border" />
          )}
        </div>
      )
    })
  }

  // 渲染拖拽预览
  const renderDragPreview = () => {
    if (!dragState.isDragging || !dragState.currentPosition) return null

    const item = dragState.draggedItem
    if (!item) return null

    const pixel = gridToPixel(dragState.currentPosition.x, dragState.currentPosition.y)
    const canPlace = canPlaceAt(item, dragState.currentPosition.x, dragState.currentPosition.y)

    return (
      <div
        className={`drag-preview ${canPlace ? 'can-place' : 'cannot-place'}`}
        style={{
          left: pixel.x,
          top: pixel.y,
          width: item.size.width * gridSize,
          height: item.size.height * gridSize
        }}
      >
        <img
          src={item.visual.sprite}
          alt={item.name}
          style={{ width: '100%', height: '100%', objectFit: 'cover', opacity: 0.7 }}
          onError={(e) => {
            (e.target as HTMLImageElement).src = '/placeholder-decoration.png'
          }}
        />
      </div>
    )
  }

  if (!isActive) return null

  return (
    <div className="decoration-mode">
      {/* 工具栏 */}
      <div className="decoration-toolbar">
        <div className="toolbar-left">
          <button 
            className={`grid-toggle ${showGrid ? 'active' : ''}`}
            onClick={() => setShowGrid(!showGrid)}
            title="显示/隐藏网格"
          >
            🔳
          </button>
          <div className="beauty-indicator">
            <span className="beauty-icon">✨</span>
            <span className="beauty-value">{totalBeauty}</span>
            <span className="beauty-label">美观度</span>
          </div>
        </div>
        
        <div className="toolbar-center">
          <h3>装饰模式</h3>
        </div>
        
        <div className="toolbar-right">
          {selectedDecoration && (
            <div className="selected-actions">
              <button 
                onClick={rotateSelectedDecoration}
                title="旋转"
                className="action-btn rotate-btn"
              >
                🔄
              </button>
              <button 
                onClick={deleteSelectedDecoration}
                title="删除"
                className="action-btn delete-btn"
              >
                🗑️
              </button>
            </div>
          )}
          <button onClick={onExit} className="exit-btn">
            退出装饰模式
          </button>
        </div>
      </div>

      {/* 装饰道具面板 */}
      <div className="decoration-panel">
        <h4>可用装饰道具</h4>
        <div className="owned-decorations">
          {ownedDecorations.length === 0 ? (
            <p className="no-decorations">暂无可用装饰道具</p>
          ) : (
            ownedDecorations.map(({ item, quantity }) => (
              <div
                key={item.id}
                className="decoration-item"
                draggable
                onMouseDown={(e) => startDragNewItem(item, e)}
              >
                <img
                  src={item.visual.sprite}
                  alt={item.name}
                  onError={(e) => {
                    (e.target as HTMLImageElement).src = '/placeholder-decoration.png'
                  }}
                />
                <div className="item-info">
                  <span className="item-name">{item.name}</span>
                  <span className="item-quantity">×{quantity}</span>
                </div>
              </div>
            ))
          )}
        </div>
      </div>

      {/* 农场区域 */}
      <div
        ref={farmRef}
        className="decoration-farm"
        style={{ width: farmWidth, height: farmHeight }}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onClick={() => setSelectedDecoration(null)}
      >
        {renderGrid()}
        {renderPlacedDecorations()}
        {renderDragPreview()}
      </div>

      {/* 选中装饰的信息面板 */}
      {selectedDecoration && (
        <div className="decoration-info-panel">
          {(() => {
            const item = decorationSystem.getPlacedDecorations()
              .find(p => p.decoration.instanceId === selectedDecoration.instanceId)?.item
            
            if (!item) return null

            return (
              <div>
                <h4>{item.name}</h4>
                <p>{item.description}</p>
                <div className="decoration-stats">
                  <div>美观度: +{item.beautyValue}</div>
                  <div>等级: {selectedDecoration.level}/{item.maxLevel}</div>
                  <div>耐久度: {selectedDecoration.condition}%</div>
                </div>
                {item.effects && item.effects.length > 0 && (
                  <div className="decoration-effects">
                    <h5>特殊效果:</h5>
                    {item.effects.map((effect, index) => (
                      <div key={index} className="effect">
                        {effect.description}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )
          })()}
        </div>
      )}
    </div>
  )
}

export default DecorationMode 