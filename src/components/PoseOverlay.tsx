import React, { useRef, useEffect } from 'react'
import { PoseLandmark, PoseLandmarkIndex, PostureAnalysis } from '../types/pose'
import './PoseOverlay.css'

interface PoseOverlayProps {
  landmarks?: any[]
  postureAnalysis?: PostureAnalysis | null
  width: number
  className?: string
  showKeypoints?: boolean
  showConnections?: boolean
  showConfidence?: boolean
}

// 骨架连接定义
const POSE_CONNECTIONS = [
  // 面部
  [PoseLandmarkIndex.LEFT_EAR, PoseLandmarkIndex.LEFT_EYE_OUTER],
  [PoseLandmarkIndex.LEFT_EYE_OUTER, PoseLandmarkIndex.LEFT_EYE],
  [PoseLandmarkIndex.LEFT_EYE, PoseLandmarkIndex.LEFT_EYE_INNER],
  [PoseLandmarkIndex.LEFT_EYE_INNER, PoseLandmarkIndex.NOSE],
  [PoseLandmarkIndex.NOSE, PoseLandmarkIndex.RIGHT_EYE_INNER],
  [PoseLandmarkIndex.RIGHT_EYE_INNER, PoseLandmarkIndex.RIGHT_EYE],
  [PoseLandmarkIndex.RIGHT_EYE, PoseLandmarkIndex.RIGHT_EYE_OUTER],
  [PoseLandmarkIndex.RIGHT_EYE_OUTER, PoseLandmarkIndex.RIGHT_EAR],
  [PoseLandmarkIndex.MOUTH_LEFT, PoseLandmarkIndex.MOUTH_RIGHT],

  // 躯干
  [PoseLandmarkIndex.LEFT_SHOULDER, PoseLandmarkIndex.RIGHT_SHOULDER],
  [PoseLandmarkIndex.LEFT_SHOULDER, PoseLandmarkIndex.LEFT_HIP],
  [PoseLandmarkIndex.RIGHT_SHOULDER, PoseLandmarkIndex.RIGHT_HIP],
  [PoseLandmarkIndex.LEFT_HIP, PoseLandmarkIndex.RIGHT_HIP],

  // 左臂
  [PoseLandmarkIndex.LEFT_SHOULDER, PoseLandmarkIndex.LEFT_ELBOW],
  [PoseLandmarkIndex.LEFT_ELBOW, PoseLandmarkIndex.LEFT_WRIST],
  [PoseLandmarkIndex.LEFT_WRIST, PoseLandmarkIndex.LEFT_PINKY],
  [PoseLandmarkIndex.LEFT_WRIST, PoseLandmarkIndex.LEFT_INDEX],
  [PoseLandmarkIndex.LEFT_WRIST, PoseLandmarkIndex.LEFT_THUMB],

  // 右臂
  [PoseLandmarkIndex.RIGHT_SHOULDER, PoseLandmarkIndex.RIGHT_ELBOW],
  [PoseLandmarkIndex.RIGHT_ELBOW, PoseLandmarkIndex.RIGHT_WRIST],
  [PoseLandmarkIndex.RIGHT_WRIST, PoseLandmarkIndex.RIGHT_PINKY],
  [PoseLandmarkIndex.RIGHT_WRIST, PoseLandmarkIndex.RIGHT_INDEX],
  [PoseLandmarkIndex.RIGHT_WRIST, PoseLandmarkIndex.RIGHT_THUMB],

  // 左腿
  [PoseLandmarkIndex.LEFT_HIP, PoseLandmarkIndex.LEFT_KNEE],
  [PoseLandmarkIndex.LEFT_KNEE, PoseLandmarkIndex.LEFT_ANKLE],
  [PoseLandmarkIndex.LEFT_ANKLE, PoseLandmarkIndex.LEFT_HEEL],
  [PoseLandmarkIndex.LEFT_ANKLE, PoseLandmarkIndex.LEFT_FOOT_INDEX],

  // 右腿
  [PoseLandmarkIndex.RIGHT_HIP, PoseLandmarkIndex.RIGHT_KNEE],
  [PoseLandmarkIndex.RIGHT_KNEE, PoseLandmarkIndex.RIGHT_ANKLE],
  [PoseLandmarkIndex.RIGHT_ANKLE, PoseLandmarkIndex.RIGHT_HEEL],
  [PoseLandmarkIndex.RIGHT_ANKLE, PoseLandmarkIndex.RIGHT_FOOT_INDEX],
]

export const PoseOverlay: React.FC<PoseOverlayProps> = ({
  landmarks,
  postureAnalysis,
  width,
  className = '',
  showKeypoints = true,
  showConnections = true,
  showConfidence = false
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null)

  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext('2d')
    if (!ctx) return

    // 清除画布
    ctx.clearRect(0, 0, width, width)

    if (!landmarks || landmarks.length === 0) return

    // 设置画布大小
    canvas.width = width
    canvas.height = width

          // 绘制骨架连接
      if (showConnections) {
        drawSkeleton(ctx, landmarks, width, postureAnalysis)
      }

      // 绘制关键点
      if (showKeypoints) {
        drawLandmarks(ctx, landmarks, width, showConfidence, postureAnalysis)
      }

      // 绘制专注状态指示器
      if (postureAnalysis) {
        drawFocusIndicator(ctx, postureAnalysis, width)
      }
  }, [landmarks, postureAnalysis, width, showKeypoints, showConnections, showConfidence])

  return (
    <div className={`pose-overlay ${className}`}>
      <canvas
        ref={canvasRef}
        width={width}
        height={width}
        className="pose-canvas"
      />
      {postureAnalysis && (
        <div className="pose-info">
          <div className={`focus-score ${postureAnalysis.isFocused ? 'good' : 'poor'}`}>
            专注度: {Math.round(postureAnalysis.focusScore)}%
          </div>
          {postureAnalysis.warnings.length > 0 && (
            <div className="warnings">
              {postureAnalysis.warnings.map((warning, index) => (
                <div key={index} className="warning-item">
                  ⚠️ {warning}
                </div>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  )
}

/**
 * 绘制骨架连接线
 */
function drawSkeleton(
  ctx: CanvasRenderingContext2D,
  landmarks: PoseLandmark[],
  width: number,
  postureAnalysis?: PostureAnalysis | null
) {
  const lineColor = postureAnalysis?.isFocused ? '#10B981' : '#EF4444'
  const lineWidth = 2

  ctx.strokeStyle = lineColor
  ctx.lineWidth = lineWidth
  ctx.lineCap = 'round'

  POSE_CONNECTIONS.forEach(([startIdx, endIdx]) => {
    const start = landmarks[startIdx]
    const end = landmarks[endIdx]

    if (!start || !end) return

    const startVisibility = start.visibility ?? 1
    const endVisibility = end.visibility ?? 1
    
    // 只绘制可见性足够的连接
    if (startVisibility > 0.5 && endVisibility > 0.5) {
      ctx.globalAlpha = Math.min(startVisibility, endVisibility)
      
      ctx.beginPath()
      ctx.moveTo(start.x * width, start.y * width)
      ctx.lineTo(end.x * width, end.y * width)
      ctx.stroke()
    }
  })

  ctx.globalAlpha = 1.0
}

/**
 * 绘制关键点
 */
function drawLandmarks(
  ctx: CanvasRenderingContext2D,
  landmarks: PoseLandmark[],
  width: number,
  showConfidence: boolean,
  postureAnalysis?: PostureAnalysis | null
) {
  landmarks.forEach((landmark, index) => {
    const visibility = landmark.visibility ?? 1
    if (visibility < 0.5) return

    const x = landmark.x * width
    const y = landmark.y * width
    const radius = getPointRadius(index)
    const color = getPointColor(index, postureAnalysis)

    // 绘制关键点
    ctx.globalAlpha = visibility
    ctx.fillStyle = color
    ctx.beginPath()
    ctx.arc(x, y, radius, 0, 2 * Math.PI)
    ctx.fill()

    // 绘制边框
    ctx.strokeStyle = '#FFFFFF'
    ctx.lineWidth = 1
    ctx.stroke()

    // 显示置信度
    if (showConfidence && visibility < 1.0) {
      ctx.fillStyle = '#000000'
      ctx.font = '10px Arial'
      ctx.fillText(
        `${Math.round(visibility * 100)}%`,
        x + radius + 2,
        y - radius
      )
    }
  })

  ctx.globalAlpha = 1.0
}

/**
 * 获取关键点半径
 */
function getPointRadius(index: number): number {
  // 重要关键点更大
  const importantPoints = [
    PoseLandmarkIndex.NOSE,
    PoseLandmarkIndex.LEFT_SHOULDER,
    PoseLandmarkIndex.RIGHT_SHOULDER,
    PoseLandmarkIndex.LEFT_HIP,
    PoseLandmarkIndex.RIGHT_HIP
  ]

  return importantPoints.includes(index) ? 6 : 4
}

/**
 * 获取关键点颜色
 */
function getPointColor(index: number, postureAnalysis?: PostureAnalysis | null): string {
  // 根据姿态分析结果调整颜色
  if (!postureAnalysis) return '#3B82F6'

  // 头部关键点
  const headPoints = [
    PoseLandmarkIndex.NOSE,
    PoseLandmarkIndex.LEFT_EAR,
    PoseLandmarkIndex.RIGHT_EAR
  ]

  // 肩膀关键点
  const shoulderPoints = [
    PoseLandmarkIndex.LEFT_SHOULDER,
    PoseLandmarkIndex.RIGHT_SHOULDER
  ]

  if (headPoints.includes(index)) {
    return postureAnalysis.headPosition === 'center' ? '#10B981' : '#F59E0B'
  }

  if (shoulderPoints.includes(index)) {
    return Math.abs(postureAnalysis.shoulderBalance) < 0.3 ? '#10B981' : '#F59E0B'
  }

  // 其他关键点根据整体专注状态
  return postureAnalysis.isFocused ? '#10B981' : '#EF4444'
}

/**
 * 绘制专注状态指示器
 */
function drawFocusIndicator(
  ctx: CanvasRenderingContext2D,
  postureAnalysis: PostureAnalysis,
  width: number
) {
  const indicatorSize = 20
  const x = width - indicatorSize - 10
  const y = 10

  // 绘制背景圆
  ctx.fillStyle = postureAnalysis.isFocused ? '#10B981' : '#EF4444'
  ctx.beginPath()
  ctx.arc(x + indicatorSize / 2, y + indicatorSize / 2, indicatorSize / 2, 0, 2 * Math.PI)
  ctx.fill()

  // 绘制边框
  ctx.strokeStyle = '#FFFFFF'
  ctx.lineWidth = 2
  ctx.stroke()

  // 绘制状态图标
  ctx.fillStyle = '#FFFFFF'
  ctx.font = 'bold 12px Arial'
  ctx.textAlign = 'center'
  ctx.textBaseline = 'middle'
  
  const icon = postureAnalysis.isFocused ? '✓' : '!'
  ctx.fillText(icon, x + indicatorSize / 2, y + indicatorSize / 2)

  // 重置文本对齐
  ctx.textAlign = 'start'
  ctx.textBaseline = 'alphabetic'
}

export default PoseOverlay 