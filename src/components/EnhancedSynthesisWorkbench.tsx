import React, { useState, useCallback, useRef, useEffect } from 'react'
import { InventoryItem } from '../types/inventory'
import { ItemRarity, ItemCategory } from '../types/lootbox'
import { 
  ENHANCED_SYNTHESIS_RECIPES, 
  getEnhancedSynthesisRecipes, 
  selectRandomR<PERSON>ult, 
  generateResultItemName 
} from '../data/enhancedSynthesisRecipes'

interface EnhancedSynthesisWorkbenchProps {
  inventoryItems: InventoryItem[]
  onSynthesis: (resultItem: InventoryItem, consumedItems: InventoryItem[]) => void
  onError: (message: string) => void
}

interface SynthesisSlot {
  item: InventoryItem | null
  isActive: boolean
  glowEffect: boolean
}

const RARITY_COLORS = {
  [ItemRarity.GRAY]: '#9CA3AF',
  [ItemRarity.GREEN]: '#10B981', 
  [ItemRarity.BLUE]: '#3B82F6',
  [ItemRarity.ORANGE]: '#F59E0B',
  [ItemRarity.GOLD]: '#EAB308',
  [ItemRarity.GOLD_RED]: '#DC2626'
}

export const EnhancedSynthesisWorkbench: React.FC<EnhancedSynthesisWorkbenchProps> = ({
  inventoryItems,
  onSynthesis,
  onError
}) => {
  const [synthesisSlots, setSynthesisSlots] = useState<SynthesisSlot[]>([
    { item: null, isActive: false, glowEffect: false },
    { item: null, isActive: false, glowEffect: false }
  ])
  
  const [draggedItem, setDraggedItem] = useState<InventoryItem | null>(null)
  const [isProcessing, setIsProcessing] = useState(false)
  const [synthesisAnimation, setSynthesisAnimation] = useState<string>('')
  const [availableRecipes, setAvailableRecipes] = useState<any[]>([])
  const [selectedRecipe, setSelectedRecipe] = useState<any>(null)
  
  const synthesisAreaRef = useRef<HTMLDivElement>(null)
  const audioRef = useRef<HTMLAudioElement>(null)

  // 检查可用的配方
  useEffect(() => {
    const filledSlots = synthesisSlots.filter(slot => slot.item !== null)
    if (filledSlots.length === 2) {
      const inputItems = filledSlots.map(slot => ({
        rarity: slot.item!.rarity,
        category: slot.item!.category
      }))
      const recipes = getEnhancedSynthesisRecipes(inputItems)
      setAvailableRecipes(recipes)
      setSelectedRecipe(recipes[0] || null)
    } else {
      setAvailableRecipes([])
      setSelectedRecipe(null)
    }
  }, [synthesisSlots])

  // 拖拽开始
  const handleDragStart = useCallback((item: InventoryItem, e: React.DragEvent) => {
    setDraggedItem(item)
    e.dataTransfer.effectAllowed = 'move'
    e.dataTransfer.setData('text/plain', item.id)
  }, [])

  // 拖拽结束
  const handleDragEnd = useCallback(() => {
    setDraggedItem(null)
  }, [])

  // 拖拽到合成槽
  const handleSlotDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.dataTransfer.dropEffect = 'move'
  }, [])

  // 放置到合成槽
  const handleSlotDrop = useCallback((slotIndex: number, e: React.DragEvent) => {
    e.preventDefault()
    
    if (!draggedItem) return

    setSynthesisSlots(prev => {
      const newSlots = [...prev]
      newSlots[slotIndex] = {
        item: draggedItem,
        isActive: true,
        glowEffect: true
      }
      return newSlots
    })

    // 光效动画
    setTimeout(() => {
      setSynthesisSlots(prev => {
        const newSlots = [...prev]
        newSlots[slotIndex].glowEffect = false
        return newSlots
      })
    }, 500)

    setDraggedItem(null)
  }, [draggedItem])

  // 移除槽位物品
  const removeSlotItem = useCallback((slotIndex: number) => {
    setSynthesisSlots(prev => {
      const newSlots = [...prev]
      newSlots[slotIndex] = { item: null, isActive: false, glowEffect: false }
      return newSlots
    })
  }, [])

  // 执行合成
  const handleSynthesize = useCallback(async () => {
    if (!selectedRecipe) {
      onError('请先放入2个物品！')
      return
    }

    const filledSlots = synthesisSlots.filter(slot => slot.item !== null)
    if (filledSlots.length !== 2) {
      onError('需要恰好2个物品进行合成！')
      return
    }

    setIsProcessing(true)
    setSynthesisAnimation('synthesis-start')

    // 播放合成音效
    if (audioRef.current) {
      audioRef.current.currentTime = 0
      audioRef.current.play().catch(() => {})
    }

    try {
      // 模拟合成过程
      await new Promise(resolve => setTimeout(resolve, 2000))

      // 判断合成是否成功
      const isSuccess = Math.random() < selectedRecipe.successRate
      
      if (isSuccess) {
        // 合成成功
        setSynthesisAnimation('synthesis-success')
        
        const selectedResult = selectRandomResult(selectedRecipe)
        const resultName = generateResultItemName(selectedResult, filledSlots.map(slot => slot.item!))
        
                 const resultItem: InventoryItem = {
           id: `synth_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
           itemId: `synth_item_${Date.now()}`,
           name: resultName,
           icon: selectedResult.icon,
           rarity: selectedRecipe.resultRarity,
           category: selectedResult.category,
           type: selectedResult.type,
           quantity: 1,
           description: `由合成产生的${selectedRecipe.resultRarity}品质物品`,
           obtainedAt: Date.now()
         }

        // 调用成功回调
        onSynthesis(resultItem, filledSlots.map(slot => slot.item!))
        
        // 清空槽位
        setSynthesisSlots([
          { item: null, isActive: false, glowEffect: false },
          { item: null, isActive: false, glowEffect: false }
        ])

      } else {
        // 合成失败
        setSynthesisAnimation('synthesis-failure')
        onError(`合成失败！成功率: ${Math.round(selectedRecipe.successRate * 100)}%`)
        
        // 失败也要消耗材料（根据配方决定）
        // 这里简化处理，可以根据需要添加不同的失败结果
      }

    } catch (error) {
      setSynthesisAnimation('synthesis-failure')
      onError('合成过程出现错误！')
    } finally {
      setIsProcessing(false)
      setTimeout(() => setSynthesisAnimation(''), 1000)
    }
  }, [selectedRecipe, synthesisSlots, onSynthesis, onError])

  return (
    <div className="enhanced-synthesis-workbench">
      {/* 音效 */}
      <audio ref={audioRef} preload="auto">
        <source src="/sounds/synthesis.mp3" type="audio/mpeg" />
      </audio>

      {/* 标题 */}
      <div className="workbench-header text-center mb-6">
        <h2 className="text-2xl font-bold text-gray-800 mb-2">🔬 增强合成工作台</h2>
        <p className="text-sm text-gray-600">将两个物品拖拽到合成槽中，创造全新物品！</p>
      </div>

      {/* 物品库存区 */}
      <div className="inventory-section mb-6">
        <h3 className="text-lg font-semibold mb-3 text-gray-700">📦 物品库存</h3>
        <div className="inventory-grid grid grid-cols-8 gap-2 max-h-32 overflow-y-auto p-2 bg-gray-50 rounded-lg">
          {(() => {
            // 展开物品显示
            const expandedItems: Array<{item: InventoryItem, index: number}> = []
            inventoryItems.forEach((item) => {
              for (let i = 0; i < item.quantity; i++) {
                expandedItems.push({ item, index: i })
              }
            })
            
            return expandedItems.map(({item, index}, globalIndex) => (
              <div
                key={`${item.id}-${index}-${globalIndex}`}
                draggable
                onDragStart={(e) => handleDragStart(item, e)}
                onDragEnd={handleDragEnd}
                className="inventory-item cursor-grab active:cursor-grabbing transform transition-all duration-200 hover:scale-110"
                style={{
                  background: `linear-gradient(135deg, ${RARITY_COLORS[item.rarity]}15, ${RARITY_COLORS[item.rarity]}25)`,
                  border: `2px solid ${RARITY_COLORS[item.rarity]}`,
                  borderRadius: '8px',
                  aspectRatio: '1',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  fontSize: '20px',
                  minHeight: '50px',
                  boxShadow: `0 2px 8px ${RARITY_COLORS[item.rarity]}40`
                }}
                title={item.name}
              >
                {item.icon}
                {/* 品质光环效果 */}
                {item.rarity !== ItemRarity.GRAY && (
                  <div 
                    className="quality-glow"
                    style={{
                      position: 'absolute',
                      inset: '-2px',
                      borderRadius: '8px',
                      background: `linear-gradient(45deg, ${RARITY_COLORS[item.rarity]}40, transparent, ${RARITY_COLORS[item.rarity]}40)`,
                      opacity: 0.6,
                      animation: 'pulse 2s infinite'
                    }}
                  />
                )}
              </div>
            ))
          })()}
        </div>
      </div>

      {/* 合成区域 */}
      <div 
        ref={synthesisAreaRef}
        className={`synthesis-area p-6 bg-gradient-to-br from-blue-50 to-purple-50 rounded-xl border-2 border-dashed border-blue-300 ${synthesisAnimation}`}
      >
        {/* 合成槽 */}
        <div className="synthesis-slots flex justify-center items-center gap-8 mb-6">
          {synthesisSlots.map((slot, index) => (
            <div key={index} className="slot-container">
              <div
                className={`synthesis-slot ${slot.isActive ? 'active' : ''} ${slot.glowEffect ? 'glow' : ''}`}
                onDragOver={handleSlotDragOver}
                onDrop={(e) => handleSlotDrop(index, e)}
                style={{
                  width: '80px',
                  height: '80px',
                  border: slot.item ? `3px solid ${RARITY_COLORS[slot.item.rarity]}` : '3px dashed #CBD5E0',
                  borderRadius: '12px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  background: slot.item 
                    ? `linear-gradient(135deg, ${RARITY_COLORS[slot.item.rarity]}20, ${RARITY_COLORS[slot.item.rarity]}30)`
                    : 'rgba(255, 255, 255, 0.8)',
                  fontSize: '32px',
                  cursor: slot.item ? 'pointer' : 'default',
                  transition: 'all 0.3s ease',
                  position: 'relative',
                  transform: slot.glowEffect ? 'scale(1.1)' : 'scale(1)'
                }}
                onClick={() => slot.item && removeSlotItem(index)}
                title={slot.item ? `点击移除 ${slot.item.name}` : '拖拽物品到此处'}
              >
                {slot.item ? slot.item.icon : '⚪'}
                
                {/* 删除按钮 */}
                {slot.item && (
                  <div className="remove-btn absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center text-xs cursor-pointer hover:bg-red-600">
                    ×
                  </div>
                )}
                
                {/* 光效动画 */}
                {slot.glowEffect && (
                  <div className="slot-glow absolute inset-0 rounded-xl animate-ping" style={{
                    background: `radial-gradient(circle, ${RARITY_COLORS[slot.item?.rarity || ItemRarity.GRAY]}60, transparent)`
                  }} />
                )}
              </div>
              
              <div className="slot-label text-center mt-2 text-sm text-gray-600">
                槽位 {index + 1}
              </div>
            </div>
          ))}
          
          {/* 合成箭头 */}
          <div className="synthesis-arrow text-4xl text-blue-500 mx-4">
            {isProcessing ? '⚡' : '→'}
          </div>
          
          {/* 结果预览 */}
          <div className="result-preview">
            <div className="result-slot" style={{
              width: '80px',
              height: '80px',
              border: selectedRecipe ? `3px solid ${RARITY_COLORS[selectedRecipe.resultRarity]}` : '3px dashed #CBD5E0',
              borderRadius: '12px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              background: selectedRecipe 
                ? `linear-gradient(135deg, ${RARITY_COLORS[selectedRecipe.resultRarity]}20, ${RARITY_COLORS[selectedRecipe.resultRarity]}30)`
                : 'rgba(255, 255, 255, 0.8)',
              fontSize: '32px'
            }}>
              {selectedRecipe ? '❓' : '⚪'}
            </div>
            <div className="slot-label text-center mt-2 text-sm text-gray-600">
              合成结果
            </div>
          </div>
        </div>

        {/* 配方信息 */}
        {selectedRecipe && (
          <div className="recipe-info bg-white p-4 rounded-lg mb-4 border border-gray-200">
            <div className="flex justify-between items-center">
              <div>
                <h4 className="font-semibold text-gray-800">{selectedRecipe.name}</h4>
                <p className="text-sm text-gray-600">{selectedRecipe.description}</p>
              </div>
              <div className="text-right">
                <div className="text-lg font-bold" style={{ color: RARITY_COLORS[selectedRecipe.resultRarity] }}>
                  成功率: {Math.round(selectedRecipe.successRate * 100)}%
                </div>
                <div className="text-sm text-gray-500">
                  结果品质: {selectedRecipe.resultRarity}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 合成按钮 */}
        <div className="synthesis-controls text-center">
          <button
            onClick={handleSynthesize}
            disabled={!selectedRecipe || isProcessing}
            className={`synthesis-button px-8 py-3 rounded-xl font-bold text-white transition-all duration-300 ${
              selectedRecipe && !isProcessing
                ? 'bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 transform hover:scale-105 shadow-lg'
                : 'bg-gray-400 cursor-not-allowed'
            }`}
          >
            {isProcessing ? '🔥 合成中...' : '⚡ 开始合成'}
          </button>
        </div>
      </div>

      {/* CSS 样式 */}
      <style>{`
        .enhanced-synthesis-workbench {
          max-width: 800px;
          margin: 0 auto;
          padding: 20px;
        }

        .synthesis-slot.active {
          box-shadow: 0 0 20px rgba(59, 130, 246, 0.5);
          transform: scale(1.05);
        }

        .synthesis-slot.glow {
          animation: synthesis-glow 0.5s ease-in-out;
        }

        .synthesis-area.synthesis-start {
          animation: synthesis-process 2s ease-in-out;
        }

        .synthesis-area.synthesis-success {
          animation: synthesis-success 1s ease-in-out;
        }

        .synthesis-area.synthesis-failure {
          animation: synthesis-failure 1s ease-in-out;
        }

        @keyframes synthesis-glow {
          0% { transform: scale(1); }
          50% { transform: scale(1.15); }
          100% { transform: scale(1.05); }
        }

        @keyframes synthesis-process {
          0%, 100% { transform: scale(1); }
          25% { transform: scale(1.02); background: linear-gradient(45deg, #BFDBFE, #DDD6FE); }
          50% { transform: scale(1.05); background: linear-gradient(45deg, #93C5FD, #C4B5FD); }
          75% { transform: scale(1.02); background: linear-gradient(45deg, #BFDBFE, #DDD6FE); }
        }

        @keyframes synthesis-success {
          0% { transform: scale(1); }
          50% { transform: scale(1.1); background: linear-gradient(45deg, #34D399, #FBBF24); }
          100% { transform: scale(1); background: linear-gradient(45deg, #D1FAE5, #FEF3C7); }
        }

        @keyframes synthesis-failure {
          0% { transform: scale(1); }
          25% { transform: translateX(-5px); background: linear-gradient(45deg, #FCA5A5, #FED7D7); }
          50% { transform: translateX(5px); background: linear-gradient(45deg, #F87171, #FCA5A5); }
          75% { transform: translateX(-5px); background: linear-gradient(45deg, #FCA5A5, #FED7D7); }
          100% { transform: translateX(0); }
        }

        @keyframes pulse {
          0%, 100% { opacity: 0.6; }
          50% { opacity: 1; }
        }

        .inventory-item:hover .quality-glow {
          opacity: 1;
        }

        .synthesis-button:hover:not(:disabled) {
          box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }
      `}</style>
    </div>
  )
}

export default EnhancedSynthesisWorkbench 