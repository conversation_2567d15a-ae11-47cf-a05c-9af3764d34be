import React, { useState, useCallback } from 'react'
import { InventoryItem } from '../types/inventory'
import { ItemRarity, RARITY_COLORS } from '../types/lootbox'
import { ItemIntegrationManager } from '../managers/ItemIntegrationManager'
import { 
  getFuturesProductById, 
  getRandomYield, 
  getQualityName, 
  getFullProductName 
} from '../data/chineseFuturesProducts'
import FuturesProductTooltip from './FuturesProductTooltip'

interface ChineseFuturesInventoryProps {
  itemManager: ItemIntegrationManager
  className?: string
}

interface SynthesisResult {
  success: boolean
  resultItem?: InventoryItem
  message: string
  animation: 'success' | 'failure' | 'processing' | ''
}

// 获取品种ID（从物品名称中提取）
function extractVarietyId(itemName: string): string {
  const nameMap: { [key: string]: string } = {
    '玉米': 'corn',
    '大豆': 'soybean', 
    '小麦': 'wheat',
    '粳米': 'rice',
    '菜籽': 'rapeseed',
    '花生': 'peanut',
    '棉花': 'cotton',
    '白糖': 'sugar',
    '苹果': 'apple',
    '红枣': 'red_jujube',
    '生猪': 'live_pig',
    '鸡蛋': 'egg',
    '豆粕': 'soybean_meal'
  }
  
  for (const [chineseName, id] of Object.entries(nameMap)) {
    if (itemName.includes(chineseName)) {
      return id
    }
  }
  
  // 对于非期货农产品，返回一个唯一的标识符
  // 这样可以防止不同类型物品被错误合成
  return `unknown_${itemName.replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, '_')}`
}

// 检查是否为农产品（期货品种）
function isChineseFuturesProduct(itemName: string): boolean {
  const productNames = ['玉米', '大豆', '小麦', '粳米', '菜籽', '花生', '棉花', '白糖', '苹果', '红枣', '生猪', '鸡蛋', '豆粕']
  return productNames.some(name => itemName.includes(name))
}

// 获取下一个品质等级
function getNextRarity(currentRarity: ItemRarity): ItemRarity | null {
  const rarityOrder = [
    ItemRarity.GRAY, 
    ItemRarity.GREEN, 
    ItemRarity.BLUE, 
    ItemRarity.ORANGE, 
    ItemRarity.GOLD, 
    ItemRarity.GOLD_RED
  ]
  
  const currentIndex = rarityOrder.indexOf(currentRarity)
  if (currentIndex >= 0 && currentIndex < rarityOrder.length - 1) {
    return rarityOrder[currentIndex + 1]
  }
  return null // 已经是最高品质
}

// 获取合成成功率
function getSynthesisSuccessRate(rarity: ItemRarity): number {
  const rates = {
    [ItemRarity.GRAY]: 0.95,    // 95%
    [ItemRarity.GREEN]: 0.90,   // 90%
    [ItemRarity.BLUE]: 0.85,    // 85%
    [ItemRarity.ORANGE]: 0.75,  // 75%
    [ItemRarity.GOLD]: 0.60,    // 60%
    [ItemRarity.GOLD_RED]: 0    // 无法再升级
  }
  return rates[rarity] || 0
}

export const ChineseFuturesInventory: React.FC<ChineseFuturesInventoryProps> = ({
  itemManager,
  className = ''
}) => {
  const [items, setItems] = useState<any[]>([])
  const [draggedItem, setDraggedItem] = useState<any | null>(null)
  const [dragOverItem, setDragOverItem] = useState<any | null>(null)
  const [synthesisResult, setSynthesisResult] = useState<SynthesisResult>({ 
    success: false, 
    message: '', 
    animation: '' 
  })
  const [isProcessing, setIsProcessing] = useState(false)

  // 刷新物品列表
  const refreshItems = useCallback(() => {
    const allItems = itemManager.getAllItems()
    setItems(allItems)
  }, [itemManager])

  // 初始化加载
  React.useEffect(() => {
    refreshItems()
    
    // 监听物品变化
    const handleItemChange = () => refreshItems()
    itemManager.onItemAdded(handleItemChange)
    itemManager.onItemRemoved(handleItemChange)
    itemManager.onItemUpdated(handleItemChange)
    
    return () => {
      itemManager.off('itemAdded', handleItemChange)
      itemManager.off('itemRemoved', handleItemChange) 
      itemManager.off('itemUpdated', handleItemChange)
    }
  }, [itemManager, refreshItems])

  // 拖拽开始
  const handleDragStart = (item: any, e: React.DragEvent) => {
    setDraggedItem(item)
    e.dataTransfer.effectAllowed = 'move'
    console.log('开始拖拽:', item.name)
  }

  // 拖拽结束
  const handleDragEnd = () => {
    setDraggedItem(null)
    setDragOverItem(null)
  }

  // 拖拽悬停
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    e.dataTransfer.dropEffect = 'move'
  }

  // 拖拽进入
  const handleDragEnter = (targetItem: any, e: React.DragEvent) => {
    e.preventDefault()
    setDragOverItem(targetItem)
  }

  // 拖拽离开
  const handleDragLeave = () => {
    setDragOverItem(null)
  }

  // 拖拽放置 - 执行合成
  const handleDrop = async (targetItem: any, e: React.DragEvent) => {
    e.preventDefault()
    setDragOverItem(null)
    
    if (!draggedItem || draggedItem.id === targetItem.id) {
      return
    }

    console.log('尝试合成:', draggedItem.name, '+', targetItem.name)
    
    // 检查合成条件
    if (!canSynthesize(draggedItem, targetItem)) {
      setSynthesisResult({
        success: false,
        message: '只能合成相同品种和品质的农产品！',
        animation: 'failure'
      })
      setTimeout(() => setSynthesisResult({ success: false, message: '', animation: '' }), 3000)
      return
    }

    await performSynthesis(draggedItem, targetItem)
  }

  // 检查是否可以合成
  const canSynthesize = (item1: any, item2: any): boolean => {
    // 必须都是中国期货农产品
    if (!isChineseFuturesProduct(item1.name) || !isChineseFuturesProduct(item2.name)) {
      return false
    }
    
    // 必须是相同品质
    if (item1.rarity !== item2.rarity) return false
    
    // 提取品种ID进行比较
    const variety1 = extractVarietyId(item1.name)
    const variety2 = extractVarietyId(item2.name)
    
    // 必须是相同品种
    if (variety1 !== variety2) return false
    
    // 检查是否还能升级
    const nextRarity = getNextRarity(item1.rarity)
    return nextRarity !== null
  }

  // 执行合成
  const performSynthesis = async (item1: any, item2: any) => {
    setIsProcessing(true)
    setSynthesisResult({ success: false, message: '🔥 合成中...', animation: 'processing' })
    
    try {
      // 合成特效时间
      await new Promise(resolve => setTimeout(resolve, 3000))
      
      const currentRarity = item1.rarity
      const nextRarity = getNextRarity(currentRarity)
      const successRate = getSynthesisSuccessRate(currentRarity)
      const isSuccess = Math.random() < successRate
      
      if (isSuccess && nextRarity) {
        // 合成成功特效
        setSynthesisResult({ success: false, message: '✨ 合成成功！', animation: 'success' })
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        const varietyId = extractVarietyId(item1.name)
        const futuresProduct = getFuturesProductById(varietyId)
        const newYield = getRandomYield(varietyId, nextRarity)
        
        // 创建新物品
        const resultItem = itemManager.addManualItem({
          name: getFullProductName(varietyId, nextRarity),
          icon: futuresProduct?.icon || '🌾',
          rarity: nextRarity,
          category: item1.category,
          type: item1.type,
          value: (futuresProduct?.basePrice || 1000) * Math.pow(2, Object.values(ItemRarity).indexOf(nextRarity)),
          quantity: 1,
          description: `通过合成获得的${getQualityName(nextRarity)}品质${futuresProduct?.name}，产量: ${newYield}`,
          source: {
            type: 'lootbox',
            timestamp: Date.now()
          }
        })
        
        // 正确移除原材料（使用原始ID）
        const originalId1 = item1.originalId || item1.id.split('_')[0]
        const originalId2 = item2.originalId || item2.id.split('_')[0]
        
        console.log('移除物品:', originalId1, originalId2)
        
        // 先减少数量，如果数量为1则完全移除
        const allItems = itemManager.getAllItems()
        const sourceItem1 = allItems.find(item => item.id === originalId1)
        const sourceItem2 = allItems.find(item => item.id === originalId2)
        
        if (sourceItem1) {
          if (sourceItem1.quantity > 1) {
            sourceItem1.quantity -= 1
          } else {
            ;(itemManager as any).integratedItems?.delete(originalId1)
          }
        }
        
        if (sourceItem2 && originalId2 !== originalId1) {
          if (sourceItem2.quantity > 1) {
            sourceItem2.quantity -= 1
          } else {
            ;(itemManager as any).integratedItems?.delete(originalId2)
          }
        } else if (sourceItem1 && originalId2 === originalId1) {
          // 如果是同一个物品，再减少1个数量
          if (sourceItem1.quantity > 1) {
            sourceItem1.quantity -= 1
          } else {
            ;(itemManager as any).integratedItems?.delete(originalId1)
          }
        }
        
        setSynthesisResult({
          success: true,
          resultItem: {
            id: resultItem.id,
            itemId: resultItem.id,
            name: resultItem.name,
            icon: resultItem.icon,
            rarity: resultItem.rarity,
            category: resultItem.category,
            type: resultItem.type,
            quantity: 1,
            description: resultItem.description || '',
            obtainedAt: Date.now()
          },
          message: `🎉 合成成功！获得 ${resultItem.name}`,
          animation: 'success'
        })
        
        refreshItems()
        
      } else {
        // 合成失败特效
        setSynthesisResult({ success: false, message: '💥 合成失败！', animation: 'failure' })
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        // 正确移除原材料（使用原始ID）
        const originalId1 = item1.originalId || item1.id.split('_')[0]
        const originalId2 = item2.originalId || item2.id.split('_')[0]
        
        console.log('失败，移除物品:', originalId1, originalId2)
        
        // 先减少数量，如果数量为1则完全移除
        const allItems = itemManager.getAllItems()
        const sourceItem1 = allItems.find(item => item.id === originalId1)
        const sourceItem2 = allItems.find(item => item.id === originalId2)
        
        if (sourceItem1) {
          if (sourceItem1.quantity > 1) {
            sourceItem1.quantity -= 1
          } else {
            ;(itemManager as any).integratedItems?.delete(originalId1)
          }
        }
        
        if (sourceItem2 && originalId2 !== originalId1) {
          if (sourceItem2.quantity > 1) {
            sourceItem2.quantity -= 1
          } else {
            ;(itemManager as any).integratedItems?.delete(originalId2)
          }
        } else if (sourceItem1 && originalId2 === originalId1) {
          // 如果是同一个物品，再减少1个数量
          if (sourceItem1.quantity > 1) {
            sourceItem1.quantity -= 1
          } else {
            ;(itemManager as any).integratedItems?.delete(originalId1)
          }
        }
        
        setSynthesisResult({
          success: false,
          message: `💥 合成失败！成功率: ${Math.round(successRate * 100)}%`,
          animation: 'failure'
        })
        
        refreshItems()
      }
      
    } catch (error) {
      setSynthesisResult({
        success: false,
        message: '❌ 合成过程出现错误！',
        animation: 'failure'
      })
    } finally {
      setIsProcessing(false)
      setTimeout(() => setSynthesisResult({ success: false, message: '', animation: '' }), 4000)
    }
  }

  // 按品种分组物品
  const groupedItems = React.useMemo(() => {
    const groups: { [key: string]: any[] } = {}
    
    items.forEach(item => {
      const varietyId = extractVarietyId(item.name)
      if (!groups[varietyId]) {
        groups[varietyId] = []
      }
      
      // 展开数量，每个物品单独显示
      for (let i = 0; i < item.quantity; i++) {
        groups[varietyId].push({
          ...item,
          id: `${item.id}_${i}`,
          quantity: 1,
          originalId: item.id
        })
      }
    })
    
    return groups
  }, [items])

  return (
    <div className={`chinese-futures-inventory ${className}`}>
      {/* 合成结果提示 */}
      {synthesisResult.message && (
        <>
          {/* 背景遮罩 */}
          <div className="synthesis-backdrop" onClick={() => setSynthesisResult({ success: false, message: '', animation: '' })} />
          
          <div className={`synthesis-notification ${synthesisResult.animation}`}>
            <div className="notification-content">
              {synthesisResult.animation === 'processing' && '⚡ '}
              {synthesisResult.animation === 'success' && '✨ '}
              {synthesisResult.animation === 'failure' && '❌ '}
              {synthesisResult.message}
              {synthesisResult.resultItem && (
                <div className="result-item">
                  <span className="result-icon">{synthesisResult.resultItem.icon}</span>
                  <span className="result-name">{synthesisResult.resultItem.name}</span>
                </div>
              )}
            </div>
          </div>
        </>
      )}

      {/* 使用说明 */}
      <div className="synthesis-instructions">
        <h3>🎯 中国期货农产品背包合成</h3>
        <p>拖拽一个农产品到相同品种、相同品质的另一个农产品上进行合成升级</p>
        <div className="success-rates">
          <span>成功率: </span>
          <span style={{color: RARITY_COLORS[ItemRarity.GRAY]}}>普通→优质 95%</span>
          <span style={{color: RARITY_COLORS[ItemRarity.GREEN]}}>优质→稀有 90%</span>
          <span style={{color: RARITY_COLORS[ItemRarity.BLUE]}}>稀有→史诗 85%</span>
          <span style={{color: RARITY_COLORS[ItemRarity.ORANGE]}}>史诗→传说 75%</span>
          <span style={{color: RARITY_COLORS[ItemRarity.GOLD]}}>传说→神话 60%</span>
        </div>
      </div>

      {/* 分组显示物品 */}
      <div className="grouped-inventory">
        {Object.entries(groupedItems).map(([varietyId, varietyItems]) => {
          const futuresProduct = getFuturesProductById(varietyId)
          
          return (
            <div key={varietyId} className="variety-group">
              <div className="variety-header">
                <span className="variety-icon">{futuresProduct?.icon}</span>
                <span className="variety-name">{futuresProduct?.name}</span>
                <span className="variety-count">({varietyItems.length})</span>
                <span className="variety-exchange">{futuresProduct?.exchange}</span>
              </div>
              
              <div className="variety-items">
                {varietyItems.map((item, index) => {
                  const isDragOver = dragOverItem?.id === item.id
                  const canAcceptDrop = draggedItem && canSynthesize(draggedItem, item)
                  
                  return (
                    <FuturesProductTooltip key={item.id} item={item}>
                      <div
                        draggable={!isProcessing}
                        onDragStart={(e) => {
                          e.stopPropagation()
                          handleDragStart(item, e)
                        }}
                        onDragEnd={handleDragEnd}
                        onDragOver={(e) => {
                          e.stopPropagation()
                          handleDragOver(e)
                        }}
                        onDragEnter={(e) => {
                          e.stopPropagation()
                          handleDragEnter(item, e)
                        }}
                        onDragLeave={handleDragLeave}
                        onDrop={(e) => {
                          e.stopPropagation()
                          handleDrop(item, e)
                        }}
                        className={`
                          inventory-item 
                          ${isDragOver ? 'drag-over' : ''} 
                          ${canAcceptDrop ? 'can-drop' : ''}
                          ${draggedItem?.id === item.id ? 'dragging' : ''}
                        `}
                        style={{
                          background: `linear-gradient(135deg, ${RARITY_COLORS[item.rarity]}15, ${RARITY_COLORS[item.rarity]}25)`,
                          border: `2px solid ${
                            isDragOver && canAcceptDrop 
                              ? '#00FF00' 
                              : isDragOver && !canAcceptDrop
                                ? '#FF0000'
                                : RARITY_COLORS[item.rarity]
                          }`,
                          opacity: draggedItem?.id === item.id ? 0.5 : 1,
                          transform: isDragOver ? 'scale(1.05)' : 'scale(1)',
                          cursor: isProcessing ? 'not-allowed' : 'grab'
                        }}
                        title={`${item.name}\n${item.description}\n拖拽到相同品种物品上合成`}
                      >
                        <div className="item-icon">{item.icon}</div>
                        <div className="item-info">
                          <div className="item-name">{item.name}</div>
                          <div className="item-rarity" style={{ color: RARITY_COLORS[item.rarity] }}>
                            {getQualityName(item.rarity)}
                          </div>
                        </div>
                        
                        {/* 产量指示器 - 仅农业产品显示 */}
                        <div className="production-indicator">
                          📈
                        </div>
                        
                        {/* 合成提示 */}
                        {draggedItem && item.id !== draggedItem.id && (
                          <div className={`synthesis-indicator ${canAcceptDrop ? 'compatible' : 'incompatible'}`}>
                            {canAcceptDrop ? '✓' : '✗'}
                          </div>
                        )}
                      </div>
                    </FuturesProductTooltip>
                  )
                })}
              </div>
            </div>
          )
        })}
      </div>

      {/* 样式 */}
      <style>{`
        .chinese-futures-inventory {
          padding: 20px;
          max-height: 70vh;
          overflow-y: auto;
        }

        .synthesis-backdrop {
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: rgba(0, 0, 0, 0.6);
          z-index: 999;
          animation: backdrop-fade-in 0.3s ease-out;
        }

        .synthesis-notification {
          position: fixed;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          z-index: 1001;
          padding: 25px 35px;
          border-radius: 20px;
          color: white;
          font-weight: bold;
          max-width: 450px;
          min-width: 320px;
          text-align: center;
          box-shadow: 0 12px 40px rgba(0,0,0,0.5);
          backdrop-filter: blur(15px);
          animation: notification-scale-in 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
        }

        .synthesis-notification.processing {
          background: linear-gradient(45deg, #FF9800, #FFC107);
          animation: pulse 1s infinite, glow 2s ease-in-out infinite alternate;
        }

        .synthesis-notification.success {
          background: linear-gradient(45deg, #4CAF50, #8BC34A, #CDDC39);
          animation: success-bounce 0.6s ease-out, sparkle 2s linear infinite;
        }

        .synthesis-notification.failure {
          background: linear-gradient(45deg, #F44336, #E57373, #EF5350);
          animation: failure-shake 0.5s ease-out, fade-pulse 1s ease-in-out;
        }

        .result-item {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-top: 8px;
          padding-top: 8px;
          border-top: 1px solid rgba(255,255,255,0.3);
          animation: item-appear 0.8s ease-out;
        }

        .result-icon {
          font-size: 20px;
          animation: icon-spin 1s ease-in-out;
        }

        .synthesis-instructions {
          background: linear-gradient(135deg, #f8f9fa, #e9ecef);
          padding: 15px;
          border-radius: 8px;
          margin-bottom: 20px;
          border: 1px solid #dee2e6;
        }

        .synthesis-instructions h3 {
          margin: 0 0 8px 0;
          color: #2C5530;
          text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
        }

        .synthesis-instructions p {
          margin: 0 0 10px 0;
          color: #666;
        }

        .success-rates {
          display: flex;
          gap: 15px;
          flex-wrap: wrap;
          font-size: 12px;
        }

        .success-rates span:first-child {
          font-weight: bold;
          color: #333;
        }

        .grouped-inventory {
          display: flex;
          flex-direction: column;
          gap: 20px;
        }

        .variety-group {
          border: 1px solid #ddd;
          border-radius: 12px;
          overflow: hidden;
          box-shadow: 0 2px 8px rgba(0,0,0,0.1);
          transition: transform 0.2s ease;
        }

        .variety-group:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 16px rgba(0,0,0,0.15);
        }

        .variety-header {
          background: linear-gradient(45deg, #2C5530, #4CAF50);
          color: white;
          padding: 12px 15px;
          display: flex;
          align-items: center;
          gap: 10px;
          font-weight: bold;
        }

        .variety-icon {
          font-size: 20px;
          animation: gentle-pulse 2s ease-in-out infinite;
        }

        .variety-name {
          flex: 1;
        }

        .variety-count {
          background: rgba(255,255,255,0.2);
          padding: 2px 8px;
          border-radius: 12px;
          font-size: 12px;
        }

        .variety-exchange {
          background: rgba(255,255,255,0.3);
          padding: 2px 6px;
          border-radius: 4px;
          font-size: 10px;
        }

        .variety-items {
          padding: 15px;
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
          gap: 10px;
        }

        .inventory-item {
          aspect-ratio: 1;
          border-radius: 8px;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          transition: all 0.3s ease;
          position: relative;
          user-select: none;
          box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .inventory-item:hover {
          transform: scale(1.05) translateY(-2px);
          box-shadow: 0 8px 16px rgba(0,0,0,0.2);
        }

        .inventory-item.drag-over {
          box-shadow: 0 0 20px rgba(0,255,0,0.8);
          animation: glow-green 0.5s ease-in-out infinite alternate;
        }

        .inventory-item.can-drop {
          border-color: #00FF00 !important;
          animation: ready-to-drop 1s ease-in-out infinite;
        }

        .inventory-item.dragging {
          opacity: 0.6;
          transform: scale(0.9) rotate(5deg);
          box-shadow: 0 8px 16px rgba(0,0,0,0.3);
        }

        .item-icon {
          font-size: 24px;
          margin-bottom: 4px;
          transition: transform 0.2s ease;
        }

        .inventory-item:hover .item-icon {
          transform: scale(1.1);
        }

        .item-info {
          text-align: center;
        }

        .item-name {
          font-size: 11px;
          font-weight: 600;
          color: #333;
          margin-bottom: 2px;
        }

        .item-rarity {
          font-size: 9px;
          font-weight: 500;
        }

        .production-indicator {
          position: absolute;
          top: 4px;
          right: 4px;
          background: rgba(76, 175, 80, 0.9);
          color: white;
          border-radius: 50%;
          width: 18px;
          height: 18px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 10px;
          box-shadow: 0 2px 4px rgba(0,0,0,0.2);
          animation: gentle-glow 2s ease-in-out infinite alternate;
        }

        .synthesis-indicator {
          position: absolute;
          top: -5px;
          right: -5px;
          width: 20px;
          height: 20px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 12px;
          font-weight: bold;
          color: white;
          animation: indicator-pulse 1s ease-in-out infinite;
        }

        .synthesis-indicator.compatible {
          background: #4CAF50;
          box-shadow: 0 0 10px rgba(76, 175, 80, 0.6);
        }

        .synthesis-indicator.incompatible {
          background: #F44336;
          box-shadow: 0 0 10px rgba(244, 67, 54, 0.6);
        }

        @keyframes pulse {
          0%, 100% { opacity: 1; transform: scale(1); }
          50% { opacity: 0.8; transform: scale(1.05); }
        }

        @keyframes glow {
          0% { box-shadow: 0 0 5px rgba(255, 152, 0, 0.5); }
          100% { box-shadow: 0 0 20px rgba(255, 152, 0, 0.8), 0 0 30px rgba(255, 193, 7, 0.6); }
        }

        @keyframes success-bounce {
          0% { transform: scale(0.5) translateY(-20px); opacity: 0; }
          60% { transform: scale(1.1) translateY(0); opacity: 1; }
          100% { transform: scale(1) translateY(0); opacity: 1; }
        }

        @keyframes sparkle {
          0%, 100% { 
            background: linear-gradient(45deg, #4CAF50, #8BC34A, #CDDC39);
          }
          25% { 
            background: linear-gradient(45deg, #8BC34A, #CDDC39, #4CAF50);
          }
          50% { 
            background: linear-gradient(45deg, #CDDC39, #4CAF50, #8BC34A);
          }
          75% { 
            background: linear-gradient(45deg, #4CAF50, #CDDC39, #8BC34A);
          }
        }

        @keyframes failure-shake {
          0%, 100% { transform: translateX(0); }
          25% { transform: translateX(-8px) rotate(-1deg); }
          75% { transform: translateX(8px) rotate(1deg); }
        }

        @keyframes fade-pulse {
          0%, 100% { opacity: 1; }
          50% { opacity: 0.7; }
        }

        @keyframes item-appear {
          0% { opacity: 0; transform: translateY(10px) scale(0.9); }
          100% { opacity: 1; transform: translateY(0) scale(1); }
        }

        @keyframes icon-spin {
          0% { transform: scale(1) rotate(0deg); }
          50% { transform: scale(1.2) rotate(180deg); }
          100% { transform: scale(1) rotate(360deg); }
        }

        @keyframes gentle-pulse {
          0%, 100% { transform: scale(1); }
          50% { transform: scale(1.05); }
        }

        @keyframes gentle-glow {
          0% { 
            background: rgba(76, 175, 80, 0.9);
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
          }
          100% { 
            background: rgba(76, 175, 80, 1);
            box-shadow: 0 2px 8px rgba(76, 175, 80, 0.4);
          }
        }

        @keyframes glow-green {
          0% { box-shadow: 0 0 10px rgba(0, 255, 0, 0.5); }
          100% { box-shadow: 0 0 25px rgba(0, 255, 0, 0.8); }
        }

        @keyframes ready-to-drop {
          0%, 100% { border-width: 2px; }
          50% { border-width: 3px; }
        }

        @keyframes indicator-pulse {
          0%, 100% { transform: scale(1); opacity: 1; }
          50% { transform: scale(1.1); opacity: 0.8; }
        }

        @keyframes backdrop-fade-in {
          0% { opacity: 0; }
          100% { opacity: 1; }
        }

        @keyframes notification-scale-in {
          0% { 
            opacity: 0;
            transform: translate(-50%, -50%) scale(0.3) rotate(180deg);
          }
          70% {
            transform: translate(-50%, -50%) scale(1.1) rotate(-10deg);
          }
          100% { 
            opacity: 1;
            transform: translate(-50%, -50%) scale(1) rotate(0deg);
          }
        }
      `}</style>
    </div>
  )
}

export default ChineseFuturesInventory 