import React, { useState, useRef, useEffect } from 'react'
import { GameItem } from '../types/enhanced-items'
import { ItemCategory, QUALITY_CONFIGS } from '../types/enhanced-items'

interface TooltipProps {
  item: GameItem
  children: React.ReactNode
  className?: string
}

export const Tooltip: React.FC<TooltipProps> = ({ item, children, className = '' }) => {
  const [isVisible, setIsVisible] = useState(false)
  const [position, setPosition] = useState({ x: 0, y: 0 })
  const tooltipRef = useRef<HTMLDivElement>(null)
  const triggerRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const updatePosition = (e: MouseEvent) => {
      if (isVisible && triggerRef.current) {
        const rect = triggerRef.current.getBoundingClientRect()
        const tooltipWidth = tooltipRef.current?.offsetWidth || 300
        const tooltipHeight = tooltipRef.current?.offsetHeight || 200
        
        let x = e.clientX + 10
        let y = e.clientY + 10
        
        // 防止工具提示超出视窗
        if (x + tooltipWidth > window.innerWidth) {
          x = e.clientX - tooltipWidth - 10
        }
        if (y + tooltipHeight > window.innerHeight) {
          y = e.clientY - tooltipHeight - 10
        }
        
        setPosition({ x, y })
      }
    }

    if (isVisible) {
      document.addEventListener('mousemove', updatePosition)
      return () => document.removeEventListener('mousemove', updatePosition)
    }
  }, [isVisible])

  const handleMouseEnter = () => {
    setIsVisible(true)
  }

  const handleMouseLeave = () => {
    setIsVisible(false)
  }

  const getProductionInfo = () => {
    if (item.category === ItemCategory.AGRICULTURAL) {
      const agriItem = item as any
      return {
        type: '农业产品',
        production: agriItem.production || { minDaily: 100, maxDaily: 120, currentRate: 1.0 },
        futuresCode: agriItem.futuresCode || 'N/A',
        futuresPrice: agriItem.futuresPrice || 0
      }
    } else if (item.category === ItemCategory.INDUSTRIAL) {
      const indItem = item as any
      return {
        type: '工业产品',
        properties: indItem.properties || { durability: 100, efficiency: 100, capacity: 100 },
        futuresCode: indItem.futuresCode || 'N/A',
        industrialType: indItem.industrialType || 'unknown'
      }
    } else if (item.category === ItemCategory.EQUIPMENT) {
      const equipItem = item as any
      return {
        type: '装备道具',
        attributes: equipItem.attributes || { focusBonus: 0, productionBonus: 0, qualityBonus: 0, duration: 24 },
        slot: equipItem.slot || 'unknown'
      }
    }
    return null
  }

  const qualityConfig = QUALITY_CONFIGS[item.quality]
  const productionInfo = getProductionInfo()

  return (
    <div 
      ref={triggerRef}
      className={`relative ${className}`}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {children}
      
      {isVisible && (
        <div
          ref={tooltipRef}
          className="tooltip-container"
          style={{
            position: 'fixed',
            left: position.x,
            top: position.y,
            zIndex: 9999,
            pointerEvents: 'none'
          }}
        >
          <div className="tooltip-content">
            {/* 头部信息 */}
            <div className="tooltip-header">
              <div className="flex items-center gap-3">
                <span className="text-3xl">{item.icon}</span>
                <div>
                  <h3 className="item-name" style={{ color: qualityConfig.color }}>
                    {item.name}
                  </h3>
                  <p className="item-quality" style={{ backgroundColor: qualityConfig.color }}>
                    {qualityConfig.name}
                  </p>
                </div>
              </div>
            </div>

            {/* 基础信息 */}
            <div className="tooltip-section">
              <h4>基础信息</h4>
              <div className="info-grid">
                <div className="info-item">
                  <span className="label">类型:</span>
                  <span className="value">{productionInfo?.type}</span>
                </div>
                <div className="info-item">
                  <span className="label">价值:</span>
                  <span className="value">{item.baseValue.toLocaleString()}</span>
                </div>
                <div className="info-item">
                  <span className="label">可堆叠:</span>
                  <span className="value">{item.stackable ? '是' : '否'}</span>
                </div>
                <div className="info-item">
                  <span className="label">可交易:</span>
                  <span className="value">{item.tradeable ? '是' : '否'}</span>
                </div>
              </div>
            </div>

            {/* 详细属性 */}
            {productionInfo && (
              <div className="tooltip-section">
                <h4>详细属性</h4>
                
                {item.category === ItemCategory.AGRICULTURAL && (
                  <div className="production-info">
                    <div className="info-item highlight">
                      <span className="label">📈 日产量:</span>
                      <span className="value production-range">
                        {productionInfo.production.minDaily} - {productionInfo.production.maxDaily}
                      </span>
                    </div>
                    <div className="info-item">
                      <span className="label">🎯 当前倍率:</span>
                      <span className="value">{(productionInfo.production.currentRate * 100).toFixed(1)}%</span>
                    </div>
                    <div className="info-item">
                      <span className="label">📊 期货代码:</span>
                      <span className="value">{productionInfo.futuresCode}</span>
                    </div>
                    {productionInfo.futuresPrice > 0 && (
                      <div className="info-item">
                        <span className="label">💰 期货价格:</span>
                        <span className="value">¥{productionInfo.futuresPrice.toLocaleString()}</span>
                      </div>
                    )}
                  </div>
                )}

                {item.category === ItemCategory.INDUSTRIAL && (
                  <div className="industrial-info">
                    <div className="info-item">
                      <span className="label">🔧 效率:</span>
                      <span className="value">{productionInfo.properties.efficiency}%</span>
                    </div>
                    <div className="info-item">
                      <span className="label">💪 耐久度:</span>
                      <span className="value">{productionInfo.properties.durability}%</span>
                    </div>
                    <div className="info-item">
                      <span className="label">📦 容量:</span>
                      <span className="value">{productionInfo.properties.capacity}</span>
                    </div>
                    <div className="info-item">
                      <span className="label">📊 期货代码:</span>
                      <span className="value">{productionInfo.futuresCode}</span>
                    </div>
                    <div className="info-item">
                      <span className="label">🏭 类型:</span>
                      <span className="value">{productionInfo.industrialType}</span>
                    </div>
                  </div>
                )}

                {item.category === ItemCategory.EQUIPMENT && (
                  <div className="equipment-info">
                    <div className="info-item">
                      <span className="label">🧠 专注加成:</span>
                      <span className="value">+{productionInfo.attributes.focusBonus}%</span>
                    </div>
                    <div className="info-item">
                      <span className="label">⚡ 生产加成:</span>
                      <span className="value">+{productionInfo.attributes.productionBonus}%</span>
                    </div>
                    <div className="info-item">
                      <span className="label">✨ 品质加成:</span>
                      <span className="value">+{productionInfo.attributes.qualityBonus}%</span>
                    </div>
                    <div className="info-item">
                      <span className="label">⏰ 持续时间:</span>
                      <span className="value">{productionInfo.attributes.duration}小时</span>
                    </div>
                    <div className="info-item">
                      <span className="label">📍 装备槽:</span>
                      <span className="value">{productionInfo.slot}</span>
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* 描述 */}
            <div className="tooltip-section">
              <h4>描述</h4>
              <p className="description">{item.description}</p>
            </div>

            {/* 获得时间 */}
            {item.obtainedAt && (
              <div className="tooltip-footer">
                <small>获得时间: {new Date(item.obtainedAt).toLocaleString()}</small>
              </div>
            )}
          </div>

          <style>{`
            .tooltip-container {
              filter: drop-shadow(0 8px 16px rgba(0, 0, 0, 0.3));
            }

            .tooltip-content {
              background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
              border: 2px solid ${qualityConfig.borderColor};
              border-radius: 12px;
              padding: 16px;
              min-width: 300px;
              max-width: 400px;
              font-size: 14px;
              box-shadow: 0 0 20px ${qualityConfig.glowColor}40;
              animation: tooltipFadeIn 0.2s ease-out;
            }

            @keyframes tooltipFadeIn {
              from {
                opacity: 0;
                transform: scale(0.9) translateY(10px);
              }
              to {
                opacity: 1;
                transform: scale(1) translateY(0);
              }
            }

            .tooltip-header {
              margin-bottom: 12px;
              padding-bottom: 12px;
              border-bottom: 2px solid ${qualityConfig.borderColor}30;
            }

            .item-name {
              font-size: 16px;
              font-weight: bold;
              margin: 0;
            }

            .item-quality {
              display: inline-block;
              color: white;
              padding: 2px 8px;
              border-radius: 12px;
              font-size: 12px;
              font-weight: bold;
              margin-top: 4px;
            }

            .tooltip-section {
              margin-bottom: 12px;
            }

            .tooltip-section h4 {
              font-size: 14px;
              font-weight: bold;
              color: #333;
              margin: 0 0 8px 0;
              border-bottom: 1px solid #eee;
              padding-bottom: 4px;
            }

            .info-grid {
              display: grid;
              gap: 6px;
            }

            .info-item {
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding: 4px 0;
            }

            .info-item.highlight {
              background: linear-gradient(90deg, ${qualityConfig.color}10, ${qualityConfig.color}20);
              padding: 6px 8px;
              border-radius: 6px;
              border-left: 3px solid ${qualityConfig.color};
            }

            .label {
              color: #666;
              font-weight: 500;
            }

            .value {
              color: #333;
              font-weight: bold;
            }

            .production-range {
              color: ${qualityConfig.color};
              background: ${qualityConfig.color}15;
              padding: 2px 6px;
              border-radius: 4px;
            }

            .description {
              color: #555;
              font-style: italic;
              line-height: 1.4;
              margin: 0;
            }

            .tooltip-footer {
              margin-top: 12px;
              padding-top: 8px;
              border-top: 1px solid #eee;
              text-align: center;
              color: #999;
            }

            .production-info,
            .industrial-info,
            .equipment-info {
              display: grid;
              gap: 6px;
            }
          `}</style>
        </div>
      )}
    </div>
  )
}

export default Tooltip 