import React, { useState, useRef, useEffect } from 'react'
import { createPortal } from 'react-dom'
import { RARITY_COLORS } from '../types/lootbox'
import { getFuturesProductById, getQualityName } from '../data/chineseFuturesProducts'

interface FuturesProductTooltipProps {
  item: any
  children: React.ReactNode
  className?: string
}

export const FuturesProductTooltip: React.FC<FuturesProductTooltipProps> = ({ item, children, className = '' }) => {
  const [isVisible, setIsVisible] = useState(false)
  const [position, setPosition] = useState({ x: 0, y: 0 })
  const tooltipRef = useRef<HTMLDivElement>(null)
  const triggerRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const updatePosition = (e: MouseEvent) => {
      if (isVisible && triggerRef.current) {
        const tooltipWidth = tooltipRef.current?.offsetWidth || 320
        const tooltipHeight = tooltipRef.current?.offsetHeight || 250
        
        let x = e.clientX + 10
        let y = e.clientY + 10
        
        // 防止工具提示超出视窗
        if (x + tooltipWidth > window.innerWidth) {
          x = e.clientX - tooltipWidth - 10
        }
        if (y + tooltipHeight > window.innerHeight) {
          y = e.clientY - tooltipHeight - 10
        }
        
        setPosition({ x, y })
      }
    }

    if (isVisible) {
      document.addEventListener('mousemove', updatePosition)
      return () => document.removeEventListener('mousemove', updatePosition)
    }
  }, [isVisible])

  const handleMouseEnter = () => {
    console.log('🐭 工具提示: 鼠标进入', item.name)
    console.log('📊 物品数据:', item)
    setIsVisible(true)
  }

  const handleMouseLeave = () => {
    console.log('🐭 工具提示: 鼠标离开', item.name)
    setIsVisible(false)
  }

  // 从物品名称中提取品种ID
  const extractVarietyId = (itemName: string): string => {
    const nameMap: { [key: string]: string } = {
      '玉米': 'corn',
      '大豆': 'soybean', 
      '小麦': 'wheat',
      '粳米': 'rice',
      '菜籽': 'rapeseed',
      '花生': 'peanut',
      '棉花': 'cotton',
      '白糖': 'sugar',
      '苹果': 'apple',
      '红枣': 'red_jujube',
      '生猪': 'live_pig',
      '鸡蛋': 'egg',
      '豆粕': 'soybean_meal',
      '豆油': 'soybean_oil',
      '棕榈油': 'palm_oil'
    }
    
    for (const [chineseName, id] of Object.entries(nameMap)) {
      if (itemName.includes(chineseName)) {
        return id
      }
    }
    
    return 'unknown'
  }

  const varietyId = extractVarietyId(item.name)
  const futuresProduct = getFuturesProductById(varietyId)
  const borderColor = RARITY_COLORS[item.rarity] || '#ccc'
  const glowColor = RARITY_COLORS[item.rarity] || '#999'

  // 调试信息
  React.useEffect(() => {
    if (isVisible) {
      console.log('🔍 调试信息:')
      console.log('  - varietyId:', varietyId)
      console.log('  - futuresProduct:', futuresProduct)
      console.log('  - item.rarity:', item.rarity)
      console.log('  - borderColor:', borderColor)
      console.log('  - isVisible:', isVisible)
      console.log('  - position:', position)
    }
  }, [isVisible, varietyId, futuresProduct, item.rarity, borderColor, position])

  // 基于物品ID生成稳定的随机种子
  const getSeededRandom = (seed: string): number => {
    let hash = 0
    for (let i = 0; i < seed.length; i++) {
      const char = seed.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // 转换为32位整数
    }
    // 转换为0-1之间的小数
    return Math.abs(hash) / 2147483647
  }

  // 获取该物品的具体产量值
  const getItemYield = () => {
    if (futuresProduct && futuresProduct.yieldRanges[item.rarity]) {
      const yieldRange = futuresProduct.yieldRanges[item.rarity]
      // 使用物品ID和名称作为种子，确保同一物品总是显示相同的产量值
      const seed = `${item.id || item.name}_${item.rarity}`
      const random = getSeededRandom(seed)
      // 在产量范围内生成具体值
      const specificYield = Math.floor(yieldRange.min + random * (yieldRange.max - yieldRange.min + 1))
      
      console.log('🎯 计算物品具体产量:')
      console.log('  - 种子:', seed)
      console.log('  - 随机值:', random)
      console.log('  - 产量范围:', yieldRange)
      console.log('  - 具体产量:', specificYield)
      
      return specificYield
    }
    return null
  }

  // 获取产量信息
  const getYieldInfo = () => {
    console.log('🌾 获取产量信息:')
    console.log('  - futuresProduct:', futuresProduct)
    console.log('  - item.rarity:', item.rarity)
    
    if (futuresProduct && futuresProduct.yieldRanges[item.rarity]) {
      const yieldRange = futuresProduct.yieldRanges[item.rarity]
      const specificYield = getItemYield()
      const result = {
        min: yieldRange.min,
        max: yieldRange.max,
        unit: getYieldUnit(futuresProduct.category, varietyId),
        specificYield: specificYield
      }
      console.log('  - 产量结果:', result)
      return result
    }
    console.log('  - 未找到产量信息')
    return null
  }

  const getYieldUnit = (category: string, productId?: string) => {
    // 特殊处理油脂类产品
    if (productId === 'soybean_oil' || productId === 'palm_oil') {
      return '%'  // 出油率
    }
    
    switch (category) {
      case 'grain':
      case 'oilseed':
      case 'fiber':
        return '公斤/亩'
      case 'fruit':
        return '公斤/亩'
      case 'livestock':
        return '公斤/头'
      case 'feed':
        return '%'  // 出粕率
      default:
        return '单位/亩'
    }
  }

  const getCategoryName = (category: string) => {
    const categoryMap: { [key: string]: string } = {
      'grain': '谷物类',
      'oilseed': '油料作物',
      'fiber': '纤维作物',
      'sugar': '糖料作物',
      'fruit': '水果类',
      'livestock': '畜牧类',
      'feed': '饲料原料'
    }
    return categoryMap[category] || '未知类型'
  }

  const yieldInfo = getYieldInfo()

  // 工具提示渲染调试
  React.useEffect(() => {
    if (isVisible) {
      console.log('🎨 工具提示渲染:')
      console.log('  - yieldInfo:', yieldInfo)
      console.log('  - DOM将要渲染')
    }
  }, [isVisible, yieldInfo])

  return (
    <div 
      ref={triggerRef}
      className={`relative ${className}`}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {children}
      
      {isVisible && createPortal(
        <div
          ref={tooltipRef}
          className="tooltip-container"
          style={{
            position: 'fixed',
            left: position.x,
            top: position.y,
            zIndex: 999999,
            pointerEvents: 'none'
          }}
        >
          <div className="tooltip-content">
            {/* 头部信息 */}
            <div className="tooltip-header">
              <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                <span style={{ fontSize: '24px' }}>{item.icon}</span>
                <div>
                  <h3 className="item-name" style={{ color: borderColor }}>
                    {item.name}
                  </h3>
                  <p className="item-quality" style={{ backgroundColor: borderColor }}>
                    {getSafeQualityName(item.rarity)}
                  </p>
                </div>
              </div>
            </div>

            {/* 类型信息 */}
            {futuresProduct && (
              <div className="tooltip-section">
                <div className="info-item">
                  <span className="label">类型:</span>
                  <span className="value">{getCategoryName(futuresProduct.category)}</span>
                </div>
              </div>
            )}

            {/* 产量信息 */}
            {yieldInfo && (
              <div className="tooltip-section">
                <div className="info-item highlight">
                  <span className="label">📈 产量范围:</span>
                  <span className="value production-range">
                    {yieldInfo.min} - {yieldInfo.max} {yieldInfo.unit}
                  </span>
                </div>
                <div className="info-item specific-yield">
                  <span className="label">🎯 该物品产量:</span>
                  <span className="value specific-production">
                    {yieldInfo.specificYield} {yieldInfo.unit}
                  </span>
                </div>
              </div>
            )}
          </div>

          <style>{`
            .tooltip-container {
              filter: drop-shadow(0 8px 16px rgba(0, 0, 0, 0.3));
            }

            .tooltip-content {
              background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
              border: 2px solid ${borderColor};
              border-radius: 12px;
              padding: 16px;
              min-width: 250px;
              max-width: 300px;
              font-size: 14px;
              box-shadow: 0 0 20px ${glowColor}40;
              animation: tooltipFadeIn 0.2s ease-out;
            }

            @keyframes tooltipFadeIn {
              from {
                opacity: 0;
                transform: scale(0.9) translateY(10px);
              }
              to {
                opacity: 1;
                transform: scale(1) translateY(0);
              }
            }

            .tooltip-header {
              margin-bottom: 12px;
              padding-bottom: 12px;
              border-bottom: 2px solid ${borderColor}30;
            }

            .item-name {
              font-size: 16px;
              font-weight: bold;
              margin: 0;
            }

            .item-quality {
              display: inline-block;
              color: white;
              padding: 2px 8px;
              border-radius: 12px;
              font-size: 12px;
              font-weight: bold;
              margin-top: 4px;
            }

            .tooltip-section {
              margin-bottom: 8px;
            }

            .info-item {
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding: 6px 0;
            }

            .info-item.highlight {
              background: linear-gradient(90deg, ${borderColor}10, ${borderColor}20);
              padding: 8px;
              border-radius: 6px;
              border-left: 3px solid ${borderColor};
              margin: 4px 0;
            }

            .label {
              color: #666;
              font-weight: 500;
              font-size: 13px;
            }

            .value {
              color: #333;
              font-weight: bold;
              font-size: 13px;
            }

            .production-range {
              color: ${borderColor};
              background: ${borderColor}15;
              padding: 3px 6px;
              border-radius: 4px;
              font-weight: bold;
            }

            .info-item.specific-yield {
              background: linear-gradient(90deg, #4CAF5020, #4CAF5030);
              padding: 8px;
              border-radius: 6px;
              border-left: 3px solid #4CAF50;
              margin: 4px 0;
            }

            .specific-production {
              color: #4CAF50;
              background: #4CAF5015;
              padding: 3px 8px;
              border-radius: 4px;
              font-weight: bold;
              font-size: 14px;
            }
          `}</style>
        </div>,
        document.body
      )}
    </div>
  )
}

// 获取品质名称的安全版本
function getSafeQualityName(rarity: any): string {
  try {
    return getQualityName(rarity)
  } catch (error) {
    // 如果获取失败，返回默认值
    const qualityNames: { [key: string]: string } = {
      'gray': '普通',
      'green': '优质',
      'blue': '稀有',
      'orange': '史诗',
      'gold': '传说',
      'gold_red': '神话'
    }
    return qualityNames[rarity] || '未知品质'
  }
}

export default FuturesProductTooltip 