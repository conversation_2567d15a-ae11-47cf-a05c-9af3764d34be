import React, { useState, useEffect, useCallback } from 'react'
import { PlantingSystemManager } from '../systems/PlantingSystemManager'
import { PlantingSlot, PlantingStage, HarvestResult, Achievement, PlantingItem } from '../types/planting'
import { ItemRarity } from '../types/lootbox'
import { getFuturesProductById } from '../data/chineseFuturesProducts'
import { getPlantingItem, PLANTING_ITEMS } from '../data/plantingData'

// 种植槽位组件
interface PlantingSlotComponentProps {
  slot: PlantingSlot
  onPlant: (slotId: string, cropId: string) => void
  onHarvest: (slotId: string) => void
  onUseItem: (slotId: string, itemId: string) => void
}

const PlantingSlotComponent: React.FC<PlantingSlotComponentProps> = ({ 
  slot, 
  onPlant, 
  onHarvest, 
  onUseItem 
}) => {
  const [showCropSelector, setShowCropSelector] = useState(false)
  const [showItemSelector, setShowItemSelector] = useState(false)

  const formatTime = (ms: number) => {
    const minutes = Math.floor(ms / 60000)
    const seconds = Math.floor((ms % 60000) / 1000)
    return `${minutes}:${seconds.toString().padStart(2, '0')}`
  }

  const getRarityColor = (rarity: ItemRarity) => {
    const colors = {
      [ItemRarity.GRAY]: 'from-gray-400 to-gray-600',
      [ItemRarity.GREEN]: 'from-green-400 to-green-600',
      [ItemRarity.BLUE]: 'from-blue-400 to-blue-600',
      [ItemRarity.ORANGE]: 'from-orange-400 to-orange-600',
      [ItemRarity.GOLD]: 'from-yellow-400 to-yellow-600',
      [ItemRarity.GOLD_RED]: 'from-red-400 via-yellow-400 to-red-600'
    }
    return colors[rarity]
  }

  const getProgressPercentage = () => {
    if (slot.stage !== PlantingStage.GROWING) return 0
    return ((slot.growthTime - slot.remainingTime) / slot.growthTime) * 100
  }

  const renderSlotContent = () => {
    switch (slot.stage) {
      case PlantingStage.EMPTY:
        return (
          <div 
            className="w-full h-full bg-gradient-to-br from-amber-100 to-amber-200 border-2 border-dashed border-amber-400 rounded-xl flex items-center justify-center cursor-pointer hover:from-amber-200 hover:to-amber-300 transition-all duration-300 group"
            onClick={() => setShowCropSelector(true)}
          >
            <div className="text-center">
              <div className="text-4xl mb-2 group-hover:scale-110 transition-transform">🌱</div>
              <div className="text-xs text-amber-600 font-medium">点击种植</div>
            </div>
          </div>
        )

      case PlantingStage.GROWING:
        const product = getFuturesProductById(slot.cropId!)
        return (
          <div 
            className={`w-full h-full bg-gradient-to-br ${getRarityColor(slot.quality)} rounded-xl relative overflow-hidden cursor-pointer group`}
            onClick={() => setShowItemSelector(true)}
          >
            {/* 生长进度条 */}
            <div className="absolute bottom-0 left-0 w-full h-2 bg-black bg-opacity-20">
              <div 
                className="h-full bg-white bg-opacity-80 transition-all duration-1000"
                style={{ width: `${getProgressPercentage()}%` }}
              />
            </div>
            
            {/* 作物图标 */}
            <div className="flex flex-col items-center justify-center h-full text-white">
              <div className="text-4xl mb-2 animate-bounce">{product?.icon}</div>
              <div className="text-xs font-bold text-center px-2">
                {product?.name}
              </div>
              <div className="text-xs opacity-90">
                {formatTime(slot.remainingTime)}
              </div>
            </div>

            {/* 品质光环 */}
            <div className={`absolute inset-0 rounded-xl border-2 border-white border-opacity-50 ${slot.quality === ItemRarity.GOLD_RED ? 'animate-pulse' : ''}`} />
            
            {/* 生长粒子效果 */}
            <div className="absolute inset-0 pointer-events-none">
              {[...Array(5)].map((_, i) => (
                <div 
                  key={i}
                  className="absolute w-1 h-1 bg-white rounded-full opacity-50 animate-ping"
                  style={{
                    left: `${20 + i * 15}%`,
                    top: `${30 + (i % 2) * 20}%`,
                    animationDelay: `${i * 0.5}s`
                  }}
                />
              ))}
            </div>
          </div>
        )

      case PlantingStage.READY_HARVEST:
        const readyProduct = getFuturesProductById(slot.cropId!)
        return (
          <div 
            className={`w-full h-full bg-gradient-to-br ${getRarityColor(slot.quality)} rounded-xl relative cursor-pointer group overflow-hidden`}
            onClick={() => onHarvest(slot.id)}
          >
            {/* 成熟光芒效果 */}
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-30 animate-pulse" />
            
            {/* 闪烁边框 */}
            <div className="absolute inset-0 rounded-xl border-4 border-yellow-300 animate-pulse" />
            
            <div className="flex flex-col items-center justify-center h-full text-white relative z-10">
              <div className="text-5xl mb-2 animate-bounce">{readyProduct?.icon}</div>
              <div className="text-xs font-bold text-center px-2 mb-1">
                {readyProduct?.name}
              </div>
              <div className="text-xs bg-green-500 px-2 py-1 rounded-full font-bold animate-pulse">
                可收获！
              </div>
            </div>

            {/* 收获粒子效果 */}
            <div className="absolute inset-0 pointer-events-none">
              {[...Array(8)].map((_, i) => (
                <div 
                  key={i}
                  className="absolute w-2 h-2 bg-yellow-300 rounded-full animate-ping"
                  style={{
                    left: `${10 + i * 10}%`,
                    top: `${20 + (i % 3) * 20}%`,
                    animationDelay: `${i * 0.3}s`
                  }}
                />
              ))}
            </div>
          </div>
        )

      default:
        return null
    }
  }

  return (
    <div className="relative">
      <div className="w-24 h-24 sm:w-32 sm:h-32">
        {renderSlotContent()}
      </div>

      {/* 作物选择弹窗 */}
      {showCropSelector && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" onClick={() => setShowCropSelector(false)}>
          <div className="bg-white rounded-xl p-6 max-w-md max-h-96 overflow-auto" onClick={(e) => e.stopPropagation()}>
            <h3 className="text-lg font-bold mb-4">选择作物种植</h3>
            <div className="grid grid-cols-3 gap-3">
              {['corn', 'wheat', 'soybean', 'cotton', 'apple'].map(cropId => {
                const product = getFuturesProductById(cropId)
                return (
                  <button
                    key={cropId}
                    className="p-3 border-2 border-gray-200 rounded-lg hover:border-green-400 hover:bg-green-50 transition-all"
                    onClick={() => {
                      onPlant(slot.id, cropId)
                      setShowCropSelector(false)
                    }}
                  >
                    <div className="text-2xl mb-1">{product?.icon}</div>
                    <div className="text-xs text-gray-600">{product?.name}</div>
                  </button>
                )
              })}
            </div>
          </div>
        </div>
      )}

      {/* 道具选择弹窗 */}
      {showItemSelector && slot.stage === PlantingStage.GROWING && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" onClick={() => setShowItemSelector(false)}>
          <div className="bg-white rounded-xl p-6 max-w-md max-h-96 overflow-auto" onClick={(e) => e.stopPropagation()}>
            <h3 className="text-lg font-bold mb-4">使用道具</h3>
            <div className="space-y-2">
              {PLANTING_ITEMS.filter(item => item.type !== 'seed').map(item => (
                <button
                  key={item.id}
                  className="w-full p-3 border-2 border-gray-200 rounded-lg hover:border-blue-400 hover:bg-blue-50 transition-all text-left"
                  onClick={() => {
                    onUseItem(slot.id, item.id)
                    setShowItemSelector(false)
                  }}
                >
                  <div className="flex items-center">
                    <span className="text-2xl mr-3">{item.icon}</span>
                    <div>
                      <div className="font-medium">{item.name}</div>
                      <div className="text-xs text-gray-500">{item.description}</div>
                    </div>
                  </div>
                </button>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

// 收获结果弹窗
interface HarvestResultModalProps {
  result: HarvestResult | null
  onClose: () => void
}

const HarvestResultModal: React.FC<HarvestResultModalProps> = ({ result, onClose }) => {
  if (!result) return null

  const product = getFuturesProductById(result.cropId)
  const getRarityColor = (rarity: ItemRarity) => {
    const colors = {
      [ItemRarity.GRAY]: 'from-gray-400 to-gray-600',
      [ItemRarity.GREEN]: 'from-green-400 to-green-600',
      [ItemRarity.BLUE]: 'from-blue-400 to-blue-600',
      [ItemRarity.ORANGE]: 'from-orange-400 to-orange-600',
      [ItemRarity.GOLD]: 'from-yellow-400 to-yellow-600',
      [ItemRarity.GOLD_RED]: 'from-red-400 via-yellow-400 to-red-600'
    }
    return colors[rarity]
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center z-50" onClick={onClose}>
      <div 
        className={`bg-gradient-to-br ${getRarityColor(result.quality)} p-8 rounded-2xl max-w-sm mx-4 text-white relative overflow-hidden`}
        onClick={(e) => e.stopPropagation()}
      >
        {/* 庆祝粒子效果 */}
        <div className="absolute inset-0 pointer-events-none">
          {[...Array(12)].map((_, i) => (
            <div 
              key={i}
              className="absolute w-2 h-2 bg-white rounded-full animate-ping"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                animationDelay: `${Math.random() * 2}s`
              }}
            />
          ))}
        </div>

        <div className="text-center relative z-10">
          <div className="text-6xl mb-4 animate-bounce">{product?.icon}</div>
          
          {result.isNewCollection && (
            <div className="mb-2 text-yellow-200 font-bold text-sm animate-pulse">
              🆕 新收集！
            </div>
          )}
          
          {result.isRareGet && (
            <div className="mb-2 text-yellow-200 font-bold text-sm animate-pulse">
              ✨ 稀有获得！
            </div>
          )}

          <h2 className="text-2xl font-bold mb-2">{product?.name}</h2>
          <div className="text-lg mb-4">收获 {result.quantity} 单位</div>
          
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span>经验值:</span>
              <span className="text-yellow-200 font-bold">+{result.experience}</span>
            </div>
            <div className="flex justify-between">
              <span>金币:</span>
              <span className="text-yellow-200 font-bold">+{result.coins}</span>
            </div>
          </div>

          <button 
            className="mt-6 bg-white bg-opacity-20 hover:bg-opacity-30 px-6 py-2 rounded-full font-bold transition-all"
            onClick={onClose}
          >
            确定
          </button>
        </div>
      </div>
    </div>
  )
}

// 主种植系统组件
export const PlantingSystem: React.FC = () => {
  const [manager] = useState(() => new PlantingSystemManager())
  const [farmData, setFarmData] = useState(() => manager.getFarmData())
  const [harvestResult, setHarvestResult] = useState<HarvestResult | null>(null)
  const [currentTab, setCurrentTab] = useState<'farm' | 'collection' | 'achievements'>('farm')

  // 更新农场数据
  const updateFarmData = useCallback(() => {
    setFarmData(manager.getFarmData())
  }, [manager])

  useEffect(() => {
    manager.addUpdateCallback(updateFarmData)
    return () => {
      manager.removeUpdateCallback(updateFarmData)
      manager.destroy()
    }
  }, [manager, updateFarmData])

  const handlePlant = async (slotId: string, cropId: string) => {
    const basicSeed = getPlantingItem('basic_seed')
    if (basicSeed) {
      await manager.plantCrop(slotId, cropId, basicSeed)
    }
  }

  const handleHarvest = async (slotId: string) => {
    const result = await manager.harvestCrop(slotId)
    if (result) {
      setHarvestResult(result)
    }
  }

  const handleUseItem = (slotId: string, itemId: string) => {
    // manager.useItem(slotId, itemId)
  }

  const renderFarmView = () => (
    <div className="space-y-6">
      {/* 农场网格 */}
      <div className="bg-gradient-to-br from-green-100 to-green-200 p-6 rounded-2xl">
        <h2 className="text-xl font-bold text-green-800 mb-4 text-center">🚜 我的农场</h2>
        <div className="grid grid-cols-3 gap-4 max-w-md mx-auto">
          {farmData.slots.map(slot => (
            <PlantingSlotComponent
              key={slot.id}
              slot={slot}
              onPlant={handlePlant}
              onHarvest={handleHarvest}
              onUseItem={handleUseItem}
            />
          ))}
        </div>
      </div>

      {/* 道具背包 */}
      <div className="bg-white p-4 rounded-xl border-2 border-gray-200">
        <h3 className="font-bold text-gray-800 mb-3">🎒 道具背包</h3>
        <div className="grid grid-cols-4 gap-2">
          {Object.entries(farmData.inventory).map(([itemId, quantity]) => {
            const item = getPlantingItem(itemId)
            return (
              <div key={itemId} className="text-center p-2 bg-gray-50 rounded-lg">
                <div className="text-xl mb-1">{item?.icon}</div>
                <div className="text-xs text-gray-600">{item?.name}</div>
                <div className="text-xs font-bold text-blue-600">{quantity}</div>
              </div>
            )
          })}
        </div>
      </div>
    </div>
  )

  const renderCollectionView = () => (
    <div className="bg-white p-4 rounded-xl border-2 border-gray-200">
      <h2 className="text-xl font-bold text-gray-800 mb-4">📚 收集册</h2>
      <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
        {Object.values(farmData.collection).map(entry => {
          const product = getFuturesProductById(entry.cropId)
          const getRarityColor = (rarity: ItemRarity) => {
            const colors = {
              [ItemRarity.GRAY]: 'border-gray-400',
              [ItemRarity.GREEN]: 'border-green-400',
              [ItemRarity.BLUE]: 'border-blue-400',
              [ItemRarity.ORANGE]: 'border-orange-400',
              [ItemRarity.GOLD]: 'border-yellow-400',
              [ItemRarity.GOLD_RED]: 'border-red-400'
            }
            return colors[rarity]
          }

          return (
            <div 
              key={`${entry.cropId}_${entry.quality}`}
              className={`p-3 border-2 ${getRarityColor(entry.quality)} rounded-lg relative ${entry.isNew ? 'animate-pulse' : ''}`}
            >
              {entry.isNew && (
                <div className="absolute -top-1 -right-1 bg-red-500 text-white text-xs px-1 rounded-full">
                  NEW
                </div>
              )}
              <div className="text-center">
                <div className="text-2xl mb-1">{product?.icon}</div>
                <div className="text-xs font-bold">{product?.name}</div>
                <div className="text-xs text-gray-500">收获 {entry.totalHarvested} 次</div>
                <div className="text-xs text-blue-600">最佳: {entry.bestYield}</div>
              </div>
            </div>
          )
        })}
      </div>
    </div>
  )

  const renderAchievementsView = () => (
    <div className="bg-white p-4 rounded-xl border-2 border-gray-200">
      <h2 className="text-xl font-bold text-gray-800 mb-4">🏆 成就系统</h2>
      <div className="space-y-3">
        {farmData.achievements.map(achievement => (
          <div 
            key={achievement.id}
            className={`p-4 rounded-lg border-2 ${achievement.isCompleted ? 'border-yellow-400 bg-yellow-50' : 'border-gray-200'}`}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <span className="text-2xl mr-3">{achievement.icon}</span>
                <div>
                  <div className="font-bold">{achievement.name}</div>
                  <div className="text-sm text-gray-600">{achievement.description}</div>
                  {!achievement.isCompleted && (
                    <div className="text-xs text-blue-600">
                      进度: {achievement.progress}/{achievement.requirements.target}
                    </div>
                  )}
                </div>
              </div>
              {achievement.isCompleted && (
                <div className="text-green-600 font-bold">✅</div>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  )

  return (
    <div className="max-w-4xl mx-auto p-4">
      {/* 头部状态栏 */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-4 rounded-xl mb-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold">🌾 农场大师</h1>
            <div className="text-sm opacity-90">等级 {farmData.level} | 经验值 {farmData.experience}</div>
          </div>
          <div className="text-right">
            <div className="text-lg font-bold">💰 {farmData.coins}</div>
            <div className="text-sm opacity-90">收集进度: {Object.keys(farmData.collection).length}/78</div>
          </div>
        </div>
      </div>

      {/* 标签页导航 */}
      <div className="flex space-x-2 mb-6">
        {[
          { key: 'farm', label: '🚜 农场', },
          { key: 'collection', label: '📚 收集册' },
          { key: 'achievements', label: '🏆 成就' }
        ].map(tab => (
          <button
            key={tab.key}
            className={`px-4 py-2 rounded-lg font-medium transition-all ${
              currentTab === tab.key 
                ? 'bg-blue-600 text-white' 
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
            onClick={() => setCurrentTab(tab.key as any)}
          >
            {tab.label}
          </button>
        ))}
      </div>

      {/* 内容区域 */}
      {currentTab === 'farm' && renderFarmView()}
      {currentTab === 'collection' && renderCollectionView()}
      {currentTab === 'achievements' && renderAchievementsView()}

      {/* 收获结果弹窗 */}
      <HarvestResultModal 
        result={harvestResult}
        onClose={() => setHarvestResult(null)}
      />
    </div>
  )
} 