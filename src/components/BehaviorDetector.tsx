import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react'
import { SelfDisciplineType } from '../data/cropSpecifications'
import { usePerformanceTracking } from '../utils/performance/ReactPerformanceAnalyzer'

interface BehaviorDetectorProps {
  onBehaviorDetected: (behaviorType: SelfDisciplineType, intensity: number) => void
  onFocusScoreUpdate: (score: number) => void
  isActive: boolean
  selectedBehaviorType?: SelfDisciplineType
}

interface DetectionState {
  isDetecting: boolean
  currentBehavior?: SelfDisciplineType
  intensity: number
  focusScore: number
  sessionDuration: number
  lastActivity: number
}

interface ActivityPattern {
  type: string
  timestamp: number
  intensity: number
  duration: number
}

/**
 * 行为检测器组件 - 优化版本
 * 模拟实时行为检测和专注度追踪
 */
export const BehaviorDetector: React.FC<BehaviorDetectorProps> = React.memo(({
  onBehaviorDetected,
  onFocusScoreUpdate,
  isActive,
  selectedBehaviorType
}) => {
  usePerformanceTracking('BehaviorDetector')

  const [detectionState, setDetectionState] = useState<DetectionState>({
    isDetecting: false,
    intensity: 0,
    focusScore: 0.5,
    sessionDuration: 0,
    lastActivity: Date.now()
  })

  const [activityLog, setActivityLog] = useState<ActivityPattern[]>([])
  const intervalRef = useRef<number | null>(null)
  const detectionIntervalRef = useRef<number | null>(null)

  // 行为类型的中文名称 - 使用useMemo优化
  const behaviorTypeNames: Record<SelfDisciplineType, string> = useMemo(() => ({
    [SelfDisciplineType.LEARNING]: '学习活动',
    [SelfDisciplineType.EXERCISE]: '体力锻炼',
    [SelfDisciplineType.TIME_MANAGEMENT]: '时间管理',
    [SelfDisciplineType.MEDITATION]: '冥想练习',
    [SelfDisciplineType.DEEP_FOCUS]: '深度专注',
    [SelfDisciplineType.READING]: '阅读习惯',
    [SelfDisciplineType.SOCIAL_INTERACTION]: '社交互动'
  }), [])

  // 使用useCallback优化模拟检测函数
  const simulateBehaviorDetection = useCallback(() => {
    if (!selectedBehaviorType) return { behavior: null, intensity: 0, focusScore: 0 }

    // 模拟算法逻辑 - 缓存复杂计算
    const baseIntensity = Math.random() * 0.3 + 0.2
    const behaviorFactor = selectedBehaviorType === SelfDisciplineType.MEDITATION ? 0.8 : 1.0
    const timeFactor = Math.sin(Date.now() / 10000) * 0.2 + 0.8
    
    const intensity = Math.min(1, baseIntensity * behaviorFactor * timeFactor)
    const focusScore = Math.min(1, intensity * 0.9 + Math.random() * 0.2)

    return {
      behavior: selectedBehaviorType,
      intensity,
      focusScore
    }
  }, [selectedBehaviorType])

  // 优化后的格式化函数
  const formatDuration = useCallback((ms: number): string => {
    const seconds = Math.floor(ms / 1000)
    const minutes = Math.floor(seconds / 60)
    const hours = Math.floor(minutes / 60)

    if (hours > 0) {
      return `${hours}:${(minutes % 60).toString().padStart(2, '0')}:${(seconds % 60).toString().padStart(2, '0')}`
    }
    return `${minutes}:${(seconds % 60).toString().padStart(2, '0')}`
  }, [])

  // 优化颜色计算
  const getIntensityColor = useCallback((intensity: number): string => {
    if (intensity < 0.3) return '#607d8b'
    if (intensity < 0.5) return '#ff9800'
    if (intensity < 0.7) return '#2196f3'
    return '#4caf50'
  }, [])

  const getFocusColor = useCallback((focusScore: number): string => {
    if (focusScore < 0.3) return '#f44336'
    if (focusScore < 0.6) return '#ff9800'
    if (focusScore < 0.8) return '#2196f3'
    return '#4caf50'
  }, [])

  // 计算活动统计 - 使用useMemo优化
  const activityStats = useMemo(() => {
    const recentActivity = activityLog.slice(-10)
    const avgIntensity = recentActivity.length > 0 
      ? recentActivity.reduce((sum, activity) => sum + activity.intensity, 0) / recentActivity.length 
      : 0

    return {
      recentActivity,
      avgIntensity,
      totalActivities: activityLog.length
    }
  }, [activityLog])

  // 主要检测逻辑 - 优化内存使用
  useEffect(() => {
    if (isActive && selectedBehaviorType) {
      setDetectionState(prev => ({
        ...prev,
        isDetecting: true,
        currentBehavior: selectedBehaviorType,
        sessionDuration: 0
      }))

      // 主要检测循环 - 降低频率到2秒
      intervalRef.current = window.setInterval(() => {
        const detection = simulateBehaviorDetection()
        
        setDetectionState(prev => {
          const newState = {
            ...prev,
            intensity: detection.intensity,
            focusScore: detection.focusScore,
            sessionDuration: prev.sessionDuration + 2000, // 调整为2秒
            lastActivity: detection.intensity > 0.1 ? Date.now() : prev.lastActivity
          }

          return newState
        })

        // 回调通知 - 减少调用频率
        if (detection.behavior && detection.intensity > 0.3) {
          onBehaviorDetected(detection.behavior, detection.intensity)
        }
        onFocusScoreUpdate(detection.focusScore)

        // 记录活动模式 - 减少记录频率
        if (detection.intensity > 0.2) {
          setActivityLog(prev => {
            const newLog = [...prev, {
              type: selectedBehaviorType,
              timestamp: Date.now(),
              intensity: detection.intensity,
              duration: 2000
            }].slice(-50) // 减少到最近50个记录

            return newLog
          })
        }
      }, 2000) // 调整为2秒间隔

      // 移除细粒度检测循环，减少CPU使用
    } else {
      setDetectionState(prev => ({
        ...prev,
        isDetecting: false,
        currentBehavior: undefined,
        intensity: 0
      }))

      if (intervalRef.current) {
        clearInterval(intervalRef.current)
        intervalRef.current = null
      }
    }

    return () => {
      if (intervalRef.current) clearInterval(intervalRef.current)
      if (detectionIntervalRef.current) clearInterval(detectionIntervalRef.current)
    }
  }, [isActive, selectedBehaviorType, simulateBehaviorDetection, onBehaviorDetected, onFocusScoreUpdate])

  // 渲染优化 - 条件渲染和简化样式
  if (!isActive || !selectedBehaviorType) {
    return (
      <div style={{ 
        padding: '20px', 
        textAlign: 'center', 
        color: '#999',
        fontSize: '14px'
      }}>
        未启用行为检测
      </div>
    )
  }

  return (
    <div style={{ 
      padding: '16px', 
      backgroundColor: '#f8f9fa', 
      borderRadius: '8px',
      maxWidth: '400px'
    }}>
      {/* 状态显示 - 简化版本 */}
      <div style={{ marginBottom: '12px' }}>
        <h4 style={{ margin: '0 0 8px 0', fontSize: '16px' }}>
          行为检测: {behaviorTypeNames[selectedBehaviorType]}
        </h4>
        <div style={{ fontSize: '12px', color: '#666' }}>
          状态: {detectionState.isDetecting ? '检测中' : '未检测'} | 
          会话时长: {formatDuration(detectionState.sessionDuration)}
        </div>
      </div>

      {/* 实时指标 - 优化版本 */}
      <div style={{ marginBottom: '12px' }}>
        <div style={{ marginBottom: '8px' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '4px' }}>
            <span style={{ fontSize: '12px' }}>强度</span>
            <span style={{ fontSize: '12px', fontWeight: 'bold' }}>
              {(detectionState.intensity * 100).toFixed(0)}%
            </span>
          </div>
          <div style={{
            width: '100%',
            height: '6px',
            backgroundColor: '#e0e0e0',
            borderRadius: '3px',
            overflow: 'hidden'
          }}>
            <div
              style={{
                width: `${detectionState.intensity * 100}%`,
                height: '100%',
                backgroundColor: getIntensityColor(detectionState.intensity),
                transition: 'width 0.5s ease'
              }}
            />
          </div>
        </div>

        <div>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '4px' }}>
            <span style={{ fontSize: '12px' }}>专注度</span>
            <span style={{ fontSize: '12px', fontWeight: 'bold' }}>
              {(detectionState.focusScore * 100).toFixed(0)}%
            </span>
          </div>
          <div style={{
            width: '100%',
            height: '6px',
            backgroundColor: '#e0e0e0',
            borderRadius: '3px',
            overflow: 'hidden'
          }}>
            <div
              style={{
                width: `${detectionState.focusScore * 100}%`,
                height: '100%',
                backgroundColor: getFocusColor(detectionState.focusScore),
                transition: 'width 0.5s ease'
              }}
            />
          </div>
        </div>
      </div>

      {/* 活动统计 - 简化版本 */}
      {activityStats.totalActivities > 0 && (
        <div style={{ fontSize: '11px', color: '#666' }}>
          <div>平均强度: {(activityStats.avgIntensity * 100).toFixed(0)}%</div>
          <div>活动记录: {activityStats.totalActivities}</div>
        </div>
      )}
    </div>
  )
})

BehaviorDetector.displayName = 'BehaviorDetector' 