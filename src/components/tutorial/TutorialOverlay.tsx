import React from 'react'

interface TutorialStep {
  id: string
  title: string
  content: string
  target?: string
  position?: 'top' | 'bottom' | 'left' | 'right' | 'center'
}

interface TutorialOverlayProps {
  isVisible: boolean
  currentStep: TutorialStep | null
  currentIndex: number
  totalSteps: number
  onNext: () => void
  onPrevious: () => void
  onSkip: () => void
  onClose: () => void
  className?: string
}

export const TutorialOverlay: React.FC<TutorialOverlayProps> = ({
  isVisible,
  currentStep,
  currentIndex,
  totalSteps,
  onNext,
  onPrevious,
  onSkip,
  onClose,
  className = ''
}) => {
  if (!isVisible || !currentStep) {
    return null
  }

  const isFirstStep = currentIndex === 0
  const isLastStep = currentIndex === totalSteps - 1

  return (
    <>
      {/* 背景遮罩 */}
      <div
        style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'rgba(0, 0, 0, 0.6)',
          zIndex: 10000,
          backdropFilter: 'blur(2px)'
        }}
        onClick={onClose}
      />
      
      {/* 教程内容 */}
      <div
        className={`tutorial-overlay ${className}`}
        style={{
          position: 'fixed',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          zIndex: 10001,
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          color: 'white',
          padding: '24px',
          borderRadius: '16px',
          minWidth: '320px',
          maxWidth: '450px',
          boxShadow: '0 20px 60px rgba(0, 0, 0, 0.3)',
          animation: 'tutorialFadeIn 0.3s ease-out'
        }}
        onClick={(e) => e.stopPropagation()}
      >
        {/* 头部 */}
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '16px'
        }}>
          <div style={{
            background: 'rgba(255, 255, 255, 0.2)',
            padding: '4px 12px',
            borderRadius: '12px',
            fontSize: '12px',
            fontWeight: 'bold'
          }}>
            {currentIndex + 1} / {totalSteps}
          </div>
          
          <button
            onClick={onClose}
            style={{
              background: 'rgba(255, 255, 255, 0.2)',
              border: 'none',
              color: 'white',
              borderRadius: '50%',
              width: '28px',
              height: '28px',
              cursor: 'pointer',
              fontSize: '14px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
          >
            ✕
          </button>
        </div>
        
        {/* 标题 */}
        <h3 style={{
          margin: '0 0 16px 0',
          fontSize: '20px',
          fontWeight: 'bold'
        }}>
          {currentStep.title}
        </h3>
        
        {/* 内容 */}
        <div style={{
          fontSize: '14px',
          lineHeight: '1.6',
          marginBottom: '24px',
          opacity: 0.95
        }}>
          {currentStep.content}
        </div>
        
        {/* 进度条 */}
        <div style={{
          background: 'rgba(255, 255, 255, 0.2)',
          height: '4px',
          borderRadius: '2px',
          marginBottom: '20px',
          overflow: 'hidden'
        }}>
          <div style={{
            background: 'rgba(255, 255, 255, 0.8)',
            height: '100%',
            width: `${((currentIndex + 1) / totalSteps) * 100}%`,
            borderRadius: '2px',
            transition: 'width 0.3s ease'
          }} />
        </div>
        
        {/* 按钮组 */}
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          gap: '12px'
        }}>
          <div style={{ display: 'flex', gap: '8px' }}>
            {!isFirstStep && (
              <button
                onClick={onPrevious}
                style={{
                  background: 'rgba(255, 255, 255, 0.2)',
                  color: 'white',
                  border: 'none',
                  borderRadius: '6px',
                  padding: '8px 16px',
                  fontSize: '13px',
                  cursor: 'pointer',
                  fontWeight: '500'
                }}
              >
                ← 上一步
              </button>
            )}
            
            <button
              onClick={onSkip}
              style={{
                background: 'rgba(255, 255, 255, 0.1)',
                color: 'rgba(255, 255, 255, 0.8)',
                border: 'none',
                borderRadius: '6px',
                padding: '8px 16px',
                fontSize: '13px',
                cursor: 'pointer',
                fontWeight: '500'
              }}
            >
              跳过教程
            </button>
          </div>
          
          <button
            onClick={isLastStep ? onClose : onNext}
            style={{
              background: 'rgba(255, 255, 255, 0.9)',
              color: '#667eea',
              border: 'none',
              borderRadius: '6px',
              padding: '8px 20px',
              fontSize: '13px',
              cursor: 'pointer',
              fontWeight: 'bold'
            }}
          >
            {isLastStep ? '完成 ✓' : '下一步 →'}
          </button>
        </div>
      </div>
      
      <style>{`
        @keyframes tutorialFadeIn {
          from {
            opacity: 0;
            transform: translate(-50%, -50%) scale(0.9);
          }
          to {
            opacity: 1;
            transform: translate(-50%, -50%) scale(1);
          }
        }
      `}</style>
    </>
  )
}

export default TutorialOverlay 