import React, { useState, useEffect } from 'react'
import { TutorialStepProps } from '../../types/tutorial'

interface PlantData {
  plantType: string
  icon: string
  name: string
  description: string
}

const plantTypeData: { [key: string]: PlantData } = {
  knowledge: {
    plantType: 'knowledge',
    icon: '🌸',
    name: '知识花',
    description: '通过专注学习培养的美丽花朵'
  },
  strength: {
    plantType: 'strength',
    icon: '🌳',
    name: '力量树',
    description: '通过运动锻炼培养的强壮大树'
  },
  time: {
    plantType: 'time',
    icon: '🥬',
    name: '时间菜',
    description: '通过时间管理培养的新鲜蔬菜'
  },
  meditation: {
    plantType: 'meditation',
    icon: '🪷',
    name: '冥想莲',
    description: '通过冥想练习培养的圣洁莲花'
  }
}

export const PlantingCelebration: React.FC<TutorialStepProps> = ({
  step,
  onNext,
  onFinish,
  currentIndex,
  totalSteps
}) => {
  const [animationPhase, setAnimationPhase] = useState<'entering' | 'celebrating' | 'exiting'>('entering')
  const [showFireworks, setShowFireworks] = useState(false)

  const plantType = step.actionData?.plantType || 'knowledge'
  const plantData = plantTypeData[plantType]

  useEffect(() => {
    // 动画序列
    const sequence = async () => {
      // 入场动画
      await new Promise(resolve => setTimeout(resolve, 500))
      setAnimationPhase('celebrating')
      
      // 烟花效果
      setTimeout(() => setShowFireworks(true), 1000)
      
      // 庆祝持续时间
      await new Promise(resolve => setTimeout(resolve, 4000))
      
      // 退场动画
      setAnimationPhase('exiting')
      await new Promise(resolve => setTimeout(resolve, 1000))
    }

    sequence()
  }, [])

  const handleContinue = () => {
    if (currentIndex === totalSteps - 1) {
      onFinish()
    } else {
      onNext()
    }
  }

  return (
    <div className="planting-celebration" style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      background: 'linear-gradient(135deg, rgba(76, 175, 80, 0.9) 0%, rgba(139, 195, 74, 0.9) 100%)',
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'center',
      alignItems: 'center',
      zIndex: 20000,
      color: 'white',
      fontFamily: 'system-ui, -apple-system, sans-serif',
      animation: animationPhase === 'entering' ? 'celebrationEnter 0.5s ease-out' :
                 animationPhase === 'exiting' ? 'celebrationExit 1s ease-in' : 'none'
    }}>
      {/* 背景装饰 */}
      <div style={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        background: `radial-gradient(circle at 50% 50%, rgba(255, 255, 255, 0.1) 0%, transparent 70%)`,
        animation: animationPhase === 'celebrating' ? 'glowPulse 2s ease-in-out infinite' : 'none'
      }} />

      {/* 烟花效果 */}
      {showFireworks && (
        <div className="fireworks-container" style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          pointerEvents: 'none'
        }}>
          {[...Array(8)].map((_, i) => (
            <div
              key={i}
              className="firework"
              style={{
                position: 'absolute',
                left: `${20 + (i * 10)}%`,
                top: `${20 + (i % 3) * 20}%`,
                fontSize: '24px',
                animation: `firework 2s ease-out ${i * 0.2}s infinite`
              }}
            >
              ✨
            </div>
          ))}
        </div>
      )}

      {/* 主要内容 */}
      <div style={{
        textAlign: 'center',
        zIndex: 1,
        animation: animationPhase === 'celebrating' ? 'contentBounce 1s ease-out' : 'none'
      }}>
        {/* 植物图标 */}
        <div style={{
          fontSize: '120px',
          marginBottom: '20px',
          animation: animationPhase === 'celebrating' ? 'plantGrow 1.5s ease-out' : 'none',
          filter: 'drop-shadow(0 8px 16px rgba(0, 0, 0, 0.3))'
        }}>
          {plantData.icon}
        </div>

        {/* 庆祝标题 */}
        <h1 style={{
          fontSize: '48px',
          fontWeight: 'bold',
          margin: '0 0 16px 0',
          textShadow: '0 4px 8px rgba(0, 0, 0, 0.3)',
          animation: animationPhase === 'celebrating' ? 'titleFadeIn 1s ease-out 0.5s both' : 'none'
        }}>
          🎉 恭喜！🎉
        </h1>

        {/* 成就描述 */}
        <div style={{
          fontSize: '24px',
          marginBottom: '32px',
          animation: animationPhase === 'celebrating' ? 'titleFadeIn 1s ease-out 1s both' : 'none'
        }}>
          <div style={{ marginBottom: '12px' }}>
            你成功培养了第一株 <strong>{plantData.name}</strong>！
          </div>
          <div style={{ 
            fontSize: '18px', 
            opacity: 0.9,
            fontStyle: 'italic'
          }}>
            {plantData.description}
          </div>
        </div>

        {/* 鼓励文字 */}
        <div style={{
          fontSize: '20px',
          marginBottom: '40px',
          background: 'rgba(255, 255, 255, 0.2)',
          padding: '16px 24px',
          borderRadius: '12px',
          backdropFilter: 'blur(10px)',
          animation: animationPhase === 'celebrating' ? 'titleFadeIn 1s ease-out 1.5s both' : 'none'
        }}>
          专注的力量让你的农场开始繁荣！<br />
          继续保持良好的习惯，培养更多美丽的植物吧！
        </div>

        {/* 继续按钮 */}
        <button
          onClick={handleContinue}
          style={{
            background: 'rgba(255, 255, 255, 0.9)',
            color: '#4CAF50',
            border: 'none',
            borderRadius: '50px',
            padding: '16px 32px',
            fontSize: '18px',
            fontWeight: 'bold',
            cursor: 'pointer',
            boxShadow: '0 4px 16px rgba(0, 0, 0, 0.2)',
            transition: 'all 0.3s ease',
            animation: animationPhase === 'celebrating' ? 'titleFadeIn 1s ease-out 2s both' : 'none'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.transform = 'scale(1.05)'
            e.currentTarget.style.boxShadow = '0 6px 20px rgba(0, 0, 0, 0.3)'
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.transform = 'scale(1)'
            e.currentTarget.style.boxShadow = '0 4px 16px rgba(0, 0, 0, 0.2)'
          }}
        >
          🌱 继续探索农场 →
        </button>
      </div>

      {/* 装饰元素 */}
      <div style={{
        position: 'absolute',
        bottom: '10%',
        left: '50%',
        transform: 'translateX(-50%)',
        fontSize: '14px',
        opacity: 0.8,
        animation: animationPhase === 'celebrating' ? 'titleFadeIn 1s ease-out 2.5s both' : 'none'
      }}>
        ✨ 你的专注之旅才刚刚开始 ✨
      </div>

      <style>{`
        @keyframes celebrationEnter {
          from {
            opacity: 0;
            transform: scale(0.8);
          }
          to {
            opacity: 1;
            transform: scale(1);
          }
        }

        @keyframes celebrationExit {
          from {
            opacity: 1;
            transform: scale(1);
          }
          to {
            opacity: 0;
            transform: scale(1.1);
          }
        }

        @keyframes glowPulse {
          0%, 100% {
            opacity: 0.5;
          }
          50% {
            opacity: 0.8;
          }
        }

        @keyframes contentBounce {
          0% {
            transform: translateY(30px);
            opacity: 0;
          }
          60% {
            transform: translateY(-10px);
            opacity: 1;
          }
          100% {
            transform: translateY(0);
            opacity: 1;
          }
        }

        @keyframes plantGrow {
          0% {
            transform: scale(0.3) rotate(-10deg);
            opacity: 0;
          }
          50% {
            transform: scale(1.2) rotate(5deg);
            opacity: 0.8;
          }
          100% {
            transform: scale(1) rotate(0deg);
            opacity: 1;
          }
        }

        @keyframes titleFadeIn {
          from {
            opacity: 0;
            transform: translateY(20px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        @keyframes firework {
          0% {
            transform: scale(0) rotate(0deg);
            opacity: 1;
          }
          50% {
            transform: scale(1.5) rotate(180deg);
            opacity: 0.8;
          }
          100% {
            transform: scale(0.5) rotate(360deg);
            opacity: 0;
          }
        }
      `}</style>
    </div>
  )
}

export default PlantingCelebration 