import React, { useState, useEffect, createContext, useContext } from 'react';
import { theme } from '../../styles/theme';

export interface ToastProps {
  id: string;
  title?: string;
  message: string;
  type?: 'info' | 'success' | 'warning' | 'error';
  duration?: number; // 自动关闭时间（毫秒），0 表示不自动关闭
  position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right' | 'top-center' | 'bottom-center';
  showCloseButton?: boolean;
  onClose?: () => void;
  onClick?: () => void;
  className?: string;
}

export interface ToastContextType {
  toasts: ToastProps[];
  addToast: (toast: Omit<ToastProps, 'id'>) => string;
  removeToast: (id: string) => void;
  clearToasts: () => void;
}

const ToastContext = createContext<ToastContextType | undefined>(undefined);

export const useToast = () => {
  const context = useContext(ToastContext);
  if (!context) {
    throw new Error('useToast 必须在 ToastProvider 内使用');
  }
  return context;
};

// 单个 Toast 组件
const Toast: React.FC<ToastProps & { onRemove: () => void }> = ({
  id,
  title,
  message,
  type = 'info',
  duration = 5000,
  showCloseButton = true,
  onClose,
  onClick,
  onRemove,
  className = '',
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isRemoving, setIsRemoving] = useState(false);

  // 获取类型样式
  const getTypeStyles = () => {
    switch (type) {
      case 'success':
        return {
          backgroundColor: theme.colors.success[50],
          borderColor: theme.colors.success[200],
          iconColor: theme.colors.success[500],
          icon: '✅',
        };
      case 'warning':
        return {
          backgroundColor: theme.colors.warning[50],
          borderColor: theme.colors.warning[200],
          iconColor: theme.colors.warning[500],
          icon: '⚠️',
        };
      case 'error':
        return {
          backgroundColor: theme.colors.error[50],
          borderColor: theme.colors.error[200],
          iconColor: theme.colors.error[500],
          icon: '❌',
        };
      default:
        return {
          backgroundColor: theme.colors.primary[50],
          borderColor: theme.colors.primary[200],
          iconColor: theme.colors.primary[500],
          icon: 'ℹ️',
        };
    }
  };

  const typeStyles = getTypeStyles();

  // 显示动画
  useEffect(() => {
    setIsVisible(true);
  }, []);

  // 自动关闭
  useEffect(() => {
    if (duration > 0) {
      const timer = setTimeout(() => {
        handleClose();
      }, duration);
      return () => clearTimeout(timer);
    }
  }, [duration]);

  const handleClose = () => {
    setIsRemoving(true);
    setTimeout(() => {
      onClose?.();
      onRemove();
    }, 300);
  };

  const handleClick = () => {
    onClick?.();
  };

  return (
    <div
      className={className}
      style={{
        backgroundColor: typeStyles.backgroundColor,
        border: `1px solid ${typeStyles.borderColor}`,
        borderRadius: theme.borderRadius.lg,
        padding: theme.spacing[4],
        marginBottom: theme.spacing[3],
        boxShadow: theme.boxShadow.md,
        display: 'flex',
        alignItems: 'flex-start',
        gap: theme.spacing[3],
        cursor: onClick ? 'pointer' : 'default',
        transform: isRemoving 
          ? 'translateX(100%) scale(0.8)' 
          : isVisible 
            ? 'translateX(0) scale(1)' 
            : 'translateX(100%) scale(0.8)',
        opacity: isRemoving ? 0 : isVisible ? 1 : 0,
        transition: 'all 0.3s ease-in-out',
        maxWidth: '400px',
        position: 'relative',
      }}
      onClick={handleClick}
    >
      {/* 图标 */}
      <div
        style={{
          fontSize: '20px',
          color: typeStyles.iconColor,
          flexShrink: 0,
        }}
      >
        {typeStyles.icon}
      </div>

      {/* 内容 */}
      <div style={{ flex: 1, minWidth: 0 }}>
        {title && (
          <div
            style={{
              fontSize: theme.typography.fontSize.sm,
              fontWeight: theme.typography.fontWeight.semibold,
              color: theme.colors.text.primary,
              marginBottom: theme.spacing[1],
            }}
          >
            {title}
          </div>
        )}
        <div
          style={{
            fontSize: theme.typography.fontSize.sm,
            color: theme.colors.text.secondary,
            lineHeight: theme.typography.lineHeight.normal,
            wordBreak: 'break-word',
          }}
        >
          {message}
        </div>
      </div>

      {/* 关闭按钮 */}
      {showCloseButton && (
        <button
          onClick={(e) => {
            e.stopPropagation();
            handleClose();
          }}
          style={{
            background: 'none',
            border: 'none',
            fontSize: '18px',
            cursor: 'pointer',
            color: theme.colors.text.secondary,
            borderRadius: theme.borderRadius.base,
            width: '24px',
            height: '24px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            transition: theme.transition.fast,
            flexShrink: 0,
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.backgroundColor = theme.colors.gray[200];
            e.currentTarget.style.color = theme.colors.text.primary;
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.backgroundColor = 'transparent';
            e.currentTarget.style.color = theme.colors.text.secondary;
          }}
          aria-label="关闭通知"
        >
          ×
        </button>
      )}

      {/* 进度条（如果有自动关闭时间） */}
      {duration > 0 && (
        <div
          style={{
            position: 'absolute',
            bottom: 0,
            left: 0,
            height: '3px',
            backgroundColor: typeStyles.iconColor,
            borderRadius: theme.borderRadius.base,
            animation: `toastProgress ${duration}ms linear`,
          }}
        />
      )}

      <style>{`
        @keyframes toastProgress {
          from {
            width: 100%;
          }
          to {
            width: 0%;
          }
        }
      `}</style>
    </div>
  );
};

// Toast 容器组件
export interface ToastContainerProps {
  position?: ToastProps['position'];
  className?: string;
}

export const ToastContainer: React.FC<ToastContainerProps> = ({
  position = 'top-right',
  className = '',
}) => {
  const { toasts, removeToast } = useToast();

  // 获取位置样式
  const getPositionStyles = () => {
    const baseStyles = {
      position: 'fixed' as const,
      zIndex: theme.zIndex.toast,
      maxHeight: '100vh',
      overflow: 'auto',
      pointerEvents: 'none' as const,
    };

    switch (position) {
      case 'top-left':
        return {
          ...baseStyles,
          top: theme.spacing[4],
          left: theme.spacing[4],
        };
      case 'top-center':
        return {
          ...baseStyles,
          top: theme.spacing[4],
          left: '50%',
          transform: 'translateX(-50%)',
        };
      case 'top-right':
        return {
          ...baseStyles,
          top: theme.spacing[4],
          right: theme.spacing[4],
        };
      case 'bottom-left':
        return {
          ...baseStyles,
          bottom: theme.spacing[4],
          left: theme.spacing[4],
        };
      case 'bottom-center':
        return {
          ...baseStyles,
          bottom: theme.spacing[4],
          left: '50%',
          transform: 'translateX(-50%)',
        };
      case 'bottom-right':
        return {
          ...baseStyles,
          bottom: theme.spacing[4],
          right: theme.spacing[4],
        };
      default:
        return {
          ...baseStyles,
          top: theme.spacing[4],
          right: theme.spacing[4],
        };
    }
  };

  if (toasts.length === 0) return null;

  return (
    <div className={className} style={getPositionStyles()}>
      {toasts.map((toast) => (
        <div key={toast.id} style={{ pointerEvents: 'auto' }}>
          <Toast
            {...toast}
            onRemove={() => removeToast(toast.id)}
          />
        </div>
      ))}
    </div>
  );
};

// Toast Provider 组件
export interface ToastProviderProps {
  children: React.ReactNode;
  maxToasts?: number;
}

export const ToastProvider: React.FC<ToastProviderProps> = ({
  children,
  maxToasts = 5,
}) => {
  const [toasts, setToasts] = useState<ToastProps[]>([]);

  const addToast = (toast: Omit<ToastProps, 'id'>): string => {
    const id = Math.random().toString(36).substr(2, 9);
    const newToast: ToastProps = { ...toast, id };

    setToasts((prev) => {
      const updated = [newToast, ...prev];
      // 限制最大数量
      return updated.slice(0, maxToasts);
    });

    return id;
  };

  const removeToast = (id: string) => {
    setToasts((prev) => prev.filter((toast) => toast.id !== id));
  };

  const clearToasts = () => {
    setToasts([]);
  };

  const contextValue: ToastContextType = {
    toasts,
    addToast,
    removeToast,
    clearToasts,
  };

  return (
    <ToastContext.Provider value={contextValue}>
      {children}
      <ToastContainer />
    </ToastContext.Provider>
  );
};

// 便捷的 hook 方法
export const useToastHelpers = () => {
  const { addToast } = useToast();

  return {
    showSuccess: (message: string, options?: Partial<ToastProps>) =>
      addToast({ ...options, message, type: 'success' }),
    showError: (message: string, options?: Partial<ToastProps>) =>
      addToast({ ...options, message, type: 'error' }),
    showWarning: (message: string, options?: Partial<ToastProps>) =>
      addToast({ ...options, message, type: 'warning' }),
    showInfo: (message: string, options?: Partial<ToastProps>) =>
      addToast({ ...options, message, type: 'info' }),
  };
};

export default Toast; 