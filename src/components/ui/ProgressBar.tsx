import React from 'react';
import { theme } from '../../styles/theme';

export interface ProgressBarProps {
  value: number; // 当前值 (0-100)
  max?: number; // 最大值，默认100
  min?: number; // 最小值，默认0
  variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'error';
  size?: 'sm' | 'md' | 'lg';
  showLabel?: boolean;
  label?: string;
  showPercentage?: boolean;
  animated?: boolean;
  striped?: boolean;
  className?: string;
  style?: React.CSSProperties;
}

const ProgressBar: React.FC<ProgressBarProps> = ({
  value,
  max = 100,
  min = 0,
  variant = 'primary',
  size = 'md',
  showLabel = false,
  label,
  showPercentage = true,
  animated = false,
  striped = false,
  className = '',
  style = {},
}) => {
  // 计算百分比
  const percentage = Math.min(Math.max(((value - min) / (max - min)) * 100, 0), 100);

  // 获取颜色
  const getVariantColor = () => {
    switch (variant) {
      case 'secondary':
        return {
          bg: theme.colors.secondary[500],
          light: theme.colors.secondary[100],
        };
      case 'success':
        return {
          bg: theme.colors.success[500],
          light: theme.colors.success[100],
        };
      case 'warning':
        return {
          bg: theme.colors.warning[500],
          light: theme.colors.warning[100],
        };
      case 'error':
        return {
          bg: theme.colors.error[500],
          light: theme.colors.error[100],
        };
      default:
        return {
          bg: theme.colors.primary[500],
          light: theme.colors.primary[100],
        };
    }
  };

  // 获取尺寸
  const getSizeStyles = () => {
    switch (size) {
      case 'sm':
        return {
          height: '6px',
          fontSize: theme.typography.fontSize.xs,
        };
      case 'lg':
        return {
          height: '16px',
          fontSize: theme.typography.fontSize.base,
        };
      default:
        return {
          height: '10px',
          fontSize: theme.typography.fontSize.sm,
        };
    }
  };

  const colors = getVariantColor();
  const sizeStyles = getSizeStyles();

  return (
    <div className={className} style={style}>
      {/* 标签和百分比 */}
      {(showLabel || showPercentage) && (
        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginBottom: theme.spacing[2],
            fontSize: sizeStyles.fontSize,
            color: theme.colors.text.primary,
          }}
        >
          {showLabel && <span>{label}</span>}
          {showPercentage && <span>{Math.round(percentage)}%</span>}
        </div>
      )}

      {/* 进度条容器 */}
      <div
        style={{
          width: '100%',
          height: sizeStyles.height,
          backgroundColor: colors.light,
          borderRadius: theme.borderRadius.full,
          overflow: 'hidden',
          position: 'relative',
        }}
      >
        {/* 进度条填充 */}
        <div
          style={{
            height: '100%',
            width: `${percentage}%`,
            backgroundColor: colors.bg,
            borderRadius: theme.borderRadius.full,
            transition: animated ? 'width 0.6s ease-in-out' : 'none',
            position: 'relative',
            backgroundImage: striped 
              ? `linear-gradient(45deg, 
                  rgba(255,255,255,0.15) 25%, 
                  transparent 25%, 
                  transparent 50%, 
                  rgba(255,255,255,0.15) 50%, 
                  rgba(255,255,255,0.15) 75%, 
                  transparent 75%, 
                  transparent)`
              : 'none',
            backgroundSize: striped ? '20px 20px' : 'auto',
            animation: animated && striped ? 'progressStripes 1s linear infinite' : 'none',
          }}
        />

        {/* 发光效果 */}
        {animated && (
          <div
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              background: `linear-gradient(90deg, 
                transparent 0%, 
                rgba(255,255,255,0.4) 50%, 
                transparent 100%)`,
              animation: 'progressGlow 2s ease-in-out infinite',
              borderRadius: theme.borderRadius.full,
            }}
          />
        )}
      </div>

      <style>{`
        @keyframes progressStripes {
          0% {
            background-position: 0 0;
          }
          100% {
            background-position: 20px 0;
          }
        }

        @keyframes progressGlow {
          0%, 100% {
            transform: translateX(-100%);
          }
          50% {
            transform: translateX(100%);
          }
        }
      `}</style>
    </div>
  );
};

// 圆形进度条组件
export interface CircularProgressProps {
  value: number;
  max?: number;
  min?: number;
  size?: number;
  strokeWidth?: number;
  variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'error';
  showLabel?: boolean;
  label?: string;
  showPercentage?: boolean;
  animated?: boolean;
  className?: string;
  style?: React.CSSProperties;
}

export const CircularProgress: React.FC<CircularProgressProps> = ({
  value,
  max = 100,
  min = 0,
  size = 120,
  strokeWidth = 8,
  variant = 'primary',
  showLabel = false,
  label,
  showPercentage = true,
  animated = false,
  className = '',
  style = {},
}) => {
  const percentage = Math.min(Math.max(((value - min) / (max - min)) * 100, 0), 100);
  const radius = (size - strokeWidth) / 2;
  const circumference = radius * 2 * Math.PI;
  const strokeDashoffset = circumference - (percentage / 100) * circumference;

  const getVariantColor = () => {
    switch (variant) {
      case 'secondary':
        return theme.colors.secondary[500];
      case 'success':
        return theme.colors.success[500];
      case 'warning':
        return theme.colors.warning[500];
      case 'error':
        return theme.colors.error[500];
      default:
        return theme.colors.primary[500];
    }
  };

  const color = getVariantColor();

  return (
    <div
      className={className}
      style={{
        position: 'relative',
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        ...style,
      }}
    >
      <svg
        width={size}
        height={size}
        style={{
          transform: 'rotate(-90deg)',
        }}
      >
        {/* 背景圆 */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke={theme.colors.gray[200]}
          strokeWidth={strokeWidth}
          fill="transparent"
        />
        {/* 进度圆 */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke={color}
          strokeWidth={strokeWidth}
          fill="transparent"
          strokeDasharray={circumference}
          strokeDashoffset={strokeDashoffset}
          strokeLinecap="round"
          style={{
            transition: animated ? 'stroke-dashoffset 0.6s ease-in-out' : 'none',
          }}
        />
      </svg>

      {/* 中心文本 */}
      {(showLabel || showPercentage) && (
        <div
          style={{
            position: 'absolute',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: size > 80 ? theme.typography.fontSize.base : theme.typography.fontSize.sm,
            color: theme.colors.text.primary,
            fontWeight: theme.typography.fontWeight.semibold,
          }}
        >
          {showPercentage && <div>{Math.round(percentage)}%</div>}
          {showLabel && (
            <div
              style={{
                fontSize: size > 80 ? theme.typography.fontSize.sm : theme.typography.fontSize.xs,
                color: theme.colors.text.secondary,
                marginTop: theme.spacing[1],
              }}
            >
              {label}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default ProgressBar; 