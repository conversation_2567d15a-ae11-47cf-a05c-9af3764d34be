import React, { ReactNode } from 'react'
import { theme } from '../../styles/theme'

// Card 组件属性接口
export interface CardProps {
  /** 卡片内容 */
  children: ReactNode
  /** 卡片标题 */
  title?: string
  /** 卡片副标题 */
  subtitle?: string
  /** 头部额外内容 */
  headerExtra?: ReactNode
  /** 是否显示阴影 */
  shadow?: 'none' | 'sm' | 'base' | 'md' | 'lg'
  /** 是否可悬停 */
  hoverable?: boolean
  /** 边框样式 */
  border?: boolean
  /** 自定义类名 */
  className?: string
  /** 自定义样式 */
  style?: React.CSSProperties
  /** 点击事件 */
  onClick?: () => void
}

export const Card: React.FC<CardProps> = ({
  children,
  title,
  subtitle,
  headerExtra,
  shadow = 'base',
  hoverable = false,
  border = true,
  className = '',
  style = {},
  onClick,
}) => {
  const cardStyle: React.CSSProperties = {
    backgroundColor: theme.colors.surface,
    borderRadius: theme.components.card.borderRadius,
    border: border ? `1px solid ${theme.colors.gray[200]}` : 'none',
    boxShadow: shadow !== 'none' ? theme.boxShadow[shadow] : 'none',
    transition: theme.transition.base,
    overflow: 'hidden',
    cursor: onClick ? 'pointer' : 'default',
    ...style,
  }

  const headerStyle: React.CSSProperties = {
    padding: theme.spacing[6],
    borderBottom: title || subtitle ? `1px solid ${theme.colors.gray[100]}` : 'none',
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  }

  const titleStyle: React.CSSProperties = {
    margin: 0,
    fontSize: theme.typography.fontSize.lg,
    fontWeight: theme.typography.fontWeight.semibold,
    color: theme.colors.text.primary,
    lineHeight: theme.typography.lineHeight.tight,
  }

  const subtitleStyle: React.CSSProperties = {
    margin: `${theme.spacing[1]} 0 0 0`,
    fontSize: theme.typography.fontSize.sm,
    fontWeight: theme.typography.fontWeight.normal,
    color: theme.colors.text.secondary,
    lineHeight: theme.typography.lineHeight.normal,
  }

  const bodyStyle: React.CSSProperties = {
    padding: theme.components.card.padding,
  }

  const hoverStyles = hoverable ? {
    ':hover': {
      transform: 'translateY(-2px)',
      boxShadow: theme.boxShadow.lg,
    }
  } : {}

  return (
    <>
      {/* 添加悬停效果的CSS */}
      {hoverable && (
        <style>
          {`
            .ui-card.hoverable:hover {
              transform: translateY(-2px);
              box-shadow: ${theme.boxShadow.lg};
            }
          `}
        </style>
      )}
      
      <div
        className={`ui-card ${hoverable ? 'hoverable' : ''} ${className}`}
        style={cardStyle}
        onClick={onClick}
      >
        {/* 卡片头部 */}
        {(title || subtitle || headerExtra) && (
          <div style={headerStyle}>
            <div>
              {title && <h3 style={titleStyle}>{title}</h3>}
              {subtitle && <p style={subtitleStyle}>{subtitle}</p>}
            </div>
            {headerExtra && <div>{headerExtra}</div>}
          </div>
        )}
        
        {/* 卡片内容 */}
        <div style={bodyStyle}>
          {children}
        </div>
      </div>
    </>
  )
}

// CardHeader 组件
export interface CardHeaderProps {
  children: ReactNode
  className?: string
  style?: React.CSSProperties
}

export const CardHeader: React.FC<CardHeaderProps> = ({ 
  children, 
  className = '',
  style = {} 
}) => {
  const headerStyle: React.CSSProperties = {
    padding: theme.spacing[6],
    borderBottom: `1px solid ${theme.colors.gray[100]}`,
    ...style,
  }

  return (
    <div className={`ui-card-header ${className}`} style={headerStyle}>
      {children}
    </div>
  )
}

// CardBody 组件
export interface CardBodyProps {
  children: ReactNode
  className?: string
  style?: React.CSSProperties
}

export const CardBody: React.FC<CardBodyProps> = ({ 
  children, 
  className = '',
  style = {} 
}) => {
  const bodyStyle: React.CSSProperties = {
    padding: theme.components.card.padding,
    ...style,
  }

  return (
    <div className={`ui-card-body ${className}`} style={bodyStyle}>
      {children}
    </div>
  )
}

// CardFooter 组件
export interface CardFooterProps {
  children: ReactNode
  className?: string
  style?: React.CSSProperties
}

export const CardFooter: React.FC<CardFooterProps> = ({ 
  children, 
  className = '',
  style = {} 
}) => {
  const footerStyle: React.CSSProperties = {
    padding: theme.spacing[6],
    borderTop: `1px solid ${theme.colors.gray[100]}`,
    backgroundColor: theme.colors.gray[50],
    ...style,
  }

  return (
    <div className={`ui-card-footer ${className}`} style={footerStyle}>
      {children}
    </div>
  )
}

export default Card 