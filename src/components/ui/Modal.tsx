import React, { useEffect, useRef } from 'react';
import { theme } from '../../styles/theme';
import { Button } from './Button';

export interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  showCloseButton?: boolean;
  closeOnOverlayClick?: boolean;
  closeOnEscape?: boolean;
  footer?: React.ReactNode;
  className?: string;
}

const Modal: React.FC<ModalProps> = ({
  isOpen,
  onClose,
  title,
  children,
  size = 'md',
  showCloseButton = true,
  closeOnOverlayClick = true,
  closeOnEscape = true,
  footer,
  className = '',
}) => {
  const modalRef = useRef<HTMLDivElement>(null);

  // 获取尺寸样式
  const getModalSize = () => {
    switch (size) {
      case 'sm':
        return { maxWidth: '400px', width: '90%' };
      case 'md':
        return { maxWidth: '500px', width: '90%' };
      case 'lg':
        return { maxWidth: '800px', width: '90%' };
      case 'xl':
        return { maxWidth: '1200px', width: '95%' };
      case 'full':
        return { maxWidth: '100%', width: '100%', height: '100%' };
      default:
        return { maxWidth: '500px', width: '90%' };
    }
  };

  // 处理 ESC 键关闭
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && closeOnEscape && isOpen) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, closeOnEscape, onClose]);

  // 处理点击遮罩关闭
  const handleOverlayClick = (e: React.MouseEvent) => {
    if (closeOnOverlayClick && e.target === e.currentTarget) {
      onClose();
    }
  };

  // 焦点管理
  useEffect(() => {
    if (isOpen && modalRef.current) {
      const focusableElements = modalRef.current.querySelectorAll(
        'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
      );
      const firstElement = focusableElements[0] as HTMLElement;
      if (firstElement) {
        firstElement.focus();
      }
    }
  }, [isOpen]);

  if (!isOpen) return null;

  const modalSizeStyle = getModalSize();

  return (
    <div
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        zIndex: theme.zIndex.modal,
        backdropFilter: 'blur(4px)',
        animation: 'modalOverlayFadeIn 0.2s ease-out',
      }}
      onClick={handleOverlayClick}
      role="dialog"
      aria-modal="true"
      aria-labelledby={title ? 'modal-title' : undefined}
    >
      <div
        ref={modalRef}
        className={className}
        style={{
          backgroundColor: theme.colors.surface,
          borderRadius: theme.borderRadius.xl,
          boxShadow: theme.boxShadow.xl,
          maxWidth: modalSizeStyle.maxWidth,
          width: modalSizeStyle.width,
          height: modalSizeStyle.height || 'auto',
          maxHeight: size === 'full' ? '100%' : '90vh',
          overflow: 'hidden',
          display: 'flex',
          flexDirection: 'column',
          animation: 'modalContentSlideIn 0.3s ease-out',
          position: 'relative',
        }}
        onClick={(e) => e.stopPropagation()}
      >
        {/* 头部 */}
        {(title || showCloseButton) && (
          <div
            style={{
              padding: theme.spacing[6],
              borderBottom: `1px solid ${theme.colors.gray[200]}`,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              background: `linear-gradient(135deg, ${theme.colors.primary[50]}, ${theme.colors.secondary[50]})`,
            }}
          >
            {title && (
              <h2
                id="modal-title"
                style={{
                  margin: 0,
                  fontSize: theme.typography.fontSize.xl,
                  fontWeight: theme.typography.fontWeight.semibold,
                  color: theme.colors.text.primary,
                }}
              >
                {title}
              </h2>
            )}
            {showCloseButton && (
              <button
                onClick={onClose}
                style={{
                  background: 'none',
                  border: 'none',
                  fontSize: '24px',
                  cursor: 'pointer',
                  color: theme.colors.text.secondary,
                  borderRadius: theme.borderRadius.base,
                  width: '32px',
                  height: '32px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  transition: theme.transition.fast,
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = theme.colors.gray[100];
                  e.currentTarget.style.color = theme.colors.text.primary;
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = 'transparent';
                  e.currentTarget.style.color = theme.colors.text.secondary;
                }}
                aria-label="关闭弹窗"
              >
                ×
              </button>
            )}
          </div>
        )}

        {/* 内容区域 */}
        <div
          style={{
            flex: 1,
            padding: theme.spacing[6],
            overflow: 'auto',
          }}
        >
          {children}
        </div>

        {/* 底部 */}
        {footer && (
          <div
            style={{
              padding: theme.spacing[6],
              borderTop: `1px solid ${theme.colors.gray[200]}`,
              backgroundColor: theme.colors.gray[50],
              display: 'flex',
              gap: theme.spacing[3],
              justifyContent: 'flex-end',
            }}
          >
            {footer}
          </div>
        )}
      </div>

      <style>{`
        @keyframes modalOverlayFadeIn {
          from {
            opacity: 0;
          }
          to {
            opacity: 1;
          }
        }

        @keyframes modalContentSlideIn {
          from {
            opacity: 0;
            transform: scale(0.95) translateY(-20px);
          }
          to {
            opacity: 1;
            transform: scale(1) translateY(0);
          }
        }
      `}</style>
    </div>
  );
};

// 便捷组件：确认对话框
export interface ConfirmModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title?: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  type?: 'info' | 'warning' | 'error' | 'success';
}

export const ConfirmModal: React.FC<ConfirmModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  title = '确认操作',
  message,
  confirmText = '确认',
  cancelText = '取消',
  type = 'info',
}) => {
  const getTypeColor = () => {
    switch (type) {
      case 'warning':
        return theme.colors.warning[500];
      case 'error':
        return theme.colors.error[500];
      case 'success':
        return theme.colors.success[500];
      default:
        return theme.colors.primary[500];
    }
  };

  const getTypeIcon = () => {
    switch (type) {
      case 'warning':
        return '⚠️';
      case 'error':
        return '❌';
      case 'success':
        return '✅';
      default:
        return 'ℹ️';
    }
  };

  const handleConfirm = () => {
    onConfirm();
    onClose();
  };

  const typeColor = getTypeColor();

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={title}
      size="sm"
      footer={
        <>
          <Button variant="outline" onClick={onClose}>
            {cancelText}
          </Button>
          <Button 
            variant="primary" 
            onClick={handleConfirm}
            style={{ backgroundColor: typeColor }}
          >
            {confirmText}
          </Button>
        </>
      }
    >
      <div
        style={{
          display: 'flex',
          alignItems: 'center',
          gap: theme.spacing[4],
        }}
      >
        <div
          style={{
            width: '48px',
            height: '48px',
            borderRadius: '50%',
            backgroundColor: `${typeColor}20`,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '24px',
            color: typeColor,
          }}
        >
          {getTypeIcon()}
        </div>
        <p
          style={{
            margin: 0,
            fontSize: theme.typography.fontSize.base,
            color: theme.colors.text.primary,
            lineHeight: theme.typography.lineHeight.normal,
          }}
        >
          {message}
        </p>
      </div>
    </Modal>
  );
};

export default Modal; 