import React, { forwardRef, ReactNode } from 'react'
import { theme } from '../../styles/theme'

// Button 组件属性接口
export interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  /** 按钮内容 */
  children: ReactNode
  /** 按钮尺寸 */
  size?: 'sm' | 'base' | 'lg'
  /** 按钮变体 */
  variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'ghost' | 'outline'
  /** 是否为加载状态 */
  loading?: boolean
  /** 是否为全宽按钮 */
  fullWidth?: boolean
  /** 按钮图标（左侧） */
  leftIcon?: ReactNode
  /** 按钮图标（右侧） */
  rightIcon?: ReactNode
  /** 自定义类名 */
  className?: string
}

// 样式配置
const variants = {
  primary: {
    background: `linear-gradient(135deg, ${theme.colors.primary[500]}, ${theme.colors.primary[600]})`,
    color: theme.colors.text.inverse,
    border: `1px solid ${theme.colors.primary[500]}`,
    hover: {
      background: `linear-gradient(135deg, ${theme.colors.primary[600]}, ${theme.colors.primary[700]})`,
      transform: 'translateY(-1px)',
      boxShadow: theme.boxShadow.glow,
    }
  },
  secondary: {
    background: `linear-gradient(135deg, ${theme.colors.secondary[500]}, ${theme.colors.secondary[600]})`,
    color: theme.colors.text.inverse,
    border: `1px solid ${theme.colors.secondary[500]}`,
    hover: {
      background: `linear-gradient(135deg, ${theme.colors.secondary[600]}, ${theme.colors.secondary[700]})`,
      transform: 'translateY(-1px)',
      boxShadow: `0 0 20px ${theme.colors.secondary[400]}60`,
    }
  },
  success: {
    background: `linear-gradient(135deg, ${theme.colors.success[500]}, ${theme.colors.success[600]})`,
    color: theme.colors.text.inverse,
    border: `1px solid ${theme.colors.success[500]}`,
    hover: {
      background: `linear-gradient(135deg, ${theme.colors.success[600]}, ${theme.colors.success[700]})`,
      transform: 'translateY(-1px)',
      boxShadow: `0 0 20px ${theme.colors.success[400]}60`,
    }
  },
  warning: {
    background: `linear-gradient(135deg, ${theme.colors.warning[500]}, ${theme.colors.warning[600]})`,
    color: theme.colors.text.inverse,
    border: `1px solid ${theme.colors.warning[500]}`,
    hover: {
      background: `linear-gradient(135deg, ${theme.colors.warning[600]}, ${theme.colors.warning[700]})`,
      transform: 'translateY(-1px)',
      boxShadow: `0 0 20px ${theme.colors.warning[400]}60`,
    }
  },
  error: {
    background: `linear-gradient(135deg, ${theme.colors.error[500]}, ${theme.colors.error[600]})`,
    color: theme.colors.text.inverse,
    border: `1px solid ${theme.colors.error[500]}`,
    hover: {
      background: `linear-gradient(135deg, ${theme.colors.error[600]}, ${theme.colors.error[700]})`,
      transform: 'translateY(-1px)',
      boxShadow: `0 0 20px ${theme.colors.error[400]}60`,
    }
  },
  ghost: {
    background: 'transparent',
    color: theme.colors.text.primary,
    border: 'none',
    hover: {
      background: theme.colors.gray[100],
      transform: 'translateY(-1px)',
      boxShadow: theme.boxShadow.sm,
    }
  },
  outline: {
    background: 'transparent',
    color: theme.colors.primary[600],
    border: `1px solid ${theme.colors.primary[300]}`,
    hover: {
      background: theme.colors.primary[50],
      borderColor: theme.colors.primary[500],
      transform: 'translateY(-1px)',
      boxShadow: theme.boxShadow.sm,
    }
  }
}

const sizes = {
  sm: {
    height: theme.components.button.height.sm,
    padding: theme.components.button.padding.sm,
    fontSize: theme.typography.fontSize.sm,
    fontWeight: theme.typography.fontWeight.medium,
  },
  base: {
    height: theme.components.button.height.base,
    padding: theme.components.button.padding.base,
    fontSize: theme.typography.fontSize.base,
    fontWeight: theme.typography.fontWeight.medium,
  },
  lg: {
    height: theme.components.button.height.lg,
    padding: theme.components.button.padding.lg,
    fontSize: theme.typography.fontSize.lg,
    fontWeight: theme.typography.fontWeight.semibold,
  }
}

// Button 组件
export const Button = forwardRef<HTMLButtonElement, ButtonProps>(({
  children,
  size = 'base',
  variant = 'primary',
  loading = false,
  fullWidth = false,
  leftIcon,
  rightIcon,
  disabled = false,
  className = '',
  style = {},
  ...props
}, ref) => {
  const variantStyles = variants[variant]
  const sizeStyles = sizes[size]
  
  const buttonStyle: React.CSSProperties = {
    // 基础样式
    display: 'inline-flex',
    alignItems: 'center',
    justifyContent: 'center',
    gap: theme.spacing[2],
    fontFamily: theme.typography.fontFamily.primary,
    borderRadius: theme.borderRadius.md,
    cursor: disabled || loading ? 'not-allowed' : 'pointer',
    transition: theme.transition.base,
    textDecoration: 'none',
    outline: 'none',
    position: 'relative',
    overflow: 'hidden',
    
    // 尺寸样式
    height: sizeStyles.height,
    padding: sizeStyles.padding,
    fontSize: sizeStyles.fontSize,
    fontWeight: sizeStyles.fontWeight,
    
    // 变体样式
    background: variantStyles.background,
    color: variantStyles.color,
    border: variantStyles.border,
    
    // 全宽样式
    width: fullWidth ? '100%' : 'auto',
    
    // 禁用状态
    ...(disabled && {
      opacity: 0.6,
      pointerEvents: 'none',
    }),
    
    // 加载状态
    ...(loading && {
      opacity: 0.8,
      pointerEvents: 'none',
    }),
    
    // 自定义样式
    ...style,
  }

  // 加载指示器组件
  const LoadingSpinner = () => (
    <svg
      width="16"
      height="16"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      style={{
        animation: 'spin 1s linear infinite',
      }}
    >
      <path d="M21 12a9 9 0 11-6.219-8.56" />
    </svg>
  )

  return (
    <>
      {/* 添加 CSS 动画 */}
      <style>
        {`
          @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
          }
          
          .ui-button:hover:not(:disabled) {
            background: ${variantStyles.hover.background};
            transform: ${variantStyles.hover.transform};
            box-shadow: ${variantStyles.hover.boxShadow};
          }
          
          .ui-button:active:not(:disabled) {
            transform: translateY(0);
            transition: ${theme.transition.fast};
          }
          
          .ui-button:focus-visible {
            outline: 2px solid ${theme.colors.primary[500]};
            outline-offset: 2px;
          }
          
          .ui-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: ${theme.transition.slow};
          }
          
          .ui-button:hover::before {
            left: 100%;
          }
        `}
      </style>
      
      <button
        ref={ref}
        className={`ui-button ${className}`}
        style={buttonStyle}
        disabled={disabled || loading}
        {...props}
      >
        {loading && <LoadingSpinner />}
        {leftIcon && !loading && leftIcon}
        {children}
        {rightIcon && !loading && rightIcon}
      </button>
    </>
  )
})

Button.displayName = 'Button'

export default Button 