import React, { forwardRef, useState } from 'react';
import { theme } from '../../styles/theme';

// 基础输入框组件
export interface InputProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'size'> {
  label?: string;
  error?: string;
  helperText?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'outline' | 'filled' | 'underline';
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  isRequired?: boolean;
  isDisabled?: boolean;
  isInvalid?: boolean;
  className?: string;
}

export const Input = forwardRef<HTMLInputElement, InputProps>(({
  label,
  error,
  helperText,
  size = 'md',
  variant = 'outline',
  leftIcon,
  rightIcon,
  isRequired = false,
  isDisabled = false,
  isInvalid = false,
  className = '',
  ...props
}, ref) => {
  const [isFocused, setIsFocused] = useState(false);

  // 获取尺寸样式
  const getSizeStyles = () => {
    switch (size) {
      case 'sm':
        return {
          height: '32px',
          fontSize: theme.typography.fontSize.sm,
          padding: `${theme.spacing[2]} ${theme.spacing[3]}`,
        };
      case 'lg':
        return {
          height: '48px',
          fontSize: theme.typography.fontSize.lg,
          padding: `${theme.spacing[3]} ${theme.spacing[4]}`,
        };
      default:
        return {
          height: '40px',
          fontSize: theme.typography.fontSize.base,
          padding: `${theme.spacing[2]} ${theme.spacing[3]}`,
        };
    }
  };

  // 获取变体样式
  const getVariantStyles = () => {
    const baseStyles = {
      width: '100%',
      border: 'none',
      outline: 'none',
      backgroundColor: 'transparent',
      color: theme.colors.text.primary,
      fontFamily: theme.typography.fontFamily.primary,
      transition: theme.transition.base,
    };

    switch (variant) {
      case 'filled':
        return {
          ...baseStyles,
          backgroundColor: theme.colors.gray[100],
          borderRadius: theme.borderRadius.md,
        };
      case 'underline':
        return {
          ...baseStyles,
          borderBottom: `2px solid ${theme.colors.gray[300]}`,
          borderRadius: 0,
        };
      default:
        return {
          ...baseStyles,
          border: `1px solid ${theme.colors.gray[300]}`,
          borderRadius: theme.borderRadius.md,
        };
    }
  };

  // 获取容器样式
  const getContainerStyles = () => {
    const isError = isInvalid || !!error;
    const borderColor = isError 
      ? theme.colors.error[500] 
      : isFocused 
        ? theme.colors.primary[500] 
        : theme.colors.gray[300];

    const baseStyles = {
      position: 'relative' as const,
      display: 'flex',
      alignItems: 'center',
      transition: theme.transition.base,
      backgroundColor: isDisabled ? theme.colors.gray[50] : 'transparent',
    };

    switch (variant) {
      case 'filled':
        return {
          ...baseStyles,
          backgroundColor: isDisabled ? theme.colors.gray[100] : theme.colors.gray[50],
          borderRadius: theme.borderRadius.md,
          border: isFocused ? `2px solid ${borderColor}` : '2px solid transparent',
        };
      case 'underline':
        return {
          ...baseStyles,
          borderBottom: `2px solid ${borderColor}`,
        };
      default:
        return {
          ...baseStyles,
          border: `1px solid ${borderColor}`,
          borderRadius: theme.borderRadius.md,
          boxShadow: isFocused ? `0 0 0 3px ${borderColor}20` : 'none',
        };
    }
  };

  const sizeStyles = getSizeStyles();
  const inputStyles = getVariantStyles();
  const containerStyles = getContainerStyles();

  return (
    <div className={className} style={{ marginBottom: theme.spacing[4] }}>
      {/* 标签 */}
      {label && (
        <label
          style={{
            display: 'block',
            fontSize: theme.typography.fontSize.sm,
            fontWeight: theme.typography.fontWeight.medium,
            color: theme.colors.text.primary,
            marginBottom: theme.spacing[2],
          }}
        >
          {label}
          {isRequired && (
            <span style={{ color: theme.colors.error[500], marginLeft: theme.spacing[1] }}>
              *
            </span>
          )}
        </label>
      )}

      {/* 输入框容器 */}
      <div style={containerStyles}>
        {/* 左侧图标 */}
        {leftIcon && (
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              paddingLeft: theme.spacing[3],
              color: theme.colors.text.secondary,
            }}
          >
            {leftIcon}
          </div>
        )}

        {/* 输入框 */}
        <input
          ref={ref}
          disabled={isDisabled}
          style={{
            ...inputStyles,
            ...sizeStyles,
            paddingLeft: leftIcon ? theme.spacing[2] : sizeStyles.padding.split(' ')[1],
            paddingRight: rightIcon ? theme.spacing[2] : sizeStyles.padding.split(' ')[1],
          }}
          onFocus={(e) => {
            setIsFocused(true);
            props.onFocus?.(e);
          }}
          onBlur={(e) => {
            setIsFocused(false);
            props.onBlur?.(e);
          }}
          {...props}
        />

        {/* 右侧图标 */}
        {rightIcon && (
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              paddingRight: theme.spacing[3],
              color: theme.colors.text.secondary,
            }}
          >
            {rightIcon}
          </div>
        )}
      </div>

      {/* 错误信息或帮助文本 */}
      {(error || helperText) && (
        <div
          style={{
            marginTop: theme.spacing[1],
            fontSize: theme.typography.fontSize.sm,
            color: error ? theme.colors.error[500] : theme.colors.text.secondary,
          }}
        >
          {error || helperText}
        </div>
      )}
    </div>
  );
});

Input.displayName = 'Input';

// 文本域组件
export interface TextareaProps extends Omit<React.TextareaHTMLAttributes<HTMLTextAreaElement>, 'size'> {
  label?: string;
  error?: string;
  helperText?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'outline' | 'filled';
  isRequired?: boolean;
  isDisabled?: boolean;
  isInvalid?: boolean;
  resize?: 'none' | 'vertical' | 'horizontal' | 'both';
  className?: string;
}

export const Textarea = forwardRef<HTMLTextAreaElement, TextareaProps>(({
  label,
  error,
  helperText,
  size = 'md',
  variant = 'outline',
  isRequired = false,
  isDisabled = false,
  isInvalid = false,
  resize = 'vertical',
  className = '',
  ...props
}, ref) => {
  const [isFocused, setIsFocused] = useState(false);

  const getSizeStyles = () => {
    switch (size) {
      case 'sm':
        return {
          minHeight: '80px',
          fontSize: theme.typography.fontSize.sm,
          padding: theme.spacing[2],
        };
      case 'lg':
        return {
          minHeight: '120px',
          fontSize: theme.typography.fontSize.lg,
          padding: theme.spacing[4],
        };
      default:
        return {
          minHeight: '100px',
          fontSize: theme.typography.fontSize.base,
          padding: theme.spacing[3],
        };
    }
  };

  const sizeStyles = getSizeStyles();
  const isError = isInvalid || !!error;
  const borderColor = isError 
    ? theme.colors.error[500] 
    : isFocused 
      ? theme.colors.primary[500] 
      : theme.colors.gray[300];

  return (
    <div className={className} style={{ marginBottom: theme.spacing[4] }}>
      {label && (
        <label
          style={{
            display: 'block',
            fontSize: theme.typography.fontSize.sm,
            fontWeight: theme.typography.fontWeight.medium,
            color: theme.colors.text.primary,
            marginBottom: theme.spacing[2],
          }}
        >
          {label}
          {isRequired && (
            <span style={{ color: theme.colors.error[500], marginLeft: theme.spacing[1] }}>
              *
            </span>
          )}
        </label>
      )}

      <textarea
        ref={ref}
        disabled={isDisabled}
        style={{
          width: '100%',
          border: variant === 'filled' ? 'none' : `1px solid ${borderColor}`,
          borderRadius: theme.borderRadius.md,
          backgroundColor: variant === 'filled' 
            ? (isDisabled ? theme.colors.gray[100] : theme.colors.gray[50])
            : (isDisabled ? theme.colors.gray[50] : 'transparent'),
          color: theme.colors.text.primary,
          fontFamily: theme.typography.fontFamily.primary,
          resize,
          outline: 'none',
          transition: theme.transition.base,
          boxShadow: variant === 'outline' && isFocused ? `0 0 0 3px ${borderColor}20` : 'none',
          ...sizeStyles,
        }}
        onFocus={(e) => {
          setIsFocused(true);
          props.onFocus?.(e);
        }}
        onBlur={(e) => {
          setIsFocused(false);
          props.onBlur?.(e);
        }}
        {...props}
      />

      {(error || helperText) && (
        <div
          style={{
            marginTop: theme.spacing[1],
            fontSize: theme.typography.fontSize.sm,
            color: error ? theme.colors.error[500] : theme.colors.text.secondary,
          }}
        >
          {error || helperText}
        </div>
      )}
    </div>
  );
});

Textarea.displayName = 'Textarea';

// 选择框组件
export interface SelectProps extends Omit<React.SelectHTMLAttributes<HTMLSelectElement>, 'size'> {
  label?: string;
  error?: string;
  helperText?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'outline' | 'filled';
  isRequired?: boolean;
  isDisabled?: boolean;
  isInvalid?: boolean;
  placeholder?: string;
  className?: string;
  children: React.ReactNode;
}

export const Select = forwardRef<HTMLSelectElement, SelectProps>(({
  label,
  error,
  helperText,
  size = 'md',
  variant = 'outline',
  isRequired = false,
  isDisabled = false,
  isInvalid = false,
  placeholder,
  className = '',
  children,
  ...props
}, ref) => {
  const [isFocused, setIsFocused] = useState(false);

  const getSizeStyles = () => {
    switch (size) {
      case 'sm':
        return {
          height: '32px',
          fontSize: theme.typography.fontSize.sm,
          padding: `${theme.spacing[1]} ${theme.spacing[8]} ${theme.spacing[1]} ${theme.spacing[3]}`,
        };
      case 'lg':
        return {
          height: '48px',
          fontSize: theme.typography.fontSize.lg,
          padding: `${theme.spacing[3]} ${theme.spacing[10]} ${theme.spacing[3]} ${theme.spacing[4]}`,
        };
      default:
        return {
          height: '40px',
          fontSize: theme.typography.fontSize.base,
          padding: `${theme.spacing[2]} ${theme.spacing[8]} ${theme.spacing[2]} ${theme.spacing[3]}`,
        };
    }
  };

  const sizeStyles = getSizeStyles();
  const isError = isInvalid || !!error;
  const borderColor = isError 
    ? theme.colors.error[500] 
    : isFocused 
      ? theme.colors.primary[500] 
      : theme.colors.gray[300];

  return (
    <div className={className} style={{ marginBottom: theme.spacing[4] }}>
      {label && (
        <label
          style={{
            display: 'block',
            fontSize: theme.typography.fontSize.sm,
            fontWeight: theme.typography.fontWeight.medium,
            color: theme.colors.text.primary,
            marginBottom: theme.spacing[2],
          }}
        >
          {label}
          {isRequired && (
            <span style={{ color: theme.colors.error[500], marginLeft: theme.spacing[1] }}>
              *
            </span>
          )}
        </label>
      )}

      <div style={{ position: 'relative' }}>
        <select
          ref={ref}
          disabled={isDisabled}
          style={{
            width: '100%',
            border: variant === 'filled' ? 'none' : `1px solid ${borderColor}`,
            borderRadius: theme.borderRadius.md,
            backgroundColor: variant === 'filled' 
              ? (isDisabled ? theme.colors.gray[100] : theme.colors.gray[50])
              : (isDisabled ? theme.colors.gray[50] : 'white'),
            color: theme.colors.text.primary,
            fontFamily: theme.typography.fontFamily.primary,
            outline: 'none',
            appearance: 'none',
            cursor: isDisabled ? 'not-allowed' : 'pointer',
            transition: theme.transition.base,
            boxShadow: variant === 'outline' && isFocused ? `0 0 0 3px ${borderColor}20` : 'none',
            ...sizeStyles,
          }}
          onFocus={(e) => {
            setIsFocused(true);
            props.onFocus?.(e);
          }}
          onBlur={(e) => {
            setIsFocused(false);
            props.onBlur?.(e);
          }}
          {...props}
        >
          {placeholder && (
            <option value="" disabled>
              {placeholder}
            </option>
          )}
          {children}
        </select>

        {/* 下拉箭头 */}
        <div
          style={{
            position: 'absolute',
            top: '50%',
            right: theme.spacing[3],
            transform: 'translateY(-50%)',
            pointerEvents: 'none',
            color: theme.colors.text.secondary,
          }}
        >
          ▼
        </div>
      </div>

      {(error || helperText) && (
        <div
          style={{
            marginTop: theme.spacing[1],
            fontSize: theme.typography.fontSize.sm,
            color: error ? theme.colors.error[500] : theme.colors.text.secondary,
          }}
        >
          {error || helperText}
        </div>
      )}
    </div>
  );
});

Select.displayName = 'Select';

export default Input; 