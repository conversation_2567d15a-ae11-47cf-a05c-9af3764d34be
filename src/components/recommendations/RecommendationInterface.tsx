// 个性化建议界面组件
// 展示和管理用户的个性化建议

import React, { useState, useEffect, useCallback } from 'react'
import { 
  RecommendationService, 
  StoredRecommendation, 
  RecommendationStatus, 
  FeedbackType,
  RecommendationStatistics 
} from '../../services/RecommendationService'
import { 
  RecommendationType, 
  RecommendationPriority, 
  RecommendationDifficulty 
} from '../../services/PersonalizedRecommendationEngine'

// 组件Props接口
interface RecommendationInterfaceProps {
  userId: string
  recommendationService: RecommendationService
  onRecommendationComplete?: (recommendationId: string) => void
  onFeedbackSubmitted?: (recommendationId: string, feedbackType: FeedbackType) => void
  className?: string
}

// 建议卡片组件Props
interface RecommendationCardProps {
  recommendation: StoredRecommendation
  onStatusUpdate: (id: string, status: RecommendationStatus) => void
  onFeedbackSubmit: (id: string, feedbackType: FeedbackType, rating: number, comment?: string) => void
  onProgressUpdate: (id: string, progress: number) => void
}

// 过滤器选项
interface FilterOptions {
  type: RecommendationType | 'all'
  priority: RecommendationPriority | 'all'
  status: RecommendationStatus | 'all'
  difficulty: RecommendationDifficulty | 'all'
}

// 主界面组件
export const RecommendationInterface: React.FC<RecommendationInterfaceProps> = ({
  userId,
  recommendationService,
  onRecommendationComplete,
  onFeedbackSubmitted,
  className = ''
}) => {
  const [recommendations, setRecommendations] = useState<StoredRecommendation[]>([])
  const [statistics, setStatistics] = useState<RecommendationStatistics | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState<'active' | 'completed' | 'all' | 'stats'>('active')
  const [filters, setFilters] = useState<FilterOptions>({
    type: 'all',
    priority: 'all',
    status: 'all',
    difficulty: 'all'
  })

  // 应用过滤器
  const applyFilters = (recs: StoredRecommendation[], filterOptions: FilterOptions): StoredRecommendation[] => {
    return recs.filter(rec => {
      if (filterOptions.type !== 'all' && rec.type !== filterOptions.type) return false
      if (filterOptions.priority !== 'all' && rec.priority !== filterOptions.priority) return false
      if (filterOptions.status !== 'all' && rec.status !== filterOptions.status) return false
      if (filterOptions.difficulty !== 'all' && rec.difficulty !== filterOptions.difficulty) return false
      return true
    })
  }

  // 加载建议数据
  const loadRecommendations = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      let loadedRecommendations: StoredRecommendation[] = []
      
      switch (activeTab) {
        case 'active':
          loadedRecommendations = await recommendationService.getActiveRecommendations(userId)
          break
        case 'completed':
          loadedRecommendations = await recommendationService.queryRecommendations({
            userId,
            status: [RecommendationStatus.COMPLETED]
          })
          break
        case 'all':
          loadedRecommendations = await recommendationService.queryRecommendations({ userId })
          break
        case 'stats':
          const stats = await recommendationService.getRecommendationStatistics(userId)
          setStatistics(stats)
          return
      }

      // 应用过滤器
      const filteredRecommendations = applyFilters(loadedRecommendations, filters)
      setRecommendations(filteredRecommendations)

    } catch (err) {
      setError(err instanceof Error ? err.message : '加载建议失败')
    } finally {
      setLoading(false)
    }
  }, [userId, recommendationService, activeTab, filters])

  // 处理状态更新
  const handleStatusUpdate = async (recommendationId: string, status: RecommendationStatus) => {
    try {
      await recommendationService.updateRecommendationStatus(recommendationId, status, userId)
      
      if (status === RecommendationStatus.COMPLETED && onRecommendationComplete) {
        onRecommendationComplete(recommendationId)
      }
      
      await loadRecommendations()
    } catch (err) {
      setError(err instanceof Error ? err.message : '状态更新失败')
    }
  }

  // 处理反馈提交
  const handleFeedbackSubmit = async (
    recommendationId: string, 
    feedbackType: FeedbackType, 
    rating: number, 
    comment?: string
  ) => {
    try {
      await recommendationService.recordFeedback(recommendationId, userId, feedbackType, rating, comment)
      
      if (onFeedbackSubmitted) {
        onFeedbackSubmitted(recommendationId, feedbackType)
      }
      
      await loadRecommendations()
    } catch (err) {
      setError(err instanceof Error ? err.message : '反馈提交失败')
    }
  }

  // 处理进度更新
  const handleProgressUpdate = async (recommendationId: string, progress: number) => {
    console.log(`更新建议 ${recommendationId} 进度到 ${progress}%`)
  }

  // 初始加载
  useEffect(() => {
    loadRecommendations()
  }, [loadRecommendations])

  // 渲染加载状态
  if (loading) {
    return (
      <div className={`recommendation-interface ${className}`} style={{ maxWidth: '1200px', margin: '0 auto', padding: '20px' }}>
        <div style={{ textAlign: 'center', padding: '40px' }}>
          <div style={{ 
            width: '40px', 
            height: '40px', 
            border: '4px solid #f3f3f3', 
            borderTop: '4px solid #3498db', 
            borderRadius: '50%', 
            animation: 'spin 1s linear infinite', 
            margin: '0 auto 20px' 
          }}></div>
          <p>正在加载个性化建议...</p>
        </div>
      </div>
    )
  }

  // 渲染错误状态
  if (error) {
    return (
      <div className={`recommendation-interface ${className}`} style={{ maxWidth: '1200px', margin: '0 auto', padding: '20px' }}>
        <div style={{ textAlign: 'center', padding: '40px' }}>
          <h3>❌ 加载失败</h3>
          <p>{error}</p>
          <button 
            onClick={loadRecommendations} 
            style={{ 
              padding: '10px 20px', 
              background: '#3498db', 
              color: 'white', 
              border: 'none', 
              borderRadius: '6px', 
              cursor: 'pointer' 
            }}
          >
            重试
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className={`recommendation-interface ${className}`} style={{ maxWidth: '1200px', margin: '0 auto', padding: '20px' }}>
      {/* 顶部导航 */}
      <div style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center', 
        marginBottom: '20px', 
        paddingBottom: '15px', 
        borderBottom: '2px solid #e1e5e9' 
      }}>
        <h2 style={{ margin: 0, color: '#2c3e50', fontSize: '1.8rem' }}>🎯 个性化建议</h2>
        <div style={{ display: 'flex', gap: '10px' }}>
          {(['active', 'completed', 'all', 'stats'] as const).map(tab => (
            <button
              key={tab}
              style={{
                padding: '8px 16px',
                border: '2px solid #3498db',
                background: activeTab === tab ? '#3498db' : 'white',
                color: activeTab === tab ? 'white' : '#3498db',
                borderRadius: '6px',
                cursor: 'pointer',
                fontWeight: '500'
              }}
              onClick={() => setActiveTab(tab)}
            >
              {getTabLabel(tab)}
            </button>
          ))}
        </div>
      </div>

      {/* 过滤器 */}
      {activeTab !== 'stats' && (
        <FilterPanel 
          filters={filters} 
          onChange={setFilters}
          onReset={() => setFilters({ type: 'all', priority: 'all', status: 'all', difficulty: 'all' })}
        />
      )}

      {/* 内容区域 */}
      <div style={{ background: 'white', borderRadius: '12px', padding: '20px', boxShadow: '0 2px 10px rgba(0,0,0,0.1)' }}>
        {activeTab === 'stats' ? (
          <StatisticsView statistics={statistics} />
        ) : (
          <RecommendationList
            recommendations={recommendations}
            onStatusUpdate={handleStatusUpdate}
            onFeedbackSubmit={handleFeedbackSubmit}
            onProgressUpdate={handleProgressUpdate}
          />
        )}
      </div>
    </div>
  )
}

// 过滤器面板组件
const FilterPanel: React.FC<{
  filters: FilterOptions
  onChange: (filters: FilterOptions) => void
  onReset: () => void
}> = ({ filters, onChange, onReset }) => (
  <div style={{ 
    display: 'flex', 
    gap: '20px', 
    alignItems: 'center', 
    padding: '15px', 
    background: '#f8f9fa', 
    borderRadius: '8px', 
    marginBottom: '20px', 
    flexWrap: 'wrap' 
  }}>
    <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
      <label style={{ fontWeight: '500', color: '#2c3e50' }}>类型:</label>
      <select
        value={filters.type}
        onChange={(e) => onChange({ ...filters, type: e.target.value as any })}
        style={{ padding: '6px 12px', border: '1px solid #ddd', borderRadius: '4px', background: 'white' }}
      >
        <option value="all">全部</option>
        <option value={RecommendationType.IMMEDIATE}>立即行动</option>
        <option value={RecommendationType.SHORT_TERM}>短期改进</option>
        <option value={RecommendationType.LONG_TERM}>长期发展</option>
        <option value={RecommendationType.HABIT_FORMING}>习惯养成</option>
        <option value={RecommendationType.PRODUCTIVITY}>生产力</option>
        <option value={RecommendationType.WELLNESS}>健康福祉</option>
        <option value={RecommendationType.MOTIVATION}>激励</option>
      </select>
    </div>

    <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
      <label style={{ fontWeight: '500', color: '#2c3e50' }}>优先级:</label>
      <select
        value={filters.priority}
        onChange={(e) => onChange({ ...filters, priority: e.target.value as any })}
        style={{ padding: '6px 12px', border: '1px solid #ddd', borderRadius: '4px', background: 'white' }}
      >
        <option value="all">全部</option>
        <option value={RecommendationPriority.CRITICAL}>紧急</option>
        <option value={RecommendationPriority.HIGH}>高</option>
        <option value={RecommendationPriority.MEDIUM}>中</option>
        <option value={RecommendationPriority.LOW}>低</option>
      </select>
    </div>

    <button 
      onClick={onReset} 
      style={{ 
        padding: '6px 12px', 
        background: '#95a5a6', 
        color: 'white', 
        border: 'none', 
        borderRadius: '4px', 
        cursor: 'pointer' 
      }}
    >
      重置筛选
    </button>
  </div>
)

// 建议列表组件
const RecommendationList: React.FC<{
  recommendations: StoredRecommendation[]
  onStatusUpdate: (id: string, status: RecommendationStatus) => void
  onFeedbackSubmit: (id: string, feedbackType: FeedbackType, rating: number, comment?: string) => void
  onProgressUpdate: (id: string, progress: number) => void
}> = ({ recommendations, onStatusUpdate, onFeedbackSubmit, onProgressUpdate }) => {
  if (recommendations.length === 0) {
    return (
      <div style={{ textAlign: 'center', padding: '40px', color: '#666' }}>
        <h3 style={{ marginBottom: '10px', color: '#2c3e50' }}>📋 暂无建议</h3>
        <p>暂时没有符合条件的建议，请尝试调整筛选条件或生成新的建议。</p>
      </div>
    )
  }

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '20px' }}>
      {recommendations.map(recommendation => (
        <RecommendationCard
          key={recommendation.id}
          recommendation={recommendation}
          onStatusUpdate={onStatusUpdate}
          onFeedbackSubmit={onFeedbackSubmit}
          onProgressUpdate={onProgressUpdate}
        />
      ))}
    </div>
  )
}

// 建议卡片组件
const RecommendationCard: React.FC<RecommendationCardProps> = ({
  recommendation,
  onStatusUpdate,
  onFeedbackSubmit,
  onProgressUpdate
}) => {
  const [showDetails, setShowDetails] = useState(false)
  const [feedbackMode, setFeedbackMode] = useState(false)
  const [rating, setRating] = useState(5)
  const [comment, setComment] = useState('')

  const getPriorityColor = (priority: RecommendationPriority) => {
    switch (priority) {
      case RecommendationPriority.CRITICAL: return '#e74c3c'
      case RecommendationPriority.HIGH: return '#f39c12'
      case RecommendationPriority.MEDIUM: return '#3498db'
      case RecommendationPriority.LOW: return '#95a5a6'
      default: return '#3498db'
    }
  }

  const getDifficultyIcon = (difficulty: RecommendationDifficulty) => {
    switch (difficulty) {
      case RecommendationDifficulty.EASY: return '🟢'
      case RecommendationDifficulty.MEDIUM: return '🟡'
      case RecommendationDifficulty.HARD: return '🔴'
      default: return '🟡'
    }
  }

  const getTypeIcon = (type: RecommendationType) => {
    switch (type) {
      case RecommendationType.IMMEDIATE: return '⚡'
      case RecommendationType.SHORT_TERM: return '📅'
      case RecommendationType.LONG_TERM: return '🎯'
      case RecommendationType.HABIT_FORMING: return '🔄'
      case RecommendationType.PRODUCTIVITY: return '⚙️'
      case RecommendationType.WELLNESS: return '🌱'
      case RecommendationType.MOTIVATION: return '🔥'
      default: return '💡'
    }
  }

  const handleFeedbackSubmit = () => {
    onFeedbackSubmit(recommendation.id, FeedbackType.HELPFUL, rating, comment)
    setFeedbackMode(false)
    setComment('')
    setRating(5)
  }

  return (
    <div style={{ 
      border: '1px solid #e1e5e9', 
      borderRadius: '12px', 
      background: 'white', 
      overflow: 'hidden' 
    }}>
      <div style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center', 
        padding: '20px', 
        background: '#f8f9fa', 
        borderBottom: '1px solid #e1e5e9' 
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '12px', flex: 1 }}>
          <span style={{ fontSize: '1.5rem' }}>{getTypeIcon(recommendation.type)}</span>
          <h3 style={{ margin: 0, color: '#2c3e50', fontSize: '1.2rem', flex: 1 }}>
            {recommendation.title}
          </h3>
          <span style={{ 
            padding: '4px 8px', 
            borderRadius: '12px', 
            color: 'white', 
            fontSize: '0.8rem', 
            fontWeight: '500', 
            textTransform: 'uppercase',
            backgroundColor: getPriorityColor(recommendation.priority) 
          }}>
            {recommendation.priority}
          </span>
          <span style={{ fontSize: '1.2rem' }}>
            {getDifficultyIcon(recommendation.difficulty)}
          </span>
        </div>
        <button 
          onClick={() => setShowDetails(!showDetails)}
          style={{ 
            padding: '6px 12px', 
            background: '#3498db', 
            color: 'white', 
            border: 'none', 
            borderRadius: '6px', 
            cursor: 'pointer' 
          }}
        >
          {showDetails ? '收起' : '详情'}
        </button>
      </div>

      <div style={{ padding: '20px' }}>
        <p style={{ margin: '0 0 20px 0', color: '#5a6c7d', lineHeight: '1.6' }}>
          {recommendation.description}
        </p>
        
        {showDetails && (
          <div style={{ 
            marginBottom: '20px', 
            padding: '15px', 
            background: '#f8f9fa', 
            borderRadius: '8px' 
          }}>
            <div style={{ marginBottom: '15px' }}>
              <strong style={{ display: 'block', marginBottom: '5px', color: '#2c3e50' }}>
                预期结果:
              </strong>
              <p>{recommendation.expectedOutcome}</p>
            </div>
            
            <div style={{ marginBottom: '15px' }}>
              <strong style={{ display: 'block', marginBottom: '5px', color: '#2c3e50' }}>
                行动步骤:
              </strong>
              <ul style={{ margin: '5px 0 0 20px', color: '#5a6c7d' }}>
                {recommendation.actionSteps.map((step, index) => (
                  <li key={index}>{step}</li>
                ))}
              </ul>
            </div>
            
            <div style={{ marginBottom: '15px' }}>
              <strong style={{ display: 'block', marginBottom: '5px', color: '#2c3e50' }}>
                所需资源:
              </strong>
              <div style={{ display: 'flex', flexWrap: 'wrap', gap: '8px', marginTop: '5px' }}>
                {recommendation.resources.map((resource, index) => (
                  <span key={index} style={{ 
                    background: '#3498db', 
                    color: 'white', 
                    padding: '4px 8px', 
                    borderRadius: '4px', 
                    fontSize: '0.8rem' 
                  }}>
                    {resource}
                  </span>
                ))}
              </div>
            </div>
            
            <div>
              <strong style={{ display: 'block', marginBottom: '5px', color: '#2c3e50' }}>
                成功指标:
              </strong>
              <ul style={{ margin: '5px 0 0 20px', color: '#5a6c7d' }}>
                {recommendation.successMetrics.map((metric, index) => (
                  <li key={index}>{metric}</li>
                ))}
              </ul>
            </div>
          </div>
        )}

        {/* 进度条 */}
        <div style={{ marginBottom: '20px' }}>
          <div style={{ fontSize: '0.9rem', color: '#5a6c7d', marginBottom: '8px' }}>
            执行进度: {Math.round(recommendation.executionProgress * 100)}%
          </div>
          <div style={{ 
            width: '100%', 
            height: '8px', 
            background: '#e1e5e9', 
            borderRadius: '4px', 
            overflow: 'hidden' 
          }}>
            <div style={{ 
              height: '100%', 
              background: '#27ae60', 
              width: `${recommendation.executionProgress * 100}%`,
              transition: 'width 0.3s ease'
            }} />
          </div>
        </div>

        {/* 状态和反馈按钮 */}
        <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
          {recommendation.status === RecommendationStatus.ACTIVE && (
            <>
              <button 
                onClick={() => onStatusUpdate(recommendation.id, RecommendationStatus.COMPLETED)}
                style={{ 
                  padding: '8px 16px', 
                  background: '#27ae60', 
                  color: 'white', 
                  border: 'none', 
                  borderRadius: '6px', 
                  cursor: 'pointer', 
                  fontWeight: '500' 
                }}
              >
                ✅ 标记完成
              </button>
              <button 
                onClick={() => onStatusUpdate(recommendation.id, RecommendationStatus.DISMISSED)}
                style={{ 
                  padding: '8px 16px', 
                  background: '#95a5a6', 
                  color: 'white', 
                  border: 'none', 
                  borderRadius: '6px', 
                  cursor: 'pointer', 
                  fontWeight: '500' 
                }}
              >
                ❌ 忽略
              </button>
              <button 
                onClick={() => setFeedbackMode(!feedbackMode)}
                style={{ 
                  padding: '8px 16px', 
                  background: '#f39c12', 
                  color: 'white', 
                  border: 'none', 
                  borderRadius: '6px', 
                  cursor: 'pointer', 
                  fontWeight: '500' 
                }}
              >
                💬 反馈
              </button>
            </>
          )}
        </div>

        {/* 反馈表单 */}
        {feedbackMode && (
          <div style={{ 
            marginTop: '20px', 
            padding: '15px', 
            background: '#f8f9fa', 
            borderRadius: '8px' 
          }}>
            <div style={{ marginBottom: '15px' }}>
              <label style={{ 
                display: 'block', 
                marginBottom: '8px', 
                fontWeight: '500', 
                color: '#2c3e50' 
              }}>
                评分:
              </label>
              <div style={{ display: 'flex', gap: '5px' }}>
                {[1, 2, 3, 4, 5].map(star => (
                  <button
                    key={star}
                    onClick={() => setRating(star)}
                    style={{ 
                      background: 'none', 
                      border: 'none', 
                      fontSize: '1.5rem', 
                      cursor: 'pointer', 
                      opacity: star <= rating ? 1 : 0.3 
                    }}
                  >
                    ⭐
                  </button>
                ))}
              </div>
            </div>
            
            <div style={{ marginBottom: '15px' }}>
              <label style={{ 
                display: 'block', 
                marginBottom: '8px', 
                fontWeight: '500', 
                color: '#2c3e50' 
              }}>
                评论 (可选):
              </label>
              <textarea
                value={comment}
                onChange={(e) => setComment(e.target.value)}
                placeholder="分享您的想法..."
                rows={3}
                style={{ 
                  width: '100%', 
                  padding: '10px', 
                  border: '1px solid #ddd', 
                  borderRadius: '6px', 
                  resize: 'vertical' 
                }}
              />
            </div>
            
            <div style={{ display: 'flex', gap: '10px' }}>
              <button 
                onClick={handleFeedbackSubmit} 
                style={{ 
                  padding: '8px 16px', 
                  background: '#27ae60', 
                  color: 'white', 
                  border: 'none', 
                  borderRadius: '6px', 
                  cursor: 'pointer', 
                  fontWeight: '500' 
                }}
              >
                提交反馈
              </button>
              <button 
                onClick={() => setFeedbackMode(false)} 
                style={{ 
                  padding: '8px 16px', 
                  background: '#95a5a6', 
                  color: 'white', 
                  border: 'none', 
                  borderRadius: '6px', 
                  cursor: 'pointer', 
                  fontWeight: '500' 
                }}
              >
                取消
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

// 统计视图组件
const StatisticsView: React.FC<{ statistics: RecommendationStatistics | null }> = ({ statistics }) => {
  if (!statistics) {
    return <div>暂无统计数据</div>
  }

  return (
    <div style={{ padding: '20px' }}>
      <div style={{ 
        display: 'grid', 
        gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', 
        gap: '20px' 
      }}>
        <div style={{ 
          background: 'white', 
          padding: '20px', 
          borderRadius: '12px', 
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)', 
          textAlign: 'center' 
        }}>
          <h3 style={{ margin: '0 0 10px 0', color: '#2c3e50', fontSize: '1rem' }}>
            📊 总建议数
          </h3>
          <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#3498db' }}>
            {statistics.totalRecommendations}
          </div>
        </div>
        
        <div style={{ 
          background: 'white', 
          padding: '20px', 
          borderRadius: '12px', 
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)', 
          textAlign: 'center' 
        }}>
          <h3 style={{ margin: '0 0 10px 0', color: '#2c3e50', fontSize: '1rem' }}>
            🎯 活跃建议
          </h3>
          <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#3498db' }}>
            {statistics.activeRecommendations}
          </div>
        </div>
        
        <div style={{ 
          background: 'white', 
          padding: '20px', 
          borderRadius: '12px', 
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)', 
          textAlign: 'center' 
        }}>
          <h3 style={{ margin: '0 0 10px 0', color: '#2c3e50', fontSize: '1rem' }}>
            ✅ 已完成
          </h3>
          <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#3498db' }}>
            {statistics.completedRecommendations}
          </div>
        </div>
        
        <div style={{ 
          background: 'white', 
          padding: '20px', 
          borderRadius: '12px', 
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)', 
          textAlign: 'center' 
        }}>
          <h3 style={{ margin: '0 0 10px 0', color: '#2c3e50', fontSize: '1rem' }}>
            📈 完成率
          </h3>
          <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#3498db' }}>
            {(statistics.completionRate * 100).toFixed(1)}%
          </div>
        </div>
        
        <div style={{ 
          background: 'white', 
          padding: '20px', 
          borderRadius: '12px', 
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)', 
          textAlign: 'center' 
        }}>
          <h3 style={{ margin: '0 0 10px 0', color: '#2c3e50', fontSize: '1rem' }}>
            ⭐ 平均评分
          </h3>
          <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#3498db' }}>
            {statistics.averageRating.toFixed(1)}
          </div>
        </div>
        
        <div style={{ 
          background: 'white', 
          padding: '20px', 
          borderRadius: '12px', 
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)', 
          textAlign: 'center' 
        }}>
          <h3 style={{ margin: '0 0 10px 0', color: '#2c3e50', fontSize: '1rem' }}>
            💬 反馈数量
          </h3>
          <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#3498db' }}>
            {statistics.userEngagement.feedbackCount}
          </div>
        </div>
      </div>
    </div>
  )
}

// 辅助函数
const getTabLabel = (tab: 'active' | 'completed' | 'all' | 'stats'): string => {
  switch (tab) {
    case 'active': return '活跃建议'
    case 'completed': return '已完成'
    case 'all': return '全部建议'
    case 'stats': return '统计数据'
    default: return tab
  }
}

export default RecommendationInterface 