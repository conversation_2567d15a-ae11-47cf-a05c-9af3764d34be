import React, { useState, useEffect, useMemo } from 'react';
import './FeedbackAnalysis.css';

// 反馈类型定义
interface Feedback {
  id: string;
  userId?: string;
  type: 'usability' | 'bug' | 'feature' | 'performance' | 'other';
  severity: 'low' | 'medium' | 'high' | 'urgent';
  content: string;
  rating: number;
  bugDetails?: {
    steps: string;
    expected: string;
    actual: string;
  };
  timestamp: number;
  status: 'new' | 'reviewed' | 'in-progress' | 'resolved' | 'closed';
  tags?: string[];
  assignee?: string;
}

// 分析报告类型
interface AnalysisReport {
  totalFeedbacks: number;
  averageRating: number;
  typeDistribution: Record<string, number>;
  severityDistribution: Record<string, number>;
  statusDistribution: Record<string, number>;
  ratingDistribution: Record<number, number>;
  timeAnalysis: {
    dailyTrends: Array<{ date: string; count: number; avgRating: number }>;
    hourlyDistribution: Record<number, number>;
  };
  topIssues: Array<{
    category: string;
    count: number;
    avgRating: number;
    keywords: string[];
  }>;
  priorityRecommendations: Array<{
    id: string;
    title: string;
    priority: 'urgent' | 'high' | 'medium' | 'low';
    impact: string;
    effort: 'low' | 'medium' | 'high';
    reasoning: string;
  }>;
}

// 关键词分析
interface KeywordAnalysis {
  keyword: string;
  frequency: number;
  sentiment: 'positive' | 'negative' | 'neutral';
  associatedTypes: string[];
}

const FeedbackAnalysis: React.FC = () => {
  const [feedbacks, setFeedbacks] = useState<Feedback[]>([]);
  const [analysisReport, setAnalysisReport] = useState<AnalysisReport | null>(null);
  const [keywordAnalysis, setKeywordAnalysis] = useState<KeywordAnalysis[]>([]);
  const [selectedDateRange, setSelectedDateRange] = useState<string>('7d');
  const [selectedFilter, setSelectedFilter] = useState<string>('all');
  const [loading, setLoading] = useState(false);

  // 模拟反馈数据生成器
  const generateMockFeedbacks = (): Feedback[] => {
    const mockFeedbacks: Feedback[] = [];
    const types: Array<Feedback['type']> = ['usability', 'bug', 'feature', 'performance', 'other'];
    const severities: Array<Feedback['severity']> = ['low', 'medium', 'high', 'urgent'];
    const statuses: Array<Feedback['status']> = ['new', 'reviewed', 'in-progress', 'resolved', 'closed'];
    
    const bugContents = [
      '游戏在种植时偶尔会卡顿',
      '收获作物时金币显示错误',
      '商店界面在手机上显示不完整',
      '应用监控功能无法正常授权',
      '专注模式下仍然收到通知',
      '统计报表数据更新延迟',
      '白名单设置保存失败',
      '用户登录时出现闪退',
      '作物生长时间计算错误',
      '界面切换时动画卡顿'
    ];

    const usabilityContents = [
      '种植按钮位置可以更明显',
      '商店分类导航需要优化',
      '设置页面布局可以改进',
      '教程指引不够清晰',
      '操作反馈提示太少',
      '色彩搭配可以更温馨',
      '字体大小可以调整',
      '导航结构需要简化',
      '功能入口不够直观',
      '新手引导需要改进'
    ];

    const featureContents = [
      '希望添加社交功能',
      '建议增加成就系统',
      '希望有更多作物种类',
      '期望添加天气系统',
      '建议增加农场装饰',
      '希望有背景音乐',
      '期望添加好友功能',
      '建议增加日夜循环',
      '希望有宠物系统',
      '期望添加季节变化'
    ];

    for (let i = 0; i < 150; i++) {
      const type = types[Math.floor(Math.random() * types.length)];
      let content = '';
      
      if (type === 'bug') {
        content = bugContents[Math.floor(Math.random() * bugContents.length)];
      } else if (type === 'usability') {
        content = usabilityContents[Math.floor(Math.random() * usabilityContents.length)];
      } else if (type === 'feature') {
        content = featureContents[Math.floor(Math.random() * featureContents.length)];
      } else {
        content = `${type}相关的反馈内容 ${i + 1}`;
      }

      const severity = severities[Math.floor(Math.random() * severities.length)];
      const status = statuses[Math.floor(Math.random() * statuses.length)];
      const rating = Math.floor(Math.random() * 5) + 1;
      
      // 近期数据为主，少量历史数据
      const daysAgo = Math.random() < 0.7 ? Math.floor(Math.random() * 7) : Math.floor(Math.random() * 30);
      const timestamp = Date.now() - daysAgo * 24 * 60 * 60 * 1000 - Math.floor(Math.random() * 24 * 60 * 60 * 1000);

      mockFeedbacks.push({
        id: `feedback_${i + 1}`,
        userId: `user_${Math.floor(Math.random() * 50) + 1}`,
        type,
        severity,
        content,
        rating,
        timestamp,
        status,
        tags: [`tag_${Math.floor(Math.random() * 10) + 1}`],
        assignee: Math.random() > 0.5 ? `dev_${Math.floor(Math.random() * 5) + 1}` : undefined,
        ...(type === 'bug' && {
          bugDetails: {
            steps: '1. 打开应用 2. 进行操作 3. 观察问题',
            expected: '功能正常运行',
            actual: '出现错误或异常'
          }
        })
      });
    }

    return mockFeedbacks;
  };

  // 加载反馈数据
  useEffect(() => {
    setLoading(true);
    
    // 尝试从localStorage加载真实数据
    const storedFeedbacks = localStorage.getItem('userFeedbacks');
    let loadedFeedbacks: Feedback[] = [];
    
    if (storedFeedbacks) {
      try {
        loadedFeedbacks = JSON.parse(storedFeedbacks);
      } catch (error) {
        console.warn('解析存储的反馈数据失败:', error);
      }
    }
    
    // 如果没有真实数据或数据太少，使用模拟数据
    if (loadedFeedbacks.length < 10) {
      const mockData = generateMockFeedbacks();
      // 限制模拟数据量以提高性能
      loadedFeedbacks = [...loadedFeedbacks, ...mockData.slice(0, 100)];
    }
    
    setFeedbacks(loadedFeedbacks);
    setLoading(false);
  }, []);

  // 过滤反馈数据
  const filteredFeedbacks = useMemo(() => {
    let filtered = [...feedbacks];
    
    // 时间范围过滤
    const now = Date.now();
    const ranges: Record<string, number> = {
      '1d': 24 * 60 * 60 * 1000,
      '7d': 7 * 24 * 60 * 60 * 1000,
      '30d': 30 * 24 * 60 * 60 * 1000,
      'all': Infinity
    };
    
    if (ranges[selectedDateRange] !== Infinity) {
      const cutoff = now - ranges[selectedDateRange];
      filtered = filtered.filter(f => f.timestamp >= cutoff);
    }
    
    // 类型过滤
    if (selectedFilter !== 'all') {
      filtered = filtered.filter(f => f.type === selectedFilter || f.severity === selectedFilter || f.status === selectedFilter);
    }
    
    return filtered;
  }, [feedbacks, selectedDateRange, selectedFilter]);

  // 生成分析报告 - 添加防抖和错误处理
  useEffect(() => {
    if (filteredFeedbacks.length === 0) {
      setAnalysisReport(null);
      setKeywordAnalysis([]);
      return;
    }

    // 防抖处理，避免频繁计算
    const timeoutId = setTimeout(() => {
      try {
        setLoading(true);
        const report = generateAnalysisReport(filteredFeedbacks);
        setAnalysisReport(report);
        
        const keywords = extractKeywords(filteredFeedbacks);
        setKeywordAnalysis(keywords);
      } catch (error) {
        console.error('分析报告生成失败:', error);
        setAnalysisReport(null);
        setKeywordAnalysis([]);
      } finally {
        setLoading(false);
      }
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [filteredFeedbacks]);

  // 生成分析报告
  const generateAnalysisReport = (feedbacks: Feedback[]): AnalysisReport => {
    try {
      if (!feedbacks || !Array.isArray(feedbacks) || feedbacks.length === 0) {
        return {
          totalFeedbacks: 0,
          averageRating: 0,
          typeDistribution: {},
          severityDistribution: {},
          statusDistribution: {},
          ratingDistribution: {},
          timeAnalysis: {
            dailyTrends: [],
            hourlyDistribution: {}
          },
          topIssues: [],
          priorityRecommendations: []
        };
      }

      const totalFeedbacks = feedbacks.length;
      const averageRating = feedbacks.reduce((sum, f) => sum + (f.rating || 0), 0) / totalFeedbacks;

      // 类型分布
      const typeDistribution: Record<string, number> = {};
      feedbacks.forEach(f => {
        const type = f.type || 'other';
        typeDistribution[type] = (typeDistribution[type] || 0) + 1;
      });

      // 严重程度分布
      const severityDistribution: Record<string, number> = {};
      feedbacks.forEach(f => {
        const severity = f.severity || 'low';
        severityDistribution[severity] = (severityDistribution[severity] || 0) + 1;
      });

      // 状态分布
      const statusDistribution: Record<string, number> = {};
      feedbacks.forEach(f => {
        const status = f.status || 'new';
        statusDistribution[status] = (statusDistribution[status] || 0) + 1;
      });

      // 评分分布
      const ratingDistribution: Record<number, number> = {};
      feedbacks.forEach(f => {
        const rating = f.rating || 0;
        ratingDistribution[rating] = (ratingDistribution[rating] || 0) + 1;
      });

      // 时间趋势分析
      const dailyTrends: Array<{ date: string; count: number; avgRating: number }> = [];
      const hourlyDistribution: Record<number, number> = {};
      
      // 按日期分组
      const dailyGroups: Record<string, Feedback[]> = {};
      feedbacks.forEach(f => {
        try {
          const date = new Date(f.timestamp).toISOString().split('T')[0];
          if (!dailyGroups[date]) dailyGroups[date] = [];
          dailyGroups[date].push(f);
          
          const hour = new Date(f.timestamp).getHours();
          if (!isNaN(hour)) {
            hourlyDistribution[hour] = (hourlyDistribution[hour] || 0) + 1;
          }
        } catch (error) {
          console.warn('处理反馈时间数据时出错:', error, f);
        }
      });

      Object.entries(dailyGroups).forEach(([date, dayFeedbacks]) => {
        if (dayFeedbacks.length > 0) {
          const validRatings = dayFeedbacks.filter(f => f.rating && !isNaN(f.rating));
          const avgRating = validRatings.length > 0 
            ? validRatings.reduce((sum, f) => sum + f.rating, 0) / validRatings.length 
            : 0;
          
          dailyTrends.push({
            date,
            count: dayFeedbacks.length,
            avgRating
          });
        }
      });

      // 排序日期
      dailyTrends.sort((a, b) => a.date.localeCompare(b.date));

      // 主要问题分析
      const topIssues = Object.entries(typeDistribution)
        .map(([type, count]) => {
          const typeFeedbacks = feedbacks.filter(f => f.type === type);
          const validRatings = typeFeedbacks.filter(f => f.rating && !isNaN(f.rating));
          const avgRating = validRatings.length > 0 
            ? validRatings.reduce((sum, f) => sum + f.rating, 0) / validRatings.length 
            : 0;
          
          const keywords = extractKeywordsFromText(
            typeFeedbacks.map(f => f.content || '').filter(content => content.trim()).join(' ')
          );
          
          return {
            category: type,
            count,
            avgRating,
            keywords: keywords.slice(0, 5).map(k => k.keyword)
          };
        })
        .sort((a, b) => b.count - a.count);

      // 优先级建议
      const priorityRecommendations = generatePriorityRecommendations(feedbacks);

      return {
        totalFeedbacks,
        averageRating,
        typeDistribution,
        severityDistribution,
        statusDistribution,
        ratingDistribution,
        timeAnalysis: {
          dailyTrends,
          hourlyDistribution
        },
        topIssues,
        priorityRecommendations
      };
    } catch (error) {
      console.error('生成分析报告时出错:', error);
      // 返回空的报告而不是崩溃
      return {
        totalFeedbacks: 0,
        averageRating: 0,
        typeDistribution: {},
        severityDistribution: {},
        statusDistribution: {},
        ratingDistribution: {},
        timeAnalysis: {
          dailyTrends: [],
          hourlyDistribution: {}
        },
        topIssues: [],
        priorityRecommendations: []
      };
    }
  };

  // 提取关键词
  const extractKeywords = (feedbacks: Feedback[]): KeywordAnalysis[] => {
    const text = feedbacks.map(f => f.content).join(' ');
    return extractKeywordsFromText(text);
  };

  // 从文本提取关键词
  const extractKeywordsFromText = (text: string): KeywordAnalysis[] => {
    try {
      if (!text || typeof text !== 'string' || text.trim().length === 0) {
        return [];
      }

      const commonWords = ['的', '了', '是', '在', '有', '和', '我', '你', '他', '她', '它', '们', '这', '那', '也', '都', '要', '可以', '能', '会', '就', '还', '不', '没', '很', '非常', '比较', '更', '最', '太', '好', '没有', '应该', '建议', '希望', '需要', '时候', '问题', '功能', '用户', '系统'];
      
      const words = text
        .toLowerCase()
        .replace(/[^\u4e00-\u9fa5a-z\s]/g, ' ')
        .split(/\s+/)
        .filter(word => word.length > 1 && !commonWords.includes(word));

      if (words.length === 0) {
        return [];
      }

      const wordCount: Record<string, number> = {};
      words.forEach(word => {
        wordCount[word] = (wordCount[word] || 0) + 1;
      });

      return Object.entries(wordCount)
        .map(([keyword, frequency]) => ({
          keyword,
          frequency,
          sentiment: determineSentiment(keyword) as 'positive' | 'negative' | 'neutral',
          associatedTypes: ['usability', 'bug', 'feature'] // 简化处理
        }))
        .sort((a, b) => b.frequency - a.frequency)
        .slice(0, 20);
    } catch (error) {
      console.error('提取关键词时出错:', error);
      return [];
    }
  };

  // 判断情感倾向
  const determineSentiment = (word: string): string => {
    const positiveWords = ['好', '棒', '优秀', '喜欢', '满意', '完美', '赞', '不错', '流畅', '清晰', '简单', '方便'];
    const negativeWords = ['差', '坏', '糟糕', '讨厌', '失望', '错误', '卡顿', '闪退', '缓慢', '复杂', '困难', '问题'];
    
    if (positiveWords.some(pw => word.includes(pw))) return 'positive';
    if (negativeWords.some(nw => word.includes(nw))) return 'negative';
    return 'neutral';
  };

  // 生成优先级建议
  const generatePriorityRecommendations = (feedbacks: Feedback[]): AnalysisReport['priorityRecommendations'] => {
    const recommendations: AnalysisReport['priorityRecommendations'] = [];
    
    // 高优先级：紧急和高严重度问题
    const urgentIssues = feedbacks.filter(f => f.severity === 'urgent' || f.severity === 'high');
    if (urgentIssues.length > 0) {
      recommendations.push({
        id: 'urgent_fixes',
        title: '修复紧急和高严重度问题',
        priority: 'urgent',
        impact: '直接影响用户体验和产品稳定性',
        effort: 'high',
        reasoning: `发现${urgentIssues.length}个紧急或高严重度问题，需要立即处理`
      });
    }

    // 中优先级：常见可用性问题
    const usabilityIssues = feedbacks.filter(f => f.type === 'usability');
    if (usabilityIssues.length > feedbacks.length * 0.2) {
      recommendations.push({
        id: 'usability_improvements',
        title: '改进用户界面和交互体验',
        priority: 'high',
        impact: '提升整体用户满意度',
        effort: 'medium',
        reasoning: `可用性问题占比${((usabilityIssues.length / feedbacks.length) * 100).toFixed(1)}%，需要优化`
      });
    }

    // 功能请求分析
    const featureRequests = feedbacks.filter(f => f.type === 'feature');
    if (featureRequests.length > 0) {
      recommendations.push({
        id: 'feature_development',
        title: '开发用户最需要的新功能',
        priority: 'medium',
        impact: '增加产品价值和用户粘性',
        effort: 'high',
        reasoning: `收到${featureRequests.length}个功能请求，建议进行需求分析和优先级排序`
      });
    }

    // 性能优化
    const performanceIssues = feedbacks.filter(f => f.type === 'performance');
    if (performanceIssues.length > 0) {
      recommendations.push({
        id: 'performance_optimization',
        title: '性能优化和稳定性提升',
        priority: 'high',
        impact: '提升应用响应速度和稳定性',
        effort: 'medium',
        reasoning: `性能相关问题需要技术优化解决`
      });
    }

    return recommendations;
  };

  // 渲染类型分布图表
  const renderTypeChart = () => {
    if (!analysisReport) return null;

    const { typeDistribution } = analysisReport;
    const total = Object.values(typeDistribution).reduce((sum, count) => sum + count, 0);
    
    const typeLabels: Record<string, string> = {
      'usability': '可用性',
      'bug': '错误报告',
      'feature': '功能建议',
      'performance': '性能问题',
      'other': '其他'
    };

    return (
      <div className="chart-container">
        <h3>反馈类型分布</h3>
        <div className="pie-chart">
          {Object.entries(typeDistribution).map(([type, count]) => {
            const percentage = (count / total) * 100;
            return (
              <div key={type} className="chart-item">
                <div className="chart-bar">
                  <div 
                    className={`chart-fill chart-${type}`}
                    style={{ width: `${percentage}%` }}
                  ></div>
                </div>
                <div className="chart-label">
                  <span className="type-name">{typeLabels[type] || type}</span>
                  <span className="type-count">{count} ({percentage.toFixed(1)}%)</span>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  // 渲染严重程度分析
  const renderSeverityAnalysis = () => {
    if (!analysisReport) return null;

    const { severityDistribution } = analysisReport;
    const severityLabels: Record<string, string> = {
      'urgent': '紧急',
      'high': '高',
      'medium': '中等',
      'low': '低'
    };

    const severityColors: Record<string, string> = {
      'urgent': '#ff4757',
      'high': '#ff6b7a',
      'medium': '#ffa502',
      'low': '#2ed573'
    };

    return (
      <div className="severity-analysis">
        <h3>严重程度分析</h3>
        <div className="severity-grid">
          {Object.entries(severityDistribution).map(([severity, count]) => (
            <div key={severity} className="severity-card">
              <div 
                className="severity-indicator"
                style={{ backgroundColor: severityColors[severity] }}
              ></div>
              <div className="severity-info">
                <div className="severity-label">{severityLabels[severity]}</div>
                <div className="severity-count">{count}</div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  // 渲染时间趋势
  const renderTimeTrends = () => {
    if (!analysisReport?.timeAnalysis.dailyTrends.length) return null;

    const { dailyTrends } = analysisReport.timeAnalysis;
    const maxCount = Math.max(...dailyTrends.map(d => d.count));

    return (
      <div className="time-trends">
        <h3>每日反馈趋势</h3>
        <div className="trend-chart">
          {dailyTrends.map((day, index) => (
            <div key={day.date} className="trend-bar">
              <div 
                className="trend-fill"
                style={{ 
                  height: `${(day.count / maxCount) * 100}%`,
                  backgroundColor: day.avgRating >= 4 ? '#2ed573' : day.avgRating >= 3 ? '#ffa502' : '#ff4757'
                }}
                title={`${day.date}: ${day.count}条反馈, 平均评分${day.avgRating.toFixed(1)}`}
              ></div>
              <div className="trend-label">
                {(() => {
                  const date = new Date(day.date);
                  return `${date.getMonth() + 1}/${date.getDate()}`;
                })()}
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  // 渲染关键词云
  const renderKeywordCloud = () => {
    if (!keywordAnalysis.length) {
      return (
        <div className="keyword-cloud">
          <h3>关键词分析</h3>
          <div className="no-keywords">
            <p>暂无关键词数据</p>
          </div>
        </div>
      );
    }

    return (
      <div className="keyword-cloud">
        <h3>关键词分析</h3>
        <div className="keywords">
          {keywordAnalysis.slice(0, 15).map((keyword, index) => (
            <span 
              key={`${keyword.keyword}_${index}`}
              className={`keyword keyword-${keyword.sentiment}`}
              style={{ 
                fontSize: `${Math.max(12, Math.min(24, keyword.frequency * 2))}px`,
                opacity: Math.max(0.6, Math.min(1, keyword.frequency / 10))
              }}
              title={`出现频率: ${keyword.frequency}次, 情感倾向: ${keyword.sentiment === 'positive' ? '积极' : keyword.sentiment === 'negative' ? '消极' : '中性'}`}
            >
              {keyword.keyword} ({keyword.frequency})
            </span>
          ))}
        </div>
      </div>
    );
  };

  // 渲染优先级建议
  const renderPriorityRecommendations = () => {
    if (!analysisReport?.priorityRecommendations.length) return null;

    const priorityColors: Record<string, string> = {
      'urgent': '#ff4757',
      'high': '#ff6b7a',
      'medium': '#ffa502',
      'low': '#2ed573'
    };

    return (
      <div className="priority-recommendations">
        <h3>优化建议</h3>
        <div className="recommendations-list">
          {analysisReport.priorityRecommendations.map((rec, index) => (
            <div key={rec.id} className="recommendation-card">
              <div className="recommendation-header">
                <div 
                  className="priority-badge"
                  style={{ backgroundColor: priorityColors[rec.priority] }}
                >
                  {rec.priority.toUpperCase()}
                </div>
                <h4>{rec.title}</h4>
              </div>
              <div className="recommendation-content">
                <p><strong>影响:</strong> {rec.impact}</p>
                <p><strong>工作量:</strong> {rec.effort}</p>
                <p><strong>原因:</strong> {rec.reasoning}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="feedback-analysis loading">
        <div className="loading-spinner">加载中...</div>
      </div>
    );
  }

  return (
    <div className="feedback-analysis">
      <div className="analysis-header">
        <h2>用户反馈分析报告</h2>
        <div className="analysis-controls">
          <select 
            value={selectedDateRange} 
            onChange={(e) => setSelectedDateRange(e.target.value)}
            className="time-range-select"
          >
            <option value="1d">最近1天</option>
            <option value="7d">最近7天</option>
            <option value="30d">最近30天</option>
            <option value="all">全部时间</option>
          </select>
          <select 
            value={selectedFilter} 
            onChange={(e) => setSelectedFilter(e.target.value)}
            className="filter-select"
          >
            <option value="all">全部类型</option>
            <option value="usability">可用性</option>
            <option value="bug">错误报告</option>
            <option value="feature">功能建议</option>
            <option value="performance">性能问题</option>
            <option value="urgent">紧急</option>
            <option value="high">高优先级</option>
          </select>
        </div>
      </div>

      {analysisReport ? (
        <div className="analysis-content">
          {/* 概览统计 */}
          <div className="overview-stats">
            <div className="stat-card">
              <div className="stat-number">{analysisReport.totalFeedbacks}</div>
              <div className="stat-label">总反馈数</div>
            </div>
            <div className="stat-card">
              <div className="stat-number">{analysisReport.averageRating.toFixed(1)}</div>
              <div className="stat-label">平均评分</div>
            </div>
            <div className="stat-card">
              <div className="stat-number">
                {(analysisReport.statusDistribution['resolved'] || 0) + (analysisReport.statusDistribution['closed'] || 0)}
              </div>
              <div className="stat-label">已解决</div>
            </div>
            <div className="stat-card">
              <div className="stat-number">
                {Math.round(((analysisReport.statusDistribution['resolved'] || 0) + (analysisReport.statusDistribution['closed'] || 0)) / analysisReport.totalFeedbacks * 100)}%
              </div>
              <div className="stat-label">解决率</div>
            </div>
          </div>

          {/* 图表区域 */}
          <div className="charts-grid">
            {renderTypeChart()}
            {renderSeverityAnalysis()}
            {renderTimeTrends()}
            {renderKeywordCloud()}
          </div>

          {/* 优先级建议 */}
          {renderPriorityRecommendations()}

          {/* 详细分析 */}
          <div className="detailed-analysis">
            <h3>详细分析</h3>
            
            {/* 主要问题 */}
            <div className="top-issues">
              <h4>主要问题分类</h4>
              {analysisReport.topIssues.map((issue, index) => (
                <div key={issue.category} className="issue-item">
                  <div className="issue-header">
                    <span className="issue-category">{issue.category}</span>
                    <span className="issue-count">{issue.count}条</span>
                    <span className="issue-rating">评分: {issue.avgRating.toFixed(1)}</span>
                  </div>
                  <div className="issue-keywords">
                    关键词: {issue.keywords.join(', ')}
                  </div>
                </div>
              ))}
            </div>

            {/* 时间分布分析 */}
            <div className="hourly-distribution">
              <h4>小时分布分析</h4>
              <div className="hourly-chart">
                {Object.entries(analysisReport.timeAnalysis.hourlyDistribution)
                  .sort(([a], [b]) => parseInt(a) - parseInt(b))
                  .map(([hour, count]) => (
                    <div key={hour} className="hourly-bar">
                      <div className="hourly-count">{count}</div>
                      <div className="hourly-time">{hour}时</div>
                    </div>
                  ))}
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div className="no-data">
          <p>暂无分析数据</p>
        </div>
      )}
    </div>
  );
};

export default FeedbackAnalysis;