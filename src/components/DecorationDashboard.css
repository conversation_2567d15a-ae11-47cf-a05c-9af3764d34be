.decoration-dashboard {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
  overflow: hidden;
}

/* 头部区域 */
.dashboard__header {
  background: white;
  border-bottom: 2px solid #e2e8f0;
  padding: 1.5rem 2rem;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.header__content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.header__title h1 {
  font-size: 2rem;
  font-weight: bold;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0 0 0.25rem 0;
}

.header__title p {
  color: #64748b;
  margin: 0;
  font-size: 1rem;
}

.header__actions {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.btn {
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  font-size: 0.875rem;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.btn--toggle {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
}

.btn--toggle:hover {
  background: linear-gradient(135deg, #059669, #047857);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
}

.btn--toggle.btn--active {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

.btn--close {
  background: #ef4444;
  color: white;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  padding: 0;
  font-size: 1.25rem;
}

.btn--close:hover {
  background: #dc2626;
}

/* 快速统计 */
.header__stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 1rem;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 1.25rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  border: 2px solid transparent;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.2s ease;
}

.stat-card:hover {
  border-color: #3b82f6;
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
}

.stat-card__icon {
  font-size: 2rem;
  width: 3rem;
  height: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  border-radius: 12px;
  color: white;
}

.stat-card__content {
  flex: 1;
}

.stat-card__value {
  font-size: 1.5rem;
  font-weight: bold;
  color: #1f2937;
  margin-bottom: 0.25rem;
}

.stat-card__label {
  color: #6b7280;
  font-size: 0.875rem;
  font-weight: 500;
}

/* 导航标签 */
.dashboard__nav {
  background: white;
  border-bottom: 1px solid #e2e8f0;
  padding: 0 2rem;
}

.nav__tabs {
  display: flex;
  gap: 0.5rem;
  overflow-x: auto;
  padding: 0.5rem 0;
}

.nav__tab {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  background: transparent;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  color: #6b7280;
  font-weight: 500;
}

.nav__tab:hover {
  background: #f8fafc;
  color: #374151;
}

.nav__tab--active {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  color: white;
  font-weight: 600;
}

.tab__icon {
  font-size: 1.25rem;
}

.tab__label {
  font-size: 0.875rem;
}

.nav__description {
  padding: 0.75rem 0;
  color: #6b7280;
  font-size: 0.875rem;
  border-top: 1px solid #f1f5f9;
}

/* 主要内容区域 */
.dashboard__content {
  flex: 1;
  overflow: auto;
  padding: 2rem;
}

/* 总览面板 */
.overview-panel {
  max-width: 1200px;
  margin: 0 auto;
}

.overview__section {
  margin-bottom: 3rem;
}

.section__title {
  font-size: 1.5rem;
  font-weight: bold;
  color: #1f2937;
  margin-bottom: 1.5rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #e2e8f0;
}

/* 当前主题信息 */
.current-theme {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.theme-info {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 2rem;
  align-items: start;
}

.theme-preview {
  width: 100%;
  height: 200px;
  background-size: cover;
  background-position: center;
  border-radius: 12px;
  border: 3px solid #e5e7eb;
  position: relative;
  overflow: hidden;
}

.theme-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
  color: white;
  padding: 1.5rem;
}

.theme-overlay h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.25rem;
  font-weight: bold;
}

.theme-overlay p {
  margin: 0;
  font-size: 0.875rem;
  opacity: 0.9;
}

.theme-effects h4 {
  color: #374151;
  margin-bottom: 1rem;
  font-size: 1.125rem;
}

.theme-effects ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.theme-effects li {
  background: #f3f4f6;
  padding: 0.75rem 1rem;
  border-radius: 8px;
  margin-bottom: 0.5rem;
  color: #374151;
  position: relative;
  padding-left: 2.5rem;
}

.theme-effects li::before {
  content: '✨';
  position: absolute;
  left: 1rem;
  top: 0.75rem;
}

/* 推荐内容 */
.recommendations {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.recommendations h3 {
  color: #374151;
  margin-bottom: 1.5rem;
  font-size: 1.25rem;
}

.recommendation-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
}

.recommendation-card {
  background: #f8fafc;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  padding: 1.5rem;
  transition: all 0.2s ease;
}

.recommendation-card:hover {
  border-color: #3b82f6;
  transform: translateY(-2px);
}

.rec-card__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.rec-card__header h4 {
  margin: 0;
  color: #1f2937;
  font-size: 1.125rem;
}

.compatibility-score {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: bold;
}

.rec-card__reason {
  color: #6b7280;
  font-size: 0.875rem;
  margin-bottom: 1rem;
  line-height: 1.5;
}

.btn--small {
  padding: 0.5rem 1rem;
  font-size: 0.75rem;
}

.btn--primary {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  color: white;
}

.btn--primary:hover {
  background: linear-gradient(135deg, #2563eb, #1d4ed8);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

/* 快速操作 */
.quick-actions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.quick-action {
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 16px;
  padding: 2rem;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.quick-action:hover {
  border-color: #3b82f6;
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.action__icon {
  font-size: 2.5rem;
  width: 4rem;
  height: 4rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  border-radius: 16px;
  color: white;
  flex-shrink: 0;
}

.action__content {
  flex: 1;
}

.action__title {
  font-size: 1.25rem;
  font-weight: bold;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.action__description {
  color: #6b7280;
  font-size: 0.875rem;
  line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .dashboard__content {
    padding: 1.5rem;
  }
  
  .theme-info {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .recommendation-grid {
    grid-template-columns: 1fr;
  }
  
  .quick-actions {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .dashboard__header {
    padding: 1rem;
  }
  
  .header__content {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .header__stats {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .dashboard__content {
    padding: 1rem;
  }
  
  .nav__tabs {
    padding: 0 1rem;
  }
  
  .current-theme,
  .recommendations {
    padding: 1.5rem;
  }
  
  .quick-action {
    padding: 1.5rem;
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }
  
  .action__icon {
    width: 3rem;
    height: 3rem;
    font-size: 2rem;
  }
}

@media (max-width: 480px) {
  .dashboard__header {
    padding: 0.75rem;
  }
  
  .header__stats {
    grid-template-columns: 1fr;
  }
  
  .dashboard__nav {
    padding: 0 0.75rem;
  }
  
  .dashboard__content {
    padding: 0.75rem;
  }
  
  .stat-card {
    padding: 1rem;
  }
  
  .stat-card__icon {
    width: 2.5rem;
    height: 2.5rem;
    font-size: 1.5rem;
  }
  
  .stat-card__value {
    font-size: 1.25rem;
  }
}

/* 滚动条样式 */
.dashboard__content::-webkit-scrollbar {
  width: 8px;
}

.dashboard__content::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

.dashboard__content::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

.dashboard__content::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* 动画效果 */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.overview__section {
  animation: fadeIn 0.6s ease forwards;
}

.overview__section:nth-child(1) { animation-delay: 0.1s; }
.overview__section:nth-child(2) { animation-delay: 0.2s; }
.overview__section:nth-child(3) { animation-delay: 0.3s; } 