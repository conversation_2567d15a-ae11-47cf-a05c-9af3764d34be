/* 响应式网格系统 */
.responsive-grid {
  width: 100%;
  box-sizing: border-box;
}

.responsive-grid > * {
  min-width: 0; /* 防止内容溢出 */
}

/* 响应式容器 */
.responsive-container {
  box-sizing: border-box;
}

/* 响应式弹性布局 */
.responsive-flex {
  box-sizing: border-box;
}

.responsive-flex > * {
  min-width: 0; /* 防止 flex 项目溢出 */
}

/* 显示控制 */
.responsive-show {
  transition: opacity 0.3s ease;
}

/* 游戏画布响应式容器 */
.responsive-game-canvas {
  position: relative;
  overflow: hidden;
  border-radius: 12px;
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  transition: all 0.3s ease;
}

.responsive-game-canvas:hover {
  box-shadow: 
    0 12px 40px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.responsive-game-canvas canvas {
  width: 100% !important;
  height: 100% !important;
  display: block !important;
  border-radius: inherit;
}

/* 响应式侧边栏 */
.responsive-sidebar {
  background: rgba(255, 255, 255, 0.97);
  border-radius: 20px;
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-sizing: border-box;
}

.sidebar-toggle {
  background: rgba(255, 255, 255, 0.9) !important;
  border: 1px solid rgba(0, 0, 0, 0.1) !important;
  border-radius: 8px !important;
  padding: 8px 12px !important;
  cursor: pointer !important;
  font-size: 14px !important;
  transition: all 0.2s ease !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
}

.sidebar-toggle:hover {
  background: rgba(255, 255, 255, 1) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  transform: translateY(-1px) !important;
}

.sidebar-toggle:active {
  transform: translateY(0) !important;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1) !important;
}

.sidebar-content {
  transition: opacity 0.3s ease, padding 0.3s ease;
}

.sidebar-overlay {
  backdrop-filter: blur(8px);
  transition: opacity 0.3s ease;
}

/* 移动端特殊样式 */
@media (max-width: 375px) {
  .responsive-container {
    padding: 0 12px !important;
  }
  
  .responsive-grid {
    gap: 12px !important;
  }
  
  .responsive-game-canvas {
    border-radius: 8px;
    margin: 0 !important;
    width: 100% !important;
  }
  
  .responsive-sidebar {
    border-radius: 0;
    width: 100% !important;
    height: 100vh !important;
  }
  
  .sidebar-toggle {
    top: 15px !important;
    right: 15px !important;
  }
}

/* 平板端样式 */
@media (min-width: 376px) and (max-width: 768px) {
  .responsive-container {
    padding: 0 20px !important;
  }
  
  .responsive-grid {
    gap: 16px !important;
  }
  
  .responsive-game-canvas {
    max-width: 90%;
    margin: 0 auto !important;
  }
  
  .responsive-sidebar {
    border-radius: 16px;
  }
}

/* 桌面端样式 */
@media (min-width: 769px) {
  .responsive-container {
    padding: 0 32px !important;
  }
  
  .responsive-grid {
    gap: 24px !important;
  }
  
  .responsive-game-canvas {
    max-width: none;
  }
  
  .responsive-sidebar {
    border-radius: 20px;
  }
}

/* 大屏幕样式 */
@media (min-width: 1200px) {
  .responsive-container {
    padding: 0 40px !important;
  }
  
  .responsive-grid {
    gap: 32px !important;
  }
}

/* 超大屏幕样式 */
@media (min-width: 1400px) {
  .responsive-container {
    padding: 0 48px !important;
  }
}

/* 横屏模式特殊处理 */
@media (orientation: landscape) and (max-height: 600px) {
  .responsive-game-canvas {
    max-height: 70vh !important;
  }
  
  .responsive-sidebar {
    max-height: 80vh;
    overflow-y: auto;
  }
}

/* 竖屏模式特殊处理 */
@media (orientation: portrait) and (max-width: 768px) {
  .responsive-game-canvas {
    max-height: 50vh !important;
  }
  
  .responsive-sidebar {
    height: auto !important;
    max-height: 90vh;
    overflow-y: auto;
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .sidebar-toggle {
    padding: 12px 16px !important;
    font-size: 16px !important;
    min-height: 44px !important;
    min-width: 44px !important;
  }
  
  .responsive-grid {
    gap: 20px !important;
  }
  
  .responsive-flex {
    gap: 16px !important;
  }
}

/* 高分辨率屏幕优化 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .responsive-game-canvas {
    box-shadow: 
      0 8px 32px rgba(0, 0, 0, 0.12),
      inset 0 0.5px 0 rgba(255, 255, 255, 0.3);
  }
  
  .responsive-sidebar {
    box-shadow: 
      0 8px 32px rgba(0, 0, 0, 0.12),
      inset 0 0.5px 0 rgba(255, 255, 255, 0.9);
  }
}

/* 暗色模式支持 */
@media (prefers-color-scheme: dark) {
  .responsive-game-canvas {
    background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
    box-shadow: 
      0 8px 32px rgba(0, 0, 0, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }
  
  .responsive-sidebar {
    background: rgba(45, 55, 72, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 
      0 8px 32px rgba(0, 0, 0, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }
  
  .sidebar-toggle {
    background: rgba(45, 55, 72, 0.9) !important;
    color: white !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
  }
  
  .sidebar-toggle:hover {
    background: rgba(45, 55, 72, 1) !important;
  }
}

/* 减少动画偏好 */
@media (prefers-reduced-motion: reduce) {
  .responsive-game-canvas,
  .responsive-sidebar,
  .sidebar-toggle,
  .sidebar-content,
  .sidebar-overlay,
  .responsive-show {
    transition: none !important;
    animation: none !important;
  }
}

/* 打印样式 */
@media print {
  .responsive-sidebar {
    display: none !important;
  }
  
  .sidebar-toggle {
    display: none !important;
  }
  
  .responsive-game-canvas {
    box-shadow: none !important;
    border: 1px solid #ccc !important;
  }
}

/* 网格系统辅助类 */
.grid-auto-fit {
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)) !important;
}

.grid-auto-fill {
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)) !important;
}

/* 弹性布局辅助类 */
.flex-center {
  align-items: center !important;
  justify-content: center !important;
}

.flex-between {
  justify-content: space-between !important;
}

.flex-around {
  justify-content: space-around !important;
}

.flex-evenly {
  justify-content: space-evenly !important;
}

/* 间距辅助类 */
.gap-xs { gap: 8px !important; }
.gap-sm { gap: 12px !important; }
.gap-md { gap: 16px !important; }
.gap-lg { gap: 24px !important; }
.gap-xl { gap: 32px !important; }

/* 响应式间距 */
@media (max-width: 375px) {
  .gap-xs { gap: 6px !important; }
  .gap-sm { gap: 8px !important; }
  .gap-md { gap: 12px !important; }
  .gap-lg { gap: 16px !important; }
  .gap-xl { gap: 20px !important; }
}

@media (min-width: 1200px) {
  .gap-xs { gap: 10px !important; }
  .gap-sm { gap: 16px !important; }
  .gap-md { gap: 24px !important; }
  .gap-lg { gap: 32px !important; }
  .gap-xl { gap: 48px !important; }
}

/* 可见性辅助类 */
.show-mobile-only { display: none !important; }
.show-tablet-only { display: none !important; }
.show-desktop-only { display: block !important; }

@media (max-width: 375px) {
  .show-mobile-only { display: block !important; }
  .show-tablet-only { display: none !important; }
  .show-desktop-only { display: none !important; }
  .hide-mobile { display: none !important; }
}

@media (min-width: 376px) and (max-width: 768px) {
  .show-mobile-only { display: none !important; }
  .show-tablet-only { display: block !important; }
  .show-desktop-only { display: none !important; }
  .hide-tablet { display: none !important; }
}

@media (min-width: 769px) {
  .show-mobile-only { display: none !important; }
  .show-tablet-only { display: none !important; }
  .show-desktop-only { display: block !important; }
  .hide-desktop { display: none !important; }
} 