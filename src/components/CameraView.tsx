import React, { useEffect, useState, useCallback } from 'react'
import { useCamera } from '../hooks/useCamera'
import { usePoseDetection } from '../hooks/usePoseDetection'
import { PoseOverlay } from './PoseOverlay'
import { PoseResults, PostureAnalysis } from '../types/pose'
import './CameraView.css'

interface CameraViewProps {
  width?: number
  height?: number
  className?: string
  onStatusChange?: (status: string) => void
  onPoseDetected?: (results: PoseResults, analysis: PostureAnalysis) => void
  showControls?: boolean
  enablePoseDetection?: boolean
  showPoseOverlay?: boolean
}

export const CameraView: React.FC<CameraViewProps> = ({
  width = 640,
  height = 480,
  className = '',
  onStatusChange,
  onPoseDetected,
  showControls = true,
  enablePoseDetection = true,
  showPoseOverlay = true
}) => {
  const {
    status,
    stream,
    error,
    isSupported,
    deviceId,
    videoRef,
    requestCamera,
    stopCamera,
    getDevices,
    switchDevice
  } = useCamera()

  const [devices, setDevices] = useState<MediaDeviceInfo[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [currentPoseData, setCurrentPoseData] = useState<{
    landmarks?: any[]
    analysis?: PostureAnalysis | null
  }>({})

  // 姿态检测回调
  const handlePoseDetected = useCallback((results: PoseResults, analysis: PostureAnalysis) => {
    setCurrentPoseData({
      landmarks: results.poseLandmarks,
      analysis
    })
    onPoseDetected?.(results, analysis)
  }, [onPoseDetected])

  // 姿态检测Hook
  const {
    isActive: isPoseActive,
    isLoading: isPoseLoading,
    error: poseError,
    startDetection,
    stopDetection,
    isGoodPosture,
    postureScore,
    sessionDuration,
    goodPosturePercentage
  } = usePoseDetection({
    videoElement: videoRef.current,
    onPoseDetected: handlePoseDetected,
    autoStart: false
  })

  // 通知父组件状态变化
  useEffect(() => {
    if (onStatusChange) {
      const combinedStatus = status === 'granted' && isPoseActive ? 'pose-active' : status
      onStatusChange(combinedStatus)
    }
  }, [status, isPoseActive, onStatusChange])

  // 获取可用设备列表
  const loadDevices = async () => {
    const deviceList = await getDevices()
    setDevices(deviceList)
  }

  // 处理摄像头启动
  const handleStartCamera = async () => {
    setIsLoading(true)
    await requestCamera()
    await loadDevices()
    setIsLoading(false)

    // 自动启动姿态检测
    if (enablePoseDetection && videoRef.current) {
      setTimeout(() => {
        startDetection()
      }, 1000) // 等待视频流稳定
    }
  }

  // 处理摄像头停止
  const handleStopCamera = () => {
    if (isPoseActive) {
      stopDetection()
    }
    stopCamera()
    setDevices([])
    setCurrentPoseData({})
  }

  // 处理设备切换
  const handleDeviceSwitch = async (deviceId: string) => {
    setIsLoading(true)
    const wasDetecting = isPoseActive
    
    if (wasDetecting) {
      stopDetection()
    }
    
    await switchDevice(deviceId)
    setIsLoading(false)

    if (wasDetecting && enablePoseDetection) {
      setTimeout(() => {
        startDetection()
      }, 1000)
    }
  }

  // 切换姿态检测
  const handleTogglePoseDetection = () => {
    if (isPoseActive) {
      stopDetection()
    } else {
      startDetection()
    }
  }

  // 渲染状态指示器
  const renderStatusIndicator = () => {
    const statusConfig = {
      idle: { color: '#6B7280', text: '未启动', icon: '⚪' },
      requesting: { color: '#F59E0B', text: '请求中...', icon: '🟡' },
      granted: { color: '#10B981', text: '已连接', icon: '🟢' },
      denied: { color: '#EF4444', text: '权限被拒绝', icon: '🔴' },
      unavailable: { color: '#EF4444', text: '不可用', icon: '❌' },
      error: { color: '#EF4444', text: '错误', icon: '⚠️' }
    }

    const config = statusConfig[status] || statusConfig.idle

    return (
      <div className="camera-status" style={{ color: config.color }}>
        <span className="status-icon">{config.icon}</span>
        <span className="status-text">{config.text}</span>
        {status === 'granted' && (
          <div className="pose-status">
            <span 
              className={`pose-indicator ${isPoseActive ? 'active' : 'inactive'}`}
              title={isPoseActive ? '姿态检测已启用' : '姿态检测已停用'}
            >
              {isPoseActive ? '🤸' : '🚶'}
            </span>
          </div>
        )}
      </div>
    )
  }

  // 渲染姿态统计信息
  const renderPoseStats = () => {
    if (!isPoseActive || !currentPoseData.analysis) return null

    return (
      <div className="pose-stats">
        <div className="stat-row">
          <span className="stat-label">专注度:</span>
          <span className={`stat-value ${isGoodPosture ? 'good' : 'poor'}`}>
            {Math.round(postureScore)}%
          </span>
        </div>
        <div className="stat-row">
          <span className="stat-label">良好姿态率:</span>
          <span className="stat-value">
            {Math.round(goodPosturePercentage)}%
          </span>
        </div>
        <div className="stat-row">
          <span className="stat-label">检测时长:</span>
          <span className="stat-value">
            {Math.floor(sessionDuration / 1000)}秒
          </span>
        </div>
      </div>
    )
  }

  // 渲染错误信息
  const renderError = () => {
    const errors = [error, poseError].filter(Boolean)
    if (errors.length === 0) return null

    return (
      <div className="camera-error">
        {errors.map((err, index) => (
          <div key={index} className="error-item">
            <div className="error-icon">⚠️</div>
            <div className="error-content">
              <h4>{err === error ? '摄像头访问失败' : '姿态检测失败'}</h4>
              <p>{err?.message}</p>
              {err === error && error?.code === 'PERMISSION_DENIED' && (
                <div className="error-help">
                  <p>解决方法：</p>
                  <ul>
                    <li>点击地址栏的摄像头图标</li>
                    <li>选择"始终允许"</li>
                    <li>刷新页面重试</li>
                  </ul>
                </div>
              )}
              {err === error && error?.code === 'NO_CAMERA' && (
                <div className="error-help">
                  <p>请确保：</p>
                  <ul>
                    <li>设备已连接摄像头</li>
                    <li>摄像头驱动程序正常</li>
                    <li>没有其他应用占用摄像头</li>
                  </ul>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>
    )
  }

  // 渲染控制面板
  const renderControls = () => {
    if (!showControls) return null

    return (
      <div className="camera-controls">
        <div className="control-row">
          {status === 'idle' || status === 'denied' || status === 'error' ? (
            <button
              className="control-btn primary"
              onClick={handleStartCamera}
              disabled={!isSupported || isLoading}
            >
              {isLoading ? '启动中...' : '🎥 启动摄像头'}
            </button>
          ) : status === 'granted' ? (
            <>
              <button
                className="control-btn secondary"
                onClick={handleStopCamera}
              >
                ⏹️ 停止摄像头
              </button>
              
              {enablePoseDetection && (
                <button
                  className={`control-btn ${isPoseActive ? 'primary' : 'secondary'}`}
                  onClick={handleTogglePoseDetection}
                  disabled={isPoseLoading}
                >
                  {isPoseLoading ? '加载中...' : 
                   isPoseActive ? '🤸 停止检测' : '🚶 开始检测'}
                </button>
              )}
            </>
          ) : null}

          {!isSupported && (
            <div className="unsupported-warning">
              ⚠️ 您的浏览器不支持摄像头功能
            </div>
          )}
        </div>

        {devices.length > 1 && status === 'granted' && (
          <div className="control-row">
            <label htmlFor="device-select">选择摄像头设备：</label>
            <select
              id="device-select"
              value={deviceId || ''}
              onChange={(e) => handleDeviceSwitch(e.target.value)}
              disabled={isLoading}
            >
              {devices.map((device, index) => (
                <option key={device.deviceId} value={device.deviceId}>
                  {device.label || `摄像头 ${index + 1}`}
                </option>
              ))}
            </select>
          </div>
        )}

        {renderStatusIndicator()}
        {renderPoseStats()}
      </div>
    )
  }

  return (
    <div className={`camera-view ${className}`}>
      <div className="camera-container">
        <div 
          className="video-wrapper"
          style={{ width, height }}
        >
          {status === 'granted' && stream ? (
            <>
              <video
                ref={videoRef}
                width={width}
                height={height}
                autoPlay
                muted
                playsInline
                className="camera-video"
              />
              
              {showPoseOverlay && isPoseActive && (
                <PoseOverlay
                  landmarks={currentPoseData.landmarks}
                  postureAnalysis={currentPoseData.analysis}
                  width={width}
                  className="video-pose-overlay"
                />
              )}
            </>
          ) : (
            <div className="video-placeholder">
              <div className="placeholder-content">
                {status === 'requesting' ? (
                  <>
                    <div className="loading-spinner"></div>
                    <p>正在启动摄像头...</p>
                  </>
                ) : status === 'denied' || status === 'error' ? (
                  <>
                    <div className="placeholder-icon">📷</div>
                    <p>摄像头不可用</p>
                  </>
                ) : (
                  <>
                    <div className="placeholder-icon">📷</div>
                    <p>点击启动摄像头</p>
                    {enablePoseDetection && (
                      <p className="pose-hint">🤸 将自动启用姿态检测</p>
                    )}
                  </>
                )}
              </div>
            </div>
          )}

          {(isLoading || isPoseLoading) && status === 'granted' && (
            <div className="video-overlay">
              <div className="loading-spinner"></div>
              <p>{isLoading ? '切换中...' : '加载姿态检测...'}</p>
            </div>
          )}
        </div>

        {renderError()}
        {renderControls()}
      </div>
    </div>
  )
} 