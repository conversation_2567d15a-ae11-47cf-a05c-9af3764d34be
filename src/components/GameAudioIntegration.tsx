import React, { useEffect } from 'react'
import { useAudioManager, useGameAudioEvents, useAudioInitialization } from '../hooks/useAudioManager'
import { AudioStatusIndicator } from './AudioStatusIndicator'

interface GameAudioIntegrationProps {
  children?: React.ReactNode
}

/**
 * 游戏音频集成组件
 * 负责将音频系统集成到游戏的各个事件中
 */
export const GameAudioIntegration: React.FC<GameAudioIntegrationProps> = ({ children }) => {
  const { playMusic, audioStatus, audioSettings } = useAudioManager()
  
  // 自动初始化音频系统
  const { isInitialized } = useAudioInitialization(true)

  // 开始播放背景音乐
  useEffect(() => {
    if (isInitialized && audioSettings.musicEnabled) {
      // 根据游戏时间或状态选择合适的背景音乐
      const shouldPlayMusic = !audioStatus.isPlaying || !audioStatus.currentMusic
      
      if (shouldPlayMusic) {
        // 默认播放农场环境音乐
        playMusic('farm_ambient', true).then(success => {
          if (success) {
            console.log('开始播放背景音乐: farm_ambient')
          }
        })
      }
    }
  }, [isInitialized, audioSettings.musicEnabled, audioStatus.isPlaying, audioStatus.currentMusic, playMusic])

  return (
    <>
      {children}
      {/* 音频状态指示器（可选，用于调试） */}
      {typeof window !== 'undefined' && window.location.hostname === 'localhost' && (
        <AudioStatusIndicator 
          status={audioStatus} 
          settings={audioSettings}
        />
      )}
    </>
  )
}

/**
 * 音频状态指示器（开发模式）
 */
interface AudioStatusIndicatorProps {
  status: {
    isInitialized: boolean
    isPlaying: boolean
    currentMusic?: string
    audioContextState?: string
    loadedAudios: number
  }
  settings: {
    masterVolume: number
    musicVolume: number
    effectsVolume: number
    musicEnabled: boolean
    effectsEnabled: boolean
  }
}

const AudioStatusIndicator: React.FC<AudioStatusIndicatorProps> = ({ status, settings }) => {
  return (
    <div 
      style={{
        position: 'fixed',
        top: '10px',
        right: '10px',
        background: 'rgba(0,0,0,0.8)',
        color: 'white',
        padding: '8px',
        borderRadius: '4px',
        fontSize: '12px',
        fontFamily: 'monospace',
        zIndex: 9999,
        minWidth: '200px'
      }}
    >
      <div><strong>音频系统状态</strong></div>
      <div>初始化: {status.isInitialized ? '✅' : '❌'}</div>
      <div>播放中: {status.isPlaying ? '✅' : '❌'}</div>
      <div>当前音乐: {status.currentMusic || '无'}</div>
      <div>音频上下文: {status.audioContextState || '未知'}</div>
      <div>已加载: {status.loadedAudios} 个音频</div>
      <hr style={{ margin: '4px 0', border: '1px solid #444' }} />
      <div>主音量: {Math.round(settings.masterVolume * 100)}%</div>
      <div>音乐: {settings.musicEnabled ? '启用' : '禁用'} ({Math.round(settings.musicVolume * 100)}%)</div>
      <div>音效: {settings.effectsEnabled ? '启用' : '禁用'} ({Math.round(settings.effectsVolume * 100)}%)</div>
    </div>
  )
}

export default GameAudioIntegration 