/* FeedbackAnalysis.css */
.feedback-analysis {
  background: #f8fafc;
  border-radius: 12px;
  padding: 24px;
  max-height: 90vh;
  overflow-y: auto;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', sans-serif;
}

.feedback-analysis.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 300px;
}

.loading-spinner {
  font-size: 18px;
  color: #6b7280;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* 分析头部 */
.analysis-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  flex-wrap: wrap;
  gap: 16px;
}

.analysis-header h2 {
  margin: 0;
  font-size: 28px;
  font-weight: 700;
  color: #1f2937;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.analysis-controls {
  display: flex;
  gap: 12px;
  align-items: center;
}

.time-range-select,
.filter-select {
  padding: 8px 16px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 14px;
  background: white;
  color: #374151;
  cursor: pointer;
  transition: all 0.2s ease;
}

.time-range-select:hover,
.filter-select:hover {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.time-range-select:focus,
.filter-select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
}

/* 概览统计 */
.overview-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
}

.stat-card {
  background: white;
  padding: 24px;
  border-radius: 12px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border: 1px solid #f1f5f9;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

.stat-number {
  font-size: 36px;
  font-weight: 800;
  margin-bottom: 8px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stat-label {
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* 图表网格 */
.charts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.chart-container,
.severity-analysis,
.time-trends,
.keyword-cloud {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border: 1px solid #f1f5f9;
}

.chart-container h3,
.severity-analysis h3,
.time-trends h3,
.keyword-cloud h3 {
  margin: 0 0 20px 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

/* 饼图样式 */
.pie-chart {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.chart-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.chart-bar {
  flex: 1;
  height: 24px;
  background: #f1f5f9;
  border-radius: 12px;
  overflow: hidden;
  position: relative;
}

.chart-fill {
  height: 100%;
  border-radius: 12px;
  transition: all 0.6s ease;
  position: relative;
}

.chart-fill.chart-usability {
  background: linear-gradient(90deg, #667eea, #764ba2);
}

.chart-fill.chart-bug {
  background: linear-gradient(90deg, #ff4757, #ff3742);
}

.chart-fill.chart-feature {
  background: linear-gradient(90deg, #2ed573, #1dd65f);
}

.chart-fill.chart-performance {
  background: linear-gradient(90deg, #ffa502, #ff9500);
}

.chart-fill.chart-other {
  background: linear-gradient(90deg, #a4b0be, #747d8c);
}

.chart-label {
  min-width: 120px;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.type-name {
  font-weight: 600;
  color: #374151;
  font-size: 14px;
}

.type-count {
  font-size: 12px;
  color: #6b7280;
}

/* 严重程度分析 */
.severity-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 16px;
}

.severity-card {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: #f8fafc;
  border-radius: 8px;
  border-left: 4px solid transparent;
  transition: all 0.3s ease;
}

.severity-card:hover {
  background: #f1f5f9;
  transform: translateX(4px);
}

.severity-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  flex-shrink: 0;
}

.severity-info {
  flex: 1;
}

.severity-label {
  font-weight: 600;
  color: #374151;
  font-size: 14px;
  margin-bottom: 4px;
}

.severity-count {
  font-size: 20px;
  font-weight: 700;
  color: #1f2937;
}

/* 时间趋势 */
.trend-chart {
  display: flex;
  align-items: end;
  gap: 4px;
  height: 200px;
  padding: 20px 0;
  overflow-x: auto;
}

.trend-bar {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 40px;
  height: 100%;
  position: relative;
}

.trend-fill {
  width: 24px;
  border-radius: 4px 4px 0 0;
  transition: all 0.4s ease;
  cursor: pointer;
  position: relative;
}

.trend-fill:hover {
  transform: scaleY(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.trend-label {
  font-size: 10px;
  color: #6b7280;
  margin-top: 8px;
  writing-mode: horizontal-tb;
  text-align: center;
}

/* 关键词云 */
.keywords {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.no-keywords {
  text-align: center;
  padding: 40px 20px;
  color: #6b7280;
}

.no-keywords p {
  font-size: 14px;
  margin: 0;
}

.keyword {
  padding: 6px 12px;
  border-radius: 20px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.keyword.keyword-positive {
  background: #d1fae5;
  color: #059669;
  border: 1px solid #a7f3d0;
}

.keyword.keyword-negative {
  background: #fee2e2;
  color: #dc2626;
  border: 1px solid #fecaca;
}

.keyword.keyword-neutral {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.keyword:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 优先级建议 */
.priority-recommendations {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border: 1px solid #f1f5f9;
  margin-bottom: 32px;
}

.priority-recommendations h3 {
  margin: 0 0 24px 0;
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
}

.recommendations-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.recommendation-card {
  background: #f8fafc;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
  transition: all 0.3s ease;
}

.recommendation-card:hover {
  border-color: #667eea;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.1);
}

.recommendation-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.priority-badge {
  color: white;
  font-size: 10px;
  font-weight: 700;
  padding: 4px 8px;
  border-radius: 4px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.recommendation-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  flex: 1;
}

.recommendation-content p {
  margin: 8px 0;
  font-size: 14px;
  color: #4b5563;
  line-height: 1.5;
}

.recommendation-content strong {
  color: #374151;
  font-weight: 600;
}

/* 详细分析 */
.detailed-analysis {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border: 1px solid #f1f5f9;
}

.detailed-analysis h3 {
  margin: 0 0 24px 0;
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
}

.detailed-analysis h4 {
  margin: 24px 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #374151;
}

/* 主要问题 */
.top-issues {
  margin-bottom: 32px;
}

.issue-item {
  background: #f8fafc;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  transition: all 0.3s ease;
}

.issue-item:hover {
  border-color: #667eea;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.issue-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 8px;
  flex-wrap: wrap;
}

.issue-category {
  background: #667eea;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.issue-count {
  background: #e5e7eb;
  color: #374151;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
}

.issue-rating {
  background: #fef3c7;
  color: #d97706;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
}

.issue-keywords {
  font-size: 14px;
  color: #6b7280;
  font-style: italic;
}

/* 小时分布 */
.hourly-distribution {
  margin-top: 32px;
}

.hourly-chart {
  display: flex;
  align-items: end;
  gap: 4px;
  height: 120px;
  overflow-x: auto;
  padding: 20px 0;
}

.hourly-bar {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 32px;
  height: 100%;
  position: relative;
}

.hourly-count {
  background: #667eea;
  color: white;
  font-size: 10px;
  font-weight: 600;
  padding: 2px 4px;
  border-radius: 4px 4px 0 0;
  writing-mode: vertical-rl;
  text-orientation: mixed;
  min-height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.hourly-time {
  font-size: 10px;
  color: #6b7280;
  margin-top: 4px;
}

/* 无数据状态 */
.no-data {
  text-align: center;
  padding: 60px 20px;
  color: #6b7280;
}

.no-data p {
  font-size: 16px;
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .feedback-analysis {
    padding: 16px;
  }

  .analysis-header {
    flex-direction: column;
    align-items: stretch;
  }

  .analysis-header h2 {
    font-size: 24px;
    text-align: center;
  }

  .analysis-controls {
    justify-content: center;
  }

  .overview-stats {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }

  .charts-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .stat-number {
    font-size: 28px;
  }

  .trend-chart {
    height: 150px;
  }

  .keywords {
    padding: 15px;
  }

  .keyword {
    font-size: 12px;
    padding: 4px 8px;
  }

  .recommendation-header {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .issue-header {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .hourly-chart {
    height: 100px;
  }
}

@media (max-width: 480px) {
  .overview-stats {
    grid-template-columns: 1fr;
  }

  .analysis-controls {
    flex-direction: column;
    gap: 8px;
  }

  .time-range-select,
  .filter-select {
    width: 100%;
  }

  .severity-grid {
    grid-template-columns: 1fr;
  }

  .trend-chart,
  .hourly-chart {
    gap: 2px;
  }

  .trend-bar,
  .hourly-bar {
    min-width: 24px;
  }
}

/* 动画效果 */
.chart-fill,
.trend-fill,
.hourly-count {
  animation: slideIn 0.8s ease-out forwards;
}

@keyframes slideIn {
  0% {
    transform: scaleY(0);
    opacity: 0;
  }
  100% {
    transform: scaleY(1);
    opacity: 1;
  }
}

.stat-card,
.recommendation-card,
.issue-item {
  animation: fadeInUp 0.6s ease-out forwards;
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.keyword {
  animation: fadeIn 0.5s ease-out forwards;
}

@keyframes fadeIn {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* 打印样式 */
@media print {
  .feedback-analysis {
    background: white;
    box-shadow: none;
    max-height: none;
    overflow: visible;
  }

  .analysis-controls {
    display: none;
  }

  .charts-grid {
    grid-template-columns: 1fr 1fr;
  }

  .keyword:hover,
  .stat-card:hover,
  .recommendation-card:hover {
    transform: none;
    box-shadow: none;
  }
} 