.pose-overlay {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.pose-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 2;
}

.pose-info {
  position: absolute;
  top: 10px;
  left: 10px;
  z-index: 3;
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-width: 250px;
}

.focus-score {
  padding: 8px 12px;
  border-radius: 8px;
  font-weight: bold;
  font-size: 14px;
  text-align: center;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.focus-score.good {
  background: rgba(16, 185, 129, 0.9);
  color: white;
  animation: focusPulseGood 2s infinite;
}

.focus-score.poor {
  background: rgba(239, 68, 68, 0.9);
  color: white;
  animation: focusPulsePoor 2s infinite;
}

@keyframes focusPulseGood {
  0%, 100% {
    box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 4px 16px rgba(16, 185, 129, 0.5);
    transform: scale(1.02);
  }
}

@keyframes focusPulsePoor {
  0%, 100% {
    box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 4px 16px rgba(239, 68, 68, 0.5);
    transform: scale(1.02);
  }
}

.warnings {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.warning-item {
  padding: 6px 10px;
  background: rgba(245, 158, 11, 0.95);
  color: white;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  animation: warningSlideIn 0.3s ease-out;
}

@keyframes warningSlideIn {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .pose-info {
    max-width: 200px;
  }
  
  .focus-score {
    font-size: 12px;
    padding: 6px 10px;
  }
  
  .warning-item {
    font-size: 11px;
    padding: 5px 8px;
  }
}

/* 调试模式样式 */
.pose-overlay.debug {
  border: 2px dashed #3B82F6;
}

.pose-overlay.debug .pose-canvas {
  border: 1px solid rgba(59, 130, 246, 0.5);
}

/* 隐藏模式 */
.pose-overlay.hidden {
  display: none;
}

/* 骨架质量指示器 */
.pose-overlay::after {
  content: '';
  position: absolute;
  top: 10px;
  right: 50px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: var(--skeleton-quality-color, #6B7280);
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  z-index: 3;
}

/* 骨架质量状态 */
.pose-overlay[data-quality="excellent"]::after {
  --skeleton-quality-color: #10B981;
  animation: qualityPulse 2s infinite;
}

.pose-overlay[data-quality="good"]::after {
  --skeleton-quality-color: #3B82F6;
}

.pose-overlay[data-quality="fair"]::after {
  --skeleton-quality-color: #F59E0B;
}

.pose-overlay[data-quality="poor"]::after {
  --skeleton-quality-color: #EF4444;
  animation: qualityPulse 1s infinite;
}

@keyframes qualityPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.8;
  }
}

/* 加载状态 */
.pose-overlay.loading .pose-canvas {
  opacity: 0.5;
}

.pose-overlay.loading::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 30px;
  height: 30px;
  margin: -15px 0 0 -15px;
  border: 3px solid rgba(59, 130, 246, 0.3);
  border-top: 3px solid #3B82F6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  z-index: 4;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
} 