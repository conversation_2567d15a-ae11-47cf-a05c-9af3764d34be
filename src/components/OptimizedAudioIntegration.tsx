import React, { useEffect, useState } from 'react'
import { OptimizedAudioManager } from '../audio/optimizedAudioManager'

interface OptimizedAudioIntegrationProps {
  enablePerformanceMonitoring?: boolean
  enableCaching?: boolean
  autoPlayBackgroundMusic?: boolean
  showDebugInfo?: boolean
}

const OptimizedAudioIntegration: React.FC<OptimizedAudioIntegrationProps> = ({
  enablePerformanceMonitoring = true,
  enableCaching = true,
  autoPlayBackgroundMusic = true,
  showDebugInfo = false
}) => {
  const [audioManager, setAudioManager] = useState<OptimizedAudioManager | null>(null)
  const [isInitialized, setIsInitialized] = useState(false)
  const [initError, setInitError] = useState<string | null>(null)
  const [debugInfo, setDebugInfo] = useState<any>(null)

  // 初始化优化音频管理器
  useEffect(() => {
    const initializeAudio = async () => {
      try {
        console.log('正在初始化优化音频系统...')
        
        const manager = new OptimizedAudioManager({
          enableCache: enableCaching,
          enablePerformanceMonitoring: enablePerformanceMonitoring,
          preloadEssentialAudios: true,
          cacheOptions: {
            maxSize: 50,
            maxCount: 20,
            autoCleanup: true
          }
        })
        
        const success = await manager.initializeOptimizations()
        
        if (success) {
          setAudioManager(manager)
          setIsInitialized(true)
          console.log('✅ 优化音频系统初始化成功')
          
          // 自动播放背景音乐
          if (autoPlayBackgroundMusic) {
            setTimeout(async () => {
              try {
                await manager.playMusic('farm_ambient', true)
                console.log('🎵 背景音乐开始播放')
              } catch (error) {
                console.warn('背景音乐播放失败:', error)
              }
            }, 1000)
          }
        } else {
          setInitError('音频系统初始化失败')
          console.error('❌ 优化音频系统初始化失败')
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '未知错误'
        setInitError(errorMessage)
        console.error('❌ 优化音频系统初始化异常:', error)
      }
    }

    // 检查浏览器支持
    if (typeof window !== 'undefined' && (window.AudioContext || (window as any).webkitAudioContext)) {
      initializeAudio()
    } else {
      setInitError('浏览器不支持Web Audio API')
    }
  }, [enableCaching, enablePerformanceMonitoring, autoPlayBackgroundMusic])

  // 定期更新调试信息
  useEffect(() => {
    if (!showDebugInfo || !audioManager || !isInitialized) return

    const interval = setInterval(() => {
      try {
        const report = audioManager.getPerformanceReport()
        const cacheStats = audioManager.getCacheStats()
        const optimizationStatus = audioManager.getOptimizationStatus()
        
        setDebugInfo({
          performance: report,
          cache: cacheStats,
          optimization: optimizationStatus,
          timestamp: new Date().toLocaleTimeString()
        })
      } catch (error) {
        console.warn('获取调试信息失败:', error)
      }
    }, 2000)

    return () => clearInterval(interval)
  }, [showDebugInfo, audioManager, isInitialized])

  // 清理
  useEffect(() => {
    return () => {
      if (audioManager) {
        audioManager.destroy()
      }
    }
  }, [audioManager])

  // 开发模式显示状态
  const isDevelopment = window.location.hostname === 'localhost' || 
                       window.location.hostname === '127.0.0.1'

  if (!isDevelopment && !showDebugInfo) {
    return null
  }

  return (
    <div className="fixed top-4 left-4 z-50">
      {/* 状态指示器 */}
      <div className="bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg p-3 max-w-sm">
        <div className="flex items-center space-x-2">
          <div className={`w-3 h-3 rounded-full ${
            isInitialized ? 'bg-green-500' : initError ? 'bg-red-500' : 'bg-yellow-500'
          }`} />
          <span className="text-sm font-medium text-gray-800 dark:text-white">
            优化音频系统
          </span>
        </div>
        
        <div className="mt-2 text-xs text-gray-600 dark:text-gray-400">
          {isInitialized ? (
            <div className="space-y-1">
              <div>✅ 系统已初始化</div>
              {enableCaching && <div>💾 缓存已启用</div>}
              {enablePerformanceMonitoring && <div>📊 性能监控已启用</div>}
            </div>
          ) : initError ? (
            <div className="text-red-600 dark:text-red-400">
              ❌ {initError}
            </div>
          ) : (
            <div>⏳ 正在初始化...</div>
          )}
        </div>

        {/* 调试信息 */}
        {showDebugInfo && debugInfo && (
          <div className="mt-3 text-xs">
            <div className="text-gray-500 dark:text-gray-400 mb-1">
              调试信息 ({debugInfo.timestamp})
            </div>
            
            {debugInfo.performance && (
              <div className="space-y-1">
                <div>延迟: {debugInfo.performance.metrics.averageLatency.toFixed(1)}ms</div>
                <div>成功率: {debugInfo.performance.metrics.successRate.toFixed(1)}%</div>
                <div>实例数: {debugInfo.performance.metrics.activeInstances}</div>
                <div>内存: {debugInfo.performance.metrics.memoryUsage.toFixed(1)}MB</div>
              </div>
            )}
            
            {debugInfo.cache && (
              <div className="mt-2 space-y-1">
                <div>缓存命中: {debugInfo.cache.hitRate.toFixed(1)}%</div>
                <div>缓存项: {debugInfo.cache.totalCached}</div>
                <div>缓存大小: {debugInfo.cache.memoryUsage.toFixed(1)}MB</div>
              </div>
            )}
            
            {debugInfo.performance?.warnings && debugInfo.performance.warnings.length > 0 && (
              <div className="mt-2">
                <div className="text-yellow-600 dark:text-yellow-400 font-medium">警告:</div>
                {debugInfo.performance.warnings.map((warning: string, index: number) => (
                  <div key={index} className="text-yellow-600 dark:text-yellow-400">
                    • {warning}
                  </div>
                ))}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  )
}

export default OptimizedAudioIntegration 