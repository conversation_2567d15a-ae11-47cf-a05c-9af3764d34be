import React, { useState, useEffect, useRef } from 'react'
import { FarmLevel, FarmUpgradeSystem, UpgradeCheckResult, FarmLevelConfig, UpgradeCondition } from '../systems/FarmUpgradeSystem'
import { FarmUnlockSystem, UnlockContent, UnlockType } from '../systems/FarmUnlockSystem'
import { FarmUpgradeAnimations } from '../systems/FarmUpgradeAnimations'
import { CropType } from '../types/crop'
import { AchievementService } from '../services/AchievementService'
import { GameProgressService } from '../services/GameProgressService'

interface FarmUpgradeInterfaceProps {
  isOpen: boolean
  onClose: () => void
  currentLevel?: FarmLevel
  onLevelUp?: (newLevel: FarmLevel, rewards: any[]) => void
  achievementService?: AchievementService
  gameProgressService?: GameProgressService
}

interface LevelProgress {
  current: number
  required: number
  percentage: number
  label: string
  isComplete: boolean
  estimatedTime?: string
}

/**
 * 农场升级界面组件
 * 展示当前等级、升级条件、解锁内容预览和升级进度
 */
export const FarmUpgradeInterface: React.FC<FarmUpgradeInterfaceProps> = ({
  isOpen,
  onClose,
  currentLevel = FarmLevel.NOVICE,
  onLevelUp,
  achievementService,
  gameProgressService
}) => {
  const [upgradeSystem, setUpgradeSystem] = useState<FarmUpgradeSystem | null>(null)
  const [unlockSystem, setUnlockSystem] = useState<FarmUnlockSystem | null>(null)
  const [animationSystem, setAnimationSystem] = useState<FarmUpgradeAnimations | null>(null)
  const [upgradeResult, setUpgradeResult] = useState<UpgradeCheckResult | null>(null)
  const [currentLevelInfo, setCurrentLevelInfo] = useState<FarmLevelConfig | null>(null)
  const [nextLevelInfo, setNextLevelInfo] = useState<FarmLevelConfig | null>(null)
  const [levelProgress, setLevelProgress] = useState<LevelProgress[]>([])
  const [isUpgrading, setIsUpgrading] = useState(false)
  const [showUpgradeConfirm, setShowUpgradeConfirm] = useState(false)
  const [selectedTab, setSelectedTab] = useState<'progress' | 'unlocks' | 'rewards'>('progress')
  
  const animationRef = useRef<HTMLDivElement>(null)

  // 初始化系统
  useEffect(() => {
    if (achievementService && gameProgressService) {
      const upgradeSystem = new FarmUpgradeSystem(achievementService, gameProgressService)
      const unlockSystem = new FarmUnlockSystem()
      const animationSystem = new FarmUpgradeAnimations()
      
      setUpgradeSystem(upgradeSystem)
      setUnlockSystem(unlockSystem)
      setAnimationSystem(animationSystem)
    }
  }, [achievementService, gameProgressService])

  // 加载升级信息
  useEffect(() => {
    if (upgradeSystem && isOpen) {
      loadUpgradeInfo()
    }
  }, [upgradeSystem, isOpen, currentLevel])

  // 加载升级信息
  const loadUpgradeInfo = async () => {
    if (!upgradeSystem) return

    try {
      const result = await upgradeSystem.checkUpgradeEligibility()
      const currentInfo = upgradeSystem.getCurrentLevelInfo()
      const nextInfo = upgradeSystem.getNextLevelInfo()
      
      setUpgradeResult(result)
      setCurrentLevelInfo(currentInfo || null)
      setNextLevelInfo(nextInfo || null)
      
      // 生成进度数据
      if (nextInfo) {
        const progress = generateLevelProgress(result, nextInfo)
        setLevelProgress(progress)
      }
    } catch (error) {
      console.error('Failed to load upgrade info:', error)
    }
  }

  // 生成等级进度数据
  const generateLevelProgress = (result: UpgradeCheckResult, nextLevel: FarmLevelConfig): LevelProgress[] => {
    const progress: LevelProgress[] = []
    
    // 处理必需条件
    nextLevel.requiredConditions.forEach(condition => {
      const isComplete = result.metConditions.some(met => met.type === condition.type)
      progress.push({
        current: isComplete ? condition.value : 0, // 简化处理，实际应该获取真实当前值
        required: condition.value,
        percentage: isComplete ? 100 : 0,
        label: condition.description,
        isComplete,
        estimatedTime: isComplete ? undefined : getConditionEstimatedTime(condition)
      })
    })

    return progress
  }

  // 获取条件预估时间
  const getConditionEstimatedTime = (condition: UpgradeCondition): string => {
    switch (condition.type) {
      case 'experience':
        return `约需 ${Math.ceil(condition.value / 50)} 次专注会话`
      case 'focus_time':
        return `约需 ${Math.ceil(condition.value / 60)} 小时`
      case 'crops_harvested':
        return `约需 ${Math.ceil(condition.value / 3)} 天种植`
      case 'streak':
        return `约需 ${condition.value} 天`
      default:
        return '时间不定'
    }
  }

  // 处理升级确认
  const handleUpgradeConfirm = async () => {
    if (!upgradeSystem || !upgradeResult?.canUpgrade || !nextLevelInfo || !unlockSystem || !animationSystem) {
      return
    }

    setIsUpgrading(true)
    setShowUpgradeConfirm(false)

    try {
      // 执行升级
      const success = await upgradeSystem.upgradeToNextLevel()
      
      if (success) {
        // 获取解锁内容
        const unlockedContent = unlockSystem.getUnlockedContent(upgradeResult.nextLevel!)
        
        // 播放升级动画
        if (animationRef.current) {
          await animationSystem.playUpgradeAnimation(
            upgradeResult.nextLevel!,
            unlockedContent,
            animationRef.current
          )
        }
        
        // 通知父组件
        if (onLevelUp) {
          onLevelUp(upgradeResult.nextLevel!, nextLevelInfo.rewards)
        }
        
        // 重新加载信息
        await loadUpgradeInfo()
      }
    } catch (error) {
      console.error('Upgrade failed:', error)
    } finally {
      setIsUpgrading(false)
    }
  }

  // 获取作物图标
  const getCropIcon = (cropType: CropType): string => {
    const cropIcons: Record<CropType, string> = {
      [CropType.KNOWLEDGE_FLOWER]: '🌸',
      [CropType.STRENGTH_TREE]: '🌳',
      [CropType.TIME_VEGGIE]: '🥕',
      [CropType.MEDITATION_LOTUS]: '🪷',
      [CropType.FOCUS_FLOWER]: '🌺',
      [CropType.READING_VINE]: '📚',
      [CropType.SOCIAL_FRUIT]: '🍎'
    }
    return cropIcons[cropType] || '🌱'
  }

  // 获取解锁类型图标
  const getUnlockIcon = (type: UnlockType): string => {
    const typeIcons: Record<UnlockType, string> = {
      [UnlockType.CROP]: '🌱',
      [UnlockType.FEATURE]: '⚡',
      [UnlockType.TOOL]: '🛠️',
      [UnlockType.DECORATION]: '🎨',
      [UnlockType.ABILITY]: '✨',
      [UnlockType.AREA]: '🗺️',
      [UnlockType.MODE]: '🎮',
      [UnlockType.CUSTOMIZATION]: '🎨'
    }
    return typeIcons[type] || '📦'
  }

  if (!isOpen) return null

  return (
    <div className="farm-upgrade-modal-overlay" onClick={onClose} style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 1000
    }}>
      <div 
        className="farm-upgrade-modal"
        onClick={(e) => e.stopPropagation()}
        style={{
          backgroundColor: 'rgba(20, 25, 40, 0.95)',
          border: '2px solid rgba(255, 255, 255, 0.2)',
          borderRadius: '16px',
          padding: '24px',
          maxWidth: '900px',
          maxHeight: '90vh',
          width: '90%',
          overflowY: 'auto',
          backdropFilter: 'blur(10px)'
        }}
      >
        {/* 头部 */}
        <div className="modal-header" style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '24px',
          borderBottom: '1px solid rgba(255, 255, 255, 0.1)',
          paddingBottom: '16px'
        }}>
          <div>
            <h2 style={{
              color: 'white',
              margin: '0 0 8px 0',
              fontSize: '28px',
              fontWeight: 'bold'
            }}>
              🏡 农场升级系统
            </h2>
            <p style={{
              color: 'rgba(255, 255, 255, 0.7)',
              margin: 0,
              fontSize: '16px'
            }}>
              {currentLevelInfo ? `当前: ${currentLevelInfo.icon} ${currentLevelInfo.name}` : '载入中...'}
            </p>
          </div>
          
          <button
            onClick={onClose}
            style={{
              background: 'none',
              border: 'none',
              color: 'rgba(255, 255, 255, 0.7)',
              fontSize: '24px',
              cursor: 'pointer',
              padding: '4px'
            }}
          >
            ×
          </button>
        </div>

        {/* 选项卡 */}
        <div className="upgrade-tabs" style={{
          display: 'flex',
          marginBottom: '24px',
          borderRadius: '12px',
          backgroundColor: 'rgba(255, 255, 255, 0.05)',
          padding: '4px'
        }}>
          {[
            { id: 'progress', label: '升级进度', icon: '📈' },
            { id: 'unlocks', label: '解锁内容', icon: '🔓' },
            { id: 'rewards', label: '升级奖励', icon: '🎁' }
          ].map(tab => (
            <button
              key={tab.id}
              onClick={() => setSelectedTab(tab.id as any)}
              style={{
                flex: 1,
                padding: '12px',
                border: 'none',
                borderRadius: '8px',
                backgroundColor: selectedTab === tab.id ? 'rgba(255, 255, 255, 0.1)' : 'transparent',
                color: selectedTab === tab.id ? 'white' : 'rgba(255, 255, 255, 0.7)',
                cursor: 'pointer',
                fontSize: '14px',
                fontWeight: selectedTab === tab.id ? 'bold' : 'normal',
                transition: 'all 0.3s ease'
              }}
            >
              {tab.icon} {tab.label}
            </button>
          ))}
        </div>

        {/* 内容区域 */}
        <div className="upgrade-content" style={{ minHeight: '400px' }}>
          
          {/* 升级进度选项卡 */}
          {selectedTab === 'progress' && (
            <div className="progress-tab">
              {/* 当前等级信息 */}
              {currentLevelInfo && (
                <div className="current-level-info" style={{
                  backgroundColor: 'rgba(255, 255, 255, 0.1)',
                  borderRadius: '12px',
                  padding: '20px',
                  marginBottom: '24px'
                }}>
                  <h3 style={{
                    color: 'white',
                    margin: '0 0 12px 0',
                    fontSize: '20px',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '12px'
                  }}>
                    <span style={{ fontSize: '32px' }}>{currentLevelInfo.icon}</span>
                    {currentLevelInfo.name}
                  </h3>
                  <p style={{
                    color: 'rgba(255, 255, 255, 0.8)',
                    margin: '0 0 16px 0',
                    lineHeight: '1.5'
                  }}>
                    {currentLevelInfo.description}
                  </p>
                  
                  <div style={{
                    display: 'grid',
                    gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
                    gap: '12px',
                    fontSize: '14px'
                  }}>
                    <div>
                      <strong style={{ color: 'white' }}>已解锁功能:</strong>
                      <br />
                      {currentLevelInfo.unlockedFeatures.map((feature, index) => (
                        <span key={index} style={{ color: 'rgba(255, 255, 255, 0.8)' }}>
                          {feature}{index < currentLevelInfo.unlockedFeatures.length - 1 ? ', ' : ''}
                        </span>
                      ))}
                    </div>
                    
                    <div>
                      <strong style={{ color: 'white' }}>已解锁作物:</strong>
                      <br />
                      <div style={{ display: 'flex', gap: '4px', marginTop: '4px' }}>
                        {currentLevelInfo.unlockedCrops.map((cropType, index) => (
                          <span key={index} style={{ fontSize: '20px' }}>
                            {getCropIcon(cropType)}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* 升级条件进度 */}
              {nextLevelInfo && (
                <div className="upgrade-progress" style={{
                  backgroundColor: 'rgba(255, 255, 255, 0.1)',
                  borderRadius: '12px',
                  padding: '20px',
                  marginBottom: '24px'
                }}>
                  <h3 style={{
                    color: 'white',
                    margin: '0 0 16px 0',
                    fontSize: '18px',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '12px'
                  }}>
                    <span style={{ fontSize: '24px' }}>{nextLevelInfo.icon}</span>
                    升级到 {nextLevelInfo.name}
                  </h3>
                  
                  {upgradeResult && (
                    <div style={{
                      backgroundColor: 'rgba(255, 255, 255, 0.05)',
                      borderRadius: '8px',
                      padding: '12px',
                      marginBottom: '16px'
                    }}>
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <span style={{ color: 'white', fontSize: '14px' }}>总体进度:</span>
                        <span style={{ color: 'white', fontSize: '14px', fontWeight: 'bold' }}>
                          {Math.round(upgradeResult.progressPercentage)}%
                        </span>
                      </div>
                      <div style={{
                        width: '100%',
                        height: '8px',
                        backgroundColor: 'rgba(255, 255, 255, 0.1)',
                        borderRadius: '4px',
                        marginTop: '8px',
                        overflow: 'hidden'
                      }}>
                        <div style={{
                          width: `${upgradeResult.progressPercentage}%`,
                          height: '100%',
                          backgroundColor: upgradeResult.canUpgrade ? '#4CAF50' : '#2196F3',
                          transition: 'width 0.3s ease'
                        }} />
                      </div>
                    </div>
                  )}

                  {/* 必需条件 */}
                  <div style={{ marginBottom: '16px' }}>
                    <h4 style={{ color: 'white', margin: '0 0 12px 0', fontSize: '16px' }}>
                      必需条件:
                    </h4>
                    <div style={{ display: 'grid', gap: '8px' }}>
                      {levelProgress.map((progress, index) => (
                        <div key={index} style={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: '12px',
                          padding: '8px',
                          backgroundColor: 'rgba(255, 255, 255, 0.05)',
                          borderRadius: '6px'
                        }}>
                          <div style={{
                            fontSize: '16px',
                            color: progress.isComplete ? '#4CAF50' : 'rgba(255, 255, 255, 0.5)'
                          }}>
                            {progress.isComplete ? '✅' : '⏱️'}
                          </div>
                          <div style={{ flex: 1 }}>
                            <div style={{
                              color: 'white',
                              fontSize: '14px',
                              marginBottom: '4px'
                            }}>
                              {progress.label}
                            </div>
                            <div style={{
                              color: 'rgba(255, 255, 255, 0.7)',
                              fontSize: '12px'
                            }}>
                              {progress.current}/{progress.required} • {progress.estimatedTime}
                            </div>
                          </div>
                          <div style={{
                            color: progress.isComplete ? '#4CAF50' : 'rgba(255, 255, 255, 0.7)',
                            fontSize: '12px',
                            fontWeight: 'bold'
                          }}>
                            {Math.round(progress.percentage)}%
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* 升级按钮 */}
                  {upgradeResult?.canUpgrade && (
                    <button
                      onClick={() => setShowUpgradeConfirm(true)}
                      disabled={isUpgrading}
                      style={{
                        width: '100%',
                        padding: '16px',
                        backgroundColor: '#4CAF50',
                        color: 'white',
                        border: 'none',
                        borderRadius: '8px',
                        fontSize: '16px',
                        fontWeight: 'bold',
                        cursor: isUpgrading ? 'not-allowed' : 'pointer',
                        opacity: isUpgrading ? 0.6 : 1,
                        transition: 'all 0.3s ease'
                      }}
                    >
                      {isUpgrading ? '升级中...' : `🚀 升级到 ${nextLevelInfo.name}`}
                    </button>
                  )}
                </div>
              )}
            </div>
          )}

          {/* 解锁内容选项卡 */}
          {selectedTab === 'unlocks' && nextLevelInfo && (
            <div className="unlocks-tab">
              <h3 style={{ color: 'white', margin: '0 0 20px 0', fontSize: '18px' }}>
                升级后将解锁:
              </h3>
              
              {/* 新作物 */}
              {nextLevelInfo.unlockedCrops.length > 0 && (
                <div style={{
                  backgroundColor: 'rgba(255, 255, 255, 0.1)',
                  borderRadius: '12px',
                  padding: '16px',
                  marginBottom: '16px'
                }}>
                  <h4 style={{ color: 'white', margin: '0 0 12px 0', fontSize: '16px' }}>
                    🌱 新作物类型
                  </h4>
                  <div style={{
                    display: 'grid',
                    gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
                    gap: '12px'
                  }}>
                    {nextLevelInfo.unlockedCrops.map((cropType, index) => (
                      <div key={index} style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '8px',
                        padding: '8px',
                        backgroundColor: 'rgba(255, 255, 255, 0.05)',
                        borderRadius: '6px'
                      }}>
                        <span style={{ fontSize: '24px' }}>{getCropIcon(cropType)}</span>
                        <span style={{ color: 'white', fontSize: '14px' }}>
                          {cropType.replace('_', ' ')}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* 新功能 */}
              {nextLevelInfo.unlockedFeatures.length > 0 && (
                <div style={{
                  backgroundColor: 'rgba(255, 255, 255, 0.1)',
                  borderRadius: '12px',
                  padding: '16px',
                  marginBottom: '16px'
                }}>
                  <h4 style={{ color: 'white', margin: '0 0 12px 0', fontSize: '16px' }}>
                    ⚡ 新功能
                  </h4>
                  <div style={{ display: 'grid', gap: '8px' }}>
                    {nextLevelInfo.unlockedFeatures.map((feature, index) => (
                      <div key={index} style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '8px',
                        padding: '8px',
                        backgroundColor: 'rgba(255, 255, 255, 0.05)',
                        borderRadius: '6px'
                      }}>
                        <span style={{ color: '#4CAF50' }}>✨</span>
                        <span style={{ color: 'white', fontSize: '14px' }}>
                          {feature}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* 农田扩展 */}
              {nextLevelInfo.plotExpansion && (
                <div style={{
                  backgroundColor: 'rgba(255, 255, 255, 0.1)',
                  borderRadius: '12px',
                  padding: '16px',
                  marginBottom: '16px'
                }}>
                  <h4 style={{ color: 'white', margin: '0 0 12px 0', fontSize: '16px' }}>
                    🗺️ 农田扩展
                  </h4>
                  <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px',
                    padding: '8px',
                    backgroundColor: 'rgba(255, 255, 255, 0.05)',
                    borderRadius: '6px'
                  }}>
                    <span style={{ color: '#2196F3' }}>📏</span>
                    <span style={{ color: 'white', fontSize: '14px' }}>
                      增加 {nextLevelInfo.plotExpansion.newPlots} 个种植地块
                      {nextLevelInfo.plotExpansion.plotSize && 
                        ` (${nextLevelInfo.plotExpansion.plotSize})`
                      }
                    </span>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* 升级奖励选项卡 */}
          {selectedTab === 'rewards' && nextLevelInfo && (
            <div className="rewards-tab">
              <h3 style={{ color: 'white', margin: '0 0 20px 0', fontSize: '18px' }}>
                升级奖励:
              </h3>
              
              {nextLevelInfo.rewards.length > 0 ? (
                <div style={{
                  display: 'grid',
                  gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
                  gap: '16px'
                }}>
                  {nextLevelInfo.rewards.map((reward, index) => (
                    <div key={index} style={{
                      backgroundColor: 'rgba(255, 255, 255, 0.1)',
                      borderRadius: '12px',
                      padding: '16px',
                      border: `2px solid ${reward.rarity === 'legendary' ? '#FFD700' : 
                                           reward.rarity === 'epic' ? '#9C27B0' :
                                           reward.rarity === 'rare' ? '#2196F3' : 
                                           'rgba(255, 255, 255, 0.2)'}`
                    }}>
                      <div style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '12px',
                        marginBottom: '8px'
                      }}>
                        <span style={{ fontSize: '32px' }}>
                          {reward.icon || '🎁'}
                        </span>
                        <div>
                          <h4 style={{
                            color: 'white',
                            margin: '0 0 4px 0',
                            fontSize: '16px'
                          }}>
                            {reward.item}
                          </h4>
                          <div style={{
                            color: 'rgba(255, 255, 255, 0.7)',
                            fontSize: '12px'
                          }}>
                            数量: {reward.quantity}
                            {reward.rarity && (
                              <span style={{
                                marginLeft: '8px',
                                padding: '2px 6px',
                                borderRadius: '4px',
                                backgroundColor: reward.rarity === 'legendary' ? '#FFD700' : 
                                                 reward.rarity === 'epic' ? '#9C27B0' :
                                                 reward.rarity === 'rare' ? '#2196F3' : '#4CAF50',
                                color: 'white',
                                fontSize: '10px'
                              }}>
                                {reward.rarity}
                              </span>
                            )}
                          </div>
                        </div>
                      </div>
                      <p style={{
                        color: 'rgba(255, 255, 255, 0.8)',
                        margin: 0,
                        fontSize: '14px',
                        lineHeight: '1.4'
                      }}>
                        {reward.description}
                      </p>
                    </div>
                  ))}
                </div>
              ) : (
                <div style={{
                  textAlign: 'center',
                  color: 'rgba(255, 255, 255, 0.7)',
                  fontSize: '16px',
                  padding: '40px'
                }}>
                  当前等级暂无额外奖励
                </div>
              )}
            </div>
          )}
        </div>

        {/* 升级确认对话框 */}
        {showUpgradeConfirm && nextLevelInfo && (
          <div style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 1001
          }}>
            <div style={{
              backgroundColor: 'rgba(20, 25, 40, 0.95)',
              border: '2px solid rgba(255, 255, 255, 0.2)',
              borderRadius: '12px',
              padding: '24px',
              maxWidth: '400px',
              width: '90%',
              backdropFilter: 'blur(10px)'
            }}>
              <h3 style={{ color: 'white', margin: '0 0 16px 0', fontSize: '20px', textAlign: 'center' }}>
                🚀 确认升级
              </h3>
              <p style={{ color: 'rgba(255, 255, 255, 0.8)', margin: '0 0 20px 0', textAlign: 'center' }}>
                确定要升级到 <strong>{nextLevelInfo.name}</strong> 吗？
              </p>
              <div style={{ display: 'flex', gap: '12px' }}>
                <button
                  onClick={() => setShowUpgradeConfirm(false)}
                  style={{
                    flex: 1,
                    padding: '12px',
                    backgroundColor: 'rgba(255, 255, 255, 0.1)',
                    color: 'white',
                    border: 'none',
                    borderRadius: '6px',
                    cursor: 'pointer'
                  }}
                >
                  取消
                </button>
                <button
                  onClick={handleUpgradeConfirm}
                  style={{
                    flex: 1,
                    padding: '12px',
                    backgroundColor: '#4CAF50',
                    color: 'white',
                    border: 'none',
                    borderRadius: '6px',
                    cursor: 'pointer'
                  }}
                >
                  确认升级
                </button>
              </div>
            </div>
          </div>
        )}

        {/* 动画容器 */}
        <div 
          ref={animationRef}
          className="upgrade-animation-container"
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            pointerEvents: 'none',
            overflow: 'hidden',
            borderRadius: '16px'
          }}
        />
      </div>
    </div>
  )
}

export default FarmUpgradeInterface
