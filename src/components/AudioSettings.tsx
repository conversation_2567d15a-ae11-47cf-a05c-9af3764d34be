import React, { useCallback, useState } from 'react'
import { useAudioManager } from '../hooks/useAudioManager'

interface AudioSettingsProps {
  className?: string
  onClose?: () => void
}

/**
 * 音频设置组件
 * 提供详细的音量控制、静音选项和音频偏好设置
 */
export const AudioSettings: React.FC<AudioSettingsProps> = ({ 
  className = '', 
  onClose 
}) => {
  const { 
    setVolume, 
    setEnabled,
    audioSettings,
    audioStatus
  } = useAudioManager()

  const [tempSettings, setTempSettings] = useState(audioSettings)

  // 临时更新设置
  const updateTempSetting = useCallback((key: keyof typeof audioSettings, value: any) => {
    setTempSettings(prev => ({ ...prev, [key]: value }))
    
    // 实时应用音量变化
    if (key.includes('Volume')) {
      const volumeType = key.replace('Volume', '') as 'master' | 'music' | 'effects'
      setVolume(volumeType, value)
    } else if (key.includes('Enabled')) {
      const enableType = key.replace('Enabled', '') as 'music' | 'effects'
      setEnabled(enableType, value)
    }
  }, [setVolume, setEnabled])

  // 重置为默认设置
  const resetToDefaults = useCallback(() => {
    const defaults = {
      masterVolume: 0.7,
      musicVolume: 0.5,
      effectsVolume: 0.6,
      musicEnabled: true,
      effectsEnabled: true,
      currentMusic: 'farm_ambient'
    }
    
    setTempSettings(defaults)
    Object.entries(defaults).forEach(([key, value]) => {
      if (key.includes('Volume')) {
        const volumeType = key.replace('Volume', '') as 'master' | 'music' | 'effects'
        setVolume(volumeType, value as number)
      } else if (key.includes('Enabled')) {
        const enableType = key.replace('Enabled', '') as 'music' | 'effects'
        setEnabled(enableType, value as boolean)
      }
    })
  }, [setVolume, setEnabled])

  // 一键静音所有音频
  const toggleMuteAll = useCallback(() => {
    const isMuted = tempSettings.masterVolume === 0
    const newVolume = isMuted ? 0.7 : 0
    updateTempSetting('masterVolume', newVolume)
  }, [tempSettings.masterVolume, updateTempSetting])

  // 音乐静音切换
  const toggleMuteMusic = useCallback(() => {
    const isMuted = tempSettings.musicVolume === 0
    const newVolume = isMuted ? 0.5 : 0
    updateTempSetting('musicVolume', newVolume)
  }, [tempSettings.musicVolume, updateTempSetting])

  // 音效静音切换
  const toggleMuteEffects = useCallback(() => {
    const isMuted = tempSettings.effectsVolume === 0
    const newVolume = isMuted ? 0.6 : 0
    updateTempSetting('effectsVolume', newVolume)
  }, [tempSettings.effectsVolume, updateTempSetting])

  const containerStyle: React.CSSProperties = {
    background: 'linear-gradient(135deg, #34495e, #2c3e50)',
    borderRadius: '16px',
    padding: '24px',
    color: 'white',
    fontFamily: "'Segoe UI', system-ui, sans-serif",
    boxShadow: '0 8px 32px rgba(0,0,0,0.4)',
    maxWidth: '500px',
    width: '100%',
    position: 'relative'
  }

  const sectionStyle: React.CSSProperties = {
    marginBottom: '24px',
    padding: '16px',
    background: 'rgba(255,255,255,0.05)',
    borderRadius: '12px',
    border: '1px solid rgba(255,255,255,0.1)'
  }

  const sliderContainerStyle: React.CSSProperties = {
    marginBottom: '16px'
  }

  const labelStyle: React.CSSProperties = {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: '8px',
    fontSize: '14px',
    fontWeight: 500
  }

  const sliderStyle: React.CSSProperties = {
    width: '100%',
    height: '8px',
    borderRadius: '4px',
    background: 'rgba(255,255,255,0.2)',
    outline: 'none',
    WebkitAppearance: 'none',
    MozAppearance: 'none'
  }

  const buttonStyle: React.CSSProperties = {
    background: '#3498db',
    border: 'none',
    color: 'white',
    padding: '10px 20px',
    borderRadius: '8px',
    cursor: 'pointer',
    fontSize: '14px',
    fontWeight: 500,
    transition: 'all 0.2s',
    marginRight: '8px'
  }

  const muteButtonStyle = (isMuted: boolean): React.CSSProperties => ({
    ...buttonStyle,
    background: isMuted ? '#e74c3c' : '#27ae60',
    minWidth: '80px'
  })

  return (
    <div className={className} style={containerStyle}>
      {/* 标题栏 */}
      <div style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center', 
        marginBottom: '24px',
        borderBottom: '1px solid rgba(255,255,255,0.2)',
        paddingBottom: '16px'
      }}>
        <h2 style={{ margin: 0, fontSize: '1.5em', fontWeight: 600 }}>
          🔊 音频设置
        </h2>
        {onClose && (
          <button
            onClick={onClose}
            style={{
              background: 'none',
              border: 'none',
              color: '#bdc3c7',
              cursor: 'pointer',
              fontSize: '24px',
              padding: '4px',
              borderRadius: '4px',
              transition: 'color 0.2s'
            }}
            title="关闭设置"
          >
            ✕
          </button>
        )}
      </div>

      {/* 系统状态 */}
      <div style={sectionStyle}>
        <h3 style={{ margin: '0 0 16px 0', fontSize: '1.1em', fontWeight: 600 }}>
          📊 系统状态
        </h3>
        <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '12px', fontSize: '13px' }}>
          <div>音频系统: {audioStatus.isInitialized ? '🟢 已初始化' : '🔴 未初始化'}</div>
          <div>播放状态: {audioStatus.isPlaying ? '🎵 播放中' : '⏸️ 已暂停'}</div>
          <div>音频上下文: {audioStatus.audioContextState || '未知'}</div>
          <div>已加载资源: {audioStatus.loadedAudios || 0} 个</div>
        </div>
      </div>

      {/* 主音量控制 */}
      <div style={sectionStyle}>
        <h3 style={{ margin: '0 0 16px 0', fontSize: '1.1em', fontWeight: 600 }}>
          🔊 主音量控制
        </h3>
        
        <div style={sliderContainerStyle}>
          <div style={labelStyle}>
            <span>主音量</span>
            <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
              <span>{Math.round(tempSettings.masterVolume * 100)}%</span>
              <button
                onClick={toggleMuteAll}
                style={muteButtonStyle(tempSettings.masterVolume === 0)}
                title={tempSettings.masterVolume === 0 ? '取消静音' : '静音'}
              >
                {tempSettings.masterVolume === 0 ? '🔇 静音' : '🔊 音量'}
              </button>
            </div>
          </div>
          <input
            type="range"
            min="0"
            max="100"
            value={Math.round(tempSettings.masterVolume * 100)}
            onChange={(e) => updateTempSetting('masterVolume', Number(e.target.value) / 100)}
            style={sliderStyle}
          />
        </div>

        <div style={{ fontSize: '12px', color: '#bdc3c7', fontStyle: 'italic' }}>
          💡 主音量控制所有音频的整体音量水平
        </div>
      </div>

      {/* 分类音量控制 */}
      <div style={sectionStyle}>
        <h3 style={{ margin: '0 0 16px 0', fontSize: '1.1em', fontWeight: 600 }}>
          🎛️ 分类音量控制
        </h3>

        {/* 背景音乐音量 */}
        <div style={sliderContainerStyle}>
          <div style={labelStyle}>
            <span>🎵 背景音乐</span>
            <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
              <span>{Math.round(tempSettings.musicVolume * 100)}%</span>
              <button
                onClick={toggleMuteMusic}
                style={muteButtonStyle(tempSettings.musicVolume === 0)}
                title={tempSettings.musicVolume === 0 ? '取消静音' : '静音'}
              >
                {tempSettings.musicVolume === 0 ? '🔇' : '🎵'}
              </button>
            </div>
          </div>
          <input
            type="range"
            min="0"
            max="100"
            value={Math.round(tempSettings.musicVolume * 100)}
            onChange={(e) => updateTempSetting('musicVolume', Number(e.target.value) / 100)}
            disabled={!tempSettings.musicEnabled}
            style={{
              ...sliderStyle,
              opacity: tempSettings.musicEnabled ? 1 : 0.5
            }}
          />
          <div style={{ marginTop: '8px' }}>
            <label style={{ display: 'flex', alignItems: 'center', gap: '8px', fontSize: '13px' }}>
              <input
                type="checkbox"
                checked={tempSettings.musicEnabled}
                onChange={(e) => updateTempSetting('musicEnabled', e.target.checked)}
                style={{ marginRight: '4px' }}
              />
              启用背景音乐
            </label>
          </div>
        </div>

        {/* 音效音量 */}
        <div style={sliderContainerStyle}>
          <div style={labelStyle}>
            <span>🔔 游戏音效</span>
            <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
              <span>{Math.round(tempSettings.effectsVolume * 100)}%</span>
              <button
                onClick={toggleMuteEffects}
                style={muteButtonStyle(tempSettings.effectsVolume === 0)}
                title={tempSettings.effectsVolume === 0 ? '取消静音' : '静音'}
              >
                {tempSettings.effectsVolume === 0 ? '🔇' : '🔔'}
              </button>
            </div>
          </div>
          <input
            type="range"
            min="0"
            max="100"
            value={Math.round(tempSettings.effectsVolume * 100)}
            onChange={(e) => updateTempSetting('effectsVolume', Number(e.target.value) / 100)}
            disabled={!tempSettings.effectsEnabled}
            style={{
              ...sliderStyle,
              opacity: tempSettings.effectsEnabled ? 1 : 0.5
            }}
          />
          <div style={{ marginTop: '8px' }}>
            <label style={{ display: 'flex', alignItems: 'center', gap: '8px', fontSize: '13px' }}>
              <input
                type="checkbox"
                checked={tempSettings.effectsEnabled}
                onChange={(e) => updateTempSetting('effectsEnabled', e.target.checked)}
                style={{ marginRight: '4px' }}
              />
              启用游戏音效
            </label>
          </div>
        </div>
      </div>

      {/* 快捷操作 */}
      <div style={sectionStyle}>
        <h3 style={{ margin: '0 0 16px 0', fontSize: '1.1em', fontWeight: 600 }}>
          ⚡ 快捷操作
        </h3>
        
        <div style={{ display: 'flex', flexWrap: 'wrap', gap: '8px' }}>
          <button
            onClick={toggleMuteAll}
            style={muteButtonStyle(tempSettings.masterVolume === 0)}
          >
            {tempSettings.masterVolume === 0 ? '🔊 取消全局静音' : '🔇 全局静音'}
          </button>
          
          <button
            onClick={resetToDefaults}
            style={{
              ...buttonStyle,
              background: '#f39c12'
            }}
          >
            🔄 恢复默认设置
          </button>
        </div>
      </div>

      {/* 音频质量信息 */}
      <div style={{ 
        fontSize: '11px', 
        color: '#95a5a6', 
        textAlign: 'center',
        marginTop: '16px',
        padding: '8px',
        background: 'rgba(255,255,255,0.03)',
        borderRadius: '6px'
      }}>
        💡 提示：音量设置会自动保存到本地存储
      </div>
    </div>
  )
}

export default AudioSettings 