import React, { ReactNode, CSSProperties } from 'react'
import { useDeviceType, useMediaQuery, useOrientation, MEDIA_QUERIES } from '../utils/responsive'
import './ResponsiveLayout.css'

// 网格系统类型定义
export interface GridProps {
  children: ReactNode
  cols?: number | { mobile?: number; tablet?: number; desktop?: number }
  gap?: number | string | { mobile?: number | string; tablet?: number | string; desktop?: number | string }
  className?: string
  style?: CSSProperties
}

// 容器组件类型定义
export interface ContainerProps {
  children: ReactNode
  maxWidth?: number | string | { mobile?: number | string; tablet?: number | string; desktop?: number | string }
  fluid?: boolean
  className?: string
  style?: CSSProperties
}

// 弹性布局组件类型定义
export interface FlexProps {
  children: ReactNode
  direction?: 'row' | 'column' | { mobile?: 'row' | 'column'; tablet?: 'row' | 'column'; desktop?: 'row' | 'column' }
  align?: 'start' | 'center' | 'end' | 'stretch'
  justify?: 'start' | 'center' | 'end' | 'between' | 'around' | 'evenly'
  wrap?: boolean
  gap?: number | string
  className?: string
  style?: CSSProperties
}

// 显示控制组件类型定义
export interface ShowProps {
  children: ReactNode
  on?: ('mobile' | 'tablet' | 'desktop')[]
  above?: 'mobile' | 'tablet' | 'desktop'
  below?: 'mobile' | 'tablet' | 'desktop'
  className?: string
}

// 网格系统组件
export const Grid: React.FC<GridProps> = ({ 
  children, 
  cols = { mobile: 1, tablet: 2, desktop: 3 }, 
  gap = 16,
  className = '',
  style = {}
}) => {
  const deviceType = useDeviceType()
  
  // 解析列数
  const getColumns = () => {
    if (typeof cols === 'number') return cols
    return cols[deviceType] || cols.desktop || 3
  }
  
  // 解析间隙
  const getGap = () => {
    if (typeof gap === 'number' || typeof gap === 'string') return gap
    return gap[deviceType] || gap.desktop || 16
  }
  
  const gridStyle: CSSProperties = {
    display: 'grid',
    gridTemplateColumns: `repeat(${getColumns()}, 1fr)`,
    gap: getGap(),
    ...style
  }
  
  return (
    <div 
      className={`responsive-grid ${className}`} 
      style={gridStyle}
    >
      {children}
    </div>
  )
}

// 容器组件
export const Container: React.FC<ContainerProps> = ({ 
  children, 
  maxWidth = { mobile: '100%', tablet: '768px', desktop: '1200px' },
  fluid = false,
  className = '',
  style = {}
}) => {
  const deviceType = useDeviceType()
  
  // 解析最大宽度
  const getMaxWidth = () => {
    if (fluid) return '100%'
    if (typeof maxWidth === 'number' || typeof maxWidth === 'string') return maxWidth
    return maxWidth[deviceType] || maxWidth.desktop || '1200px'
  }
  
  const containerStyle: CSSProperties = {
    width: '100%',
    maxWidth: getMaxWidth(),
    margin: '0 auto',
    padding: deviceType === 'mobile' ? '0 16px' : '0 24px',
    ...style
  }
  
  return (
    <div 
      className={`responsive-container ${className}`} 
      style={containerStyle}
    >
      {children}
    </div>
  )
}

// 弹性布局组件
export const Flex: React.FC<FlexProps> = ({ 
  children, 
  direction = 'row',
  align = 'start',
  justify = 'start',
  wrap = false,
  gap = 0,
  className = '',
  style = {}
}) => {
  const deviceType = useDeviceType()
  
  // 解析方向
  const getDirection = () => {
    if (typeof direction === 'string') return direction
    return direction[deviceType] || direction.desktop || 'row'
  }
  
  const flexStyle: CSSProperties = {
    display: 'flex',
    flexDirection: getDirection(),
    alignItems: align === 'start' ? 'flex-start' : 
                align === 'end' ? 'flex-end' : 
                align === 'center' ? 'center' : 'stretch',
    justifyContent: justify === 'start' ? 'flex-start' : 
                   justify === 'end' ? 'flex-end' : 
                   justify === 'center' ? 'center' : 
                   justify === 'between' ? 'space-between' : 
                   justify === 'around' ? 'space-around' : 
                   justify === 'evenly' ? 'space-evenly' : 'flex-start',
    flexWrap: wrap ? 'wrap' : 'nowrap',
    gap: gap,
    ...style
  }
  
  return (
    <div 
      className={`responsive-flex ${className}`} 
      style={flexStyle}
    >
      {children}
    </div>
  )
}

// 显示控制组件
export const Show: React.FC<ShowProps> = ({ 
  children, 
  on, 
  above, 
  below,
  className = ''
}) => {
  const deviceType = useDeviceType()
  const isMobile = useMediaQuery(MEDIA_QUERIES.mobile)
  const isTablet = useMediaQuery(MEDIA_QUERIES.tablet)
  
  // 检查是否应该显示
  const shouldShow = () => {
    // 如果指定了特定设备
    if (on) {
      return on.includes(deviceType)
    }
    
    // 如果指定了above
    if (above) {
      if (above === 'mobile') return !isMobile
      if (above === 'tablet') return !isTablet && !isMobile
      if (above === 'desktop') return !isTablet && !isMobile
    }
    
    // 如果指定了below
    if (below) {
      if (below === 'desktop') return isTablet || isMobile
      if (below === 'tablet') return isMobile
    }
    
    return true
  }
  
  if (!shouldShow()) return null
  
  return (
    <div className={`responsive-show ${className}`}>
      {children}
    </div>
  )
}

// 断点包装器组件
interface BreakpointProps {
  children: ReactNode
  mobile?: ReactNode
  tablet?: ReactNode
  desktop?: ReactNode
}

export const Breakpoint: React.FC<BreakpointProps> = ({ 
  children, 
  mobile, 
  tablet, 
  desktop 
}) => {
  const deviceType = useDeviceType()
  
  switch (deviceType) {
    case 'mobile':
      return <>{mobile || children}</>
    case 'tablet':
      return <>{tablet || children}</>
    case 'desktop':
      return <>{desktop || children}</>
    default:
      return <>{children}</>
  }
}

// 游戏画布响应式包装器
interface ResponsiveGameCanvasProps {
  children: ReactNode
  baseWidth?: number
  baseHeight?: number
  maintainAspectRatio?: boolean
  className?: string
}

export const ResponsiveGameCanvas: React.FC<ResponsiveGameCanvasProps> = ({
  children,
  baseWidth = 800,
  baseHeight = 600,
  maintainAspectRatio = true,
  className = ''
}) => {
  const deviceType = useDeviceType()
  const orientation = useOrientation()
  
  const getCanvasStyle = (): CSSProperties => {
    if (deviceType === 'mobile') {
      const aspectRatio = baseWidth / baseHeight
      
      if (orientation === 'portrait') {
        return {
          width: '100%',
          height: maintainAspectRatio ? `${(window.innerWidth * 0.9) / aspectRatio}px` : 'auto',
          maxHeight: '60vh',
          margin: '0 auto'
        }
      } else {
        return {
          width: '70vw',
          height: maintainAspectRatio ? `${(window.innerWidth * 0.7) / aspectRatio}px` : 'auto',
          maxHeight: '80vh',
          margin: '0 auto'
        }
      }
    }
    
    if (deviceType === 'tablet') {
      return {
        width: orientation === 'portrait' ? '90%' : '70%',
        height: 'auto',
        maxWidth: `${baseWidth}px`,
        maxHeight: `${baseHeight}px`,
        margin: '0 auto'
      }
    }
    
    // Desktop
    return {
      width: baseWidth,
      height: baseHeight,
      margin: '0 auto'
    }
  }
  
  return (
    <div 
      className={`responsive-game-canvas ${className}`}
      style={getCanvasStyle()}
    >
      {children}
    </div>
  )
}

// 自适应侧边栏组件
interface ResponsiveSidebarProps {
  children: ReactNode
  collapsible?: boolean
  defaultCollapsed?: boolean
  className?: string
}

export const ResponsiveSidebar: React.FC<ResponsiveSidebarProps> = ({
  children,
  collapsible = true,
  defaultCollapsed = false,
  className = ''
}) => {
  const deviceType = useDeviceType()
  const [isCollapsed, setIsCollapsed] = React.useState(
    deviceType === 'mobile' ? true : defaultCollapsed
  )
  
  React.useEffect(() => {
    // 在移动设备上自动折叠
    if (deviceType === 'mobile' && !isCollapsed) {
      setIsCollapsed(true)
    }
  }, [deviceType])
  
  const sidebarStyle: CSSProperties = {
    width: deviceType === 'mobile' ? 
      (isCollapsed ? '0' : '100%') : 
      (isCollapsed ? '60px' : '320px'),
    transition: 'width 0.3s ease',
    overflow: 'hidden',
    position: deviceType === 'mobile' ? 'fixed' : 'relative',
    top: deviceType === 'mobile' ? 0 : 'auto',
    left: deviceType === 'mobile' ? 0 : 'auto',
    height: deviceType === 'mobile' ? '100vh' : 'auto',
    zIndex: deviceType === 'mobile' ? 1000 : 'auto',
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    backdropFilter: 'blur(10px)'
  }
  
  return (
    <>
      {deviceType === 'mobile' && !isCollapsed && (
        <div 
          className="sidebar-overlay"
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            zIndex: 999
          }}
          onClick={() => setIsCollapsed(true)}
        />
      )}
      
      <div 
        className={`responsive-sidebar ${className}`}
        style={sidebarStyle}
      >
        {collapsible && (
          <button
            className="sidebar-toggle"
            onClick={() => setIsCollapsed(!isCollapsed)}
            style={{
              position: 'absolute',
              top: '10px',
              right: '10px',
              zIndex: 10,
              background: 'rgba(255, 255, 255, 0.9)',
              border: '1px solid #ccc',
              borderRadius: '4px',
              padding: '5px 10px',
              cursor: 'pointer'
            }}
          >
            {isCollapsed ? '📊' : '✕'}
          </button>
        )}
        
        <div 
          className="sidebar-content"
          style={{
            opacity: isCollapsed ? 0 : 1,
            transition: 'opacity 0.3s ease',
            padding: isCollapsed ? 0 : '20px'
          }}
        >
          {children}
        </div>
      </div>
    </>
  )
} 