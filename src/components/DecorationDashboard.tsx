import React, { useState, useEffect } from 'react'
import './DecorationDashboard.css'
import DecorationShop from './DecorationShop'
import DecorationMode from './DecorationMode'
import ThemeManager from './ThemeManager'
import { DecorationSystem } from '../systems/DecorationSystem'
import { 
  DecorationItem, 
  PlacedDecoration, 
  FarmTheme,
  DecorationType,
  DecorationEffectType
} from '../types/decoration'

interface DecorationDashboardProps {
  farmData: {
    level: number
    focusTokens: number
    achievements: string[]
    farmLayout: any // 农场布局数据
  }
  onClose: () => void
}

type TabType = 'shop' | 'decorations' | 'themes' | 'overview'

interface TabConfig {
  id: TabType
  label: string
  icon: string
  description: string
}

export const DecorationDashboard: React.FC<DecorationDashboardProps> = ({
  farmData,
  onClose
}) => {
  const [activeTab, setActiveTab] = useState<TabType>('overview')
  const [decorationSystem] = useState(() => new DecorationSystem())
  const [currentTheme, setCurrentTheme] = useState<string>('natural_paradise')
  const [unlockedThemes, setUnlockedThemes] = useState<string[]>(['natural_paradise'])
  const [isDecorationMode, setIsDecorationMode] = useState<boolean>(false)
  const [stats, setStats] = useState({
    totalBeauty: 0,
    totalDecorations: 0,
    ownedItems: 0,
    availableThemes: 0
  })

  // 标签页配置
  const tabs: TabConfig[] = [
    {
      id: 'overview',
      label: '总览',
      icon: '🏡',
      description: '查看装饰统计和推荐'
    },
    {
      id: 'shop',
      label: '装饰商店',
      icon: '🛒',
      description: '购买装饰道具和主题'
    },
    {
      id: 'decorations',
      label: '装饰模式',
      icon: '🎨',
      description: '放置和管理装饰道具'
    },
    {
      id: 'themes',
      label: '主题管理',
      icon: '🌟',
      description: '管理和切换农场主题'
    }
  ]

  // 初始化数据
  useEffect(() => {
    updateStats()
    loadThemes()
  }, [])

  const updateStats = () => {
    const effects = decorationSystem.calculateTotalEffects()
    const ownedDecorations = decorationSystem.getOwnedDecorations()
    const placedDecorations = decorationSystem.getPlacedDecorations()

    setStats({
      totalBeauty: effects.totalBeauty,
      totalDecorations: placedDecorations.length,
      ownedItems: ownedDecorations.length,
      availableThemes: unlockedThemes.length
    })
  }

  const loadThemes = async () => {
    const manager = decorationSystem.getManager()
    setCurrentTheme(manager.currentTheme)
    setUnlockedThemes(manager.unlockedThemes)
  }

  // 处理装饰道具购买
  const handleDecorationPurchase = async (decorationId: string, quantity: number) => {
    const result = await decorationSystem.purchaseDecoration(decorationId, quantity)
    if (result.success) {
      updateStats()
      // 显示成功消息
      console.log(result.message)
    } else {
      // 显示错误消息
      console.error(result.message)
    }
  }

  // 处理主题购买
  const handleThemePurchase = async (themeId: string, price: number) => {
    const result = await decorationSystem.purchaseTheme(themeId)
    if (result.success) {
      loadThemes()
      updateStats()
      console.log(result.message)
    } else {
      console.error(result.message)
    }
  }

  // 处理主题切换
  const handleThemeChange = (themeId: string) => {
    const result = decorationSystem.applyTheme(themeId)
    if (result.success) {
      setCurrentTheme(themeId)
      console.log(result.message)
    } else {
      console.error(result.message)
    }
  }

  // 处理装饰模式切换
  const handleDecorationModeToggle = () => {
    setIsDecorationMode(!isDecorationMode)
    if (!isDecorationMode) {
      setActiveTab('decorations')
    }
  }

  // 获取当前主题信息
  const getCurrentThemeInfo = (): FarmTheme | null => {
    return decorationSystem.getCurrentTheme()
  }

  // 获取推荐内容
  const getRecommendations = () => {
    const recommendedThemes = decorationSystem.getRecommendedThemes().slice(0, 3)
    return {
      themes: recommendedThemes,
      // 这里可以添加推荐装饰道具的逻辑
      decorations: []
    }
  }

  const currentThemeInfo = getCurrentThemeInfo()
  const recommendations = getRecommendations()

  return (
    <div className="decoration-dashboard">
      {/* 头部区域 */}
      <div className="dashboard__header">
        <div className="header__content">
          <div className="header__title">
            <h1>装饰与个性化</h1>
            <p>打造您独特的农场风格</p>
          </div>
          
          <div className="header__actions">
            <button 
              className={`btn btn--toggle ${isDecorationMode ? 'btn--active' : ''}`}
              onClick={handleDecorationModeToggle}
            >
              {isDecorationMode ? '退出装饰' : '进入装饰'}
            </button>
            <button className="btn btn--close" onClick={onClose}>
              ✕
            </button>
          </div>
        </div>

        {/* 快速统计 */}
        <div className="header__stats">
          <div className="stat-card">
            <div className="stat-card__icon">✨</div>
            <div className="stat-card__content">
              <div className="stat-card__value">{stats.totalBeauty}</div>
              <div className="stat-card__label">美观度</div>
            </div>
          </div>
          
          <div className="stat-card">
            <div className="stat-card__icon">🎨</div>
            <div className="stat-card__content">
              <div className="stat-card__value">{stats.totalDecorations}</div>
              <div className="stat-card__label">已放置装饰</div>
            </div>
          </div>
          
          <div className="stat-card">
            <div className="stat-card__icon">📦</div>
            <div className="stat-card__content">
              <div className="stat-card__value">{stats.ownedItems}</div>
              <div className="stat-card__label">拥有道具</div>
            </div>
          </div>
          
          <div className="stat-card">
            <div className="stat-card__icon">🌟</div>
            <div className="stat-card__content">
              <div className="stat-card__value">{stats.availableThemes}</div>
              <div className="stat-card__label">解锁主题</div>
            </div>
          </div>
        </div>
      </div>

      {/* 导航标签 */}
      <div className="dashboard__nav">
        <div className="nav__tabs">
          {tabs.map(tab => (
            <button
              key={tab.id}
              className={`nav__tab ${activeTab === tab.id ? 'nav__tab--active' : ''}`}
              onClick={() => setActiveTab(tab.id)}
            >
              <span className="tab__icon">{tab.icon}</span>
              <span className="tab__label">{tab.label}</span>
            </button>
          ))}
        </div>
        
        <div className="nav__description">
          {tabs.find(tab => tab.id === activeTab)?.description}
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="dashboard__content">
        {activeTab === 'overview' && (
          <div className="overview-panel">
            {/* 当前主题信息 */}
            <div className="overview__section">
              <h2 className="section__title">当前主题</h2>
              <div className="current-theme">
                {currentThemeInfo ? (
                  <div className="theme-info">
                    <div 
                      className="theme-preview"
                      style={{
                        backgroundImage: `url(${currentThemeInfo.visual.backgroundTexture})`,
                        borderColor: currentThemeInfo.visual.colorPalette?.primary
                      }}
                    >
                      <div className="theme-overlay">
                        <h3>{currentThemeInfo.name}</h3>
                        <p>{currentThemeInfo.description}</p>
                      </div>
                    </div>
                    
                    {currentThemeInfo.themeEffects && currentThemeInfo.themeEffects.length > 0 && (
                      <div className="theme-effects">
                        <h4>主题效果</h4>
                        <ul>
                          {currentThemeInfo.themeEffects.slice(0, 3).map((effect, index) => (
                            <li key={index}>{effect.description}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                ) : (
                  <p>未找到当前主题信息</p>
                )}
              </div>
            </div>

            {/* 推荐区域 */}
            <div className="overview__section">
              <h2 className="section__title">推荐内容</h2>
              
              {recommendations.themes.length > 0 && (
                <div className="recommendations">
                  <h3>推荐主题</h3>
                  <div className="recommendation-grid">
                    {recommendations.themes.map(rec => (
                      <div key={rec.themeId} className="recommendation-card">
                        <div className="rec-card__header">
                          <h4>{rec.theme.name}</h4>
                          <span className="compatibility-score">
                            匹配度: {rec.compatibilityScore}
                          </span>
                        </div>
                        <p className="rec-card__reason">{rec.reason}</p>
                        <button 
                          className="btn btn--small btn--primary"
                          onClick={() => setActiveTab('themes')}
                        >
                          查看详情
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* 快速操作 */}
            <div className="overview__section">
              <h2 className="section__title">快速操作</h2>
              <div className="quick-actions">
                <button 
                  className="quick-action"
                  onClick={() => setActiveTab('shop')}
                >
                  <div className="action__icon">🛒</div>
                  <div className="action__content">
                    <div className="action__title">购买装饰</div>
                    <div className="action__description">浏览和购买新的装饰道具</div>
                  </div>
                </button>
                
                <button 
                  className="quick-action"
                  onClick={handleDecorationModeToggle}
                >
                  <div className="action__icon">🎨</div>
                  <div className="action__content">
                    <div className="action__title">装饰农场</div>
                    <div className="action__description">放置和调整装饰道具</div>
                  </div>
                </button>
                
                <button 
                  className="quick-action"
                  onClick={() => setActiveTab('themes')}
                >
                  <div className="action__icon">🌟</div>
                  <div className="action__content">
                    <div className="action__title">更换主题</div>
                    <div className="action__description">探索不同的农场主题</div>
                  </div>
                </button>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'shop' && (
          <DecorationShop
            availableDecorations={[]} // 这里需要从系统获取
            ownedDecorations={decorationSystem.getOwnedDecorations()}
            availableThemes={[]} // 这里需要从系统获取
            unlockedThemes={unlockedThemes}
            playerLevel={farmData.level}
            focusTokens={farmData.focusTokens}
            achievements={farmData.achievements}
            onPurchaseDecoration={handleDecorationPurchase}
            onPurchaseTheme={handleThemePurchase}
          />
        )}

        {activeTab === 'decorations' && (
          <DecorationMode
            ownedDecorations={decorationSystem.getOwnedDecorations()}
            placedDecorations={decorationSystem.getPlacedDecorations()}
            farmLayout={farmData.farmLayout}
            currentTheme={currentTheme}
            onPlaceDecoration={(decorationId, x, y, variant) => {
              const result = decorationSystem.placeDecoration(decorationId, x, y, variant)
              if (result.success) {
                updateStats()
              }
              return result
            }}
            onRemoveDecoration={(instanceId) => {
              const result = decorationSystem.removeDecoration(instanceId)
              if (result.success) {
                updateStats()
              }
              return result
            }}
            onMoveDecoration={(instanceId, x, y) => {
              const result = decorationSystem.moveDecoration(instanceId, x, y)
              return result
            }}
            onExit={() => setIsDecorationMode(false)}
          />
        )}

        {activeTab === 'themes' && (
          <ThemeManager
            currentTheme={currentTheme}
            unlockedThemes={unlockedThemes}
            playerLevel={farmData.level}
            focusTokens={farmData.focusTokens}
            achievements={farmData.achievements}
            onThemeChange={handleThemeChange}
            onThemePurchase={handleThemePurchase}
          />
        )}
      </div>
    </div>
  )
}

export default DecorationDashboard 