import React from 'react'
import App from '../App'
import { GameAudioIntegration } from './GameAudioIntegration'
import { MusicPlayerControl } from './MusicPlayerControl'

/**
 * 带有音频集成的App包装组件
 * 将音频系统无缝集成到整个应用中
 */
export const AppWithAudio: React.FC = () => {
  return (
    <GameAudioIntegration>
      <div style={{ position: 'relative', width: '100%', height: '100%' }}>
        {/* 主要应用内容 */}
        <App />
        
        {/* 音乐播放控制面板 - 最小化模式 */}
        <div style={{ 
          position: 'fixed', 
          top: '20px', 
          left: '20px', 
          zIndex: 1000 
        }}>
          <MusicPlayerControl showMinimal={true} />
        </div>
      </div>
    </GameAudioIntegration>
  )
}

export default AppWithAudio 