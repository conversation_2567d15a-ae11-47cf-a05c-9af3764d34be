import React from 'react'
import { GameItem, ItemCategory, QUALITY_CONFIGS } from '../types/enhanced-items'
import Tooltip from './Tooltip'

interface ItemCardProps {
  item: GameItem
  isSelected?: boolean
  showDetails?: boolean
  showTooltip?: boolean
  onClick?: (item: GameItem) => void
  onDoubleClick?: (item: GameItem) => void
  className?: string
}

export const ItemCard: React.FC<ItemCardProps> = ({
  item,
  isSelected = false,
  showDetails = false,
  showTooltip = true,
  onClick,
  onDoubleClick,
  className = ''
}) => {
  const qualityConfig = QUALITY_CONFIGS[item.quality]

  const handleClick = () => {
    onClick?.(item)
  }

  const handleDoubleClick = () => {
    onDoubleClick?.(item)
  }

  const getItemSubtitle = () => {
    if (item.category === ItemCategory.AGRICULTURAL) {
      const agriItem = item as any
      return `${agriItem.futuresCode || 'AGR'} | 产量: ${agriItem.production?.minDaily || 100}-${agriItem.production?.maxDaily || 120}`
    } else if (item.category === ItemCategory.INDUSTRIAL) {
      const indItem = item as any
      return `${indItem.futuresCode || 'IND'} | ${indItem.industrialType || 'industrial'} | 效率: ${indItem.properties?.efficiency || 100}%`
    } else if (item.category === ItemCategory.EQUIPMENT) {
      const equipItem = item as any
      return `${equipItem.slot || 'equipment'} | 效果: +${equipItem.attributes?.focusBonus || 0}%`
    }
    return ''
  }

  const getAttributesDisplay = () => {
    if (!showDetails) return null

    if (item.category === ItemCategory.AGRICULTURAL) {
      const agriItem = item as any
      const production = agriItem.production || { minDaily: 100, maxDaily: 120, currentRate: 1.0 }
      return (
        <div className="text-xs text-gray-600 space-y-1">
          <div className="flex justify-between">
            <span>产量倍率:</span>
            <span className="font-bold text-green-600">{(production.currentRate * 100).toFixed(1)}%</span>
          </div>
          {agriItem.futuresPrice && (
            <div className="flex justify-between">
              <span>期货价格:</span>
              <span className="font-bold">¥{agriItem.futuresPrice.toLocaleString()}</span>
            </div>
          )}
        </div>
      )
    } else if (item.category === ItemCategory.INDUSTRIAL) {
      const indItem = item as any
      const properties = indItem.properties || { durability: 100, efficiency: 100, capacity: 100 }
      return (
        <div className="text-xs text-gray-600 space-y-1">
          <div className="flex justify-between">
            <span>效率:</span>
            <span className="font-bold text-blue-600">{properties.efficiency}%</span>
          </div>
          <div className="flex justify-between">
            <span>耐久:</span>
            <span className="font-bold">{properties.durability}%</span>
          </div>
        </div>
      )
    } else if (item.category === ItemCategory.EQUIPMENT) {
      const equipItem = item as any
      const attributes = equipItem.attributes || { focusBonus: 0, productionBonus: 0, qualityBonus: 0, duration: 24 }
      return (
        <div className="text-xs text-gray-600 space-y-1">
          <div className="flex justify-between">
            <span>专注:</span>
            <span className="font-bold text-purple-600">+{attributes.focusBonus}%</span>
          </div>
          <div className="flex justify-between">
            <span>生产:</span>
            <span className="font-bold text-green-600">+{attributes.productionBonus}%</span>
          </div>
        </div>
      )
    }

    return null
  }

  const cardContent = (
    <div
      className={`
        relative p-3 rounded-lg cursor-pointer transition-all duration-200
        border-2 bg-white shadow-sm hover:shadow-md
        ${isSelected ? 'ring-2 ring-blue-400 transform scale-105' : ''}
        ${className}
      `}
      style={{
        borderColor: qualityConfig.borderColor,
        boxShadow: isSelected 
          ? `0 0 10px ${qualityConfig.glowColor}` 
          : undefined
      }}
      onClick={handleClick}
      onDoubleClick={handleDoubleClick}
    >
      {/* 品质光效 */}
      {isSelected && (
        <div 
          className="absolute inset-0 rounded-lg opacity-20 animate-pulse"
          style={{ backgroundColor: qualityConfig.glowColor }}
        />
      )}

      {/* 品质标识 */}
      <div 
        className="absolute top-1 right-1 px-2 py-1 rounded text-xs font-bold text-white"
        style={{ backgroundColor: qualityConfig.color }}
      >
        {qualityConfig.name}
      </div>

      {/* 道具图标 */}
      <div className="flex items-center justify-center w-12 h-12 mx-auto mb-2 text-2xl bg-gray-50 rounded">
        {item.icon}
      </div>

      {/* 道具名称 */}
      <h3 
        className="font-semibold text-center text-sm mb-1 truncate"
        style={{ color: qualityConfig.color }}
        title={item.name}
      >
        {item.name}
      </h3>

      {/* 道具副标题 */}
      <p className="text-xs text-gray-500 text-center mb-2 truncate" title={getItemSubtitle()}>
        {getItemSubtitle()}
      </p>

      {/* 详细属性 */}
      {getAttributesDisplay()}

      {/* 价值显示 */}
      <div className="text-xs text-gray-400 text-center mt-2">
        价值: {item.baseValue.toLocaleString()}
      </div>

      {/* 获得时间 */}
      {item.obtainedAt && (
        <div className="text-xs text-gray-400 text-center">
          {new Date(item.obtainedAt).toLocaleDateString()}
        </div>
      )}

      {/* 装备状态标识 */}
      {item.category === ItemCategory.EQUIPMENT && (item as any).isEquipped && (
        <div className="absolute top-1 left-1 w-3 h-3 bg-green-500 rounded-full">
          <div className="absolute inset-0 bg-green-400 rounded-full animate-ping opacity-75" />
        </div>
      )}

      {/* 可堆叠标识 */}
      {item.stackable && (
        <div className="absolute bottom-1 left-1 text-xs bg-blue-100 text-blue-600 px-1 rounded">
          可叠
        </div>
      )}

      {/* 可交易标识 */}
      {item.tradeable && (
        <div className="absolute bottom-1 right-1 text-xs bg-green-100 text-green-600 px-1 rounded">
          可交易
        </div>
      )}

      {/* 产量指示器 - 仅农业产品显示 */}
      {item.category === ItemCategory.AGRICULTURAL && (
        <div className="absolute top-8 left-1 text-xs bg-yellow-100 text-yellow-800 px-1 rounded flex items-center">
          📈
        </div>
      )}
    </div>
  )

  // 根据 showTooltip 决定是否包装在 Tooltip 中
  if (showTooltip) {
    return (
      <Tooltip item={item} className="w-full">
        {cardContent}
      </Tooltip>
    )
  }

  return cardContent
}

// 简化版道具卡片 (用于合成材料槽等)
export const SimpleItemCard: React.FC<{
  item?: GameItem
  placeholder?: string
  showTooltip?: boolean
  onClick?: () => void
  onRemove?: () => void
  className?: string
}> = ({ item, placeholder = '拖拽道具到此处', showTooltip = true, onClick, onRemove, className = '' }) => {
  if (!item) {
    return (
      <div 
        className={`
          w-20 h-20 border-2 border-dashed border-gray-300 rounded-lg
          flex items-center justify-center text-xs text-gray-500 text-center
          cursor-pointer hover:border-gray-400 transition-colors
          ${className}
        `}
        onClick={onClick}
      >
        {placeholder}
      </div>
    )
  }

  const qualityConfig = QUALITY_CONFIGS[item.quality]

  const cardContent = (
    <div 
      className={`
        relative w-20 h-20 border-2 rounded-lg bg-white shadow-sm
        cursor-pointer hover:shadow-md transition-all
        ${className}
      `}
      style={{ borderColor: qualityConfig.borderColor }}
      onClick={onClick}
    >
      {/* 移除按钮 */}
      {onRemove && (
        <button
          className="absolute -top-2 -right-2 w-5 h-5 bg-red-500 text-white rounded-full text-xs hover:bg-red-600 z-10"
          onClick={(e) => {
            e.stopPropagation()
            onRemove()
          }}
        >
          ×
        </button>
      )}

      {/* 道具图标 */}
      <div className="flex items-center justify-center w-full h-full text-xl">
        {item.icon}
      </div>

      {/* 品质标识 */}
      <div 
        className="absolute bottom-0 left-0 right-0 text-xs text-white text-center py-1 rounded-b"
        style={{ backgroundColor: qualityConfig.color }}
      >
        {qualityConfig.name}
      </div>

      {/* 产量指示器 - 仅农业产品显示 */}
      {item.category === ItemCategory.AGRICULTURAL && (
        <div className="absolute top-1 left-1 text-xs bg-yellow-100 text-yellow-800 px-1 rounded flex items-center">
          📈
        </div>
      )}
    </div>
  )

  // 根据 showTooltip 决定是否包装在 Tooltip 中
  if (showTooltip) {
    return (
      <Tooltip item={item}>
        {cardContent}
      </Tooltip>
    )
  }

  return cardContent
} 