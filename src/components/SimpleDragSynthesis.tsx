import React, { useState, useCallback, useRef } from 'react'
import { InventoryItem } from '../types/inventory'
import { ItemRarity } from '../types/lootbox'
import { 
  getEnhancedSynthesisRecipes, 
  selectRandomResult, 
  generateResultItemName 
} from '../data/enhancedSynthesisRecipes'

interface SimpleDragSynthesisProps {
  inventoryItems: InventoryItem[]
  onSynthesis: (resultItem: InventoryItem, consumedItems: InventoryItem[]) => void
  onError: (message: string) => void
}

const RARITY_COLORS = {
  [ItemRarity.GRAY]: '#9CA3AF',
  [ItemRarity.GREEN]: '#10B981', 
  [ItemRarity.BLUE]: '#3B82F6',
  [ItemRarity.ORANGE]: '#F59E0B',
  [ItemRarity.GOLD]: '#EAB308',
  [ItemRarity.GOLD_RED]: '#DC2626'
}

export const SimpleDragSynthesis: React.FC<SimpleDragSynthesisProps> = ({
  inventoryItems,
  onSynthesis,
  onError
}) => {
  const [slot1, setSlot1] = useState<InventoryItem | null>(null)
  const [slot2, setSlot2] = useState<InventoryItem | null>(null)
  const [isProcessing, setIsProcessing] = useState(false)
  const [animationClass, setAnimationClass] = useState('')
  const [dragOverSlot, setDragOverSlot] = useState<number | null>(null)
  
  const synthAreaRef = useRef<HTMLDivElement>(null)

  // 获取可用配方
  const getAvailableRecipe = useCallback(() => {
    if (!slot1 || !slot2) return null
    
    const inputItems = [
      { rarity: slot1.rarity, category: slot1.category },
      { rarity: slot2.rarity, category: slot2.category }
    ]
    
    const recipes = getEnhancedSynthesisRecipes(inputItems)
    return recipes[0] || null
  }, [slot1, slot2])

  // 拖拽开始 - 修复版本
  const handleDragStart = (item: InventoryItem, e: React.DragEvent) => {
    console.log('开始拖拽:', item.name)
    
    // 设置拖拽数据
    e.dataTransfer.setData('text/plain', JSON.stringify({
      id: item.id,
      itemId: item.itemId,
      name: item.name,
      icon: item.icon,
      rarity: item.rarity,
      category: item.category,
      type: item.type,
      quantity: 1, // 拖拽时只处理单个物品
      description: item.description,
      obtainedAt: item.obtainedAt
    }))
    
    e.dataTransfer.effectAllowed = 'copy'
    
    // 添加拖拽样式
    const target = e.currentTarget as HTMLElement
    target.style.opacity = '0.5'
    target.style.transform = 'scale(0.95)'
  }

  // 拖拽结束
  const handleDragEnd = (e: React.DragEvent) => {
    const target = e.currentTarget as HTMLElement
    target.style.opacity = '1'
    target.style.transform = 'scale(1)'
    setDragOverSlot(null)
  }

  // 拖拽进入槽位
  const handleDragEnter = (slotNumber: 1 | 2, e: React.DragEvent) => {
    e.preventDefault()
    setDragOverSlot(slotNumber)
    console.log('拖拽进入槽位:', slotNumber)
  }

  // 拖拽离开槽位
  const handleDragLeave = (e: React.DragEvent) => {
    // 检查是否真的离开了槽位区域
    const rect = (e.currentTarget as HTMLElement).getBoundingClientRect()
    const x = e.clientX
    const y = e.clientY
    
    if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {
      setDragOverSlot(null)
    }
  }

  // 拖拽悬停
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    e.dataTransfer.dropEffect = 'copy'
  }

  // 拖拽放置到槽位 - 修复版本
  const handleDrop = (slotNumber: 1 | 2, e: React.DragEvent) => {
    e.preventDefault()
    setDragOverSlot(null)
    
    try {
      const itemDataStr = e.dataTransfer.getData('text/plain')
      console.log('接收到拖拽数据:', itemDataStr)
      
      if (!itemDataStr) {
        onError('拖拽数据为空')
        return
      }
      
      const item: InventoryItem = JSON.parse(itemDataStr)
      console.log('解析后的物品:', item)
      
      if (slotNumber === 1) {
        setSlot1(item)
        console.log('放置到槽位1:', item.name)
      } else {
        setSlot2(item)
        console.log('放置到槽位2:', item.name)
      }
      
      // 添加放置动画
      setAnimationClass('drop-animation')
      setTimeout(() => setAnimationClass(''), 500)
      
    } catch (error) {
      console.error('拖拽数据解析失败:', error)
      onError('拖拽数据解析失败')
    }
  }

  // 清空槽位
  const clearSlot = (slotNumber: 1 | 2) => {
    if (slotNumber === 1) {
      setSlot1(null)
      console.log('清空槽位1')
    } else {
      setSlot2(null)
      console.log('清空槽位2')
    }
  }

  // 执行合成 - 增强动画版本
  const handleSynthesize = async () => {
    const recipe = getAvailableRecipe()
    if (!recipe || !slot1 || !slot2) {
      onError('请放入两个相同品质的农产品！')
      return
    }

    console.log('开始合成:', slot1.name, '+', slot2.name)
    setIsProcessing(true)
    setAnimationClass('synthesis-processing')

    try {
      // 模拟合成过程
      await new Promise(resolve => setTimeout(resolve, 2500))

      // 判断成功率
      const isSuccess = Math.random() < recipe.successRate
      console.log('合成结果:', isSuccess ? '成功' : '失败')
      
      if (isSuccess) {
        // 合成成功
        setAnimationClass('synthesis-success')
        
        const selectedResult = selectRandomResult(recipe)
        const resultName = generateResultItemName(selectedResult, [slot1, slot2])
        
        const resultItem: InventoryItem = {
          id: `synth_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          itemId: `synth_item_${Date.now()}`,
          name: resultName,
          icon: selectedResult.icon,
          rarity: recipe.resultRarity,
          category: selectedResult.category,
          type: selectedResult.type,
          quantity: 1,
          description: `通过合成获得的${recipe.resultRarity}品质物品`,
          obtainedAt: Date.now()
        }

        onSynthesis(resultItem, [slot1, slot2])
        
        // 清空槽位
        setSlot1(null)
        setSlot2(null)
        
      } else {
        // 合成失败
        setAnimationClass('synthesis-failure')
        onError(`合成失败！成功率: ${Math.round(recipe.successRate * 100)}%`)
      }

    } catch (error) {
      setAnimationClass('synthesis-failure')
      onError('合成过程出现错误！')
    } finally {
      setIsProcessing(false)
      setTimeout(() => setAnimationClass(''), 2000)
    }
  }

  const recipe = getAvailableRecipe()

  return (
    <div className="simple-drag-synthesis">
      <div className="synthesis-header text-center mb-6">
        <h2 className="text-2xl font-bold text-gray-800 mb-2">⚗️ 物品合成工作台</h2>
        <p className="text-sm text-gray-600">拖拽两个相同品质的农产品到合成槽中进行合成</p>
        <p className="text-xs text-blue-600 mt-1">💡 提示：鼠标按住物品拖拽到合成槽位中</p>
      </div>

      {/* 物品库存 */}
      <div className="inventory-section mb-6">
        <h3 className="text-lg font-semibold mb-3 text-gray-700">📦 物品库存</h3>
        <div className="inventory-grid grid grid-cols-8 gap-2 max-h-32 overflow-y-auto p-2 bg-gray-50 rounded-lg">
          {(() => {
            const expandedItems: Array<{item: InventoryItem, index: number}> = []
            inventoryItems.forEach((item) => {
              for (let i = 0; i < item.quantity; i++) {
                expandedItems.push({ item, index: i })
              }
            })
            
            return expandedItems.map(({item, index}, globalIndex) => (
              <div
                key={`${item.id}-${index}-${globalIndex}`}
                draggable={true}
                onDragStart={(e) => handleDragStart(item, e)}
                onDragEnd={handleDragEnd}
                className="inventory-item cursor-grab active:cursor-grabbing transform transition-all duration-200 hover:scale-110 select-none"
                style={{
                  background: `linear-gradient(135deg, ${RARITY_COLORS[item.rarity]}15, ${RARITY_COLORS[item.rarity]}25)`,
                  border: `2px solid ${RARITY_COLORS[item.rarity]}`,
                  borderRadius: '8px',
                  aspectRatio: '1',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  fontSize: '20px',
                  minHeight: '50px',
                  boxShadow: `0 2px 8px ${RARITY_COLORS[item.rarity]}40`,
                  position: 'relative',
                  userSelect: 'none'
                }}
                title={`${item.name} (拖拽到合成槽)`}
              >
                {item.icon}
                {/* 品质光环 */}
                {item.rarity !== ItemRarity.GRAY && (
                  <div 
                    className="absolute inset-0 rounded-lg animate-pulse"
                    style={{
                      background: `radial-gradient(circle, ${RARITY_COLORS[item.rarity]}30, transparent)`,
                      pointerEvents: 'none'
                    }}
                  />
                )}
                {/* 拖拽提示 */}
                <div 
                  className="absolute -top-1 -right-1 w-4 h-4 bg-blue-500 text-white rounded-full flex items-center justify-center text-xs font-bold opacity-75"
                  title="可拖拽"
                >
                  ↕
                </div>
              </div>
            ))
          })()}
        </div>
      </div>

      {/* 合成区域 */}
      <div 
        ref={synthAreaRef}
        className={`synthesis-area p-6 bg-gradient-to-br from-blue-50 to-purple-50 rounded-xl border-2 border-dashed border-blue-300 ${animationClass}`}
      >
        {/* 合成槽位 */}
        <div className="synthesis-slots flex justify-center items-center gap-8 mb-6">
          {/* 槽位 1 */}
          <div className="slot-container">
            <div
              className="synthesis-slot"
              onDrop={(e) => handleDrop(1, e)}
              onDragOver={handleDragOver}
              onDragEnter={(e) => handleDragEnter(1, e)}
              onDragLeave={handleDragLeave}
              onClick={() => slot1 && clearSlot(1)}
              style={{
                width: '80px',
                height: '80px',
                border: dragOverSlot === 1 
                  ? '3px solid #3B82F6' 
                  : slot1 
                    ? `3px solid ${RARITY_COLORS[slot1.rarity]}` 
                    : '3px dashed #CBD5E0',
                borderRadius: '12px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                background: dragOverSlot === 1
                  ? 'linear-gradient(135deg, #DBEAFE, #BFDBFE)'
                  : slot1 
                    ? `linear-gradient(135deg, ${RARITY_COLORS[slot1.rarity]}20, ${RARITY_COLORS[slot1.rarity]}30)`
                    : 'rgba(255, 255, 255, 0.8)',
                fontSize: '32px',
                cursor: slot1 ? 'pointer' : 'default',
                transition: 'all 0.3s ease',
                position: 'relative',
                transform: dragOverSlot === 1 ? 'scale(1.05)' : 'scale(1)',
                boxShadow: dragOverSlot === 1 ? '0 0 20px rgba(59, 130, 246, 0.5)' : ''
              }}
              title={slot1 ? `点击移除 ${slot1.name}` : '拖拽物品到此处'}
            >
              {slot1 ? slot1.icon : '⚪'}
              {slot1 && (
                <div className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center text-xs hover:bg-red-600">
                  ×
                </div>
              )}
              {dragOverSlot === 1 && (
                <div className="absolute inset-0 rounded-lg bg-blue-400 opacity-20 animate-pulse" />
              )}
            </div>
            <div className="text-center mt-2 text-sm text-gray-600">槽位 1</div>
          </div>

          {/* 合成箭头 */}
          <div className={`synthesis-arrow text-4xl ${isProcessing ? 'text-yellow-500 animate-bounce' : 'text-blue-500'}`}>
            {isProcessing ? '⚡' : '→'}
          </div>

          {/* 槽位 2 */}
          <div className="slot-container">
            <div
              className="synthesis-slot"
              onDrop={(e) => handleDrop(2, e)}
              onDragOver={handleDragOver}
              onDragEnter={(e) => handleDragEnter(2, e)}
              onDragLeave={handleDragLeave}
              onClick={() => slot2 && clearSlot(2)}
              style={{
                width: '80px',
                height: '80px',
                border: dragOverSlot === 2 
                  ? '3px solid #3B82F6' 
                  : slot2 
                    ? `3px solid ${RARITY_COLORS[slot2.rarity]}` 
                    : '3px dashed #CBD5E0',
                borderRadius: '12px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                background: dragOverSlot === 2
                  ? 'linear-gradient(135deg, #DBEAFE, #BFDBFE)'
                  : slot2 
                    ? `linear-gradient(135deg, ${RARITY_COLORS[slot2.rarity]}20, ${RARITY_COLORS[slot2.rarity]}30)`
                    : 'rgba(255, 255, 255, 0.8)',
                fontSize: '32px',
                cursor: slot2 ? 'pointer' : 'default',
                transition: 'all 0.3s ease',
                position: 'relative',
                transform: dragOverSlot === 2 ? 'scale(1.05)' : 'scale(1)',
                boxShadow: dragOverSlot === 2 ? '0 0 20px rgba(59, 130, 246, 0.5)' : ''
              }}
              title={slot2 ? `点击移除 ${slot2.name}` : '拖拽物品到此处'}
            >
              {slot2 ? slot2.icon : '⚪'}
              {slot2 && (
                <div className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center text-xs hover:bg-red-600">
                  ×
                </div>
              )}
              {dragOverSlot === 2 && (
                <div className="absolute inset-0 rounded-lg bg-blue-400 opacity-20 animate-pulse" />
              )}
            </div>
            <div className="text-center mt-2 text-sm text-gray-600">槽位 2</div>
          </div>

          {/* 结果箭头 */}
          <div className={`synthesis-arrow text-4xl ${isProcessing ? 'text-yellow-500 animate-bounce' : 'text-green-500'}`}>
            →
          </div>

          {/* 结果预览 */}
          <div className="result-container">
            <div
              className={`result-slot ${isProcessing ? 'animate-pulse' : ''}`}
              style={{
                width: '80px',
                height: '80px',
                border: recipe ? `3px solid ${RARITY_COLORS[recipe.resultRarity]}` : '3px dashed #CBD5E0',
                borderRadius: '12px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                background: recipe 
                  ? `linear-gradient(135deg, ${RARITY_COLORS[recipe.resultRarity]}20, ${RARITY_COLORS[recipe.resultRarity]}30)`
                  : 'rgba(255, 255, 255, 0.8)',
                fontSize: '32px',
                transition: 'all 0.3s ease'
              }}
            >
              {isProcessing ? '🔥' : recipe ? '❓' : '⚪'}
            </div>
            <div className="text-center mt-2 text-sm text-gray-600">合成结果</div>
          </div>
        </div>

        {/* 配方信息 */}
        {recipe && (
          <div className="recipe-info bg-white p-4 rounded-lg mb-4 border border-gray-200 shadow-sm">
            <div className="flex justify-between items-center">
              <div>
                <h4 className="font-semibold text-gray-800">{recipe.name}</h4>
                <p className="text-sm text-gray-600">{recipe.description}</p>
              </div>
              <div className="text-right">
                <div className="text-lg font-bold" style={{ color: RARITY_COLORS[recipe.resultRarity] }}>
                  成功率: {Math.round(recipe.successRate * 100)}%
                </div>
                <div className="text-sm text-gray-500">
                  品质: {recipe.resultRarity}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 合成按钮 */}
        <div className="text-center">
          <button
            onClick={handleSynthesize}
            disabled={!recipe || isProcessing}
            className={`px-8 py-3 rounded-xl font-bold text-white transition-all duration-300 ${
              recipe && !isProcessing
                ? 'bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 transform hover:scale-105 shadow-lg hover:shadow-xl'
                : 'bg-gray-400 cursor-not-allowed'
            }`}
          >
            {isProcessing ? '🔥 合成中...' : '⚡ 开始合成'}
          </button>
        </div>
      </div>

      {/* CSS 样式 - 增强版本 */}
      <style>{`
        .simple-drag-synthesis {
          max-width: 800px;
          margin: 0 auto;
          padding: 20px;
        }

        .drop-animation {
          animation: drop-glow 0.5s ease-in-out;
        }

        .synthesis-processing {
          animation: synthesis-process 2s ease-in-out infinite;
        }

        .synthesis-success {
          animation: synthesis-success 2s ease-in-out;
        }

        .synthesis-failure {
          animation: synthesis-failure 1s ease-in-out;
        }

        @keyframes drop-glow {
          0% { 
            transform: scale(1); 
            background: linear-gradient(45deg, #DBEAFE, #EDE9FE);
          }
          50% { 
            transform: scale(1.05); 
            background: linear-gradient(45deg, #BFDBFE, #DDD6FE);
            box-shadow: 0 0 30px rgba(59, 130, 246, 0.6); 
          }
          100% { 
            transform: scale(1); 
            background: linear-gradient(45deg, #DBEAFE, #EDE9FE);
          }
        }

        @keyframes synthesis-process {
          0%, 100% { 
            transform: scale(1); 
            background: linear-gradient(45deg, #DBEAFE, #EDE9FE);
            box-shadow: 0 0 10px rgba(59, 130, 246, 0.3);
          }
          25% { 
            transform: scale(1.02); 
            background: linear-gradient(45deg, #BFDBFE, #DDD6FE);
            box-shadow: 0 0 20px rgba(147, 51, 234, 0.4);
          }
          50% { 
            transform: scale(1.04); 
            background: linear-gradient(45deg, #A78BFA, #C084FC);
            box-shadow: 0 0 30px rgba(147, 51, 234, 0.5);
          }
          75% { 
            transform: scale(1.02); 
            background: linear-gradient(45deg, #BFDBFE, #DDD6FE);
            box-shadow: 0 0 20px rgba(147, 51, 234, 0.4);
          }
        }

        @keyframes synthesis-success {
          0% { 
            transform: scale(1); 
            background: linear-gradient(45deg, #DBEAFE, #EDE9FE);
          }
          25% { 
            transform: scale(1.08); 
            background: linear-gradient(45deg, #34D399, #FBBF24);
            box-shadow: 0 0 40px rgba(52, 211, 153, 0.8);
          }
          50% { 
            transform: scale(1.12); 
            background: linear-gradient(45deg, #10B981, #F59E0B);
            box-shadow: 0 0 60px rgba(16, 185, 129, 1);
          }
          75% { 
            transform: scale(1.08); 
            background: linear-gradient(45deg, #34D399, #FBBF24);
            box-shadow: 0 0 40px rgba(52, 211, 153, 0.8);
          }
          100% { 
            transform: scale(1); 
            background: linear-gradient(45deg, #D1FAE5, #FEF3C7);
            box-shadow: 0 0 20px rgba(52, 211, 153, 0.4);
          }
        }

        @keyframes synthesis-failure {
          0% { 
            transform: translateX(0) scale(1); 
            background: linear-gradient(45deg, #DBEAFE, #EDE9FE);
          }
          15% { 
            transform: translateX(-15px) scale(1.02); 
            background: linear-gradient(45deg, #FCA5A5, #FED7D7);
            box-shadow: 0 0 20px rgba(248, 113, 113, 0.6);
          }
          30% { 
            transform: translateX(15px) scale(1.02); 
            background: linear-gradient(45deg, #F87171, #FCA5A5);
            box-shadow: 0 0 30px rgba(248, 113, 113, 0.8);
          }
          45% { 
            transform: translateX(-10px) scale(1.01); 
            background: linear-gradient(45deg, #FCA5A5, #FED7D7);
            box-shadow: 0 0 20px rgba(248, 113, 113, 0.6);
          }
          60% { 
            transform: translateX(10px) scale(1.01); 
            background: linear-gradient(45deg, #F87171, #FCA5A5);
            box-shadow: 0 0 30px rgba(248, 113, 113, 0.8);
          }
          75% { 
            transform: translateX(-5px) scale(1.005); 
            background: linear-gradient(45deg, #FCA5A5, #FED7D7);
            box-shadow: 0 0 15px rgba(248, 113, 113, 0.4);
          }
          90% { 
            transform: translateX(5px) scale(1.005); 
            background: linear-gradient(45deg, #F87171, #FCA5A5);
            box-shadow: 0 0 15px rgba(248, 113, 113, 0.4);
          }
          100% { 
            transform: translateX(0) scale(1); 
            background: linear-gradient(45deg, #DBEAFE, #EDE9FE);
          }
        }

        .inventory-item:hover {
          transform: scale(1.15) !important;
          z-index: 10;
          position: relative;
        }

        .inventory-item:active {
          transform: scale(0.95) !important;
        }

        .synthesis-slot:hover {
          border-color: #60A5FA !important;
          box-shadow: 0 0 15px rgba(96, 165, 250, 0.4);
        }

        button:hover:not(:disabled) {
          box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
        }

        button:active:not(:disabled) {
          transform: scale(0.98);
        }

        /* 拖拽状态的全局样式 */
        .inventory-item[draggable="true"] {
          -webkit-user-drag: element;
          -webkit-user-select: none;
          -moz-user-select: none;
          -ms-user-select: none;
          user-select: none;
        }

        /* 添加发光效果 */
        .inventory-item:hover::before {
          content: '';
          position: absolute;
          inset: -2px;
          border-radius: 10px;
          background: linear-gradient(45deg, transparent, rgba(59, 130, 246, 0.3), transparent);
          z-index: -1;
        }
      `}</style>
    </div>
  )
}

export default SimpleDragSynthesis 