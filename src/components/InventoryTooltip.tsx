import React, { useState, useRef, useEffect } from 'react'
import { InventoryItem, RARITY_NAMES } from '../types/inventory'
import { RARITY_COLORS } from '../types/lootbox'

interface InventoryTooltipProps {
  item: InventoryItem
  children: React.ReactNode
  className?: string
}

export const InventoryTooltip: React.FC<InventoryTooltipProps> = ({ item, children, className = '' }) => {
  const [isVisible, setIsVisible] = useState(false)
  const [position, setPosition] = useState({ x: 0, y: 0 })
  const tooltipRef = useRef<HTMLDivElement>(null)
  const triggerRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const updatePosition = (e: MouseEvent) => {
      if (isVisible && triggerRef.current) {
        const tooltipWidth = tooltipRef.current?.offsetWidth || 300
        const tooltipHeight = tooltipRef.current?.offsetHeight || 200
        
        let x = e.clientX + 10
        let y = e.clientY + 10
        
        // 防止工具提示超出视窗
        if (x + tooltipWidth > window.innerWidth) {
          x = e.clientX - tooltipWidth - 10
        }
        if (y + tooltipHeight > window.innerHeight) {
          y = e.clientY - tooltipHeight - 10
        }
        
        setPosition({ x, y })
      }
    }

    if (isVisible) {
      document.addEventListener('mousemove', updatePosition)
      return () => document.removeEventListener('mousemove', updatePosition)
    }
  }, [isVisible])

  const handleMouseEnter = () => {
    setIsVisible(true)
  }

  const handleMouseLeave = () => {
    setIsVisible(false)
  }

  const borderColor = RARITY_COLORS[item.rarity] || '#ccc'
  const glowColor = RARITY_COLORS[item.rarity] || '#999'

  return (
    <div 
      ref={triggerRef}
      className={`relative ${className}`}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {children}
      
      {isVisible && (
        <div
          ref={tooltipRef}
          className="tooltip-container"
          style={{
            position: 'fixed',
            left: position.x,
            top: position.y,
            zIndex: 99999,
            pointerEvents: 'none'
          }}
        >
          <div className="tooltip-content">
            {/* 头部信息 */}
            <div className="tooltip-header">
              <div className="flex items-center gap-3">
                <span className="text-3xl">{item.icon}</span>
                <div>
                  <h3 className="item-name" style={{ color: borderColor }}>
                    {item.name}
                  </h3>
                  <p className="item-quality" style={{ backgroundColor: borderColor }}>
                    {RARITY_NAMES[item.rarity]}
                  </p>
                </div>
              </div>
            </div>

            {/* 基础信息 */}
            <div className="tooltip-section">
              <h4>基础信息</h4>
              <div className="info-grid">
                <div className="info-item">
                  <span className="label">类型:</span>
                  <span className="value">
                    {item.category === 'agricultural' ? '农业产品' : 
                     item.category === 'industrial' ? '工业产品' : 
                     '装备道具'}
                  </span>
                </div>
                <div className="info-item">
                  <span className="label">数量:</span>
                  <span className="value">{item.quantity}</span>
                </div>
                <div className="info-item">
                  <span className="label">获得时间:</span>
                  <span className="value">{new Date(item.obtainedAt).toLocaleDateString()}</span>
                </div>
              </div>
            </div>

            {/* 描述 */}
            <div className="tooltip-section">
              <h4>描述</h4>
              <p className="description">{item.description}</p>
            </div>

            {/* 特殊属性显示 */}
            {item.category === 'agricultural' && (
              <div className="tooltip-section">
                <h4>农业属性</h4>
                <div className="production-info">
                  <div className="info-item highlight">
                    <span className="label">📈 预估产量:</span>
                    <span className="value production-range">
                      根据品质自动调整
                    </span>
                  </div>
                  <div className="info-item">
                    <span className="label">🎯 品质等级:</span>
                    <span className="value">{RARITY_NAMES[item.rarity]}</span>
                  </div>
                </div>
              </div>
            )}

            {item.category === 'industrial' && (
              <div className="tooltip-section">
                <h4>工业属性</h4>
                <div className="industrial-info">
                  <div className="info-item">
                    <span className="label">🔧 工业品质:</span>
                    <span className="value">{RARITY_NAMES[item.rarity]}</span>
                  </div>
                  <div className="info-item">
                    <span className="label">💪 预估效率:</span>
                    <span className="value">根据品质自动调整</span>
                  </div>
                </div>
              </div>
            )}
          </div>

          <style>{`
            .tooltip-container {
              filter: drop-shadow(0 8px 16px rgba(0, 0, 0, 0.3));
            }

            .tooltip-content {
              background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
              border: 2px solid ${borderColor};
              border-radius: 12px;
              padding: 16px;
              min-width: 280px;
              max-width: 350px;
              font-size: 14px;
              box-shadow: 0 0 20px ${glowColor}40;
              animation: tooltipFadeIn 0.2s ease-out;
            }

            @keyframes tooltipFadeIn {
              from {
                opacity: 0;
                transform: scale(0.9) translateY(10px);
              }
              to {
                opacity: 1;
                transform: scale(1) translateY(0);
              }
            }

            .tooltip-header {
              margin-bottom: 12px;
              padding-bottom: 12px;
              border-bottom: 2px solid ${borderColor}30;
            }

            .item-name {
              font-size: 16px;
              font-weight: bold;
              margin: 0;
            }

            .item-quality {
              display: inline-block;
              color: white;
              padding: 2px 8px;
              border-radius: 12px;
              font-size: 12px;
              font-weight: bold;
              margin-top: 4px;
            }

            .tooltip-section {
              margin-bottom: 12px;
            }

            .tooltip-section h4 {
              font-size: 14px;
              font-weight: bold;
              color: #333;
              margin: 0 0 8px 0;
              border-bottom: 1px solid #eee;
              padding-bottom: 4px;
            }

            .info-grid {
              display: grid;
              gap: 6px;
            }

            .info-item {
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding: 4px 0;
            }

            .info-item.highlight {
              background: linear-gradient(90deg, ${borderColor}10, ${borderColor}20);
              padding: 6px 8px;
              border-radius: 6px;
              border-left: 3px solid ${borderColor};
            }

            .label {
              color: #666;
              font-weight: 500;
            }

            .value {
              color: #333;
              font-weight: bold;
            }

            .production-range {
              color: ${borderColor};
              background: ${borderColor}15;
              padding: 2px 6px;
              border-radius: 4px;
            }

            .description {
              color: #555;
              font-style: italic;
              line-height: 1.4;
              margin: 0;
            }

            .production-info,
            .industrial-info {
              display: grid;
              gap: 6px;
            }
          `}</style>
        </div>
      )}
    </div>
  )
}

export default InventoryTooltip 