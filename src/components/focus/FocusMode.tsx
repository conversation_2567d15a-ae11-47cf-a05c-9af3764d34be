import React, { useState } from 'react'

interface FocusModeProps {
  className?: string
}

export const FocusMode: React.FC<FocusModeProps> = ({ className = '' }) => {
  const [isActive, setIsActive] = useState(false)
  const [focusTime, setFocusTime] = useState(0)

  React.useEffect(() => {
    let interval: NodeJS.Timeout | null = null
    
    if (isActive) {
      interval = setInterval(() => {
        setFocusTime(prev => prev + 1)
      }, 1000)
    }

    return () => {
      if (interval) clearInterval(interval)
    }
  }, [isActive])

  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = seconds % 60
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`
  }

  const toggleFocus = () => {
    if (isActive) {
      setIsActive(false)
    } else {
      setFocusTime(0)
      setIsActive(true)
    }
  }

  return (
    <div className={`focus-mode ${className}`} style={{
      position: 'fixed',
      top: '60px',
      right: '10px',
      zIndex: 9998,
      background: isActive ? 'rgba(76, 175, 80, 0.9)' : 'rgba(96, 125, 139, 0.9)',
      color: 'white',
      padding: '12px',
      borderRadius: '8px',
      fontSize: '14px',
      minWidth: '150px',
      textAlign: 'center',
      boxShadow: '0 2px 8px rgba(0, 0, 0, 0.2)'
    }}>
      <div style={{ marginBottom: '8px', fontWeight: 'bold' }}>
        🎯 专注模式
      </div>
      
      <div style={{ 
        fontSize: '18px', 
        fontFamily: 'monospace',
        marginBottom: '8px'
      }}>
        {formatTime(focusTime)}
      </div>
      
      <button
        onClick={toggleFocus}
        style={{
          background: isActive ? '#f44336' : '#4CAF50',
          color: 'white',
          border: 'none',
          borderRadius: '4px',
          padding: '6px 12px',
          fontSize: '12px',
          cursor: 'pointer',
          width: '100%'
        }}
      >
        {isActive ? '⏹ 结束专注' : '▶ 开始专注'}
      </button>
      
      {isActive && (
        <div style={{ 
          marginTop: '8px', 
          fontSize: '10px', 
          opacity: 0.8 
        }}>
          保持专注，勿要分心
        </div>
      )}
    </div>
  )
}

export default FocusMode 