/* ============ 装饰模式主容器 ============ */
.decoration-mode {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  z-index: 2000;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* ============ 工具栏 ============ */
.decoration-toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px 25px;
  background: linear-gradient(135deg, #2d3748, #4a5568);
  color: white;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  z-index: 1001;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.toolbar-center {
  flex: 1;
  text-align: center;
}

.toolbar-center h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #e2e8f0;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 15px;
}

.grid-toggle {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 10px 12px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 16px;
  transition: all 0.3s ease;
}

.grid-toggle:hover {
  background: rgba(255, 255, 255, 0.2);
}

.grid-toggle.active {
  background: rgba(74, 222, 128, 0.3);
  border-color: #4ade80;
  color: #4ade80;
}

.beauty-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.1);
  padding: 10px 16px;
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.beauty-icon {
  font-size: 18px;
}

.beauty-value {
  font-size: 18px;
  font-weight: 700;
  color: #fbbf24;
}

.beauty-label {
  font-size: 14px;
  color: #cbd5e1;
}

.selected-actions {
  display: flex;
  gap: 10px;
}

.action-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 10px 12px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 16px;
  transition: all 0.3s ease;
}

.action-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.05);
}

.rotate-btn:hover {
  background: rgba(59, 130, 246, 0.3);
  border-color: #3b82f6;
}

.delete-btn:hover {
  background: rgba(239, 68, 68, 0.3);
  border-color: #ef4444;
}

.exit-btn {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  border: none;
  color: white;
  padding: 12px 20px;
  border-radius: 10px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.exit-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(239, 68, 68, 0.3);
}

/* ============ 装饰道具面板 ============ */
.decoration-panel {
  position: fixed;
  top: 80px;
  left: 20px;
  width: 280px;
  max-height: calc(100vh - 120px);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.decoration-panel h4 {
  margin: 0;
  padding: 20px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  font-size: 16px;
  font-weight: 600;
  text-align: center;
}

.owned-decorations {
  flex: 1;
  padding: 15px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.no-decorations {
  text-align: center;
  color: #64748b;
  font-size: 14px;
  padding: 40px 20px;
  margin: 0;
}

.decoration-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  cursor: grab;
  transition: all 0.3s ease;
  user-select: none;
}

.decoration-item:hover {
  border-color: #667eea;
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(102, 126, 234, 0.15);
}

.decoration-item:active {
  cursor: grabbing;
  transform: scale(0.98);
}

.decoration-item img {
  width: 48px;
  height: 48px;
  object-fit: cover;
  border-radius: 8px;
  background: #f1f5f9;
}

.item-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.item-name {
  font-size: 14px;
  font-weight: 600;
  color: #1e293b;
}

.item-quantity {
  font-size: 12px;
  color: #64748b;
}

/* ============ 农场区域 ============ */
.decoration-farm {
  position: relative;
  background: linear-gradient(135deg, #10b981, #059669);
  margin: 20px auto;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  cursor: crosshair;
  overflow: hidden;
}

.decoration-grid {
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: none;
  z-index: 1;
}

/* ============ 已放置的装饰道具 ============ */
.placed-decoration {
  position: absolute;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 100;
  border-radius: 8px;
  overflow: hidden;
}

.placed-decoration:hover {
  transform: scale(1.05);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

.placed-decoration.selected {
  z-index: 200;
}

.placed-decoration.selected .decoration-selection-border {
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  border: 3px solid #3b82f6;
  border-radius: 12px;
  background: rgba(59, 130, 246, 0.1);
  pointer-events: none;
  animation: pulse-selection 2s infinite;
}

@keyframes pulse-selection {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.02);
  }
}

.placed-decoration.dragging {
  z-index: 300;
  opacity: 0.8;
  transform: scale(1.1);
  box-shadow: 0 16px 32px rgba(0, 0, 0, 0.3);
}

.placed-decoration img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
}

/* ============ 拖拽预览 ============ */
.drag-preview {
  position: absolute;
  pointer-events: none;
  z-index: 400;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.1s ease;
}

.drag-preview.can-place {
  border: 3px solid #10b981;
  background: rgba(16, 185, 129, 0.2);
  animation: glow-green 1s infinite alternate;
}

.drag-preview.cannot-place {
  border: 3px solid #ef4444;
  background: rgba(239, 68, 68, 0.2);
  animation: glow-red 1s infinite alternate;
}

@keyframes glow-green {
  from {
    box-shadow: 0 0 10px rgba(16, 185, 129, 0.5);
  }
  to {
    box-shadow: 0 0 20px rgba(16, 185, 129, 0.8);
  }
}

@keyframes glow-red {
  from {
    box-shadow: 0 0 10px rgba(239, 68, 68, 0.5);
  }
  to {
    box-shadow: 0 0 20px rgba(239, 68, 68, 0.8);
  }
}

.drag-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  opacity: 0.7;
  border-radius: 8px;
}

/* ============ 装饰信息面板 ============ */
.decoration-info-panel {
  position: fixed;
  top: 80px;
  right: 20px;
  width: 300px;
  max-height: calc(100vh - 120px);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  overflow: hidden;
  padding: 20px;
}

.decoration-info-panel h4 {
  margin: 0 0 12px 0;
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
}

.decoration-info-panel p {
  margin: 0 0 16px 0;
  font-size: 14px;
  color: #64748b;
  line-height: 1.5;
}

.decoration-stats {
  margin-bottom: 16px;
  padding: 12px;
  background: #f8fafc;
  border-radius: 8px;
}

.decoration-stats > div {
  margin-bottom: 8px;
  font-size: 14px;
  color: #475569;
}

.decoration-stats > div:last-child {
  margin-bottom: 0;
}

.decoration-effects {
  margin-top: 16px;
}

.decoration-effects h5 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: #667eea;
}

.decoration-effects .effect {
  margin-bottom: 6px;
  font-size: 13px;
  color: #64748b;
  padding-left: 12px;
  position: relative;
}

.decoration-effects .effect::before {
  content: '•';
  color: #667eea;
  position: absolute;
  left: 0;
}

/* ============ 滚动条样式 ============ */
.owned-decorations::-webkit-scrollbar,
.decoration-info-panel::-webkit-scrollbar {
  width: 6px;
}

.owned-decorations::-webkit-scrollbar-track,
.decoration-info-panel::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.owned-decorations::-webkit-scrollbar-thumb,
.decoration-info-panel::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.owned-decorations::-webkit-scrollbar-thumb:hover,
.decoration-info-panel::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* ============ 响应式设计 ============ */
@media (max-width: 1024px) {
  .decoration-panel {
    width: 240px;
  }
  
  .decoration-info-panel {
    width: 260px;
  }
  
  .decoration-toolbar {
    padding: 12px 20px;
  }
  
  .toolbar-center h3 {
    font-size: 18px;
  }
}

@media (max-width: 768px) {
  .decoration-mode {
    flex-direction: column;
  }
  
  .decoration-toolbar {
    padding: 10px 15px;
    flex-wrap: wrap;
    gap: 10px;
  }
  
  .toolbar-left {
    order: 2;
    gap: 10px;
  }
  
  .toolbar-center {
    order: 1;
    flex: none;
    width: 100%;
    text-align: center;
  }
  
  .toolbar-right {
    order: 3;
    gap: 10px;
  }
  
  .decoration-panel {
    position: relative;
    top: 0;
    left: 0;
    width: 100%;
    max-height: 200px;
    border-radius: 0;
    margin-bottom: 10px;
  }
  
  .owned-decorations {
    flex-direction: row;
    overflow-x: auto;
    overflow-y: hidden;
    padding: 15px;
  }
  
  .decoration-item {
    min-width: 120px;
    flex-direction: column;
    text-align: center;
    gap: 8px;
  }
  
  .decoration-farm {
    margin: 10px;
    border-radius: 12px;
  }
  
  .decoration-info-panel {
    position: relative;
    top: 0;
    right: 0;
    width: 100%;
    max-height: none;
    border-radius: 0;
    margin-top: 10px;
  }
  
  .beauty-indicator {
    flex-direction: column;
    gap: 4px;
    padding: 8px 12px;
  }
  
  .beauty-value {
    font-size: 16px;
  }
  
  .beauty-label {
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .decoration-toolbar {
    padding: 8px 10px;
  }
  
  .toolbar-center h3 {
    font-size: 16px;
  }
  
  .decoration-panel h4 {
    padding: 15px;
    font-size: 14px;
  }
  
  .decoration-item {
    min-width: 100px;
    padding: 10px;
  }
  
  .decoration-item img {
    width: 40px;
    height: 40px;
  }
  
  .item-name {
    font-size: 12px;
  }
  
  .item-quantity {
    font-size: 11px;
  }
  
  .action-btn,
  .grid-toggle {
    padding: 8px 10px;
    font-size: 14px;
  }
  
  .exit-btn {
    padding: 10px 16px;
    font-size: 12px;
  }
}

/* ============ 动画和过渡效果 ============ */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.decoration-mode {
  animation: fade-in 0.3s ease-out;
}

.decoration-panel,
.decoration-info-panel {
  animation: fade-in 0.4s ease-out;
}

/* ============ 可访问性 ============ */
.decoration-item:focus,
.placed-decoration:focus,
.action-btn:focus,
.grid-toggle:focus,
.exit-btn:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.decoration-item[aria-selected="true"] {
  border-color: #3b82f6;
  background: rgba(59, 130, 246, 0.1);
}

/* ============ 高对比度模式支持 ============ */
@media (prefers-contrast: high) {
  .decoration-panel,
  .decoration-info-panel {
    background: white;
    border: 2px solid #000;
  }
  
  .decoration-item {
    border-color: #000;
  }
  
  .placed-decoration.selected .decoration-selection-border {
    border-color: #000;
    border-width: 4px;
  }
}

/* ============ 减少动画偏好 ============ */
@media (prefers-reduced-motion: reduce) {
  .decoration-mode,
  .decoration-panel,
  .decoration-info-panel,
  .placed-decoration,
  .decoration-item,
  .action-btn,
  .grid-toggle,
  .exit-btn {
    animation: none;
    transition: none;
  }
  
  .decoration-selection-border {
    animation: none;
  }
  
  .drag-preview.can-place,
  .drag-preview.cannot-place {
    animation: none;
  }
} 