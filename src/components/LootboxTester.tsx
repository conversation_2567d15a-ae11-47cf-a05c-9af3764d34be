import React, { useState, useEffect } from 'react'
import { LootboxType, LootboxResult, ItemRarity, RARITY_COLORS } from '../types/lootbox'
import { CurrencyType } from '../types/currency'
import { LootboxGenerator, RARITY_NAMES, CURRENCY_NAMES } from '../utils/lootboxGenerator'
import { LOOTBOX_CONFIGS } from '../data/lootboxConfigs'
import { InventorySystem } from '../systems/InventorySystem'
import { InventoryPanel } from './InventoryPanel'
import { ItemIntegrationManager } from '../managers/ItemIntegrationManager'
import { InventoryItem } from '../types/inventory'
import FuturesProductTooltip from './FuturesProductTooltip'
import EnhancedLootboxDisplay from './EnhancedLootboxDisplay'

// 模拟用户货币
const INITIAL_CURRENCY = {
  [CurrencyType.FOCUS_COIN]: 10000,
  [CurrencyType.DISCIPLINE_TOKEN]: 500,
  [CurrencyType.FUTURES_CRYSTAL]: 100,
  [CurrencyType.GOLDEN_HARVEST]: 20
}

interface LootboxTesterProps {
  className?: string
  onItemsReceived?: (items: InventoryItem[]) => void
  itemManager?: ItemIntegrationManager
}

export const LootboxTester: React.FC<LootboxTesterProps> = ({ 
  className = '',
  onItemsReceived,
  itemManager 
}) => {
  const [userCurrency, setUserCurrency] = useState(INITIAL_CURRENCY)
  const [selectedLootbox, setSelectedLootbox] = useState<LootboxType>(LootboxType.BASIC_FARM)
  const [openResults, setOpenResults] = useState<LootboxResult[]>([])
  const [isOpening, setIsOpening] = useState(false)
  const [showPreview, setShowPreview] = useState(false)
  const [showInventory, setShowInventory] = useState(false)
  
  // 新增：弹出模态框状态
  const [showItemsModal, setShowItemsModal] = useState(false)
  const [currentOpenResult, setCurrentOpenResult] = useState<LootboxResult | null>(null)
  
  // 创建背包系统实例（如果没有传入外部管理器）
  const [inventorySystem] = useState(() => itemManager ? null : new InventorySystem(200))

  // 开启盲盒
  const handleOpenLootbox = async (count: number = 1) => {
    setIsOpening(true)
    
    try {
      // 模拟开盒延迟
      await new Promise(resolve => setTimeout(resolve, 800))
      
      const results = LootboxGenerator.generateMultipleLootboxes(
        selectedLootbox,
        count,
        { ...userCurrency }
      )
      
      if (results.length > 0) {
        setOpenResults(prev => [...results, ...prev])
        
        // 收集所有获得的物品
        const allItems: InventoryItem[] = []
        
        results.forEach(result => {
          result.items.forEach(itemResult => {
            // 转换为 InventoryItem 格式用于通知外部系统
            const inventoryItem: InventoryItem = {
              id: `${itemResult.item.id}_${Date.now()}_${Math.random()}`,
              itemId: itemResult.item.id,
              name: itemResult.item.name,
              icon: itemResult.item.icon,
              rarity: itemResult.item.rarity,
              category: itemResult.item.category,
              type: itemResult.item.type,
              quantity: itemResult.quantity,
              description: itemResult.item.description || '',
              obtainedAt: Date.now()
            }
            allItems.push(inventoryItem)
            
            // 如果使用外部物品管理器，手动添加物品到集成系统
            if (itemManager) {
              // 将盲盒物品转换为IntegratedItem格式并添加
              const integratedItem = itemManager.addManualItem({
                id: inventoryItem.id,
                name: inventoryItem.name,
                description: inventoryItem.description,
                category: inventoryItem.category,
                type: inventoryItem.type,
                rarity: inventoryItem.rarity,
                icon: inventoryItem.icon,
                value: itemResult.item.value || 10,
                quantity: inventoryItem.quantity,
                stackable: true,
                tradeable: true,
                synthesizable: true,
                source: {
                  type: 'lootbox',
                  timestamp: Date.now()
                }
              })
              
              console.log(`🎁 盲盒物品已添加: ${integratedItem.name} x${integratedItem.quantity}`)
            } else if (inventorySystem) {
              // 使用内部背包系统
              inventorySystem.addItem(itemResult.item, itemResult.quantity)
            }
          })
        })
        
        // 通知外部系统
        if (onItemsReceived) {
          onItemsReceived(allItems)
        }
        
        // 更新用户货币
        const newCurrency = { ...userCurrency }
        results.forEach(result => {
          newCurrency[result.cost.currency] -= result.cost.amount
        })
        setUserCurrency(newCurrency)
        
        // 🎉 显示获得物品的弹出模态框
        if (results.length === 1) {
          // 单次开盒，显示详细的获得物品模态框
          setCurrentOpenResult(results[0])
          setShowItemsModal(true)
        } else {
          // 多次开盒，显示汇总结果
          const combinedItems = results.flatMap(r => r.items)
          
          // 计算品质统计
          const rarityBreakdown: Record<ItemRarity, number> = {
            [ItemRarity.GRAY]: 0,
            [ItemRarity.GREEN]: 0,
            [ItemRarity.BLUE]: 0,
            [ItemRarity.ORANGE]: 0,
            [ItemRarity.GOLD]: 0,
            [ItemRarity.GOLD_RED]: 0
          }
          
          combinedItems.forEach(itemResult => {
            rarityBreakdown[itemResult.item.rarity] += itemResult.quantity
          })
          
          const combinedResult: LootboxResult = {
            lootboxType: results[0].lootboxType,
            items: combinedItems,
            totalValue: results.reduce((sum, r) => sum + r.totalValue, 0),
            rarityBreakdown,
            cost: {
              currency: results[0].cost.currency,
              amount: results.reduce((sum, r) => sum + r.cost.amount, 0)
            },
            openedAt: Date.now()
          }
          setCurrentOpenResult(combinedResult)
          setShowItemsModal(true)
        }
      }
    } catch (error) {
      alert(error instanceof Error ? error.message : '开盒失败')
    } finally {
      setIsOpening(false)
    }
  }

  // 关闭物品模态框
  const handleCloseItemsModal = () => {
    setShowItemsModal(false)
    setCurrentOpenResult(null)
  }

  // 清除历史记录
  const handleClearHistory = () => {
    setOpenResults([])
  }

  // 重置货币
  const handleResetCurrency = () => {
    setUserCurrency(INITIAL_CURRENCY)
  }

  // 获取当前盲盒配置
  const currentConfig = LOOTBOX_CONFIGS[selectedLootbox]
  const canAfford = userCurrency[currentConfig.price.currency] >= currentConfig.price.amount
  const preview = LootboxGenerator.getLootboxPreview(selectedLootbox)

  return (
    <div className={`lootbox-tester ${className}`}>
      <div className="lootbox-tester__header">
        <h2>🎁 期货农产品盲盒测试器</h2>
        <p>测试基于中国期货农产品的盲盒开启系统</p>
      </div>

      {/* 用户货币显示 */}
      <div className="currency-display">
        <h3>💰 当前货币</h3>
        <div className="currency-grid">
          {Object.entries(userCurrency).map(([currency, amount]) => (
            <div key={currency} className="currency-item">
              <span className="currency-name">{CURRENCY_NAMES[currency as CurrencyType]}</span>
              <span className="currency-amount">{amount.toLocaleString()}</span>
            </div>
          ))}
        </div>
        <button 
          onClick={handleResetCurrency}
          className="btn btn-secondary btn-sm"
        >
          重置货币
        </button>
      </div>

      {/* 盲盒选择 - 使用新的美观设计 */}
      <EnhancedLootboxDisplay
        selectedLootbox={selectedLootbox}
        onLootboxSelect={setSelectedLootbox}
        userCurrency={userCurrency}
        className="enhanced-lootbox-section"
      />


      {/* 盲盒操作 */}
      <div className="lootbox-actions">
        <div className="action-buttons">
          <button
            onClick={() => handleOpenLootbox(1)}
            disabled={isOpening || !canAfford}
            className="btn btn-primary"
          >
            {isOpening ? '开启中...' : '开启 1个'}
          </button>
          
          <button
            onClick={() => handleOpenLootbox(10)}
            disabled={isOpening || userCurrency[currentConfig.price.currency] < currentConfig.price.amount * 10}
            className="btn btn-primary"
          >
            开启 10个
          </button>
          
          <button
            onClick={() => setShowPreview(!showPreview)}
            className="btn btn-secondary"
          >
            {showPreview ? '隐藏预览' : '显示预览'}
          </button>
          
          {/* 只有在使用内部背包系统时才显示背包按钮 */}
          {!itemManager && inventorySystem && (
            <button
              onClick={() => setShowInventory(true)}
              className="btn btn-success"
            >
              📦 打开背包
            </button>
          )}
          
          {/* 如果使用外部管理器，显示提示 */}
          {itemManager && (
            <div className="external-manager-hint">
              💡 物品已自动添加到统一背包系统，请切换到"物品背包"标签查看
            </div>
          )}
        </div>

        {!canAfford && (
          <div className="insufficient-funds">
            ⚠️ 货币不足！需要 {currentConfig.price.amount} {CURRENCY_NAMES[currentConfig.price.currency]}
          </div>
        )}
      </div>

      {/* 盲盒预览 */}
      {showPreview && (
        <div className="lootbox-preview">
          <h4>📊 {currentConfig.name} 预览信息</h4>
          <div className="preview-info">
            <div className="drop-rates">
              <h5>掉落概率</h5>
              {Object.entries(currentConfig.dropRates).map(([rarity, rate]) => (
                rate > 0 && (
                  <div key={rarity} className="rate-item">
                    <span 
                      className="rarity-indicator"
                      style={{ backgroundColor: RARITY_COLORS[rarity as ItemRarity] }}
                    ></span>
                    <span className="rarity-name">{RARITY_NAMES[rarity as ItemRarity]}</span>
                    <span className="rate-value">{(rate * 100).toFixed(1)}%</span>
                  </div>
                )
              ))}
            </div>
            <div className="expected-value">
              <strong>期望价值: {preview.expectedValue}</strong>
            </div>
          </div>
        </div>
      )}

      {/* 开盒结果 */}
      {openResults.length > 0 && (
        <div className="open-results">
          <div className="results-header">
            <h3>🎉 开盒记录 ({openResults.length})</h3>
            <button 
              onClick={handleClearHistory}
              className="btn btn-secondary btn-sm"
            >
              清除记录
            </button>
          </div>
          
          <div className="results-list">
            {openResults.map((result, index) => (
              <div key={index} className="result-item">
                <div className="result-header">
                  <span className="result-box">{LOOTBOX_CONFIGS[result.lootboxType].icon} {LOOTBOX_CONFIGS[result.lootboxType].name}</span>
                  <span className="result-value">总价值: {result.totalValue}</span>
                  <span className="result-time">
                    {new Date(result.openedAt).toLocaleTimeString()}
                  </span>
                </div>
                
                <div className="result-items">
                  {result.items.map((item, itemIndex) => (
                    <FuturesProductTooltip key={itemIndex} item={item.item}>
                      <div 
                        className={`item-result ${item.isBonus ? 'bonus' : ''} ${item.isGuaranteed ? 'guaranteed' : ''}`}
                        style={{ 
                          borderLeftColor: RARITY_COLORS[item.item.rarity],
                          position: 'relative'
                        }}
                      >
                        <span className="item-icon">{item.item.icon}</span>
                        <div className="item-info">
                          <div className="item-name">{item.item.name}</div>
                          <div className="item-details">
                            <span className="item-rarity" style={{ color: RARITY_COLORS[item.item.rarity] }}>
                              {RARITY_NAMES[item.item.rarity]}
                            </span>
                            {item.quantity > 1 && <span className="item-quantity">x{item.quantity}</span>}
                            <span className="item-value">价值: {item.item.value * item.quantity}</span>
                          </div>
                        </div>
                        {item.isBonus && <span className="bonus-tag">🎁 奖励</span>}
                        {item.isGuaranteed && <span className="guaranteed-tag">✅ 保底</span>}
                        
                        {/* 产量指示器 - 仅农业产品显示 */}
                        <div className="production-indicator" style={{
                          position: 'absolute',
                          top: '5px',
                          right: '5px',
                          fontSize: '12px',
                          animation: 'pulse 2s infinite'
                        }}>
                          📈
                        </div>
                      </div>
                    </FuturesProductTooltip>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* 背包面板 - 只有在使用内部系统时才显示 */}
      {showInventory && !itemManager && inventorySystem && (
        <InventoryPanel
          inventorySystem={inventorySystem}
          onClose={() => setShowInventory(false)}
        />
      )}

      {/* 物品模态框 */}
      {showItemsModal && currentOpenResult && (
        <div className="items-modal" onClick={handleCloseItemsModal}>
          <div className="items-modal__content" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h3>🎊 恭喜获得道具！</h3>
              <button className="close-btn" onClick={handleCloseItemsModal}>×</button>
            </div>
            
            <div className="lootbox-info">
              <div className="lootbox-icon">{LOOTBOX_CONFIGS[currentOpenResult.lootboxType].icon}</div>
              <div className="lootbox-name">{LOOTBOX_CONFIGS[currentOpenResult.lootboxType].name}</div>
              <div className="open-time">{new Date(currentOpenResult.openedAt).toLocaleString()}</div>
            </div>
            
            <div className="items-grid">
              {currentOpenResult.items.map((item, index) => (
                <FuturesProductTooltip key={index} item={item.item}>
                  <div 
                    className={`modal-item-card ${item.isBonus ? 'bonus' : ''} ${item.isGuaranteed ? 'guaranteed' : ''}`}
                    style={{ 
                      borderColor: RARITY_COLORS[item.item.rarity],
                      animationDelay: `${index * 0.1}s`,
                      position: 'relative'
                    }}
                  >
                    <div className="item-glow" style={{ backgroundColor: RARITY_COLORS[item.item.rarity] + '30' }}></div>
                    <div className="item-icon-large">{item.item.icon}</div>
                    <div className="item-name">{item.item.name}</div>
                    <div className="item-rarity" style={{ color: RARITY_COLORS[item.item.rarity] }}>
                      {RARITY_NAMES[item.item.rarity]}
                    </div>
                    {item.quantity > 1 && (
                      <div className="item-quantity">x{item.quantity}</div>
                    )}
                    <div className="item-value">💰 {item.item.value * item.quantity}</div>
                    
                    {item.isBonus && <div className="special-tag bonus-tag">🎁 奖励</div>}
                    {item.isGuaranteed && <div className="special-tag guaranteed-tag">✅ 保底</div>}
                    
                    {/* 产量指示器 - 仅农业产品显示 */}
                    <div className="production-indicator modal-production" style={{
                      position: 'absolute',
                      top: '8px',
                      right: '8px',
                      fontSize: '14px',
                      opacity: '0.8',
                      animation: 'pulse 2s infinite'
                    }}>
                      📈
                    </div>
                    
                    {/* 稀有品质特效 */}
                    {(item.item.rarity === ItemRarity.GOLD || item.item.rarity === ItemRarity.GOLD_RED) && (
                      <div className="legendary-effect">
                        <div className="sparkle sparkle-1">✨</div>
                        <div className="sparkle sparkle-2">✨</div>
                        <div className="sparkle sparkle-3">✨</div>
                      </div>
                    )}
                  </div>
                </FuturesProductTooltip>
              ))}
            </div>
            
            <div className="modal-summary">
              <div className="summary-row">
                <span>总价值:</span>
                <span className="total-value">💰 {currentOpenResult.totalValue}</span>
              </div>
              <div className="summary-row">
                <span>消耗:</span>
                <span className="cost">
                  {currentOpenResult.cost.amount} {CURRENCY_NAMES[currentOpenResult.cost.currency]}
                </span>
              </div>
              {currentOpenResult.totalValue > currentOpenResult.cost.amount && (
                <div className="summary-row profit">
                  <span>净收益:</span>
                  <span className="profit-value">
                    📈 +{currentOpenResult.totalValue - currentOpenResult.cost.amount}
                  </span>
                </div>
              )}
            </div>
            
            <div className="modal-actions">
              <button
                onClick={handleCloseItemsModal}
                className="btn btn-primary btn-large"
              >
                🎉 太棒了！
              </button>
            </div>
          </div>
        </div>
      )}

      <style>{`
        .lootbox-tester {
          max-width: 1200px;
          margin: 0 auto;
          padding: 20px;
          font-family: system-ui, -apple-system, sans-serif;
        }

        .lootbox-tester__header {
          text-align: center;
          margin-bottom: 30px;
        }

        .lootbox-tester__header h2 {
          color: #2563eb;
          margin-bottom: 10px;
        }

        .currency-display {
          background: #f8fafc;
          padding: 20px;
          border-radius: 12px;
          margin-bottom: 30px;
        }

        .currency-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: 15px;
          margin-bottom: 15px;
        }

        .currency-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          background: white;
          padding: 12px 16px;
          border-radius: 8px;
          border: 1px solid #e2e8f0;
        }

        .currency-name {
          font-weight: 600;
          color: #475569;
        }

        .currency-amount {
          font-weight: 700;
          color: #059669;
        }

        .lootbox-selector {
          margin-bottom: 40px;
        }

        .selector-header {
          text-align: center;
          margin-bottom: 32px;
          padding: 24px;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          border-radius: 20px;
          color: white;
          position: relative;
          overflow: hidden;
        }

        .selector-header::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
          opacity: 0.3;
        }

        .selector-header h3 {
          font-size: 1.8rem;
          font-weight: 800;
          margin: 0 0 8px 0;
          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
          position: relative;
          z-index: 2;
        }

        .selector-header p {
          font-size: 1rem;
          margin: 0;
          opacity: 0.9;
          position: relative;
          z-index: 2;
        }

        .lootbox-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
          gap: 24px;
          margin-top: 20px;
          padding: 10px;
        }

        .lootbox-card {
          background: linear-gradient(145deg, #ffffff, #f8fafc);
          border: 3px solid transparent;
          border-radius: 20px;
          padding: 28px 24px;
          cursor: pointer;
          transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
          text-align: center;
          position: relative;
          overflow: hidden;
          box-shadow:
            0 4px 20px rgba(0, 0, 0, 0.08),
            0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .lootbox-card::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(135deg,
            rgba(59, 130, 246, 0.1) 0%,
            rgba(16, 185, 129, 0.1) 50%,
            rgba(245, 158, 11, 0.1) 100%);
          opacity: 0;
          transition: opacity 0.3s ease;
          border-radius: 17px;
        }

        .lootbox-card:hover::before {
          opacity: 1;
        }

        .lootbox-card:hover {
          transform: translateY(-8px) scale(1.02);
          box-shadow:
            0 20px 40px rgba(0, 0, 0, 0.15),
            0 8px 16px rgba(59, 130, 246, 0.2);
          border-color: rgba(59, 130, 246, 0.3);
        }

        .lootbox-card.selected {
          border-color: #3b82f6;
          background: linear-gradient(145deg, #eff6ff, #dbeafe);
          transform: translateY(-4px);
          box-shadow:
            0 12px 32px rgba(59, 130, 246, 0.25),
            0 4px 8px rgba(59, 130, 246, 0.15);
        }

        .lootbox-card.selected::before {
          opacity: 0.7;
        }

        .lootbox-card.unaffordable {
          opacity: 0.5;
          cursor: not-allowed;
          filter: grayscale(0.6);
        }

        .lootbox-card.unaffordable:hover {
          transform: none;
          box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        }

        .lootbox-icon {
          font-size: 3.5rem;
          margin-bottom: 16px;
          display: block;
          position: relative;
          z-index: 2;
          filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
          transition: all 0.3s ease;
        }

        .lootbox-card:hover .lootbox-icon {
          transform: scale(1.1) rotate(5deg);
          filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
        }

        .lootbox-name {
          font-weight: 800;
          font-size: 1.25rem;
          margin-bottom: 12px;
          color: #1e293b;
          position: relative;
          z-index: 2;
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        }

        .lootbox-price {
          color: #059669;
          font-weight: 700;
          font-size: 1.1rem;
          margin-bottom: 12px;
          position: relative;
          z-index: 2;
          background: linear-gradient(135deg, #059669, #10b981);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
        }

        .lootbox-desc {
          font-size: 0.95rem;
          color: #64748b;
          line-height: 1.5;
          position: relative;
          z-index: 2;
          margin-bottom: 16px;
          padding: 0 8px;
        }

        .lootbox-rarity-indicator {
          position: absolute;
          top: 12px;
          right: 12px;
          width: 12px;
          height: 12px;
          border-radius: 50%;
          z-index: 3;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .lootbox-category-badge {
          position: absolute;
          top: 12px;
          left: 12px;
          background: rgba(255, 255, 255, 0.9);
          backdrop-filter: blur(10px);
          padding: 4px 8px;
          border-radius: 12px;
          font-size: 0.75rem;
          font-weight: 600;
          color: #475569;
          z-index: 3;
          border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .lootbox-guarantee {
          position: absolute;
          bottom: 12px;
          left: 50%;
          transform: translateX(-50%);
          background: linear-gradient(135deg, #fbbf24, #f59e0b);
          color: white;
          padding: 4px 12px;
          border-radius: 16px;
          font-size: 0.8rem;
          font-weight: 700;
          z-index: 3;
          box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3);
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        .lootbox-actions {
          background: #f8fafc;
          padding: 20px;
          border-radius: 12px;
          margin-bottom: 30px;
        }

        .action-buttons {
          display: flex;
          gap: 15px;
          flex-wrap: wrap;
          margin-bottom: 15px;
        }

        .btn {
          padding: 12px 24px;
          border: none;
          border-radius: 8px;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.2s;
          min-width: 120px;
        }

        .btn:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }

        .btn-primary {
          background: #3b82f6;
          color: white;
        }

        .btn-primary:hover:not(:disabled) {
          background: #2563eb;
        }

        .btn-secondary {
          background: #6b7280;
          color: white;
        }

        .btn-secondary:hover:not(:disabled) {
          background: #4b5563;
        }

        .btn-success {
          background: #059669;
          color: white;
        }

        .btn-success:hover:not(:disabled) {
          background: #047857;
        }

        .btn-sm {
          padding: 8px 16px;
          font-size: 0.9rem;
          min-width: auto;
        }

        .insufficient-funds {
          color: #dc2626;
          font-weight: 600;
          background: #fef2f2;
          padding: 12px;
          border-radius: 8px;
          border: 1px solid #fecaca;
        }

        .lootbox-preview {
          background: #f0f9ff;
          padding: 20px;
          border-radius: 12px;
          margin-bottom: 30px;
          border: 1px solid #bae6fd;
        }

        .drop-rates {
          margin-bottom: 15px;
        }

        .rate-item {
          display: flex;
          align-items: center;
          gap: 10px;
          margin-bottom: 8px;
        }

        .rarity-indicator {
          width: 16px;
          height: 16px;
          border-radius: 50%;
        }

        .rate-value {
          margin-left: auto;
          font-weight: 600;
        }

        .open-results {
          background: white;
          border-radius: 12px;
          border: 1px solid #e2e8f0;
        }

        .results-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 20px;
          border-bottom: 1px solid #e2e8f0;
        }

        .results-list {
          max-height: 600px;
          overflow-y: auto;
        }

        .result-item {
          padding: 20px;
          border-bottom: 1px solid #f1f5f9;
        }

        .result-item:last-child {
          border-bottom: none;
        }

        .result-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 15px;
          flex-wrap: wrap;
          gap: 10px;
        }

        .result-box {
          font-weight: 700;
          color: #1e293b;
        }

        .result-value {
          color: #059669;
          font-weight: 600;
        }

        .result-time {
          color: #64748b;
          font-size: 0.9rem;
        }

        .result-items {
          display: grid;
          gap: 10px;
        }

        .item-result {
          display: flex;
          align-items: center;
          gap: 15px;
          padding: 12px;
          background: #f8fafc;
          border-radius: 8px;
          border-left: 4px solid;
          position: relative;
        }

        .item-result.bonus {
          background: #fef3c7;
        }

        .item-result.guaranteed {
          background: #d1fae5;
        }

        .item-icon {
          font-size: 1.5rem;
        }

        .item-info {
          flex: 1;
        }

        .item-name {
          font-weight: 600;
          color: #1e293b;
          margin-bottom: 4px;
        }

        .item-details {
          display: flex;
          gap: 12px;
          font-size: 0.9rem;
          color: #64748b;
        }

        .item-rarity {
          font-weight: 600;
        }

        .bonus-tag, .guaranteed-tag {
          position: absolute;
          top: 8px;
          right: 8px;
          font-size: 0.8rem;
          background: white;
          padding: 2px 6px;
          border-radius: 4px;
          border: 1px solid currentColor;
        }

        .external-manager-hint {
          background: #e0f2fe;
          color: #0277bd;
          padding: 12px;
          border-radius: 8px;
          border: 1px solid #b3e5fc;
          font-size: 0.9rem;
          margin-top: 10px;
        }

        .items-modal {
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: rgba(0, 0, 0, 0.8);
          display: flex;
          justify-content: center;
          align-items: center;
          z-index: 1000;
          animation: modal-fade-in 0.3s ease-out;
        }

        .items-modal__content {
          background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
          padding: 0;
          border-radius: 16px;
          max-width: 90vw;
          max-height: 90vh;
          overflow: auto;
          box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
          animation: modal-slide-up 0.4s ease-out;
          border: 3px solid #e2e8f0;
        }

        .modal-header {
          background: linear-gradient(135deg, #4CAF50, #2E7D32);
          color: white;
          padding: 20px 30px;
          border-radius: 13px 13px 0 0;
          display: flex;
          justify-content: space-between;
          align-items: center;
          position: relative;
          overflow: hidden;
        }

        .modal-header::before {
          content: '';
          position: absolute;
          top: -50%;
          left: -50%;
          width: 200%;
          height: 200%;
          background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
          animation: shine 2s ease-in-out infinite;
        }

        .modal-header h3 {
          margin: 0;
          font-size: 1.5rem;
          text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .close-btn {
          background: rgba(255,255,255,0.2);
          border: none;
          color: white;
          font-size: 1.5rem;
          width: 35px;
          height: 35px;
          border-radius: 50%;
          cursor: pointer;
          transition: all 0.2s ease;
        }

        .close-btn:hover {
          background: rgba(255,255,255,0.3);
          transform: scale(1.1);
        }

        .lootbox-info {
          text-align: center;
          padding: 20px;
          background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
          border-bottom: 2px solid #e2e8f0;
        }

        .lootbox-info .lootbox-icon {
          font-size: 3rem;
          margin-bottom: 8px;
          animation: bounce 2s ease-in-out infinite;
        }

        .lootbox-info .lootbox-name {
          font-size: 1.2rem;
          font-weight: bold;
          color: #1e293b;
          margin-bottom: 4px;
        }

        .lootbox-info .open-time {
          font-size: 0.9rem;
          color: #64748b;
        }

        .items-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: 20px;
          padding: 25px;
          background: #f8fafc;
          max-height: 65vh;
          overflow-y: auto;
          min-height: 300px;
          border-radius: 12px;
          box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
        }

        .modal-item-card {
          background: white;
          border: 3px solid;
          border-radius: 16px;
          padding: 25px;
          text-align: center;
          position: relative;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          animation: item-appear 0.6s ease-out both;
          overflow: hidden;
          cursor: pointer;
          min-height: 200px;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
        }

        .modal-item-card:hover {
          transform: translateY(-8px) scale(1.05);
          box-shadow: 0 20px 40px rgba(0,0,0,0.15), 0 0 30px rgba(0,0,0,0.1);
          z-index: 10;
        }

        .item-glow {
          position: absolute;
          top: -3px;
          left: -3px;
          right: -3px;
          bottom: -3px;
          border-radius: 16px;
          z-index: -1;
          animation: glow-pulse 2s ease-in-out infinite;
          opacity: 0.6;
        }

        .item-icon-large {
          font-size: 4rem;
          margin-bottom: 15px;
          animation: icon-float 3s ease-in-out infinite;
          filter: drop-shadow(0 4px 8px rgba(0,0,0,0.2));
        }

        .modal-item-card .item-name {
          font-size: 1.2rem;
          font-weight: bold;
          color: #1e293b;
          margin-bottom: 10px;
          line-height: 1.3;
          max-width: 100%;
          word-wrap: break-word;
        }

        .modal-item-card .item-rarity {
          font-weight: 700;
          font-size: 1rem;
          margin-bottom: 12px;
          text-transform: uppercase;
          letter-spacing: 2px;
          text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }

        .modal-item-card .item-quantity {
          position: absolute;
          top: 15px;
          right: 15px;
          background: linear-gradient(135deg, #4CAF50, #45a049);
          color: white;
          padding: 6px 12px;
          border-radius: 20px;
          font-size: 0.9rem;
          font-weight: bold;
          box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
        }

        .modal-item-card .item-value {
          background: linear-gradient(135deg, #f59e0b, #d97706);
          color: white;
          padding: 8px 16px;
          border-radius: 25px;
          font-weight: bold;
          font-size: 1rem;
          margin-top: 12px;
          display: inline-block;
          box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
          min-width: 80px;
        }

        .special-tag {
          position: absolute;
          top: 12px;
          left: 12px;
          padding: 6px 12px;
          border-radius: 12px;
          font-size: 0.8rem;
          font-weight: bold;
          color: white;
          z-index: 2;
          box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        }

        .bonus-tag {
          background: linear-gradient(135deg, #f59e0b, #d97706);
        }

        .guaranteed-tag {
          background: linear-gradient(135deg, #10b981, #059669);
        }

        .legendary-effect {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          pointer-events: none;
          z-index: 1;
          overflow: hidden;
        }

        .sparkle {
          position: absolute;
          font-size: 1.5rem;
          animation: sparkle-rotate 3s linear infinite;
          text-shadow: 0 0 10px currentColor;
        }

        .sparkle-1 {
          top: 15%;
          left: 20%;
          animation-delay: 0s;
        }

        .sparkle-2 {
          top: 25%;
          right: 15%;
          animation-delay: 1s;
        }

        .sparkle-3 {
          bottom: 20%;
          left: 15%;
          animation-delay: 2s;
        }

        .modal-summary {
          background: white;
          padding: 25px 30px;
          border-top: 2px solid #e2e8f0;
        }

        .summary-row {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 12px;
          font-size: 1.1rem;
        }

        .summary-row:last-child {
          margin-bottom: 0;
        }

        .summary-row.profit {
          background: linear-gradient(135deg, #dcfce7, #bbf7d0);
          padding: 12px 16px;
          border-radius: 8px;
          border: 2px solid #22c55e;
        }

        .total-value {
          font-weight: bold;
          color: #059669;
          font-size: 1.2rem;
        }

        .cost {
          font-weight: 600;
          color: #dc2626;
        }

        .profit-value {
          font-weight: bold;
          color: #16a34a;
          font-size: 1.1rem;
        }

        .modal-actions {
          padding: 20px 30px;
          background: #f8fafc;
          border-radius: 0 0 13px 13px;
          text-align: center;
        }

        .btn-large {
          padding: 15px 40px;
          font-size: 1.1rem;
          font-weight: bold;
          min-width: 180px;
          background: linear-gradient(135deg, #4CAF50, #45a049);
          transition: all 0.3s ease;
        }

        .btn-large:hover:not(:disabled) {
          background: linear-gradient(135deg, #45a049, #3d8b40);
          transform: translateY(-2px);
          box-shadow: 0 8px 16px rgba(76, 175, 80, 0.3);
        }

        /* 动画关键帧 */
        @keyframes modal-fade-in {
          from { opacity: 0; }
          to { opacity: 1; }
        }

        @keyframes modal-slide-up {
          from { 
            opacity: 0;
            transform: translateY(50px) scale(0.9);
          }
          to { 
            opacity: 1;
            transform: translateY(0) scale(1);
          }
        }

        @keyframes shine {
          0% { transform: translateX(-100%) translateY(-100%) rotate(30deg); }
          100% { transform: translateX(100%) translateY(100%) rotate(30deg); }
        }

        @keyframes bounce {
          0%, 100% { transform: translateY(0); }
          50% { transform: translateY(-10px); }
        }

        @keyframes item-appear {
          from {
            opacity: 0;
            transform: translateY(30px) scale(0.8);
          }
          to {
            opacity: 1;
            transform: translateY(0) scale(1);
          }
        }

        @keyframes glow-pulse {
          0%, 100% { opacity: 0.5; }
          50% { opacity: 0.8; }
        }

        @keyframes icon-float {
          0%, 100% { transform: translateY(0); }
          50% { transform: translateY(-8px); }
        }

        @keyframes sparkle-rotate {
          0% { transform: rotate(0deg) scale(1); opacity: 1; }
          25% { transform: rotate(90deg) scale(1.2); opacity: 0.8; }
          50% { transform: rotate(180deg) scale(1); opacity: 0.6; }
          75% { transform: rotate(270deg) scale(1.2); opacity: 0.8; }
          100% { transform: rotate(360deg) scale(1); opacity: 1; }
        }

        @keyframes pulse {
          0%, 100% {
            transform: scale(1);
            opacity: 0.7;
          }
          50% {
            transform: scale(1.2);
            opacity: 1;
          }
        }

        @keyframes card-entrance {
          0% {
            opacity: 0;
            transform: translateY(30px) scale(0.9);
          }
          100% {
            opacity: 1;
            transform: translateY(0) scale(1);
          }
        }

        @keyframes gradient-shift {
          0%, 100% {
            background-position: 0% 50%;
          }
          50% {
            background-position: 100% 50%;
          }
        }

        .lootbox-card {
          animation: card-entrance 0.6s ease-out;
        }

        .lootbox-card:nth-child(1) { animation-delay: 0.1s; }
        .lootbox-card:nth-child(2) { animation-delay: 0.2s; }
        .lootbox-card:nth-child(3) { animation-delay: 0.3s; }
        .lootbox-card:nth-child(4) { animation-delay: 0.4s; }
        .lootbox-card:nth-child(5) { animation-delay: 0.5s; }
        .lootbox-card:nth-child(6) { animation-delay: 0.6s; }

        .selector-header {
          background: linear-gradient(-45deg, #667eea, #764ba2, #667eea, #764ba2);
          background-size: 400% 400%;
          animation: gradient-shift 8s ease infinite;
        }

        /* 产量指示器样式 */
        .production-indicator {
          z-index: 5;
          color: #059669;
          font-weight: bold;
          text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }

        .modal-production {
          z-index: 3;
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
          .lootbox-tester {
            padding: 15px;
          }

          .action-buttons {
            flex-direction: column;
          }

          .btn {
            width: 100%;
          }

          .currency-grid {
            grid-template-columns: 1fr;
          }

          .lootbox-grid {
            grid-template-columns: 1fr;
            gap: 20px;
          }

          .lootbox-card {
            padding: 24px 20px;
          }

          .lootbox-icon {
            font-size: 3rem;
          }

          .selector-header {
            padding: 20px;
            margin-bottom: 24px;
          }

          .selector-header h3 {
            font-size: 1.5rem;
          }

          .result-header {
            flex-direction: column;
            align-items: flex-start;
          }
          
          .items-grid {
            grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
            gap: 15px;
            padding: 20px;
          }
          
          .modal-item-card {
            padding: 20px;
            min-height: 180px;
          }
          
          .item-icon-large {
            font-size: 3rem;
            margin-bottom: 12px;
          }
          
          .modal-item-card .item-name {
            font-size: 1.1rem;
          }
          
          .modal-item-card .item-rarity {
            font-size: 0.9rem;
          }
        }

        @media (max-width: 480px) {
          .items-grid {
            grid-template-columns: repeat(3, 1fr);
            gap: 12px;
            padding: 15px;
          }
          
          .modal-item-card {
            padding: 15px;
            min-height: 160px;
          }
          
          .item-icon-large {
            font-size: 2.5rem;
          }
        }

        @media (max-width: 360px) {
          .items-grid {
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
            padding: 12px;
          }
          
          .modal-item-card {
            padding: 12px;
            min-height: 140px;
          }
          
          .item-icon-large {
            font-size: 2rem;
          }
        }
      `}</style>
    </div>
  )
} 