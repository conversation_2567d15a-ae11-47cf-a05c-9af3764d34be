import React, { useEffect, useState, useRef, useCallback, useMemo } from 'react'
import { CropInstance, CropType, CropStage, CropQuality } from '../types/crop'
import { usePerformanceTracking } from '../utils/performance/ReactPerformanceAnalyzer'
import '../styles/crops.css'

interface CropVisualizerProps {
  crop: CropInstance
  onCropClick?: (crop: CropInstance) => void
  showParticles?: boolean
  showTooltip?: boolean
  className?: string
}

interface Particle {
  id: string
  x: number
  y: number
  delay: number
  type: string
}

/**
 * 作物可视化组件 - 性能优化版本
 * 负责渲染作物的外观、动画和特效
 */
export const CropVisualizer: React.FC<CropVisualizerProps> = React.memo(({
  crop,
  onCropClick,
  showParticles = true,
  showTooltip = true,
  className = ''
}) => {
  usePerformanceTracking('CropVisualizer')

  const [particles, setParticles] = useState<Particle[]>([])
  const [isHovered, setIsHovered] = useState(false)
  const containerRef = useRef<HTMLDivElement>(null)
  const particleIdRef = useRef(0)

  // 缓存映射表 - 使用useMemo优化
  const typeClassMap = useMemo(() => ({
    [CropType.KNOWLEDGE_FLOWER]: 'knowledge-flower',
    [CropType.STRENGTH_TREE]: 'strength-tree',
    [CropType.TIME_VEGGIE]: 'time-veggie',
    [CropType.MEDITATION_LOTUS]: 'meditation-lotus',
    [CropType.FOCUS_FLOWER]: 'focus-flower',
    [CropType.READING_VINE]: 'reading-vine',
    [CropType.SOCIAL_FRUIT]: 'social-fruit'
  }), [])

  const stageClassMap = useMemo(() => ({
    [CropStage.SEED]: 'seed',
    [CropStage.SPROUT]: 'sprout',
    [CropStage.GROWING]: 'growing',
    [CropStage.MATURE]: 'mature',
    [CropStage.READY_TO_HARVEST]: 'ready',
    [CropStage.HARVESTED]: 'harvested'
  }), [])

  const particleTypeMap = useMemo(() => ({
    [CropType.KNOWLEDGE_FLOWER]: 'knowledge',
    [CropType.STRENGTH_TREE]: 'strength',
    [CropType.TIME_VEGGIE]: 'time',
    [CropType.MEDITATION_LOTUS]: 'meditation',
    [CropType.FOCUS_FLOWER]: 'focus',
    [CropType.READING_VINE]: 'reading',
    [CropType.SOCIAL_FRUIT]: 'social'
  }), [])

  // 优化函数 - 使用useCallback
  const getCropTypeClass = useCallback((type: CropType): string => {
    return typeClassMap[type] || 'knowledge-flower'
  }, [typeClassMap])

  const getStageClass = useCallback((stage: CropStage): string => {
    return stageClassMap[stage] || 'seed'
  }, [stageClassMap])

  const getQualityClass = useCallback((quality: CropQuality): string => {
    return `crop-quality-${quality}`
  }, [])

  const getParticleType = useCallback((cropType: CropType): string => {
    return particleTypeMap[cropType] || 'knowledge'
  }, [particleTypeMap])

  // 优化粒子创建函数
  const createParticle = useCallback((): Particle => {
    const id = `particle-${particleIdRef.current++}`
    const x = Math.random() * 60 + 2
    const y = Math.random() * 10 + 50
    const delay = Math.random() * 2000
    const type = getParticleType(crop.type)
    
    return { id, x, y, delay, type }
  }, [crop.type, getParticleType])

  // 粒子数量计算 - 使用useMemo缓存
  const particleCount = useMemo(() => {
    const stageMultiplier = {
      [CropStage.SEED]: 0,
      [CropStage.SPROUT]: 1,
      [CropStage.GROWING]: 2,
      [CropStage.MATURE]: 3,
      [CropStage.READY_TO_HARVEST]: 5,
      [CropStage.HARVESTED]: 0
    }

    const qualityMultiplier = {
      [CropQuality.COMMON]: 1,
      [CropQuality.UNCOMMON]: 1.5,
      [CropQuality.RARE]: 2,
      [CropQuality.EPIC]: 3,
      [CropQuality.LEGENDARY]: 4
    }

    return Math.floor((stageMultiplier[crop.stage] || 0) * (qualityMultiplier[crop.quality] || 1))
  }, [crop.stage, crop.quality])

  // 优化粒子生成函数
  const generateParticles = useCallback(() => {
    if (!showParticles || crop.stage === CropStage.SEED) return

    const newParticles: Particle[] = []
    for (let i = 0; i < particleCount; i++) {
      newParticles.push(createParticle())
    }

    setParticles(prev => [...prev, ...newParticles])

    // 定期清理粒子 - 减少内存占用
    setTimeout(() => {
      setParticles(prev => prev.filter(p => !newParticles.includes(p)))
    }, 3000)
  }, [showParticles, crop.stage, particleCount, createParticle])

  // 优化点击处理
  const handleClick = useCallback(() => {
    if (onCropClick) {
      onCropClick(crop)
    }
  }, [onCropClick, crop])

  // 状态类计算 - 使用useMemo优化
  const stateClasses = useMemo(() => {
    const classes: string[] = []
    
    if (crop.isPaused) classes.push('crop-paused')
    if (crop.metadata.growthBoosts > 0) classes.push('crop-boosted')
    if (!crop.isGrowing && crop.stage !== CropStage.READY_TO_HARVEST) {
      classes.push('crop-withering')
    }
    
    return classes
  }, [crop.isPaused, crop.metadata.growthBoosts, crop.isGrowing, crop.stage])

  // 工具提示内容 - 使用useMemo优化
  const tooltipContent = useMemo(() => {
    const stageNames = {
      [CropStage.SEED]: '种子',
      [CropStage.SPROUT]: '幼苗',
      [CropStage.GROWING]: '生长中',
      [CropStage.MATURE]: '成熟',
      [CropStage.READY_TO_HARVEST]: '可收获',
      [CropStage.HARVESTED]: '已收获'
    }

    const qualityNames = {
      [CropQuality.COMMON]: '普通',
      [CropQuality.UNCOMMON]: '优良',
      [CropQuality.RARE]: '稀有',
      [CropQuality.EPIC]: '史诗',
      [CropQuality.LEGENDARY]: '传说'
    }

    const typeNames = {
      [CropType.KNOWLEDGE_FLOWER]: '知识花',
      [CropType.STRENGTH_TREE]: '力量树',
      [CropType.TIME_VEGGIE]: '时间菜',
      [CropType.MEDITATION_LOTUS]: '冥想莲',
      [CropType.FOCUS_FLOWER]: '专注花',
      [CropType.READING_VINE]: '读书藤',
      [CropType.SOCIAL_FRUIT]: '社交果'
    }

    return `${typeNames[crop.type]} (${qualityNames[crop.quality]})
阶段: ${stageNames[crop.stage]}
专注度: ${Math.round(crop.averageFocusScore)}%
会话数: ${crop.metadata.sessionsContributed}`
  }, [crop.type, crop.quality, crop.stage, crop.averageFocusScore, crop.metadata.sessionsContributed])

  // 优化粒子生成effect - 降低频率
  useEffect(() => {
    if (!showParticles) return

    const interval = setInterval(() => {
      if (crop.isGrowing && crop.stage !== CropStage.SEED) {
        generateParticles()
      }
    }, 2000) // 从1.5秒改为2秒，减少CPU使用

    return () => clearInterval(interval)
  }, [showParticles, crop.isGrowing, crop.stage, generateParticles])

  // 动画优化注册
  useEffect(() => {
    const element = containerRef.current
    if (!element) return

    // 根据作物阶段设置动画优先级
    const priority = crop.stage === CropStage.READY_TO_HARVEST ? 'high' : 
                    crop.stage === CropStage.GROWING ? 'medium' : 'low'
    
    element.setAttribute('data-animation-priority', priority)
    element.setAttribute('data-animating', 'true')
    element.classList.add('optimized-animation')
    
    return () => {
      element.removeAttribute('data-animation-priority')
      element.removeAttribute('data-animating')
      element.classList.remove('optimized-animation')
    }
  }, [crop.stage])

  // 组合最终CSS类名 - 使用useMemo
  const finalClassName = useMemo(() => {
    const baseClasses = [
      'crop-sprite',
      `crop-${getCropTypeClass(crop.type)}`,
      getStageClass(crop.stage),
      getQualityClass(crop.quality),
      'optimized-animation',
      ...stateClasses,
      className
    ]
    
    return baseClasses.filter(Boolean).join(' ')
  }, [crop.type, crop.stage, crop.quality, stateClasses, className, getCropTypeClass, getStageClass, getQualityClass])

  // 鼠标事件处理 - 使用useCallback优化
  const handleMouseEnter = useCallback(() => setIsHovered(true), [])
  const handleMouseLeave = useCallback(() => setIsHovered(false), [])

  return (
    <div
      ref={containerRef}
      className={`crop-container ${className}`}
      onClick={handleClick}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      style={{
        position: 'relative',
        cursor: onCropClick ? 'pointer' : 'default',
        transform: 'translateZ(0)', // GPU优化
        willChange: 'transform'
      }}
    >
      {/* 作物主体 */}
      <div
        className={finalClassName}
        style={{
          transform: 'translateZ(0)', // GPU优化
        }}
      />

      {/* 粒子效果 - 条件渲染优化 */}
      {showParticles && particles.length > 0 && (
        <div className="crop-particles">
          {particles.map(particle => (
            <div
              key={particle.id}
              className={`particle ${particle.type} optimized-animation`}
              style={{
                left: `${particle.x}%`,
                top: `${particle.y}%`,
                animationDelay: `${particle.delay}ms`,
                transform: 'translateZ(0)', // GPU优化
              }}
            />
          ))}
        </div>
      )}

      {/* 工具提示 - 条件渲染优化 */}
      {showTooltip && isHovered && (
        <div className="crop-tooltip">
          {tooltipContent}
        </div>
      )}
    </div>
  )
})

CropVisualizer.displayName = 'CropVisualizer'

/**
 * 批量作物可视化组件
 * 用于显示农场网格中的多个作物
 */
interface CropGridVisualizerProps {
  crops: (CropInstance | null)[][]
  onCropClick?: (crop: CropInstance, gridX: number, gridY: number) => void
  onEmptyPlotClick?: (gridX: number, gridY: number) => void
  className?: string
}

export const CropGridVisualizer: React.FC<CropGridVisualizerProps> = ({
  crops,
  onCropClick,
  onEmptyPlotClick,
  className = ''
}) => {
  const handleCropClick = (crop: CropInstance) => {
    if (onCropClick) {
      onCropClick(crop, crop.position.gridX, crop.position.gridY)
    }
  }

  const handleEmptyPlotClick = (gridY: number, gridX: number) => {
    if (onEmptyPlotClick) {
      onEmptyPlotClick(gridX, gridY)
    }
  }

  return (
    <div className={`crop-grid ${className}`}>
      {crops.map((row, gridY) => (
        <div key={gridY} className="crop-grid-row">
          {row.map((crop, gridX) => (
            <div
              key={`${gridY}-${gridX}`}
              className="crop-grid-cell"
              style={{
                width: '64px',
                height: '64px',
                border: '1px solid rgba(255, 255, 255, 0.1)',
                backgroundColor: crop ? 'transparent' : 'rgba(0, 0, 0, 0.1)',
                cursor: crop ? 'pointer' : onEmptyPlotClick ? 'pointer' : 'default'
              }}
              onClick={() => {
                if (crop) {
                  handleCropClick(crop)
                } else {
                  handleEmptyPlotClick(gridY, gridX)
                }
              }}
            >
              {crop && (
                <CropVisualizer
                  crop={crop}
                  onCropClick={handleCropClick}
                  showParticles={true}
                  showTooltip={true}
                />
              )}
            </div>
          ))}
        </div>
      ))}
    </div>
  )
}

/**
 * 作物预览组件
 * 用于作物选择界面
 */
interface CropPreviewProps {
  cropType: CropType
  stage?: CropStage
  quality?: CropQuality
  size?: 'small' | 'medium' | 'large'
  showLabel?: boolean
  onClick?: () => void
}

export const CropPreview: React.FC<CropPreviewProps> = ({
  cropType,
  stage = CropStage.MATURE,
  quality = CropQuality.COMMON,
  size = 'medium',
  showLabel = true,
  onClick
}) => {
  // 创建模拟作物实例
  const mockCrop: CropInstance = {
    id: `preview-${cropType}`,
    type: cropType,
    stage,
    quality,
    plantedAt: Date.now(),
    stageStartTime: Date.now(),
    totalGrowthTime: 0,
    focusTimeContributed: 0,
    averageFocusScore: 85,
    position: { x: 0, y: 0, gridX: 0, gridY: 0 },
    isGrowing: stage !== CropStage.READY_TO_HARVEST,
    isPaused: false,
    harvestable: stage === CropStage.READY_TO_HARVEST,
    metadata: {
      sessionsContributed: 5,
      bestFocusStreak: 120,
      growthBoosts: 1
    }
  }

  const typeNames = {
    [CropType.KNOWLEDGE_FLOWER]: '知识花',
    [CropType.STRENGTH_TREE]: '力量树',
    [CropType.TIME_VEGGIE]: '时间菜',
    [CropType.MEDITATION_LOTUS]: '冥想莲',
    [CropType.FOCUS_FLOWER]: '专注花',
    [CropType.READING_VINE]: '读书藤',
    [CropType.SOCIAL_FRUIT]: '社交果'
  }

  const sizeMap = {
    small: '32px',
    medium: '64px',
    large: '96px'
  }

  return (
    <div 
      className="crop-preview"
      style={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        gap: '8px',
        cursor: onClick ? 'pointer' : 'default',
        padding: '8px',
        borderRadius: '8px',
        transition: 'background-color 0.2s'
      }}
      onClick={onClick}
      onMouseEnter={(e) => {
        if (onClick) {
          e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.1)'
        }
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.backgroundColor = 'transparent'
      }}
    >
      <div style={{ width: sizeMap[size], height: sizeMap[size] }}>
        <CropVisualizer
          crop={mockCrop}
          showParticles={size !== 'small'}
          showTooltip={false}
        />
      </div>
      {showLabel && (
        <span style={{
          fontSize: size === 'small' ? '12px' : '14px',
          color: 'rgba(255, 255, 255, 0.9)',
          textAlign: 'center'
        }}>
          {typeNames[cropType]}
        </span>
      )}
    </div>
  )
} 