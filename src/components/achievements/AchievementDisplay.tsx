import React, { useState, useEffect } from 'react';
import {
  Achievement,
  AchievementCategory,
  AchievementType,
  UserAchievementProgress,
  AchievementStats
} from '../../types/achievements';
import { getAchievementsByCategory, getAchievementsByType } from '../../data/achievementsConfig';

interface AchievementDisplayProps {
  achievements: Achievement[];
  userProgress: UserAchievementProgress[];
  completedAchievements: Set<string>;
  stats: AchievementStats;
  onAchievementClick?: (achievement: Achievement) => void;
}

interface AchievementCardProps {
  achievement: Achievement;
  progress: UserAchievementProgress;
  isCompleted: boolean;
  onClick?: () => void;
}

const AchievementCard: React.FC<AchievementCardProps> = ({
  achievement,
  progress,
  isCompleted,
  onClick
}) => {
  const progressPercent = (progress.progress / progress.maxProgress) * 100;

  return (
    <div 
      className={`achievement-card ${isCompleted ? 'completed' : 'incomplete'}`}
      onClick={onClick}
      style={{
        border: '2px solid #e1e5e9',
        borderRadius: '12px',
        padding: '16px',
        margin: '8px',
        cursor: 'pointer',
        transition: 'all 0.3s ease',
        backgroundColor: isCompleted ? '#f0f9ff' : '#ffffff',
        borderColor: isCompleted ? '#0ea5e9' : '#e1e5e9',
        boxShadow: isCompleted ? '0 4px 12px rgba(14, 165, 233, 0.15)' : '0 2px 4px rgba(0, 0, 0, 0.1)',
        transform: 'translateY(0)',
      }}
      onMouseEnter={(e) => {
        e.currentTarget.style.transform = 'translateY(-2px)';
        e.currentTarget.style.boxShadow = isCompleted 
          ? '0 8px 20px rgba(14, 165, 233, 0.25)' 
          : '0 4px 12px rgba(0, 0, 0, 0.15)';
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.transform = 'translateY(0)';
        e.currentTarget.style.boxShadow = isCompleted 
          ? '0 4px 12px rgba(14, 165, 233, 0.15)' 
          : '0 2px 4px rgba(0, 0, 0, 0.1)';
      }}
    >
      <div style={{ display: 'flex', alignItems: 'center', marginBottom: '12px' }}>
        <div 
          style={{
            fontSize: '32px',
            marginRight: '12px',
            filter: isCompleted ? 'none' : 'grayscale(100%)',
            opacity: isCompleted ? 1 : 0.6
          }}
        >
          {achievement.icon}
        </div>
        <div style={{ flex: 1 }}>
          <h3 style={{
            margin: '0 0 4px 0',
            fontSize: '16px',
            fontWeight: '600',
            color: isCompleted ? '#0f172a' : '#64748b'
          }}>
            {achievement.name}
          </h3>
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            fontSize: '12px',
            color: '#64748b'
          }}>
            <span style={{
              padding: '2px 8px',
              backgroundColor: getCategoryColor(achievement.category),
              color: 'white',
              borderRadius: '12px',
              fontSize: '10px',
              fontWeight: '500'
            }}>
              {getCategoryName(achievement.category)}
            </span>
            <span style={{
              padding: '2px 8px',
              backgroundColor: getTypeColor(achievement.type),
              color: 'white',
              borderRadius: '12px',
              fontSize: '10px',
              fontWeight: '500'
            }}>
              {getTypeName(achievement.type)}
            </span>
          </div>
        </div>
        {isCompleted && (
          <div style={{
            backgroundColor: '#10b981',
            color: 'white',
            padding: '4px 8px',
            borderRadius: '8px',
            fontSize: '12px',
            fontWeight: '500'
          }}>
            已完成
          </div>
        )}
      </div>

      <p style={{
        margin: '0 0 12px 0',
        fontSize: '14px',
        color: '#64748b',
        lineHeight: '1.4'
      }}>
        {achievement.description}
      </p>

      <div style={{ marginBottom: '12px' }}>
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '4px'
        }}>
          <span style={{ fontSize: '12px', color: '#64748b' }}>进度</span>
          <span style={{ fontSize: '12px', fontWeight: '500', color: '#374151' }}>
            {progress.progress} / {progress.maxProgress}
          </span>
        </div>
        <div style={{
          width: '100%',
          height: '6px',
          backgroundColor: '#e5e7eb',
          borderRadius: '3px',
          overflow: 'hidden'
        }}>
          <div
            style={{
              width: `${Math.min(progressPercent, 100)}%`,
              height: '100%',
              backgroundColor: isCompleted ? '#10b981' : '#3b82f6',
              borderRadius: '3px',
              transition: 'width 0.3s ease'
            }}
          />
        </div>
      </div>

      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        <span style={{
          fontSize: '12px',
          color: '#64748b'
        }}>
          {achievement.requirement.description}
        </span>
        <span style={{
          fontSize: '14px',
          fontWeight: '600',
          color: '#f59e0b',
          display: 'flex',
          alignItems: 'center',
          gap: '4px'
        }}>
          <span style={{ fontSize: '12px' }}>⭐</span>
          {achievement.experienceReward} EXP
        </span>
      </div>
    </div>
  );
};

const AchievementDisplay: React.FC<AchievementDisplayProps> = ({
  achievements,
  userProgress,
  completedAchievements,
  stats,
  onAchievementClick
}) => {
  const [activeTab, setActiveTab] = useState<'all' | AchievementCategory | AchievementType>('all');
  const [filteredAchievements, setFilteredAchievements] = useState<Achievement[]>(achievements);

  useEffect(() => {
    let filtered = achievements;

    if (activeTab !== 'all') {
      // 检查是否是类别或类型
      if (Object.values(AchievementCategory).includes(activeTab as AchievementCategory)) {
        filtered = getAchievementsByCategory(activeTab as AchievementCategory);
      } else if (Object.values(AchievementType).includes(activeTab as AchievementType)) {
        filtered = getAchievementsByType(activeTab as AchievementType);
      }
    }

    setFilteredAchievements(filtered);
  }, [activeTab, achievements]);

  const getProgressForAchievement = (achievementId: string): UserAchievementProgress => {
    return userProgress.find(p => p.achievementId === achievementId) || {
      achievementId,
      progress: 0,
      maxProgress: 1,
      isCompleted: false,
      lastUpdated: new Date()
    };
  };

  const completedCount = filteredAchievements.filter(a => 
    completedAchievements.has(a.id)
  ).length;

  const tabs = [
    { key: 'all', label: '全部', count: achievements.length },
    { key: AchievementCategory.FOCUS, label: '专注', count: stats.categoryCounts[AchievementCategory.FOCUS]?.total || 0 },
    { key: AchievementCategory.POSTURE, label: '姿态', count: stats.categoryCounts[AchievementCategory.POSTURE]?.total || 0 },
    { key: AchievementCategory.TIME, label: '时间', count: stats.categoryCounts[AchievementCategory.TIME]?.total || 0 },
    { key: AchievementCategory.CONSISTENCY, label: '坚持', count: stats.categoryCounts[AchievementCategory.CONSISTENCY]?.total || 0 },
    { key: AchievementCategory.IMPROVEMENT, label: '改进', count: stats.categoryCounts[AchievementCategory.IMPROVEMENT]?.total || 0 },
  ];

  return (
    <div style={{ padding: '20px', fontFamily: 'system-ui, -apple-system, sans-serif' }}>
      {/* 统计概览 */}
      <div style={{ marginBottom: '24px' }}>
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
          gap: '16px',
          marginBottom: '20px'
        }}>
          <div style={{
            backgroundColor: '#f8fafc',
            padding: '20px',
            borderRadius: '12px',
            border: '1px solid #e2e8f0',
            textAlign: 'center'
          }}>
            <div style={{ fontSize: '32px', fontWeight: '700', color: '#0f172a', marginBottom: '4px' }}>
              {stats.completedAchievements}
            </div>
            <div style={{ fontSize: '14px', color: '#64748b' }}>已完成成就</div>
          </div>
          <div style={{
            backgroundColor: '#f8fafc',
            padding: '20px',
            borderRadius: '12px',
            border: '1px solid #e2e8f0',
            textAlign: 'center'
          }}>
            <div style={{ fontSize: '32px', fontWeight: '700', color: '#3b82f6', marginBottom: '4px' }}>
              {Math.round(stats.completionRate)}%
            </div>
            <div style={{ fontSize: '14px', color: '#64748b' }}>完成率</div>
          </div>
          <div style={{
            backgroundColor: '#f8fafc',
            padding: '20px',
            borderRadius: '12px',
            border: '1px solid #e2e8f0',
            textAlign: 'center'
          }}>
            <div style={{ fontSize: '32px', fontWeight: '700', color: '#10b981', marginBottom: '4px' }}>
              {completedCount}
            </div>
            <div style={{ fontSize: '14px', color: '#64748b' }}>当前分类已完成</div>
          </div>
        </div>
      </div>

      {/* 分类标签 */}
      <div style={{ marginBottom: '24px' }}>
        <div style={{
          display: 'flex',
          flexWrap: 'wrap',
          gap: '8px',
          borderBottom: '1px solid #e2e8f0',
          paddingBottom: '16px'
        }}>
          {tabs.map(tab => (
            <button
              key={tab.key}
              onClick={() => setActiveTab(tab.key as any)}
              style={{
                padding: '8px 16px',
                border: 'none',
                borderRadius: '8px',
                fontSize: '14px',
                fontWeight: '500',
                cursor: 'pointer',
                transition: 'all 0.2s ease',
                backgroundColor: activeTab === tab.key ? '#3b82f6' : '#f1f5f9',
                color: activeTab === tab.key ? 'white' : '#64748b',
                display: 'flex',
                alignItems: 'center',
                gap: '4px'
              }}
            >
              {tab.label}
              <span style={{
                backgroundColor: activeTab === tab.key ? 'rgba(255,255,255,0.2)' : '#cbd5e1',
                color: activeTab === tab.key ? 'white' : '#475569',
                padding: '2px 6px',
                borderRadius: '10px',
                fontSize: '12px',
                minWidth: '20px',
                textAlign: 'center'
              }}>
                {tab.count}
              </span>
            </button>
          ))}
        </div>
      </div>

      {/* 成就列表 */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fill, minmax(320px, 1fr))',
        gap: '16px'
      }}>
        {filteredAchievements.map(achievement => (
          <AchievementCard
            key={achievement.id}
            achievement={achievement}
            progress={getProgressForAchievement(achievement.id)}
            isCompleted={completedAchievements.has(achievement.id)}
            onClick={() => onAchievementClick?.(achievement)}
          />
        ))}
      </div>

      {filteredAchievements.length === 0 && (
        <div style={{
          textAlign: 'center',
          padding: '40px',
          color: '#64748b',
          fontSize: '16px'
        }}>
          暂无相关成就
        </div>
      )}
    </div>
  );
};

// 辅助函数
function getCategoryColor(category: AchievementCategory): string {
  const colors = {
    [AchievementCategory.FOCUS]: '#3b82f6',
    [AchievementCategory.POSTURE]: '#10b981',
    [AchievementCategory.TIME]: '#f59e0b',
    [AchievementCategory.CONSISTENCY]: '#8b5cf6',
    [AchievementCategory.IMPROVEMENT]: '#ef4444'
  };
  return colors[category];
}

function getCategoryName(category: AchievementCategory): string {
  const names = {
    [AchievementCategory.FOCUS]: '专注',
    [AchievementCategory.POSTURE]: '姿态',
    [AchievementCategory.TIME]: '时间',
    [AchievementCategory.CONSISTENCY]: '坚持',
    [AchievementCategory.IMPROVEMENT]: '改进'
  };
  return names[category];
}

function getTypeColor(type: AchievementType): string {
  const colors = {
    [AchievementType.DAILY]: '#06b6d4',
    [AchievementType.WEEKLY]: '#84cc16',
    [AchievementType.MILESTONE]: '#f97316',
    [AchievementType.STREAK]: '#ec4899',
    [AchievementType.SPECIAL]: '#6366f1'
  };
  return colors[type];
}

function getTypeName(type: AchievementType): string {
  const names = {
    [AchievementType.DAILY]: '每日',
    [AchievementType.WEEKLY]: '每周',
    [AchievementType.MILESTONE]: '里程碑',
    [AchievementType.STREAK]: '连续',
    [AchievementType.SPECIAL]: '特殊'
  };
  return names[type];
}

export default AchievementDisplay; 