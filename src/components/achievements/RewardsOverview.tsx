import React, { useState } from 'react';
import {
  Achievement,
  UserLevel,
  AchievementStats,
  LevelUpHistory,
  UserAchievementProgress
} from '../../types/achievements';
import AchievementDisplay from './AchievementDisplay';

interface RewardsOverviewProps {
  currentLevel: UserLevel;
  totalExperience: number;
  levelUpHistory: LevelUpHistory[];
  recentAchievements: Achievement[];
  achievements: Achievement[];
  userProgress: UserAchievementProgress[];
  completedAchievements: Set<string>;
  stats: AchievementStats;
  onAchievementClick?: (achievement: Achievement) => void;
}

interface LevelProgressProps {
  currentLevel: UserLevel;
  totalExperience: number;
  experienceForNextLevel?: number;
}

const LevelProgress: React.FC<LevelProgressProps> = ({
  currentLevel,
  totalExperience,
  experienceForNextLevel
}) => {
  const progressPercent = experienceForNextLevel 
    ? ((totalExperience - currentLevel.experienceRequired) / (experienceForNextLevel - currentLevel.experienceRequired)) * 100
    : 100; // 最高等级

  return (
    <div style={{
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      padding: '24px',
      borderRadius: '16px',
      color: 'white',
      marginBottom: '24px',
      position: 'relative',
      overflow: 'hidden'
    }}>
      {/* 背景装饰 */}
      <div style={{
        position: 'absolute',
        top: '-50%',
        right: '-20%',
        width: '200px',
        height: '200px',
        background: 'linear-gradient(45deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05))',
        borderRadius: '50%',
        transform: 'rotate(45deg)'
      }} />
      
      <div style={{ position: 'relative', zIndex: 1 }}>
        <div style={{ display: 'flex', alignItems: 'center', marginBottom: '16px' }}>
          <div style={{ fontSize: '48px', marginRight: '16px' }}>
            {currentLevel.icon}
          </div>
          <div>
            <h2 style={{ margin: '0 0 4px 0', fontSize: '24px', fontWeight: '700' }}>
              {currentLevel.name}
            </h2>
            <p style={{ margin: 0, fontSize: '14px', opacity: 0.9 }}>
              等级 {currentLevel.level} • {totalExperience.toLocaleString()} 总经验值
            </p>
          </div>
        </div>

        {experienceForNextLevel && (
          <div>
            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginBottom: '8px'
            }}>
              <span style={{ fontSize: '14px', opacity: 0.9 }}>下一等级进度</span>
              <span style={{ fontSize: '14px', fontWeight: '600' }}>
                {Math.round(progressPercent)}%
              </span>
            </div>
            <div style={{
              width: '100%',
              height: '8px',
              backgroundColor: 'rgba(255,255,255,0.2)',
              borderRadius: '4px',
              overflow: 'hidden'
            }}>
              <div
                style={{
                  width: `${Math.min(progressPercent, 100)}%`,
                  height: '100%',
                  background: 'linear-gradient(90deg, #fbbf24, #f59e0b)',
                  borderRadius: '4px',
                  transition: 'width 0.3s ease'
                }}
              />
            </div>
            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              marginTop: '4px',
              fontSize: '12px',
              opacity: 0.8
            }}>
              <span>{(totalExperience - currentLevel.experienceRequired).toLocaleString()}</span>
              <span>需要 {(experienceForNextLevel - totalExperience).toLocaleString()} EXP</span>
            </div>
          </div>
        )}

        {!experienceForNextLevel && (
          <div style={{
            textAlign: 'center',
            padding: '16px',
            backgroundColor: 'rgba(255,255,255,0.1)',
            borderRadius: '8px',
            fontSize: '14px'
          }}>
            🏆 恭喜！您已达到最高等级！
          </div>
        )}
      </div>
    </div>
  );
};

interface RecentAchievementsProps {
  achievements: Achievement[];
  onViewAll: () => void;
}

const RecentAchievements: React.FC<RecentAchievementsProps> = ({
  achievements,
  onViewAll
}) => {
  return (
    <div style={{
      backgroundColor: '#f8fafc',
      padding: '20px',
      borderRadius: '12px',
      border: '1px solid #e2e8f0',
      marginBottom: '24px'
    }}>
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '16px'
      }}>
        <h3 style={{ margin: 0, fontSize: '18px', fontWeight: '600', color: '#0f172a' }}>
          🎉 最近解锁的成就
        </h3>
        <button
          onClick={onViewAll}
          style={{
            background: 'none',
            border: 'none',
            color: '#3b82f6',
            fontSize: '14px',
            fontWeight: '500',
            cursor: 'pointer',
            textDecoration: 'underline'
          }}
        >
          查看全部
        </button>
      </div>

      {achievements.length === 0 ? (
        <div style={{
          textAlign: 'center',
          padding: '40px',
          color: '#64748b',
          fontSize: '14px'
        }}>
          还没有解锁任何成就，继续努力吧！💪
        </div>
      ) : (
        <div style={{ display: 'flex', flexWrap: 'wrap', gap: '12px' }}>
          {achievements.slice(0, 6).map(achievement => (
            <div
              key={achievement.id}
              style={{
                display: 'flex',
                alignItems: 'center',
                backgroundColor: 'white',
                padding: '12px',
                borderRadius: '8px',
                border: '1px solid #e2e8f0',
                minWidth: '200px',
                flex: '1 1 calc(50% - 6px)'
              }}
            >
              <div style={{ fontSize: '24px', marginRight: '12px' }}>
                {achievement.icon}
              </div>
              <div style={{ flex: 1 }}>
                <div style={{
                  fontSize: '14px',
                  fontWeight: '600',
                  color: '#0f172a',
                  marginBottom: '2px'
                }}>
                  {achievement.name}
                </div>
                <div style={{
                  fontSize: '12px',
                  color: '#64748b',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '4px'
                }}>
                  <span>⭐</span>
                  <span>{achievement.experienceReward} EXP</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

interface LevelHistoryProps {
  history: LevelUpHistory[];
}

const LevelHistory: React.FC<LevelHistoryProps> = ({ history }) => {
  if (history.length === 0) {
    return null;
  }

  return (
    <div style={{
      backgroundColor: '#ffffff',
      padding: '20px',
      borderRadius: '12px',
      border: '1px solid #e2e8f0',
      marginBottom: '24px'
    }}>
      <h3 style={{ margin: '0 0 16px 0', fontSize: '18px', fontWeight: '600', color: '#0f172a' }}>
        📈 等级晋升历史
      </h3>

      <div style={{ maxHeight: '300px', overflowY: 'auto' }}>
        {history.slice(0, 10).map((entry, index) => (
          <div
            key={index}
            style={{
              display: 'flex',
              alignItems: 'center',
              padding: '12px',
              backgroundColor: index % 2 === 0 ? '#f8fafc' : 'transparent',
              borderRadius: '8px',
              marginBottom: '4px'
            }}
          >
            <div style={{
              width: '40px',
              height: '40px',
              backgroundColor: '#3b82f6',
              color: 'white',
              borderRadius: '20px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: '14px',
              fontWeight: '600',
              marginRight: '12px'
            }}>
              {entry.newLevel}
            </div>
            <div style={{ flex: 1 }}>
              <div style={{
                fontSize: '14px',
                fontWeight: '600',
                color: '#0f172a',
                marginBottom: '2px'
              }}>
                升级到等级 {entry.newLevel}
              </div>
              <div style={{ fontSize: '12px', color: '#64748b' }}>
                {entry.timestamp.toLocaleDateString('zh-CN', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric',
                  hour: '2-digit',
                  minute: '2-digit'
                })}
              </div>
            </div>
            <div style={{
              fontSize: '12px',
              color: '#10b981',
              fontWeight: '600',
              backgroundColor: '#ecfdf5',
              padding: '4px 8px',
              borderRadius: '6px'
            }}>
              +{entry.experienceGained} EXP
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

const RewardsOverview: React.FC<RewardsOverviewProps> = ({
  currentLevel,
  totalExperience,
  levelUpHistory,
  recentAchievements,
  achievements,
  userProgress,
  completedAchievements,
  stats,
  onAchievementClick
}) => {
  const [activeView, setActiveView] = useState<'overview' | 'achievements'>('overview');

  // 计算下一等级所需经验值
  const experienceForNextLevel = currentLevel.level < 10 
    ? Math.pow(currentLevel.level + 1, 2) * 1000 
    : undefined;

  if (activeView === 'achievements') {
    return (
      <div>
        <div style={{ marginBottom: '20px' }}>
          <button
            onClick={() => setActiveView('overview')}
            style={{
              background: 'none',
              border: 'none',
              color: '#3b82f6',
              fontSize: '14px',
              fontWeight: '500',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              gap: '4px'
            }}
          >
            ← 返回奖励概览
          </button>
        </div>
        <AchievementDisplay
          achievements={achievements}
          userProgress={userProgress}
          completedAchievements={completedAchievements}
          stats={stats}
          onAchievementClick={onAchievementClick}
        />
      </div>
    );
  }

  return (
    <div style={{ maxWidth: '1200px', margin: '0 auto', padding: '20px' }}>
      <h1 style={{
        margin: '0 0 24px 0',
        fontSize: '28px',
        fontWeight: '700',
        color: '#0f172a',
        textAlign: 'center'
      }}>
        🏆 奖励系统
      </h1>

      <LevelProgress
        currentLevel={currentLevel}
        totalExperience={totalExperience}
        experienceForNextLevel={experienceForNextLevel}
      />

      <RecentAchievements
        achievements={recentAchievements}
        onViewAll={() => setActiveView('achievements')}
      />

      <LevelHistory history={levelUpHistory} />

      {/* 快速统计 */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(240px, 1fr))',
        gap: '16px',
        marginBottom: '24px'
      }}>
        <div style={{
          backgroundColor: '#fef3c7',
          padding: '20px',
          borderRadius: '12px',
          textAlign: 'center',
          border: '1px solid #fcd34d'
        }}>
          <div style={{ fontSize: '32px', marginBottom: '8px' }}>⭐</div>
          <div style={{ fontSize: '24px', fontWeight: '700', color: '#92400e', marginBottom: '4px' }}>
            {totalExperience.toLocaleString()}
          </div>
          <div style={{ fontSize: '14px', color: '#92400e' }}>总经验值</div>
        </div>

        <div style={{
          backgroundColor: '#dbeafe',
          padding: '20px',
          borderRadius: '12px',
          textAlign: 'center',
          border: '1px solid #60a5fa'
        }}>
          <div style={{ fontSize: '32px', marginBottom: '8px' }}>🎯</div>
          <div style={{ fontSize: '24px', fontWeight: '700', color: '#1e40af', marginBottom: '4px' }}>
            {stats.completedAchievements}
          </div>
          <div style={{ fontSize: '14px', color: '#1e40af' }}>已完成成就</div>
        </div>

        <div style={{
          backgroundColor: '#dcfce7',
          padding: '20px',
          borderRadius: '12px',
          textAlign: 'center',
          border: '1px solid #4ade80'
        }}>
          <div style={{ fontSize: '32px', marginBottom: '8px' }}>📊</div>
          <div style={{ fontSize: '24px', fontWeight: '700', color: '#15803d', marginBottom: '4px' }}>
            {Math.round(stats.completionRate)}%
          </div>
          <div style={{ fontSize: '14px', color: '#15803d' }}>完成率</div>
        </div>

        <div style={{
          backgroundColor: '#f3e8ff',
          padding: '20px',
          borderRadius: '12px',
          textAlign: 'center',
          border: '1px solid #a78bfa'
        }}>
          <div style={{ fontSize: '32px', marginBottom: '8px' }}>🔥</div>
          <div style={{ fontSize: '24px', fontWeight: '700', color: '#7c3aed', marginBottom: '4px' }}>
            {levelUpHistory.length}
          </div>
          <div style={{ fontSize: '14px', color: '#7c3aed' }}>等级晋升次数</div>
        </div>
      </div>

      {/* 查看详细成就按钮 */}
      <div style={{ textAlign: 'center' }}>
        <button
          onClick={() => setActiveView('achievements')}
          style={{
            backgroundColor: '#3b82f6',
            color: 'white',
            border: 'none',
            padding: '12px 24px',
            borderRadius: '8px',
            fontSize: '16px',
            fontWeight: '600',
            cursor: 'pointer',
            transition: 'all 0.2s ease'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.backgroundColor = '#2563eb';
            e.currentTarget.style.transform = 'translateY(-1px)';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.backgroundColor = '#3b82f6';
            e.currentTarget.style.transform = 'translateY(0)';
          }}
        >
          查看详细成就列表 →
        </button>
      </div>
    </div>
  );
};

export default RewardsOverview; 