import React, { useState, useEffect } from 'react';
import { AchievementDataService } from '../../services/AchievementDataService';

interface DataManagementProps {
  dataService: AchievementDataService;
  onDataChange?: () => void;
}

interface StorageInfo {
  used: number;
  total: number;
  percentage: number;
}

interface SyncStatus {
  lastSync: Date;
  isOnline: boolean;
  hasPendingChanges: boolean;
  errorCount: number;
  lastError?: string;
}

const DataManagement: React.FC<DataManagementProps> = ({
  dataService,
  onDataChange
}) => {
  const [storageInfo, setStorageInfo] = useState<StorageInfo>({ used: 0, total: 0, percentage: 0 });
  const [syncStatus, setSyncStatus] = useState<SyncStatus | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error' | 'info'; text: string } | null>(null);

  useEffect(() => {
    updateStorageInfo();
    updateSyncStatus();
  }, [dataService]);

  const updateStorageInfo = () => {
    const info = dataService.getStorageUsage();
    setStorageInfo(info);
  };

  const updateSyncStatus = () => {
    const status = dataService.getSyncStatus();
    setSyncStatus(status);
  };

  const showMessage = (type: 'success' | 'error' | 'info', text: string) => {
    setMessage({ type, text });
    setTimeout(() => setMessage(null), 5000);
  };

  const handleExportData = async () => {
    setIsLoading(true);
    try {
      const backup = await dataService.exportData();
      const blob = new Blob([JSON.stringify(backup, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      
      const link = document.createElement('a');
      link.href = url;
      link.download = `achievement-backup-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      showMessage('success', '数据导出成功！文件已开始下载。');
    } catch (error) {
      console.error('Export error:', error);
      showMessage('error', '数据导出失败，请稍后重试。');
    } finally {
      setIsLoading(false);
    }
  };

  const handleImportData = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setIsLoading(true);
    try {
      const text = await file.text();
      const backup = JSON.parse(text);
      
      // 基本验证
      if (!backup.version || !backup.userExperience) {
        throw new Error('Invalid backup file format');
      }

      await dataService.importData(backup);
      showMessage('success', '数据导入成功！');
      onDataChange?.();
      updateStorageInfo();
    } catch (error) {
      console.error('Import error:', error);
      showMessage('error', '数据导入失败，请检查文件格式是否正确。');
    } finally {
      setIsLoading(false);
      // 清空文件输入
      event.target.value = '';
    }
  };

  const handleClearData = async () => {
    if (!window.confirm('⚠️ 确定要清空所有成就数据吗？此操作无法撤销！')) {
      return;
    }

    if (!window.confirm('🚨 最后确认：这将删除所有经验值、成就进度和历史记录。您确定要继续吗？')) {
      return;
    }

    setIsLoading(true);
    try {
      await dataService.clearAllData();
      showMessage('success', '所有数据已清空，系统已重置为初始状态。');
      onDataChange?.();
      updateStorageInfo();
    } catch (error) {
      console.error('Clear data error:', error);
      showMessage('error', '清空数据失败，请稍后重试。');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSyncData = async () => {
    setIsLoading(true);
    try {
      await dataService.forceSyncData();
      updateSyncStatus();
      showMessage('success', '数据同步完成！');
    } catch (error) {
      console.error('Sync error:', error);
      showMessage('error', '数据同步失败，请检查网络连接。');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCleanupData = async () => {
    if (!window.confirm('确定要清理旧数据吗？这将删除过期的记录和通知。')) {
      return;
    }

    setIsLoading(true);
    try {
      await dataService.cleanupOldData();
      updateStorageInfo();
      showMessage('success', '数据清理完成！');
    } catch (error) {
      console.error('Cleanup error:', error);
      showMessage('error', '数据清理失败，请稍后重试。');
    } finally {
      setIsLoading(false);
    }
  };

  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getStorageColor = (percentage: number): string => {
    if (percentage < 50) return '#10b981'; // green
    if (percentage < 80) return '#f59e0b'; // yellow
    return '#ef4444'; // red
  };

  return (
    <div style={{ 
      maxWidth: '800px', 
      margin: '0 auto', 
      padding: '20px',
      fontFamily: 'system-ui, -apple-system, sans-serif'
    }}>
      <h2 style={{ 
        margin: '0 0 24px 0', 
        fontSize: '24px', 
        fontWeight: '700', 
        color: '#0f172a',
        textAlign: 'center'
      }}>
        📊 数据管理中心
      </h2>

      {/* 消息提示 */}
      {message && (
        <div style={{
          padding: '12px 16px',
          borderRadius: '8px',
          marginBottom: '20px',
          backgroundColor: message.type === 'success' ? '#dcfce7' : 
                          message.type === 'error' ? '#fef2f2' : '#eff6ff',
          color: message.type === 'success' ? '#15803d' : 
                 message.type === 'error' ? '#dc2626' : '#1d4ed8',
          border: `1px solid ${message.type === 'success' ? '#bbf7d0' : 
                              message.type === 'error' ? '#fecaca' : '#bfdbfe'}`
        }}>
          {message.text}
        </div>
      )}

      {/* 存储使用情况 */}
      <div style={{
        backgroundColor: 'white',
        padding: '20px',
        borderRadius: '12px',
        border: '1px solid #e2e8f0',
        marginBottom: '20px'
      }}>
        <h3 style={{ margin: '0 0 16px 0', fontSize: '18px', fontWeight: '600' }}>
          💾 存储使用情况
        </h3>
        
        <div style={{ marginBottom: '12px' }}>
          <div style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginBottom: '8px'
          }}>
            <span style={{ fontSize: '14px', color: '#64748b' }}>
              已使用 {formatBytes(storageInfo.used)} / {formatBytes(storageInfo.total)}
            </span>
            <span style={{ 
              fontSize: '14px', 
              fontWeight: '600',
              color: getStorageColor(storageInfo.percentage)
            }}>
              {storageInfo.percentage.toFixed(1)}%
            </span>
          </div>
          
          <div style={{
            width: '100%',
            height: '8px',
            backgroundColor: '#e5e7eb',
            borderRadius: '4px',
            overflow: 'hidden'
          }}>
            <div
              style={{
                width: `${Math.min(storageInfo.percentage, 100)}%`,
                height: '100%',
                backgroundColor: getStorageColor(storageInfo.percentage),
                transition: 'width 0.3s ease'
              }}
            />
          </div>
        </div>
        
        {storageInfo.percentage > 80 && (
          <div style={{
            padding: '8px 12px',
            backgroundColor: '#fef3c7',
            color: '#92400e',
            borderRadius: '6px',
            fontSize: '12px',
            marginTop: '8px'
          }}>
            ⚠️ 存储空间不足，建议清理旧数据或导出备份
          </div>
        )}
      </div>

      {/* 同步状态 */}
      {syncStatus && (
        <div style={{
          backgroundColor: 'white',
          padding: '20px',
          borderRadius: '12px',
          border: '1px solid #e2e8f0',
          marginBottom: '20px'
        }}>
          <h3 style={{ margin: '0 0 16px 0', fontSize: '18px', fontWeight: '600' }}>
            🔄 同步状态
          </h3>
          
          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }}>
            <div>
              <div style={{ fontSize: '12px', color: '#64748b', marginBottom: '4px' }}>
                网络状态
              </div>
              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '6px',
                fontSize: '14px',
                fontWeight: '500'
              }}>
                <div style={{
                  width: '8px',
                  height: '8px',
                  borderRadius: '50%',
                  backgroundColor: syncStatus.isOnline ? '#10b981' : '#ef4444'
                }} />
                {syncStatus.isOnline ? '在线' : '离线'}
              </div>
            </div>
            
            <div>
              <div style={{ fontSize: '12px', color: '#64748b', marginBottom: '4px' }}>
                最后同步
              </div>
              <div style={{ fontSize: '14px', fontWeight: '500' }}>
                {syncStatus.lastSync.getTime() > 0 
                  ? syncStatus.lastSync.toLocaleString('zh-CN')
                  : '从未同步'
                }
              </div>
            </div>
            
            {syncStatus.hasPendingChanges && (
              <div>
                <div style={{ fontSize: '12px', color: '#64748b', marginBottom: '4px' }}>
                  待同步变更
                </div>
                <div style={{ fontSize: '14px', fontWeight: '500', color: '#f59e0b' }}>
                  有未同步的数据
                </div>
              </div>
            )}
            
            {syncStatus.errorCount > 0 && (
              <div>
                <div style={{ fontSize: '12px', color: '#64748b', marginBottom: '4px' }}>
                  错误计数
                </div>
                <div style={{ fontSize: '14px', fontWeight: '500', color: '#ef4444' }}>
                  {syncStatus.errorCount} 次失败
                </div>
              </div>
            )}
          </div>
          
          {syncStatus.lastError && (
            <div style={{
              marginTop: '12px',
              padding: '8px 12px',
              backgroundColor: '#fef2f2',
              color: '#dc2626',
              borderRadius: '6px',
              fontSize: '12px'
            }}>
              最后错误: {syncStatus.lastError}
            </div>
          )}
        </div>
      )}

      {/* 操作按钮 */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
        gap: '16px'
      }}>
        {/* 导出数据 */}
        <button
          onClick={handleExportData}
          disabled={isLoading}
          style={{
            backgroundColor: '#3b82f6',
            color: 'white',
            border: 'none',
            padding: '16px',
            borderRadius: '12px',
            fontSize: '14px',
            fontWeight: '600',
            cursor: isLoading ? 'not-allowed' : 'pointer',
            opacity: isLoading ? 0.6 : 1,
            transition: 'all 0.2s ease',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            gap: '8px'
          }}
          onMouseEnter={(e) => {
            if (!isLoading) {
              e.currentTarget.style.backgroundColor = '#2563eb';
              e.currentTarget.style.transform = 'translateY(-1px)';
            }
          }}
          onMouseLeave={(e) => {
            if (!isLoading) {
              e.currentTarget.style.backgroundColor = '#3b82f6';
              e.currentTarget.style.transform = 'translateY(0)';
            }
          }}
        >
          <span style={{ fontSize: '24px' }}>📤</span>
          <span>导出数据</span>
          <span style={{ fontSize: '12px', opacity: 0.8 }}>
            将数据保存为备份文件
          </span>
        </button>

        {/* 导入数据 */}
        <label style={{
          backgroundColor: '#10b981',
          color: 'white',
          border: 'none',
          padding: '16px',
          borderRadius: '12px',
          fontSize: '14px',
          fontWeight: '600',
          cursor: isLoading ? 'not-allowed' : 'pointer',
          opacity: isLoading ? 0.6 : 1,
          transition: 'all 0.2s ease',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          gap: '8px'
        }}>
          <span style={{ fontSize: '24px' }}>📥</span>
          <span>导入数据</span>
          <span style={{ fontSize: '12px', opacity: 0.8 }}>
            从备份文件恢复数据
          </span>
          <input
            type="file"
            accept=".json"
            onChange={handleImportData}
            disabled={isLoading}
            style={{ display: 'none' }}
          />
        </label>

        {/* 同步数据 */}
        <button
          onClick={handleSyncData}
          disabled={isLoading || !syncStatus?.isOnline}
          style={{
            backgroundColor: syncStatus?.isOnline ? '#8b5cf6' : '#9ca3af',
            color: 'white',
            border: 'none',
            padding: '16px',
            borderRadius: '12px',
            fontSize: '14px',
            fontWeight: '600',
            cursor: isLoading || !syncStatus?.isOnline ? 'not-allowed' : 'pointer',
            opacity: isLoading || !syncStatus?.isOnline ? 0.6 : 1,
            transition: 'all 0.2s ease',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            gap: '8px'
          }}
        >
          <span style={{ fontSize: '24px' }}>🔄</span>
          <span>同步数据</span>
          <span style={{ fontSize: '12px', opacity: 0.8 }}>
            与云端服务器同步
          </span>
        </button>

        {/* 清理数据 */}
        <button
          onClick={handleCleanupData}
          disabled={isLoading}
          style={{
            backgroundColor: '#f59e0b',
            color: 'white',
            border: 'none',
            padding: '16px',
            borderRadius: '12px',
            fontSize: '14px',
            fontWeight: '600',
            cursor: isLoading ? 'not-allowed' : 'pointer',
            opacity: isLoading ? 0.6 : 1,
            transition: 'all 0.2s ease',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            gap: '8px'
          }}
        >
          <span style={{ fontSize: '24px' }}>🧹</span>
          <span>清理数据</span>
          <span style={{ fontSize: '12px', opacity: 0.8 }}>
            删除过期记录和通知
          </span>
        </button>

        {/* 重置所有数据 */}
        <button
          onClick={handleClearData}
          disabled={isLoading}
          style={{
            backgroundColor: '#ef4444',
            color: 'white',
            border: 'none',
            padding: '16px',
            borderRadius: '12px',
            fontSize: '14px',
            fontWeight: '600',
            cursor: isLoading ? 'not-allowed' : 'pointer',
            opacity: isLoading ? 0.6 : 1,
            transition: 'all 0.2s ease',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            gap: '8px'
          }}
        >
          <span style={{ fontSize: '24px' }}>🗑️</span>
          <span>重置数据</span>
          <span style={{ fontSize: '12px', opacity: 0.8 }}>
            清空所有成就数据
          </span>
        </button>
      </div>

      {/* 使用说明 */}
      <div style={{
        marginTop: '32px',
        padding: '20px',
        backgroundColor: '#f8fafc',
        borderRadius: '12px',
        border: '1px solid #e2e8f0'
      }}>
        <h4 style={{ margin: '0 0 12px 0', fontSize: '16px', fontWeight: '600' }}>
          💡 使用说明
        </h4>
        <ul style={{ margin: 0, paddingLeft: '20px', fontSize: '14px', lineHeight: '1.6' }}>
          <li><strong>导出数据：</strong>将所有成就数据保存为JSON文件，可用于备份或迁移</li>
          <li><strong>导入数据：</strong>从之前导出的备份文件恢复数据，会覆盖当前数据</li>
          <li><strong>同步数据：</strong>将本地数据与云端服务器同步（需要网络连接）</li>
          <li><strong>清理数据：</strong>删除过期的记录和通知，释放存储空间</li>
          <li><strong>重置数据：</strong>完全清空所有数据，谨慎使用！</li>
        </ul>
      </div>
    </div>
  );
};

export default DataManagement; 