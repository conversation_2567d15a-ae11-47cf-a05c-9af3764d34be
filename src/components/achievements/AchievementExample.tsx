import React, { useState, useEffect } from 'react';
import { RewardsOverview, AchievementNotification } from './index';
import { 
  Achievement, 
  UserLevel, 
  LevelUpHistory, 
  UserAchievementProgress, 
  AchievementStats,
  AchievementNotification as NotificationType
} from '../../types/achievements';
import { ExperienceSystem } from '../../utils/ExperienceSystem';
import { AchievementService } from '../../services/AchievementService';

// 示例数据生成器
const generateSampleData = () => {
  const experienceSystem = new ExperienceSystem();
  const achievementService = new AchievementService();
  
  // 模拟当前用户等级
  const currentLevel: UserLevel = {
    level: 5,
    requiredExperience: 25000,
    experienceRequired: 25000,
    name: '专注达人',
    description: '已经掌握了基本的专注技巧',
    icon: '🎯',
    rewards: [
      { type: 'badge', value: 'focus_master' }
    ]
  };

  // 模拟总经验值
  const totalExperience = 28500;

  // 模拟等级晋升历史
  const levelUpHistory: LevelUpHistory[] = [
    {
      previousLevel: 4,
      newLevel: 5,
      experienceGained: 3500,
      totalExperience: 28500,
      timestamp: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
      rewards: [
        { 
          id: 'badge_focus_master',
          name: '专注达人徽章',
          description: '达到等级5的奖励',
          type: 'badge' as any,
          value: 'focus_master',
          rarity: 'rare' as const
        }
      ]
    },
    {
      previousLevel: 3,
      newLevel: 4,
      experienceGained: 2800,
      totalExperience: 25000,
      timestamp: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
    },
    {
      previousLevel: 2,
      newLevel: 3,
      experienceGained: 2000,
      totalExperience: 22200,
      timestamp: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000),
    }
  ];

  // 模拟最近解锁的成就
  const recentAchievements: Achievement[] = [
    {
      id: 'perfect_session_3',
      name: '完美三连击',
      description: '连续3次完美会话',
      type: 'streak' as any,
      category: 'focus' as any,
      icon: '🔥',
      experienceReward: 500,
      requirement: {
        type: 'perfect_sessions_streak',
        value: 3,
        description: '连续完成3次完美专注会话'
      }
    },
    {
      id: 'morning_warrior',
      name: '晨间战士',
      description: '在早上6-9点间完成会话',
      type: 'special' as any,
      category: 'time' as any,
      icon: '🌅',
      experienceReward: 200,
      requirement: {
        type: 'morning_session',
        value: 1,
        description: '在早晨时间段完成专注会话'
      }
    }
  ];

  // 模拟成就统计
  const stats: AchievementStats = {
    totalAchievements: 16,
    completedAchievements: 8,
    completionRate: 50,
    lastAchievement: {
      achievement: recentAchievements[0],
      completedAt: new Date(Date.now() - 2 * 60 * 60 * 1000)
    },
    categoryCounts: {
      focus: { total: 4, completed: 2 },
      posture: { total: 3, completed: 1 },
      time: { total: 3, completed: 2 },
      consistency: { total: 3, completed: 2 },
      improvement: { total: 3, completed: 1 }
    }
  };

  return {
    currentLevel,
    totalExperience,
    levelUpHistory,
    recentAchievements,
    stats,
    achievements: [], // 这里应该从配置中获取
    userProgress: [] as UserAchievementProgress[],
    completedAchievements: new Set(['perfect_session_3', 'morning_warrior'])
  };
};

const AchievementExample: React.FC = () => {
  const [notifications, setNotifications] = useState<NotificationType[]>([]);
  const sampleData = generateSampleData();

  // 模拟通知
  useEffect(() => {
    const timer = setTimeout(() => {
      const newNotifications: NotificationType[] = [
        {
          type: 'achievement',
          title: '🎉 成就解锁！',
          message: '恭喜您解锁了"完美三连击"成就！',
          icon: '🔥',
          experience: 500,
          createdAt: new Date(),
          isRead: false,
          achievement: sampleData.recentAchievements[0]
        },
        {
          type: 'level_up',
          title: '🎊 等级提升！',
          message: '恭喜您升级到等级5 - 专注达人！',
          icon: '⬆️',
          experience: 3500,
          createdAt: new Date(),
          isRead: false,
          level: sampleData.currentLevel
        }
      ];
      setNotifications(newNotifications);
    }, 2000);

    return () => clearTimeout(timer);
  }, []);

  const handleDismissNotification = (index: number) => {
    setNotifications(prev => prev.filter((_, i) => i !== index));
  };

  const handleDismissAllNotifications = () => {
    setNotifications([]);
  };

  const handleAchievementClick = (achievement: Achievement) => {
    console.log('Achievement clicked:', achievement);
    // 这里可以显示成就详情弹窗
  };

  return (
    <div style={{ minHeight: '100vh', backgroundColor: '#f8fafc' }}>
      {/* 奖励系统概览 */}
      <RewardsOverview
        currentLevel={sampleData.currentLevel}
        totalExperience={sampleData.totalExperience}
        levelUpHistory={sampleData.levelUpHistory}
        recentAchievements={sampleData.recentAchievements}
        achievements={sampleData.achievements}
        userProgress={sampleData.userProgress}
        completedAchievements={sampleData.completedAchievements}
        stats={sampleData.stats}
        onAchievementClick={handleAchievementClick}
      />

      {/* 成就通知 */}
      <AchievementNotification
        notifications={notifications}
        onDismiss={handleDismissNotification}
        onDismissAll={handleDismissAllNotifications}
        autoHideDelay={8000}
      />

      {/* 示例说明 */}
      <div style={{
        position: 'fixed',
        bottom: '20px',
        left: '20px',
        backgroundColor: 'rgba(0,0,0,0.8)',
        color: 'white',
        padding: '12px 16px',
        borderRadius: '8px',
        fontSize: '14px',
        maxWidth: '300px'
      }}>
        <h4 style={{ margin: '0 0 8px 0', fontSize: '16px' }}>🚀 成就系统演示</h4>
        <p style={{ margin: '0 0 8px 0', lineHeight: '1.4' }}>
          这是成就和奖励系统的完整演示。通知会在2秒后自动出现。
        </p>
        <p style={{ margin: 0, fontSize: '12px', opacity: 0.8 }}>
          点击成就卡片查看详情，通知会自动消失或可手动关闭。
        </p>
      </div>
    </div>
  );
};

export default AchievementExample; 