import React, { useState, useEffect } from 'react';
import {
  AchievementDisplay,
  RewardsOverview,
  AchievementNotification,
  DataManagement,
  AchievementNotificationType
} from './index';
import { ExperienceSystem } from '../../utils/ExperienceSystem';
import { AchievementService } from '../../services/AchievementService';
import { AchievementDataService } from '../../services/AchievementDataService';
import { ACHIEVEMENTS } from '../../data/achievementsConfig';
import { 
  Achievement, 
  UserLevel, 
  LevelUpHistory, 
  UserAchievementProgress, 
  AchievementStats 
} from '../../types/achievements';

// 页面导航枚举
enum AchievementPage {
  OVERVIEW = 'overview',
  ACHIEVEMENTS = 'achievements',
  DATA_MANAGEMENT = 'data_management'
}

const AchievementSystemExample: React.FC = () => {
  // 系统服务实例
  const [experienceSystem] = useState(() => new ExperienceSystem());
  const [achievementService] = useState(() => new AchievementService());
  const [dataService] = useState(() => new AchievementDataService());

  // 界面状态
  const [currentPage, setCurrentPage] = useState<AchievementPage>(AchievementPage.OVERVIEW);
  const [notifications, setNotifications] = useState<AchievementNotificationType[]>([]);
  const [isInitialized, setIsInitialized] = useState(false);

  // 数据状态
  const [currentLevel, setCurrentLevel] = useState<UserLevel | null>(null);
  const [totalExperience, setTotalExperience] = useState(0);
  const [levelUpHistory, setLevelUpHistory] = useState<LevelUpHistory[]>([]);
  const [recentAchievements, setRecentAchievements] = useState<Achievement[]>([]);
  const [userProgress, setUserProgress] = useState<UserAchievementProgress[]>([]);
  const [completedAchievements, setCompletedAchievements] = useState<Set<string>>(new Set());
  const [stats, setStats] = useState<AchievementStats | null>(null);

  // 初始化系统
  useEffect(() => {
    initializeSystem();
  }, []);

  const initializeSystem = async () => {
    try {
      // 初始化数据服务
      await dataService.initialize();

      // 加载用户数据
      await loadUserData();

      // 设置数据变更监听
      const unsubscribe = dataService.addChangeListener(handleDataChange);

      setIsInitialized(true);
      console.log('🎮 Achievement system initialized successfully');

      // 模拟一些初始通知
      setTimeout(() => {
        simulateInitialNotifications();
      }, 2000);

      // 清理函数
      return () => {
        unsubscribe();
      };
    } catch (error) {
      console.error('❌ Failed to initialize achievement system:', error);
    }
  };

  const loadUserData = async () => {
    try {
      // 加载用户经验值
      const userExp = await dataService.getUserExperience();
      setTotalExperience(userExp.totalExperience);

      // 获取当前等级信息
      const level = experienceSystem.getLevelInfo(userExp.currentLevel);
      setCurrentLevel({
        ...level,
        level: level.level || 1,
        name: level.name || '专注新手',
        description: level.description || '刚开始专注训练',
        requiredExperience: level.requiredExperience || 1000,
        rewards: level.rewards || [],
        experienceRequired: level.requiredExperience || 1000,
        icon: (level.level || 1) <= 2 ? '🌱' : 
              (level.level || 1) <= 4 ? '🌿' :
              (level.level || 1) <= 6 ? '🌳' :
              (level.level || 1) <= 8 ? '🎯' : '👑'
      });

      // 加载成就进度
      const progress = await dataService.getAchievementProgress();
      setUserProgress(progress);

      // 设置已完成的成就
      const completed = new Set(progress.filter(p => p.isCompleted).map(p => p.achievementId));
      setCompletedAchievements(completed);

      // 加载等级晋升历史
      const history = await dataService.getLevelUpHistory();
      setLevelUpHistory(history);

      // 获取最近解锁的成就
      const recentlyCompleted = progress
        .filter(p => p.isCompleted && p.completedAt)
        .sort((a, b) => (b.completedAt?.getTime() || 0) - (a.completedAt?.getTime() || 0))
        .slice(0, 5)
        .map(p => ACHIEVEMENTS.find((a: Achievement) => a.id === p.achievementId))
        .filter(Boolean) as Achievement[];
      
      setRecentAchievements(recentlyCompleted);

      // 计算统计数据
      const calculatedStats = await dataService.calculateStats();
      setStats(calculatedStats);

    } catch (error) {
      console.error('❌ Error loading user data:', error);
    }
  };

  const handleDataChange = (key: string, data: any) => {
    console.log('📊 Data changed:', key, data);
    // 在实际应用中，这里可以根据变更的数据类型来更新对应的状态
    if (key.includes('experience') || key.includes('achievement')) {
      loadUserData();
    }
  };

  const simulateInitialNotifications = () => {
    const initialNotifications: AchievementNotificationType[] = [
      {
        type: 'achievement',
        title: '🎉 欢迎使用成就系统！',
        message: '开始您的专注之旅，解锁各种精彩成就！',
        icon: '🚀',
        experience: 0,
        createdAt: new Date(),
        isRead: false
      }
    ];

    setNotifications(initialNotifications);
  };

  const handleSimulateAchievement = async () => {
    // 模拟成就解锁
    const achievement = ACHIEVEMENTS[Math.floor(Math.random() * ACHIEVEMENTS.length)];
    
    // 更新成就进度
    await dataService.updateAchievementProgress(achievement.id, {
      progress: achievement.requirement.value,
      maxProgress: achievement.requirement.value,
      isCompleted: true,
      completedAt: new Date()
    });

    // 添加经验值记录
    await dataService.addExperienceRecord({
      amount: achievement.experienceReward,
      source: 'achievement',
      sourceId: achievement.id,
      multiplier: 1,
      earnedAt: new Date(),
      description: `完成成就：${achievement.name}`
    });

    // 添加通知
    const notification: AchievementNotificationType = {
      type: 'achievement',
      title: '🏆 成就解锁！',
      message: `恭喜您解锁了"${achievement.name}"成就！`,
      icon: achievement.icon,
      experience: achievement.experienceReward,
      createdAt: new Date(),
      isRead: false,
      achievement
    };

    setNotifications(prev => [notification, ...prev]);
    await dataService.addNotification(notification);
  };

  const handleSimulateLevelUp = async () => {
    // 模拟等级提升
    const newLevel = (currentLevel?.level || 1) + 1;
    const experienceGained = 3000;

    // 更新用户经验值
    const currentExp = await dataService.getUserExperience();
    await dataService.saveUserExperience({
      ...currentExp,
      currentLevel: newLevel,
      totalExperience: currentExp.totalExperience + experienceGained,
      currentExperience: 0,
      experienceToNextLevel: experienceSystem.getLevelInfo(newLevel + 1).requiredExperience
    });

    // 添加等级晋升历史
    const levelUpRecord: LevelUpHistory = {
      previousLevel: currentLevel?.level || 1,
      newLevel,
      experienceGained,
      totalExperience: currentExp.totalExperience + experienceGained,
      timestamp: new Date()
    };

    await dataService.addLevelUpRecord(levelUpRecord);

    // 添加通知
    const levelInfo = experienceSystem.getLevelInfo(newLevel);
    const notification: AchievementNotificationType = {
      type: 'level_up',
      title: '🎊 等级提升！',
      message: `恭喜您升级到等级 ${newLevel} - ${levelInfo.name}！`,
      icon: '⬆️',
      experience: experienceGained,
      createdAt: new Date(),
      isRead: false
    };

    setNotifications(prev => [notification, ...prev]);
    await dataService.addNotification(notification);
  };

  const handleDismissNotification = (index: number) => {
    setNotifications(prev => prev.filter((_, i) => i !== index));
  };

  const handleDismissAllNotifications = () => {
    setNotifications([]);
  };

  const handleAchievementClick = (achievement: Achievement) => {
    console.log('Achievement clicked:', achievement);
    // 在实际应用中，这里可以显示成就详情弹窗
  };

  const renderPage = () => {
    if (!isInitialized || !currentLevel || !stats) {
      return (
        <div style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '400px',
          fontSize: '18px',
          color: '#64748b'
        }}>
          🔄 正在初始化成就系统...
        </div>
      );
    }

    switch (currentPage) {
      case AchievementPage.OVERVIEW:
        return (
          <RewardsOverview
            currentLevel={currentLevel}
            totalExperience={totalExperience}
            levelUpHistory={levelUpHistory}
            recentAchievements={recentAchievements}
            achievements={achievementsConfig.achievements}
            userProgress={userProgress}
            completedAchievements={completedAchievements}
            stats={stats}
            onAchievementClick={handleAchievementClick}
          />
        );

      case AchievementPage.ACHIEVEMENTS:
        return (
          <AchievementDisplay
            achievements={achievementsConfig.achievements}
            userProgress={userProgress}
            completedAchievements={completedAchievements}
            stats={stats}
            onAchievementClick={handleAchievementClick}
          />
        );

      case AchievementPage.DATA_MANAGEMENT:
        return (
          <DataManagement
            dataService={dataService}
            onDataChange={loadUserData}
          />
        );

      default:
        return null;
    }
  };

  if (!isInitialized) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        fontSize: '20px',
        color: '#64748b'
      }}>
        🚀 正在启动成就系统...
      </div>
    );
  }

  return (
    <div style={{ minHeight: '100vh', backgroundColor: '#f8fafc' }}>
      {/* 导航栏 */}
      <div style={{
        backgroundColor: 'white',
        borderBottom: '1px solid #e2e8f0',
        padding: '0 20px'
      }}>
        <div style={{
          maxWidth: '1200px',
          margin: '0 auto',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          height: '64px'
        }}>
          <h1 style={{
            margin: 0,
            fontSize: '20px',
            fontWeight: '700',
            color: '#0f172a'
          }}>
            🏆 成就系统演示
          </h1>

          <div style={{ display: 'flex', gap: '12px' }}>
            {[
              { key: AchievementPage.OVERVIEW, label: '📊 概览', icon: '📊' },
              { key: AchievementPage.ACHIEVEMENTS, label: '🏆 成就', icon: '🏆' },
              { key: AchievementPage.DATA_MANAGEMENT, label: '⚙️ 数据管理', icon: '⚙️' }
            ].map(tab => (
              <button
                key={tab.key}
                onClick={() => setCurrentPage(tab.key)}
                style={{
                  backgroundColor: currentPage === tab.key ? '#3b82f6' : 'transparent',
                  color: currentPage === tab.key ? 'white' : '#64748b',
                  border: '1px solid',
                  borderColor: currentPage === tab.key ? '#3b82f6' : '#e2e8f0',
                  padding: '8px 16px',
                  borderRadius: '8px',
                  fontSize: '14px',
                  fontWeight: '500',
                  cursor: 'pointer',
                  transition: 'all 0.2s ease'
                }}
                onMouseEnter={(e) => {
                  if (currentPage !== tab.key) {
                    e.currentTarget.style.backgroundColor = '#f1f5f9';
                    e.currentTarget.style.borderColor = '#cbd5e1';
                  }
                }}
                onMouseLeave={(e) => {
                  if (currentPage !== tab.key) {
                    e.currentTarget.style.backgroundColor = 'transparent';
                    e.currentTarget.style.borderColor = '#e2e8f0';
                  }
                }}
              >
                {tab.label}
              </button>
            ))}
          </div>

          {/* 模拟操作按钮 */}
          <div style={{ display: 'flex', gap: '8px' }}>
            <button
              onClick={handleSimulateAchievement}
              style={{
                backgroundColor: '#10b981',
                color: 'white',
                border: 'none',
                padding: '8px 12px',
                borderRadius: '6px',
                fontSize: '12px',
                fontWeight: '500',
                cursor: 'pointer'
              }}
            >
              🏆 模拟成就
            </button>
            <button
              onClick={handleSimulateLevelUp}
              style={{
                backgroundColor: '#8b5cf6',
                color: 'white',
                border: 'none',
                padding: '8px 12px',
                borderRadius: '6px',
                fontSize: '12px',
                fontWeight: '500',
                cursor: 'pointer'
              }}
            >
              ⬆️ 模拟升级
            </button>
          </div>
        </div>
      </div>

      {/* 页面内容 */}
      <div style={{ padding: '20px' }}>
        {renderPage()}
      </div>

      {/* 成就通知 */}
      <AchievementNotification
        notifications={notifications}
        onDismiss={handleDismissNotification}
        onDismissAll={handleDismissAllNotifications}
        autoHideDelay={6000}
      />

      {/* 状态栏 */}
      <div style={{
        position: 'fixed',
        bottom: '20px',
        left: '20px',
        backgroundColor: 'rgba(0,0,0,0.8)',
        color: 'white',
        padding: '12px 16px',
        borderRadius: '8px',
        fontSize: '12px',
        maxWidth: '300px'
      }}>
        <div style={{ fontWeight: '600', marginBottom: '4px' }}>
          🎮 系统状态
        </div>
        <div>等级: {currentLevel?.level} - {currentLevel?.name}</div>
        <div>总经验: {totalExperience.toLocaleString()}</div>
        <div>已完成成就: {completedAchievements.size}/{achievementsConfig.achievements.length}</div>
        <div style={{ fontSize: '10px', opacity: 0.7, marginTop: '4px' }}>
          点击右上角的模拟按钮体验成就解锁和升级
        </div>
      </div>
    </div>
  );
};

export default AchievementSystemExample; 