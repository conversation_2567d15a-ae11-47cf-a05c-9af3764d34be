import React, { useState, useEffect } from 'react';
import { Achievement, AchievementNotification } from '../../types/achievements';

interface AchievementNotificationProps {
  notifications: AchievementNotification[];
  onDismiss: (index: number) => void;
  onDismissAll: () => void;
  autoHideDelay?: number;
}

interface NotificationItemProps {
  notification: AchievementNotification;
  onDismiss: () => void;
  autoHide?: boolean;
  delay?: number;
}

const NotificationItem: React.FC<NotificationItemProps> = ({
  notification,
  onDismiss,
  autoHide = true,
  delay = 5000
}) => {
  const [isVisible, setIsVisible] = useState(true);
  const [isAnimating, setIsAnimating] = useState(false);

  useEffect(() => {
    if (autoHide) {
      const timer = setTimeout(() => {
        handleDismiss();
      }, delay);

      return () => clearTimeout(timer);
    }
  }, [autoHide, delay]);

  const handleDismiss = () => {
    setIsAnimating(true);
    setTimeout(() => {
      setIsVisible(false);
      onDismiss();
    }, 300);
  };

  if (!isVisible) return null;

  const getNotificationStyles = () => {
    const baseStyles = {
      padding: '16px 20px',
      borderRadius: '12px',
      marginBottom: '12px',
      cursor: 'pointer',
      position: 'relative' as const,
      overflow: 'hidden' as const,
      transition: 'all 0.3s ease',
      transform: isAnimating ? 'translateX(100%)' : 'translateX(0)',
      opacity: isAnimating ? 0 : 1,
      border: '2px solid'
    };

    switch (notification.type) {
      case 'achievement':
        return {
          ...baseStyles,
          background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',
          borderColor: '#047857',
          color: 'white'
        };
      case 'level_up':
        return {
          ...baseStyles,
          background: 'linear-gradient(135deg, #3b82f6 0%, #2563eb 100%)',
          borderColor: '#1d4ed8',
          color: 'white'
        };
      case 'task_complete':
        return {
          ...baseStyles,
          background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',
          borderColor: '#b45309',
          color: 'white'
        };
      case 'milestone':
        return {
          ...baseStyles,
          background: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',
          borderColor: '#6d28d9',
          color: 'white'
        };
      default:
        return {
          ...baseStyles,
          backgroundColor: '#f3f4f6',
          borderColor: '#d1d5db',
          color: '#374151'
        };
    }
  };

  const getIcon = () => {
    if (notification.icon) return notification.icon;
    
    switch (notification.type) {
      case 'achievement':
        return '🏆';
      case 'level_up':
        return '⬆️';
      case 'task_complete':
        return '✅';
      case 'milestone':
        return '🎯';
      default:
        return '🔔';
    }
  };

  return (
    <div
      style={getNotificationStyles()}
      onClick={handleDismiss}
    >
      {/* 背景装饰 */}
      <div style={{
        position: 'absolute',
        top: 0,
        right: 0,
        width: '100px',
        height: '100%',
        background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.1))',
        pointerEvents: 'none'
      }} />

      <div style={{
        display: 'flex',
        alignItems: 'center',
        position: 'relative',
        zIndex: 1
      }}>
        <div style={{
          fontSize: '32px',
          marginRight: '16px',
          animation: notification.type === 'achievement' || notification.type === 'level_up' 
            ? 'bounce 0.6s ease-in-out' : 'none'
        }}>
          {getIcon()}
        </div>

        <div style={{ flex: 1 }}>
          <h4 style={{
            margin: '0 0 4px 0',
            fontSize: '16px',
            fontWeight: '700'
          }}>
            {notification.title}
          </h4>
          <p style={{
            margin: 0,
            fontSize: '14px',
            opacity: 0.9,
            lineHeight: '1.4'
          }}>
            {notification.message}
          </p>
          
          {notification.experience && (
            <div style={{
              marginTop: '8px',
              display: 'flex',
              alignItems: 'center',
              gap: '4px',
              fontSize: '12px',
              fontWeight: '600'
            }}>
              <span>⭐</span>
              <span>+{notification.experience} EXP</span>
            </div>
          )}
        </div>

        <div style={{
          fontSize: '20px',
          opacity: 0.7,
          marginLeft: '12px'
        }}>
          ×
        </div>
      </div>

      {/* 进度条 */}
      {autoHide && (
        <div style={{
          position: 'absolute',
          bottom: 0,
          left: 0,
          height: '3px',
          backgroundColor: 'rgba(255,255,255,0.3)',
          width: '100%',
          overflow: 'hidden'
        }}>
          <div style={{
            height: '100%',
            backgroundColor: 'rgba(255,255,255,0.8)',
            animation: `shrink ${delay}ms linear forwards`
          }} />
        </div>
      )}

      <style>
        {`
          @keyframes bounce {
            0%, 20%, 53%, 80%, 100% {
              transform: translate3d(0,0,0);
            }
            40%, 43% {
              transform: translate3d(0,-8px,0);
            }
            70% {
              transform: translate3d(0,-4px,0);
            }
            90% {
              transform: translate3d(0,-2px,0);
            }
          }
          
          @keyframes shrink {
            from {
              width: 100%;
            }
            to {
              width: 0%;
            }
          }
        `}
      </style>
    </div>
  );
};

const AchievementNotificationComponent: React.FC<AchievementNotificationProps> = ({
  notifications,
  onDismiss,
  onDismissAll,
  autoHideDelay = 5000
}) => {
  if (notifications.length === 0) {
    return null;
  }

  return (
    <div style={{
      position: 'fixed',
      top: '20px',
      right: '20px',
      width: '400px',
      maxWidth: 'calc(100vw - 40px)',
      zIndex: 1000,
      pointerEvents: 'none'
    }}>
      {/* 批量关闭按钮 */}
      {notifications.length > 1 && (
        <div style={{
          marginBottom: '12px',
          textAlign: 'right',
          pointerEvents: 'auto'
        }}>
          <button
            onClick={onDismissAll}
            style={{
              backgroundColor: 'rgba(0,0,0,0.7)',
              color: 'white',
              border: 'none',
              padding: '8px 12px',
              borderRadius: '6px',
              fontSize: '12px',
              fontWeight: '500',
              cursor: 'pointer',
              transition: 'all 0.2s ease'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = 'rgba(0,0,0,0.8)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = 'rgba(0,0,0,0.7)';
            }}
          >
            全部关闭 ({notifications.length})
          </button>
        </div>
      )}

      {/* 通知列表 */}
      <div style={{ pointerEvents: 'auto' }}>
        {notifications.map((notification, index) => (
          <NotificationItem
            key={`${notification.type}-${notification.createdAt.getTime()}-${index}`}
            notification={notification}
            onDismiss={() => onDismiss(index)}
            autoHide={true}
            delay={autoHideDelay}
          />
        ))}
      </div>
    </div>
  );
};

export default AchievementNotificationComponent; 