import React, { useState, useEffect, useCallback } from 'react'
import { CropType, CropData, CropStage, CropQuality } from '../types/crop'
import { CropVisualizer, CropGridVisualizer } from './CropVisualizer'
import { CropSelectionModal } from './CropSelectionModal'
import { FarmSystemIntegrator } from '../adapters/FarmSystemIntegrator'

interface FarmPosition {
  x: number
  y: number
  crop?: CropData
}

interface FarmInterfaceProps {
  gridWidth?: number
  gridHeight?: number
  userLevel?: number
  userExperience?: number
  onLevelUp?: (newLevel: number) => void
  onExperienceGain?: (experience: number) => void
}

interface BehaviorSession {
  id: string
  behaviorType: string
  startTime: Date
  endTime?: Date
  isActive: boolean
  focusScore?: number
  quality?: number
}

/**
 * 农场界面主组件
 * 提供完整的作物管理和用户交互功能
 */
export const FarmInterface: React.FC<FarmInterfaceProps> = ({
  gridWidth = 8,
  gridHeight = 6,
  userLevel = 1,
  userExperience = 0,
  onLevelUp,
  onExperienceGain
}) => {
  // 状态管理
  const [farmGrid, setFarmGrid] = useState<FarmPosition[][]>([])
  const [selectedPosition, setSelectedPosition] = useState<{ x: number; y: number } | null>(null)
  const [showCropSelection, setShowCropSelection] = useState(false)
  const [unlockedCropTypes, setUnlockedCropTypes] = useState<CropType[]>([])
  const [activeSessions, setActiveSessions] = useState<BehaviorSession[]>([])
  const [farmIntegrator, setFarmIntegrator] = useState<FarmSystemIntegrator | null>(null)
  const [selectedCrop, setSelectedCrop] = useState<CropData | null>(null)
  const [showCropDetails, setShowCropDetails] = useState(false)

  // 初始化农场网格
  useEffect(() => {
    const grid: FarmPosition[][] = []
    for (let y = 0; y < gridHeight; y++) {
      const row: FarmPosition[] = []
      for (let x = 0; x < gridWidth; x++) {
        row.push({ x, y })
      }
      grid.push(row)
    }
    setFarmGrid(grid)
  }, [gridWidth, gridHeight])

  // 初始化农场集成器
  useEffect(() => {
    const integrator = new FarmSystemIntegrator()
    setFarmIntegrator(integrator)

    // 监听系统事件
    const handleCropPlanted = (event: any) => {
      console.log('作物种植:', event.detail)
      updateFarmGrid()
    }

    const handleCropHarvested = (event: any) => {
      console.log('作物收获:', event.detail)
      if (onExperienceGain) {
        onExperienceGain(event.detail.experience || 10)
      }
      updateFarmGrid()
    }

    const handleBehaviorSessionStart = (event: any) => {
      console.log('行为会话开始:', event.detail)
      const session: BehaviorSession = {
        id: Date.now().toString(),
        behaviorType: event.detail.behaviorType,
        startTime: new Date(),
        isActive: true
      }
      setActiveSessions(prev => [...prev, session])
    }

    const handleBehaviorSessionEnd = (event: any) => {
      console.log('行为会话结束:', event.detail)
      setActiveSessions(prev => 
        prev.map(session => 
          session.behaviorType === event.detail.behaviorType && session.isActive
            ? { ...session, endTime: new Date(), isActive: false, ...event.detail }
            : session
        )
      )
    }

    // 监听事件
    window.addEventListener('cropPlanted', handleCropPlanted)
    window.addEventListener('cropHarvested', handleCropHarvested)
    window.addEventListener('behaviorSessionStart', handleBehaviorSessionStart)
    window.addEventListener('behaviorSessionEnd', handleBehaviorSessionEnd)

    return () => {
      window.removeEventListener('cropPlanted', handleCropPlanted)
      window.removeEventListener('cropHarvested', handleCropHarvested)
      window.removeEventListener('behaviorSessionStart', handleBehaviorSessionStart)
      window.removeEventListener('behaviorSessionEnd', handleBehaviorSessionEnd)
    }
  }, [onExperienceGain])

  // 更新解锁的作物类型
  useEffect(() => {
    const unlocked = Object.values(CropType).filter(cropType => {
      // 根据用户等级解锁作物
      const unlockLevel = getUnlockLevel(cropType)
      return userLevel >= unlockLevel
    })
    setUnlockedCropTypes(unlocked)
  }, [userLevel])

  // 获取作物解锁等级
  const getUnlockLevel = (cropType: CropType): number => {
    const unlockLevels: Record<CropType, number> = {
      [CropType.KNOWLEDGE_FLOWER]: 1,
      [CropType.STRENGTH_TREE]: 2,
      [CropType.TIME_VEGGIE]: 3,
      [CropType.MEDITATION_LOTUS]: 4,
      [CropType.FOCUS_FLOWER]: 5,
      [CropType.READING_VINE]: 6,
      [CropType.SOCIAL_FRUIT]: 7
    }
    return unlockLevels[cropType] || 1
  }

  // 更新农场网格数据
  const updateFarmGrid = useCallback(() => {
    if (!farmIntegrator) return

    // 从集成器获取作物数据
    const crops = farmIntegrator.getAllCrops()
    
    setFarmGrid(prev => 
      prev.map(row =>
        row.map(position => {
          const crop = crops.find(c => c.position?.x === position.x && c.position?.y === position.y)
          return { ...position, crop }
        })
      )
    )
  }, [farmIntegrator])

  // 处理网格点击
  const handleGridClick = (x: number, y: number) => {
    const position = farmGrid[y]?.[x]
    if (!position) return

    if (position.crop) {
      // 显示作物详情
      setSelectedCrop(position.crop)
      setShowCropDetails(true)
    } else {
      // 显示作物选择
      setSelectedPosition({ x, y })
      setShowCropSelection(true)
    }
  }

  // 处理作物选择
  const handleCropSelect = async (cropType: CropType) => {
    if (!selectedPosition || !farmIntegrator) return

    try {
      await farmIntegrator.plantCrop(
        cropType,
        selectedPosition.x,
        selectedPosition.y
      )
      
      setShowCropSelection(false)
      setSelectedPosition(null)
      updateFarmGrid()
    } catch (error) {
      console.error('种植作物失败:', error)
      alert('种植失败: ' + (error instanceof Error ? error.message : '未知错误'))
    }
  }

  // 处理作物收获
  const handleCropHarvest = async (crop: CropData) => {
    if (!farmIntegrator) return

    try {
      const rewards = await farmIntegrator.harvestCrop(crop.id)
      alert(`收获成功！获得奖励: ${rewards.join(', ')}`)
      setShowCropDetails(false)
      setSelectedCrop(null)
      updateFarmGrid()
    } catch (error) {
      console.error('收获作物失败:', error)
      alert('收获失败: ' + (error instanceof Error ? error.message : '未知错误'))
    }
  }

  // 开始行为会话
  const startBehaviorSession = (behaviorType: string) => {
    if (!farmIntegrator) return

    farmIntegrator.startBehaviorSession(behaviorType, 0.8) // 默认专注度
    
    // 触发事件
    window.dispatchEvent(new CustomEvent('behaviorSessionStart', {
      detail: { behaviorType }
    }))
  }

  // 结束行为会话
  const endBehaviorSession = (behaviorType: string) => {
    if (!farmIntegrator) return

    const session = activeSessions.find(s => s.behaviorType === behaviorType && s.isActive)
    if (!session) return

    const duration = Date.now() - session.startTime.getTime()
    const focusScore = Math.random() * 0.5 + 0.5 // 模拟专注分数 0.5-1.0
    const quality = Math.random() * 0.4 + 0.6     // 模拟质量分数 0.6-1.0

    farmIntegrator.endBehaviorSession(behaviorType, duration, focusScore, quality)

    // 触发事件
    window.dispatchEvent(new CustomEvent('behaviorSessionEnd', {
      detail: { behaviorType, duration, focusScore, quality }
    }))
  }

  // 获取行为类型的中文名称
  const getBehaviorTypeName = (behaviorType: string): string => {
    const names: Record<string, string> = {
      'LEARNING': '学习活动',
      'EXERCISE': '体力锻炼',
      'TIME_MANAGEMENT': '时间管理',
      'MEDITATION': '冥想练习',
      'DEEP_FOCUS': '深度专注',
      'READING': '阅读习惯',
      'SOCIAL_INTERACTION': '社交互动'
    }
    return names[behaviorType] || behaviorType
  }

  // 获取作物阶段的中文名称
  const getStageName = (stage: CropStage): string => {
    const names: Record<CropStage, string> = {
      [CropStage.SEED]: '种子',
      [CropStage.SPROUT]: '幼苗',
      [CropStage.GROWING]: '生长中',
      [CropStage.MATURE]: '成熟',
      [CropStage.HARVESTABLE]: '可收获',
      [CropStage.HARVESTED]: '已收获'
    }
    return names[stage]
  }

  // 获取品质的中文名称
  const getQualityName = (quality: CropQuality): string => {
    const names: Record<CropQuality, string> = {
      [CropQuality.COMMON]: '普通',
      [CropQuality.UNCOMMON]: '优良',
      [CropQuality.RARE]: '稀有',
      [CropQuality.EPIC]: '史诗',
      [CropQuality.LEGENDARY]: '传说'
    }
    return names[quality]
  }

  return (
    <div className="farm-interface" style={{
      padding: '20px',
      backgroundColor: 'rgba(15, 20, 30, 0.9)',
      borderRadius: '16px',
      color: 'white'
    }}>
      {/* 顶部状态栏 */}
      <div className="farm-header" style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '20px',
        padding: '16px',
        backgroundColor: 'rgba(255, 255, 255, 0.1)',
        borderRadius: '12px'
      }}>
        <div>
          <h2 style={{ margin: 0, fontSize: '24px' }}>专注力农场</h2>
          <p style={{ margin: '4px 0 0', color: 'rgba(255, 255, 255, 0.7)' }}>
            等级 {userLevel} • 经验值 {userExperience}
          </p>
        </div>
        
        <div style={{ display: 'flex', gap: '16px', alignItems: 'center' }}>
          <div style={{ textAlign: 'center' }}>
            <div style={{ fontSize: '14px', color: 'rgba(255, 255, 255, 0.7)' }}>
              已解锁作物
            </div>
            <div style={{ fontSize: '20px', fontWeight: 'bold' }}>
              {unlockedCropTypes.length}/7
            </div>
          </div>
          
          <div style={{ textAlign: 'center' }}>
            <div style={{ fontSize: '14px', color: 'rgba(255, 255, 255, 0.7)' }}>
              活跃会话
            </div>
            <div style={{ fontSize: '20px', fontWeight: 'bold' }}>
              {activeSessions.filter(s => s.isActive).length}
            </div>
          </div>
        </div>
      </div>

      {/* 行为会话控制面板 */}
      <div className="behavior-controls" style={{
        marginBottom: '20px',
        padding: '16px',
        backgroundColor: 'rgba(255, 255, 255, 0.05)',
        borderRadius: '12px'
      }}>
        <h3 style={{ margin: '0 0 12px', fontSize: '18px' }}>行为会话控制</h3>
        
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
          gap: '12px'
        }}>
          {['LEARNING', 'EXERCISE', 'TIME_MANAGEMENT', 'MEDITATION', 'DEEP_FOCUS', 'READING', 'SOCIAL_INTERACTION'].map(behaviorType => {
            const activeSession = activeSessions.find(s => s.behaviorType === behaviorType && s.isActive)
            const isActive = !!activeSession
            
            return (
              <div key={behaviorType} style={{
                padding: '12px',
                backgroundColor: isActive ? 'rgba(76, 175, 80, 0.2)' : 'rgba(255, 255, 255, 0.1)',
                borderRadius: '8px',
                border: isActive ? '2px solid #4CAF50' : '1px solid rgba(255, 255, 255, 0.2)'
              }}>
                <div style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center'
                }}>
                  <span style={{ fontSize: '14px' }}>
                    {getBehaviorTypeName(behaviorType)}
                  </span>
                  
                  <button
                    onClick={() => isActive ? endBehaviorSession(behaviorType) : startBehaviorSession(behaviorType)}
                    style={{
                      padding: '4px 12px',
                      borderRadius: '6px',
                      border: 'none',
                      backgroundColor: isActive ? '#f44336' : '#4CAF50',
                      color: 'white',
                      fontSize: '12px',
                      cursor: 'pointer'
                    }}
                  >
                    {isActive ? '结束' : '开始'}
                  </button>
                </div>
                
                {isActive && activeSession && (
                  <div style={{
                    fontSize: '12px',
                    color: 'rgba(255, 255, 255, 0.7)',
                    marginTop: '4px'
                  }}>
                    进行中: {Math.floor((Date.now() - activeSession.startTime.getTime()) / 1000 / 60)}分钟
                  </div>
                )}
              </div>
            )
          })}
        </div>
      </div>

      {/* 农场网格 */}
      <div className="farm-grid-container" style={{
        backgroundColor: 'rgba(255, 255, 255, 0.05)',
        borderRadius: '12px',
        padding: '20px'
      }}>
        <h3 style={{ margin: '0 0 16px', fontSize: '18px' }}>农场网格</h3>
        
        <CropGridVisualizer
          crops={farmGrid.flat().map(pos => pos.crop).filter(Boolean) as CropData[]}
          gridWidth={gridWidth}
          gridHeight={gridHeight}
          onCropClick={(crop) => {
            setSelectedCrop(crop)
            setShowCropDetails(true)
          }}
          onEmptyClick={handleGridClick}
          cellSize={80}
          showLabels={true}
        />
      </div>

      {/* 作物选择模态框 */}
      {showCropSelection && selectedPosition && (
        <CropSelectionModal
          isOpen={showCropSelection}
          onClose={() => {
            setShowCropSelection(false)
            setSelectedPosition(null)
          }}
          onCropSelect={handleCropSelect}
          unlockedCropTypes={unlockedCropTypes}
          gridPosition={selectedPosition}
          userLevel={userLevel}
          userExperience={userExperience}
        />
      )}

      {/* 作物详情模态框 */}
      {showCropDetails && selectedCrop && (
        <div className="crop-details-modal-overlay" onClick={() => setShowCropDetails(false)} style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000
        }}>
          <div 
            className="crop-details-modal"
            onClick={(e) => e.stopPropagation()}
            style={{
              backgroundColor: 'rgba(20, 25, 40, 0.95)',
              border: '2px solid rgba(255, 255, 255, 0.2)',
              borderRadius: '16px',
              padding: '24px',
              maxWidth: '500px',
              width: '90%',
              backdropFilter: 'blur(10px)'
            }}
          >
            {/* 作物可视化 */}
            <div style={{ display: 'flex', justifyContent: 'center', marginBottom: '20px' }}>
              <CropVisualizer
                crop={selectedCrop}
                size="large"
                showParticles={true}
                showTooltip={false}
              />
            </div>

            {/* 作物信息 */}
            <div style={{ textAlign: 'center', marginBottom: '20px' }}>
              <h2 style={{ color: 'white', margin: '0 0 8px' }}>
                {selectedCrop.type.replace('_', ' ')}
              </h2>
              
              <div style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(2, 1fr)',
                gap: '12px',
                fontSize: '14px'
              }}>
                <div>
                  <strong style={{ color: 'white' }}>阶段:</strong>
                  <br />
                  <span style={{ color: 'rgba(255, 255, 255, 0.8)' }}>
                    {getStageName(selectedCrop.stage)}
                  </span>
                </div>
                
                <div>
                  <strong style={{ color: 'white' }}>品质:</strong>
                  <br />
                  <span style={{ color: 'rgba(255, 255, 255, 0.8)' }}>
                    {getQualityName(selectedCrop.quality)}
                  </span>
                </div>
                
                <div>
                  <strong style={{ color: 'white' }}>成长进度:</strong>
                  <br />
                  <span style={{ color: 'rgba(255, 255, 255, 0.8)' }}>
                    {Math.round(selectedCrop.progress * 100)}%
                  </span>
                </div>
                
                <div>
                  <strong style={{ color: 'white' }}>种植时间:</strong>
                  <br />
                  <span style={{ color: 'rgba(255, 255, 255, 0.8)' }}>
                    {selectedCrop.plantedAt ? new Date(selectedCrop.plantedAt).toLocaleDateString() : '未知'}
                  </span>
                </div>
              </div>
            </div>

            {/* 操作按钮 */}
            <div style={{
              display: 'flex',
              justifyContent: 'center',
              gap: '12px'
            }}>
              <button
                onClick={() => setShowCropDetails(false)}
                style={{
                  padding: '12px 24px',
                  borderRadius: '8px',
                  border: '1px solid rgba(255, 255, 255, 0.3)',
                  backgroundColor: 'transparent',
                  color: 'rgba(255, 255, 255, 0.8)',
                  cursor: 'pointer',
                  fontSize: '14px'
                }}
              >
                关闭
              </button>
              
              {selectedCrop.stage === CropStage.HARVESTABLE && (
                <button
                  onClick={() => handleCropHarvest(selectedCrop)}
                  style={{
                    padding: '12px 24px',
                    borderRadius: '8px',
                    border: 'none',
                    backgroundColor: '#4CAF50',
                    color: 'white',
                    cursor: 'pointer',
                    fontSize: '14px',
                    fontWeight: 'bold'
                  }}
                >
                  收获
                </button>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  )
} 