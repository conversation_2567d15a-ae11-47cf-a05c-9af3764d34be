import React, { useState, useEffect } from 'react'
import { LootBoxManager } from '../managers/LootBoxManager'
import { LootboxType, LootboxResult } from '../types/lootbox'
import { GameItem } from '../types/enhanced-items'
import { CurrencyType } from '../types/currency'

interface LootBoxDisplayItem {
  id: string
  name: string
  description: string
  price: number
  currency: string
  rarity: string
  icon: string
}

export const EnhancedLootboxDemo: React.FC = () => {
  const [lootBoxManager] = useState(() => new LootBoxManager())
  const [availableBoxes, setAvailableBoxes] = useState<LootBoxDisplayItem[]>([])
  const [userCurrency, setUserCurrency] = useState<Record<CurrencyType, number>>(() => ({
    [CurrencyType.FOCUS_COIN]: 0,
    [CurrencyType.DISCIPLINE_TOKEN]: 0,
    [CurrencyType.FUTURES_CRYSTAL]: 0,
    [CurrencyType.GOLDEN_HARVEST]: 0
  }))
  const [openedItems, setOpenedItems] = useState<GameItem[]>([])
  const [lastResult, setLastResult] = useState<LootboxResult | null>(null)
  const [isOpening, setIsOpening] = useState(false)
  const [message, setMessage] = useState<string>('')

  useEffect(() => {
    // 初始化盲盒列表和货币
    const boxes = lootBoxManager.getAvailableLootBoxes()
    const displayBoxes: LootBoxDisplayItem[] = boxes.map(box => ({
      id: box.id,
      name: box.name,
      description: box.description,
      price: box.price,
      currency: box.currency,
      rarity: box.rarity,
      icon: '📦' // 默认图标
    }))
    setAvailableBoxes(displayBoxes)
    setUserCurrency(lootBoxManager.getUserCurrency())
  }, [lootBoxManager])

  const handleOpenLootbox = async (lootboxId: string) => {
    setIsOpening(true)
    setMessage('')
    
    try {
      const result = await lootBoxManager.openLootBox(lootboxId)
      
      if (result.success && result.items && result.result) {
        setOpenedItems(prev => [...prev, ...result.items!])
        setLastResult(result.result)
        setUserCurrency(lootBoxManager.getUserCurrency())
        setMessage(`🎉 成功开启盲盒！获得 ${result.items.length} 个物品`)
      } else {
        setMessage(`❌ ${result.error || '开启失败'}`)
      }
    } catch (error) {
      setMessage(`❌ ${error instanceof Error ? error.message : '未知错误'}`)
    } finally {
      setIsOpening(false)
    }
  }

  const getQualityColor = (quality: string) => {
    const colorMap: Record<string, string> = {
      'common': '#9E9E9E',
      'good': '#4CAF50',
      'rare': '#2196F3',
      'epic': '#9C27B0',
      'legendary': '#FF9800'
    }
    return colorMap[quality] || '#9E9E9E'
  }

  const getCurrencyIcon = (currency: string) => {
    const iconMap: Record<string, string> = {
      [CurrencyType.FOCUS_COIN]: '🪙',
      [CurrencyType.DISCIPLINE_TOKEN]: '⚡',
      [CurrencyType.FUTURES_CRYSTAL]: '💎',
      [CurrencyType.GOLDEN_HARVEST]: '🏆'
    }
    return iconMap[currency] || '💰'
  }

  const addTestCurrency = () => {
    lootBoxManager.addCurrency(CurrencyType.FOCUS_COIN, 1000)
    lootBoxManager.addCurrency(CurrencyType.DISCIPLINE_TOKEN, 100)
    lootBoxManager.addCurrency(CurrencyType.FUTURES_CRYSTAL, 50)
    lootBoxManager.addCurrency(CurrencyType.GOLDEN_HARVEST, 10)
    setUserCurrency(lootBoxManager.getUserCurrency())
    setMessage('💰 添加了测试货币')
  }

  const containerStyle: React.CSSProperties = {
    padding: '24px',
    backgroundColor: '#f5f5f5',
    minHeight: '100vh',
    fontFamily: 'Arial, sans-serif'
  }

  const maxWidthStyle: React.CSSProperties = {
    maxWidth: '1200px',
    margin: '0 auto'
  }

  const cardStyle: React.CSSProperties = {
    backgroundColor: 'white',
    borderRadius: '8px',
    boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
    padding: '16px',
    marginBottom: '24px'
  }

  const gridStyle: React.CSSProperties = {
    display: 'grid',
    gap: '16px'
  }

  const buttonStyle: React.CSSProperties = {
    padding: '8px 16px',
    backgroundColor: '#4CAF50',
    color: 'white',
    border: 'none',
    borderRadius: '4px',
    cursor: 'pointer',
    fontSize: '14px',
    fontWeight: 'bold'
  }

  const disabledButtonStyle: React.CSSProperties = {
    ...buttonStyle,
    backgroundColor: '#cccccc',
    cursor: 'not-allowed'
  }

  return (
    <div style={containerStyle}>
      <div style={maxWidthStyle}>
        <h1 style={{ 
          fontSize: '2rem', 
          fontWeight: 'bold', 
          textAlign: 'center', 
          marginBottom: '32px', 
          color: '#333' 
        }}>
          🎁 期货游戏盲盒系统演示
        </h1>

        {/* 货币显示 */}
        <div style={cardStyle}>
          <h2 style={{ fontSize: '1.25rem', fontWeight: '600', marginBottom: '16px' }}>💰 我的货币</h2>
          <div style={{ 
            ...gridStyle, 
            gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))',
            marginBottom: '16px'
          }}>
            {Object.entries(userCurrency).map(([currency, amount]) => (
              <div key={currency} style={{ 
                textAlign: 'center', 
                padding: '12px', 
                backgroundColor: '#f9f9f9', 
                borderRadius: '8px' 
              }}>
                <div style={{ fontSize: '1.5rem', marginBottom: '4px' }}>{getCurrencyIcon(currency)}</div>
                <div style={{ fontSize: '0.875rem', color: '#666', marginBottom: '4px' }}>{currency}</div>
                <div style={{ fontSize: '1.125rem', fontWeight: 'bold' }}>{amount}</div>
              </div>
            ))}
          </div>
          <button
            onClick={addTestCurrency}
            style={{ ...buttonStyle, backgroundColor: '#4CAF50' }}
            onMouseOver={(e) => e.currentTarget.style.backgroundColor = '#45a049'}
            onMouseOut={(e) => e.currentTarget.style.backgroundColor = '#4CAF50'}
          >
            添加测试货币
          </button>
        </div>

        {/* 消息显示 */}
        {message && (
          <div style={{
            backgroundColor: '#e3f2fd',
            border: '1px solid #2196f3',
            borderRadius: '8px',
            padding: '12px',
            marginBottom: '24px'
          }}>
            <p style={{ color: '#1976d2', margin: 0 }}>{message}</p>
          </div>
        )}

        {/* 盲盒列表 */}
        <div style={cardStyle}>
          <h2 style={{ fontSize: '1.25rem', fontWeight: '600', marginBottom: '16px' }}>🎁 可用盲盒</h2>
          <div style={{ 
            ...gridStyle, 
            gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))' 
          }}>
            {availableBoxes.map(box => (
              <div key={box.id} style={{ 
                border: '1px solid #e0e0e0', 
                borderRadius: '8px', 
                padding: '16px' 
              }}>
                <div style={{ textAlign: 'center' }}>
                  <div style={{ fontSize: '2.5rem', marginBottom: '8px' }}>📦</div>
                  <h3 style={{ 
                    fontSize: '1.125rem', 
                    fontWeight: '600', 
                    marginBottom: '8px' 
                  }}>{box.name}</h3>
                  <p style={{ 
                    fontSize: '0.875rem', 
                    color: '#666', 
                    marginBottom: '12px',
                    lineHeight: '1.4'
                  }}>{box.description}</p>
                  <div style={{ 
                    display: 'flex', 
                    justifyContent: 'center', 
                    alignItems: 'center', 
                    gap: '8px', 
                    marginBottom: '12px' 
                  }}>
                    <span style={{ fontSize: '1.25rem' }}>{getCurrencyIcon(box.currency)}</span>
                    <span style={{ fontWeight: 'bold' }}>{box.price}</span>
                  </div>
                  <button
                    onClick={() => handleOpenLootbox(box.id)}
                    disabled={isOpening || (userCurrency[box.currency as CurrencyType] || 0) < box.price}
                    style={
                      isOpening || (userCurrency[box.currency as CurrencyType] || 0) < box.price 
                        ? disabledButtonStyle 
                        : { ...buttonStyle, backgroundColor: '#2196f3', width: '100%' }
                    }
                    onMouseOver={(e) => {
                      if (!e.currentTarget.disabled) {
                        e.currentTarget.style.backgroundColor = '#1976d2'
                      }
                    }}
                    onMouseOut={(e) => {
                      if (!e.currentTarget.disabled) {
                        e.currentTarget.style.backgroundColor = '#2196f3'
                      }
                    }}
                  >
                    {isOpening ? '开启中...' : '开启盲盒'}
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 开出的道具 */}
        {openedItems.length > 0 && (
          <div style={cardStyle}>
            <h2 style={{ fontSize: '1.25rem', fontWeight: '600', marginBottom: '16px' }}>🎯 获得的道具</h2>
            <div style={{ 
              ...gridStyle, 
              gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))' 
            }}>
              {openedItems.map((item, index) => (
                <div 
                  key={`${item.id}-${index}`}
                  style={{
                    border: `2px solid ${getQualityColor(item.quality)}`,
                    borderRadius: '8px',
                    padding: '12px'
                  }}
                >
                  <div style={{ textAlign: 'center' }}>
                    <div style={{ fontSize: '2rem', marginBottom: '8px' }}>{item.icon}</div>
                    <h4 style={{ 
                      fontSize: '0.875rem', 
                      fontWeight: '600', 
                      marginBottom: '4px' 
                    }}>{item.name}</h4>
                    <p style={{ 
                      fontSize: '0.75rem', 
                      color: '#666', 
                      marginBottom: '8px',
                      lineHeight: '1.3'
                    }}>{item.description}</p>
                    <div style={{ 
                      display: 'flex', 
                      justifyContent: 'space-between', 
                      alignItems: 'center', 
                      fontSize: '0.75rem',
                      marginBottom: '4px'
                    }}>
                      <span style={{
                        padding: '2px 6px',
                        borderRadius: '4px',
                        color: 'white',
                        backgroundColor: getQualityColor(item.quality),
                        fontSize: '0.7rem'
                      }}>
                        {item.quality}
                      </span>
                      <span style={{ fontWeight: 'bold' }}>{item.baseValue}💰</span>
                    </div>
                    <div style={{ 
                      fontSize: '0.7rem', 
                      color: '#888' 
                    }}>
                      {item.category}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 最后一次开盒结果 */}
        {lastResult && (
          <div style={cardStyle}>
            <h2 style={{ fontSize: '1.25rem', fontWeight: '600', marginBottom: '16px' }}>📊 最后开盒统计</h2>
            <div style={{ 
              ...gridStyle, 
              gridTemplateColumns: 'repeat(auto-fit, minmax(120px, 1fr))' 
            }}>
              <div style={{ 
                textAlign: 'center', 
                padding: '12px', 
                backgroundColor: '#f9f9f9', 
                borderRadius: '8px' 
              }}>
                <div style={{ fontSize: '1.125rem', fontWeight: 'bold' }}>{lastResult.items.length}</div>
                <div style={{ fontSize: '0.875rem', color: '#666' }}>获得道具</div>
              </div>
              <div style={{ 
                textAlign: 'center', 
                padding: '12px', 
                backgroundColor: '#f9f9f9', 
                borderRadius: '8px' 
              }}>
                <div style={{ fontSize: '1.125rem', fontWeight: 'bold' }}>{lastResult.totalValue}</div>
                <div style={{ fontSize: '0.875rem', color: '#666' }}>总价值</div>
              </div>
              <div style={{ 
                textAlign: 'center', 
                padding: '12px', 
                backgroundColor: '#f9f9f9', 
                borderRadius: '8px' 
              }}>
                <div style={{ fontSize: '1.125rem', fontWeight: 'bold' }}>{lastResult.cost.amount}</div>
                <div style={{ fontSize: '0.875rem', color: '#666' }}>花费</div>
              </div>
              <div style={{ 
                textAlign: 'center', 
                padding: '12px', 
                backgroundColor: '#f9f9f9', 
                borderRadius: '8px' 
              }}>
                <div style={{ fontSize: '1.125rem', fontWeight: 'bold' }}>
                  {((lastResult.totalValue / lastResult.cost.amount) * 100).toFixed(0)}%
                </div>
                <div style={{ fontSize: '0.875rem', color: '#666' }}>回报率</div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default EnhancedLootboxDemo 