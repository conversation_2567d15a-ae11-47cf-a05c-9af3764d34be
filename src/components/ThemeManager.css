.theme-manager {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 16px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  min-height: 80vh;
}

/* 头部区域 */
.theme-manager__header {
  text-align: center;
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 2px solid rgba(147, 197, 253, 0.3);
}

.theme-manager__title {
  font-size: 2.5rem;
  font-weight: bold;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 0.5rem;
}

.theme-manager__subtitle {
  color: #64748b;
  font-size: 1.1rem;
  margin-bottom: 1rem;
}

.theme-manager__current {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: rgba(59, 130, 246, 0.1);
  border: 2px solid #3b82f6;
  border-radius: 50px;
  font-weight: 500;
}

.current-theme__label {
  color: #64748b;
}

.current-theme__name {
  color: #3b82f6;
  font-weight: bold;
}

/* 预览模式栏 */
.theme-manager__preview-bar {
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
  border-radius: 12px;
  padding: 1rem 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 12px rgba(251, 191, 36, 0.3);
}

.preview-bar__content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.preview-bar__text {
  color: white;
  font-weight: 600;
  font-size: 1.1rem;
}

.preview-bar__actions {
  display: flex;
  gap: 0.75rem;
}

/* 筛选区域 */
.theme-manager__filters {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  border: 1px solid #e2e8f0;
}

.filters__row {
  display: flex;
  gap: 1.5rem;
  align-items: end;
  flex-wrap: wrap;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  min-width: 150px;
}

.filter-group--search {
  flex: 1;
  min-width: 200px;
}

.filter-label {
  font-weight: 600;
  color: #374151;
  font-size: 0.875rem;
}

.filter-select,
.search-input {
  padding: 0.75rem 1rem;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.2s ease;
  background: white;
}

.filter-select:focus,
.search-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.search-input {
  width: 100%;
}

/* 主题网格 */
.theme-manager__grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

/* 主题卡片 */
.theme-card {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border: 2px solid transparent;
  transition: all 0.3s ease;
  position: relative;
}

.theme-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.theme-card--current {
  border-color: #10b981;
  box-shadow: 0 4px 20px rgba(16, 185, 129, 0.3);
}

.theme-card--selected {
  border-color: #3b82f6;
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.3);
}

.theme-card--locked {
  opacity: 0.7;
  position: relative;
}

.theme-card--locked::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.1);
  z-index: 1;
  pointer-events: none;
}

/* 主题预览 */
.theme-card__preview {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.theme-preview {
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  position: relative;
  border-bottom: 3px solid #e5e7eb;
}

.theme-preview__overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, transparent 0%, rgba(0, 0, 0, 0.3) 100%);
  display: flex;
  justify-content: flex-end;
  align-items: flex-start;
  padding: 1rem;
}

/* 状态标识 */
.status-badge {
  padding: 0.375rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  backdrop-filter: blur(10px);
}

.status-badge--current {
  background: rgba(16, 185, 129, 0.9);
  color: white;
}

.status-badge--selected {
  background: rgba(59, 130, 246, 0.9);
  color: white;
}

.status-badge--locked {
  background: rgba(107, 114, 128, 0.9);
  color: white;
  font-size: 1rem;
}

/* 主题信息 */
.theme-card__info {
  padding: 1.5rem;
}

.theme-info__header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.75rem;
  gap: 1rem;
}

.theme-info__name {
  font-size: 1.25rem;
  font-weight: bold;
  color: #1f2937;
  margin: 0;
  flex: 1;
}

.theme-info__category {
  background: linear-gradient(135deg, #e0e7ff, #c7d2fe);
  color: #3730a3;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  white-space: nowrap;
}

.theme-info__description {
  color: #6b7280;
  font-size: 0.875rem;
  line-height: 1.5;
  margin-bottom: 1rem;
}

/* 主题标签 */
.theme-info__tags {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
  margin-bottom: 1rem;
}

.theme-tag {
  background: #f3f4f6;
  color: #374151;
  padding: 0.25rem 0.5rem;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 500;
}

/* 主题效果 */
.theme-info__effects {
  margin-bottom: 1rem;
}

.effects__title {
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.5rem;
}

.effects__list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.effect-item {
  font-size: 0.75rem;
  color: #6b7280;
  margin-bottom: 0.25rem;
  position: relative;
  padding-left: 1rem;
}

.effect-item::before {
  content: '✨';
  position: absolute;
  left: 0;
  top: 0;
}

/* 主题操作 */
.theme-card__actions {
  padding: 1rem 1.5rem;
  background: #f8fafc;
  border-top: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
}

.theme-status__text {
  font-size: 0.875rem;
  font-weight: 600;
  color: #6b7280;
}

.theme-actions {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.current-indicator {
  color: #10b981;
  font-weight: bold;
  font-size: 0.875rem;
}

/* 按钮样式 */
.btn {
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  font-size: 0.875rem;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.btn--small {
  padding: 0.5rem 1rem;
  font-size: 0.75rem;
}

.btn--primary {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  color: white;
}

.btn--primary:hover {
  background: linear-gradient(135deg, #2563eb, #1d4ed8);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.btn--secondary {
  background: #f1f5f9;
  color: #475569;
  border: 1px solid #e2e8f0;
}

.btn--secondary:hover {
  background: #e2e8f0;
  color: #334155;
}

.btn--purchase {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
}

.btn--purchase:hover {
  background: linear-gradient(135deg, #059669, #047857);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
}

/* 空状态 */
.theme-manager__empty {
  text-align: center;
  padding: 3rem;
  color: #64748b;
  background: white;
  border-radius: 12px;
  border: 2px dashed #cbd5e1;
}

.theme-manager__empty p {
  font-size: 1.125rem;
  margin-bottom: 1rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .theme-manager {
    padding: 1rem;
  }
  
  .theme-manager__title {
    font-size: 2rem;
  }
  
  .theme-manager__grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .filters__row {
    flex-direction: column;
    align-items: stretch;
  }
  
  .filter-group {
    min-width: auto;
  }
  
  .preview-bar__content {
    flex-direction: column;
    text-align: center;
  }
  
  .theme-card__actions {
    flex-direction: column;
    align-items: stretch;
    gap: 0.75rem;
  }
  
  .theme-actions {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .theme-manager {
    padding: 0.75rem;
  }
  
  .theme-card__info {
    padding: 1rem;
  }
  
  .theme-info__header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .btn {
    width: 100%;
    justify-content: center;
  }
  
  .theme-actions {
    flex-direction: column;
    width: 100%;
  }
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.theme-card {
  animation: fadeInUp 0.6s ease forwards;
}

.theme-card:nth-child(1) { animation-delay: 0.1s; }
.theme-card:nth-child(2) { animation-delay: 0.2s; }
.theme-card:nth-child(3) { animation-delay: 0.3s; }
.theme-card:nth-child(4) { animation-delay: 0.4s; }
.theme-card:nth-child(5) { animation-delay: 0.5s; }
.theme-card:nth-child(6) { animation-delay: 0.6s; }

/* 滚动条样式 */
.theme-manager::-webkit-scrollbar {
  width: 8px;
}

.theme-manager::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

.theme-manager::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

.theme-manager::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
} 