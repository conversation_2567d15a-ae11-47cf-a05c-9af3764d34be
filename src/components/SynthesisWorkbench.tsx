import React, { useState, useRef, useCallback } from 'react';
import { InventorySystem } from '../systems/InventorySystem';
import { InventoryItem, RARITY_NAMES, SynthesisResult } from '../types/inventory';
import { ItemRarity, ItemCategory, RARITY_COLORS } from '../types/lootbox';

interface DraggedItem {
  item: InventoryItem;
  dragImage?: HTMLImageElement;
}

interface SynthesisSlot {
  id: number;
  item: InventoryItem | null;
  position: { x: number; y: number };
}

interface SynthesisWorkbenchProps {
  inventorySystem: InventorySystem;
  onClose: () => void;
}

export const SynthesisWorkbench: React.FC<SynthesisWorkbenchProps> = ({
  inventorySystem,
  onClose
}) => {
  const [inventoryItems, setInventoryItems] = useState(inventorySystem.getState().items);
  const [synthesisSlots, setSynthesisSlots] = useState<SynthesisSlot[]>([
    { id: 1, item: null, position: { x: 150, y: 200 } },
    { id: 2, item: null, position: { x: 250, y: 200 } },
    { id: 3, item: null, position: { x: 350, y: 200 } },
    { id: 4, item: null, position: { x: 200, y: 280 } },
    { id: 5, item: null, position: { x: 300, y: 280 } }
  ]);
  
  const [draggedItem, setDraggedItem] = useState<DraggedItem | null>(null);
  const [synthesisResult, setSynthesisResult] = useState<SynthesisResult | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  
  const workbenchRef = useRef<HTMLDivElement>(null);
  const resultSlotRef = useRef<HTMLDivElement>(null);

  // 更新库存状态
  React.useEffect(() => {
    const unsubscribe = inventorySystem.subscribe((state) => {
      setInventoryItems(state.items);
    });
    return unsubscribe;
  }, [inventorySystem]);

  // 开始拖拽
  const handleDragStart = useCallback((item: InventoryItem, e: React.DragEvent) => {
    const dragImage = new Image();
    dragImage.src = `data:image/svg+xml,${encodeURIComponent(`
      <svg xmlns="http://www.w3.org/2000/svg" width="60" height="60" viewBox="0 0 60 60">
        <rect width="60" height="60" fill="${RARITY_COLORS[item.rarity]}" opacity="0.8" rx="8"/>
        <text x="30" y="40" text-anchor="middle" font-size="24">${item.icon}</text>
      </svg>
    `)}`;
    
    e.dataTransfer.setDragImage(dragImage, 30, 30);
    e.dataTransfer.effectAllowed = 'move';
    
    setDraggedItem({ item, dragImage });
  }, []);

  // 拖拽结束
  const handleDragEnd = useCallback(() => {
    setDraggedItem(null);
  }, []);

  // 拖拽到合成槽位
  const handleSlotDrop = useCallback((e: React.DragEvent, slotId: number) => {
    e.preventDefault();
    
    if (!draggedItem) return;
    
    setSynthesisSlots(prev => prev.map(slot => 
      slot.id === slotId 
        ? { ...slot, item: draggedItem.item }
        : slot
    ));
    
    // 添加放置音效和视觉反馈
    const slotElement = e.currentTarget as HTMLElement;
    slotElement.classList.add('item-dropped');
    setTimeout(() => slotElement.classList.remove('item-dropped'), 500);
    
    setDraggedItem(null);
  }, [draggedItem]);

  // 从合成槽位移除物品
  const removeFromSlot = useCallback((slotId: number) => {
    setSynthesisSlots(prev => prev.map(slot => 
      slot.id === slotId 
        ? { ...slot, item: null }
        : slot
    ));
  }, []);

  // 允许拖拽放置
  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
  }, []);

  // 清空所有槽位
  const clearAllSlots = useCallback(() => {
    setSynthesisSlots(prev => prev.map(slot => ({ ...slot, item: null })));
  }, []);

  // 执行合成
  const handleSynthesize = useCallback(async () => {
    const itemsInSlots = synthesisSlots.filter(slot => slot.item !== null);
    
    if (itemsInSlots.length < 2) {
      alert('至少需要2个物品才能进行合成！');
      return;
    }

    setIsProcessing(true);
    
    // 模拟合成过程的视觉效果
    setTimeout(() => {
      // 检查是否有可用的合成配方
      const recipes = inventorySystem.getAvailableRecipes();
      const slotItems = itemsInSlots.map(slot => slot.item!);
      
      // 尝试找到匹配的配方
      let matchedRecipe = null;
      for (const recipe of recipes) {
        const canSynthesize = recipe.requiredItems.every(requirement => {
          const matchingItems = slotItems.filter(item => {
            const rarityMatch = item.rarity === requirement.rarity;
            const categoryMatch = !requirement.category || item.category === requirement.category;
            return rarityMatch && categoryMatch;
          });
          return matchingItems.length >= requirement.quantity;
        });
        
        if (canSynthesize) {
          matchedRecipe = recipe;
          break;
        }
      }

      if (matchedRecipe) {
        const result = inventorySystem.synthesize(matchedRecipe.id);
        setSynthesisResult(result);
        
        if (result.success) {
          // 清空槽位
          clearAllSlots();
        }
      } else {
        // 如果没有匹配的配方，显示失败结果
        setSynthesisResult({
          success: false,
          consumedItems: slotItems,
          message: '合成失败：找不到适合的配方'
        });
      }
      
      setIsProcessing(false);
      
      // 3秒后清除结果
      setTimeout(() => setSynthesisResult(null), 3000);
    }, 1500);
  }, [synthesisSlots, inventorySystem, clearAllSlots]);

  return (
    <div 
      className="synthesis-workbench-modal fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center"
      style={{ zIndex: 15100 }}
      onClick={(e) => {
        if (e.target === e.currentTarget) {
          onClose();
        }
      }}
    >
      <div 
        className="bg-gradient-to-b from-amber-50 to-amber-100 rounded-lg p-6 max-w-4xl w-full mx-4 relative"
        onClick={(e) => e.stopPropagation()}
      >
        {/* 关闭按钮 */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-gray-500 hover:text-gray-700 text-2xl"
        >
          ×
        </button>

        {/* 标题 */}
        <div className="text-center mb-6">
          <h2 className="text-2xl font-bold text-amber-800 mb-2">🧪 炼金工作台</h2>
          <p className="text-amber-600">拖拽物品到合成区域进行炼金合成</p>
        </div>

        <div className="flex gap-6">
          {/* 左侧：物品库存 */}
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-amber-700 mb-3">📦 物品库存</h3>
            <div 
              className="bg-white rounded-lg p-4 border-2 border-amber-200 max-h-96 overflow-y-auto"
              style={{ minHeight: '300px' }}
            >
              <div className="grid grid-cols-5 gap-2">
                {(() => {
                  // 展开物品：每个物品根据数量分别显示
                  const expandedItems: Array<{item: InventoryItem, index: number}> = [];
                  inventoryItems.forEach((item) => {
                    for (let i = 0; i < item.quantity; i++) {
                      expandedItems.push({ item, index: i });
                    }
                  });
                  
                  return expandedItems.map(({item, index}, globalIndex) => (
                    <div
                      key={`${item.id}-${index}-${globalIndex}`}
                      draggable
                      onDragStart={(e) => handleDragStart(item, e)}
                      onDragEnd={handleDragEnd}
                      className="inventory-slot group cursor-grab active:cursor-grabbing transform transition-all duration-200 hover:scale-105"
                      style={{ 
                        background: `linear-gradient(135deg, ${RARITY_COLORS[item.rarity]}15, ${RARITY_COLORS[item.rarity]}25)`,
                        border: `3px solid ${RARITY_COLORS[item.rarity]}`,
                        borderRadius: '12px',
                        aspectRatio: '1',
                        position: 'relative',
                        minHeight: '70px',
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                        justifyContent: 'center',
                        padding: '6px',
                        boxShadow: `0 2px 8px ${RARITY_COLORS[item.rarity]}30, inset 0 1px 0 rgba(255,255,255,0.2)`
                      }}
                      title={`${item.name} (${RARITY_NAMES[item.rarity]})`}
                    >
                      {/* 拖拽时的发光效果 */}
                      <div 
                        className="absolute inset-0 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                        style={{
                          background: `radial-gradient(circle at center, ${RARITY_COLORS[item.rarity]}40, transparent 70%)`,
                          animation: 'slot-glow 2s ease-in-out infinite alternate'
                        }}
                      />
                      
                      <div className="relative z-10 text-center w-full">
                        {/* 物品图标 */}
                        <div className="text-lg mb-1 drop-shadow-sm">{item.icon}</div>
                        
                        {/* 物品名称 - 小字体 */}
                        <div className="text-xs font-medium text-gray-800 truncate leading-tight">
                          {item.name}
                        </div>
                        
                        {/* 品质标记 - 左上角 */}
                        <div className="absolute top-0.5 left-0.5">
                          <span 
                            className="text-xs font-bold px-1 py-0.5 rounded"
                            style={{ 
                              backgroundColor: `${RARITY_COLORS[item.rarity]}80`,
                              color: 'white',
                              fontSize: '8px'
                            }}
                          >
                            {RARITY_NAMES[item.rarity]}
                          </span>
                        </div>
                      </div>
                    </div>
                  ));
                })()}
              </div>
              
              {inventoryItems.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  <div className="text-4xl mb-2">📦</div>
                  <p>库存为空</p>
                  <p className="text-xs">请先收集一些物品</p>
                </div>
              )}
            </div>
          </div>

          {/* 右侧：合成区域 */}
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-amber-700 mb-3">⚗️ 合成区域</h3>
            <div 
              ref={workbenchRef}
              className="bg-gradient-to-br from-amber-200 to-amber-300 rounded-lg p-4 border-2 border-amber-400 relative"
              style={{ height: '400px' }}
            >
              {/* 合成槽位 */}
              {synthesisSlots.map(slot => (
                <div
                  key={slot.id}
                  className={`synthesis-slot absolute w-16 h-16 border-4 border-dashed border-amber-600 rounded-lg bg-amber-100 flex items-center justify-center transition-all duration-300 ${slot.item ? 'has-item' : ''}`}
                  style={{ 
                    left: slot.position.x, 
                    top: slot.position.y,
                    borderColor: slot.item ? RARITY_COLORS[slot.item.rarity] : '#d97706',
                    backgroundColor: slot.item ? `${RARITY_COLORS[slot.item.rarity]}30` : '#FEF3C7'
                  }}
                  onDragOver={handleDragOver}
                  onDrop={(e) => handleSlotDrop(e, slot.id)}
                  onClick={() => slot.item && removeFromSlot(slot.id)}
                >
                  {slot.item ? (
                    <div className="text-center cursor-pointer hover:scale-110 transition-transform">
                      <div className="text-2xl">{slot.item.icon}</div>
                      <div className="text-xs mt-1 opacity-75">{slot.item.name}</div>
                    </div>
                  ) : (
                    <div className="text-amber-600 text-xs text-center opacity-60">
                      拖拽<br/>物品
                    </div>
                  )}
                </div>
              ))}

              {/* 结果槽位 */}
              <div 
                ref={resultSlotRef}
                className={`result-slot absolute w-20 h-20 border-4 border-solid rounded-lg flex items-center justify-center transition-all duration-500 ${
                  synthesisResult?.success ? 'has-result' : ''
                } ${isProcessing ? 'animate-pulse' : ''}`}
                style={{ 
                  left: 225, 
                  top: 100,
                  backgroundColor: synthesisResult?.success && synthesisResult.resultItem 
                    ? `${RARITY_COLORS[synthesisResult.resultItem.rarity]}40` 
                    : '#FEF08A',
                  borderColor: synthesisResult?.success && synthesisResult.resultItem
                    ? RARITY_COLORS[synthesisResult.resultItem.rarity]
                    : '#EAB308'
                }}
              >
                {synthesisResult && synthesisResult.success && synthesisResult.resultItem ? (
                  <div className="text-center animate-bounce">
                    <div className="text-3xl mb-1">{synthesisResult.resultItem.icon}</div>
                    <div className="text-xs font-bold" style={{ color: RARITY_COLORS[synthesisResult.resultItem.rarity] }}>
                      {RARITY_NAMES[synthesisResult.resultItem.rarity]}
                    </div>
                  </div>
                ) : isProcessing ? (
                  <div className="text-center">
                    <div className="text-3xl animate-spin mb-1">⚡</div>
                    <div className="text-xs text-amber-600">炼金中</div>
                  </div>
                ) : (
                  <div className="text-yellow-600 text-xs text-center opacity-60">
                    结果<br/>物品
                  </div>
                )}
              </div>

              {/* 合成按钮 */}
              <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2">
                <button
                  onClick={handleSynthesize}
                  disabled={isProcessing || synthesisSlots.filter(s => s.item).length < 2}
                  className="synthesize-button px-6 py-3 bg-gradient-to-r from-yellow-400 to-orange-500 text-white font-bold rounded-lg shadow-lg hover:from-yellow-500 hover:to-orange-600 disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-105 transition-all"
                >
                  {isProcessing ? (
                    <span className="flex items-center">
                      <span className="animate-spin mr-2">⚡</span>
                      炼金中...
                    </span>
                  ) : (
                    '🔥 开始炼金'
                  )}
                </button>
              </div>

              {/* 清空按钮 */}
              <button
                onClick={clearAllSlots}
                className="absolute top-4 right-4 px-3 py-1 bg-red-500 text-white text-sm rounded hover:bg-red-600 transition-colors"
              >
                清空
              </button>
            </div>
          </div>
        </div>

        {/* 合成结果提示 */}
        {synthesisResult && (
          <div className="fixed inset-0 flex items-center justify-center z-60 pointer-events-none">
            <div className="synthesis-result-modal bg-white p-8 rounded-xl shadow-2xl border-4 pointer-events-auto max-w-md mx-4 relative overflow-hidden">
              {/* 背景特效 */}
              <div className="absolute inset-0 bg-gradient-to-br from-purple-100 via-blue-50 to-green-100 opacity-80"></div>
              
              {/* 成功时的庆祝粒子效果 */}
              {synthesisResult.success && (
                <>
                  <div className="celebration-particles absolute inset-0 pointer-events-none">
                    {Array.from({length: 20}).map((_, i) => (
                      <div 
                        key={i}
                        className="particle absolute w-2 h-2 rounded-full bg-gradient-to-r from-yellow-400 to-orange-500"
                        style={{
                          left: `${Math.random() * 100}%`,
                          top: `${Math.random() * 100}%`,
                          animationDelay: `${Math.random() * 2}s`,
                          animationDuration: `${2 + Math.random() * 2}s`
                        }}
                      />
                    ))}
                  </div>
                  
                  {/* 闪光效果 */}
                  <div className="flash-overlay absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-50 transform -skew-x-12 animate-flash"></div>
                </>
              )}
              
              <div className="text-center relative z-10">
                <div className="text-6xl mb-4 animate-bounce-enhanced">
                  {synthesisResult.success ? '✨' : '💥'}
                </div>
                <h3 className={`text-xl font-bold mb-4 ${synthesisResult.success ? 'text-green-600' : 'text-red-600'} animate-pulse-glow`}>
                  {synthesisResult.message}
                </h3>
                {synthesisResult.success && synthesisResult.resultItem && (
                  <div className="mt-6 p-4 border-4 rounded-xl bg-white shadow-inner relative" style={{ borderColor: RARITY_COLORS[synthesisResult.resultItem.rarity] }}>
                    {/* 结果物品的光环效果 */}
                    <div 
                      className="absolute inset-0 rounded-xl animate-ring-glow"
                      style={{ 
                        background: `radial-gradient(circle, ${RARITY_COLORS[synthesisResult.resultItem.rarity]}30, transparent 70%)`,
                        filter: 'blur(8px)'
                      }}
                    ></div>
                    
                    <div className="relative z-10">
                      <div className="text-5xl mb-3 animate-icon-celebrate">{synthesisResult.resultItem.icon}</div>
                      <div className="font-bold text-lg text-gray-800">{synthesisResult.resultItem.name}</div>
                      <div 
                        className="text-sm font-bold mt-2 animate-text-shimmer"
                        style={{ color: RARITY_COLORS[synthesisResult.resultItem.rarity] }}
                      >
                        {RARITY_NAMES[synthesisResult.resultItem.rarity]}
                      </div>
                    </div>
                    
                    {/* 传说品质额外特效 */}
                    {(synthesisResult.resultItem.rarity === ItemRarity.GOLD || synthesisResult.resultItem.rarity === ItemRarity.GOLD_RED) && (
                      <div className="absolute inset-0 pointer-events-none">
                        {Array.from({length: 8}).map((_, i) => (
                          <div 
                            key={i}
                            className="absolute text-2xl animate-sparkle-orbit"
                            style={{
                              left: '50%',
                              top: '50%',
                              transformOrigin: '0 40px',
                              animationDelay: `${i * 0.2}s`,
                              transform: `rotate(${i * 45}deg)`
                            }}
                          >
                            ✨
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* 合成过程特效 */}
        {isProcessing && (
          <div className="fixed inset-0 pointer-events-none z-50">
            <div className="synthesis-process-effects absolute inset-0 flex items-center justify-center">
              {/* 能量环 */}
              <div className="energy-ring w-64 h-64 border-4 border-blue-400 rounded-full animate-spin-slow opacity-60"></div>
              <div className="energy-ring w-48 h-48 border-4 border-purple-400 rounded-full animate-spin-reverse opacity-80 absolute"></div>
              <div className="energy-ring w-32 h-32 border-4 border-yellow-400 rounded-full animate-spin opacity-90 absolute"></div>
              
              {/* 中心能量球 */}
              <div className="energy-core absolute w-16 h-16 bg-gradient-to-r from-blue-400 via-purple-500 to-pink-400 rounded-full animate-pulse-intense"></div>
              
              {/* 能量粒子 */}
              {Array.from({length: 12}).map((_, i) => (
                <div 
                  key={i}
                  className="energy-particle absolute w-3 h-3 bg-yellow-400 rounded-full animate-orbit"
                  style={{
                    animationDelay: `${i * 0.1}s`,
                    transform: `rotate(${i * 30}deg) translateX(100px)`
                  }}
                />
              ))}
            </div>
          </div>
        )}
      </div>

      {/* 新增CSS样式 */}
      <style>{`
        .synthesis-result-modal {
          animation: result-modal-appear 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
        }

        .particle {
          animation: particle-float 3s ease-out infinite;
        }

        .flash-overlay {
          animation: flash-sweep 0.8s ease-out;
        }

        .energy-ring {
          border-style: dashed;
          border-width: 3px;
        }

        .energy-core {
          filter: blur(1px);
        }

        @keyframes result-modal-appear {
          0% {
            opacity: 0;
            transform: scale(0.3) rotate(180deg);
          }
          70% {
            transform: scale(1.1) rotate(-10deg);
          }
          100% {
            opacity: 1;
            transform: scale(1) rotate(0deg);
          }
        }

        @keyframes particle-float {
          0% {
            opacity: 1;
            transform: translateY(0) scale(0);
          }
          50% {
            opacity: 1;
            transform: translateY(-20px) scale(1);
          }
          100% {
            opacity: 0;
            transform: translateY(-40px) scale(0) rotate(180deg);
          }
        }

        @keyframes flash-sweep {
          0% {
            transform: translateX(-100%) skewX(-12deg);
            opacity: 0;
          }
          50% {
            opacity: 1;
          }
          100% {
            transform: translateX(100%) skewX(-12deg);
            opacity: 0;
          }
        }

        @keyframes bounce-enhanced {
          0%, 20%, 53%, 80%, 100% {
            transform: translate3d(0,0,0) scale(1);
          }
          40%, 43% {
            transform: translate3d(0, -15px, 0) scale(1.1);
          }
          70% {
            transform: translate3d(0, -8px, 0) scale(1.05);
          }
          90% {
            transform: translate3d(0, -3px, 0) scale(1.02);
          }
        }

        @keyframes pulse-glow {
          0%, 100% {
            text-shadow: 0 0 5px currentColor;
          }
          50% {
            text-shadow: 0 0 20px currentColor, 0 0 30px currentColor;
          }
        }

        @keyframes ring-glow {
          0%, 100% {
            opacity: 0.3;
            transform: scale(1);
          }
          50% {
            opacity: 0.8;
            transform: scale(1.1);
          }
        }

        @keyframes icon-celebrate {
          0%, 100% {
            transform: scale(1) rotate(0deg);
          }
          25% {
            transform: scale(1.2) rotate(-5deg);
          }
          50% {
            transform: scale(1.3) rotate(5deg);
          }
          75% {
            transform: scale(1.1) rotate(-2deg);
          }
        }

        @keyframes text-shimmer {
          0% {
            background-position: -200% center;
          }
          100% {
            background-position: 200% center;
          }
        }

        @keyframes sparkle-orbit {
          0% {
            transform: rotate(0deg) translateX(40px) rotate(0deg);
            opacity: 0;
          }
          10% {
            opacity: 1;
          }
          90% {
            opacity: 1;
          }
          100% {
            transform: rotate(360deg) translateX(40px) rotate(-360deg);
            opacity: 0;
          }
        }

        @keyframes spin-slow {
          from {
            transform: rotate(0deg);
          }
          to {
            transform: rotate(360deg);
          }
        }

        @keyframes spin-reverse {
          from {
            transform: rotate(360deg);
          }
          to {
            transform: rotate(0deg);
          }
        }

        @keyframes pulse-intense {
          0%, 100% {
            transform: scale(1);
            opacity: 0.7;
          }
          50% {
            transform: scale(1.3);
            opacity: 1;
          }
        }

        @keyframes orbit {
          0% {
            transform: rotate(0deg) translateX(100px) rotate(0deg);
            opacity: 1;
          }
          100% {
            transform: rotate(360deg) translateX(100px) rotate(-360deg);
            opacity: 0.3;
          }
        }

        .animate-bounce-enhanced {
          animation: bounce-enhanced 2s infinite;
        }

        .animate-pulse-glow {
          animation: pulse-glow 2s ease-in-out infinite;
        }

        .animate-ring-glow {
          animation: ring-glow 2s ease-in-out infinite;
        }

        .animate-icon-celebrate {
          animation: icon-celebrate 1.5s ease-in-out;
        }

        .animate-text-shimmer {
          background: linear-gradient(90deg, currentColor, white, currentColor);
          background-size: 200% 100%;
          animation: text-shimmer 2s ease-in-out infinite;
          background-clip: text;
          -webkit-background-clip: text;
        }

        .animate-sparkle-orbit {
          animation: sparkle-orbit 3s linear infinite;
        }

        .animate-spin-slow {
          animation: spin-slow 4s linear infinite;
        }

        .animate-spin-reverse {
          animation: spin-reverse 3s linear infinite;
        }

        .animate-pulse-intense {
          animation: pulse-intense 1.5s ease-in-out infinite;
        }

        .animate-orbit {
          animation: orbit 2s linear infinite;
        }
      `}</style>
    </div>
  );
}; 