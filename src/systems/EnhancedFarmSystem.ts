import { 
  ExtendedFarmData, 
  FarmPlot, 
  CropInstance, 
  TerrainType, 
  Season, 
  WeatherType
} from '../types/gameModels'
import { ItemRarity } from '../types/lootbox'

// 增强农场系统 - 具有真正可玩性的农场管理
export class EnhancedFarmSystem {
  private farmData: ExtendedFarmData
  private gameTimer: NodeJS.Timeout | null = null
  private updateCallbacks: Set<() => void> = new Set()
  
  // 时间系统
  private currentGameTime: number = Date.now()
  private seasonStartTime: number = Date.now()
  private seasonDuration: number = 10 * 60 * 1000 // 10分钟一个季节
  private lastWeatherChange: number = Date.now()
  private weatherChangeInterval: number = 2 * 60 * 1000 // 2分钟换天气

  constructor(farmData: ExtendedFarmData) {
    this.farmData = farmData
    this.initializeFarm()
    this.startGameLoop()
  }

  // ===== 初始化系统 =====
  private initializeFarm() {
    if (this.farmData.plots.length === 0) {
      this.generateInitialPlots()
    }
    
    if (!this.farmData.currentSeason) {
      this.farmData.currentSeason = Season.SPRING
    }
    
    if (!this.farmData.currentWeather) {
      this.farmData.currentWeather = WeatherType.SUNNY
    }
  }

  private generateInitialPlots() {
    const gridSize = Math.sqrt(this.farmData.size.totalPlots)
    
    for (let y = 0; y < gridSize; y++) {
      for (let x = 0; x < gridSize; x++) {
        const plot: FarmPlot = {
          id: `plot_${x}_${y}`,
          x,
          y,
          terrain: this.getRandomTerrain(), // 随机生成地形而非单一地形
          soilFertility: 50 + Math.random() * 50, // 50-100随机肥力
          rotationHistory: [],
          diseases: [],
          pests: []
        }
        
        this.farmData.plots.push(plot)
      }
    }
    
    this.farmData.size.usedPlots = 0
  }

  // ===== 地形系统 =====
  private getRandomTerrain(): TerrainType {
    const terrainTypes = Object.values(TerrainType)
    const weights = {
      [TerrainType.PLAINS]: 40,        // 40% - 最常见
      [TerrainType.HILLS]: 25,         // 25% - 较常见
      [TerrainType.WETLANDS]: 15,      // 15% - 中等
      [TerrainType.MOUNTAINSIDE]: 15,  // 15% - 中等
      [TerrainType.FERTILE_VALLEY]: 5  // 5% - 稀有
    }
    
    const totalWeight = Object.values(weights).reduce((a, b) => a + b, 0)
    let random = Math.random() * totalWeight
    
    for (const terrain of terrainTypes) {
      random -= weights[terrain]
      if (random <= 0) {
        return terrain
      }
    }
    
    return TerrainType.PLAINS // 默认值
  }

  // ===== 地形改造功能 =====
  public transformTerrain(plotId: string, newTerrain: TerrainType, cost: number = 0): {success: boolean, message: string} {
    const plot = this.farmData.plots.find(p => p.id === plotId)
    if (!plot) {
      return { success: false, message: '地块不存在' }
    }

    if (plot.currentCrop) {
      return { success: false, message: '地块上有作物，无法改造地形' }
    }

    // 检查改造成本（这里简化为直接改造）
    const oldTerrain = plot.terrain
    plot.terrain = newTerrain
    
    // 地形改造会影响土壤肥力
    const fertilityChange = this.getTerrainFertilityBonus(newTerrain) - this.getTerrainFertilityBonus(oldTerrain)
    plot.soilFertility = Math.max(20, Math.min(100, plot.soilFertility + fertilityChange))

    console.log(`🌍 地形改造: ${plotId} 从 ${oldTerrain} 改造为 ${newTerrain}`)
    this.notifyUpdate()
    
    return { 
      success: true, 
      message: `成功将 ${plotId} 从 ${this.getTerrainName(oldTerrain)} 改造为 ${this.getTerrainName(newTerrain)}` 
    }
  }

  private getTerrainFertilityBonus(terrain: TerrainType): number {
    const bonuses = {
      [TerrainType.PLAINS]: 0,
      [TerrainType.HILLS]: -5,
      [TerrainType.WETLANDS]: 5,
      [TerrainType.FERTILE_VALLEY]: 15,
      [TerrainType.MOUNTAINSIDE]: -10
    }
    return bonuses[terrain] || 0
  }

  public getTerrainName(terrain: TerrainType): string {
    const names = {
      [TerrainType.PLAINS]: '平原',
      [TerrainType.HILLS]: '山地',
      [TerrainType.WETLANDS]: '湿地',
      [TerrainType.FERTILE_VALLEY]: '富饶山谷',
      [TerrainType.MOUNTAINSIDE]: '山腰'
    }
    return names[terrain] || '未知地形'
  }

  // ===== 地形探索功能 =====
  public discoverNewTerrain(): {terrain: TerrainType, message: string} {
    const discoveredTerrain = this.getRandomTerrain()
    const terrainName = this.getTerrainName(discoveredTerrain)
    
    console.log(`🔍 发现新地形: ${terrainName}`)
    
    return {
      terrain: discoveredTerrain,
      message: `🎉 你发现了一片 ${terrainName}！可以用于地形改造。`
    }
  }

  // ===== 地形商店功能 =====
  public getTerrainShop() {
    return [
      {
        terrain: TerrainType.PLAINS,
        name: '🌾 平原改造包',
        description: '适合大多数作物，玉米+20%，小麦+10%',
        cost: 100,
        rarity: 'common'
      },
      {
        terrain: TerrainType.HILLS,
        name: '⛰️ 山地改造包',
        description: '适合特殊作物，棉花+10%，大豆+5%',
        cost: 150,
        rarity: 'common'
      },
      {
        terrain: TerrainType.WETLANDS,
        name: '🌿 湿地改造包',
        description: '水稻种植专用，水稻+30%',
        cost: 200,
        rarity: 'uncommon'
      },
      {
        terrain: TerrainType.MOUNTAINSIDE,
        name: '🏔️ 山腰改造包',
        description: '果树种植优选，苹果+20%',
        cost: 250,
        rarity: 'uncommon'
      },
      {
        terrain: TerrainType.FERTILE_VALLEY,
        name: '🌺 富饶山谷改造包',
        description: '终极地形，所有作物+10%',
        cost: 500,
        rarity: 'rare'
      }
    ]
  }

  public purchaseTerrainTransform(terrain: TerrainType, playerCoins: number): {success: boolean, message: string, newCoins?: number} {
    const shop = this.getTerrainShop()
    const item = shop.find(i => i.terrain === terrain)
    
    if (!item) {
      return { success: false, message: '地形改造包不存在' }
    }
    
    if (playerCoins < item.cost) {
      return { success: false, message: `金币不足，需要 ${item.cost} 金币` }
    }
    
    return {
      success: true,
      message: `成功购买 ${item.name}，消耗 ${item.cost} 金币`,
      newCoins: playerCoins - item.cost
    }
  }

  // ===== 游戏循环 =====
  private startGameLoop() {
    if (this.gameTimer) {
      clearInterval(this.gameTimer)
    }
    
    this.gameTimer = setInterval(() => {
      this.updateGameState()
    }, 1000) // 每秒更新
  }

  private updateGameState() {
    this.currentGameTime += 1000
    
    this.updateCropGrowth()
    this.updateEnvironment()
    this.updateDiseasesAndPests()
    
    this.notifyUpdate()
  }

  // ===== 作物生长系统 =====
  private updateCropGrowth() {
    this.farmData.plots.forEach(plot => {
      if (plot.currentCrop) {
        this.updateSingleCrop(plot)
      }
    })
  }

  private updateSingleCrop(plot: FarmPlot) {
    if (!plot.currentCrop) return
    
    const crop = plot.currentCrop
    const growthModifier = this.calculateGrowthModifier(plot, crop)
    
    // 每秒增长的百分比
    const growthIncrement = growthModifier / 100
    crop.growthStage += growthIncrement
    
    // 限制在100%
    crop.growthStage = Math.min(100, crop.growthStage)
    
    // 更新健康度
    this.updateCropHealth(plot, crop)
  }

  private calculateGrowthModifier(plot: FarmPlot, crop: CropInstance): number {
    let modifier = 1.0
    
    // 地形影响
    modifier *= this.getTerrainModifier(plot.terrain, crop.cropType)
    
    // 季节影响
    modifier *= this.getSeasonModifier(crop.cropType)
    
    // 天气影响
    modifier *= this.getWeatherModifier()
    
    // 土壤肥力影响
    modifier *= (plot.soilFertility / 100)
    
    // 病虫害影响
    modifier *= this.getDiseaseModifier(plot)
    
    return Math.max(0.1, modifier) // 最低10%生长速度
  }

  private getTerrainModifier(terrain: TerrainType, cropType: string): number {
    // 地形对不同作物的影响
    const terrainEffects = {
      [TerrainType.PLAINS]: { default: 1.0, corn: 1.2, wheat: 1.1 },
      [TerrainType.HILLS]: { default: 0.9, cotton: 1.1, soybean: 1.05 },
      [TerrainType.WETLANDS]: { default: 0.8, rice: 1.3 },
      [TerrainType.FERTILE_VALLEY]: { default: 1.1 },
      [TerrainType.MOUNTAINSIDE]: { default: 0.7, apple: 1.2 }
    }
    
    return terrainEffects[terrain][cropType] || terrainEffects[terrain].default || 1.0
  }

  private getSeasonModifier(cropType: string): number {
    // 季节对作物的影响
    const seasonEffects = {
      [Season.SPRING]: { default: 1.2, wheat: 1.4, corn: 1.3 },
      [Season.SUMMER]: { default: 1.0, cotton: 1.3, soybean: 1.2 },
      [Season.AUTUMN]: { default: 0.9, apple: 1.4, jujube: 1.3 },
      [Season.WINTER]: { default: 0.6, wheat: 0.8 }
    }
    
    return seasonEffects[this.farmData.currentSeason][cropType] || 
           seasonEffects[this.farmData.currentSeason].default || 1.0
  }

  private getWeatherModifier(): number {
    const weatherEffects = {
      [WeatherType.SUNNY]: 1.0,
      [WeatherType.RAINY]: 1.1,
      [WeatherType.CLOUDY]: 0.95,
      [WeatherType.STORMY]: 0.8,
      [WeatherType.DROUGHT]: 0.6,
      [WeatherType.SNOW]: 0.4
    }
    
    return weatherEffects[this.farmData.currentWeather] || 1.0
  }

  private getDiseaseModifier(plot: FarmPlot): number {
    let modifier = 1.0
    
    plot.diseases.forEach(disease => {
      if (!disease.treated) {
        modifier -= (disease.severity / 100) * 0.5
      }
    })
    
    plot.pests.forEach(pest => {
      if (!pest.controlled) {
        modifier -= (pest.damage / 100) * 0.3
      }
    })
    
    return Math.max(0.1, modifier)
  }

  private updateCropHealth(plot: FarmPlot, crop: CropInstance) {
    // 健康度缓慢恢复
    crop.health = Math.min(100, crop.health + 0.1)
    
    // 疾病影响健康度
    plot.diseases.forEach(disease => {
      if (!disease.treated) {
        crop.health -= disease.severity * 0.01
      }
    })
    
    // 虫害影响健康度
    plot.pests.forEach(pest => {
      if (!pest.controlled) {
        crop.health -= pest.damage * 0.005
      }
    })
    
    crop.health = Math.max(0, crop.health)
  }

  // ===== 环境系统 =====
  private updateEnvironment() {
    this.updateSeason()
    this.updateWeather()
    this.updateSoilHealth()
  }

  private updateSeason() {
    const seasonElapsed = this.currentGameTime - this.seasonStartTime
    
    if (seasonElapsed >= this.seasonDuration) {
      const seasons = [Season.SPRING, Season.SUMMER, Season.AUTUMN, Season.WINTER]
      const currentIndex = seasons.indexOf(this.farmData.currentSeason)
      this.farmData.currentSeason = seasons[(currentIndex + 1) % seasons.length]
      this.seasonStartTime = this.currentGameTime
      
      console.log(`🌸 季节变更: ${this.farmData.currentSeason}`)
    }
  }

  private updateWeather() {
    const weatherElapsed = this.currentGameTime - this.lastWeatherChange
    
    if (weatherElapsed >= this.weatherChangeInterval) {
      this.farmData.currentWeather = this.generateRandomWeather()
      this.lastWeatherChange = this.currentGameTime
      
      console.log(`🌤️ 天气变化: ${this.farmData.currentWeather}`)
    }
  }

  private generateRandomWeather(): WeatherType {
    // 基于季节的天气概率
    const seasonWeather = {
      [Season.SPRING]: [WeatherType.SUNNY, WeatherType.RAINY, WeatherType.CLOUDY],
      [Season.SUMMER]: [WeatherType.SUNNY, WeatherType.DROUGHT, WeatherType.STORMY],
      [Season.AUTUMN]: [WeatherType.CLOUDY, WeatherType.RAINY, WeatherType.SUNNY],
      [Season.WINTER]: [WeatherType.SNOW, WeatherType.CLOUDY, WeatherType.SUNNY]
    }
    
    const options = seasonWeather[this.farmData.currentSeason]
    return options[Math.floor(Math.random() * options.length)]
  }

  private updateSoilHealth() {
    this.farmData.plots.forEach(plot => {
      // 土壤肥力自然恢复（很慢）
      plot.soilFertility = Math.min(100, plot.soilFertility + 0.01)
      
      // 轮作机制：连续种植同一作物会降低肥力
      if (plot.currentCrop && plot.rotationHistory.length > 0) {
        const lastCrop = plot.rotationHistory[plot.rotationHistory.length - 1]
        if (lastCrop === plot.currentCrop.cropType) {
          plot.soilFertility = Math.max(20, plot.soilFertility - 0.05)
        }
      }
    })
  }

  // ===== 病虫害系统 =====
  private updateDiseasesAndPests() {
    this.farmData.plots.forEach(plot => {
      if (plot.currentCrop) {
        this.checkForNewDiseases(plot)
        this.checkForNewPests(plot)
      }
    })
  }

  private checkForNewDiseases(plot: FarmPlot) {
    let diseaseProbability = 0.0001 // 基础疾病概率
    
    // 雨天增加疾病概率
    if (this.farmData.currentWeather === WeatherType.RAINY) {
      diseaseProbability *= 2
    }
    
    // 土壤肥力低增加疾病概率
    if (plot.soilFertility < 50) {
      diseaseProbability *= 1.5
    }
    
    if (Math.random() < diseaseProbability) {
      const diseaseTypes = ['leaf_spot', 'root_rot', 'powdery_mildew', 'bacterial_wilt']
      const diseaseType = diseaseTypes[Math.floor(Math.random() * diseaseTypes.length)]
      
      plot.diseases.push({
        type: diseaseType,
        severity: 10 + Math.random() * 20, // 10-30初始严重度
        startedAt: new Date(),
        treated: false
      })
      
      console.log(`🦠 疾病爆发: ${plot.id} - ${diseaseType}`)
    }
  }

  private checkForNewPests(plot: FarmPlot) {
    let pestProbability = 0.00008 // 基础虫害概率
    
    // 夏季增加虫害概率
    if (this.farmData.currentSeason === Season.SUMMER) {
      pestProbability *= 1.5
    }
    
    if (Math.random() < pestProbability) {
      const pestTypes = ['aphids', 'spider_mites', 'caterpillars', 'beetles']
      const pestType = pestTypes[Math.floor(Math.random() * pestTypes.length)]
      
      plot.pests.push({
        type: pestType,
        population: 5 + Math.random() * 15, // 5-20初始数量
        damage: 0,
        controlled: false
      })
      
      console.log(`🐛 虫害爆发: ${plot.id} - ${pestType}`)
    }
  }

  // ===== 公共API =====
  public getFarmData(): ExtendedFarmData {
    return { ...this.farmData }
  }

  public getPlot(plotId: string): FarmPlot | undefined {
    return this.farmData.plots.find(p => p.id === plotId)
  }

  public getAllPlots(): FarmPlot[] {
    return [...this.farmData.plots]
  }

  public getCurrentEnvironment() {
    return {
      season: this.farmData.currentSeason,
      weather: this.farmData.currentWeather,
      gameTime: this.currentGameTime
    }
  }

  // ===== 种植操作 =====
  public plantCrop(plotId: string, cropId: string): boolean {
    const plot = this.farmData.plots.find(p => p.id === plotId)
    if (!plot || plot.currentCrop) {
      return false
    }

    // 检查地形适宜性
    const terrainModifier = this.getTerrainModifier(plot.terrain, cropId)
    if (terrainModifier < 0.5) {
      console.log(`❌ 地形不适宜: ${plot.terrain} 不适合种植 ${cropId}`)
      return false
    }

    const crop: CropInstance = {
      id: `crop_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      cropType: cropId,
      plantedAt: new Date(),
      growthStage: 0,
      quality: ItemRarity.GRAY,
      health: 100,
      estimatedYield: 0,
      appliedItems: []
    }

    plot.currentCrop = crop
    this.farmData.size.usedPlots += 1

    // 添加到轮作历史
    plot.rotationHistory.push(cropId)
    if (plot.rotationHistory.length > 5) {
      plot.rotationHistory.shift()
    }

    // 种植消耗土壤肥力
    plot.soilFertility = Math.max(20, plot.soilFertility - 5)

    console.log(`🌱 种植成功: ${cropId} 在 ${plotId}`)
    this.notifyUpdate()
    return true
  }

  public harvestCrop(plotId: string): CropInstance | null {
    const plot = this.farmData.plots.find(p => p.id === plotId)
    if (!plot || !plot.currentCrop || plot.currentCrop.growthStage < 100) {
      return null
    }

    const harvestedCrop = plot.currentCrop
    plot.currentCrop = undefined
    this.farmData.size.usedPlots -= 1

    // 土壤肥力恢复
    plot.soilFertility = Math.min(100, plot.soilFertility + 10)

    console.log(`🎉 收获成功: ${harvestedCrop.cropType} 从 ${plotId}`)
    this.notifyUpdate()
    return harvestedCrop
  }

  // ===== 治疗和管理 =====
  public treatDisease(plotId: string, diseaseIndex: number): boolean {
    const plot = this.farmData.plots.find(p => p.id === plotId)
    if (!plot || diseaseIndex >= plot.diseases.length) {
      return false
    }

    const disease = plot.diseases[diseaseIndex]
    disease.treated = true
    disease.severity = Math.max(0, disease.severity - 50)

    console.log(`💊 疾病治疗: ${plot.id} - ${disease.type}`)
    return true
  }

  public controlPest(plotId: string, pestIndex: number): boolean {
    const plot = this.farmData.plots.find(p => p.id === plotId)
    if (!plot || pestIndex >= plot.pests.length) {
      return false
    }

    const pest = plot.pests[pestIndex]
    pest.controlled = true
    pest.population = Math.max(0, pest.population - 30)

    console.log(`🔫 虫害控制: ${plot.id} - ${pest.type}`)
    return true
  }

  public improveSoil(plotId: string, method: 'fertilizer' | 'compost' | 'lime'): boolean {
    const plot = this.farmData.plots.find(p => p.id === plotId)
    if (!plot) {
      return false
    }

    const improvements = {
      fertilizer: 20,
      compost: 15,
      lime: 10
    }

    plot.soilFertility = Math.min(100, plot.soilFertility + improvements[method])
    console.log(`🌱 土壤改良: ${plot.id} - ${method}`)
    return true
  }

  // ===== 事件系统 =====
  public addUpdateCallback(callback: () => void) {
    this.updateCallbacks.add(callback)
  }

  public removeUpdateCallback(callback: () => void) {
    this.updateCallbacks.delete(callback)
  }

  private notifyUpdate() {
    this.updateCallbacks.forEach(callback => callback())
  }

  // ===== 清理 =====
  public destroy() {
    if (this.gameTimer) {
      clearInterval(this.gameTimer)
      this.gameTimer = null
    }
    this.updateCallbacks.clear()
  }
}

export default EnhancedFarmSystem 