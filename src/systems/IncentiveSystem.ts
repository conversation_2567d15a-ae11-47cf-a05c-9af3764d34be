import { EventEmitter } from 'eventemitter3'
import { CurrencyType, UserCurrency, CurrencyTransaction, CurrencySource } from '../types/currency'
import { LootboxType, LootboxResult, ItemRarity, LootboxItem } from '../types/lootbox'
import { SynthesisR<PERSON>ipe, SynthesisResult, IndustryChain } from '../types/synthesis'
import { FuturesType, TradingResult } from '../types/futures'

// 激励系统事件类型
export enum IncentiveEventType {
  CURRENCY_EARNED = 'currency_earned',
  LOOTBOX_OPENED = 'lootbox_opened',
  ITEM_SYNTHESIZED = 'item_synthesized',
  ACHIEVEMENT_UNLOCKED = 'achievement_unlocked',
  MILESTONE_REACHED = 'milestone_reached',
  INDUSTRY_UPGRADED = 'industry_upgraded'
}

// 任务完成奖励
export interface TaskReward {
  taskType: string
  focusScore: number
  duration: number
  streakCount: number
  qualityBonus: number
  baseReward: {
    [CurrencyType.FOCUS_COIN]: number
    [CurrencyType.DISCIPLINE_TOKEN]: number
  }
  bonusRewards?: {
    currency?: Partial<UserCurrency>
    items?: LootboxItem[]
    lootboxes?: LootboxType[]
  }
}

// 用户进度数据
export interface UserProgress {
  level: number
  experience: number
  currency: UserCurrency
  
  // 专注任务统计
  totalFocusTime: number
  totalTasks: number
  averageFocusScore: number
  currentStreak: number
  maxStreak: number
  
  // 盲盒统计
  totalLootboxOpened: number
  rareItemsObtained: number
  
  // 合成统计
  totalSynthesis: number
  successfulSynthesis: number
  
  // 产业发展
  agriculturalLevel: number
  industrialLevel: number
  unlockedBuildings: string[]
  
  // 期货交易
  totalTrades: number
  totalProfit: number
  winRate: number
  
  // 成就
  achievements: string[]
  
  // 最后更新时间
  lastUpdated: number
}

// 里程碑定义
export interface Milestone {
  id: string
  name: string
  description: string
  category: 'focus' | 'synthesis' | 'trading' | 'industry' | 'collection'
  requirements: {
    level?: number
    totalFocusTime?: number
    streakCount?: number
    itemsCollected?: number
    synthesisCount?: number
    tradingProfit?: number
    buildingsUnlocked?: number
  }
  rewards: {
    currency: Partial<UserCurrency>
    items?: LootboxItem[]
    lootboxes?: LootboxType[]
    unlocks?: string[]
    specialEffects?: string[]
  }
  isRepeatable: boolean
  rarity: ItemRarity
}

export class IncentiveSystem extends EventEmitter {
  private userProgress: UserProgress
  private dailyResetTime: number = 24 * 60 * 60 * 1000 // 24小时
  private lastDailyReset: number = 0

  constructor(initialProgress?: Partial<UserProgress>) {
    super()
    
    this.userProgress = {
      level: 1,
      experience: 0,
      currency: {
        [CurrencyType.FOCUS_COIN]: 0,
        [CurrencyType.DISCIPLINE_TOKEN]: 0,
        [CurrencyType.FUTURES_CRYSTAL]: 0,
        [CurrencyType.GOLDEN_HARVEST]: 0
      },
      totalFocusTime: 0,
      totalTasks: 0,
      averageFocusScore: 0,
      currentStreak: 0,
      maxStreak: 0,
      totalLootboxOpened: 0,
      rareItemsObtained: 0,
      totalSynthesis: 0,
      successfulSynthesis: 0,
      agriculturalLevel: 1,
      industrialLevel: 1,
      unlockedBuildings: [],
      totalTrades: 0,
      totalProfit: 0,
      winRate: 0,
      achievements: [],
      lastUpdated: Date.now(),
      ...initialProgress
    }
    
    this.lastDailyReset = Date.now()
  }

  /**
   * 专注任务完成奖励
   */
  async rewardFocusTask(taskParams: {
    taskType: string
    focusScore: number
    duration: number
    isStreakContinued: boolean
  }): Promise<TaskReward> {
    const { taskType, focusScore, duration, isStreakContinued } = taskParams
    
    // 更新连击
    if (isStreakContinued) {
      this.userProgress.currentStreak++
      this.userProgress.maxStreak = Math.max(
        this.userProgress.maxStreak, 
        this.userProgress.currentStreak
      )
    } else {
      this.userProgress.currentStreak = 1
    }
    
    // 计算基础奖励
    const baseFocusCoin = Math.floor(duration / 60) * Math.max(1, focusScore / 20)
    const baseDisciplineToken = focusScore >= 80 ? Math.floor(baseFocusCoin / 10) : 0
    
    // 计算质量加成
    const qualityBonus = this.calculateQualityBonus(focusScore)
    
    // 计算连击加成
    const streakBonus = this.calculateStreakBonus(this.userProgress.currentStreak)
    
    // 应用所有加成
    const finalFocusCoin = Math.floor(baseFocusCoin * qualityBonus * streakBonus)
    const finalDisciplineToken = Math.floor(baseDisciplineToken * qualityBonus * streakBonus)
    
    const taskReward: TaskReward = {
      taskType,
      focusScore,
      duration,
      streakCount: this.userProgress.currentStreak,
      qualityBonus,
      baseReward: {
        [CurrencyType.FOCUS_COIN]: finalFocusCoin,
        [CurrencyType.DISCIPLINE_TOKEN]: finalDisciplineToken
      }
    }

    // 特殊奖励判定
    if (focusScore >= 95 && this.userProgress.currentStreak >= 5) {
      taskReward.bonusRewards = {
        currency: {
          [CurrencyType.FUTURES_CRYSTAL]: 1
        }
      }
    }

    if (this.userProgress.currentStreak >= 10) {
      taskReward.bonusRewards = taskReward.bonusRewards || {}
      taskReward.bonusRewards.lootboxes = [LootboxType.BASIC_FARM]
    }

    // 应用奖励
    await this.applyCurrencyReward(taskReward.baseReward)
    if (taskReward.bonusRewards?.currency) {
      await this.applyCurrencyReward(taskReward.bonusRewards.currency)
    }

    // 更新统计
    this.userProgress.totalTasks++
    this.userProgress.totalFocusTime += duration
    this.userProgress.averageFocusScore = 
      (this.userProgress.averageFocusScore * (this.userProgress.totalTasks - 1) + focusScore) / 
      this.userProgress.totalTasks

    // 检查里程碑和成就
    await this.checkMilestones()
    
    this.emit(IncentiveEventType.CURRENCY_EARNED, taskReward)
    
    return taskReward
  }

  /**
   * 开盲盒
   */
  async openLootbox(lootboxType: LootboxType, quantity: number = 1): Promise<LootboxResult[]> {
    const results: LootboxResult[] = []
    
    for (let i = 0; i < quantity; i++) {
      const result = await this.simulateLootboxOpen(lootboxType)
      results.push(result)
      
      // 扣除代币
      const config = this.getLootboxConfig(lootboxType)
      await this.spendCurrency(config.price.currency, config.price.amount)
      
      // 更新统计
      this.userProgress.totalLootboxOpened++
      
      // 检查稀有物品
      const hasRareItem = result.items.some(item => 
        [ItemRarity.ORANGE, ItemRarity.GOLD, ItemRarity.GOLD_RED].includes(item.item.rarity)
      )
      if (hasRareItem) {
        this.userProgress.rareItemsObtained++
      }
    }

    this.emit(IncentiveEventType.LOOTBOX_OPENED, results)
    return results
  }

  /**
   * 合成物品
   */
  async synthesizeItem(recipeId: string, materials: Record<string, number>): Promise<SynthesisResult> {
    const recipe = this.getSynthesisRecipe(recipeId)
    
    // 检查材料和费用
    const canSynthesize = this.checkSynthesisRequirements(recipe, materials)
    if (!canSynthesize) {
      throw new Error('合成材料或费用不足')
    }

    // 计算成功率
    const successRate = this.calculateSynthesisSuccessRate(recipe, materials)
    const isSuccess = Math.random() < successRate

    const result: SynthesisResult = {
      recipeId,
      success: isSuccess,
      materialsUsed: Object.entries(materials).map(([itemId, quantity]) => ({
        itemId,
        quantity,
        rarity: this.getItemRarity(itemId)
      })),
      timestamp: Date.now(),
      metadata: {
        successRate,
        bonusApplied: []
      }
    }

    // 消耗材料和费用
    await this.consumeSynthesisMaterials(recipe, materials)

    if (isSuccess) {
      result.result = {
        item: this.createItemFromRecipe(recipe),
        quantity: recipe.result.quantity,
        actualRarity: recipe.result.rarity
      }

      // 成功奖励
      result.bonusRewards = {
        experience: 50,
        currency: {
          [CurrencyType.FOCUS_COIN]: 10
        }
      }

      this.userProgress.successfulSynthesis++
    }

    this.userProgress.totalSynthesis++
    
    this.emit(IncentiveEventType.ITEM_SYNTHESIZED, result)
    return result
  }

  /**
   * 期货交易奖励
   */
  async rewardTrading(tradingResult: TradingResult): Promise<void> {
    const { pnl, pnlPercentage, riskLevel } = tradingResult

    // 基础奖励
    const baseExperience = 20
    const baseCurrency = {
      [CurrencyType.FOCUS_COIN]: 10,
      [CurrencyType.DISCIPLINE_TOKEN]: 2
    }

    // 盈利加成
    if (pnl > 0) {
      const profitMultiplier = this.getProfitMultiplier(pnlPercentage)
      baseCurrency[CurrencyType.FOCUS_COIN] *= profitMultiplier
      baseCurrency[CurrencyType.DISCIPLINE_TOKEN] *= profitMultiplier

      // 高收益特殊奖励
      if (pnlPercentage > 50) {
        baseCurrency[CurrencyType.FUTURES_CRYSTAL] = 1
      }
      if (pnlPercentage > 100) {
        baseCurrency[CurrencyType.GOLDEN_HARVEST] = 1
      }
    }

    // 风险调整奖励
    const riskMultiplier = this.getRiskMultiplier(riskLevel)
    Object.keys(baseCurrency).forEach(key => {
      baseCurrency[key as CurrencyType] *= riskMultiplier
    })

    await this.applyCurrencyReward(baseCurrency)

    // 更新交易统计
    this.userProgress.totalTrades++
    this.userProgress.totalProfit += pnl
    this.userProgress.winRate = this.calculateWinRate()
  }

  /**
   * 产业升级
   */
  async upgradeIndustry(category: 'agricultural' | 'industrial', buildingId: string): Promise<void> {
    if (category === 'agricultural') {
      this.userProgress.agriculturalLevel++
    } else {
      this.userProgress.industrialLevel++
    }

    this.userProgress.unlockedBuildings.push(buildingId)

    // 产业升级奖励
    const upgradeReward = {
      [CurrencyType.FOCUS_COIN]: 100 * this.userProgress.level,
      [CurrencyType.DISCIPLINE_TOKEN]: 10 * this.userProgress.level,
      [CurrencyType.FUTURES_CRYSTAL]: Math.floor(this.userProgress.level / 5)
    }

    await this.applyCurrencyReward(upgradeReward)
    
    this.emit(IncentiveEventType.INDUSTRY_UPGRADED, {
      category,
      buildingId,
      level: category === 'agricultural' ? 
        this.userProgress.agriculturalLevel : 
        this.userProgress.industrialLevel
    })
  }

  // 私有辅助方法
  private calculateQualityBonus(focusScore: number): number {
    if (focusScore >= 95) return 2.0
    if (focusScore >= 90) return 1.8
    if (focusScore >= 85) return 1.5
    if (focusScore >= 80) return 1.3
    if (focusScore >= 70) return 1.1
    return 1.0
  }

  private calculateStreakBonus(streak: number): number {
    if (streak >= 50) return 3.0
    if (streak >= 20) return 2.5
    if (streak >= 10) return 2.0
    if (streak >= 5) return 1.5
    if (streak >= 3) return 1.2
    return 1.0
  }

  private async applyCurrencyReward(reward: Partial<UserCurrency>): Promise<void> {
    Object.entries(reward).forEach(([currency, amount]) => {
      if (amount && amount > 0) {
        this.userProgress.currency[currency as CurrencyType] += amount
        
        // 记录交易
        const transaction: CurrencyTransaction = {
          id: this.generateTransactionId(),
          type: 'earn',
          currency: currency as CurrencyType,
          amount,
          source: CurrencySource.FOCUS_TASK,
          timestamp: Date.now()
        }
        
        this.emit('transaction', transaction)
      }
    })
  }

  private async spendCurrency(currency: CurrencyType, amount: number): Promise<void> {
    if (this.userProgress.currency[currency] < amount) {
      throw new Error(`${currency}不足`)
    }
    
    this.userProgress.currency[currency] -= amount
    
    const transaction: CurrencyTransaction = {
      id: this.generateTransactionId(),
      type: 'spend',
      currency,
      amount,
      source: CurrencySource.TRADING,
      timestamp: Date.now()
    }
    
    this.emit('transaction', transaction)
  }

  private async checkMilestones(): Promise<void> {
    const milestones = this.getMilestones()
    
    for (const milestone of milestones) {
      if (this.checkMilestoneRequirements(milestone)) {
        await this.unlockMilestone(milestone)
      }
    }
  }

  private checkMilestoneRequirements(milestone: Milestone): boolean {
    const req = milestone.requirements
    const progress = this.userProgress

    return (
      (!req.level || progress.level >= req.level) &&
      (!req.totalFocusTime || progress.totalFocusTime >= req.totalFocusTime) &&
      (!req.streakCount || progress.maxStreak >= req.streakCount) &&
      (!req.synthesisCount || progress.totalSynthesis >= req.synthesisCount) &&
      (!req.tradingProfit || progress.totalProfit >= req.tradingProfit) &&
      (!req.buildingsUnlocked || progress.unlockedBuildings.length >= req.buildingsUnlocked)
    )
  }

  private async unlockMilestone(milestone: Milestone): Promise<void> {
    // 防止重复解锁
    if (!milestone.isRepeatable && this.userProgress.achievements.includes(milestone.id)) {
      return
    }

    this.userProgress.achievements.push(milestone.id)
    await this.applyCurrencyReward(milestone.rewards.currency)

    this.emit(IncentiveEventType.MILESTONE_REACHED, milestone)
  }

  // 模拟方法（实际项目中需要连接真实数据）
  private simulateLootboxOpen(lootboxType: LootboxType): LootboxResult {
    // 这里应该实现真实的开盒逻辑
    return {} as LootboxResult
  }

  private getLootboxConfig(lootboxType: LootboxType): any {
    // 返回盲盒配置
    return {}
  }

  private getSynthesisRecipe(recipeId: string): SynthesisRecipe {
    // 返回合成配方
    return {} as SynthesisRecipe
  }

  private checkSynthesisRequirements(recipe: SynthesisRecipe, materials: Record<string, number>): boolean {
    // 检查合成要求
    return true
  }

  private calculateSynthesisSuccessRate(recipe: SynthesisRecipe, materials: Record<string, number>): number {
    // 计算合成成功率
    return 0.8
  }

  private getItemRarity(itemId: string): ItemRarity {
    // 获取物品稀有度
    return ItemRarity.GRAY
  }

  private createItemFromRecipe(recipe: SynthesisRecipe): LootboxItem {
    // 从配方创建物品
    return {} as LootboxItem
  }

  private async consumeSynthesisMaterials(recipe: SynthesisRecipe, materials: Record<string, number>): Promise<void> {
    // 消耗合成材料
  }

  private getProfitMultiplier(pnlPercentage: number): number {
    if (pnlPercentage > 100) return 3.0
    if (pnlPercentage > 50) return 2.0
    if (pnlPercentage > 20) return 1.5
    if (pnlPercentage > 10) return 1.2
    return 1.0
  }

  private getRiskMultiplier(riskLevel: string): number {
    switch (riskLevel) {
      case 'extreme': return 1.5
      case 'aggressive': return 1.3
      case 'moderate': return 1.1
      case 'conservative': return 1.0
      default: return 1.0
    }
  }

  private calculateWinRate(): number {
    // 计算胜率
    return 0.6
  }

  private getMilestones(): Milestone[] {
    // 返回里程碑列表
    return []
  }

  private generateTransactionId(): string {
    return `tx_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  // 公共接口
  public getUserProgress(): UserProgress {
    return { ...this.userProgress }
  }

  public getCurrency(type: CurrencyType): number {
    return this.userProgress.currency[type]
  }

  public async addExperience(amount: number): Promise<void> {
    this.userProgress.experience += amount
    
    // 检查升级
    const newLevel = this.calculateLevelFromExperience(this.userProgress.experience)
    if (newLevel > this.userProgress.level) {
      const oldLevel = this.userProgress.level
      this.userProgress.level = newLevel
      
      // 升级奖励
      const levelUpReward = {
        [CurrencyType.FOCUS_COIN]: 100 * newLevel,
        [CurrencyType.DISCIPLINE_TOKEN]: 10 * newLevel
      }
      
      await this.applyCurrencyReward(levelUpReward)
      
      this.emit('level_up', { oldLevel, newLevel, rewards: levelUpReward })
    }
  }

  private calculateLevelFromExperience(experience: number): number {
    return Math.floor(Math.sqrt(experience / 100)) + 1
  }
} 