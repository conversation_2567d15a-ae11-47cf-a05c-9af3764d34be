// 农场升级动画系统 - 创建升级时的视觉效果和庆祝动画
// 提供丰富的视觉反馈，增强升级时的成就感和沉浸感

import { FarmLevel } from './FarmUpgradeSystem'
import { UnlockContent } from './FarmUnlockSystem'

// 动画类型枚举
export enum AnimationType {
  LEVEL_UP = 'level_up',           // 等级提升动画
  CONTENT_UNLOCK = 'content_unlock', // 内容解锁动画
  REWARD_DISPLAY = 'reward_display', // 奖励展示动画
  FIREWORKS = 'fireworks',          // 烟花庆祝
  LIGHT_RAYS = 'light_rays',        // 光芒效果
  PARTICLE_BURST = 'particle_burst', // 粒子爆发
  GROW_EFFECT = 'grow_effect',      // 生长效果
  GLOW_PULSE = 'glow_pulse',        // 光晕脉冲
  TEXT_REVEAL = 'text_reveal',      // 文字揭示
  CONFETTI = 'confetti',            // 彩带庆祝
  SPARKLE = 'sparkle',              // 闪烁效果
  ZOOM_IN = 'zoom_in',              // 放大进入
  SLIDE_UP = 'slide_up'             // 向上滑动
}

// 动画配置接口
export interface AnimationConfig {
  type: AnimationType
  duration: number          // 动画持续时间（毫秒）
  delay?: number           // 延迟时间（毫秒）
  easing?: string          // 缓动函数
  intensity?: 'low' | 'medium' | 'high' | 'extreme'
  colors?: string[]        // 动画颜色
  particles?: number       // 粒子数量
  scale?: number          // 缩放比例
  opacity?: number        // 透明度
  loop?: boolean          // 是否循环
  soundEffect?: string    // 音效文件
  metadata?: {
    [key: string]: any
  }
}

// 升级动画序列
export interface UpgradeAnimationSequence {
  id: string
  name: string
  level: FarmLevel
  totalDuration: number
  animations: AnimationConfig[]
  onComplete?: () => void
  onProgress?: (progress: number) => void
}

// 动画播放状态
export interface AnimationState {
  isPlaying: boolean
  currentAnimation: string | null
  progress: number
  startTime: number
  pausedTime?: number
}

// 预定义动画配置
const PREDEFINED_ANIMATIONS: Record<AnimationType, AnimationConfig> = {
  [AnimationType.LEVEL_UP]: {
    type: AnimationType.LEVEL_UP,
    duration: 2000,
    easing: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
    intensity: 'high',
    colors: ['#FFD700', '#FFA500', '#FF6347'],
    scale: 1.2,
    soundEffect: 'level_up_fanfare.mp3'
  },
  
  [AnimationType.CONTENT_UNLOCK]: {
    type: AnimationType.CONTENT_UNLOCK,
    duration: 1500,
    easing: 'ease-out',
    intensity: 'medium',
    colors: ['#00FF7F', '#32CD32', '#7CFC00'],
    particles: 50,
    soundEffect: 'unlock_chime.mp3'
  },

  [AnimationType.REWARD_DISPLAY]: {
    type: AnimationType.REWARD_DISPLAY,
    duration: 1000,
    delay: 500,
    easing: 'ease-in-out',
    intensity: 'medium',
    colors: ['#DA70D6', '#BA55D3', '#9370DB'],
    scale: 1.1,
    soundEffect: 'reward_collect.mp3'
  },

  [AnimationType.FIREWORKS]: {
    type: AnimationType.FIREWORKS,
    duration: 3000,
    easing: 'ease-out',
    intensity: 'extreme',
    colors: ['#FF0000', '#00FF00', '#0000FF', '#FFFF00', '#FF00FF'],
    particles: 200,
    soundEffect: 'fireworks_burst.mp3'
  },

  [AnimationType.LIGHT_RAYS]: {
    type: AnimationType.LIGHT_RAYS,
    duration: 1800,
    easing: 'linear',
    intensity: 'high',
    colors: ['#FFFFFF', '#FFFAF0', '#F0F8FF'],
    opacity: 0.8,
    soundEffect: 'divine_light.mp3'
  },

  [AnimationType.PARTICLE_BURST]: {
    type: AnimationType.PARTICLE_BURST,
    duration: 1200,
    easing: 'ease-out',
    intensity: 'high',
    colors: ['#FFD700', '#FFEC8C', '#FFF8DC'],
    particles: 100,
    soundEffect: 'magic_sparkle.mp3'
  },

  [AnimationType.GROW_EFFECT]: {
    type: AnimationType.GROW_EFFECT,
    duration: 800,
    easing: 'cubic-bezier(0.175, 0.885, 0.32, 1.275)',
    intensity: 'medium',
    colors: ['#90EE90', '#98FB98', '#F0FFF0'],
    scale: 1.3,
    soundEffect: 'plant_grow.mp3'
  },

  [AnimationType.GLOW_PULSE]: {
    type: AnimationType.GLOW_PULSE,
    duration: 2000,
    easing: 'ease-in-out',
    intensity: 'medium',
    colors: ['#FFE4B5', '#FFEFD5', '#FFF8DC'],
    loop: true,
    opacity: 0.6,
    soundEffect: 'gentle_hum.mp3'
  },

  [AnimationType.TEXT_REVEAL]: {
    type: AnimationType.TEXT_REVEAL,
    duration: 1000,
    delay: 300,
    easing: 'ease-out',
    intensity: 'low',
    colors: ['#2F4F4F', '#708090', '#B0C4DE'],
    soundEffect: 'typewriter.mp3'
  },

  [AnimationType.CONFETTI]: {
    type: AnimationType.CONFETTI,
    duration: 2500,
    easing: 'ease-out',
    intensity: 'high',
    colors: ['#FF69B4', '#FFB6C1', '#FFC0CB', '#FF1493'],
    particles: 150,
    soundEffect: 'celebration.mp3'
  },

  [AnimationType.SPARKLE]: {
    type: AnimationType.SPARKLE,
    duration: 1500,
    easing: 'ease-in-out',
    intensity: 'medium',
    colors: ['#E6E6FA', '#D8BFD8', '#DDA0DD'],
    particles: 30,
    loop: true,
    soundEffect: 'sparkle_twinkle.mp3'
  },

  [AnimationType.ZOOM_IN]: {
    type: AnimationType.ZOOM_IN,
    duration: 600,
    easing: 'ease-out',
    intensity: 'low',
    scale: 1.05,
    soundEffect: 'zoom_in.mp3'
  },

  [AnimationType.SLIDE_UP]: {
    type: AnimationType.SLIDE_UP,
    duration: 800,
    easing: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)',
    intensity: 'low',
    soundEffect: 'slide_up.mp3'
  }
}

// 等级对应动画序列
const LEVEL_ANIMATION_SEQUENCES: Record<FarmLevel, UpgradeAnimationSequence> = {
  [FarmLevel.APPRENTICE]: {
    id: 'apprentice_upgrade',
    name: '学徒农场升级',
    level: FarmLevel.APPRENTICE,
    totalDuration: 4500,
    animations: [
      { ...PREDEFINED_ANIMATIONS[AnimationType.GROW_EFFECT], delay: 0 },
      { ...PREDEFINED_ANIMATIONS[AnimationType.LEVEL_UP], delay: 800 },
      { ...PREDEFINED_ANIMATIONS[AnimationType.CONTENT_UNLOCK], delay: 2000 },
      { ...PREDEFINED_ANIMATIONS[AnimationType.SPARKLE], delay: 2500 }
    ]
  },

  [FarmLevel.EXPERIENCED]: {
    id: 'experienced_upgrade',
    name: '熟练农场升级',
    level: FarmLevel.EXPERIENCED,
    totalDuration: 5200,
    animations: [
      { ...PREDEFINED_ANIMATIONS[AnimationType.LIGHT_RAYS], delay: 0 },
      { ...PREDEFINED_ANIMATIONS[AnimationType.LEVEL_UP], delay: 500 },
      { ...PREDEFINED_ANIMATIONS[AnimationType.PARTICLE_BURST], delay: 1500 },
      { ...PREDEFINED_ANIMATIONS[AnimationType.CONTENT_UNLOCK], delay: 2200 },
      { ...PREDEFINED_ANIMATIONS[AnimationType.GLOW_PULSE], delay: 3000 }
    ]
  },

  [FarmLevel.EXPERT]: {
    id: 'expert_upgrade',
    name: '专家农场升级',
    level: FarmLevel.EXPERT,
    totalDuration: 6000,
    animations: [
      { ...PREDEFINED_ANIMATIONS[AnimationType.FIREWORKS], delay: 0 },
      { ...PREDEFINED_ANIMATIONS[AnimationType.LEVEL_UP], delay: 800 },
      { ...PREDEFINED_ANIMATIONS[AnimationType.LIGHT_RAYS], delay: 1500 },
      { ...PREDEFINED_ANIMATIONS[AnimationType.CONTENT_UNLOCK], delay: 2800 },
      { ...PREDEFINED_ANIMATIONS[AnimationType.REWARD_DISPLAY], delay: 3500 },
      { ...PREDEFINED_ANIMATIONS[AnimationType.CONFETTI], delay: 4000 }
    ]
  },

  [FarmLevel.MASTER]: {
    id: 'master_upgrade',
    name: '大师农场升级',
    level: FarmLevel.MASTER,
    totalDuration: 7500,
    animations: [
      { ...PREDEFINED_ANIMATIONS[AnimationType.LIGHT_RAYS], delay: 0 },
      { ...PREDEFINED_ANIMATIONS[AnimationType.FIREWORKS], delay: 500 },
      { ...PREDEFINED_ANIMATIONS[AnimationType.LEVEL_UP], delay: 1200 },
      { ...PREDEFINED_ANIMATIONS[AnimationType.PARTICLE_BURST], delay: 2000 },
      { ...PREDEFINED_ANIMATIONS[AnimationType.CONTENT_UNLOCK], delay: 3200 },
      { ...PREDEFINED_ANIMATIONS[AnimationType.REWARD_DISPLAY], delay: 4000 },
      { ...PREDEFINED_ANIMATIONS[AnimationType.CONFETTI], delay: 5000 },
      { ...PREDEFINED_ANIMATIONS[AnimationType.GLOW_PULSE], delay: 6000 }
    ]
  },

  [FarmLevel.LEGENDARY]: {
    id: 'legendary_upgrade',
    name: '传奇农场升级',
    level: FarmLevel.LEGENDARY,
    totalDuration: 10000,
    animations: [
      { 
        ...PREDEFINED_ANIMATIONS[AnimationType.LIGHT_RAYS], 
        delay: 0,
        intensity: 'extreme',
        colors: ['#FFD700', '#FFA500', '#FF6347']
      },
      { 
        ...PREDEFINED_ANIMATIONS[AnimationType.FIREWORKS], 
        delay: 1000,
        duration: 4000,
        particles: 300
      },
      { 
        ...PREDEFINED_ANIMATIONS[AnimationType.LEVEL_UP], 
        delay: 2000,
        scale: 1.5,
        colors: ['#FFD700', '#FF6347', '#DA70D6']
      },
      { 
        ...PREDEFINED_ANIMATIONS[AnimationType.PARTICLE_BURST], 
        delay: 3500,
        particles: 200
      },
      { 
        ...PREDEFINED_ANIMATIONS[AnimationType.CONTENT_UNLOCK], 
        delay: 5000,
        intensity: 'extreme'
      },
      { 
        ...PREDEFINED_ANIMATIONS[AnimationType.REWARD_DISPLAY], 
        delay: 6000,
        scale: 1.3
      },
      { 
        ...PREDEFINED_ANIMATIONS[AnimationType.CONFETTI], 
        delay: 7000,
        duration: 3000,
        particles: 250
      },
      { 
        ...PREDEFINED_ANIMATIONS[AnimationType.GLOW_PULSE], 
        delay: 8000,
        duration: 5000,
        loop: true
      }
    ]
  },

  // 其他等级使用默认动画序列
  [FarmLevel.NOVICE]: {
    id: 'novice_upgrade',
    name: '新手农场升级',
    level: FarmLevel.NOVICE,
    totalDuration: 0,
    animations: []
  },

  [FarmLevel.MYTHICAL]: {
    id: 'mythical_upgrade',
    name: '神话农场升级',
    level: FarmLevel.MYTHICAL,
    totalDuration: 8000,
    animations: [
      { ...PREDEFINED_ANIMATIONS[AnimationType.LIGHT_RAYS], delay: 0 },
      { ...PREDEFINED_ANIMATIONS[AnimationType.FIREWORKS], delay: 1000 },
      { ...PREDEFINED_ANIMATIONS[AnimationType.LEVEL_UP], delay: 2000 },
      { ...PREDEFINED_ANIMATIONS[AnimationType.CONTENT_UNLOCK], delay: 4000 },
      { ...PREDEFINED_ANIMATIONS[AnimationType.CONFETTI], delay: 5500 }
    ]
  },

  [FarmLevel.TRANSCENDENT]: {
    id: 'transcendent_upgrade',
    name: '超凡农场升级',
    level: FarmLevel.TRANSCENDENT,
    totalDuration: 8500,
    animations: [
      { ...PREDEFINED_ANIMATIONS[AnimationType.LIGHT_RAYS], delay: 0 },
      { ...PREDEFINED_ANIMATIONS[AnimationType.FIREWORKS], delay: 1000 },
      { ...PREDEFINED_ANIMATIONS[AnimationType.LEVEL_UP], delay: 2000 },
      { ...PREDEFINED_ANIMATIONS[AnimationType.CONTENT_UNLOCK], delay: 4000 },
      { ...PREDEFINED_ANIMATIONS[AnimationType.CONFETTI], delay: 6000 }
    ]
  },

  [FarmLevel.DIVINE]: {
    id: 'divine_upgrade',
    name: '神圣农场升级',
    level: FarmLevel.DIVINE,
    totalDuration: 9000,
    animations: [
      { ...PREDEFINED_ANIMATIONS[AnimationType.LIGHT_RAYS], delay: 0 },
      { ...PREDEFINED_ANIMATIONS[AnimationType.FIREWORKS], delay: 1000 },
      { ...PREDEFINED_ANIMATIONS[AnimationType.LEVEL_UP], delay: 2500 },
      { ...PREDEFINED_ANIMATIONS[AnimationType.CONTENT_UNLOCK], delay: 4500 },
      { ...PREDEFINED_ANIMATIONS[AnimationType.CONFETTI], delay: 6500 }
    ]
  },

  [FarmLevel.ULTIMATE]: {
    id: 'ultimate_upgrade',
    name: '终极农场升级',
    level: FarmLevel.ULTIMATE,
    totalDuration: 12000,
    animations: [
      { 
        ...PREDEFINED_ANIMATIONS[AnimationType.LIGHT_RAYS], 
        delay: 0,
        intensity: 'extreme',
        duration: 3000
      },
      { 
        ...PREDEFINED_ANIMATIONS[AnimationType.FIREWORKS], 
        delay: 1500,
        duration: 5000,
        particles: 400
      },
      { 
        ...PREDEFINED_ANIMATIONS[AnimationType.LEVEL_UP], 
        delay: 3000,
        scale: 2.0
      },
      { 
        ...PREDEFINED_ANIMATIONS[AnimationType.CONTENT_UNLOCK], 
        delay: 6000,
        intensity: 'extreme'
      },
      { 
        ...PREDEFINED_ANIMATIONS[AnimationType.CONFETTI], 
        delay: 8000,
        duration: 4000,
        particles: 300
      }
    ]
  }
}

export class FarmUpgradeAnimations {
  private animationState: AnimationState = {
    isPlaying: false,
    currentAnimation: null,
    progress: 0,
    startTime: 0
  }

  private activeAnimations: Map<string, {
    config: AnimationConfig
    element: HTMLElement
    startTime: number
  }> = new Map()

  private soundEnabled: boolean = true
  private performanceMode: 'auto' | 'high' | 'medium' | 'low' = 'auto'

  constructor() {
    this.detectPerformanceMode()
  }

  /**
   * 播放升级动画序列
   */
  async playUpgradeAnimation(
    farmLevel: FarmLevel,
    unlockedContent: UnlockContent[],
    targetElement?: HTMLElement
  ): Promise<void> {
    if (this.animationState.isPlaying) {
      console.warn('Animation already playing, skipping new animation')
      return
    }

    const sequence = LEVEL_ANIMATION_SEQUENCES[farmLevel]
    if (!sequence || sequence.animations.length === 0) {
      console.log('No animation sequence defined for level:', farmLevel)
      return
    }

    this.animationState.isPlaying = true
    this.animationState.currentAnimation = sequence.id
    this.animationState.startTime = Date.now()
    this.animationState.progress = 0

    try {
      // 播放主要升级动画序列
      await this.playAnimationSequence(sequence, targetElement)

      // 播放解锁内容动画
      if (unlockedContent.length > 0) {
        await this.playUnlockAnimations(unlockedContent, targetElement)
      }

      // 播放完成后的效果
      await this.playCompletionEffects(farmLevel, targetElement)

    } catch (error) {
      console.error('Animation playback error:', error)
    } finally {
      this.animationState.isPlaying = false
      this.animationState.currentAnimation = null
      this.animationState.progress = 100
    }
  }

  /**
   * 播放单个解锁内容动画
   */
  async playUnlockAnimation(
    content: UnlockContent,
    targetElement?: HTMLElement
  ): Promise<void> {
    const animationConfig = this.getUnlockAnimationConfig(content)
    await this.playAnimation(animationConfig, targetElement)
  }

  /**
   * 停止所有动画
   */
  stopAllAnimations(): void {
    this.activeAnimations.forEach((animation, id) => {
      this.stopAnimation(id)
    })
    this.activeAnimations.clear()
    this.animationState.isPlaying = false
    this.animationState.currentAnimation = null
  }

  /**
   * 暂停/恢复动画
   */
  pauseAnimation(): void {
    if (this.animationState.isPlaying && !this.animationState.pausedTime) {
      this.animationState.pausedTime = Date.now()
    }
  }

  resumeAnimation(): void {
    if (this.animationState.pausedTime) {
      const pauseDuration = Date.now() - this.animationState.pausedTime
      this.animationState.startTime += pauseDuration
      delete this.animationState.pausedTime
    }
  }

  /**
   * 获取动画状态
   */
  getAnimationState(): AnimationState {
    return { ...this.animationState }
  }

  /**
   * 设置性能模式
   */
  setPerformanceMode(mode: 'auto' | 'high' | 'medium' | 'low'): void {
    this.performanceMode = mode
  }

  /**
   * 设置音效开关
   */
  setSoundEnabled(enabled: boolean): void {
    this.soundEnabled = enabled
  }

  /**
   * 私有方法：播放动画序列
   */
  private async playAnimationSequence(
    sequence: UpgradeAnimationSequence,
    targetElement?: HTMLElement
  ): Promise<void> {
    const startTime = Date.now()

    // 播放所有动画
    const animationPromises = sequence.animations.map(async (config, index) => {
      if (config.delay) {
        await this.delay(config.delay)
      }
      
      return this.playAnimation(config, targetElement, `${sequence.id}_${index}`)
    })

    // 更新进度
    const progressInterval = setInterval(() => {
      const elapsed = Date.now() - startTime
      const progress = Math.min((elapsed / sequence.totalDuration) * 100, 100)
      this.animationState.progress = progress

      if (sequence.onProgress) {
        sequence.onProgress(progress)
      }

      if (progress >= 100) {
        clearInterval(progressInterval)
        if (sequence.onComplete) {
          sequence.onComplete()
        }
      }
    }, 16) // 60fps

    // 等待所有动画完成
    await Promise.all(animationPromises)
    clearInterval(progressInterval)
  }

  /**
   * 私有方法：播放单个动画
   */
  private async playAnimation(
    config: AnimationConfig,
    targetElement?: HTMLElement,
    animationId?: string
  ): Promise<void> {
    const id = animationId || `animation_${Date.now()}_${Math.random()}`
    const element = targetElement || document.body

    // 根据性能模式调整动画配置
    const adjustedConfig = this.adjustAnimationForPerformance(config)

    // 播放音效
    if (this.soundEnabled && adjustedConfig.soundEffect) {
      this.playSound(adjustedConfig.soundEffect)
    }

    // 创建动画元素
    const animationElement = this.createAnimationElement(adjustedConfig)
    element.appendChild(animationElement)

    // 记录活动动画
    this.activeAnimations.set(id, {
      config: adjustedConfig,
      element: animationElement,
      startTime: Date.now()
    })

    // 执行动画
    return new Promise<void>((resolve) => {
      this.executeAnimation(animationElement, adjustedConfig, () => {
        this.cleanupAnimation(id)
        resolve()
      })
    })
  }

  /**
   * 私有方法：播放解锁动画
   */
  private async playUnlockAnimations(
    unlockedContent: UnlockContent[],
    targetElement?: HTMLElement
  ): Promise<void> {
    // 为每个解锁内容播放动画，延迟执行以避免重叠
    for (let i = 0; i < unlockedContent.length; i++) {
      const content = unlockedContent[i]
      const delay = i * 300 // 每个动画间隔300ms

      setTimeout(async () => {
        await this.playUnlockAnimation(content, targetElement)
      }, delay)
    }

    // 等待所有解锁动画完成
    const totalDelay = unlockedContent.length * 300 + 1500 // 额外1.5秒缓冲
    await this.delay(totalDelay)
  }

  /**
   * 私有方法：播放完成效果
   */
  private async playCompletionEffects(
    farmLevel: FarmLevel,
    targetElement?: HTMLElement
  ): Promise<void> {
    // 根据等级播放不同的完成效果
    if (farmLevel >= FarmLevel.LEGENDARY) {
      await this.playAnimation(PREDEFINED_ANIMATIONS[AnimationType.GLOW_PULSE], targetElement)
    } else if (farmLevel >= FarmLevel.EXPERT) {
      await this.playAnimation(PREDEFINED_ANIMATIONS[AnimationType.SPARKLE], targetElement)
    }
  }

  /**
   * 私有方法：获取解锁动画配置
   */
  private getUnlockAnimationConfig(content: UnlockContent): AnimationConfig {
    const baseConfig = PREDEFINED_ANIMATIONS[AnimationType.CONTENT_UNLOCK]
    
    // 根据内容类型和稀有度调整动画
    const rarityIntensity: Record<string, 'low' | 'medium' | 'high' | 'extreme'> = {
      common: 'low',
      uncommon: 'medium',
      rare: 'medium',
      epic: 'high',
      legendary: 'extreme'
    }

    return {
      ...baseConfig,
      intensity: rarityIntensity[content.rarity] || 'medium',
      duration: baseConfig.duration + (content.rarity === 'legendary' ? 500 : 0)
    }
  }

  /**
   * 私有方法：根据性能模式调整动画
   */
  private adjustAnimationForPerformance(config: AnimationConfig): AnimationConfig {
    const adjustedConfig = { ...config }

    switch (this.performanceMode) {
      case 'low':
        adjustedConfig.particles = Math.floor((adjustedConfig.particles || 50) * 0.3)
        adjustedConfig.duration = Math.floor(adjustedConfig.duration * 0.7)
        break
      case 'medium':
        adjustedConfig.particles = Math.floor((adjustedConfig.particles || 50) * 0.6)
        adjustedConfig.duration = Math.floor(adjustedConfig.duration * 0.85)
        break
      case 'high':
        // 保持原始配置
        break
      case 'auto':
        // 自动检测性能
        adjustedConfig.particles = Math.floor((adjustedConfig.particles || 50) * this.getPerformanceMultiplier())
        break
    }

    return adjustedConfig
  }

  /**
   * 私有方法：创建动画元素
   */
  private createAnimationElement(config: AnimationConfig): HTMLElement {
    const element = document.createElement('div')
    element.className = `farm-animation farm-animation-${config.type}`
    element.style.cssText = `
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      pointer-events: none;
      z-index: 10000;
    `

    // 根据动画类型设置样式
    this.applyAnimationStyles(element, config)

    return element
  }

  /**
   * 私有方法：应用动画样式
   */
  private applyAnimationStyles(element: HTMLElement, config: AnimationConfig): void {
    // 这里应该根据动画类型应用不同的样式
    // 为了简化，我们使用CSS类名来处理样式
    if (config.colors && config.colors.length > 0) {
      element.style.color = config.colors[0]
    }

    if (config.scale) {
      element.style.transform += ` scale(${config.scale})`
    }

    if (config.opacity !== undefined) {
      element.style.opacity = config.opacity.toString()
    }
  }

  /**
   * 私有方法：执行动画
   */
  private executeAnimation(
    element: HTMLElement,
    config: AnimationConfig,
    onComplete: () => void
  ): void {
    // 使用Web Animation API或CSS动画
    // 这里为了简化，使用setTimeout模拟动画完成
    setTimeout(() => {
      onComplete()
    }, config.duration)
  }

  /**
   * 私有方法：播放音效
   */
  private playSound(soundFile: string): void {
    try {
      const audio = new Audio(`/sounds/${soundFile}`)
      audio.volume = 0.3
      audio.play().catch(error => {
        console.warn('Failed to play sound:', error)
      })
    } catch (error) {
      console.warn('Sound playback error:', error)
    }
  }

  /**
   * 私有方法：检测性能模式
   */
  private detectPerformanceMode(): void {
    // 简单的性能检测逻辑
    const deviceMemory = (navigator as any).deviceMemory
    const hardwareConcurrency = navigator.hardwareConcurrency

    if (deviceMemory && deviceMemory < 4) {
      this.performanceMode = 'low'
    } else if (hardwareConcurrency && hardwareConcurrency < 4) {
      this.performanceMode = 'medium'
    } else {
      this.performanceMode = 'high'
    }
  }

  /**
   * 私有方法：获取性能倍数
   */
  private getPerformanceMultiplier(): number {
    switch (this.performanceMode) {
      case 'low': return 0.3
      case 'medium': return 0.6
      case 'high': return 1.0
      default: return 0.8
    }
  }

  /**
   * 私有方法：停止单个动画
   */
  private stopAnimation(animationId: string): void {
    const animation = this.activeAnimations.get(animationId)
    if (animation) {
      animation.element.remove()
      this.activeAnimations.delete(animationId)
    }
  }

  /**
   * 私有方法：清理动画
   */
  private cleanupAnimation(animationId: string): void {
    this.stopAnimation(animationId)
  }

  /**
   * 私有方法：延迟函数
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
} 