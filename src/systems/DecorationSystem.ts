import { 
  DecorationItem, 
  PlacedDecoration, 
  DecorationManager, 
  FarmTheme,
  DecorationType,
  DecorationEffectType,
  UnlockCondition,
  UnlockConditionType
} from '../types/decoration'
import { DECORATION_ITEMS, FARM_THEMES } from '../data/decorationItems'
import { ItemRarity } from '../types/lootbox'

export class DecorationSystem {
  private manager: DecorationManager
  private availableItems: Map<string, DecorationItem>
  private availableThemes: Map<string, FarmTheme>
  
  // 事件监听器
  private eventListeners: Map<string, Function[]> = new Map()

  constructor() {
    this.availableItems = new Map()
    this.availableThemes = new Map()
    
    // 初始化装饰道具数据
    DECORATION_ITEMS.forEach(item => {
      this.availableItems.set(item.id, item)
    })
    
    // 初始化主题数据
    FARM_THEMES.forEach(theme => {
      this.availableThemes.set(theme.id, theme)
    })
    
    // 初始化管理器状态
    this.manager = this.createDefaultManager()
  }

  private createDefaultManager(): DecorationManager {
    return {
      ownedDecorations: {},
      placedDecorations: [],
      currentTheme: 'natural_paradise', // 默认主题
      unlockedThemes: ['natural_paradise'],
      beautyStats: {
        totalBeauty: 0,
        beautyByType: {} as Record<DecorationType, number>,
        beautyHistory: []
      },
      shopState: {
        lastVisitTime: Date.now(),
        recentPurchases: [],
        wishlist: []
      }
    }
  }

  // ============ 购买系统 ============
  
  /**
   * 购买装饰道具
   */
  async purchaseDecoration(decorationId: string, quantity: number = 1): Promise<{
    success: boolean
    message: string
    cost?: number
    item?: DecorationItem
  }> {
    const item = this.availableItems.get(decorationId)
    if (!item) {
      return { success: false, message: '装饰道具不存在' }
    }

    // 检查解锁条件
    const unlockResult = await this.checkUnlockConditions(item.economy.unlockConditions || [])
    if (!unlockResult.canUnlock) {
      return { 
        success: false, 
        message: `解锁条件不满足: ${unlockResult.missingConditions.join(', ')}` 
      }
    }

    // 检查等级要求
    const currentLevel = await this.getCurrentFarmLevel()
    if (currentLevel < item.economy.unlockLevel) {
      return { 
        success: false, 
        message: `需要农场等级 ${item.economy.unlockLevel}，当前等级 ${currentLevel}` 
      }
    }

    const totalCost = item.economy.basePrice * quantity
    
    // 检查货币是否足够（这里假设使用专注代币）
    const hasEnoughTokens = await this.checkFocusTokens(totalCost)
    if (!hasEnoughTokens) {
      return { 
        success: false, 
        message: `专注代币不足，需要 ${totalCost} 个代币` 
      }
    }

    // 执行购买
    await this.deductFocusTokens(totalCost)
    
    // 添加到拥有的装饰道具
    const currentOwned = this.manager.ownedDecorations[decorationId] || 0
    this.manager.ownedDecorations[decorationId] = currentOwned + quantity
    
    // 记录购买历史
    this.manager.shopState.recentPurchases.unshift(decorationId)
    if (this.manager.shopState.recentPurchases.length > 10) {
      this.manager.shopState.recentPurchases.pop()
    }

    // 触发购买事件
    this.emit('decorationPurchased', {
      item,
      quantity,
      cost: totalCost
    })

    return {
      success: true,
      message: `成功购买 ${item.name} x${quantity}`,
      cost: totalCost,
      item
    }
  }

  /**
   * 购买农场主题
   */
  async purchaseTheme(themeId: string): Promise<{
    success: boolean
    message: string
    cost?: number
    theme?: FarmTheme
  }> {
    const theme = this.availableThemes.get(themeId)
    if (!theme) {
      return { success: false, message: '主题不存在' }
    }

    // 检查是否已解锁
    if (this.manager.unlockedThemes.includes(themeId)) {
      return { success: false, message: '主题已解锁' }
    }

    // 检查解锁条件
    const unlockResult = await this.checkUnlockConditions(theme.unlockConditions)
    if (!unlockResult.canUnlock) {
      return { 
        success: false, 
        message: `解锁条件不满足: ${unlockResult.missingConditions.join(', ')}` 
      }
    }

    // 检查货币
    if (theme.price > 0) {
      const hasEnoughTokens = await this.checkFocusTokens(theme.price)
      if (!hasEnoughTokens) {
        return { 
          success: false, 
          message: `专注代币不足，需要 ${theme.price} 个代币` 
        }
      }
      await this.deductFocusTokens(theme.price)
    }

    // 解锁主题
    this.manager.unlockedThemes.push(themeId)

    // 触发主题解锁事件
    this.emit('themeUnlocked', { theme })

    return {
      success: true,
      message: `成功解锁主题：${theme.name}`,
      cost: theme.price,
      theme
    }
  }

  // ============ 放置系统 ============
  
  /**
   * 放置装饰道具
   */
  placeDecoration(decorationId: string, x: number, y: number, variant?: string): {
    success: boolean
    message: string
    placedDecoration?: PlacedDecoration
  } {
    const item = this.availableItems.get(decorationId)
    if (!item) {
      return { success: false, message: '装饰道具不存在' }
    }

    // 检查是否拥有该装饰道具
    const ownedCount = this.manager.ownedDecorations[decorationId] || 0
    if (ownedCount <= 0) {
      return { success: false, message: '未拥有该装饰道具' }
    }

    // 检查放置位置是否可用
    const canPlace = this.checkPlacementValid(item, x, y)
    if (!canPlace.valid) {
      return { success: false, message: canPlace.reason }
    }

    // 创建放置实例
    const placedDecoration: PlacedDecoration = {
      instanceId: this.generateInstanceId(),
      decorationId,
      x,
      y,
      rotation: 0,
      variant,
      level: 1,
      placedTime: Date.now(),
      condition: 100,
      needsMaintenance: false
    }

    // 添加到已放置列表
    this.manager.placedDecorations.push(placedDecoration)

    // 减少拥有数量
    this.manager.ownedDecorations[decorationId] = ownedCount - 1

    // 更新美观度统计
    this.updateBeautyStats()

    // 触发放置事件
    this.emit('decorationPlaced', { placedDecoration, item })

    return {
      success: true,
      message: `成功放置 ${item.name}`,
      placedDecoration
    }
  }

  /**
   * 移除装饰道具
   */
  removeDecoration(instanceId: string): {
    success: boolean
    message: string
    removedDecoration?: PlacedDecoration
  } {
    const index = this.manager.placedDecorations.findIndex(
      decoration => decoration.instanceId === instanceId
    )
    
    if (index === -1) {
      return { success: false, message: '装饰道具不存在' }
    }

    const removedDecoration = this.manager.placedDecorations[index]
    const item = this.availableItems.get(removedDecoration.decorationId)
    
    // 从已放置列表中移除
    this.manager.placedDecorations.splice(index, 1)

    // 返还到库存（考虑出售价格）
    if (item) {
      const currentOwned = this.manager.ownedDecorations[removedDecoration.decorationId] || 0
      this.manager.ownedDecorations[removedDecoration.decorationId] = currentOwned + 1
    }

    // 更新美观度统计
    this.updateBeautyStats()

    // 触发移除事件
    this.emit('decorationRemoved', { removedDecoration, item })

    return {
      success: true,
      message: `成功移除装饰道具`,
      removedDecoration
    }
  }

  /**
   * 移动装饰道具
   */
  moveDecoration(instanceId: string, newX: number, newY: number): {
    success: boolean
    message: string
  } {
    const decoration = this.manager.placedDecorations.find(
      d => d.instanceId === instanceId
    )
    
    if (!decoration) {
      return { success: false, message: '装饰道具不存在' }
    }

    const item = this.availableItems.get(decoration.decorationId)
    if (!item) {
      return { success: false, message: '装饰道具配置不存在' }
    }

    // 检查新位置是否可用
    const canPlace = this.checkPlacementValid(item, newX, newY, instanceId)
    if (!canPlace.valid) {
      return { success: false, message: canPlace.reason }
    }

    // 更新位置
    decoration.x = newX
    decoration.y = newY

    // 更新美观度统计
    this.updateBeautyStats()

    // 触发移动事件
    this.emit('decorationMoved', { decoration, item, newX, newY })

    return {
      success: true,
      message: '成功移动装饰道具'
    }
  }

  // ============ 主题系统 ============
  
  /**
   * 切换农场主题
   */
  applyTheme(themeId: string): {
    success: boolean
    message: string
    theme?: FarmTheme
  } {
    if (!this.manager.unlockedThemes.includes(themeId)) {
      return { success: false, message: '主题未解锁' }
    }

    const theme = this.availableThemes.get(themeId)
    if (!theme) {
      return { success: false, message: '主题不存在' }
    }

    const previousTheme = this.manager.currentTheme
    this.manager.currentTheme = themeId

    // 触发主题切换事件
    this.emit('themeApplied', { theme, previousTheme })

    return {
      success: true,
      message: `成功应用主题：${theme.name}`,
      theme
    }
  }

  /**
   * 预览主题（不实际应用）
   */
  previewTheme(themeId: string): {
    success: boolean
    message: string
    theme?: FarmTheme
    previewData?: any
  } {
    const theme = this.availableThemes.get(themeId)
    if (!theme) {
      return { success: false, message: '主题不存在' }
    }

    // 计算预览数据
    const previewData = this.calculateThemePreview(theme)

    // 触发主题预览事件
    this.emit('themePreview', { theme, previewData })

    return {
      success: true,
      message: `正在预览主题：${theme.name}`,
      theme,
      previewData
    }
  }

  /**
   * 获取当前主题信息
   */
  getCurrentTheme(): FarmTheme | null {
    return this.availableThemes.get(this.manager.currentTheme) || null
  }

  /**
   * 获取主题效果加成
   */
  getThemeEffects(themeId?: string): Map<DecorationEffectType, number> {
    const targetThemeId = themeId || this.manager.currentTheme
    const theme = this.availableThemes.get(targetThemeId)
    const effects = new Map<DecorationEffectType, number>()

    if (theme?.themeEffects) {
      theme.themeEffects.forEach(effect => {
        effects.set(effect.type, effect.value)
      })
    }

    return effects
  }

  /**
   * 计算主题与装饰道具的兼容性
   */
  calculateThemeCompatibility(themeId: string): {
    compatibleDecorations: string[]
    bonusDecorations: string[]
    discouragedDecorations: string[]
  } {
    const theme = this.availableThemes.get(themeId)
    if (!theme) {
      return {
        compatibleDecorations: [],
        bonusDecorations: [],
        discouragedDecorations: []
      }
    }

    const preferences = theme.decorationPreferences
    const allDecorations = Array.from(this.availableItems.keys())

    return {
      compatibleDecorations: allDecorations.filter(id => {
        const item = this.availableItems.get(id)
        return item && (
          preferences.recommendedTypes?.includes(item.type) ||
          preferences.bonusTypes?.includes(item.type) ||
          (!preferences.restrictedTypes?.includes(item.type))
        )
      }),
      bonusDecorations: allDecorations.filter(id => {
        const item = this.availableItems.get(id)
        return item && preferences.bonusTypes?.includes(item.type)
      }),
      discouragedDecorations: allDecorations.filter(id => {
        const item = this.availableItems.get(id)
        return item && preferences.restrictedTypes?.includes(item.type)
      })
    }
  }

  /**
   * 获取季节性主题变化
   */
  getSeasonalThemeVariation(themeId: string, season: 'spring' | 'summer' | 'autumn' | 'winter'): {
    tint?: string
    intensity?: number
    availableDecorations?: string[]
  } {
    const theme = this.availableThemes.get(themeId)
    if (!theme?.decorationPreferences?.seasonalVariations) {
      return {}
    }

    const variation = theme.decorationPreferences.seasonalVariations[season]
    if (!variation) {
      return {}
    }

    // 获取该季节推荐的装饰道具
    const seasonalDecorations = Array.from(this.availableItems.values())
      .filter(item => {
        // 检查是否有季节性标记
        return item.temporal?.seasonalAvailability?.includes(season) ||
               (!item.temporal?.seasonalAvailability) // 如果没有季节限制，则全年可用
      })
      .map(item => item.id)

    return {
      ...variation,
      availableDecorations: seasonalDecorations
    }
  }

  /**
   * 计算主题切换后的美观度变化
   */
  calculateThemeBeautyChange(newThemeId: string): {
    currentBeauty: number
    newBeauty: number
    change: number
    effectChanges: Map<DecorationEffectType, { old: number, new: number, change: number }>
  } {
    // 当前状态
    const currentEffects = this.calculateTotalEffects()
    
    // 模拟切换主题后的状态
    const originalTheme = this.manager.currentTheme
    this.manager.currentTheme = newThemeId
    const newEffects = this.calculateTotalEffects()
    this.manager.currentTheme = originalTheme // 恢复原状态

    // 计算效果变化
    const effectChanges = new Map<DecorationEffectType, { old: number, new: number, change: number }>()
    
    // 合并所有效果类型
    const allEffectTypes = new Set([
      ...currentEffects.effects.keys(),
      ...newEffects.effects.keys()
    ])

    allEffectTypes.forEach(effectType => {
      const oldValue = currentEffects.effects.get(effectType) || 0
      const newValue = newEffects.effects.get(effectType) || 0
      effectChanges.set(effectType, {
        old: oldValue,
        new: newValue,
        change: newValue - oldValue
      })
    })

    return {
      currentBeauty: currentEffects.totalBeauty,
      newBeauty: newEffects.totalBeauty,
      change: newEffects.totalBeauty - currentEffects.totalBeauty,
      effectChanges
    }
  }

  /**
   * 获取推荐主题（基于当前装饰布局）
   */
  getRecommendedThemes(): {
    themeId: string
    theme: FarmTheme
    compatibilityScore: number
    reason: string
  }[] {
    const placedDecorations = this.manager.placedDecorations
    const decorationTypes = placedDecorations.map(placed => {
      const item = this.availableItems.get(placed.decorationId)
      return item?.type
    }).filter(Boolean) as DecorationType[]

    const recommendations = Array.from(this.availableThemes.values())
      .filter(theme => this.manager.unlockedThemes.includes(theme.id))
      .map(theme => {
        const preferences = theme.decorationPreferences
        let score = 0
        let reasons: string[] = []

        // 计算兼容性得分
        decorationTypes.forEach(type => {
          if (preferences.recommendedTypes?.includes(type)) {
            score += 3
            reasons.push(`推荐${type}类装饰`)
          } else if (preferences.bonusTypes?.includes(type)) {
            score += 5
            reasons.push(`${type}类装饰获得加成`)
          } else if (preferences.restrictedTypes?.includes(type)) {
            score -= 2
            reasons.push(`不推荐${type}类装饰`)
          }
        })

        // 考虑主题效果的有用性
        theme.themeEffects?.forEach(effect => {
          if (effect.value > 0) {
            score += 1
            reasons.push(effect.description)
          }
        })

        return {
          themeId: theme.id,
          theme,
          compatibilityScore: score,
          reason: reasons.slice(0, 3).join('；') || '通用主题'
        }
      })
      .sort((a, b) => b.compatibilityScore - a.compatibilityScore)

    return recommendations
  }

  /**
   * 计算主题预览数据
   */
  private calculateThemePreview(theme: FarmTheme): any {
    const currentDecorations = this.manager.placedDecorations
    const beautyChange = this.calculateThemeBeautyChange(theme.id)
    const compatibility = this.calculateThemeCompatibility(theme.id)

    return {
      beautyChange,
      compatibility,
      currentDecorations: currentDecorations.length,
      compatibleDecorations: compatibility.compatibleDecorations.length,
      bonusDecorations: compatibility.bonusDecorations.length,
      themeEffects: theme.themeEffects || [],
      visualPreview: {
        backgroundTexture: theme.visual.backgroundTexture,
        colorPalette: theme.visual.colorPalette,
        lightingPreset: theme.visual.lightingPreset
      }
    }
  }

  // ============ 查询功能 ============
  
  /**
   * 获取可购买的装饰道具
   */
  async getAvailableDecorations(category?: DecorationType): Promise<DecorationItem[]> {
    let items = Array.from(this.availableItems.values())
    
    if (category) {
      items = items.filter(item => item.type === category)
    }

    // 过滤解锁条件
    const availableItems: DecorationItem[] = []
    for (const item of items) {
      const unlockResult = await this.checkUnlockConditions(item.economy.unlockConditions || [])
      const currentLevel = await this.getCurrentFarmLevel()
      
      if (unlockResult.canUnlock && currentLevel >= item.economy.unlockLevel) {
        availableItems.push(item)
      }
    }

    return availableItems
  }

  /**
   * 获取已拥有的装饰道具
   */
  getOwnedDecorations(): { item: DecorationItem, quantity: number }[] {
    const result: { item: DecorationItem, quantity: number }[] = []
    
    Object.entries(this.manager.ownedDecorations).forEach(([id, quantity]) => {
      if (quantity > 0) {
        const item = this.availableItems.get(id)
        if (item) {
          result.push({ item, quantity })
        }
      }
    })

    return result
  }

  /**
   * 获取已放置的装饰道具
   */
  getPlacedDecorations(): { decoration: PlacedDecoration, item: DecorationItem }[] {
    return this.manager.placedDecorations.map(decoration => {
      const item = this.availableItems.get(decoration.decorationId)
      return { decoration, item: item! }
    }).filter(result => result.item)
  }

  /**
   * 获取可用主题
   */
  async getAvailableThemes(): Promise<FarmTheme[]> {
    const themes: FarmTheme[] = []
    
    for (const theme of this.availableThemes.values()) {
      const unlockResult = await this.checkUnlockConditions(theme.unlockConditions)
      if (unlockResult.canUnlock) {
        themes.push(theme)
      }
    }

    return themes
  }

  /**
   * 计算总美观度和效果
   */
  calculateTotalEffects(): {
    totalBeauty: number
    effects: Map<DecorationEffectType, number>
  } {
    const effects = new Map<DecorationEffectType, number>()
    let totalBeauty = 0

    // 计算放置装饰的效果
    this.manager.placedDecorations.forEach(placed => {
      const item = this.availableItems.get(placed.decorationId)
      if (!item) return

      totalBeauty += item.beautyValue * (placed.level / item.maxLevel)

      // 累加装饰效果
      item.effects?.forEach(effect => {
        const currentValue = effects.get(effect.type) || 0
        effects.set(effect.type, currentValue + effect.value)
      })
    })

    // 计算主题效果
    const currentTheme = this.availableThemes.get(this.manager.currentTheme)
    if (currentTheme?.themeEffects) {
      currentTheme.themeEffects.forEach(effect => {
        const currentValue = effects.get(effect.type) || 0
        effects.set(effect.type, currentValue + effect.value)
      })
    }

    return { totalBeauty, effects }
  }

  // ============ 辅助方法 ============
  
  private checkPlacementValid(
    item: DecorationItem, 
    x: number, 
    y: number, 
    excludeInstanceId?: string
  ): { valid: boolean, reason: string } {
    // 检查位置是否被占用
    const existingDecoration = this.manager.placedDecorations.find(
      decoration => 
        decoration.instanceId !== excludeInstanceId &&
        this.isOverlapping(decoration, x, y, item.size.width, item.size.height)
    )

    if (existingDecoration && !item.placement.canOverlap) {
      return { valid: false, reason: '位置已被占用' }
    }

    // 这里可以添加更多放置规则检查
    // 如：边界检查、特殊区域检查等

    return { valid: true, reason: '' }
  }

  private isOverlapping(
    decoration: PlacedDecoration, 
    x: number, 
    y: number, 
    width: number, 
    height: number
  ): boolean {
    const item = this.availableItems.get(decoration.decorationId)
    if (!item) return false

    return !(
      decoration.x + item.size.width <= x ||
      decoration.x >= x + width ||
      decoration.y + item.size.height <= y ||
      decoration.y >= y + height
    )
  }

  private async checkUnlockConditions(conditions: UnlockCondition[]): Promise<{
    canUnlock: boolean
    missingConditions: string[]
  }> {
    const missingConditions: string[] = []

    for (const condition of conditions) {
      let satisfied = false

      switch (condition.type) {
        case UnlockConditionType.FARM_LEVEL:
          const currentLevel = await this.getCurrentFarmLevel()
          satisfied = currentLevel >= (condition.value as number)
          break
        case UnlockConditionType.FOCUS_TOKENS:
          const totalTokens = await this.getTotalFocusTokens()
          satisfied = totalTokens >= (condition.value as number)
          break
        case UnlockConditionType.ITEM_COUNT:
          const ownedCount = Object.keys(this.manager.ownedDecorations).length
          satisfied = ownedCount >= (condition.value as number)
          break
        case UnlockConditionType.ACHIEVEMENT:
          satisfied = await this.hasAchievement(condition.value as string)
          break
        default:
          satisfied = true
      }

      if (!satisfied) {
        missingConditions.push(condition.description)
      }
    }

    return {
      canUnlock: missingConditions.length === 0,
      missingConditions
    }
  }

  private updateBeautyStats(): void {
    const { totalBeauty, effects } = this.calculateTotalEffects()
    
    this.manager.beautyStats.totalBeauty = totalBeauty
    
    // 更新按类型分组的美观度
    const beautyByType: Record<DecorationType, number> = {} as Record<DecorationType, number>
    
    this.manager.placedDecorations.forEach(placed => {
      const item = this.availableItems.get(placed.decorationId)
      if (item) {
        beautyByType[item.type] = (beautyByType[item.type] || 0) + item.beautyValue
      }
    })
    
    this.manager.beautyStats.beautyByType = beautyByType

    // 记录历史
    this.manager.beautyStats.beautyHistory.push({
      timestamp: Date.now(),
      totalBeauty,
      majorChanges: []
    })

    // 保持历史记录不超过100条
    if (this.manager.beautyStats.beautyHistory.length > 100) {
      this.manager.beautyStats.beautyHistory.shift()
    }
  }

  private generateInstanceId(): string {
    return `decoration_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  // ============ 外部依赖方法（需要与其他系统集成） ============
  
  private async getCurrentFarmLevel(): Promise<number> {
    // TODO: 集成农场升级系统
    return 10 // 临时返回值
  }

  private async checkFocusTokens(amount: number): Promise<boolean> {
    // TODO: 集成专注代币系统
    return true // 临时返回值
  }

  private async deductFocusTokens(amount: number): Promise<void> {
    // TODO: 集成专注代币系统
    console.log(`扣除专注代币: ${amount}`)
  }

  private async getTotalFocusTokens(): Promise<number> {
    // TODO: 集成专注代币系统
    return 10000 // 临时返回值
  }

  private async hasAchievement(achievementId: string): Promise<boolean> {
    // TODO: 集成成就系统
    return false // 临时返回值
  }

  // ============ 事件系统 ============
  
  on(event: string, callback: Function): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, [])
    }
    this.eventListeners.get(event)!.push(callback)
  }

  off(event: string, callback: Function): void {
    const listeners = this.eventListeners.get(event)
    if (listeners) {
      const index = listeners.indexOf(callback)
      if (index > -1) {
        listeners.splice(index, 1)
      }
    }
  }

  private emit(event: string, data: any): void {
    const listeners = this.eventListeners.get(event)
    if (listeners) {
      listeners.forEach(callback => callback(data))
    }
  }

  // ============ 持久化 ============
  
  /**
   * 导出管理器状态
   */
  exportState(): DecorationManager {
    return JSON.parse(JSON.stringify(this.manager))
  }

  /**
   * 导入管理器状态
   */
  importState(state: DecorationManager): void {
    this.manager = state
    this.emit('stateImported', { state })
  }

  /**
   * 获取当前状态
   */
  getManager(): DecorationManager {
    return this.manager
  }
} 