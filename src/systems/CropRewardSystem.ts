import { 
  CropInstance, 
  CropType, 
  CropQuality, 
  CROP_CONFIGS 
} from '../types/crop'
import { 
  SelfDisciplineType, 
  getCropDetectionConfig,
  CropBalanceAnalyzer 
} from '../data/cropSpecifications'

// 奖励类型枚举
export enum RewardType {
  EXPERIENCE = 'experience',
  GROWTH_POINTS = 'growth_points',
  SPECIAL_ITEM = 'special_item',
  ACHIEVEMENT = 'achievement',
  CURRENCY = 'currency',
  BOOST = 'boost',
  KNOWLEDGE = 'knowledge',
  SOCIAL_CREDIT = 'social_credit'
}

// 奖励物品接口
export interface RewardItem {
  id: string
  type: RewardType
  name: string
  description: string
  icon: string
  value: number
  rarity: 'common' | 'uncommon' | 'rare' | 'epic' | 'legendary'
  stackable: boolean
  metadata?: {
    duration?: number // 持续时间（毫秒）
    multiplier?: number // 倍数效果
    category?: string // 分类
    effects?: string[] // 特殊效果
  }
}

// 奖励包接口
export interface RewardBundle {
  id: string
  name: string
  description: string
  items: {
    item: RewardItem
    quantity: number
    probability: number // 0-1, 获得概率
  }[]
  totalValue: number
  bonusMultiplier: number
}

// 收获结果接口
export interface HarvestResult {
  cropId: string
  cropType: CropType
  quality: CropQuality
  baseRewards: RewardBundle
  bonusRewards: RewardBundle[]
  totalExperience: number
  totalGrowthPoints: number
  specialItems: RewardItem[]
  achievements: string[]
  metadata: {
    harvestTime: number
    totalGrowthTime: number
    averageFocusScore: number
    sessionsContributed: number
    behaviorConsistency: number
    streakBonus: number
  }
}

// 动态奖励配置
export interface DynamicRewardConfig {
  baseMultiplier: number
  qualityMultipliers: Record<CropQuality, number>
  behaviorBonuses: Record<SelfDisciplineType, number>
  streakThresholds: { threshold: number; bonus: number }[]
  timeOfDayBonuses: Record<string, number>
  seasonalBonuses: Record<string, number>
  weatherBonuses: Record<string, number>
}

// 预定义奖励物品
export const REWARD_ITEMS: Record<string, RewardItem> = {
  // 经验类
  basic_exp: {
    id: 'basic_exp',
    type: RewardType.EXPERIENCE,
    name: '基础经验',
    description: '通过专注获得的经验值',
    icon: '⭐',
    value: 10,
    rarity: 'common',
    stackable: true
  },
  bonus_exp: {
    id: 'bonus_exp',
    type: RewardType.EXPERIENCE,
    name: '奖励经验',
    description: '高品质收获的额外经验',
    icon: '🌟',
    value: 25,
    rarity: 'uncommon',
    stackable: true
  },
  
  // 成长点类
  growth_points: {
    id: 'growth_points',
    type: RewardType.GROWTH_POINTS,
    name: '成长点数',
    description: '用于提升作物培育能力',
    icon: '🌱',
    value: 5,
    rarity: 'common',
    stackable: true
  },
  
  // 知识类特殊物品
  wisdom_essence: {
    id: 'wisdom_essence',
    type: RewardType.SPECIAL_ITEM,
    name: '智慧精华',
    description: '从知识花中提取的智慧能量',
    icon: '✨',
    value: 50,
    rarity: 'rare',
    stackable: true,
    metadata: {
      category: 'knowledge',
      effects: ['boost_learning_speed', 'enhance_memory']
    }
  },
  focus_crystal: {
    id: 'focus_crystal',
    type: RewardType.SPECIAL_ITEM,
    name: '专注水晶',
    description: '凝聚专注力的神奇水晶',
    icon: '💎',
    value: 100,
    rarity: 'epic',
    stackable: true,
    metadata: {
      category: 'focus',
      effects: ['deep_focus_boost', 'distraction_immunity']
    }
  },
  
  // 力量类特殊物品
  power_essence: {
    id: 'power_essence',
    type: RewardType.SPECIAL_ITEM,
    name: '力量精华',
    description: '从力量树中获得的体能精华',
    icon: '💪',
    value: 60,
    rarity: 'rare',
    stackable: true,
    metadata: {
      category: 'strength',
      effects: ['physical_boost', 'endurance_enhancement']
    }
  },
  endurance_boost: {
    id: 'endurance_boost',
    type: RewardType.BOOST,
    name: '耐力提升',
    description: '暂时增强身体耐力',
    icon: '🏃',
    value: 30,
    rarity: 'uncommon',
    stackable: false,
    metadata: {
      duration: 2 * 60 * 60 * 1000, // 2小时
      multiplier: 1.5,
      category: 'strength'
    }
  },
  
  // 时间管理类
  efficiency_essence: {
    id: 'efficiency_essence',
    type: RewardType.SPECIAL_ITEM,
    name: '效率精华',
    description: '时间管理的智慧结晶',
    icon: '⏰',
    value: 40,
    rarity: 'uncommon',
    stackable: true,
    metadata: {
      category: 'time',
      effects: ['time_boost', 'productivity_enhancement']
    }
  },
  time_boost: {
    id: 'time_boost',
    type: RewardType.BOOST,
    name: '时间加速',
    description: '暂时提升时间感知和效率',
    icon: '⚡',
    value: 25,
    rarity: 'uncommon',
    stackable: false,
    metadata: {
      duration: 1 * 60 * 60 * 1000, // 1小时
      multiplier: 2.0,
      category: 'time'
    }
  },
  
  // 冥想类
  serenity_essence: {
    id: 'serenity_essence',
    type: RewardType.SPECIAL_ITEM,
    name: '宁静精华',
    description: '冥想中获得的内心平静',
    icon: '🧘',
    value: 80,
    rarity: 'rare',
    stackable: true,
    metadata: {
      category: 'meditation',
      effects: ['inner_peace', 'stress_reduction']
    }
  },
  enlightenment_crystal: {
    id: 'enlightenment_crystal',
    type: RewardType.SPECIAL_ITEM,
    name: '开悟水晶',
    description: '深度冥想的终极收获',
    icon: '🔮',
    value: 200,
    rarity: 'legendary',
    stackable: true,
    metadata: {
      category: 'meditation',
      effects: ['enlightenment_state', 'wisdom_amplification']
    }
  },
  
  // 专注类
  concentration_essence: {
    id: 'concentration_essence',
    type: RewardType.SPECIAL_ITEM,
    name: '凝神精华',
    description: '深度专注状态的能量结晶',
    icon: '🎯',
    value: 90,
    rarity: 'rare',
    stackable: true,
    metadata: {
      category: 'focus',
      effects: ['concentration_boost', 'mental_clarity']
    }
  },
  clarity_boost: {
    id: 'clarity_boost',
    type: RewardType.BOOST,
    name: '思维清晰',
    description: '大幅提升思维清晰度',
    icon: '🧠',
    value: 40,
    rarity: 'rare',
    stackable: false,
    metadata: {
      duration: 3 * 60 * 60 * 1000, // 3小时
      multiplier: 1.8,
      category: 'focus'
    }
  },
  
  // 读书类
  knowledge_vine: {
    id: 'knowledge_vine',
    type: RewardType.SPECIAL_ITEM,
    name: '知识藤蔓',
    description: '承载无穷智慧的神奇藤蔓',
    icon: '📿',
    value: 70,
    rarity: 'rare',
    stackable: true,
    metadata: {
      category: 'reading',
      effects: ['knowledge_network', 'learning_acceleration']
    }
  },
  wisdom_scroll: {
    id: 'wisdom_scroll',
    type: RewardType.KNOWLEDGE,
    name: '智慧卷轴',
    description: '记录珍贵知识的古老卷轴',
    icon: '📜',
    value: 120,
    rarity: 'epic',
    stackable: true,
    metadata: {
      category: 'reading',
      effects: ['knowledge_preservation', 'wisdom_sharing']
    }
  },
  learning_boost: {
    id: 'learning_boost',
    type: RewardType.BOOST,
    name: '学习加速',
    description: '显著提升学习能力和记忆力',
    icon: '📚',
    value: 35,
    rarity: 'uncommon',
    stackable: false,
    metadata: {
      duration: 4 * 60 * 60 * 1000, // 4小时
      multiplier: 1.6,
      category: 'reading'
    }
  },
  
  // 社交类
  friendship_essence: {
    id: 'friendship_essence',
    type: RewardType.SPECIAL_ITEM,
    name: '友谊精华',
    description: '真挚友谊的珍贵结晶',
    icon: '💖',
    value: 50,
    rarity: 'uncommon',
    stackable: true,
    metadata: {
      category: 'social',
      effects: ['charisma_boost', 'empathy_enhancement']
    }
  },
  harmony_crystal: {
    id: 'harmony_crystal',
    type: RewardType.SPECIAL_ITEM,
    name: '和谐水晶',
    description: '人际和谐的能量水晶',
    icon: '🔰',
    value: 80,
    rarity: 'rare',
    stackable: true,
    metadata: {
      category: 'social',
      effects: ['social_harmony', 'conflict_resolution']
    }
  },
  social_boost: {
    id: 'social_boost',
    type: RewardType.BOOST,
    name: '社交魅力',
    description: '临时提升社交能力和亲和力',
    icon: '🌈',
    value: 30,
    rarity: 'uncommon',
    stackable: false,
    metadata: {
      duration: 2 * 60 * 60 * 1000, // 2小时
      multiplier: 1.4,
      category: 'social'
    }
  }
}

// 作物奖励系统类
export class CropRewardSystem {
  private dynamicConfig: DynamicRewardConfig
  private userInventory: Map<string, number> = new Map()
  private streakCounters: Map<string, number> = new Map()
  
  constructor(config?: Partial<DynamicRewardConfig>) {
    this.dynamicConfig = {
      baseMultiplier: 1.0,
      qualityMultipliers: {
        [CropQuality.COMMON]: 1.0,
        [CropQuality.UNCOMMON]: 1.2,
        [CropQuality.RARE]: 1.5,
        [CropQuality.EPIC]: 2.0,
        [CropQuality.LEGENDARY]: 3.0
      },
      behaviorBonuses: {
        [SelfDisciplineType.LEARNING]: 1.1,
        [SelfDisciplineType.EXERCISE]: 1.2,
        [SelfDisciplineType.TIME_MANAGEMENT]: 1.15,
        [SelfDisciplineType.MEDITATION]: 1.25,
        [SelfDisciplineType.DEEP_FOCUS]: 1.3,
        [SelfDisciplineType.READING]: 1.1,
        [SelfDisciplineType.SOCIAL_INTERACTION]: 1.05
      },
      streakThresholds: [
        { threshold: 3, bonus: 0.1 },
        { threshold: 7, bonus: 0.2 },
        { threshold: 14, bonus: 0.3 },
        { threshold: 30, bonus: 0.5 }
      ],
      timeOfDayBonuses: {
        'morning': 1.1,
        'afternoon': 1.0,
        'evening': 1.05,
        'night': 0.9
      },
      seasonalBonuses: {
        'spring': 1.1,
        'summer': 1.0,
        'autumn': 1.05,
        'winter': 0.95
      },
      weatherBonuses: {
        'sunny': 1.1,
        'cloudy': 1.0,
        'rainy': 0.9,
        'stormy': 0.8
      },
      ...config
    }
  }
  
  /**
   * 计算作物收获奖励
   */
  calculateHarvestRewards(
    crop: CropInstance,
    metadata: {
      averageFocusScore: number
      sessionsContributed: number
      behaviorConsistency: number
      totalGrowthTime: number
      streakCount?: number
    }
  ): HarvestResult {
    const cropConfig = CROP_CONFIGS[crop.type]
    const detectionConfig = getCropDetectionConfig(crop.type)
    
    // 计算基础奖励
    const baseRewards = this.calculateBaseRewards(crop, cropConfig)
    
    // 计算质量倍数
    const qualityMultiplier = this.dynamicConfig.qualityMultipliers[crop.quality]
    
    // 计算行为奖励倍数
    const behaviorMultiplier = this.dynamicConfig.behaviorBonuses[detectionConfig.behaviorType]
    
    // 计算连击奖励
    const streakMultiplier = this.calculateStreakBonus(metadata.streakCount || 0)
    
    // 计算时间和环境奖励
    const timeMultiplier = this.getTimeOfDayMultiplier()
    const seasonMultiplier = this.getSeasonalMultiplier()
    
    // 综合倍数
    const totalMultiplier = 
      this.dynamicConfig.baseMultiplier *
      qualityMultiplier *
      behaviorMultiplier *
      streakMultiplier *
      timeMultiplier *
      seasonMultiplier
    
    // 应用倍数到基础奖励
    const enhancedBaseRewards = this.applyMultiplierToBundle(baseRewards, totalMultiplier)
    
    // 生成奖励奖励
    const bonusRewards = this.generateBonusRewards(crop, metadata, totalMultiplier)
    
    // 生成特殊物品
    const specialItems = this.generateSpecialItems(crop, metadata.behaviorConsistency)
    
    // 检查成就
    const achievements = this.checkAchievements(crop, metadata)
    
    // 计算总经验和成长点
    const totalExperience = this.calculateTotalValue(enhancedBaseRewards, RewardType.EXPERIENCE) +
                           bonusRewards.reduce((sum, bundle) => sum + this.calculateTotalValue(bundle, RewardType.EXPERIENCE), 0)
    
    const totalGrowthPoints = this.calculateTotalValue(enhancedBaseRewards, RewardType.GROWTH_POINTS) +
                             bonusRewards.reduce((sum, bundle) => sum + this.calculateTotalValue(bundle, RewardType.GROWTH_POINTS), 0)
    
    return {
      cropId: crop.id,
      cropType: crop.type,
      quality: crop.quality,
      baseRewards: enhancedBaseRewards,
      bonusRewards,
      totalExperience,
      totalGrowthPoints,
      specialItems,
      achievements,
      metadata: {
        harvestTime: Date.now(),
        totalGrowthTime: metadata.totalGrowthTime,
        averageFocusScore: metadata.averageFocusScore,
        sessionsContributed: metadata.sessionsContributed,
        behaviorConsistency: metadata.behaviorConsistency,
        streakBonus: streakMultiplier - 1
      }
    }
  }
  
  /**
   * 计算基础奖励
   */
  private calculateBaseRewards(crop: CropInstance, config: any): RewardBundle {
    const items = [
      {
        item: REWARD_ITEMS.basic_exp,
        quantity: config.rewards.baseExp,
        probability: 1.0
      },
      {
        item: REWARD_ITEMS.growth_points,
        quantity: config.rewards.growthPoints,
        probability: 1.0
      }
    ]
    
    // 添加作物特定的特殊物品
    if (config.rewards.specialItems) {
      config.rewards.specialItems.forEach((itemId: string) => {
        if (REWARD_ITEMS[itemId]) {
          items.push({
            item: REWARD_ITEMS[itemId],
            quantity: 1,
            probability: 0.8 // 80%概率获得特殊物品
          })
        }
      })
    }
    
    return {
      id: `base_rewards_${crop.id}`,
      name: '基础收获奖励',
      description: '作物收获的基础奖励',
      items,
      totalValue: config.rewards.baseExp + config.rewards.growthPoints,
      bonusMultiplier: 1.0
    }
  }
  
  /**
   * 生成奖励奖励
   */
  private generateBonusRewards(
    crop: CropInstance, 
    metadata: any, 
    totalMultiplier: number
  ): RewardBundle[] {
    const bonusRewards: RewardBundle[] = []
    
    // 高质量奖励
    if (crop.quality === CropQuality.EPIC || crop.quality === CropQuality.LEGENDARY) {
      bonusRewards.push({
        id: `quality_bonus_${crop.id}`,
        name: '品质奖励',
        description: '高品质作物的额外奖励',
        items: [{
          item: REWARD_ITEMS.bonus_exp,
          quantity: crop.quality === CropQuality.LEGENDARY ? 100 : 50,
          probability: 1.0
        }],
        totalValue: crop.quality === CropQuality.LEGENDARY ? 100 : 50,
        bonusMultiplier: totalMultiplier
      })
    }
    
    // 专注度奖励
    if (metadata.averageFocusScore >= 90) {
      bonusRewards.push({
        id: `focus_bonus_${crop.id}`,
        name: '专注奖励',
        description: '高专注度的额外奖励',
        items: [{
          item: REWARD_ITEMS.focus_crystal,
          quantity: 1,
          probability: 0.3
        }],
        totalValue: 100,
        bonusMultiplier: 1.0
      })
    }
    
    // 连击奖励
    if (metadata.streakCount && metadata.streakCount >= 7) {
      const streakRewardItems = this.getStreakRewardItems(crop.type, metadata.streakCount)
      bonusRewards.push({
        id: `streak_bonus_${crop.id}`,
        name: '连击奖励',
        description: `${metadata.streakCount}连击的特殊奖励`,
        items: streakRewardItems,
        totalValue: streakRewardItems.reduce((sum, item) => sum + item.item.value * item.quantity, 0),
        bonusMultiplier: 1.0
      })
    }
    
    return bonusRewards
  }
  
  /**
   * 生成特殊物品
   */
  private generateSpecialItems(crop: CropInstance, behaviorConsistency: number): RewardItem[] {
    const specialItems: RewardItem[] = []
    
    // 根据行为一致性生成特殊物品
    if (behaviorConsistency >= 0.8) {
      const cropSpecificItems = this.getCropSpecificItems(crop.type)
      const randomItem = cropSpecificItems[Math.floor(Math.random() * cropSpecificItems.length)]
      if (randomItem && Math.random() < 0.4) { // 40%概率
        specialItems.push(randomItem)
      }
    }
    
    return specialItems
  }
  
  /**
   * 获取作物特定物品
   */
  private getCropSpecificItems(cropType: CropType): RewardItem[] {
    switch (cropType) {
      case CropType.KNOWLEDGE_FLOWER:
        return [REWARD_ITEMS.wisdom_essence, REWARD_ITEMS.focus_crystal]
      case CropType.STRENGTH_TREE:
        return [REWARD_ITEMS.power_essence, REWARD_ITEMS.endurance_boost]
      case CropType.TIME_VEGGIE:
        return [REWARD_ITEMS.efficiency_essence, REWARD_ITEMS.time_boost]
      case CropType.MEDITATION_LOTUS:
        return [REWARD_ITEMS.serenity_essence, REWARD_ITEMS.enlightenment_crystal]
      case CropType.FOCUS_FLOWER:
        return [REWARD_ITEMS.concentration_essence, REWARD_ITEMS.clarity_boost]
      case CropType.READING_VINE:
        return [REWARD_ITEMS.knowledge_vine, REWARD_ITEMS.wisdom_scroll, REWARD_ITEMS.learning_boost]
      case CropType.SOCIAL_FRUIT:
        return [REWARD_ITEMS.friendship_essence, REWARD_ITEMS.harmony_crystal, REWARD_ITEMS.social_boost]
      default:
        return []
    }
  }
  
  /**
   * 获取连击奖励物品
   */
  private getStreakRewardItems(cropType: CropType, streakCount: number): any[] {
    const baseItems = this.getCropSpecificItems(cropType)
    const quantity = Math.min(Math.floor(streakCount / 7), 3) // 每7连击获得1个，最多3个
    
    return baseItems.slice(0, 1).map(item => ({
      item,
      quantity,
      probability: 1.0
    }))
  }
  
  /**
   * 计算连击奖励倍数
   */
  private calculateStreakBonus(streakCount: number): number {
    let bonus = 1.0
    
    for (const threshold of this.dynamicConfig.streakThresholds) {
      if (streakCount >= threshold.threshold) {
        bonus = 1.0 + threshold.bonus
      }
    }
    
    return bonus
  }
  
  /**
   * 获取时间段倍数
   */
  private getTimeOfDayMultiplier(): number {
    const hour = new Date().getHours()
    
    if (hour >= 6 && hour < 12) return this.dynamicConfig.timeOfDayBonuses.morning
    if (hour >= 12 && hour < 18) return this.dynamicConfig.timeOfDayBonuses.afternoon
    if (hour >= 18 && hour < 22) return this.dynamicConfig.timeOfDayBonuses.evening
    return this.dynamicConfig.timeOfDayBonuses.night
  }
  
  /**
   * 获取季节倍数
   */
  private getSeasonalMultiplier(): number {
    const month = new Date().getMonth()
    
    if (month >= 2 && month <= 4) return this.dynamicConfig.seasonalBonuses.spring
    if (month >= 5 && month <= 7) return this.dynamicConfig.seasonalBonuses.summer
    if (month >= 8 && month <= 10) return this.dynamicConfig.seasonalBonuses.autumn
    return this.dynamicConfig.seasonalBonuses.winter
  }
  
  /**
   * 应用倍数到奖励包
   */
  private applyMultiplierToBundle(bundle: RewardBundle, multiplier: number): RewardBundle {
    return {
      ...bundle,
      items: bundle.items.map(item => ({
        ...item,
        quantity: Math.round(item.quantity * multiplier)
      })),
      totalValue: Math.round(bundle.totalValue * multiplier),
      bonusMultiplier: multiplier
    }
  }
  
  /**
   * 计算特定类型奖励的总价值
   */
  private calculateTotalValue(bundle: RewardBundle, rewardType: RewardType): number {
    return bundle.items
      .filter(item => item.item.type === rewardType)
      .reduce((sum, item) => sum + item.item.value * item.quantity, 0)
  }
  
  /**
   * 检查成就
   */
  private checkAchievements(crop: CropInstance, metadata: any): string[] {
    const achievements: string[] = []
    
    // 基础成就检查逻辑（可扩展）
    if (crop.quality === CropQuality.LEGENDARY) {
      achievements.push(`legendary_${crop.type}_harvest`)
    }
    
    if (metadata.streakCount >= 30) {
      achievements.push(`master_${crop.type}_streak`)
    }
    
    if (metadata.averageFocusScore >= 95) {
      achievements.push(`perfect_focus_${crop.type}`)
    }
    
    return achievements
  }
  
  /**
   * 更新用户库存
   */
  updateInventory(rewards: HarvestResult): void {
    // 更新基础奖励
    this.addItemsToInventory(rewards.baseRewards)
    
    // 更新奖励奖励
    rewards.bonusRewards.forEach(bundle => {
      this.addItemsToInventory(bundle)
    })
    
    // 更新特殊物品
    rewards.specialItems.forEach(item => {
      const currentCount = this.userInventory.get(item.id) || 0
      this.userInventory.set(item.id, currentCount + 1)
    })
    
    console.log(`📦 库存更新: 作物${rewards.cropId}, 总经验+${rewards.totalExperience}, 成长点+${rewards.totalGrowthPoints}`)
  }
  
  /**
   * 添加物品到库存
   */
  private addItemsToInventory(bundle: RewardBundle): void {
    bundle.items.forEach(bundleItem => {
      if (Math.random() <= bundleItem.probability) {
        const currentCount = this.userInventory.get(bundleItem.item.id) || 0
        this.userInventory.set(bundleItem.item.id, currentCount + bundleItem.quantity)
      }
    })
  }
  
  /**
   * 获取库存
   */
  getInventory(): Map<string, number> {
    return new Map(this.userInventory)
  }
  
  /**
   * 使用物品
   */
  useItem(itemId: string, quantity: number = 1): boolean {
    const currentCount = this.userInventory.get(itemId) || 0
    
    if (currentCount >= quantity) {
      this.userInventory.set(itemId, currentCount - quantity)
      console.log(`✨ 使用物品: ${itemId} x${quantity}`)
      return true
    }
    
    return false
  }
}

// 导出单例实例
export const cropRewardSystem = new CropRewardSystem() 