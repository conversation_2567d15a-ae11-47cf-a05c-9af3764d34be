// 农场升级系统 - 定义农场等级提升条件和机制
// 提供渐进式解锁体验，与现有成就系统、作物系统集成

import { CropType } from '../types/crop'
import { AchievementService } from '../services/AchievementService'
import { GameProgressService } from '../services/GameProgressService'

// 农场等级枚举
export enum FarmLevel {
  NOVICE = 1,        // 新手农场
  APPRENTICE = 2,    // 学徒农场  
  EXPERIENCED = 3,   // 熟练农场
  EXPERT = 4,        // 专家农场
  MASTER = 5,        // 大师农场
  LEGENDARY = 6,     // 传奇农场
  MYTHICAL = 7,      // 神话农场
  TRANSCENDENT = 8,  // 超凡农场
  DIVINE = 9,        // 神圣农场
  ULTIMATE = 10      // 终极农场
}

// 升级条件类型
export interface UpgradeCondition {
  type: 'experience' | 'crops_harvested' | 'focus_time' | 'achievement' | 'crop_variety' | 'streak' | 'custom'
  value: number
  description: string
  isOptional?: boolean  // 可选条件，满足可获得额外奖励
  metadata?: any        // 额外数据
}

// 农场等级配置
export interface FarmLevelConfig {
  level: FarmLevel
  name: string
  description: string
  icon: string
  requiredConditions: UpgradeCondition[]  // 必须满足的条件
  optionalConditions: UpgradeCondition[]  // 可选条件
  rewards: FarmUpgradeReward[]            // 升级奖励
  unlockedFeatures: string[]              // 解锁的功能
  unlockedCrops: CropType[]               // 解锁的作物
  plotExpansion?: {                       // 农田扩展
    newPlots: number
    plotSize?: string
  }
  nextLevelPreview?: string               // 下一等级预览
}

// 升级奖励接口
export interface FarmUpgradeReward {
  type: 'experience' | 'currency' | 'seeds' | 'tools' | 'decoration' | 'boost' | 'special'
  item: string
  quantity: number
  description: string
  icon?: string
  rarity?: 'common' | 'rare' | 'epic' | 'legendary'
}

// 升级检查结果
export interface UpgradeCheckResult {
  canUpgrade: boolean
  currentLevel: FarmLevel
  nextLevel?: FarmLevel
  metConditions: UpgradeCondition[]
  unmetConditions: UpgradeCondition[]
  optionalMet: UpgradeCondition[]
  optionalUnmet: UpgradeCondition[]
  progressPercentage: number
  estimatedTimeToUpgrade?: string
}

// 升级历史记录
export interface UpgradeHistory {
  level: FarmLevel
  achievedAt: Date
  timeTaken: number  // 从上一等级到这一等级的时间（小时）
  conditionsMet: UpgradeCondition[]
  rewards: FarmUpgradeReward[]
}

// 农场等级配置数据
const FARM_LEVEL_CONFIGS: FarmLevelConfig[] = [
  {
    level: FarmLevel.NOVICE,
    name: '新手农场',
    description: '刚刚起步的专注农场，充满无限可能',
    icon: '🌱',
    requiredConditions: [],
    optionalConditions: [],
    rewards: [],
    unlockedFeatures: ['基础种植', '简单收获'],
    unlockedCrops: [CropType.STRENGTH_TREE, CropType.TIME_VEGETABLE],
    nextLevelPreview: '完成更多专注训练，解锁新作物'
  },
  {
    level: FarmLevel.APPRENTICE,
    name: '学徒农场',
    description: '初步掌握专注技巧的农场',
    icon: '🌿',
    requiredConditions: [
      {
        type: 'experience',
        value: 200,
        description: '获得200点经验值'
      },
      {
        type: 'crops_harvested',
        value: 5,
        description: '收获5株作物'
      },
      {
        type: 'focus_time',
        value: 120,
        description: '累计专注时间2小时'
      }
    ],
    optionalConditions: [
      {
        type: 'streak',
        value: 3,
        description: '连续3天完成专注任务',
        isOptional: true
      }
    ],
    rewards: [
      {
        type: 'seeds',
        item: 'meditation_lotus',
        quantity: 2,
        description: '冥想莲花种子',
        icon: '🪷'
      },
      {
        type: 'currency',
        item: 'focus_coins',
        quantity: 50,
        description: '专注金币'
      }
    ],
    unlockedFeatures: ['作物品质系统', '基础成就'],
    unlockedCrops: [CropType.MEDITATION_LOTUS],
    plotExpansion: {
      newPlots: 2
    },
    nextLevelPreview: '解锁专注花，提升专注深度'
  },
  {
    level: FarmLevel.EXPERIENCED,
    name: '熟练农场',
    description: '展现稳定专注能力的农场',
    icon: '🌸',
    requiredConditions: [
      {
        type: 'experience',
        value: 500,
        description: '获得500点经验值'
      },
      {
        type: 'crops_harvested',
        value: 15,
        description: '收获15株作物'
      },
      {
        type: 'crop_variety',
        value: 3,
        description: '种植3种不同作物',
        metadata: { types: ['strength_tree', 'time_vegetable', 'meditation_lotus'] }
      },
      {
        type: 'achievement',
        value: 3,
        description: '完成3个成就'
      }
    ],
    optionalConditions: [
      {
        type: 'focus_time',
        value: 600,
        description: '累计专注时间10小时',
        isOptional: true
      },
      {
        type: 'custom',
        value: 95,
        description: '单次专注会话平均分数达到95分',
        isOptional: true,
        metadata: { type: 'perfect_session' }
      }
    ],
    rewards: [
      {
        type: 'seeds',
        item: 'focus_flower',
        quantity: 3,
        description: '专注花种子',
        icon: '🌺',
        rarity: 'rare'
      },
      {
        type: 'tools',
        item: 'focus_enhancer',
        quantity: 1,
        description: '专注增强器',
        icon: '🔧'
      },
      {
        type: 'currency',
        item: 'focus_coins',
        quantity: 100,
        description: '专注金币'
      }
    ],
    unlockedFeatures: ['天气系统', '高级成就', '作物特效'],
    unlockedCrops: [CropType.FOCUS_FLOWER],
    plotExpansion: {
      newPlots: 3
    },
    nextLevelPreview: '解锁读书藤，拓展知识领域'
  },
  {
    level: FarmLevel.EXPERT,
    name: '专家农场',
    description: '拥有多样化技能的专业农场',
    icon: '🌺',
    requiredConditions: [
      {
        type: 'experience',
        value: 1000,
        description: '获得1000点经验值'
      },
      {
        type: 'crops_harvested',
        value: 30,
        description: '收获30株作物'
      },
      {
        type: 'crop_variety',
        value: 4,
        description: '种植4种不同作物'
      },
      {
        type: 'achievement',
        value: 8,
        description: '完成8个成就'
      },
      {
        type: 'focus_time',
        value: 1200,
        description: '累计专注时间20小时'
      }
    ],
    optionalConditions: [
      {
        type: 'streak',
        value: 7,
        description: '连续7天完成专注任务',
        isOptional: true
      },
      {
        type: 'custom',
        value: 3,
        description: '完成3次完美专注会话',
        isOptional: true,
        metadata: { type: 'perfect_sessions_count' }
      }
    ],
    rewards: [
      {
        type: 'seeds',
        item: 'reading_vine',
        quantity: 2,
        description: '读书藤种子',
        icon: '📚',
        rarity: 'rare'
      },
      {
        type: 'decoration',
        item: 'focus_statue',
        quantity: 1,
        description: '专注雕像',
        icon: '🗿',
        rarity: 'epic'
      },
      {
        type: 'boost',
        item: 'exp_multiplier',
        quantity: 1,
        description: '经验值倍数增强 (1.2x)',
        icon: '⚡'
      }
    ],
    unlockedFeatures: ['农场装饰', '经验倍数', '深度分析'],
    unlockedCrops: [CropType.READING_VINE],
    plotExpansion: {
      newPlots: 3,
      plotSize: 'medium'
    },
    nextLevelPreview: '解锁社交果，发展人际技能'
  },
  {
    level: FarmLevel.MASTER,
    name: '大师农场',
    description: '掌握全面技能的高级农场',
    icon: '🏆',
    requiredConditions: [
      {
        type: 'experience',
        value: 1800,
        description: '获得1800点经验值'
      },
      {
        type: 'crops_harvested',
        value: 50,
        description: '收获50株作物'
      },
      {
        type: 'crop_variety',
        value: 5,
        description: '种植5种不同作物'
      },
      {
        type: 'achievement',
        value: 15,
        description: '完成15个成就'
      },
      {
        type: 'focus_time',
        value: 2400,
        description: '累计专注时间40小时'
      },
      {
        type: 'streak',
        value: 10,
        description: '连续10天完成专注任务'
      }
    ],
    optionalConditions: [
      {
        type: 'custom',
        value: 5,
        description: '完成5次马拉松会话(2小时+)',
        isOptional: true,
        metadata: { type: 'marathon_sessions' }
      },
      {
        type: 'custom',
        value: 90,
        description: '平均专注分数超过90分',
        isOptional: true,
        metadata: { type: 'average_score' }
      }
    ],
    rewards: [
      {
        type: 'seeds',
        item: 'social_fruit',
        quantity: 3,
        description: '社交果种子',
        icon: '🍎',
        rarity: 'epic'
      },
      {
        type: 'tools',
        item: 'master_toolkit',
        quantity: 1,
        description: '大师工具包',
        icon: '🛠️',
        rarity: 'epic'
      },
      {
        type: 'special',
        item: 'farm_expansion',
        quantity: 1,
        description: '农场扩展许可证',
        icon: '📜',
        rarity: 'legendary'
      }
    ],
    unlockedFeatures: ['大师模式', '高级统计', '自定义目标'],
    unlockedCrops: [CropType.SOCIAL_FRUIT],
    plotExpansion: {
      newPlots: 5,
      plotSize: 'large'
    },
    nextLevelPreview: '进入传奇等级，探索极限潜能'
  },
  {
    level: FarmLevel.LEGENDARY,
    name: '传奇农场',
    description: '达到卓越水准的传奇农场',
    icon: '⭐',
    requiredConditions: [
      {
        type: 'experience',
        value: 3000,
        description: '获得3000点经验值'
      },
      {
        type: 'crops_harvested',
        value: 100,
        description: '收获100株作物'
      },
      {
        type: 'crop_variety',
        value: 6,
        description: '种植全部6种作物类型'
      },
      {
        type: 'achievement',
        value: 25,
        description: '完成25个成就'
      },
      {
        type: 'focus_time',
        value: 4800,
        description: '累计专注时间80小时'
      },
      {
        type: 'streak',
        value: 21,
        description: '连续21天完成专注任务'
      },
      {
        type: 'custom',
        value: 10,
        description: '完成10次完美会话',
        metadata: { type: 'perfect_sessions_count' }
      }
    ],
    optionalConditions: [
      {
        type: 'custom',
        value: 3,
        description: '创造3次个人专注记录',
        isOptional: true,
        metadata: { type: 'personal_records' }
      }
    ],
    rewards: [
      {
        type: 'special',
        item: 'legendary_crown',
        quantity: 1,
        description: '传奇王冠',
        icon: '👑',
        rarity: 'legendary'
      },
      {
        type: 'boost',
        item: 'legendary_aura',
        quantity: 1,
        description: '传奇光环 (全属性加成)',
        icon: '✨',
        rarity: 'legendary'
      },
      {
        type: 'currency',
        item: 'legendary_coins',
        quantity: 500,
        description: '传奇金币'
      }
    ],
    unlockedFeatures: ['传奇模式', '无限潜能', '历史记录'],
    unlockedCrops: [], // 已解锁全部作物
    plotExpansion: {
      newPlots: 10,
      plotSize: 'legendary'
    },
    nextLevelPreview: '踏入神话领域，超越常人极限'
  }
  // 更高等级可以继续扩展...
]

export class FarmUpgradeSystem {
  private currentLevel: FarmLevel = FarmLevel.NOVICE
  private achievementService: AchievementService
  private gameProgressService: GameProgressService
  private upgradeHistory: UpgradeHistory[] = []
  private levelConfigs: Map<FarmLevel, FarmLevelConfig> = new Map()

  constructor(
    achievementService: AchievementService,
    gameProgressService: GameProgressService
  ) {
    this.achievementService = achievementService
    this.gameProgressService = gameProgressService
    
    // 初始化等级配置
    FARM_LEVEL_CONFIGS.forEach(config => {
      this.levelConfigs.set(config.level, config)
    })
  }

  /**
   * 检查是否可以升级
   */
  async checkUpgradeEligibility(): Promise<UpgradeCheckResult> {
    const nextLevel = this.currentLevel + 1 as FarmLevel
    const nextLevelConfig = this.levelConfigs.get(nextLevel)
    
    if (!nextLevelConfig) {
      return {
        canUpgrade: false,
        currentLevel: this.currentLevel,
        metConditions: [],
        unmetConditions: [],
        optionalMet: [],
        optionalUnmet: [],
        progressPercentage: 100
      }
    }

    // 获取当前游戏数据
    const userData = await this.getUserData()
    
    // 检查必需条件
    const metConditions: UpgradeCondition[] = []
    const unmetConditions: UpgradeCondition[] = []
    
    for (const condition of nextLevelConfig.requiredConditions) {
      if (await this.checkCondition(condition, userData)) {
        metConditions.push(condition)
      } else {
        unmetConditions.push(condition)
      }
    }

    // 检查可选条件
    const optionalMet: UpgradeCondition[] = []
    const optionalUnmet: UpgradeCondition[] = []
    
    for (const condition of nextLevelConfig.optionalConditions) {
      if (await this.checkCondition(condition, userData)) {
        optionalMet.push(condition)
      } else {
        optionalUnmet.push(condition)
      }
    }

    const canUpgrade = unmetConditions.length === 0
    const totalRequired = nextLevelConfig.requiredConditions.length
    const metRequired = metConditions.length
    const progressPercentage = totalRequired > 0 ? (metRequired / totalRequired) * 100 : 100

    // 估算升级时间
    const estimatedTime = this.estimateUpgradeTime(unmetConditions, userData)

    return {
      canUpgrade,
      currentLevel: this.currentLevel,
      nextLevel,
      metConditions,
      unmetConditions,
      optionalMet,
      optionalUnmet,
      progressPercentage,
      estimatedTimeToUpgrade: estimatedTime
    }
  }

  /**
   * 执行升级
   */
  async performUpgrade(): Promise<{
    success: boolean
    newLevel: FarmLevel
    rewards: FarmUpgradeReward[]
    unlockedFeatures: string[]
    unlockedCrops: CropType[]
    upgradeHistory: UpgradeHistory
  }> {
    const upgradeCheck = await this.checkUpgradeEligibility()
    
    if (!upgradeCheck.canUpgrade || !upgradeCheck.nextLevel) {
      throw new Error('Cannot upgrade: conditions not met')
    }

    const oldLevel = this.currentLevel
    const newLevel = upgradeCheck.nextLevel
    const levelConfig = this.levelConfigs.get(newLevel)!
    
    // 记录升级历史
    const upgradeRecord: UpgradeHistory = {
      level: newLevel,
      achievedAt: new Date(),
      timeTaken: this.calculateTimeSinceLastUpgrade(),
      conditionsMet: [...upgradeCheck.metConditions, ...upgradeCheck.optionalMet],
      rewards: levelConfig.rewards
    }
    
    this.upgradeHistory.push(upgradeRecord)
    this.currentLevel = newLevel
    
    // 应用奖励
    await this.applyUpgradeRewards(levelConfig.rewards)
    
    // 解锁新功能和作物
    await this.unlockFeatures(levelConfig.unlockedFeatures)
    await this.unlockCrops(levelConfig.unlockedCrops)
    
    // 扩展农田
    if (levelConfig.plotExpansion) {
      await this.expandFarm(levelConfig.plotExpansion)
    }

    // 保存进度
    await this.saveUpgradeProgress()

    return {
      success: true,
      newLevel,
      rewards: levelConfig.rewards,
      unlockedFeatures: levelConfig.unlockedFeatures,
      unlockedCrops: levelConfig.unlockedCrops,
      upgradeHistory: upgradeRecord
    }
  }

  /**
   * 获取当前农场等级信息
   */
  getCurrentLevelInfo(): FarmLevelConfig | undefined {
    return this.levelConfigs.get(this.currentLevel)
  }

  /**
   * 获取下一等级信息
   */
  getNextLevelInfo(): FarmLevelConfig | undefined {
    const nextLevel = this.currentLevel + 1 as FarmLevel
    return this.levelConfigs.get(nextLevel)
  }

  /**
   * 获取所有等级配置
   */
  getAllLevels(): FarmLevelConfig[] {
    return Array.from(this.levelConfigs.values()).sort((a, b) => a.level - b.level)
  }

  /**
   * 获取升级历史
   */
  getUpgradeHistory(): UpgradeHistory[] {
    return [...this.upgradeHistory]
  }

  /**
   * 私有方法：检查单个条件
   */
  private async checkCondition(condition: UpgradeCondition, userData: any): Promise<boolean> {
    switch (condition.type) {
      case 'experience':
        return userData.totalExperience >= condition.value

      case 'crops_harvested':
        return userData.cropsHarvested >= condition.value

      case 'focus_time':
        return userData.totalFocusTime >= condition.value * 60 // 转换为秒

      case 'achievement':
        return userData.achievementsCompleted >= condition.value

      case 'crop_variety':
        return userData.cropVarieties >= condition.value

      case 'streak':
        return userData.currentStreak >= condition.value

      case 'custom':
        return this.checkCustomCondition(condition, userData)

      default:
        return false
    }
  }

  /**
   * 私有方法：检查自定义条件
   */
  private checkCustomCondition(condition: UpgradeCondition, userData: any): boolean {
    if (!condition.metadata) return false

    switch (condition.metadata.type) {
      case 'perfect_session':
        return userData.bestSessionScore >= condition.value

      case 'perfect_sessions_count':
        return userData.perfectSessionsCount >= condition.value

      case 'marathon_sessions':
        return userData.marathonSessionsCount >= condition.value

      case 'average_score':
        return userData.averageScore >= condition.value

      case 'personal_records':
        return userData.personalRecordsCount >= condition.value

      default:
        return false
    }
  }

  /**
   * 私有方法：获取用户数据
   */
  private async getUserData(): Promise<any> {
    // 从成就服务获取数据
    const achievementData = this.achievementService.getUserAchievementData()
    
    // 这里需要根据实际数据结构调整
    return {
      totalExperience: achievementData.experience?.totalExperience || 0,
      cropsHarvested: 0, // 需要从游戏状态获取
      totalFocusTime: 0, // 需要从游戏状态获取（秒）
      achievementsCompleted: 0, // 需要从成就数据获取
      cropVarieties: 0, // 需要从游戏状态获取
      currentStreak: 0, // 需要从成就数据获取
      bestSessionScore: 0, // 需要从会话数据获取
      perfectSessionsCount: 0, // 需要从会话数据获取
      marathonSessionsCount: 0, // 需要从会话数据获取
      averageScore: 0, // 需要从会话数据获取
      personalRecordsCount: 0 // 需要从游戏数据获取
    }
  }

  /**
   * 私有方法：估算升级时间
   */
  private estimateUpgradeTime(unmetConditions: UpgradeCondition[], userData: any): string {
    if (unmetConditions.length === 0) return '立即可升级'

    // 基于当前进度估算时间
    // 这里是简化的估算逻辑
    const estimatedDays = Math.ceil(unmetConditions.length * 2)
    
    if (estimatedDays <= 1) return '约1天'
    if (estimatedDays <= 7) return `约${estimatedDays}天`
    if (estimatedDays <= 30) return `约${Math.ceil(estimatedDays / 7)}周`
    return `约${Math.ceil(estimatedDays / 30)}个月`
  }

  /**
   * 私有方法：计算距离上次升级的时间
   */
  private calculateTimeSinceLastUpgrade(): number {
    if (this.upgradeHistory.length === 0) return 0
    
    const lastUpgrade = this.upgradeHistory[this.upgradeHistory.length - 1]
    const timeDiff = Date.now() - lastUpgrade.achievedAt.getTime()
    return Math.floor(timeDiff / (1000 * 60 * 60)) // 转换为小时
  }

  /**
   * 私有方法：应用升级奖励
   */
  private async applyUpgradeRewards(rewards: FarmUpgradeReward[]): Promise<void> {
    // 实现奖励应用逻辑
    // 这里需要与游戏状态管理器集成
    console.log('Applying upgrade rewards:', rewards)
  }

  /**
   * 私有方法：解锁功能
   */
  private async unlockFeatures(features: string[]): Promise<void> {
    // 实现功能解锁逻辑
    console.log('Unlocking features:', features)
  }

  /**
   * 私有方法：解锁作物
   */
  private async unlockCrops(crops: CropType[]): Promise<void> {
    // 实现作物解锁逻辑
    console.log('Unlocking crops:', crops)
  }

  /**
   * 私有方法：扩展农田
   */
  private async expandFarm(expansion: { newPlots: number; plotSize?: string }): Promise<void> {
    // 实现农田扩展逻辑
    console.log('Expanding farm:', expansion)
  }

  /**
   * 私有方法：保存升级进度
   */
  private async saveUpgradeProgress(): Promise<void> {
    // 实现进度保存逻辑
    // 需要与游戏进度服务集成
    console.log('Saving upgrade progress')
  }

  /**
   * 设置当前农场等级
   */
  setCurrentLevel(level: FarmLevel): void {
    this.currentLevel = level
  }

  /**
   * 获取当前农场等级
   */
  getCurrentLevel(): FarmLevel {
    return this.currentLevel
  }

  /**
   * 添加升级历史记录
   */
  addUpgradeHistory(history: UpgradeHistory): void {
    this.upgradeHistory.push(history)
  }

  /**
   * 设置升级历史
   */
  setUpgradeHistory(history: UpgradeHistory[]): void {
    this.upgradeHistory = [...history]
  }
} 