import { GameItem, Quality, QUALITY_CONFIGS, ItemCategory, AgriculturalVariety, IndustrialVariety, EquipmentType } from '../types/enhanced-items'

// 合成结果类型
export interface SynthesisResult {
  success: boolean
  resultItem?: GameItem
  consumedItems: GameItem[]
  successRate: number
  appliedBonuses: string[]
  message: string
  timestamp: number
}

// 合成配方接口
export interface SynthesisRecipe {
  id: string
  name: string
  description: string
  requiredItems: {
    variety: AgriculturalVariety | IndustrialVariety | EquipmentType
    quality: Quality
    quantity: number
  }[]
  resultQuality: Quality
  baseSuccessRate: number
}

// 专注时间状态
export interface FocusTimeState {
  dailyFocusMinutes: number // 今日专注时间
  focusStreak: number       // 连续专注天数
  lastFocusDate: string     // 最后专注日期
}

// 合成系统类
export class SynthesisSystem {
  private focusTimeState: FocusTimeState = {
    dailyFocusMinutes: 0,
    focusStreak: 0,
    lastFocusDate: ''
  }

  constructor() {
    this.loadFocusTimeState()
  }

  /**
   * 执行道具合成
   * @param materials 合成材料
   * @param recipe 合成配方 (可选，如果不提供会自动查找)
   */
  public synthesize(materials: GameItem[], recipe?: SynthesisRecipe): SynthesisResult {
    try {
      // 如果没有提供配方，尝试自动识别
      if (!recipe) {
        recipe = this.findSynthesisRecipe(materials)
      }

      if (!recipe) {
        return {
          success: false,
          consumedItems: [],
          successRate: 0,
          appliedBonuses: [],
          message: '无法找到匹配的合成配方',
          timestamp: Date.now()
        }
      }

      // 验证材料是否满足配方要求
      const validation = this.validateMaterials(materials, recipe)
      if (!validation.valid) {
        return {
          success: false,
          consumedItems: [],
          successRate: 0,
          appliedBonuses: [],
          message: validation.message,
          timestamp: Date.now()
        }
      }

      // 计算成功率
      const { successRate, bonuses } = this.calculateSuccessRate(recipe)

      // 执行合成
      const success = Math.random() < (successRate / 100)
      const result: SynthesisResult = {
        success,
        consumedItems: materials,
        successRate,
        appliedBonuses: bonuses,
        message: success ? '合成成功！' : '合成失败...',
        timestamp: Date.now()
      }

      if (success) {
        // 创建合成结果道具
        result.resultItem = this.createResultItem(recipe, materials)
      }

      return result

    } catch (error) {
      return {
        success: false,
        consumedItems: [],
        successRate: 0,
        appliedBonuses: [],
        message: `合成过程中发生错误: ${error instanceof Error ? error.message : '未知错误'}`,
        timestamp: Date.now()
      }
    }
  }

  /**
   * 查找合成配方
   */
  private findSynthesisRecipe(materials: GameItem[]): SynthesisRecipe | null {
    // 检查是否为相同道具的品质提升合成
    if (materials.length === 2) {
      const [item1, item2] = materials
      
      // 必须是相同种类的道具
      if (this.isSameVariety(item1, item2) && item1.quality === item2.quality) {
        return this.createUpgradeRecipe(item1)
      }
    }

    return null
  }

  /**
   * 创建品质提升配方
   */
  private createUpgradeRecipe(item: GameItem): SynthesisRecipe | null {
    const nextQuality = this.getNextQuality(item.quality)
    if (!nextQuality) return null

    let variety: AgriculturalVariety | IndustrialVariety | EquipmentType
    
    if (item.category === ItemCategory.AGRICULTURAL) {
      variety = (item as any).variety as AgriculturalVariety
    } else if (item.category === ItemCategory.INDUSTRIAL) {
      variety = (item as any).variety as IndustrialVariety
    } else {
      variety = (item as any).equipmentType as EquipmentType
    }

    return {
      id: `upgrade_${variety}_${item.quality}_to_${nextQuality}`,
      name: `${item.name}品质提升`,
      description: `将两个${QUALITY_CONFIGS[item.quality].name}${item.name}合成为${QUALITY_CONFIGS[nextQuality].name}${item.name}`,
      requiredItems: [{
        variety,
        quality: item.quality,
        quantity: 2
      }],
      resultQuality: nextQuality,
      baseSuccessRate: QUALITY_CONFIGS[item.quality].synthesisSuccessRate
    }
  }

  /**
   * 获取下一品质等级
   */
  private getNextQuality(currentQuality: Quality): Quality | null {
    const qualityOrder = [Quality.COMMON, Quality.GOOD, Quality.RARE, Quality.EPIC, Quality.LEGENDARY]
    const currentIndex = qualityOrder.indexOf(currentQuality)
    
    if (currentIndex === -1 || currentIndex === qualityOrder.length - 1) {
      return null // 已经是最高品质
    }
    
    return qualityOrder[currentIndex + 1]
  }

  /**
   * 验证合成材料
   */
  private validateMaterials(materials: GameItem[], recipe: SynthesisRecipe): { valid: boolean; message: string } {
    if (materials.length !== recipe.requiredItems.reduce((sum, req) => sum + req.quantity, 0)) {
      return { valid: false, message: '材料数量不足' }
    }

    // 验证每个所需材料
    for (const required of recipe.requiredItems) {
      const matchingItems = materials.filter(item => 
        this.getItemVariety(item) === required.variety && 
        item.quality === required.quality
      )

      if (matchingItems.length < required.quantity) {
        return { 
          valid: false, 
          message: `缺少 ${required.quantity} 个 ${QUALITY_CONFIGS[required.quality].name} 品质的材料` 
        }
      }
    }

    return { valid: true, message: '' }
  }

  /**
   * 计算合成成功率
   */
  private calculateSuccessRate(recipe: SynthesisRecipe): { successRate: number; bonuses: string[] } {
    let finalRate = recipe.baseSuccessRate
    const bonuses: string[] = []

    // 专注时间加成
    const focusBonus = this.getFocusTimeBonus()
    if (focusBonus > 0) {
      finalRate += focusBonus
      bonuses.push(`专注时间加成: +${focusBonus}%`)
    }

    // 连续专注加成
    const streakBonus = this.getFocusStreakBonus()
    if (streakBonus > 0) {
      finalRate += streakBonus
      bonuses.push(`连续专注加成: +${streakBonus}%`)
    }

    // 确保成功率在合理范围内
    finalRate = Math.min(finalRate, 95) // 最高95%
    finalRate = Math.max(finalRate, 5)  // 最低5%

    return { successRate: finalRate, bonuses }
  }

  /**
   * 获取专注时间加成
   */
  private getFocusTimeBonus(): number {
    const minutes = this.focusTimeState.dailyFocusMinutes
    
    if (minutes >= 120) {
      return 20 // 满120分钟：+20%
    } else if (minutes >= 90) {
      return 15 // 90-119分钟：+15%
    } else if (minutes >= 60) {
      return 10 // 60-89分钟：+10%
    } else if (minutes >= 30) {
      return 5  // 30-59分钟：+5%
    }
    
    return 0 // 少于30分钟：无加成
  }

  /**
   * 获取连续专注加成
   */
  private getFocusStreakBonus(): number {
    const streak = this.focusTimeState.focusStreak
    
    if (streak >= 30) {
      return 10 // 连续30天：+10%
    } else if (streak >= 14) {
      return 7  // 连续14天：+7%
    } else if (streak >= 7) {
      return 5  // 连续7天：+5%
    } else if (streak >= 3) {
      return 3  // 连续3天：+3%
    }
    
    return 0 // 少于3天：无加成
  }

  /**
   * 创建合成结果道具
   */
  private createResultItem(recipe: SynthesisRecipe, materials: GameItem[]): GameItem {
    const template = materials[0] // 使用第一个材料作为模板
    const variety = this.getItemVariety(template)
    
    // 这里需要调用 createItem 函数，但由于循环依赖，我们先简化实现
    const resultItem: GameItem = {
      ...template,
      id: `synthesized_${variety}_${recipe.resultQuality}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      quality: recipe.resultQuality,
      obtainedAt: Date.now()
    }

    // 根据新品质更新属性
    this.updateItemAttributes(resultItem, recipe.resultQuality)
    
    return resultItem
  }

  /**
   * 更新道具属性根据品质
   */
  private updateItemAttributes(item: GameItem, quality: Quality): void {
    const qualityConfig = QUALITY_CONFIGS[quality]

    if (item.category === ItemCategory.AGRICULTURAL) {
      const agriItem = item as any
      const [min, max] = qualityConfig.productionRange
      agriItem.production.minDaily = min
      agriItem.production.maxDaily = max
      agriItem.production.currentRate = 1.0 + (qualityConfig.attributeBonus / 100)
    } else if (item.category === ItemCategory.INDUSTRIAL) {
      const indItem = item as any
      const bonus = qualityConfig.attributeBonus / 100
      const baseProps = indItem.properties
      indItem.properties = {
        durability: Math.round(baseProps.durability * (1 + bonus)),
        efficiency: Math.round(baseProps.efficiency * (1 + bonus)),
        capacity: Math.round(baseProps.capacity * (1 + bonus))
      }
    } else if (item.category === ItemCategory.EQUIPMENT) {
      const equipItem = item as any
      equipItem.attributes = {
        ...equipItem.attributes,
        focusBonus: qualityConfig.attributeBonus,
        productionBonus: qualityConfig.attributeBonus,
        qualityBonus: qualityConfig.attributeBonus
      }
    }
  }

  /**
   * 检查两个道具是否为同一品种
   */
  private isSameVariety(item1: GameItem, item2: GameItem): boolean {
    if (item1.category !== item2.category) return false
    
    const variety1 = this.getItemVariety(item1)
    const variety2 = this.getItemVariety(item2)
    
    return variety1 === variety2
  }

  /**
   * 获取道具品种
   */
  private getItemVariety(item: GameItem): AgriculturalVariety | IndustrialVariety | EquipmentType {
    if (item.category === ItemCategory.AGRICULTURAL) {
      return (item as any).variety as AgriculturalVariety
    } else if (item.category === ItemCategory.INDUSTRIAL) {
      return (item as any).variety as IndustrialVariety
    } else {
      return (item as any).equipmentType as EquipmentType
    }
  }

  /**
   * 更新专注时间状态
   */
  public updateFocusTime(minutes: number): void {
    const today = new Date().toDateString()
    
    // 如果是新的一天，重置日常专注时间
    if (this.focusTimeState.lastFocusDate !== today) {
      // 检查是否连续专注
      const yesterday = new Date()
      yesterday.setDate(yesterday.getDate() - 1)
      
      if (this.focusTimeState.lastFocusDate === yesterday.toDateString()) {
        this.focusTimeState.focusStreak += 1
      } else {
        this.focusTimeState.focusStreak = 1
      }
      
      this.focusTimeState.dailyFocusMinutes = 0
      this.focusTimeState.lastFocusDate = today
    }
    
    this.focusTimeState.dailyFocusMinutes += minutes
    this.saveFocusTimeState()
  }

  /**
   * 获取当前专注状态
   */
  public getFocusTimeState(): FocusTimeState {
    return { ...this.focusTimeState }
  }

  /**
   * 保存专注时间状态
   */
  private saveFocusTimeState(): void {
    try {
      localStorage.setItem('focusTimeState', JSON.stringify(this.focusTimeState))
    } catch (error) {
      console.warn('无法保存专注时间状态:', error)
    }
  }

  /**
   * 加载专注时间状态
   */
  private loadFocusTimeState(): void {
    try {
      const saved = localStorage.getItem('focusTimeState')
      if (saved) {
        this.focusTimeState = JSON.parse(saved)
      }
    } catch (error) {
      console.warn('无法加载专注时间状态:', error)
    }
  }

  /**
   * 重置专注时间状态 (用于测试)
   */
  public resetFocusTimeState(): void {
    this.focusTimeState = {
      dailyFocusMinutes: 0,
      focusStreak: 0,
      lastFocusDate: ''
    }
    this.saveFocusTimeState()
  }
} 