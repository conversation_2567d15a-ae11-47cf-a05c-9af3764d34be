import { 
  PlantingSlot, 
  PlantingStage, 
  FarmData, 
  HarvestResult, 
  CollectionEntry,
  Achievement,
  PlantingItem
} from '../types/planting'
import { ItemRarity } from '../types/lootbox'
import { 
  getCropGrowthConfig, 
  getPlantingItem, 
  calculateRarityProbability,
  getExperienceRequired,
  ACHIEVEMENTS,
  LEVEL_CONFIG
} from '../data/plantingData'
import { getFuturesProductById } from '../data/chineseFuturesProducts'

// 种植系统管理器 - 农场收集游戏的核心
export class PlantingSystemManager {
  private farmData: FarmData
  private updateCallbacks: Set<() => void> = new Set()
  private growthTimer: NodeJS.Timeout | null = null

  constructor(initialData?: Partial<FarmData>) {
    this.farmData = {
      level: 1,
      experience: 0,
      coins: 1000,
      slots: this.initializeSlots(9), // 3x3网格
      inventory: {
        'basic_seed': 10,
        'organic_fertilizer': 5
      },
      collection: {},
      achievements: [...ACHIEVEMENTS].map(a => ({ ...a })), // 修复：深拷贝成就对象
      dailyStreak: 0,
      lastLoginDate: new Date().toISOString().split('T')[0],
      ...initialData
    }
    
    this.startGrowthTimer()
  }

  // 初始化种植槽位
  private initializeSlots(count: number): PlantingSlot[] {
    return Array.from({ length: count }, (_, index) => ({
      id: `slot_${index}`,
      stage: PlantingStage.EMPTY,
      quality: ItemRarity.GRAY,
      plantedAt: 0,
      growthTime: 0,
      remainingTime: 0,
      appliedItems: [],
      yieldMultiplier: 1,
      qualityBonus: 0,
      specialEffects: []
    }))
  }

  // 开始生长计时器
  private startGrowthTimer() {
    if (this.growthTimer) {
      clearInterval(this.growthTimer)
    }
    
    this.growthTimer = setInterval(() => {
      this.updateGrowthProgress()
    }, 1000) // 每秒更新一次
  }

  // 更新生长进度
  private updateGrowthProgress() {
    let hasChanges = false
    
    this.farmData.slots.forEach(slot => {
      if (slot.stage === PlantingStage.GROWING) {
        slot.remainingTime = Math.max(0, slot.remainingTime - 1000)
        
        if (slot.remainingTime <= 0) {
          slot.stage = PlantingStage.READY_HARVEST
          hasChanges = true
          
          // 触发成熟特效
          this.triggerMatureEffect(slot)
        }
      }
    })
    
    if (hasChanges) {
      this.notifyUpdate()
    }
  }

  // 种植作物
  public async plantCrop(slotId: string, cropId: string, seedItem?: PlantingItem): Promise<boolean> {
    const slot = this.farmData.slots.find(s => s.id === slotId)
    if (!slot || slot.stage !== PlantingStage.EMPTY) {
      return false
    }

    const cropConfig = getCropGrowthConfig(cropId)
    if (!cropConfig) {
      return false
    }

    // 计算生长时间（考虑道具效果）
    let growthTime = cropConfig.baseGrowthTime * 60 * 1000 // 转换为毫秒
    let appliedItems: PlantingItem[] = []
    
    if (seedItem) {
      appliedItems.push(seedItem)
      if (seedItem.effects.speedBoost) {
        growthTime *= (1 - seedItem.effects.speedBoost / 100)
      }
      
      // 消耗种子
      this.consumeItem(seedItem.id, 1)
    }

    // 预先计算品质（基于概率）
    const rarityWeights = calculateRarityProbability(cropConfig.rarityWeights, appliedItems)
    const quality = this.rollRarity(rarityWeights)

    // 更新槽位
    slot.cropId = cropId
    slot.stage = PlantingStage.GROWING
    slot.quality = quality
    slot.plantedAt = Date.now()
    slot.growthTime = growthTime
    slot.remainingTime = growthTime
    slot.appliedItems = appliedItems
    slot.yieldMultiplier = 1
    slot.qualityBonus = 0
    slot.specialEffects = []

    // 触发种植特效
    this.triggerPlantingEffect(slot)
    
    this.notifyUpdate()
    return true
  }

  // 收获作物
  public async harvestCrop(slotId: string): Promise<HarvestResult | null> {
    const slot = this.farmData.slots.find(s => s.id === slotId)
    if (!slot || slot.stage !== PlantingStage.READY_HARVEST || !slot.cropId) {
      return null
    }

    const cropProduct = getFuturesProductById(slot.cropId)
    if (!cropProduct) {
      return null
    }

    // 计算收获数量 (避免使用yield关键字)
    const baseProduction = cropProduct.yieldRanges[slot.quality]
    const actualProduction = Math.floor(
      (baseProduction.min + Math.random() * (baseProduction.max - baseProduction.min)) * 
      slot.yieldMultiplier
    )

    // 计算经验和金币
    const experience = this.calculateExperience(slot.quality, actualProduction)
    const coins = this.calculateCoins(slot.quality, actualProduction)

    // 检查是否为新收集
    const collectionKey = `${slot.cropId}_${slot.quality}`
    const isNewCollection = !this.farmData.collection[collectionKey]
    const isRareGet = slot.quality >= ItemRarity.BLUE

    // 更新收集册
    this.updateCollection(slot.cropId, slot.quality, actualProduction)

    // 添加奖励
    this.farmData.experience += experience
    this.farmData.coins += coins

    // 检查升级
    this.checkLevelUp()

    // 更新成就
    this.updateAchievements(slot.cropId, slot.quality, actualProduction)

    // 创建收获结果
    const result: HarvestResult = {
      cropId: slot.cropId,
      quality: slot.quality,
      quantity: actualProduction,
      experience,
      coins,
      isNewCollection,
      isRareGet,
      specialEffects: slot.specialEffects
    }

    // 重置槽位
    slot.cropId = undefined
    slot.stage = PlantingStage.EMPTY
    slot.quality = ItemRarity.GRAY
    slot.plantedAt = 0
    slot.growthTime = 0
    slot.remainingTime = 0
    slot.appliedItems = []
    slot.yieldMultiplier = 1
    slot.qualityBonus = 0
    slot.specialEffects = []

    // 触发收获特效
    this.triggerHarvestEffect(result)

    this.notifyUpdate()
    return result
  }

  // 计算经验值
  private calculateExperience(quality: ItemRarity, production: number): number {
    const baseExp = 10
    const qualityMultiplier = {
      [ItemRarity.GRAY]: 1,
      [ItemRarity.GREEN]: 1.5,
      [ItemRarity.BLUE]: 2.5,
      [ItemRarity.ORANGE]: 4,
      [ItemRarity.GOLD]: 7,
      [ItemRarity.GOLD_RED]: 15
    }
    
    return Math.floor(baseExp * qualityMultiplier[quality] * (1 + production / 1000))
  }

  // 计算金币
  private calculateCoins(quality: ItemRarity, production: number): number {
    const baseCoins = 5
    const qualityMultiplier = {
      [ItemRarity.GRAY]: 1,
      [ItemRarity.GREEN]: 2,
      [ItemRarity.BLUE]: 4,
      [ItemRarity.ORANGE]: 8,
      [ItemRarity.GOLD]: 15,
      [ItemRarity.GOLD_RED]: 30
    }
    
    return Math.floor(baseCoins * qualityMultiplier[quality] * (1 + production / 500))
  }

  // 更新收集册
  private updateCollection(cropId: string, quality: ItemRarity, production: number) {
    const key = `${cropId}_${quality}`
    const existing = this.farmData.collection[key]
    
    if (existing) {
      existing.totalHarvested += 1
      existing.bestYield = Math.max(existing.bestYield, production)
      existing.isNew = false
    } else {
      this.farmData.collection[key] = {
        cropId,
        quality,
        firstObtained: Date.now(),
        totalHarvested: 1,
        bestYield: production,
        isNew: true,
        isRare: quality >= ItemRarity.BLUE
      }
    }
  }

  // 检查升级
  private checkLevelUp() {
    const currentLevel = this.farmData.level
    const requiredExp = getExperienceRequired(currentLevel + 1)
    
    if (this.farmData.experience >= requiredExp && currentLevel < LEVEL_CONFIG.maxLevel) {
      this.farmData.level += 1
      this.farmData.experience -= requiredExp
      
      // 发放升级奖励
      const rewards = LEVEL_CONFIG.levelRewards[this.farmData.level]
      if (rewards) {
        this.farmData.coins += rewards.coins
        if (rewards.items) {
          rewards.items.forEach(item => {
            this.addItem(item.id, item.quantity)
          })
        }
      }
      
      // 触发升级特效
      this.triggerLevelUpEffect(this.farmData.level)
    }
  }

  // 更新成就
  private updateAchievements(cropId: string, quality: ItemRarity, production: number) {
    this.farmData.achievements.forEach(achievement => {
      if (achievement.isCompleted) return
      
      const req = achievement.requirements
      let progressIncrement = 0
      
      switch (req.type) {
        case 'harvest_count':
          if (!req.cropId || req.cropId === cropId) {
            progressIncrement = 1
          }
          break
          
        case 'rare_harvest':
          if (quality >= (req.quality || ItemRarity.BLUE)) {
            progressIncrement = 1
          }
          break
          
        case 'level_reach':
          achievement.progress = this.farmData.level
          break
          
        case 'collection_rate':
          const totalPossible = 13 * 6 // 13个作物 * 6个品质
          const collected = Object.keys(this.farmData.collection).length
          achievement.progress = Math.floor((collected / totalPossible) * 100)
          break
      }
      
      if (progressIncrement > 0) {
        achievement.progress += progressIncrement
      }
      
      // 检查是否完成
      if (achievement.progress >= req.target && !achievement.isCompleted) {
        achievement.isCompleted = true
        
        // 发放奖励
        this.farmData.experience += achievement.rewards.experience
        this.farmData.coins += achievement.rewards.coins
        
        if (achievement.rewards.items) {
          achievement.rewards.items.forEach(item => {
            this.addItem(item.id, item.quantity)
          })
        }
        
        // 触发成就完成特效
        this.triggerAchievementEffect(achievement)
      }
    })
  }

  // 辅助方法
  private rollRarity(weights: { [key in ItemRarity]: number }): ItemRarity {
    const totalWeight = Object.values(weights).reduce((sum, weight) => sum + weight, 0)
    let random = Math.random() * totalWeight
    
    for (const [rarity, weight] of Object.entries(weights)) {
      random -= weight
      if (random <= 0) {
        return rarity as ItemRarity
      }
    }
    
    return ItemRarity.GRAY
  }

  private hasItem(itemId: string, quantity: number): boolean {
    return (this.farmData.inventory[itemId] || 0) >= quantity
  }

  private consumeItem(itemId: string, quantity: number) {
    if (this.hasItem(itemId, quantity)) {
      this.farmData.inventory[itemId] = (this.farmData.inventory[itemId] || 0) - quantity
      if (this.farmData.inventory[itemId] <= 0) {
        delete this.farmData.inventory[itemId]
      }
    }
  }

  private addItem(itemId: string, quantity: number) {
    this.farmData.inventory[itemId] = (this.farmData.inventory[itemId] || 0) + quantity
  }

  // 特效触发方法（供UI使用）
  private triggerPlantingEffect(slot: PlantingSlot) {
    console.log(`🌱 种植特效: ${slot.cropId} 在 ${slot.id}`)
  }

  private triggerMatureEffect(slot: PlantingSlot) {
    console.log(`🌟 成熟特效: ${slot.cropId} 品质 ${slot.quality}`)
  }

  private triggerHarvestEffect(result: HarvestResult) {
    console.log(`🎉 收获特效: ${result.quantity} ${result.cropId} (${result.quality})`)
  }

  private triggerLevelUpEffect(level: number) {
    console.log(`🎊 升级特效: 达到等级 ${level}`)
  }

  private triggerAchievementEffect(achievement: Achievement) {
    console.log(`🏆 成就完成: ${achievement.name}`)
  }

  // 公共API
  public getFarmData(): FarmData {
    return { ...this.farmData }
  }

  public getSlot(slotId: string): PlantingSlot | undefined {
    return this.farmData.slots.find(s => s.id === slotId)
  }

  public getAllSlots(): PlantingSlot[] {
    return [...this.farmData.slots]
  }

  public getInventory(): { [itemId: string]: number } {
    return { ...this.farmData.inventory }
  }

  public getCollection(): { [key: string]: CollectionEntry } {
    return { ...this.farmData.collection }
  }

  public getAchievements(): Achievement[] {
    return [...this.farmData.achievements]
  }

  public addUpdateCallback(callback: () => void) {
    this.updateCallbacks.add(callback)
  }

  public removeUpdateCallback(callback: () => void) {
    this.updateCallbacks.delete(callback)
  }

  private notifyUpdate() {
    this.updateCallbacks.forEach(callback => callback())
  }

  public destroy() {
    if (this.growthTimer) {
      clearInterval(this.growthTimer)
      this.growthTimer = null
    }
    this.updateCallbacks.clear()
  }
} 