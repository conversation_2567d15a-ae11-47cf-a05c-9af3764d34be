// 农场升级数据管理器
// 作为升级系统和数据持久化服务之间的桥梁，统一管理升级相关数据

import { FarmUpgradeSystem, FarmLevel, UpgradeHistory } from './FarmUpgradeSystem'
import { FarmUnlockSystem, UnlockContent, UnlockType } from './FarmUnlockSystem'
import { FarmUpgradeDataService, FarmUpgradeProgress, FarmLevelStatistics, UnlockHistory, UnlockProgress } from '../services/FarmUpgradeDataService'
import { AchievementService } from '../services/AchievementService'
import { GameProgressService } from '../services/GameProgressService'
import { CropType } from '../types/crop'

/**
 * 农场升级数据管理器
 * 统一管理农场升级、解锁内容和数据持久化
 */
export class FarmUpgradeDataManager {
  private farmUpgradeSystem: FarmUpgradeSystem
  private farmUnlockSystem: FarmUnlockSystem
  private dataService: FarmUpgradeDataService
  private achievementService: AchievementService
  private gameProgressService: GameProgressService

  constructor(
    achievementService: AchievementService,
    gameProgressService: GameProgressService
  ) {
    this.achievementService = achievementService
    this.gameProgressService = gameProgressService
    this.dataService = FarmUpgradeDataService.getInstance()
    this.farmUpgradeSystem = new FarmUpgradeSystem(achievementService, gameProgressService)
    this.farmUnlockSystem = new FarmUnlockSystem()

    this.initializeManager()
  }

  // ===== 初始化 =====

  /**
   * 初始化管理器
   */
  private async initializeManager(): Promise<void> {
    try {
      // 加载保存的等级
      const savedLevel = await this.dataService.getCurrentFarmLevel()
      this.farmUpgradeSystem.setCurrentLevel(savedLevel)

      // 加载升级历史
      const upgradeHistory = await this.dataService.getUpgradeHistory()
      this.farmUpgradeSystem.setUpgradeHistory(upgradeHistory)

      // 加载解锁进度
      const unlockProgress = await this.dataService.getUnlockProgress()
      this.syncUnlockSystemWithProgress(unlockProgress)

      console.log('✅ Farm upgrade data manager initialized')
    } catch (error) {
      console.error('❌ Failed to initialize farm upgrade data manager:', error)
    }
  }

  // ===== 升级管理 =====

  /**
   * 检查升级条件
   */
  async checkUpgradeEligibility() {
    return await this.farmUpgradeSystem.checkUpgradeEligibility()
  }

  /**
   * 执行升级
   */
  async performUpgrade() {
    try {
      const upgradeResult = await this.farmUpgradeSystem.performUpgrade()
      
      if (upgradeResult.success) {
        // 保存新的等级
        await this.dataService.saveCurrentFarmLevel(upgradeResult.newLevel)
        
        // 保存升级历史
        await this.dataService.addUpgradeHistory(upgradeResult.upgradeHistory)
        
        // 解锁新内容
        await this.processNewUnlocks(upgradeResult.newLevel)
        
        // 更新等级统计
        await this.updateLevelStatistics(upgradeResult.newLevel, upgradeResult.upgradeHistory)
        
        // 更新升级进度
        await this.updateUpgradeProgress(upgradeResult.newLevel)

        console.log(`🎉 Successfully upgraded to ${upgradeResult.newLevel}`)
      }

      return upgradeResult
    } catch (error) {
      console.error('❌ Error performing upgrade:', error)
      throw error
    }
  }

  /**
   * 获取当前等级信息
   */
  getCurrentLevelInfo() {
    return this.farmUpgradeSystem.getCurrentLevelInfo()
  }

  /**
   * 获取下一等级信息
   */
  getNextLevelInfo() {
    return this.farmUpgradeSystem.getNextLevelInfo()
  }

  /**
   * 获取当前农场等级
   */
  getCurrentLevel(): FarmLevel {
    return this.farmUpgradeSystem.getCurrentLevel()
  }

  // ===== 解锁管理 =====

  /**
   * 处理新等级的解锁内容
   */
  private async processNewUnlocks(newLevel: FarmLevel): Promise<void> {
    try {
      const newUnlocks = this.farmUnlockSystem.unlockByLevel(newLevel)
      
      for (const unlock of newUnlocks) {
        // 添加解锁历史
        const unlockHistory: UnlockHistory = {
          contentId: unlock.id,
          contentName: unlock.name,
          type: unlock.type,
          level: newLevel,
          unlockedAt: new Date(),
          prerequisites: unlock.prerequisites || []
        }
        
        await this.dataService.addUnlockHistory(unlockHistory)
        
        // 更新解锁进度
        await this.updateUnlockProgress(unlock.id, true)
      }
    } catch (error) {
      console.error('❌ Error processing new unlocks:', error)
    }
  }

  /**
   * 更新解锁进度
   */
  private async updateUnlockProgress(contentId: string, isUnlocked: boolean): Promise<void> {
    const currentProgress = await this.dataService.getUnlockProgress()
    const existingIndex = currentProgress.findIndex(p => p.contentId === contentId)

    const progressItem: UnlockProgress = {
      contentId,
      isUnlocked,
      unlockedAt: isUnlocked ? new Date() : undefined,
      lastChecked: new Date(),
      progress: isUnlocked ? 100 : 0
    }

    if (existingIndex >= 0) {
      currentProgress[existingIndex] = progressItem
    } else {
      currentProgress.push(progressItem)
    }

    await this.dataService.saveUnlockProgress(currentProgress)
  }

  /**
   * 获取解锁的内容
   */
  getUnlockedByType(type: UnlockType): UnlockContent[] {
    return this.farmUnlockSystem.getUnlockedByType(type)
  }

  /**
   * 获取解锁的作物
   */
  getUnlockedCrops(): CropType[] {
    return this.farmUnlockSystem.getUnlockedCrops()
  }

  /**
   * 获取解锁进度概览
   */
  getUnlockProgress() {
    return this.farmUnlockSystem.getUnlockProgress()
  }

  // ===== 进度管理 =====

  /**
   * 更新升级进度
   */
  private async updateUpgradeProgress(newLevel: FarmLevel): Promise<void> {
    try {
      // 获取当前游戏数据
      const gameData = await this.getGameData()
      
      const progress: FarmUpgradeProgress = {
        currentLevel: newLevel,
        currentExperience: gameData.experience || 0,
        currentFocusTime: gameData.focusTime || 0,
        cropsHarvested: gameData.cropsHarvested || 0,
        cropVariety: gameData.cropVariety || [],
        achievementsCompleted: gameData.achievementsCompleted || 0,
        currentStreak: gameData.currentStreak || 0,
        lastStreakDate: gameData.lastStreakDate,
        perfectSessions: gameData.perfectSessions || 0,
        marathonSessions: gameData.marathonSessions || 0,
        personalRecords: gameData.personalRecords || 0,
        averageScore: gameData.averageScore || 0,
        lastUpdated: new Date()
      }

      await this.dataService.saveFarmUpgradeProgress(progress)
    } catch (error) {
      console.error('❌ Error updating upgrade progress:', error)
    }
  }

  /**
   * 更新等级统计
   */
  private async updateLevelStatistics(newLevel: FarmLevel, upgradeHistory: UpgradeHistory): Promise<void> {
    try {
      const currentStats = await this.dataService.getLevelStatistics()
      const stats = currentStats || {
        totalTimeInLevel: {} as Record<FarmLevel, number>,
        upgradeAttempts: {} as Record<FarmLevel, number>,
        conditionsProgress: {} as Record<FarmLevel, Record<string, number>>,
        fastestUpgrades: {} as Record<FarmLevel, number>,
        upgradeSuccessRate: {} as Record<FarmLevel, number>,
        levelFirstAchieved: {} as Record<FarmLevel, Date>,
        totalExperienceGained: 0,
        totalFocusTimeLogged: 0,
        totalCropsHarvested: 0,
        lastUpdated: new Date()
      }

      // 更新首次达到等级时间
      if (!stats.levelFirstAchieved[newLevel]) {
        stats.levelFirstAchieved[newLevel] = new Date()
      }

      // 更新升级尝试次数
      stats.upgradeAttempts[newLevel] = (stats.upgradeAttempts[newLevel] || 0) + 1

      // 更新最快升级时间
      if (!stats.fastestUpgrades[newLevel] || upgradeHistory.timeTaken < stats.fastestUpgrades[newLevel]) {
        stats.fastestUpgrades[newLevel] = upgradeHistory.timeTaken
      }

      // 更新升级成功率（简化计算）
      stats.upgradeSuccessRate[newLevel] = 100 // 假设成功率100%，实际可能需要更复杂的计算

      await this.dataService.saveLevelStatistics(stats)
    } catch (error) {
      console.error('❌ Error updating level statistics:', error)
    }
  }

  /**
   * 获取游戏数据
   */
  private async getGameData(): Promise<any> {
    try {
             // 从多个服务收集数据
       const achievementData = this.achievementService.getUserAchievementData()
       const gameProgress = await this.gameProgressService.dbManager.getGameProgress()

       return {
         experience: achievementData.experience?.totalExperience || 0,
         focusTime: 0, // 需要从专注系统获取
         cropsHarvested: gameProgress?.crops ? Object.keys(gameProgress.crops).length : 0,
         cropVariety: [], // 需要从作物系统获取
         achievementsCompleted: achievementData.stats?.completedCount || 0,
        currentStreak: 0, // 需要从成就数据获取
        lastStreakDate: undefined,
        perfectSessions: 0, // 需要从会话数据获取
        marathonSessions: 0, // 需要从会话数据获取
        personalRecords: 0, // 需要从统计数据获取
        averageScore: 0 // 需要从会话数据获取
      }
    } catch (error) {
      console.error('❌ Error getting game data:', error)
      return {}
    }
  }

  // ===== 数据同步 =====

  /**
   * 同步解锁系统与进度数据
   */
  private syncUnlockSystemWithProgress(progressList: UnlockProgress[]): void {
    for (const progress of progressList) {
      if (progress.isUnlocked) {
        this.farmUnlockSystem.unlockContent(progress.contentId)
      }
    }
  }

  /**
   * 获取升级历史
   */
  async getUpgradeHistory(): Promise<UpgradeHistory[]> {
    return await this.dataService.getUpgradeHistory()
  }

  /**
   * 获取解锁历史
   */
  async getUnlockHistory(): Promise<UnlockHistory[]> {
    return await this.dataService.getUnlockHistory()
  }

  /**
   * 获取等级统计
   */
  async getLevelStatistics(): Promise<FarmLevelStatistics | null> {
    return await this.dataService.getLevelStatistics()
  }

  /**
   * 获取升级进度
   */
  async getFarmUpgradeProgress(): Promise<FarmUpgradeProgress | null> {
    return await this.dataService.getFarmUpgradeProgress()
  }

  // ===== 数据备份和恢复 =====

  /**
   * 创建数据备份
   */
  async createBackup(type: 'manual' | 'auto' = 'manual') {
    return await this.dataService.createBackup(type)
  }

  /**
   * 从备份恢复数据
   */
  async restoreFromBackup(backup: any) {
    try {
      await this.dataService.restoreFromBackup(backup)
      await this.initializeManager() // 重新初始化
      console.log('✅ Data restored successfully')
    } catch (error) {
      console.error('❌ Error restoring from backup:', error)
      throw error
    }
  }

  /**
   * 导出数据
   */
  async exportData(): Promise<string> {
    return await this.dataService.exportData()
  }

  /**
   * 导入数据
   */
  async importData(dataString: string): Promise<void> {
    try {
      await this.dataService.importData(dataString)
      await this.initializeManager() // 重新初始化
      console.log('✅ Data imported successfully')
    } catch (error) {
      console.error('❌ Error importing data:', error)
      throw error
    }
  }

  // ===== 设置管理 =====

  /**
   * 获取升级设置
   */
  async getUpgradeSettings() {
    return await this.dataService.getUpgradeSettings()
  }

  /**
   * 更新升级设置
   */
  async updateSettings(updates: any) {
    return await this.dataService.updateSettings(updates)
  }

  // ===== 清理和销毁 =====

  /**
   * 清理旧数据
   */
  async cleanupOldData(): Promise<void> {
    await this.dataService.cleanupOldData()
  }

  /**
   * 销毁管理器
   */
  destroy(): void {
    this.dataService.destroy()
  }
}

export default FarmUpgradeDataManager 