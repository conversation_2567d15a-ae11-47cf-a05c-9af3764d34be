// 农场解锁系统 - 定义每个等级解锁的具体内容和功能
// 与农场升级系统配合，提供详细的解锁物品和功能管理

import { FarmLevel } from './FarmUpgradeSystem'
import { CropType } from '../types/crop'

// 解锁内容类型
export enum UnlockType {
  CROP = 'crop',              // 新作物
  FEATURE = 'feature',        // 新功能
  TOOL = 'tool',             // 工具
  DECORATION = 'decoration',  // 装饰物
  ABILITY = 'ability',       // 特殊能力
  AREA = 'area',             // 新区域
  MODE = 'mode',             // 游戏模式
  CUSTOMIZATION = 'customization' // 自定义选项
}

// 解锁内容接口
export interface UnlockContent {
  id: string
  type: UnlockType
  name: string
  description: string
  icon: string
  category: string
  rarity: 'common' | 'uncommon' | 'rare' | 'epic' | 'legendary'
  levelUnlocked: FarmLevel
  prerequisites?: string[]     // 前置解锁ID
  metadata?: {
    [key: string]: any
  }
  isActive: boolean           // 是否已激活
  unlockMessage: string       // 解锁时显示的消息
  benefits: string[]          // 解锁带来的好处
}

// 作物解锁定义
export interface CropUnlock extends UnlockContent {
  type: UnlockType.CROP
  cropType: CropType
  seedAmount: number          // 初始种子数量
  specialProperties: string[] // 特殊属性
  growthTime: number         // 生长时间（分钟）
  behaviorBonus: string      // 对应的自律行为
}

// 功能解锁定义
export interface FeatureUnlock extends UnlockContent {
  type: UnlockType.FEATURE
  featureId: string
  implementation: string     // 实现说明
  dependencies: string[]     // 技术依赖
  settings?: {              // 功能设置
    [key: string]: any
  }
}

// 工具解锁定义
export interface ToolUnlock extends UnlockContent {
  type: UnlockType.TOOL
  toolId: string
  effects: {
    type: string
    value: number
    duration?: number        // 持续时间（毫秒）
  }[]
  usageLimit?: number       // 使用限制
  cooldown?: number         // 冷却时间（毫秒）
}

// 装饰解锁定义
export interface DecorationUnlock extends UnlockContent {
  type: UnlockType.DECORATION
  decorationId: string
  placementRules: {
    maxCount: number         // 最大放置数量
    allowedAreas: string[]   // 允许放置的区域
    size: string            // 占用空间
  }
  visualEffects: string[]   // 视觉效果
  bonusEffects?: {          // 奖励效果
    type: string
    value: number
  }[]
}

// 能力解锁定义
export interface AbilityUnlock extends UnlockContent {
  type: UnlockType.ABILITY
  abilityId: string
  effects: {
    target: string          // 影响目标
    modifier: string        // 修饰符类型
    value: number          // 效果值
  }[]
  isPassive: boolean        // 是否被动技能
  activationMethod?: string // 激活方式
}

// 解锁内容数据库
const UNLOCK_CONTENT_DATABASE: UnlockContent[] = [
  // === 作物解锁 === //
  {
    id: 'meditation_lotus_unlock',
    type: UnlockType.CROP,
    name: '冥想莲花',
    description: '宁静致远的冥想作物，帮助提升专注深度和心理平静',
    icon: '🪷',
    category: '冥想作物',
    rarity: 'uncommon',
    levelUnlocked: FarmLevel.APPRENTICE,
    isActive: false,
    unlockMessage: '🪷 解锁冥想莲花！在宁静中寻找内心的力量',
    benefits: [
      '提升冥想练习效果',
      '增强心理平静度',
      '专注时间延长奖励',
      '压力释放加成'
    ],
    metadata: {
      cropType: CropType.MEDITATION_LOTUS,
      seedAmount: 2,
      specialProperties: ['压力缓解', '专注增强', '心理平衡'],
      growthTime: 120,
      behaviorBonus: '冥想练习'
    }
  },

  {
    id: 'focus_flower_unlock',
    type: UnlockType.CROP,
    name: '专注花',
    description: '象征深度专注的神奇花朵，提升专注能力的极限',
    icon: '🌺',
    category: '专注作物',
    rarity: 'rare',
    levelUnlocked: FarmLevel.EXPERIENCED,
    prerequisites: ['meditation_lotus_unlock'],
    isActive: false,
    unlockMessage: '🌺 解锁专注花！进入深度专注的全新境界',
    benefits: [
      '深度专注模式解锁',
      '专注分数上限提升',
      '抗干扰能力增强',
      '心流状态触发率提升'
    ],
    metadata: {
      cropType: CropType.FOCUS_FLOWER,
      seedAmount: 3,
      specialProperties: ['深度专注', '抗干扰', '心流增强'],
      growthTime: 180,
      behaviorBonus: '深度专注'
    }
  },

  {
    id: 'reading_vine_unlock',
    type: UnlockType.CROP,
    name: '读书藤',
    description: '蕴含知识力量的藤蔓植物，促进学习和阅读习惯',
    icon: '📚',
    category: '知识作物',
    rarity: 'rare',
    levelUnlocked: FarmLevel.EXPERT,
    prerequisites: ['focus_flower_unlock'],
    isActive: false,
    unlockMessage: '📚 解锁读书藤！让知识如藤蔓般生长蔓延',
    benefits: [
      '阅读效率提升',
      '知识点记忆加强',
      '学习时间奖励',
      '理解力增强'
    ],
    metadata: {
      cropType: CropType.READING_VINE,
      seedAmount: 2,
      specialProperties: ['记忆增强', '理解力提升', '学习效率'],
      growthTime: 200,
      behaviorBonus: '阅读习惯'
    }
  },

  {
    id: 'social_fruit_unlock',
    type: UnlockType.CROP,
    name: '社交果',
    description: '增进人际关系的奇特果实，提升社交技能',
    icon: '🍎',
    category: '社交作物',
    rarity: 'epic',
    levelUnlocked: FarmLevel.MASTER,
    prerequisites: ['reading_vine_unlock'],
    isActive: false,
    unlockMessage: '🍎 解锁社交果！培养温暖的人际关系',
    benefits: [
      '社交活动奖励',
      '沟通技巧提升',
      '团队协作加成',
      '情商发展助力'
    ],
    metadata: {
      cropType: CropType.SOCIAL_FRUIT,
      seedAmount: 3,
      specialProperties: ['沟通增强', '情商提升', '团队协作'],
      growthTime: 240,
      behaviorBonus: '社交活动'
    }
  },

  // === 功能解锁 === //
  {
    id: 'weather_system_unlock',
    type: UnlockType.FEATURE,
    name: '天气系统',
    description: '动态天气环境，影响作物生长和专注体验',
    icon: '🌦️',
    category: '环境系统',
    rarity: 'rare',
    levelUnlocked: FarmLevel.EXPERIENCED,
    isActive: false,
    unlockMessage: '🌦️ 天气系统激活！感受自然环境的变化',
    benefits: [
      '动态环境体验',
      '天气影响作物生长',
      '雨天专注加成',
      '季节性奖励'
    ],
    metadata: {
      featureId: 'weather_system',
      implementation: 'WeatherManager + WeatherEffects',
      dependencies: ['weather_api', 'visual_effects'],
      settings: {
        updateInterval: 300000, // 5分钟
        enableSounds: true,
        enableVisuals: true
      }
    }
  },

  {
    id: 'advanced_analytics_unlock',
    type: UnlockType.FEATURE,
    name: '高级分析',
    description: '详细的专注数据分析和个人成长报告',
    icon: '📊',
    category: '分析工具',
    rarity: 'epic',
    levelUnlocked: FarmLevel.EXPERT,
    prerequisites: ['weather_system_unlock'],
    isActive: false,
    unlockMessage: '📊 高级分析功能上线！深度了解你的成长轨迹',
    benefits: [
      '详细数据报告',
      '趋势分析图表',
      '个性化建议',
      '习惯模式识别'
    ],
    metadata: {
      featureId: 'advanced_analytics',
      implementation: 'AnalyticsService + ChartComponents',
      dependencies: ['data_processing', 'chart_library'],
      settings: {
        reportFrequency: 'weekly',
        includeComparisons: true,
        exportFormats: ['pdf', 'json']
      }
    }
  },

  {
    id: 'custom_goals_unlock',
    type: UnlockType.FEATURE,
    name: '自定义目标',
    description: '设置个人专注目标和挑战，个性化成长路径',
    icon: '🎯',
    category: '目标管理',
    rarity: 'epic',
    levelUnlocked: FarmLevel.MASTER,
    prerequisites: ['advanced_analytics_unlock'],
    isActive: false,
    unlockMessage: '🎯 自定义目标系统启动！制定专属的成长计划',
    benefits: [
      '个性化目标设定',
      '自定义挑战模式',
      '灵活奖励系统',
      '目标进度追踪'
    ],
    metadata: {
      featureId: 'custom_goals',
      implementation: 'GoalManager + CustomChallenges',
      dependencies: ['goal_templates', 'reward_system'],
      settings: {
        maxActiveGoals: 5,
        difficultyLevels: ['easy', 'medium', 'hard', 'extreme'],
        allowCustomRewards: true
      }
    }
  },

  // === 工具解锁 === //
  {
    id: 'focus_enhancer_unlock',
    type: UnlockType.TOOL,
    name: '专注增强器',
    description: '临时提升专注效果的强化工具',
    icon: '🔧',
    category: '效率工具',
    rarity: 'rare',
    levelUnlocked: FarmLevel.EXPERIENCED,
    isActive: false,
    unlockMessage: '🔧 专注增强器就绪！提升你的专注潜能',
    benefits: [
      '专注分数提升20%',
      '干扰抵抗增强',
      '心流状态易触发',
      '持续2小时效果'
    ],
    metadata: {
      toolId: 'focus_enhancer',
      effects: [
        { type: 'focus_boost', value: 1.2, duration: 7200000 },
        { type: 'distraction_resistance', value: 0.3, duration: 7200000 }
      ],
      usageLimit: 3, // 每天最多使用3次
      cooldown: 1800000 // 30分钟冷却
    }
  },

  {
    id: 'master_toolkit_unlock',
    type: UnlockType.TOOL,
    name: '大师工具包',
    description: '专为大师级用户设计的综合工具套装',
    icon: '🛠️',
    category: '高级工具',
    rarity: 'epic',
    levelUnlocked: FarmLevel.MASTER,
    prerequisites: ['focus_enhancer_unlock'],
    isActive: false,
    unlockMessage: '🛠️ 大师工具包解锁！掌握专注的全套秘籍',
    benefits: [
      '全属性提升15%',
      '特殊能力解锁',
      '高级自定义选项',
      '独家大师功能'
    ],
    metadata: {
      toolId: 'master_toolkit',
      effects: [
        { type: 'all_stats_boost', value: 1.15 },
        { type: 'unlock_master_modes', value: 1 },
        { type: 'custom_options', value: 1 }
      ],
      usageLimit: -1, // 无限使用
      cooldown: 0
    }
  },

  // === 装饰解锁 === //
  {
    id: 'focus_statue_unlock',
    type: UnlockType.DECORATION,
    name: '专注雕像',
    description: '象征专注力量的神圣雕像，为农场带来宁静氛围',
    icon: '🗿',
    category: '雕像装饰',
    rarity: 'epic',
    levelUnlocked: FarmLevel.EXPERT,
    isActive: false,
    unlockMessage: '🗿 专注雕像矗立！为农场注入神圣的专注力量',
    benefits: [
      '农场氛围提升',
      '专注效果加成5%',
      '视觉美化',
      '成就感增强'
    ],
    metadata: {
      decorationId: 'focus_statue',
      placementRules: {
        maxCount: 1,
        allowedAreas: ['center', 'garden'],
        size: 'medium'
      },
      visualEffects: ['gentle_glow', 'particle_effects'],
      bonusEffects: [
        { type: 'focus_enhancement', value: 1.05 }
      ]
    }
  },

  {
    id: 'legendary_crown_unlock',
    type: UnlockType.DECORATION,
    name: '传奇王冠',
    description: '传奇等级的终极象征，展现你的卓越成就',
    icon: '👑',
    category: '荣誉装饰',
    rarity: 'legendary',
    levelUnlocked: FarmLevel.LEGENDARY,
    prerequisites: ['focus_statue_unlock'],
    isActive: false,
    unlockMessage: '👑 传奇王冠加冕！你已成为专注领域的王者',
    benefits: [
      '传奇身份认证',
      '所有效果加成10%',
      '特殊视觉效果',
      '传奇专属功能'
    ],
    metadata: {
      decorationId: 'legendary_crown',
      placementRules: {
        maxCount: 1,
        allowedAreas: ['avatar', 'throne'],
        size: 'special'
      },
      visualEffects: ['golden_aura', 'sparkle_effects', 'royal_glow'],
      bonusEffects: [
        { type: 'all_enhancement', value: 1.1 }
      ]
    }
  },

  // === 能力解锁 === //
  {
    id: 'deep_focus_mode_unlock',
    type: UnlockType.ABILITY,
    name: '深度专注模式',
    description: '进入极致专注状态的特殊能力',
    icon: '🧘',
    category: '专注能力',
    rarity: 'rare',
    levelUnlocked: FarmLevel.EXPERIENCED,
    isActive: false,
    unlockMessage: '🧘 深度专注模式激活！达到前所未有的专注深度',
    benefits: [
      '专注深度翻倍',
      '时间感知延缓',
      '干扰完全屏蔽',
      '心流状态保持'
    ],
    metadata: {
      abilityId: 'deep_focus_mode',
      effects: [
        { target: 'focus_depth', modifier: 'multiply', value: 2 },
        { target: 'distraction_resistance', modifier: 'add', value: 100 },
        { target: 'flow_state_duration', modifier: 'multiply', value: 3 }
      ],
      isPassive: false,
      activationMethod: 'manual_trigger'
    }
  },

  {
    id: 'legendary_aura_unlock',
    type: UnlockType.ABILITY,
    name: '传奇光环',
    description: '传奇大师独有的强大光环效果',
    icon: '✨',
    category: '光环能力',
    rarity: 'legendary',
    levelUnlocked: FarmLevel.LEGENDARY,
    prerequisites: ['deep_focus_mode_unlock'],
    isActive: false,
    unlockMessage: '✨ 传奇光环觉醒！散发出无与伦比的专注能量',
    benefits: [
      '全属性永久加成',
      '传奇特效光环',
      '感悟农场共鸣',
      '无限潜能释放'
    ],
    metadata: {
      abilityId: 'legendary_aura',
      effects: [
        { target: 'all_stats', modifier: 'multiply', value: 1.25 },
        { target: 'farm_synergy', modifier: 'add', value: 1 },
        { target: 'potential_unlock', modifier: 'set', value: 1 }
      ],
      isPassive: true,
      activationMethod: 'automatic'
    }
  },

  // === 区域解锁 === //
  {
    id: 'meditation_garden_unlock',
    type: UnlockType.AREA,
    name: '冥想花园',
    description: '专门用于冥想练习的宁静花园区域',
    icon: '🏞️',
    category: '特殊区域',
    rarity: 'rare',
    levelUnlocked: FarmLevel.EXPERT,
    isActive: false,
    unlockMessage: '🏞️ 冥想花园开放！寻找内心的宁静之地',
    benefits: [
      '冥想效果增强',
      '压力释放加速',
      '心理平衡促进',
      '特殊冥想模式'
    ],
    metadata: {
      areaId: 'meditation_garden',
      size: '20x15',
      specialFeatures: ['water_fountain', 'zen_stones', 'bamboo_grove'],
      accessRequirements: ['meditation_lotus_planted']
    }
  },

  // === 模式解锁 === //
  {
    id: 'master_mode_unlock',
    type: UnlockType.MODE,
    name: '大师模式',
    description: '为专注大师设计的高难度挑战模式',
    icon: '👨‍🎓',
    category: '游戏模式',
    rarity: 'epic',
    levelUnlocked: FarmLevel.MASTER,
    isActive: false,
    unlockMessage: '👨‍🎓 大师模式开启！接受最高难度的专注挑战',
    benefits: [
      '高难度挑战',
      '额外经验奖励',
      '独特成就解锁',
      '大师专属奖励'
    ],
    metadata: {
      modeId: 'master_mode',
      difficultyMultiplier: 2.5,
      rewardMultiplier: 3.0,
      specialMechanics: ['perfect_precision', 'zero_error_tolerance'],
      unlockRequirements: ['15_achievements', '40_hours_focus']
    }
  }
]

export class FarmUnlockSystem {
  private unlockedContent: Set<string> = new Set()
  private contentDatabase: Map<string, UnlockContent> = new Map()

  constructor() {
    // 初始化内容数据库
    UNLOCK_CONTENT_DATABASE.forEach(content => {
      this.contentDatabase.set(content.id, content)
    })

    // 默认解锁新手等级内容
    this.unlockByLevel(FarmLevel.NOVICE)
  }

  /**
   * 根据农场等级解锁内容
   */
  unlockByLevel(farmLevel: FarmLevel): UnlockContent[] {
    const newUnlocks: UnlockContent[] = []
    
    for (const content of this.contentDatabase.values()) {
      if (content.levelUnlocked === farmLevel && !this.isUnlocked(content.id)) {
        // 检查前置条件
        if (this.checkPrerequisites(content)) {
          this.unlockContent(content.id)
          newUnlocks.push(content)
        }
      }
    }

    return newUnlocks
  }

  /**
   * 解锁特定内容
   */
  unlockContent(contentId: string): boolean {
    const content = this.contentDatabase.get(contentId)
    if (!content) return false

    if (!this.checkPrerequisites(content)) return false

    this.unlockedContent.add(contentId)
    content.isActive = true

    console.log(`🎉 解锁新内容: ${content.name}`)
    return true
  }

  /**
   * 检查内容是否已解锁
   */
  isUnlocked(contentId: string): boolean {
    return this.unlockedContent.has(contentId)
  }

  /**
   * 获取指定类型的已解锁内容
   */
  getUnlockedByType(type: UnlockType): UnlockContent[] {
    return Array.from(this.contentDatabase.values())
      .filter(content => content.type === type && this.isUnlocked(content.id))
  }

  /**
   * 获取指定等级的所有可解锁内容
   */
  getContentByLevel(level: FarmLevel): UnlockContent[] {
    return Array.from(this.contentDatabase.values())
      .filter(content => content.levelUnlocked === level)
  }

  /**
   * 获取已解锁的作物类型
   */
  getUnlockedCrops(): CropType[] {
    return this.getUnlockedByType(UnlockType.CROP)
      .map(content => content.metadata?.cropType)
      .filter(Boolean)
  }

  /**
   * 获取解锁进度摘要
   */
  getUnlockProgress(): {
    totalContent: number
    unlockedContent: number
    progressPercentage: number
    byType: Record<UnlockType, { total: number; unlocked: number }>
    byLevel: Record<FarmLevel, { total: number; unlocked: number }>
  } {
    const totalContent = this.contentDatabase.size
    const unlockedCount = this.unlockedContent.size
    const progressPercentage = (unlockedCount / totalContent) * 100

    // 按类型统计
    const byType = {} as Record<UnlockType, { total: number; unlocked: number }>
    for (const type of Object.values(UnlockType)) {
      const total = Array.from(this.contentDatabase.values())
        .filter(content => content.type === type).length
      const unlocked = this.getUnlockedByType(type).length
      byType[type] = { total, unlocked }
    }

    // 按等级统计
    const byLevel = {} as Record<FarmLevel, { total: number; unlocked: number }>
    for (const level of Object.values(FarmLevel)) {
      if (typeof level === 'number') {
        const total = this.getContentByLevel(level).length
        const unlocked = this.getContentByLevel(level)
          .filter(content => this.isUnlocked(content.id)).length
        byLevel[level] = { total, unlocked }
      }
    }

    return {
      totalContent,
      unlockedContent: unlockedCount,
      progressPercentage,
      byType,
      byLevel
    }
  }

  /**
   * 获取下一可解锁内容预览
   */
  getNextUnlockPreview(currentLevel: FarmLevel): UnlockContent[] {
    const nextLevel = currentLevel + 1 as FarmLevel
    return this.getContentByLevel(nextLevel)
      .filter(content => this.checkPrerequisites(content, false))
      .sort((a, b) => {
        // 按稀有度排序
        const rarityOrder = { common: 0, uncommon: 1, rare: 2, epic: 3, legendary: 4 }
        return rarityOrder[b.rarity] - rarityOrder[a.rarity]
      })
  }

  /**
   * 私有方法：检查前置条件
   */
  private checkPrerequisites(content: UnlockContent, checkUnlocked: boolean = true): boolean {
    if (!content.prerequisites) return true

    return content.prerequisites.every(prereqId => {
      return checkUnlocked ? this.isUnlocked(prereqId) : this.contentDatabase.has(prereqId)
    })
  }

  /**
   * 获取特定内容的详细信息
   */
  getContentDetails(contentId: string): UnlockContent | undefined {
    return this.contentDatabase.get(contentId)
  }

  /**
   * 获取解锁历史
   */
  getUnlockHistory(): Array<{ content: UnlockContent; unlockedAt: Date }> {
    // 这里应该从持久化存储中获取实际的解锁历史
    // 目前返回模拟数据
    return Array.from(this.unlockedContent)
      .map(contentId => ({
        content: this.contentDatabase.get(contentId)!,
        unlockedAt: new Date() // 实际应该存储真实的解锁时间
      }))
      .filter(item => item.content)
  }

  /**
   * 导出解锁状态
   */
  exportUnlockState(): {
    unlockedContent: string[]
    timestamp: number
  } {
    return {
      unlockedContent: Array.from(this.unlockedContent),
      timestamp: Date.now()
    }
  }

  /**
   * 导入解锁状态
   */
  importUnlockState(state: { unlockedContent: string[] }): boolean {
    try {
      this.unlockedContent.clear()
      
      for (const contentId of state.unlockedContent) {
        if (this.contentDatabase.has(contentId)) {
          this.unlockedContent.add(contentId)
          const content = this.contentDatabase.get(contentId)!
          content.isActive = true
        }
      }

      return true
    } catch (error) {
      console.error('Failed to import unlock state:', error)
      return false
    }
  }
} 