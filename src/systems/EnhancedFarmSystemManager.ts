import { 
  ExtendedFarmData, 
  FarmPlot, 
  CropInstance, 
  TerrainType, 
  Season, 
  WeatherType,
  Disease,
  Pest,
  Equipment,
  FarmBuilding,
  Decoration,
  PlayerProfile,
  MarketData
} from '../types/gameModels'
import { ItemRarity } from '../types/lootbox'
import { 
  getCropGrowthConfig, 
  getPlantingItem, 
  calculateRarityProbability
} from '../data/plantingData'
import { getFuturesProductById } from '../data/chineseFuturesProducts'

// 事件类型定义
export interface FarmEvent {
  type: 'crop_mature' | 'disease_outbreak' | 'pest_attack' | 'weather_change' | 'season_change' | 'harvest_ready'
  data: any
  timestamp: number
}

export interface PlantingResult {
  success: boolean
  message: string
  cropInstance?: CropInstance
}

export interface HarvestResult {
  cropId: string
  quality: ItemRarity
  quantity: number
  value: number
  experience: number
  isRareGet: boolean
  diseaseAffected: boolean
  pestAffected: boolean
}

// 增强农场系统管理器 - 支持深度可玩性机制
export class EnhancedFarmSystemManager {
  private farmData: ExtendedFarmData
  private playerProfile: PlayerProfile
  private marketData: MarketData
  private updateCallbacks: Set<() => void> = new Set()
  private eventCallbacks: Set<(event: FarmEvent) => void> = new Set()
  private gameTimer: NodeJS.Timeout | null = null
  
  // 时间和环境状态
  private currentGameTime: number = Date.now()
  private seasonStartTime: number = Date.now()
  private seasonDuration: number = 10 * 60 * 1000 // 10分钟一个季节
  private weatherChangeInterval: number = 2 * 60 * 1000 // 2分钟换一次天气
  private lastWeatherChange: number = Date.now()

  constructor(
    farmData: ExtendedFarmData, 
    playerProfile: PlayerProfile, 
    marketData: MarketData
  ) {
    this.farmData = farmData
    this.playerProfile = playerProfile
    this.marketData = marketData
    
    this.initializeFarmIfNeeded()
    this.startGameLoop()
  }

  // ===== 初始化和设置 =====
  private initializeFarmIfNeeded() {
    // 确保农场有基础的地块
    if (this.farmData.plots.length === 0) {
      this.generateInitialPlots()
    }
    
    // 设置初始环境
    if (!this.farmData.currentSeason) {
      this.farmData.currentSeason = Season.SPRING
    }
    
    if (!this.farmData.currentWeather) {
      this.farmData.currentWeather = WeatherType.SUNNY
    }
  }

  private generateInitialPlots() {
    const gridSize = Math.sqrt(this.farmData.size.totalPlots)
    
    for (let y = 0; y < gridSize; y++) {
      for (let x = 0; x < gridSize; x++) {
        const plot: FarmPlot = {
          id: `plot_${x}_${y}`,
          x,
          y,
          terrain: this.farmData.terrain || TerrainType.PLAINS,
          soilFertility: this.generateRandomFertility(),
          rotationHistory: [],
          diseases: [],
          pests: []
        }
        
        this.farmData.plots.push(plot)
      }
    }
    
    this.farmData.size.usedPlots = 0
  }

  private generateRandomFertility(): number {
    return 50 + Math.random() * 50 // 50-100范围
  }

  // ===== 游戏循环系统 =====
  private startGameLoop() {
    if (this.gameTimer) {
      clearInterval(this.gameTimer)
    }
    
    // 每秒更新一次游戏状态
    this.gameTimer = setInterval(() => {
      this.updateGameState()
    }, 1000)
  }

  private updateGameState() {
    this.currentGameTime += 1000
    
    // 更新作物生长
    this.updateCropGrowth()
    
    // 更新环境系统
    this.updateEnvironmentalSystems()
    
    // 更新病虫害
    this.updateDiseasesAndPests()
    
    // 更新建筑效果
    this.updateBuildingEffects()
    
    this.notifyUpdate()
  }

  private updateCropGrowth() {
    this.farmData.plots.forEach(plot => {
      if (plot.currentCrop) {
        this.updateSingleCropGrowth(plot)
      }
    })
  }

  private updateSingleCropGrowth(plot: FarmPlot) {
    if (!plot.currentCrop) return
    
    const crop = plot.currentCrop
    const cropConfig = getCropGrowthConfig(crop.cropType)
    if (!cropConfig) return
    
    // 计算生长速度修正
    const growthModifier = this.calculateGrowthModifier(plot, crop)
    const growthIncrement = growthModifier / 100 // 每秒增长百分比
    
    crop.growthStage += growthIncrement
    
    // 检查是否成熟
    if (crop.growthStage >= 100 && crop.growthStage < 100 + growthIncrement) {
      this.emitEvent({
        type: 'crop_mature',
        data: { plotId: plot.id, crop },
        timestamp: this.currentGameTime
      })
    }
    
    // 更新作物健康度
    this.updateCropHealth(plot, crop)
  }

  private calculateGrowthModifier(plot: FarmPlot, crop: CropInstance): number {
    let modifier = 1.0
    
    // 地形影响
    modifier *= this.getTerrainGrowthModifier(plot.terrain, crop.cropType)
    
    // 季节影响
    modifier *= this.getSeasonGrowthModifier(this.farmData.currentSeason, crop.cropType)
    
    // 天气影响
    modifier *= this.getWeatherGrowthModifier(this.farmData.currentWeather)
    
    // 土壤肥力影响
    modifier *= (plot.soilFertility / 100)
    
    // 装备影响
    modifier *= this.getEquipmentGrowthModifier()
    
    // 病虫害影响
    modifier *= this.getDiseaseGrowthModifier(plot)
    
    return Math.max(0.1, modifier) // 最低10%生长速度
  }

  private getTerrainGrowthModifier(terrain: TerrainType, cropType: string): number {
    // 不同地形对不同作物的影响
    const terrainEffects: { [key in TerrainType]: { [cropType: string]: number } } = {
      [TerrainType.PLAINS]: { default: 1.0, corn: 1.2, wheat: 1.1 },
      [TerrainType.HILLS]: { default: 0.9, cotton: 1.1, soybean: 1.05 },
      [TerrainType.WETLANDS]: { default: 0.8, rice: 1.3, sugarcane: 1.2 },
      [TerrainType.FERTILE_VALLEY]: { default: 1.1, vegetables: 1.3 },
      [TerrainType.MOUNTAINSIDE]: { default: 0.7, apple: 1.2 }
    }
    
    return terrainEffects[terrain][cropType] || terrainEffects[terrain].default || 1.0
  }

  private getSeasonGrowthModifier(season: Season, cropType: string): number {
    // 季节对作物的影响
    const seasonEffects: { [key in Season]: { [cropType: string]: number } } = {
      [Season.SPRING]: { default: 1.2, wheat: 1.4, corn: 1.3 },
      [Season.SUMMER]: { default: 1.0, cotton: 1.3, soybean: 1.2 },
      [Season.AUTUMN]: { default: 0.9, apple: 1.4, jujube: 1.3 },
      [Season.WINTER]: { default: 0.6, wheat: 0.8 }
    }
    
    return seasonEffects[season][cropType] || seasonEffects[season].default || 1.0
  }

  private getWeatherGrowthModifier(weather: WeatherType): number {
    const weatherEffects = {
      [WeatherType.SUNNY]: 1.0,
      [WeatherType.RAINY]: 1.1,
      [WeatherType.CLOUDY]: 0.95,
      [WeatherType.STORMY]: 0.8,
      [WeatherType.DROUGHT]: 0.6,
      [WeatherType.SNOW]: 0.4
    }
    
    return weatherEffects[weather] || 1.0
  }

  private getEquipmentGrowthModifier(): number {
    let modifier = 1.0
    
    this.farmData.equipment.forEach(equipment => {
      equipment.effects.forEach(effect => {
        if (effect.type === 'growth_speed') {
          modifier += (effect.value / 100)
        }
      })
    })
    
    return modifier
  }

  private getDiseaseGrowthModifier(plot: FarmPlot): number {
    let modifier = 1.0
    
    plot.diseases.forEach(disease => {
      if (!disease.treated) {
        modifier -= (disease.severity / 100) * 0.5 // 疾病减慢生长
      }
    })
    
    plot.pests.forEach(pest => {
      if (!pest.controlled) {
        modifier -= (pest.damage / 100) * 0.3 // 虫害减慢生长
      }
    })
    
    return Math.max(0.1, modifier)
  }

  private updateCropHealth(plot: FarmPlot, crop: CropInstance) {
    // 健康度自然恢复
    crop.health = Math.min(100, crop.health + 0.1)
    
    // 疾病和虫害影响健康度
    plot.diseases.forEach(disease => {
      if (!disease.treated) {
        crop.health -= disease.severity * 0.01
      }
    })
    
    plot.pests.forEach(pest => {
      if (!pest.controlled) {
        crop.health -= pest.damage * 0.005
      }
    })
    
    crop.health = Math.max(0, crop.health)
  }

  // ===== 环境系统 =====
  private updateEnvironmentalSystems() {
    this.updateSeason()
    this.updateWeather()
    this.updateSoilHealth()
  }

  private updateSeason() {
    const seasonElapsed = this.currentGameTime - this.seasonStartTime
    
    if (seasonElapsed >= this.seasonDuration) {
      const seasons = [Season.SPRING, Season.SUMMER, Season.AUTUMN, Season.WINTER]
      const currentIndex = seasons.indexOf(this.farmData.currentSeason)
      const nextSeason = seasons[(currentIndex + 1) % seasons.length]
      
      this.farmData.currentSeason = nextSeason
      this.seasonStartTime = this.currentGameTime
      
      this.emitEvent({
        type: 'season_change',
        data: { newSeason: nextSeason },
        timestamp: this.currentGameTime
      })
    }
  }

  private updateWeather() {
    const weatherElapsed = this.currentGameTime - this.lastWeatherChange
    
    if (weatherElapsed >= this.weatherChangeInterval) {
      const newWeather = this.generateRandomWeather()
      
      if (newWeather !== this.farmData.currentWeather) {
        this.farmData.currentWeather = newWeather
        this.lastWeatherChange = this.currentGameTime
        
        this.emitEvent({
          type: 'weather_change',
          data: { newWeather },
          timestamp: this.currentGameTime
        })
      }
    }
  }

  private generateRandomWeather(): WeatherType {
    // 基于季节的天气概率
    const seasonWeatherProbabilities = {
      [Season.SPRING]: {
        [WeatherType.SUNNY]: 0.4,
        [WeatherType.RAINY]: 0.3,
        [WeatherType.CLOUDY]: 0.3
      },
      [Season.SUMMER]: {
        [WeatherType.SUNNY]: 0.6,
        [WeatherType.DROUGHT]: 0.2,
        [WeatherType.STORMY]: 0.2
      },
      [Season.AUTUMN]: {
        [WeatherType.CLOUDY]: 0.4,
        [WeatherType.RAINY]: 0.3,
        [WeatherType.SUNNY]: 0.3
      },
      [Season.WINTER]: {
        [WeatherType.SNOW]: 0.4,
        [WeatherType.CLOUDY]: 0.4,
        [WeatherType.SUNNY]: 0.2
      }
    }
    
    const probabilities = seasonWeatherProbabilities[this.farmData.currentSeason]
    const random = Math.random()
    let cumulative = 0
    
    for (const [weather, probability] of Object.entries(probabilities)) {
      cumulative += probability
      if (random <= cumulative) {
        return weather as WeatherType
      }
    }
    
    return WeatherType.SUNNY
  }

  private updateSoilHealth() {
    this.farmData.plots.forEach(plot => {
      // 土壤肥力自然恢复（缓慢）
      plot.soilFertility = Math.min(100, plot.soilFertility + 0.01)
      
      // 连续种植同一作物会降低肥力
      if (plot.currentCrop && plot.rotationHistory.length > 0) {
        const lastCrop = plot.rotationHistory[plot.rotationHistory.length - 1]
        if (lastCrop === plot.currentCrop.cropType) {
          plot.soilFertility = Math.max(20, plot.soilFertility - 0.05)
        }
      }
    })
  }

  // ===== 病虫害系统 =====
  private updateDiseasesAndPests() {
    this.farmData.plots.forEach(plot => {
      if (plot.currentCrop) {
        this.checkForNewDiseases(plot)
        this.checkForNewPests(plot)
        this.updateExistingDiseasesAndPests(plot)
      }
    })
  }

  private checkForNewDiseases(plot: FarmPlot) {
    // 基础疾病概率
    const baseDiseaseProbability = 0.0001 // 每秒0.01%
    
    // 环境因素影响
    let diseaseProbability = baseDiseaseProbability
    
    if (this.farmData.currentWeather === WeatherType.RAINY) {
      diseaseProbability *= 2
    }
    
    if (plot.soilFertility < 50) {
      diseaseProbability *= 1.5
    }
    
    if (Math.random() < diseaseProbability) {
      const diseaseTypes = ['leaf_spot', 'root_rot', 'powdery_mildew', 'bacterial_wilt']
      const diseaseType = diseaseTypes[Math.floor(Math.random() * diseaseTypes.length)]
      
      const disease: Disease = {
        type: diseaseType,
        severity: 10 + Math.random() * 20, // 10-30初始严重程度
        startedAt: new Date(),
        treated: false
      }
      
      plot.diseases.push(disease)
      
      this.emitEvent({
        type: 'disease_outbreak',
        data: { plotId: plot.id, disease },
        timestamp: this.currentGameTime
      })
    }
  }

  private checkForNewPests(plot: FarmPlot) {
    const basePestProbability = 0.00008 // 每秒0.008%
    
    let pestProbability = basePestProbability
    
    if (this.farmData.currentSeason === Season.SUMMER) {
      pestProbability *= 1.5
    }
    
    if (Math.random() < pestProbability) {
      const pestTypes = ['aphids', 'spider_mites', 'caterpillars', 'beetles']
      const pestType = pestTypes[Math.floor(Math.random() * pestTypes.length)]
      
      const pest: Pest = {
        type: pestType,
        population: 5 + Math.random() * 15, // 5-20初始数量
        damage: 0,
        controlled: false
      }
      
      plot.pests.push(pest)
      
      this.emitEvent({
        type: 'pest_attack',
        data: { plotId: plot.id, pest },
        timestamp: this.currentGameTime
      })
    }
  }

  private updateExistingDiseasesAndPests(plot: FarmPlot) {
    // 更新疾病
    plot.diseases.forEach(disease => {
      if (!disease.treated) {
        disease.severity = Math.min(100, disease.severity + 0.1)
      } else {
        disease.severity = Math.max(0, disease.severity - 1)
      }
    })
    
    // 清除已治愈的疾病
    plot.diseases = plot.diseases.filter(disease => disease.severity > 0)
    
    // 更新虫害
    plot.pests.forEach(pest => {
      if (!pest.controlled) {
        pest.population = Math.min(100, pest.population + 0.05)
        pest.damage = Math.min(100, pest.damage + pest.population * 0.01)
      } else {
        pest.population = Math.max(0, pest.population - 0.5)
        if (pest.population === 0) {
          pest.damage = 0
        }
      }
    })
    
    // 清除已消灭的虫害
    plot.pests = plot.pests.filter(pest => pest.population > 0)
  }

  // ===== 建筑系统 =====
  private updateBuildingEffects() {
    this.farmData.buildings.forEach(building => {
      switch (building.type) {
        case 'greenhouse':
          this.applyGreenhouseEffect(building)
          break
        case 'irrigation':
          this.applyIrrigationEffect(building)
          break
        case 'pestControl':
          this.applyPestControlEffect(building)
          break
      }
    })
  }

  private applyGreenhouseEffect(building: FarmBuilding) {
    // 温室减少天气影响
    const affectedPlots = this.getPlotsInRange(building.x, building.y, 2)
    // 这里可以实现温室的具体效果
  }

  private applyIrrigationEffect(building: FarmBuilding) {
    // 灌溉系统提高周围土地的水分
    const affectedPlots = this.getPlotsInRange(building.x, building.y, 3)
    // 这里可以实现灌溉的具体效果
  }

  private applyPestControlEffect(building: FarmBuilding) {
    // 害虫防治减少周围的虫害
    const affectedPlots = this.getPlotsInRange(building.x, building.y, 2)
    affectedPlots.forEach(plot => {
      plot.pests.forEach(pest => {
        if (!pest.controlled) {
          pest.population = Math.max(0, pest.population - 0.1)
        }
      })
    })
  }

  private getPlotsInRange(centerX: number, centerY: number, range: number): FarmPlot[] {
    return this.farmData.plots.filter(plot => {
      const distance = Math.sqrt((plot.x - centerX) ** 2 + (plot.y - centerY) ** 2)
      return distance <= range
    })
  }

  // ===== 种植和收获 =====
  public async plantCrop(plotId: string, cropId: string, appliedItems: string[] = []): Promise<PlantingResult> {
    const plot = this.farmData.plots.find(p => p.id === plotId)
    if (!plot) {
      return { success: false, message: '地块不存在' }
    }
    
    if (plot.currentCrop) {
      return { success: false, message: '地块已有作物' }
    }
    
    const cropConfig = getCropGrowthConfig(cropId)
    if (!cropConfig) {
      return { success: false, message: '作物配置不存在' }
    }
    
    // 检查地形适宜性
    const terrainModifier = this.getTerrainGrowthModifier(plot.terrain, cropId)
    if (terrainModifier < 0.5) {
      return { success: false, message: '当前地形不适合种植此作物' }
    }
    
    // 创建作物实例
    const crop: CropInstance = {
      id: `crop_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      cropType: cropId,
      plantedAt: new Date(),
      growthStage: 0,
      quality: ItemRarity.GRAY, // 初始品质，收获时重新计算
      health: 100,
      estimatedYield: 0,
      appliedItems
    }
    
    plot.currentCrop = crop
    this.farmData.size.usedPlots += 1
    
    // 添加到轮作历史
    plot.rotationHistory.push(cropId)
    if (plot.rotationHistory.length > 5) {
      plot.rotationHistory.shift() // 只保留最近5次记录
    }
    
    // 种植消耗一些土壤肥力
    plot.soilFertility = Math.max(20, plot.soilFertility - 5)
    
    this.notifyUpdate()
    
    return { success: true, message: '种植成功', cropInstance: crop }
  }

  public async harvestCrop(plotId: string): Promise<HarvestResult | null> {
    const plot = this.farmData.plots.find(p => p.id === plotId)
    if (!plot || !plot.currentCrop) {
      return null
    }
    
    const crop = plot.currentCrop
    if (crop.growthStage < 100) {
      return null // 未成熟
    }
    
    const cropProduct = getFuturesProductById(crop.cropType)
    if (!cropProduct) {
      return null
    }
    
    // 计算最终品质（基于多种因素）
    const finalQuality = this.calculateFinalQuality(plot, crop)
    
    // 计算产量
    const baseYield = cropProduct.yieldRanges[finalQuality]
    let actualYield = baseYield.min + Math.random() * (baseYield.max - baseYield.min)
    
    // 健康度影响产量
    actualYield *= (crop.health / 100)
    
    // 土壤肥力影响产量
    actualYield *= (plot.soilFertility / 100)
    
    actualYield = Math.floor(actualYield)
    
    // 计算价值
    const marketPrice = this.marketData.currentPrices[crop.cropType]?.current || 100
    const qualityMultiplier = this.getQualityValueMultiplier(finalQuality)
    const totalValue = Math.floor(actualYield * marketPrice * qualityMultiplier)
    
    // 计算经验
    const experience = this.calculateHarvestExperience(finalQuality, actualYield, crop.health)
    
    // 创建收获结果
    const result: HarvestResult = {
      cropId: crop.cropType,
      quality: finalQuality,
      quantity: actualYield,
      value: totalValue,
      experience,
      isRareGet: finalQuality >= ItemRarity.BLUE,
      diseaseAffected: plot.diseases.length > 0,
      pestAffected: plot.pests.length > 0
    }
    
    // 清理地块
    plot.currentCrop = undefined
    this.farmData.size.usedPlots -= 1
    
    // 土壤肥力恢复一些
    plot.soilFertility = Math.min(100, plot.soilFertility + 10)
    
    this.emitEvent({
      type: 'harvest_ready',
      data: { plotId, result },
      timestamp: this.currentGameTime
    })
    
    this.notifyUpdate()
    
    return result
  }

  private calculateFinalQuality(plot: FarmPlot, crop: CropInstance): ItemRarity {
    let qualityScore = 0
    
    // 健康度影响
    qualityScore += crop.health
    
    // 土壤肥力影响
    qualityScore += plot.soilFertility * 0.5
    
    // 地形影响
    const terrainModifier = this.getTerrainGrowthModifier(plot.terrain, crop.cropType)
    qualityScore += terrainModifier * 20
    
    // 季节影响
    const seasonModifier = this.getSeasonGrowthModifier(this.farmData.currentSeason, crop.cropType)
    qualityScore += seasonModifier * 15
    
    // 装备影响
    qualityScore += this.getEquipmentQualityBonus()
    
    // 道具影响
    crop.appliedItems.forEach(itemId => {
      const item = getPlantingItem(itemId)
      if (item?.effects.qualityBoost) {
        qualityScore += item.effects.qualityBoost
      }
    })
    
    // 转换为品质等级
    if (qualityScore >= 180) return ItemRarity.GOLD_RED  // 神话
    if (qualityScore >= 160) return ItemRarity.GOLD      // 传说
    if (qualityScore >= 140) return ItemRarity.ORANGE    // 史诗
    if (qualityScore >= 120) return ItemRarity.BLUE      // 稀有
    if (qualityScore >= 100) return ItemRarity.GREEN     // 优质
    return ItemRarity.GRAY // 普通
  }

  private getEquipmentQualityBonus(): number {
    let bonus = 0
    
    this.farmData.equipment.forEach(equipment => {
      equipment.effects.forEach(effect => {
        if (effect.type === 'quality_boost') {
          bonus += effect.value
        }
      })
    })
    
    return bonus
  }

  private getQualityValueMultiplier(quality: ItemRarity): number {
    const multipliers = {
      [ItemRarity.GRAY]: 1.0,
      [ItemRarity.GREEN]: 1.5,
      [ItemRarity.BLUE]: 2.5,
      [ItemRarity.ORANGE]: 4.0,
      [ItemRarity.GOLD]: 7.0,
      [ItemRarity.GOLD_RED]: 15.0
    }
    
    return multipliers[quality] || 1.0
  }

  private calculateHarvestExperience(quality: ItemRarity, quantity: number, health: number): number {
    const baseExp = 20
    const qualityMultiplier = this.getQualityValueMultiplier(quality)
    const healthBonus = health / 100
    const quantityBonus = Math.log(quantity + 1) / 10
    
    return Math.floor(baseExp * qualityMultiplier * healthBonus * (1 + quantityBonus))
  }

  // ===== 治疗和管理 =====
  public treatDisease(plotId: string, diseaseIndex: number, treatmentItem: string): boolean {
    const plot = this.farmData.plots.find(p => p.id === plotId)
    if (!plot || diseaseIndex >= plot.diseases.length) {
      return false
    }
    
    const disease = plot.diseases[diseaseIndex]
    
    // 这里可以根据treatmentItem的类型实现不同的治疗效果
    disease.treated = true
    disease.severity = Math.max(0, disease.severity - 50)
    
    return true
  }

  public controlPest(plotId: string, pestIndex: number, controlMethod: string): boolean {
    const plot = this.farmData.plots.find(p => p.id === plotId)
    if (!plot || pestIndex >= plot.pests.length) {
      return false
    }
    
    const pest = plot.pests[pestIndex]
    
    // 这里可以根据controlMethod的类型实现不同的控制效果
    pest.controlled = true
    pest.population = Math.max(0, pest.population - 30)
    
    return true
  }

  public improveSoil(plotId: string, improveMethod: string): boolean {
    const plot = this.farmData.plots.find(p => p.id === plotId)
    if (!plot) {
      return false
    }
    
    switch (improveMethod) {
      case 'fertilizer':
        plot.soilFertility = Math.min(100, plot.soilFertility + 20)
        break
      case 'compost':
        plot.soilFertility = Math.min(100, plot.soilFertility + 15)
        break
      case 'lime':
        plot.soilFertility = Math.min(100, plot.soilFertility + 10)
        break
    }
    
    return true
  }

  // ===== 公共API =====
  public getFarmData(): ExtendedFarmData {
    return { ...this.farmData }
  }

  public getPlot(plotId: string): FarmPlot | undefined {
    return this.farmData.plots.find(p => p.id === plotId)
  }

  public getAllPlots(): FarmPlot[] {
    return [...this.farmData.plots]
  }

  public getCurrentEnvironment() {
    return {
      season: this.farmData.currentSeason,
      weather: this.farmData.currentWeather,
      soilHealth: this.farmData.soilHealth,
      waterLevel: this.farmData.waterLevel
    }
  }

  public getGameTime(): number {
    return this.currentGameTime
  }

  // ===== 事件系统 =====
  public addEventListener(callback: (event: FarmEvent) => void) {
    this.eventCallbacks.add(callback)
  }

  public removeEventListener(callback: (event: FarmEvent) => void) {
    this.eventCallbacks.delete(callback)
  }

  private emitEvent(event: FarmEvent) {
    this.eventCallbacks.forEach(callback => callback(event))
  }

  // ===== 更新回调 =====
  public addUpdateCallback(callback: () => void) {
    this.updateCallbacks.add(callback)
  }

  public removeUpdateCallback(callback: () => void) {
    this.updateCallbacks.delete(callback)
  }

  private notifyUpdate() {
    this.updateCallbacks.forEach(callback => callback())
  }

  // ===== 清理 =====
  public destroy() {
    if (this.gameTimer) {
      clearInterval(this.gameTimer)
      this.gameTimer = null
    }
    
    this.updateCallbacks.clear()
    this.eventCallbacks.clear()
  }
}

export default EnhancedFarmSystemManager 