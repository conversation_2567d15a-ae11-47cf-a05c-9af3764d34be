import { InventoryItem, InventoryState, SynthesisRecipe, SynthesisResult, RARITY_LEVELS } from '../types/inventory';
import { LootboxItem, ItemRarity, ItemCategory } from '../types/lootbox';
import { synthesisRecipes, getAvailableRecipes } from '../data/synthesisRecipes';
import { ALL_LOOTBOX_ITEMS } from '../data/lootboxItems';

export class InventorySystem {
  private state: InventoryState;
  private listeners: ((state: InventoryState) => void)[] = [];

  constructor(maxSlots: number = 100) {
    this.state = {
      items: [],
      maxSlots,
      usedSlots: 0
    };
  }

  // 订阅状态变化
  subscribe(listener: (state: InventoryState) => void) {
    this.listeners.push(listener);
    return () => {
      this.listeners = this.listeners.filter(l => l !== listener);
    };
  }

  // 通知状态变化
  private notify() {
    this.state.usedSlots = this.state.items.reduce((sum, item) => sum + item.quantity, 0);
    this.listeners.forEach(listener => listener({ ...this.state }));
  }

  // 获取当前状态
  getState(): InventoryState {
    return { ...this.state };
  }

  // 添加物品到背包
  addItem(lootboxItem: LootboxItem, quantity: number = 1): boolean {
    // 检查背包空间
    if (this.state.usedSlots + quantity > this.state.maxSlots) {
      return false;
    }

    // 查找是否已有相同物品
    const existingItem = this.state.items.find(item => item.itemId === lootboxItem.id);
    
    if (existingItem) {
      // 增加数量
      existingItem.quantity += quantity;
    } else {
      // 创建新物品
      const newItem: InventoryItem = {
        id: `inv_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        itemId: lootboxItem.id,
        name: lootboxItem.name,
        rarity: lootboxItem.rarity,
        category: lootboxItem.category,
        type: lootboxItem.type,
        description: lootboxItem.description,
        icon: lootboxItem.icon,
        quantity,
        obtainedAt: Date.now()
      };
      this.state.items.push(newItem);
    }

    this.notify();
    return true;
  }

  // 移除物品
  removeItem(itemId: string, quantity: number = 1): boolean {
    const item = this.state.items.find(item => item.id === itemId);
    if (!item || item.quantity < quantity) {
      return false;
    }

    item.quantity -= quantity;
    if (item.quantity <= 0) {
      this.state.items = this.state.items.filter(item => item.id !== itemId);
    }

    this.notify();
    return true;
  }

  // 获取指定品质和类别的物品统计
  getItemStats(): { rarity: string; category: string; quantity: number }[] {
    const stats = new Map<string, number>();
    
    this.state.items.forEach(item => {
      const key = `${item.rarity}_${item.category}`;
      stats.set(key, (stats.get(key) || 0) + item.quantity);
    });

    return Array.from(stats.entries()).map(([key, quantity]) => {
      const [rarity, category] = key.split('_');
      return { rarity, category, quantity };
    });
  }

  // 获取可用的合成配方
  getAvailableRecipes(): SynthesisRecipe[] {
    const itemStats = this.getItemStats();
    return getAvailableRecipes(itemStats);
  }

  // 执行合成
  synthesize(recipeId: string): SynthesisResult {
    const recipe = synthesisRecipes.find(r => r.id === recipeId);
    if (!recipe) {
      return {
        success: false,
        consumedItems: [],
        message: '未找到合成配方'
      };
    }

    // 检查材料是否足够
    const consumedItems: InventoryItem[] = [];
    let canSynthesize = true;

    for (const requirement of recipe.requiredItems) {
      let neededQuantity = requirement.quantity;
      
      // 查找匹配的物品
      const matchingItems = this.state.items.filter(item => {
        const rarityMatch = item.rarity === requirement.rarity;
        const categoryMatch = !requirement.category || item.category === requirement.category;
        return rarityMatch && categoryMatch;
      }).sort((a, b) => a.quantity - b.quantity); // 优先消耗数量少的

      for (const item of matchingItems) {
        if (neededQuantity <= 0) break;
        
        const consumeQuantity = Math.min(neededQuantity, item.quantity);
        consumedItems.push({
          ...item,
          quantity: consumeQuantity
        });
        neededQuantity -= consumeQuantity;
      }

      if (neededQuantity > 0) {
        canSynthesize = false;
        break;
      }
    }

    if (!canSynthesize) {
      return {
        success: false,
        consumedItems: [],
        message: '材料不足'
      };
    }

    // 判断合成是否成功
    const isSuccess = Math.random() < recipe.successRate;
    
    if (!isSuccess) {
      // 合成失败，消耗材料
      this.consumeItems(consumedItems);
      return {
        success: false,
        consumedItems,
        message: '合成失败！材料已消耗'
      };
    }

    // 合成成功
    this.consumeItems(consumedItems);
    
    // 生成结果物品
    const resultItem = this.generateResultItem(recipe.resultRarity, consumedItems[0].category);
    if (resultItem) {
      this.addItem(resultItem, 1);
    }

    return {
      success: true,
      resultItem: resultItem ? {
        id: `inv_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        itemId: resultItem.id,
        name: resultItem.name,
        rarity: resultItem.rarity,
        category: resultItem.category,
        type: resultItem.type,
        description: resultItem.description,
        icon: resultItem.icon,
        quantity: 1,
        obtainedAt: Date.now()
      } : undefined,
      consumedItems,
      message: '合成成功！'
    };
  }

  // 消耗物品
  private consumeItems(items: InventoryItem[]) {
    for (const consumedItem of items) {
      this.removeItem(consumedItem.id, consumedItem.quantity);
    }
  }

  // 生成结果物品
  private generateResultItem(rarity: ItemRarity, preferredCategory?: ItemCategory): LootboxItem | null {
    const allItems = Object.values(ALL_LOOTBOX_ITEMS);
    let candidates = allItems.filter((item: LootboxItem) => item.rarity === rarity);
    
    // 如果有首选类别，优先选择该类别
    if (preferredCategory) {
      const categoryItems = candidates.filter((item: LootboxItem) => item.category === preferredCategory);
      if (categoryItems.length > 0) {
        candidates = categoryItems;
      }
    }

    if (candidates.length === 0) {
      return null;
    }

    return candidates[Math.floor(Math.random() * candidates.length)];
  }

  // 按品质分组物品
  getItemsByRarity(): Record<string, InventoryItem[]> {
    const groups: Record<string, InventoryItem[]> = {};
    
    this.state.items.forEach(item => {
      if (!groups[item.rarity]) {
        groups[item.rarity] = [];
      }
      groups[item.rarity].push(item);
    });

    return groups;
  }

  // 按类别分组物品
  getItemsByCategory(): Record<string, InventoryItem[]> {
    const groups: Record<string, InventoryItem[]> = {};
    
    this.state.items.forEach(item => {
      if (!groups[item.category]) {
        groups[item.category] = [];
      }
      groups[item.category].push(item);
    });

    return groups;
  }

  // 清空背包
  clear() {
    this.state.items = [];
    this.notify();
  }

  // 扩展背包槽位
  expandSlots(additionalSlots: number) {
    this.state.maxSlots += additionalSlots;
    this.notify();
  }
} 