import { TutorialStep } from '../types/tutorial'
import { PlantingCelebration } from '../components/tutorial/PlantingCelebration'

// 摄像头设置引导步骤
export const cameraSetupSteps: TutorialStep[] = [
  {
    id: 'welcome',
    title: '欢迎来到自律农场！',
    description: '这是一个通过摄像头监测你的专注状态，帮助你养成良好习惯的农场游戏。让我们开始设置吧！',
    position: 'center',
    skipable: false,
    onAfterShow: async () => {
      // 确保应用界面已加载
      await new Promise(resolve => setTimeout(resolve, 500))
    }
  },
  {
    id: 'camera-toggle',
    title: '开启摄像头',
    description: '首先，我们需要开启摄像头来监测你的姿态。点击"显示摄像头"按钮开始。',
    targetElement: '.toggle-camera-btn',
    position: 'bottom',
    action: 'click',
    waitForElement: true,
    onBeforeNext: async () => {
      // 检查摄像头是否已显示
      const cameraSection = document.querySelector('.camera-section')
      return cameraSection !== null
    }
  },
  {
    id: 'camera-permission',
    title: '允许摄像头权限',
    description: '浏览器会请求摄像头权限，请点击"允许"以便我们能够检测你的姿态。这些数据只在本地处理，不会上传到服务器。',
    targetElement: '.camera-section',
    position: 'right',
    waitForElement: true,
    onBeforeNext: async () => {
      // 等待摄像头权限获取
      let attempts = 0
      while (attempts < 30) { // 最多等待15秒
        const statusElement = document.querySelector('.status-indicator')
        if (statusElement && statusElement.textContent?.includes('🟢')) {
          return true
        }
        await new Promise(resolve => setTimeout(resolve, 500))
        attempts++
      }
      return false
    }
  },
  {
    id: 'camera-position',
    title: '调整摄像头位置',
    description: '请调整你的坐姿，确保摄像头能清楚看到你的上半身。保持坐姿端正，这样系统能更好地检测你的专注状态。',
    targetElement: '.main-camera',
    position: 'right',
    onAfterShow: async () => {
      // 给用户一些时间调整位置
      await new Promise(resolve => setTimeout(resolve, 2000))
    }
  }
]

// 农场介绍步骤
export const farmIntroSteps: TutorialStep[] = [
  {
    id: 'farm-intro',
    title: '你的专注农场',
    description: '这是你的专属农场！当你保持专注时，农场里的植物会茁壮成长。让我们来了解一下农场的各个区域。',
    targetElement: '.game-section',
    position: 'top'
  },
  {
    id: 'control-panel',
    title: '控制面板',
    description: '这里显示你的专注状态、农场统计和操作按钮。你可以在这里查看当前的专注度和农场收成。',
    targetElement: '.control-panel',
    position: 'left'
  },
  {
    id: 'focus-status',
    title: '专注状态监测',
    description: '这里实时显示你的专注度。当你坐姿端正、注视前方时，专注度会提高，农场里的植物就会开始生长！',
    targetElement: '.focus-indicator',
    position: 'left'
  }
]

// 第一次种植步骤
export const firstPlantingSteps: TutorialStep[] = [
  {
    id: 'start-focus',
    title: '开始专注训练',
    description: '点击"开始专注学习"按钮来开始你的第一次专注会话。记住要保持良好的坐姿！',
    targetElement: '.focus-btn',
    position: 'left',
    action: 'click',
    onBeforeNext: async () => {
      // 检查专注会话是否已开始
      const focusBtn = document.querySelector('.focus-btn')
      return focusBtn?.classList.contains('active') || false
    }
  },
  {
    id: 'maintain-posture',
    title: '保持专注姿态',
    description: '很好！现在请保持端正的坐姿，注视前方。你会看到专注度逐渐提升，当达到一定水平时，农场里就会长出第一株植物！',
    targetElement: '.focus-score',
    position: 'left',
    onBeforeNext: async () => {
      // 等待用户获得一定的专注度
      let attempts = 0
      while (attempts < 60) { // 最多等待30秒
        const scoreElement = document.querySelector('.focus-score')
        if (scoreElement) {
          const scoreText = scoreElement.textContent || ''
          const score = parseInt(scoreText.match(/\d+/)?.[0] || '0')
          if (score >= 70) {
            return true
          }
        }
        await new Promise(resolve => setTimeout(resolve, 500))
        attempts++
      }
      return true // 即使没达到也允许继续
    }
  },
  {
    id: 'first-plant',
    title: '第一株植物诞生！',
    description: '恭喜！你已经成功种植了第一株知识花。继续保持专注，你的农场会越来越繁荣！',
    targetElement: '.phaser-container',
    position: 'top',
    onAfterShow: async () => {
      // 给用户一些时间欣赏成果
      await new Promise(resolve => setTimeout(resolve, 3000))
    }
  },
  {
    id: 'first-planting-celebration',
    title: '庆祝第一次种植！',
    description: '让我们为这个重要的里程碑庆祝一下！',
    position: 'center',
    action: 'celebration',
    customComponent: PlantingCelebration,
    actionData: { plantType: 'knowledge' },
    skipable: false
  }
]

// 基础操作教学步骤
export const basicOperationsSteps: TutorialStep[] = [
  {
    id: 'farm-stats',
    title: '📊 农场统计面板',
    description: '这里显示你培养的各种植物数量。知识花🌸来自专注学习，力量树🌳来自运动，时间菜🥬来自时间管理，冥想莲🪷来自冥想练习。',
    targetElement: '.stats-grid',
    position: 'left',
    onAfterShow: async () => {
      // 高亮统计面板
      const statsGrid = document.querySelector('.stats-grid')
      if (statsGrid) {
        statsGrid.classList.add('tutorial-highlight-stats')
        setTimeout(() => {
          statsGrid.classList.remove('tutorial-highlight-stats')
        }, 3000)
      }
      await new Promise(resolve => setTimeout(resolve, 2000))
    }
  },
  {
    id: 'daily-data',
    title: '📈 今日数据追踪',
    description: '查看你今天的专注时长和表现。建议每天至少专注30分钟，保持80%以上的专注度可获得更好的成长效果！',
    targetElement: '.daily-stats',
    position: 'left',
    onAfterShow: async () => {
      // 高亮今日数据
      const dailyStats = document.querySelector('.daily-stats')
      if (dailyStats) {
        dailyStats.classList.add('tutorial-highlight-daily')
        setTimeout(() => {
          dailyStats.classList.remove('tutorial-highlight-daily')
        }, 3000)
      }
      await new Promise(resolve => setTimeout(resolve, 2000))
    }
  },
  {
    id: 'rewards',
    title: '🎁 成就奖励系统',
    description: '完成每日目标获得奖励！🏆连续专注30分钟、🌱培养5株植物、⭐保持高专注度。达成目标会有特殊奖励！',
    targetElement: '.reward-container',
    position: 'left',
    onAfterShow: async () => {
      // 高亮奖励容器
      const rewardContainer = document.querySelector('.reward-container')
      if (rewardContainer) {
        rewardContainer.classList.add('tutorial-highlight-rewards')
        setTimeout(() => {
          rewardContainer.classList.remove('tutorial-highlight-rewards')
        }, 3000)
      }
      await new Promise(resolve => setTimeout(resolve, 2000))
    }
  },
  {
    id: 'other-activities',
    title: '🛠️ 多样化活动中心',
    description: '探索不同的专注活动！💡专注学习培养知识花，🏃运动打卡培养力量树，⏰时间管理培养时间菜，🧘冥想练习培养冥想莲。',
    targetElement: '.action-buttons',
    position: 'left',
    onAfterShow: async () => {
      // 高亮操作按钮
      const actionButtons = document.querySelector('.action-buttons')
      if (actionButtons) {
        actionButtons.classList.add('tutorial-highlight-actions')
        setTimeout(() => {
          actionButtons.classList.remove('tutorial-highlight-actions')
        }, 3000)
      }
      await new Promise(resolve => setTimeout(resolve, 2000))
    }
  }
]

// 完成庆祝步骤
export const completionSteps: TutorialStep[] = [
  {
    id: 'tutorial-complete',
    title: '教程完成！',
    description: '恭喜你完成了所有的新手引导！现在你已经掌握了自律农场的基本操作。继续保持专注，让你的农场茁壮成长吧！',
    position: 'center',
    skipable: false,
    onAfterShow: async () => {
      // 播放庆祝动画或音效
      console.log('🎉 Tutorial completed! 🎉')
    }
  }
]

// 完整的引导流程
export const fullTutorialSteps: TutorialStep[] = [
  ...cameraSetupSteps,
  ...farmIntroSteps,
  ...firstPlantingSteps,
  ...basicOperationsSteps,
  ...completionSteps
]

// 单独的引导流程（可用于特定场景）
export const tutorialFlows = {
  cameraSetup: cameraSetupSteps,
  farmIntro: farmIntroSteps,
  firstPlanting: firstPlantingSteps,
  basicOperations: basicOperationsSteps,
  completion: completionSteps,
  full: fullTutorialSteps
} 