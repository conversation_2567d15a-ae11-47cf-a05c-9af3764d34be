/* 全局样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif;
  background: linear-gradient(135deg, #87CEEB 0%, #98FB98 50%, #90EE90 100%);
  min-height: 100vh;
  animation: backgroundShift 20s ease-in-out infinite;
}

@keyframes backgroundShift {
  0%, 100% { background: linear-gradient(135deg, #87CEEB 0%, #98FB98 50%, #90EE90 100%); }
  50% { background: linear-gradient(135deg, #98FB98 0%, #87CEEB 50%, #ADD8E6 100%); }
}

.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 头部样式 */
.app-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(15px);
  padding: 1.5rem 2rem;
  text-align: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  border-bottom: 3px solid rgba(144, 238, 144, 0.5);
  position: relative;
  overflow: hidden;
}

.app-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shimmer 3s infinite;
}

@keyframes shimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}

.app-header h1 {
  color: #2C5530;
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
  animation: titlePulse 4s ease-in-out infinite;
}

@keyframes titlePulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.02); }
}

.app-header p {
  color: #555;
  font-size: 1.2rem;
  font-weight: 300;
}

/* 游戏容器 */
.game-container {
  flex: 1;
  display: flex;
  gap: 2.5rem;
  padding: 2.5rem;
  align-items: flex-start;
  position: relative;
}

.game-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 30%, rgba(255, 255, 255, 0.1) 1px, transparent 1px),
    radial-gradient(circle at 80% 70%, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
  animation: sparkle 10s linear infinite;
  pointer-events: none;
}

@keyframes sparkle {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 0.6; }
}

.phaser-container {
  border: 3px solid #8B4513;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  background: white;
  position: relative;
  width: 800px;
  height: 600px;
  margin: 0 auto;
}

.phaser-container canvas {
  display: block !important;
  width: 100% !important;
  height: 100% !important;
  object-fit: contain;
}

/* 控制面板样式 */
.control-panel {
  width: 320px;
  background: rgba(255, 255, 255, 0.97);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: transform 0.3s ease;
}

.control-panel:hover {
  transform: translateY(-5px);
  box-shadow: 
    0 12px 40px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

/* 左侧面板专用样式 */
.control-panel.left-panel {
  width: 280px;
  max-height: calc(100vh - 120px);
  overflow-y: auto;
  padding: 0;
  border-radius: 16px;
  position: sticky;
  top: 20px;
}

.control-panel.left-panel .panel-header {
  border-radius: 16px 16px 0 0;
  margin-bottom: 0;
}

.control-panel.left-panel .panel-section {
  padding: 1.5rem;
  border-bottom: 1px solid rgba(224, 224, 224, 0.3);
}

.control-panel.left-panel .panel-section:last-child {
  border-bottom: none;
  border-radius: 0 0 16px 16px;
}

/* 顶部功能按钮栏样式 */
.top-action-bar {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(15px);
  border-bottom: 2px solid rgba(144, 238, 144, 0.3);
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  position: sticky;
  top: 0;
  z-index: 100;
  overflow-x: auto;
  overflow-y: hidden;
}

.action-bar-container {
  display: flex;
  padding: 12px 20px;
  gap: 30px;
  min-width: fit-content;
  align-items: flex-start;
}

.action-bar-section {
  display: flex;
  flex-direction: column;
  min-width: 200px;
}

.action-bar-section h4 {
  margin: 0 0 8px 0;
  font-size: 0.9rem;
  color: #2C5530;
  font-weight: 600;
  text-align: center;
  padding: 4px 8px;
  background: linear-gradient(135deg, #E8F5E8, #F0F8F0);
  border-radius: 12px;
  border: 1px solid rgba(144, 238, 144, 0.3);
}

.action-bar-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  justify-content: center;
}

.top-action-btn {
  padding: 8px 12px;
  font-size: 0.85rem;
  font-weight: 500;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  color: #495057;
  border: 1px solid rgba(0, 0, 0, 0.1);
  min-width: 120px;
  text-align: center;
  white-space: nowrap;
}

.top-action-btn:hover {
  background: linear-gradient(135deg, #e9ecef, #dee2e6);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.top-action-btn.active {
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
  box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
}

.top-action-btn.active:hover {
  background: linear-gradient(135deg, #45a049, #3d8b40);
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.4);
}

/* 特定按钮颜色 */
.top-action-btn.focus-btn {
  background: linear-gradient(135deg, #007bff, #0056b3);
  color: white;
}

.top-action-btn.focus-btn:hover {
  background: linear-gradient(135deg, #0056b3, #004085);
}

.top-action-btn.exercise-btn {
  background: linear-gradient(135deg, #ff6b6b, #ee5a52);
  color: white;
}

.top-action-btn.exercise-btn:hover {
  background: linear-gradient(135deg, #ee5a52, #dc4c42);
}

.top-action-btn.sleep-btn {
  background: linear-gradient(135deg, #6f42c1, #59359a);
  color: white;
}

.top-action-btn.sleep-btn:hover {
  background: linear-gradient(135deg, #59359a, #4c2d85);
}

.top-action-btn.meditate-btn {
  background: linear-gradient(135deg, #20c997, #17a2b8);
  color: white;
}

.top-action-btn.meditate-btn:hover {
  background: linear-gradient(135deg, #17a2b8, #138496);
}

.top-action-btn.agricultural-btn {
  background: linear-gradient(135deg, #fd7e14, #e8630e);
  color: white;
}

.top-action-btn.agricultural-btn:hover {
  background: linear-gradient(135deg, #e8630e, #d05610);
}

.top-action-btn.tutorial-skip-btn {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
  animation: buttonPulse 2s infinite;
}

@keyframes buttonPulse {
  0%, 100% { 
    transform: scale(1);
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
  }
  50% { 
    transform: scale(1.02);
    box-shadow: 0 4px 16px rgba(40, 167, 69, 0.5);
  }
}

/* 响应式适配 */
@media (max-width: 1200px) {
  .action-bar-container {
    gap: 20px;
  }
  
  .action-bar-section {
    min-width: 160px;
  }
  
  .top-action-btn {
    min-width: 100px;
    font-size: 0.8rem;
    padding: 6px 10px;
  }
}

@media (max-width: 768px) {
  .action-bar-container {
    flex-direction: column;
    gap: 15px;
    padding: 10px 15px;
  }
  
  .action-bar-section {
    min-width: auto;
    width: 100%;
  }
  
  .action-bar-buttons {
    justify-content: flex-start;
  }
  
  .top-action-btn {
    min-width: auto;
    flex: 1;
    max-width: 180px;
  }
}

.control-panel h3 {
  color: #2C5530;
  margin-bottom: 1.5rem;
  font-size: 1.3rem;
  font-weight: 600;
  border-bottom: 3px solid #90EE90;
  padding-bottom: 0.8rem;
  position: relative;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

.control-panel h3::after {
  content: '';
  position: absolute;
  bottom: -3px;
  left: 0;
  width: 50%;
  height: 3px;
  background: linear-gradient(90deg, #32CD32, #90EE90);
  border-radius: 1.5px;
  animation: underlineGlow 2s ease-in-out infinite;
}

@keyframes underlineGlow {
  0%, 100% { box-shadow: 0 0 5px rgba(50, 205, 50, 0.3); }
  50% { box-shadow: 0 0 15px rgba(50, 205, 50, 0.6); }
}

/* 专注状态 */
.focus-status {
  margin-bottom: 2.5rem;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: linear-gradient(135deg, #F0F8F0, #E8F5E8);
  border-radius: 12px;
  border-left: 5px solid #32CD32;
  box-shadow: 
    0 4px 15px rgba(50, 205, 50, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
}

.status-indicator:hover {
  transform: translateX(5px);
  box-shadow: 
    0 6px 20px rgba(50, 205, 50, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 1);
}

.status-dot {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: radial-gradient(circle at 30% 30%, #FFD700, #FFA500);
  animation: statusPulse 2s infinite, statusRotate 4s linear infinite;
  box-shadow: 
    0 0 10px rgba(255, 165, 0, 0.5),
    inset 1px 1px 2px rgba(255, 255, 255, 0.3);
}

@keyframes statusPulse {
  0%, 100% { 
    opacity: 1; 
    transform: scale(1);
    box-shadow: 0 0 10px rgba(255, 165, 0, 0.5);
  }
  50% { 
    opacity: 0.7; 
    transform: scale(1.2);
    box-shadow: 0 0 20px rgba(255, 165, 0, 0.8);
  }
}

@keyframes statusRotate {
  0% { background: radial-gradient(circle at 30% 30%, #FFD700, #FFA500); }
  33% { background: radial-gradient(circle at 30% 30%, #32CD32, #228B22); }
  66% { background: radial-gradient(circle at 30% 30%, #4169E1, #1E90FF); }
  100% { background: radial-gradient(circle at 30% 30%, #FFD700, #FFA500); }
}

.status-indicator span {
  font-weight: 500;
  color: #2C5530;
  text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.8);
}

/* 农场统计 */
.farm-stats {
  margin-bottom: 2.5rem;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 0;
  border-bottom: 1px solid rgba(224, 224, 224, 0.5);
  transition: all 0.3s ease;
  position: relative;
}

.stat-item:hover {
  background: rgba(144, 238, 144, 0.1);
  padding-left: 1rem;
  margin: 0 -1rem;
  border-radius: 8px;
}

.stat-item:last-child {
  border-bottom: none;
}

.stat-item span:first-child {
  color: #666;
  font-weight: 500;
  position: relative;
}

.stat-item span:last-child {
  font-weight: bold;
  color: #2C5530;
  padding: 0.3rem 0.8rem;
  background: linear-gradient(135deg, #90EE90, #98FB98);
  border-radius: 20px;
  box-shadow: 0 2px 8px rgba(144, 238, 144, 0.3);
  animation: valueGlow 3s ease-in-out infinite;
}

@keyframes valueGlow {
  0%, 100% { box-shadow: 0 2px 8px rgba(144, 238, 144, 0.3); }
  50% { box-shadow: 0 4px 15px rgba(144, 238, 144, 0.5); }
}

/* 按钮样式 */
.actions {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.action-btn {
  padding: 1rem 1.8rem;
  border: none;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  overflow: hidden;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.action-btn::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.3s, height 0.3s;
}

.action-btn:hover::before {
  width: 300px;
  height: 300px;
}

.action-btn.primary {
  background: linear-gradient(135deg, #32CD32, #228B22, #006400);
  background-size: 200% 200%;
  color: white;
  box-shadow: 
    0 4px 15px rgba(50, 205, 50, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  animation: primaryGradient 4s ease-in-out infinite;
}

@keyframes primaryGradient {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

.action-btn.primary:hover {
  transform: translateY(-3px) scale(1.02);
  box-shadow: 
    0 8px 25px rgba(50, 205, 50, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.action-btn.primary:active {
  transform: translateY(-1px) scale(0.98);
}

.action-btn.secondary {
  background: linear-gradient(135deg, #F8F8F8, #E8E8E8);
  color: #555;
  border: 2px solid #DDD;
  box-shadow: 
    0 4px 15px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

.action-btn.secondary:hover {
  background: linear-gradient(135deg, #EEEEEE, #DDDDDD);
  border-color: #BBB;
  transform: translateY(-2px) scale(1.01);
  box-shadow: 
    0 6px 20px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 1);
}

.action-btn.secondary:active {
  transform: translateY(0) scale(0.98);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .game-container {
    flex-direction: column;
    align-items: center;
    gap: 2rem;
  }
  
  .control-panel {
    width: 100%;
    max-width: 800px;
  }
  
  .phaser-container {
    width: 100%;
    max-width: 800px;
    height: auto;
  }
}

@media (max-width: 768px) {
  .app-header {
    padding: 1rem;
  }
  
  .app-header h1 {
    font-size: 2rem;
  }
  
  .game-container {
    padding: 1rem;
  }
  
  .control-panel {
    padding: 1.5rem;
    width: 100%;
  }
  
  .action-btn {
    padding: 0.8rem 1.5rem;
    font-size: 1rem;
  }
}

/* 新增：性能优化的loading动画 */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* 摄像头状态栏 */
.camera-status-bar {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-top: 1rem;
  padding: 0.75rem 1.5rem;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 25px;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.status-indicator {
  font-size: 1.2rem;
  animation: pulse 2s infinite;
}

.status-idle { color: #6B7280; }
.status-requesting { color: #F59E0B; }
.status-granted { color: #10B981; }
.status-denied { color: #EF4444; }
.status-error { color: #EF4444; }

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

.toggle-camera-btn {
  padding: 0.5rem 1rem;
  background: linear-gradient(135deg, #3B82F6, #1D4ED8);
  color: white;
  border: none;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.toggle-camera-btn:hover {
  background: linear-gradient(135deg, #1D4ED8, #1E40AF);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

/* 主要内容区域 */
.app-main {
  flex: 1;
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
}

.content-container {
  display: grid;
  grid-template-columns: 1fr 1fr 300px;
  gap: 2rem;
  align-items: start;
}

/* 摄像头区域 */
.camera-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(15px);
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.camera-section h2 {
  margin: 0 0 1rem 0;
  color: #1F2937;
  font-size: 1.25rem;
  font-weight: 700;
}

/* 游戏区域 */
.game-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(15px);
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.game-section h2 {
  margin: 0 0 1rem 0;
  color: #1F2937;
  font-size: 1.25rem;
  font-weight: 700;
}

/* 更新控制面板样式 */
.control-panel {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(15px);
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  height: fit-content;
  position: sticky;
  top: 2rem;
}

.panel-section {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.panel-section h3 {
  margin: 0;
  color: #1F2937;
  font-size: 1rem;
  font-weight: 700;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #E5E7EB;
}

/* 专注状态指示器 */
.focus-indicator {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.focus-status {
  padding: 0.75rem 1rem;
  border-radius: 8px;
  font-weight: 500;
  text-align: center;
  transition: all 0.3s ease;
}

.focus-status.active {
  background: linear-gradient(135deg, #D1FAE5, #A7F3D0);
  color: #065F46;
  border: 1px solid #10B981;
}

.focus-status.inactive {
  background: linear-gradient(135deg, #F3F4F6, #E5E7EB);
  color: #6B7280;
  border: 1px solid #D1D5DB;
}

/* 统计网格 */
.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.75rem;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0.75rem;
  background: linear-gradient(135deg, #F8FAFC, #E2E8F0);
  border-radius: 8px;
  border: 1px solid #CBD5E0;
  transition: all 0.3s ease;
}

.stat-item:hover {
  background: linear-gradient(135deg, #E2E8F0, #CBD5E0);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-label {
  font-size: 0.75rem;
  color: #6B7280;
  font-weight: 500;
  margin-bottom: 0.25rem;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1F2937;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.action-btn {
  padding: 0.75rem 1rem;
  border: none;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.focus-btn {
  background: linear-gradient(135deg, #FEF3C7, #FDE68A);
  color: #92400E;
  border: 1px solid #F59E0B;
}

.focus-btn:hover {
  background: linear-gradient(135deg, #FDE68A, #FBBF24);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
}

.exercise-btn {
  background: linear-gradient(135deg, #DBEAFE, #BFDBFE);
  color: #1E40AF;
  border: 1px solid #3B82F6;
}

.exercise-btn:hover {
  background: linear-gradient(135deg, #BFDBFE, #93C5FD);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.sleep-btn {
  background: linear-gradient(135deg, #E0E7FF, #C7D2FE);
  color: #3730A3;
  border: 1px solid #6366F1;
}

.sleep-btn:hover {
  background: linear-gradient(135deg, #C7D2FE, #A5B4FC);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

.meditate-btn {
  background: linear-gradient(135deg, #F3E8FF, #E9D5FF);
  color: #581C87;
  border: 1px solid #8B5CF6;
}

.meditate-btn:hover {
  background: linear-gradient(135deg, #E9D5FF, #DDD6FE);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
}

/* 奖励容器 */
.reward-container {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.reward-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.reward-item.pending {
  background: linear-gradient(135deg, #FEF7CD, #FEF3C7);
  color: #92400E;
  border: 1px solid #F59E0B;
}

.reward-item.completed {
  background: linear-gradient(135deg, #D1FAE5, #A7F3D0);
  color: #065F46;
  border: 1px solid #10B981;
}

.reward-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 页脚 */
.app-footer {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(15px);
  text-align: center;
  padding: 1.5rem;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  margin-top: 2rem;
}

.app-footer p {
  margin: 0;
  color: #6B7280;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .content-container {
    grid-template-columns: 1fr 280px;
  }
  
  .camera-section {
    grid-column: 1 / -1;
  }
}

@media (max-width: 768px) {
  .app-main {
    padding: 1rem;
  }
  
  .content-container {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .camera-status-bar {
    flex-direction: column;
    gap: 0.5rem;
    text-align: center;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .control-panel {
    position: static;
  }

  /* 响应式新增样式 */
  .camera-section,
  .game-section {
    padding: 1rem;
    margin: 0.5rem 0;
  }

  .phaser-container {
    width: 100% !important;
    height: auto !important;
  }

  .action-buttons {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .action-btn {
    width: 100%;
    min-height: 44px;
    font-size: 1rem;
    padding: 12px 16px;
  }
}

/* 专注状态增强样式 */
.focus-status.good {
  background: linear-gradient(135deg, #E8F5E8, #F0FFF0);
  border-left: 5px solid #32CD32;
  color: #2C5530;
}

.focus-status.poor {
  background: linear-gradient(135deg, #FFF5F5, #FEF2F2);
  border-left: 5px solid #FF6B6B;
  color: #8B2635;
}

.focus-score, .focus-streak, .focus-average {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.focus-score {
  font-size: 1.2rem;
  font-weight: 700;
}

.session-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 1rem;
  padding: 0.8rem;
  background: rgba(255, 215, 0, 0.1);
  border-radius: 8px;
  border-left: 4px solid #FFD700;
}

.session-indicator {
  animation: sessionPulse 1.5s infinite;
}

@keyframes sessionPulse {
  0%, 100% { 
    transform: scale(1); 
    filter: drop-shadow(0 0 5px rgba(255, 215, 0, 0.5));
  }
  50% { 
    transform: scale(1.1); 
    filter: drop-shadow(0 0 10px rgba(255, 215, 0, 0.8));
  }
}

/* 成长积分样式 */
.growth-points {
  margin-top: 1rem;
  padding: 1rem;
  background: linear-gradient(135deg, #FFF8DC, #F5F5DC);
  border-radius: 8px;
  border-left: 4px solid #DAA520;
  text-align: center;
  font-weight: 600;
  color: #8B7355;
}

/* 今日数据样式 */
.daily-stats {
  background: rgba(245, 245, 245, 0.5);
  border-radius: 8px;
  padding: 1rem;
}

.stat-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.8rem;
  padding: 0.5rem 0;
  border-bottom: 1px solid rgba(200, 200, 200, 0.3);
}

.stat-row:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.stat-row span:first-child {
  color: #666;
  font-weight: 500;
}

.stat-row span:last-child {
  color: #2C5530;
  font-weight: 700;
}

/* 操作按钮激活状态 */
.action-btn.active {
  background: linear-gradient(135deg, #32CD32, #90EE90);
  color: white;
  transform: scale(1.05);
  box-shadow: 0 4px 15px rgba(50, 205, 50, 0.3);
}

.action-btn.active:hover {
  background: linear-gradient(135deg, #228B22, #32CD32);
  transform: scale(1.08);
}

/* 奖励状态增强 */
.reward-item.completed span:first-child {
  filter: drop-shadow(0 0 5px rgba(50, 205, 50, 0.5));
  animation: rewardGlow 2s infinite;
}

@keyframes rewardGlow {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

/* 调试信息 */
.debug-info {
  margin-top: 1rem;
  padding: 0.5rem;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
  text-align: center;
}

.debug-info small {
  color: #666;
  font-family: 'Courier New', monospace;
  font-size: 0.8rem;
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .action-btn,
  .toggle-camera-btn,
  .sidebar-toggle {
    min-height: 44px !important;
    min-width: 44px !important;
    padding: 12px 16px !important;
    font-size: 16px !important;
  }

  .control-panel {
    touch-action: pan-y;
  }

  .phaser-container {
    touch-action: manipulation;
  }

  .stats-grid {
    gap: 16px !important;
  }

  .stat-item {
    padding: 16px !important;
    border-radius: 12px !important;
  }

  .panel-section {
    margin-bottom: 24px !important;
  }
}

/* 横屏模式优化 */
@media (orientation: landscape) and (max-height: 600px) {
  .app-header {
    padding: 0.5rem 1rem !important;
  }

  .app-header h1 {
    font-size: 1.5rem !important;
    margin-bottom: 0.25rem !important;
  }

  .app-header p {
    font-size: 0.9rem !important;
  }

  .app-main {
    padding: 0.5rem !important;
  }

  .camera-section,
  .game-section {
    padding: 0.75rem !important;
  }

  .control-panel {
    max-height: 70vh !important;
    overflow-y: auto !important;
  }
}

/* 竖屏模式优化 */
@media (orientation: portrait) and (max-width: 768px) {
  .phaser-container {
    max-height: 50vh !important;
  }
  
  .control-panel {
    position: static !important;
    width: 100% !important;
    margin-top: 1rem !important;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 8px !important;
  }

  .action-buttons {
    grid-template-columns: 1fr !important;
  }
}

/* 高分辨率屏幕优化 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .app-header,
  .camera-section,
  .game-section,
  .control-panel {
    border: 0.5px solid rgba(255, 255, 255, 0.3);
  }
}

/* 暗色模式支持 */
@media (prefers-color-scheme: dark) {
  body {
    background: linear-gradient(135deg, #1a202c 0%, #2d3748 50%, #4a5568 100%);
  }

  .app-header,
  .camera-section,
  .game-section,
  .control-panel {
    background: rgba(45, 55, 72, 0.95) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    color: white !important;
  }

  .app-header h1,
  .app-header p,
  .camera-section h2,
  .game-section h2,
  .panel-section h3 {
    color: white !important;
  }

  .stat-item {
    background: linear-gradient(135deg, #4a5568, #2d3748) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    color: white !important;
  }

  .action-btn {
    background: linear-gradient(135deg, #4a5568, #2d3748) !important;
    color: white !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
  }

  .action-btn:hover {
    background: linear-gradient(135deg, #2d3748, #1a202c) !important;
  }
}

/* 专注模式覆盖层样式 */
.focus-mode-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.95);
  backdrop-filter: blur(10px);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  animation: overlayFadeIn 0.3s ease-out;
}

.focus-mode-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(15px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  position: sticky;
  top: 0;
  z-index: 1001;
}

.focus-mode-header h2 {
  color: white;
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.close-focus-btn {
  background: rgba(239, 68, 68, 0.3);
  border: 1px solid rgba(239, 68, 68, 0.5);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
}

.close-focus-btn:hover {
  background: rgba(239, 68, 68, 0.5);
  transform: translateY(-1px);
}

/* 专注模式内容区域 */
.focus-mode-overlay .focus-mode {
  flex: 1;
  min-height: auto;
  background: transparent;
}

/* 手机端专注模式按钮样式 */
.action-btn.focus-mode-btn {
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-color: rgba(102, 126, 234, 0.5);
}

.action-btn.focus-mode-btn:hover {
  background: linear-gradient(135deg, #5a67d8, #6b46c1);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(102, 126, 234, 0.3);
}

.action-btn.focus-mode-btn.active {
  background: linear-gradient(135deg, #5a67d8, #6b46c1);
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.5);
}

/* 应用监控覆盖层样式 */
.app-monitor-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  animation: overlayFadeIn 0.3s ease-out;
}

@keyframes overlayFadeIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.monitor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(15px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.monitor-header h2 {
  color: #2C5530;
  font-size: 1.5rem;
  margin: 0;
  flex: 1;
}

.close-monitor-btn {
  background: #ef4444;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.close-monitor-btn:hover {
  background: #dc2626;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
}

.close-monitor-btn:active {
  transform: translateY(0);
}

/* 应用监控按钮样式 */
.monitor-btn {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border: 2px solid transparent;
}

.monitor-btn:hover {
  background: linear-gradient(135deg, #2563eb, #1e40af);
  border-color: rgba(59, 130, 246, 0.3);
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.monitor-btn.active {
  background: linear-gradient(135deg, #059669, #047857);
  box-shadow: 0 0 20px rgba(5, 150, 105, 0.4);
}

.monitor-btn.active:hover {
  background: linear-gradient(135deg, #047857, #065f46);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .monitor-header {
    padding: 1rem;
    flex-direction: column;
    gap: 0.5rem;
    text-align: center;
  }
  
  .monitor-header h2 {
    font-size: 1.2rem;
  }
  
  .close-monitor-btn {
    width: 100%;
    max-width: 200px;
  }
}

/* 用户测试覆盖层样式 */
.user-testing-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(8px);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  animation: overlayFadeIn 0.3s ease-out;
}

.testing-header {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  padding: 20px 32px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

.testing-header h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
}

.close-testing-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  padding: 8px 16px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.close-testing-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
}

.close-testing-btn:active {
  transform: scale(0.98);
}

.testing-btn {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
}

.testing-btn:hover {
  background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
}

.testing-btn.active {
  background: linear-gradient(135deg, #1e40af 0%, #1e3a8a 100%);
  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.5);
}

.testing-btn.active:hover {
  background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%);
}

/* 测试计划覆盖层样式 */
.test-plan-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(8px);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  animation: overlayFadeIn 0.3s ease-out;
}

.plan-header {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  padding: 20px 32px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

.plan-header h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
}

.close-plan-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  padding: 8px 16px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.close-plan-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
}

.close-plan-btn:active {
  transform: scale(0.98);
}

.plan-btn {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
}

.plan-btn:hover {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
}

.plan-btn.active {
  background: linear-gradient(135deg, #047857 0%, #065f46 100%);
  box-shadow: 0 4px 16px rgba(16, 185, 129, 0.5);
}

.plan-btn.active:hover {
  background: linear-gradient(135deg, #065f46 0%, #047857 100%);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .testing-header,
  .plan-header {
    padding: 16px 20px;
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .testing-header h2,
  .plan-header h2 {
    font-size: 1.3rem;
  }

  .close-testing-btn,
  .close-plan-btn {
    align-self: flex-end;
    padding: 6px 12px;
    font-size: 0.8rem;
  }
}

/* 反馈分析覆盖层样式 */
.feedback-analysis-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(8px);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  animation: overlayFadeIn 0.3s ease-out;
}

.feedback-analysis-overlay .analysis-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 30px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-bottom: 2px solid rgba(255, 255, 255, 0.2);
  color: white;
}

.feedback-analysis-overlay .analysis-header h2 {
  margin: 0;
  font-size: 1.8rem;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.close-analysis-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  padding: 10px 16px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.close-analysis-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
}

.close-analysis-btn:active {
  transform: translateY(0);
}

.analysis-btn {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.analysis-btn:hover {
  background: linear-gradient(135deg, #5a6fd8, #6a4190);
  transform: translateY(-2px);
}

.analysis-btn.active {
  background: linear-gradient(135deg, #764ba2, #667eea);
  transform: scale(1.05);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.analysis-btn.active:hover {
  background: linear-gradient(135deg, #6a4190, #5a6fd8);
  transform: scale(1.08);
}

.feedback-analysis-overlay .feedback-analysis {
  flex: 1;
  margin: 0;
  border-radius: 0;
  background: white;
  overflow-y: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .feedback-analysis-overlay .analysis-header {
    padding: 16px 20px;
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .feedback-analysis-overlay .analysis-header h2 {
    font-size: 1.3rem;
  }

  .close-analysis-btn {
    align-self: flex-end;
    padding: 6px 12px;
    font-size: 0.8rem;
  }
}

/* 盲盒测试覆盖层样式 */
.lootbox-tester-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: white;
  z-index: 1000;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  animation: overlayFadeIn 0.3s ease-out;
}

.tester-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #e0e0e0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  position: sticky;
  top: 0;
  z-index: 1001;
}

.tester-header h2 {
  margin: 0;
  color: white;
  font-size: 1.5rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.close-tester-btn {
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.2s;
  backdrop-filter: blur(10px);
}

.close-tester-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.close-tester-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* 盲盒按钮样式 */
.lootbox-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.lootbox-btn:hover {
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
}

.lootbox-btn.active {
  background: linear-gradient(135deg, #4c51bf 0%, #553c9a 100%);
  transform: scale(1.05);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.8);
}

.lootbox-btn.active:hover {
  background: linear-gradient(135deg, #4c51bf 0%, #553c9a 100%);
  transform: scale(1.08);
}

/* 响应式适配 */
@media (max-width: 768px) {
  .tester-header {
    padding: 15px;
    flex-direction: column;
    gap: 10px;
    text-align: center;
  }

  .tester-header h2 {
    font-size: 1.3rem;
  }

  .close-tester-btn {
    width: 100%;
    max-width: 200px;
  }
}

/* 盲盒测试内容区域 */
.lootbox-tester-overlay .lootbox-tester {
  flex: 1;
  padding: 0;
  max-width: none;
  margin: 0;
}

/* 确保内容不被头部遮挡 */
.lootbox-tester-overlay .lootbox-tester__header {
  margin-top: 0;
  padding-top: 20px;
}

/* 拖拽工作台样式 */
.synthesis-workbench-modal {
  backdrop-filter: blur(5px);
}

.synthesis-workbench-modal .item-card {
  transition: all 0.3s ease;
}

.synthesis-workbench-modal .item-card:hover {
  transform: scale(1.05) rotate(2deg);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.synthesis-workbench-modal .item-card:active {
  transform: scale(0.95);
  cursor: grabbing !important;
}

.synthesis-slot {
  transition: all 0.3s ease;
  position: relative;
}

.synthesis-slot:hover {
  transform: scale(1.1);
  border-width: 6px !important;
}

.synthesis-slot.has-item {
  animation: synthesis-glow 2s ease-in-out infinite;
}

.synthesis-slot .item-dropped {
  animation: item-drop 0.5s ease-out;
}

.result-slot {
  transition: all 0.3s ease;
  position: relative;
}

.result-slot.has-result {
  animation: result-appear 0.8s ease-out;
}

.synthesize-button {
  position: relative;
  overflow: hidden;
}

.synthesize-button:before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.5s ease;
}

.synthesize-button:hover:before {
  left: 100%;
}

.synthesize-button:disabled {
  transform: none !important;
}

/* 合成动画效果 */
@keyframes synthesis-glow {
  0% { 
    box-shadow: 0 0 5px rgba(251, 191, 36, 0.5); 
    border-color: #d97706;
  }
  50% { 
    box-shadow: 0 0 25px rgba(251, 191, 36, 0.8), 0 0 35px rgba(251, 191, 36, 0.6); 
    border-color: #f59e0b;
  }
  100% { 
    box-shadow: 0 0 5px rgba(251, 191, 36, 0.5); 
    border-color: #d97706;
  }
}

@keyframes item-drop {
  0% { 
    transform: scale(1.2) rotate(5deg); 
    opacity: 0.8; 
  }
  50% { 
    transform: scale(1.1) rotate(-2deg); 
    opacity: 0.9; 
  }
  100% { 
    transform: scale(1) rotate(0deg); 
    opacity: 1; 
  }
}

@keyframes result-appear {
  0% { 
    transform: scale(0) rotate(180deg); 
    opacity: 0; 
  }
  50% { 
    transform: scale(1.3) rotate(90deg); 
    opacity: 0.8; 
  }
  100% { 
    transform: scale(1) rotate(0deg); 
    opacity: 1; 
  }
}

@keyframes magic-particles {
  0% { 
    transform: translateY(0px) scale(1); 
    opacity: 1; 
  }
  50% { 
    transform: translateY(-20px) scale(1.2); 
    opacity: 0.8; 
  }
  100% { 
    transform: translateY(-40px) scale(0.8); 
    opacity: 0; 
  }
}

/* 工作台背景纹理 */
.synthesis-workbench-modal .bg-gradient-to-br {
  background-image: 
    radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.2) 2px, transparent 2px),
    radial-gradient(circle at 75% 75%, rgba(139, 69, 19, 0.1) 1px, transparent 1px);
  background-size: 30px 30px;
}

/* 拖拽提示效果 */
.synthesis-slot:empty:after {
  content: '✨';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 12px;
  opacity: 0.5;
  animation: magic-particles 2s ease-in-out infinite;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .synthesis-workbench-modal .flex {
    flex-direction: column;
  }
  
  .synthesis-workbench-modal .flex-1 {
    width: 100%;
  }
  
  .synthesis-workbench-modal .grid-cols-4 {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* 魔法背包特效 */
@keyframes magical-glow {
  0% { transform: scale(1); opacity: 0.6; }
  100% { transform: scale(1.05); opacity: 1; }
}

@keyframes floating {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes sparkle {
  0%, 100% { opacity: 0; transform: scale(0.5) rotate(0deg); }
  50% { opacity: 1; transform: scale(1) rotate(180deg); }
}

@keyframes pulse-magic {
  0%, 100% { box-shadow: 0 0 20px rgba(138, 43, 226, 0.5); }
  50% { box-shadow: 0 0 40px rgba(138, 43, 226, 0.8), 0 0 60px rgba(138, 43, 226, 0.6); }
}

@keyframes fadeInScale {
  0% { 
    opacity: 0; 
    transform: scale(0.8) translateY(-20px); 
  }
  100% { 
    opacity: 1; 
    transform: scale(1) translateY(0); 
  }
}

/* 魔法背包模态窗口 */
.magical-inventory-modal {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  z-index: 15000 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  padding: 20px !important;
  box-sizing: border-box !important;
}

/* 魔法背包窗口 */
.magical-inventory-window {
  animation: fadeInScale 0.3s ease-out;
  box-shadow: 
    0 25px 50px rgba(0, 0, 0, 0.5),
    0 0 0 1px rgba(255, 255, 255, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  position: relative !important;
  z-index: 15001 !important;
  max-width: 1200px !important;
  max-height: 800px !important;
  width: 90vw !important;
  height: 85vh !important;
  overflow: hidden !important;
  border-radius: 16px !important;
  display: flex !important;
  flex-direction: column !important;
}

/* 确保按钮可点击 */
.magical-inventory-window button {
  pointer-events: auto !important;
  cursor: pointer !important;
  z-index: 15002 !important;
  position: relative !important;
}

/* 确保所有交互元素都可点击 */
.magical-inventory-window .magical-btn,
.magical-inventory-window .tab-buttons button,
.magical-inventory-window .groupby-controls button {
  pointer-events: auto !important;
  cursor: pointer !important;
  z-index: 15002 !important;
  position: relative !important;
}

/* 工作台窗口 */
.synthesis-workbench-modal {
  z-index: 15100 !important;
}

/* 魔法粒子背景 */
.magical-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 1;
}

.magical-particles::before {
  content: '✨';
  position: absolute;
  font-size: 20px;
  animation: sparkle 3s infinite ease-in-out;
  color: rgba(255, 255, 255, 0.8);
  top: 10%;
  left: 10%;
}

.magical-particles::after {
  content: '⭐';
  position: absolute;
  font-size: 16px;
  animation: sparkle 2s infinite ease-in-out 1s;
  color: rgba(255, 255, 255, 0.6);
  top: 70%;
  right: 20%;
}

/* 漂浮的光球 */
.floating-orbs {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.floating-orbs::before {
  content: '';
  position: absolute;
  width: 40px;
  height: 40px;
  background: radial-gradient(circle, rgba(255, 215, 0, 0.3), transparent);
  border-radius: 50%;
  animation: floating 4s infinite ease-in-out;
  top: 20%;
  left: 80%;
}

.floating-orbs::after {
  content: '';
  position: absolute;
  width: 30px;
  height: 30px;
  background: radial-gradient(circle, rgba(138, 43, 226, 0.3), transparent);
  border-radius: 50%;
  animation: floating 3s infinite ease-in-out 1.5s;
  top: 60%;
  left: 15%;
}

/* 魔法物品卡片悬浮效果 */
.magical-item-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.magical-item-card:hover {
  animation: floating 1s ease-in-out infinite;
  z-index: 10;
}

/* 魔法按钮效果 */
.magical-btn {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.magical-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s ease;
}

.magical-btn:hover::before {
  left: 100%;
}

.magical-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
}

/* 魔法网格布局 */
.magical-inventory-grid {
  max-height: 60vh;
  overflow-y: auto;
  padding-right: 8px;
}

.magical-inventory-grid::-webkit-scrollbar {
  width: 6px;
}

.magical-inventory-grid::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
}

.magical-inventory-grid::-webkit-scrollbar-thumb {
  background: linear-gradient(45deg, #667eea, #764ba2);
  border-radius: 10px;
}

.magical-inventory-grid::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(45deg, #764ba2, #667eea);
}

/* 品质头部标签 */
.rarity-header {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  transform: translateY(-3px);
  position: relative;
  font-size: 12px;
}

.rarity-header::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-top: 6px solid currentColor;
  opacity: 0.7;
}

/* 类别头部标签 */
.category-header {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  animation: pulse-magic 3s infinite ease-in-out;
  font-size: 14px;
}

/* 数量徽章发光 */
.quantity-badge {
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

/* 星星闪烁效果 */
.magical-item-card .absolute.top-1.right-1 span {
  animation: sparkle 2s infinite ease-in-out;
  display: inline-block;
}

.magical-item-card .absolute.top-1.right-1 span:nth-child(2) {
  animation-delay: 0.2s;
}

.magical-item-card .absolute.top-1.right-1 span:nth-child(3) {
  animation-delay: 0.4s;
}

.magical-item-card .absolute.top-1.right-1 span:nth-child(4) {
  animation-delay: 0.6s;
}

.magical-item-card .absolute.top-1.right-1 span:nth-child(5) {
  animation-delay: 0.8s;
}

/* 调整魔法网格为居中窗口 */
.magical-grid {
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 12px;
}

.magical-item-card {
  min-height: 140px;
  padding: 8px;
}

.magical-item-card .text-4xl {
  font-size: 2.5rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .magical-inventory-window {
    width: 95vw !important;
    height: 90vh !important;
    margin: 0 !important;
  }
  
  .magical-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
  }
  
  .magical-item-card {
    padding: 6px;
    min-height: 120px;
  }
  
  .magical-item-card .text-4xl {
    font-size: 1.8rem;
  }
  
  .magical-tabs {
    padding: 12px !important;
  }
  
  .action-buttons {
    gap: 8px;
  }
  
  .magical-btn {
    padding: 6px 8px !important;
    font-size: 10px !important;
  }

  .magical-header h2 {
    font-size: 1.3rem;
  }
}

@media (max-width: 480px) {
  .magical-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .magical-header h2 {
    font-size: 1.1rem;
  }
  
  .magical-item-card .text-4xl {
    font-size: 1.5rem;
  }
}

/* 格子式背包库存样式 */
.inventory-grid-container {
  padding: 16px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.inventory-slot {
  position: relative;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.2s ease;
  cursor: pointer;
  min-height: 80px;
  aspect-ratio: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.inventory-slot:hover {
  transform: scale(1.05) translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
  z-index: 10;
}

.inventory-slot .absolute {
  position: absolute;
}

/* 格子发光动画 */
@keyframes slot-glow {
  0% {
    opacity: 0.3;
    transform: scale(0.95);
  }
  100% {
    opacity: 0.6;
    transform: scale(1.05);
  }
}

/* 数量标记样式优化 */
.inventory-slot .quantity-badge {
  position: absolute;
  bottom: 4px;
  right: 4px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: bold;
  line-height: 1;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* 星级评价样式 */
.inventory-slot .stars {
  position: absolute;
  top: 4px;
  left: 4px;
  display: flex;
  gap: 1px;
}

.inventory-slot .stars span {
  color: #ffd700;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  font-size: 10px;
  line-height: 1;
}

/* 响应式网格 */
@media (max-width: 768px) {
  .inventory-grid-container .grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 8px;
  }
  
  .inventory-slot {
    min-height: 70px;
  }
  
  .inventory-slot .text-2xl {
    font-size: 1.25rem;
  }
}

@media (max-width: 480px) {
  .inventory-grid-container .grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 6px;
  }
  
  .inventory-slot {
    min-height: 60px;
    padding: 4px;
  }
  
  .inventory-slot .text-2xl {
    font-size: 1rem;
  }
  
  .inventory-slot .text-xs {
    font-size: 9px;
  }
}

/* 分组标题样式优化 */
.rarity-header, .category-header {
  display: inline-block;
  margin: 0 auto 16px auto;
  font-weight: bold;
  text-align: center;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 空背包提示样式 */
.empty-inventory {
  text-align: center;
  padding: 60px 20px;
  color: #666;
}

.empty-inventory .text-6xl {
  font-size: 4rem;
  margin-bottom: 16px;
  opacity: 0.5;
}

/* 背包内容区域滚动优化 */
.magical-content {
  scrollbar-width: thin;
  scrollbar-color: rgba(138, 43, 226, 0.3) rgba(0, 0, 0, 0.1);
}

.magical-content::-webkit-scrollbar {
  width: 8px;
}

.magical-content::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

.magical-content::-webkit-scrollbar-thumb {
  background: rgba(138, 43, 226, 0.3);
  border-radius: 4px;
}

.magical-content::-webkit-scrollbar-thumb:hover {
  background: rgba(138, 43, 226, 0.5);
} 