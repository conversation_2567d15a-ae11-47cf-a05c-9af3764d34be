import { SynthesisRecipe } from '../types/inventory';
import { ItemRarity, ItemCategory } from '../types/lootbox';

// 品质升级合成配方
export const synthesisRecipes: SynthesisRecipe[] = [
  // 灰色 -> 绿色
  {
    id: 'gray_to_green',
    name: '初级合成',
    description: '将3个灰色物品合成为1个绿色物品',
    requiredItems: [
      {
        rarity: ItemRarity.GRAY,
        quantity: 3
      }
    ],
    resultRarity: ItemRarity.GREEN,
    successRate: 0.95
  },
  // 绿色 -> 蓝色
  {
    id: 'green_to_blue',
    name: '中级合成',
    description: '将3个绿色物品合成为1个蓝色物品',
    requiredItems: [
      {
        rarity: ItemRarity.GREEN,
        quantity: 3
      }
    ],
    resultRarity: ItemRarity.BLUE,
    successRate: 0.90
  },
  // 蓝色 -> 橙色
  {
    id: 'blue_to_orange',
    name: '高级合成',
    description: '将3个蓝色物品合成为1个橙色物品',
    requiredItems: [
      {
        rarity: ItemRarity.BLUE,
        quantity: 3
      }
    ],
    resultRarity: ItemRarity.ORANGE,
    successRate: 0.85
  },
  // 橙色 -> 金色
  {
    id: 'orange_to_gold',
    name: '精英合成',
    description: '将3个橙色物品合成为1个金色物品',
    requiredItems: [
      {
        rarity: ItemRarity.ORANGE,
        quantity: 3
      }
    ],
    resultRarity: ItemRarity.GOLD,
    successRate: 0.75
  },
  // 金色 -> 金红色
  {
    id: 'gold_to_golden_red',
    name: '传奇合成',
    description: '将5个金色物品合成为1个金红色物品',
    requiredItems: [
      {
        rarity: ItemRarity.GOLD,
        quantity: 5
      }
    ],
    resultRarity: ItemRarity.GOLD_RED,
    successRate: 0.60
  },
  // 跨类别合成（农产品 + 工业品）
  {
    id: 'cross_category_green',
    name: '产业融合（绿色）',
    description: '将2个灰色农产品和2个灰色工业品合成为1个绿色物品',
    requiredItems: [
      {
        rarity: ItemRarity.GRAY,
        quantity: 2,
        category: ItemCategory.AGRICULTURAL
      },
      {
        rarity: ItemRarity.GRAY,
        quantity: 2,
        category: ItemCategory.INDUSTRIAL
      }
    ],
    resultRarity: ItemRarity.GREEN,
    successRate: 0.85
  },
  {
    id: 'cross_category_blue',
    name: '产业融合（蓝色）',
    description: '将2个绿色农产品和2个绿色工业品合成为1个蓝色物品',
    requiredItems: [
      {
        rarity: ItemRarity.GREEN,
        quantity: 2,
        category: ItemCategory.AGRICULTURAL
      },
      {
        rarity: ItemRarity.GREEN,
        quantity: 2,
        category: ItemCategory.INDUSTRIAL
      }
    ],
    resultRarity: ItemRarity.BLUE,
    successRate: 0.80
  },
  // 同类别强化合成
  {
    id: 'agricultural_boost',
    name: '农业强化',
    description: '将4个相同品质的农产品合成为高一级品质的农产品',
    requiredItems: [
      {
        rarity: ItemRarity.GREEN,
        quantity: 4,
        category: ItemCategory.AGRICULTURAL
      }
    ],
    resultRarity: ItemRarity.BLUE,
    successRate: 0.88
  },
  {
    id: 'industrial_boost',
    name: '工业强化',
    description: '将4个相同品质的工业品合成为高一级品质的工业品',
    requiredItems: [
      {
        rarity: ItemRarity.GREEN,
        quantity: 4,
        category: ItemCategory.INDUSTRIAL
      }
    ],
    resultRarity: ItemRarity.BLUE,
    successRate: 0.88
  }
];

// 根据品质和类别获取可用的合成配方
export function getAvailableRecipes(
  items: { rarity: string; category: string; quantity: number }[]
): SynthesisRecipe[] {
  return synthesisRecipes.filter(recipe => {
    return recipe.requiredItems.every(requirement => {
      const matchingItems = items.filter(item => {
        const rarityMatch = item.rarity === requirement.rarity;
        const categoryMatch = !requirement.category || item.category === requirement.category;
        return rarityMatch && categoryMatch;
      });
      
      const totalQuantity = matchingItems.reduce((sum, item) => sum + item.quantity, 0);
      return totalQuantity >= requirement.quantity;
    });
  });
} 