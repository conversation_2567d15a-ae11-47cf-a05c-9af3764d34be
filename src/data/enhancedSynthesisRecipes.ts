import { ItemRarity, ItemCategory, ItemType } from '../types/lootbox'
import { SynthesisRecipe } from '../types/inventory'

// 增强合成配方：2个物品合成1个高品质物品（可以是不同类型）
export interface EnhancedSynthesisRecipe extends SynthesisRecipe {
  // 可能的产出物品类型（随机选择一个）
  possibleResults: {
    type: ItemType
    category: ItemCategory
    probability: number  // 概率权重
    namePattern: string  // 名称模板
    icon: string
    valueMultiplier: number  // 价值倍数
  }[]
  
  // 品质变换类型
  qualityUpgrade: 'next_level' | 'skip_level' | 'same_level'
  
  // 是否允许跨类别合成
  allowCrossCategorySynthesis: boolean
}

// 基础合成配方：普通→优质（同类型合成）
export const ENHANCED_SYNTHESIS_RECIPES: EnhancedSynthesisRecipe[] = [
  
  // ==================== 普通品质合成 ====================
  {
    id: 'basic_gray_synthesis',
    name: '基础合成',
    description: '将2个普通物品合成为1个优质物品',
    requiredItems: [
      {
        rarity: ItemRarity.GRAY,
        quantity: 2
      }
    ],
    resultRarity: ItemRarity.GREEN,
    successRate: 0.95,
    qualityUpgrade: 'next_level',
    allowCrossCategorySynthesis: false,
    possibleResults: [
      // 只有农产品结果，保持同类型
      {
        type: ItemType.SEED,
        category: ItemCategory.AGRICULTURAL,
        probability: 0.30,
        namePattern: '优质{variety}种子',
        icon: '🌱',
        valueMultiplier: 2.2
      },
      {
        type: ItemType.CROP,
        category: ItemCategory.AGRICULTURAL,
        probability: 0.40,
        namePattern: '优质{variety}',
        icon: '🌾',
        valueMultiplier: 2.5
      },
      {
        type: ItemType.LIVESTOCK,
        category: ItemCategory.AGRICULTURAL,
        probability: 0.20,
        namePattern: '优质{variety}',
        icon: '🐄',
        valueMultiplier: 3.0
      },
      {
        type: ItemType.FARM_TOOL,
        category: ItemCategory.AGRICULTURAL,
        probability: 0.10,
        namePattern: '改良{variety}农具',
        icon: '🛠️',
        valueMultiplier: 2.8
      }
    ]
  },

  // ==================== 优质品质合成 ====================
  {
    id: 'advanced_green_synthesis',
    name: '进阶合成',
    description: '将2个优质物品合成为1个稀有物品',
    requiredItems: [
      {
        rarity: ItemRarity.GREEN,
        quantity: 2
      }
    ],
    resultRarity: ItemRarity.BLUE,
    successRate: 0.90,
    qualityUpgrade: 'next_level',
    allowCrossCategorySynthesis: false,
    possibleResults: [
      // 只有农产品结果，保持同类型
      {
        type: ItemType.SEED,
        category: ItemCategory.AGRICULTURAL,
        probability: 0.25,
        namePattern: '稀有{variety}种子',
        icon: '🌟',
        valueMultiplier: 3.0
      },
      {
        type: ItemType.CROP,
        category: ItemCategory.AGRICULTURAL,
        probability: 0.35,
        namePattern: '稀有{variety}',
        icon: '💎',
        valueMultiplier: 3.5
      },
      {
        type: ItemType.LIVESTOCK,
        category: ItemCategory.AGRICULTURAL,
        probability: 0.25,
        namePattern: '珍稀{variety}',
        icon: '🦄',
        valueMultiplier: 4.0
      },
      {
        type: ItemType.FARM_TOOL,
        category: ItemCategory.AGRICULTURAL,
        probability: 0.15,
        namePattern: '精良{variety}农具',
        icon: '🔨',
        valueMultiplier: 4.2
      }
    ]
  },

  // ==================== 稀有品质合成 ====================
  {
    id: 'rare_blue_synthesis',
    name: '稀有合成',
    description: '将2个稀有物品合成为1个史诗物品',
    requiredItems: [
      {
        rarity: ItemRarity.BLUE,
        quantity: 2
      }
    ],
    resultRarity: ItemRarity.ORANGE,
    successRate: 0.85,
    qualityUpgrade: 'next_level',
    allowCrossCategorySynthesis: false,
    possibleResults: [
      // 只有农产品结果，保持同类型
      {
        type: ItemType.CROP,
        category: ItemCategory.AGRICULTURAL,
        probability: 0.40,
        namePattern: '史诗{variety}',
        icon: '🔥',
        valueMultiplier: 5.0
      },
      {
        type: ItemType.LIVESTOCK,
        category: ItemCategory.AGRICULTURAL,
        probability: 0.25,
        namePattern: '神兽{variety}',
        icon: '🐉',
        valueMultiplier: 6.0
      },
      {
        type: ItemType.SEED,
        category: ItemCategory.AGRICULTURAL,
        probability: 0.20,
        namePattern: '史诗{variety}种子',
        icon: '⚡',
        valueMultiplier: 5.5
      },
      {
        type: ItemType.FARM_TOOL,
        category: ItemCategory.AGRICULTURAL,
        probability: 0.15,
        namePattern: '史诗{variety}农具',
        icon: '✨',
        valueMultiplier: 5.8
      }
    ]
  },

  // ==================== 史诗品质合成 ====================
  {
    id: 'epic_orange_synthesis',
    name: '史诗合成',
    description: '将2个史诗物品合成为1个传说物品',
    requiredItems: [
      {
        rarity: ItemRarity.ORANGE,
        quantity: 2
      }
    ],
    resultRarity: ItemRarity.GOLD,
    successRate: 0.75,
    qualityUpgrade: 'next_level',
    allowCrossCategorySynthesis: false,
    possibleResults: [
      // 只有农产品结果，保持同类型
      {
        type: ItemType.CROP,
        category: ItemCategory.AGRICULTURAL,
        probability: 0.45,
        namePattern: '传说{variety}',
        icon: '👑',
        valueMultiplier: 8.0
      },
      {
        type: ItemType.LIVESTOCK,
        category: ItemCategory.AGRICULTURAL,
        probability: 0.25,
        namePattern: '圣兽{variety}',
        icon: '🦅',
        valueMultiplier: 10.0
      },
      {
        type: ItemType.SEED,
        category: ItemCategory.AGRICULTURAL,
        probability: 0.20,
        namePattern: '传说{variety}种子',
        icon: '🌟',
        valueMultiplier: 9.0
      },
      {
        type: ItemType.FARM_TOOL,
        category: ItemCategory.AGRICULTURAL,
        probability: 0.10,
        namePattern: '传说{variety}神器',
        icon: '🏆',
        valueMultiplier: 12.0
      }
    ]
  },

  // ==================== 传说品质合成 ====================
  {
    id: 'legendary_gold_synthesis',
    name: '传奇合成',
    description: '将2个传说物品合成为1个神话物品',
    requiredItems: [
      {
        rarity: ItemRarity.GOLD,
        quantity: 2
      }
    ],
    resultRarity: ItemRarity.GOLD_RED,
    successRate: 0.60,
    qualityUpgrade: 'next_level',
    allowCrossCategorySynthesis: false,
    possibleResults: [
      // 只有农产品结果，保持同类型
      {
        type: ItemType.CROP,
        category: ItemCategory.AGRICULTURAL,
        probability: 0.50,
        namePattern: '神话{variety}',
        icon: '🌈',
        valueMultiplier: 15.0
      },
      {
        type: ItemType.LIVESTOCK,
        category: ItemCategory.AGRICULTURAL,
        probability: 0.30,
        namePattern: '神兽{variety}',
        icon: '🔮',
        valueMultiplier: 18.0
      },
      {
        type: ItemType.SEED,
        category: ItemCategory.AGRICULTURAL,
        probability: 0.15,
        namePattern: '神话{variety}种子',
        icon: '☀️',
        valueMultiplier: 20.0
      },
      {
        type: ItemType.FARM_TOOL,
        category: ItemCategory.AGRICULTURAL,
        probability: 0.05,
        namePattern: '神话{variety}圣器',
        icon: '🌟',
        valueMultiplier: 25.0
      }
    ]
  },


]

// 根据输入物品获取可用的合成配方
export function getEnhancedSynthesisRecipes(inputItems: { rarity: ItemRarity; category: ItemCategory }[]): EnhancedSynthesisRecipe[] {
  if (inputItems.length !== 2) return []
  
  return ENHANCED_SYNTHESIS_RECIPES.filter(recipe => {
    // 只支持两个相同品质、相同类别的物品合成
    const requirement = recipe.requiredItems[0]
    return inputItems.every(item => 
      item.rarity === requirement.rarity && 
      item.category === ItemCategory.AGRICULTURAL // 只允许农产品合成
    )
  })
}

// 从可能的结果中随机选择一个
export function selectRandomResult(recipe: EnhancedSynthesisRecipe): EnhancedSynthesisRecipe['possibleResults'][0] {
  const totalProbability = recipe.possibleResults.reduce((sum, result) => sum + result.probability, 0)
  let random = Math.random() * totalProbability
  
  for (const result of recipe.possibleResults) {
    random -= result.probability
    if (random <= 0) {
      return result
    }
  }
  
  // fallback到第一个结果
  return recipe.possibleResults[0]
}

// 生成结果物品名称
export function generateResultItemName(selectedResult: EnhancedSynthesisRecipe['possibleResults'][0], inputItems: any[]): string {
  // 从输入物品中提取品种信息
  const varieties = inputItems.map(item => extractVariety(item.name))
  const primaryVariety = varieties[0] || '神秘'
  
  return selectedResult.namePattern.replace('{variety}', primaryVariety)
}

// 从物品名称中提取品种
function extractVariety(itemName: string): string {
  // 移除品质前缀
  const withoutQuality = itemName.replace(/^(普通|优质|稀有|史诗|传说|神话)/, '')
  // 移除后缀
  const variety = withoutQuality.replace(/(种子|农具|材料|设备|药剂|精华|神水)$/, '')
  return variety || '神秘'
} 