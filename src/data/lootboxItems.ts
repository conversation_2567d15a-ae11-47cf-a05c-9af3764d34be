import { LootboxItem, ItemRarity, ItemCategory, ItemType } from '../types/lootbox'
import { FuturesType } from '../types/futures'

// 农产品盲盒物品
export const AGRICULTURAL_ITEMS: Record<string, LootboxItem> = {
  // 种子类 - 灰色普通
  'wheat_seed_gray': {
    id: 'wheat_seed_gray',
    name: '普通小麦种子',
    description: '品质普通的小麦种子，适合新手种植',
    category: ItemCategory.AGRICULTURAL,
    type: ItemType.SEED,
    rarity: ItemRarity.GRAY,
    icon: '🌱',
    value: 10,
    stackable: true,
    tradeable: true,
    synthesizable: true,
    metadata: {
      yieldMultiplier: 1.0,
      growthSpeed: 1.0,
      qualityBonus: 0,
      futuresPrice: 2500,
      priceVolatility: 0.25,
      marketDemand: 0.7
    }
  },
  'corn_seed_gray': {
    id: 'corn_seed_gray',
    name: '普通玉米种子',
    description: '常见的玉米种子，产量稳定',
    category: ItemCategory.AGRICULTURAL,
    type: ItemType.SEED,
    rarity: ItemRarity.GRAY,
    icon: '🌽',
    value: 8,
    stackable: true,
    tradeable: true,
    synthesizable: true,
    metadata: {
      yieldMultiplier: 1.0,
      growthSpeed: 1.1,
      qualityBonus: 0,
      futuresPrice: 2800,
      priceVolatility: 0.22,
      marketDemand: 0.8
    }
  },

  // 种子类 - 绿色优秀
  'wheat_seed_green': {
    id: 'wheat_seed_green',
    name: '优质小麦种子',
    description: '经过改良的小麦种子，产量更高',
    category: ItemCategory.AGRICULTURAL,
    type: ItemType.SEED,
    rarity: ItemRarity.GREEN,
    icon: '🌾',
    value: 25,
    stackable: true,
    tradeable: true,
    synthesizable: true,
    metadata: {
      yieldMultiplier: 1.2,
      growthSpeed: 1.0,
      qualityBonus: 10,
      futuresPrice: 2600,
      priceVolatility: 0.25,
      marketDemand: 0.75,
      synthesisRecipes: ['wheat_upgrade']
    }
  },

  // 种子类 - 蓝色稀有
  'premium_wheat_seed': {
    id: 'premium_wheat_seed',
    name: '强筋小麦种子',
    description: '高品质强筋小麦种子，制作面包的首选',
    category: ItemCategory.AGRICULTURAL,
    type: ItemType.SEED,
    rarity: ItemRarity.BLUE,
    icon: '🌾',
    value: 60,
    stackable: true,
    tradeable: true,
    synthesizable: true,
    metadata: {
      yieldMultiplier: 1.5,
      growthSpeed: 0.9,
      qualityBonus: 25,
      futuresPrice: 2800,
      priceVolatility: 0.3,
      marketDemand: 0.9
    }
  },

  // 农具类 - 灰色到蓝色
  'basic_hoe': {
    id: 'basic_hoe',
    name: '基础锄头',
    description: '简单的农具，用于翻土播种',
    category: ItemCategory.AGRICULTURAL,
    type: ItemType.FARM_TOOL,
    rarity: ItemRarity.GRAY,
    icon: '🥄',
    value: 15,
    stackable: false,
    tradeable: true,
    synthesizable: true,
    metadata: {
      efficiency: 1.0,
      effects: ['basic_farming']
    }
  },
  'iron_plow': {
    id: 'iron_plow',
    name: '铁制犁',
    description: '坚固的铁制犁，大幅提高耕作效率',
    category: ItemCategory.AGRICULTURAL,
    type: ItemType.FARM_TOOL,
    rarity: ItemRarity.GREEN,
    icon: '🚜',
    value: 45,
    stackable: false,
    tradeable: true,
    synthesizable: true,
    metadata: {
      efficiency: 1.5,
      effects: ['efficient_farming', 'soil_improvement']
    }
  },

  // 橙色史诗级
  'golden_wheat_seed': {
    id: 'golden_wheat_seed',
    name: '黄金小麦种子',
    description: '传说中的黄金小麦种子，据说能结出金色的麦穗',
    category: ItemCategory.AGRICULTURAL,
    type: ItemType.SEED,
    rarity: ItemRarity.ORANGE,
    icon: '🏆',
    value: 200,
    stackable: true,
    tradeable: false,
    synthesizable: true,
    metadata: {
      yieldMultiplier: 2.5,
      growthSpeed: 0.8,
      qualityBonus: 50,
      futuresPrice: 3500,
      priceVolatility: 0.4,
      marketDemand: 1.0,
      effects: ['golden_harvest', 'luck_boost']
    }
  },

  // 金色传说级
  'legendary_farm_tractor': {
    id: 'legendary_farm_tractor',
    name: '传说拖拉机',
    description: '智能化农业拖拉机，能够自动化完成各种农作业',
    category: ItemCategory.AGRICULTURAL,
    type: ItemType.FARM_TOOL,
    rarity: ItemRarity.GOLD,
    icon: '🚛',
    value: 1000,
    stackable: false,
    tradeable: false,
    synthesizable: false,
    metadata: {
      efficiency: 5.0,
      effects: ['auto_farming', 'smart_irrigation', 'yield_optimization'],
      capacity: 100
    }
  },

  // 金红色神话级
  'mythical_harvest_altar': {
    id: 'mythical_harvest_altar',
    name: '神话丰收祭坛',
    description: '古老的丰收祭坛，能够祝福整个农场',
    category: ItemCategory.AGRICULTURAL,
    type: ItemType.FARM_BUILDING,
    rarity: ItemRarity.GOLD_RED,
    icon: '⛩️',
    value: 5000,
    stackable: false,
    tradeable: false,
    synthesizable: false,
    metadata: {
      efficiency: 10.0,
      effects: ['divine_blessing', 'weather_control', 'time_acceleration', 'mythical_yield'],
      capacity: 1000
    }
  }
}

// 工业品盲盒物品
export const INDUSTRIAL_ITEMS: Record<string, LootboxItem> = {
  // 原材料 - 灰色
  'iron_ore': {
    id: 'iron_ore',
    name: '铁矿石',
    description: '工业生产的基础原材料',
    category: ItemCategory.INDUSTRIAL,
    type: ItemType.RAW_MATERIAL,
    rarity: ItemRarity.GRAY,
    icon: '⚫',
    value: 5,
    stackable: true,
    tradeable: true,
    synthesizable: true,
    metadata: {
      productionSpeed: 1.0
    }
  },

  // 组件 - 绿色
  'steel_gear': {
    id: 'steel_gear',
    name: '钢制齿轮',
    description: '精密的钢制齿轮，机械设备的重要组件',
    category: ItemCategory.INDUSTRIAL,
    type: ItemType.COMPONENT,
    rarity: ItemRarity.GREEN,
    icon: '⚙️',
    value: 30,
    stackable: true,
    tradeable: true,
    synthesizable: true,
    metadata: {
      productionSpeed: 1.3,
      efficiency: 1.2
    }
  },

  // 机械 - 蓝色
  'processing_machine': {
    id: 'processing_machine',
    name: '加工机械',
    description: '自动化农产品加工机械',
    category: ItemCategory.INDUSTRIAL,
    type: ItemType.MACHINERY,
    rarity: ItemRarity.BLUE,
    icon: '🏭',
    value: 150,
    stackable: false,
    tradeable: true,
    synthesizable: true,
    metadata: {
      productionSpeed: 2.0,
      efficiency: 1.8,
      capacity: 50
    }
  },

  // 橙色工厂设备
  'smart_factory_line': {
    id: 'smart_factory_line',
    name: '智能生产线',
    description: '全自动化智能生产线，效率极高',
    category: ItemCategory.INDUSTRIAL,
    type: ItemType.MACHINERY,
    rarity: ItemRarity.ORANGE,
    icon: '🤖',
    value: 800,
    stackable: false,
    tradeable: false,
    synthesizable: true,
    metadata: {
      productionSpeed: 5.0,
      efficiency: 4.0,
      capacity: 200,
      effects: ['ai_optimization', 'quality_control']
    }
  },

  // 金色传说
  'mega_factory_complex': {
    id: 'mega_factory_complex',
    name: '超级工厂综合体',
    description: '巨型工业综合体，能够处理整个产业链',
    category: ItemCategory.INDUSTRIAL,
    type: ItemType.FACTORY_BUILDING,
    rarity: ItemRarity.GOLD,
    icon: '🏢',
    value: 3000,
    stackable: false,
    tradeable: false,
    synthesizable: false,
    metadata: {
      productionSpeed: 10.0,
      efficiency: 8.0,
      capacity: 1000,
      effects: ['full_automation', 'supply_chain_optimization', 'global_network']
    }
  }
}

// 通用物品
export const COMMON_ITEMS: Record<string, LootboxItem> = {
  // 货币类
  'focus_coin_small': {
    id: 'focus_coin_small',
    name: '专注币包(小)',
    description: '包含50个专注币',
    category: ItemCategory.AGRICULTURAL,
    type: ItemType.CURRENCY,
    rarity: ItemRarity.GRAY,
    icon: '💰',
    value: 50,
    stackable: true,
    tradeable: false,
    synthesizable: false,
    metadata: {}
  },
  'focus_coin_large': {
    id: 'focus_coin_large',
    name: '专注币包(大)',
    description: '包含500个专注币',
    category: ItemCategory.AGRICULTURAL,
    type: ItemType.CURRENCY,
    rarity: ItemRarity.BLUE,
    icon: '💎',
    value: 500,
    stackable: true,
    tradeable: false,
    synthesizable: false,
    metadata: {}
  },

  // 增益道具
  'growth_booster': {
    id: 'growth_booster',
    name: '生长加速剂',
    description: '临时加速作物生长，持续2小时',
    category: ItemCategory.AGRICULTURAL,
    type: ItemType.BOOST,
    rarity: ItemRarity.GREEN,
    icon: '🧪',
    value: 80,
    stackable: true,
    tradeable: true,
    synthesizable: true,
    metadata: {
      effects: ['growth_speed_x2'],
      duration: 2 * 60 * 60 * 1000 // 2小时
    }
  }
}

// 合并所有物品
export const ALL_LOOTBOX_ITEMS: Record<string, LootboxItem> = {
  ...AGRICULTURAL_ITEMS,
  ...INDUSTRIAL_ITEMS,
  ...COMMON_ITEMS
} 