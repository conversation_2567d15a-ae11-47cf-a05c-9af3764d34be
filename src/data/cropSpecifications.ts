import { CropType } from '../types/crop'

// 自律行为类型枚举
export enum SelfDisciplineType {
  LEARNING = 'learning',           // 学习活动
  EXERCISE = 'exercise',           // 体力锻炼
  TIME_MANAGEMENT = 'time_management', // 时间管理
  MEDITATION = 'meditation',       // 冥想正念
  DEEP_FOCUS = 'deep_focus',      // 深度专注
  READING = 'reading',            // 阅读习惯
  SOCIAL_INTERACTION = 'social_interaction' // 社交互动
}

// 行为检测标准接口
export interface BehaviorDetectionCriteria {
  type: SelfDisciplineType
  minimumDuration: number         // 最小持续时间（毫秒）
  focusScoreThreshold: number     // 专注度阈值
  sessionQualityMetrics: {
    consistencyWeight: number     // 一致性权重 (0-1)
    intensityWeight: number       // 强度权重 (0-1)
    durationWeight: number        // 持续时间权重 (0-1)
  }
  specificRequirements?: {
    minBreaksBetweenSessions?: number  // 会话间最小休息时间
    maxSessionsPerDay?: number         // 每日最大会话数
    requiredTimeOfDay?: 'morning' | 'afternoon' | 'evening' | 'any' // 推荐时间段
  }
}

// 作物检测配置接口
export interface CropDetectionConfig {
  cropType: CropType
  behaviorType: SelfDisciplineType
  detectionCriteria: BehaviorDetectionCriteria
  growthModifiers: {
    baseMultiplier: number        // 基础成长倍数
    qualityBonus: number          // 品质加成
    streakBonus: number           // 连续达成加成
    timeOfDayBonus?: number       // 时间段加成
  }
  balanceFactors: {
    difficulty: 'easy' | 'medium' | 'hard' | 'expert' // 难度等级
    rewardRatio: number           // 奖励比例
    unlockLevel: number           // 解锁等级
    dependencyComplexity: number  // 依赖复杂度 (1-5)
  }
}

// 预定义作物检测配置
export const CROP_DETECTION_CONFIGS: Record<CropType, CropDetectionConfig> = {
  [CropType.KNOWLEDGE_FLOWER]: {
    cropType: CropType.KNOWLEDGE_FLOWER,
    behaviorType: SelfDisciplineType.LEARNING,
    detectionCriteria: {
      type: SelfDisciplineType.LEARNING,
      minimumDuration: 15 * 60 * 1000, // 15分钟
      focusScoreThreshold: 70,
      sessionQualityMetrics: {
        consistencyWeight: 0.4,
        intensityWeight: 0.3,
        durationWeight: 0.3
      },
      specificRequirements: {
        minBreaksBetweenSessions: 5 * 60 * 1000, // 5分钟休息
        maxSessionsPerDay: 8,
        requiredTimeOfDay: 'any'
      }
    },
    growthModifiers: {
      baseMultiplier: 1.0,
      qualityBonus: 0.2,
      streakBonus: 0.15,
      timeOfDayBonus: 0.1
    },
    balanceFactors: {
      difficulty: 'easy',
      rewardRatio: 1.0,
      unlockLevel: 1,
      dependencyComplexity: 1
    }
  },

  [CropType.STRENGTH_TREE]: {
    cropType: CropType.STRENGTH_TREE,
    behaviorType: SelfDisciplineType.EXERCISE,
    detectionCriteria: {
      type: SelfDisciplineType.EXERCISE,
      minimumDuration: 20 * 60 * 1000, // 20分钟
      focusScoreThreshold: 60,
      sessionQualityMetrics: {
        consistencyWeight: 0.5,
        intensityWeight: 0.4,
        durationWeight: 0.1
      },
      specificRequirements: {
        minBreaksBetweenSessions: 2 * 60 * 60 * 1000, // 2小时休息
        maxSessionsPerDay: 3,
        requiredTimeOfDay: 'morning'
      }
    },
    growthModifiers: {
      baseMultiplier: 0.8,
      qualityBonus: 0.25,
      streakBonus: 0.2,
      timeOfDayBonus: 0.15
    },
    balanceFactors: {
      difficulty: 'medium',
      rewardRatio: 1.2,
      unlockLevel: 3,
      dependencyComplexity: 2
    }
  },

  [CropType.TIME_VEGGIE]: {
    cropType: CropType.TIME_VEGGIE,
    behaviorType: SelfDisciplineType.TIME_MANAGEMENT,
    detectionCriteria: {
      type: SelfDisciplineType.TIME_MANAGEMENT,
      minimumDuration: 25 * 60 * 1000, // 25分钟 (番茄工作法)
      focusScoreThreshold: 80,
      sessionQualityMetrics: {
        consistencyWeight: 0.6,
        intensityWeight: 0.2,
        durationWeight: 0.2
      },
      specificRequirements: {
        minBreaksBetweenSessions: 5 * 60 * 1000, // 5分钟休息
        maxSessionsPerDay: 12,
        requiredTimeOfDay: 'any'
      }
    },
    growthModifiers: {
      baseMultiplier: 1.3,
      qualityBonus: 0.3,
      streakBonus: 0.25,
      timeOfDayBonus: 0.05
    },
    balanceFactors: {
      difficulty: 'medium',
      rewardRatio: 0.8,
      unlockLevel: 2,
      dependencyComplexity: 2
    }
  },

  [CropType.MEDITATION_LOTUS]: {
    cropType: CropType.MEDITATION_LOTUS,
    behaviorType: SelfDisciplineType.MEDITATION,
    detectionCriteria: {
      type: SelfDisciplineType.MEDITATION,
      minimumDuration: 10 * 60 * 1000, // 10分钟
      focusScoreThreshold: 85,
      sessionQualityMetrics: {
        consistencyWeight: 0.7,
        intensityWeight: 0.2,
        durationWeight: 0.1
      },
      specificRequirements: {
        minBreaksBetweenSessions: 30 * 60 * 1000, // 30分钟休息
        maxSessionsPerDay: 4,
        requiredTimeOfDay: 'morning'
      }
    },
    growthModifiers: {
      baseMultiplier: 0.6,
      qualityBonus: 0.4,
      streakBonus: 0.35,
      timeOfDayBonus: 0.2
    },
    balanceFactors: {
      difficulty: 'hard',
      rewardRatio: 1.5,
      unlockLevel: 5,
      dependencyComplexity: 3
    }
  },

  [CropType.FOCUS_FLOWER]: {
    cropType: CropType.FOCUS_FLOWER,
    behaviorType: SelfDisciplineType.DEEP_FOCUS,
    detectionCriteria: {
      type: SelfDisciplineType.DEEP_FOCUS,
      minimumDuration: 45 * 60 * 1000, // 45分钟
      focusScoreThreshold: 90,
      sessionQualityMetrics: {
        consistencyWeight: 0.8,
        intensityWeight: 0.15,
        durationWeight: 0.05
      },
      specificRequirements: {
        minBreaksBetweenSessions: 15 * 60 * 1000, // 15分钟休息
        maxSessionsPerDay: 4,
        requiredTimeOfDay: 'any'
      }
    },
    growthModifiers: {
      baseMultiplier: 0.4,
      qualityBonus: 0.5,
      streakBonus: 0.4,
      timeOfDayBonus: 0.1
    },
    balanceFactors: {
      difficulty: 'expert',
      rewardRatio: 1.8,
      unlockLevel: 4,
      dependencyComplexity: 4
    }
  },

  [CropType.READING_VINE]: {
    cropType: CropType.READING_VINE,
    behaviorType: SelfDisciplineType.READING,
    detectionCriteria: {
      type: SelfDisciplineType.READING,
      minimumDuration: 30 * 60 * 1000, // 30分钟
      focusScoreThreshold: 75,
      sessionQualityMetrics: {
        consistencyWeight: 0.5,
        intensityWeight: 0.3,
        durationWeight: 0.2
      },
      specificRequirements: {
        minBreaksBetweenSessions: 10 * 60 * 1000, // 10分钟休息
        maxSessionsPerDay: 6,
        requiredTimeOfDay: 'any'
      }
    },
    growthModifiers: {
      baseMultiplier: 0.9,
      qualityBonus: 0.3,
      streakBonus: 0.25,
      timeOfDayBonus: 0.15
    },
    balanceFactors: {
      difficulty: 'medium',
      rewardRatio: 1.3,
      unlockLevel: 3,
      dependencyComplexity: 2
    }
  },

  [CropType.SOCIAL_FRUIT]: {
    cropType: CropType.SOCIAL_FRUIT,
    behaviorType: SelfDisciplineType.SOCIAL_INTERACTION,
    detectionCriteria: {
      type: SelfDisciplineType.SOCIAL_INTERACTION,
      minimumDuration: 20 * 60 * 1000, // 20分钟
      focusScoreThreshold: 65,
      sessionQualityMetrics: {
        consistencyWeight: 0.3,
        intensityWeight: 0.4,
        durationWeight: 0.3
      },
      specificRequirements: {
        minBreaksBetweenSessions: 60 * 60 * 1000, // 1小时休息
        maxSessionsPerDay: 5,
        requiredTimeOfDay: 'any'
      }
    },
    growthModifiers: {
      baseMultiplier: 1.1,
      qualityBonus: 0.2,
      streakBonus: 0.15,
      timeOfDayBonus: 0.1
    },
    balanceFactors: {
      difficulty: 'easy',
      rewardRatio: 1.0,
      unlockLevel: 2,
      dependencyComplexity: 2
    }
  }
}

// 平衡性分析工具
export class CropBalanceAnalyzer {
  // 计算作物的总体难度评分
  static calculateDifficultyScore(config: CropDetectionConfig): number {
    const criteria = config.detectionCriteria
    const balance = config.balanceFactors
    
    // 基础难度分数
    let score = 0
    
    // 时间要求影响
    const durationHours = criteria.minimumDuration / (60 * 60 * 1000)
    score += durationHours * 10
    
    // 专注度阈值影响
    score += (criteria.focusScoreThreshold - 50) / 5
    
    // 一致性要求影响
    score += criteria.sessionQualityMetrics.consistencyWeight * 20
    
    // 特殊要求影响
    if (criteria.specificRequirements) {
      if (criteria.specificRequirements.maxSessionsPerDay && criteria.specificRequirements.maxSessionsPerDay < 5) {
        score += 5
      }
      if (criteria.specificRequirements.requiredTimeOfDay !== 'any') {
        score += 3
      }
    }
    
    // 解锁等级影响
    score += balance.unlockLevel * 2
    
    return Math.round(score)
  }
  
  // 计算作物的奖励效率比
  static calculateRewardEfficiency(config: CropDetectionConfig): number {
    const difficultyScore = this.calculateDifficultyScore(config)
    const rewardRatio = config.balanceFactors.rewardRatio
    const baseMultiplier = config.growthModifiers.baseMultiplier
    
    return (rewardRatio * baseMultiplier) / (difficultyScore / 10)
  }
  
  // 获取平衡性建议
  static getBalanceRecommendations(): {
    cropType: CropType
    difficulty: number
    efficiency: number
    recommendation: string
  }[] {
    return Object.values(CROP_DETECTION_CONFIGS).map(config => {
      const difficulty = this.calculateDifficultyScore(config)
      const efficiency = this.calculateRewardEfficiency(config)
      
      let recommendation = ''
      if (efficiency < 0.8) {
        recommendation = '奖励可能偏低，建议增加奖励或降低难度'
      } else if (efficiency > 1.5) {
        recommendation = '奖励可能偏高，建议降低奖励或增加难度'
      } else {
        recommendation = '平衡性良好'
      }
      
      return {
        cropType: config.cropType,
        difficulty,
        efficiency: Math.round(efficiency * 100) / 100,
        recommendation
      }
    })
  }
}

// 导出工具函数
export const getCropDetectionConfig = (cropType: CropType): CropDetectionConfig => {
  return CROP_DETECTION_CONFIGS[cropType]
}

export const getAllCropTypes = (): CropType[] => {
  return Object.values(CropType)
}

export const getCropsByDifficulty = (difficulty: 'easy' | 'medium' | 'hard' | 'expert'): CropType[] => {
  return Object.values(CROP_DETECTION_CONFIGS)
    .filter(config => config.balanceFactors.difficulty === difficulty)
    .map(config => config.cropType)
}

export const getCropsByBehaviorType = (behaviorType: SelfDisciplineType): CropType[] => {
  return Object.values(CROP_DETECTION_CONFIGS)
    .filter(config => config.behaviorType === behaviorType)
    .map(config => config.cropType)
} 