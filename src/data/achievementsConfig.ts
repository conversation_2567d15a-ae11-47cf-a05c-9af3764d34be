import {
  Achievement,
  AchievementType,
  AchievementCategory,
  DailyTask,
  WeeklyTask
} from '../types/achievements';

// 成就配置数据
export const ACHIEVEMENTS: Achievement[] = [
  // 专注类成就
  {
    id: 'focus_beginner',
    name: '专注入门',
    description: '连续专注5分钟',
    type: AchievementType.MILESTONE,
    category: AchievementCategory.FOCUS,
    icon: '🎯',
    experienceReward: 50,
    requirement: {
      type: 'continuous_focus_time',
      value: 5,
      description: '连续专注时间达到5分钟'
    }
  },
  {
    id: 'focus_apprentice',
    name: '专注学徒',
    description: '连续专注15分钟',
    type: AchievementType.MILESTONE,
    category: AchievementCategory.FOCUS,
    icon: '🏹',
    experienceReward: 100,
    requirement: {
      type: 'continuous_focus_time',
      value: 15,
      description: '连续专注时间达到15分钟'
    },
    prerequisite: ['focus_beginner']
  },
  {
    id: 'focus_master',
    name: '专注大师',
    description: '连续专注30分钟',
    type: AchievementType.MILESTONE,
    category: AchievementCategory.FOCUS,
    icon: '🎖️',
    experienceReward: 200,
    requirement: {
      type: 'continuous_focus_time',
      value: 30,
      description: '连续专注时间达到30分钟'
    },
    prerequisite: ['focus_apprentice']
  },
  {
    id: 'focus_legend',
    name: '专注传说',
    description: '连续专注60分钟',
    type: AchievementType.MILESTONE,
    category: AchievementCategory.FOCUS,
    icon: '👑',
    experienceReward: 500,
    requirement: {
      type: 'continuous_focus_time',
      value: 60,
      description: '连续专注时间达到60分钟'
    },
    prerequisite: ['focus_master']
  },
  {
    id: 'daily_focus',
    name: '每日专注',
    description: '今天专注超过20分钟',
    type: AchievementType.DAILY,
    category: AchievementCategory.FOCUS,
    icon: '🌟',
    experienceReward: 30,
    requirement: {
      type: 'daily_focus_time',
      value: 20,
      description: '单日专注时间超过20分钟'
    }
  },

  // 姿态类成就
  {
    id: 'posture_guardian',
    name: '姿态卫士',
    description: '保持良好姿态10分钟',
    type: AchievementType.MILESTONE,
    category: AchievementCategory.POSTURE,
    icon: '🛡️',
    experienceReward: 75,
    requirement: {
      type: 'good_posture_time',
      value: 10,
      description: '连续保持良好姿态10分钟'
    }
  },
  {
    id: 'posture_perfectionist',
    name: '姿态完美主义者',
    description: '单次会话姿态分数超过90分',
    type: AchievementType.SPECIAL,
    category: AchievementCategory.POSTURE,
    icon: '💎',
    experienceReward: 150,
    requirement: {
      type: 'session_posture_score',
      value: 90,
      description: '单次会话平均姿态分数超过90分'
    }
  },

  // 时间类成就
  {
    id: 'early_bird',
    name: '早起鸟',
    description: '早上8点前开始专注',
    type: AchievementType.DAILY,
    category: AchievementCategory.TIME,
    icon: '🐦',
    experienceReward: 40,
    requirement: {
      type: 'session_start_time',
      value: 8,
      description: '在早上8点前开始专注会话'
    }
  },
  {
    id: 'night_owl',
    name: '夜猫子',
    description: '晚上10点后还在专注',
    type: AchievementType.DAILY,
    category: AchievementCategory.TIME,
    icon: '🦉',
    experienceReward: 40,
    requirement: {
      type: 'session_during_time',
      value: 22,
      description: '在晚上10点后进行专注会话'
    }
  },

  // 一致性类成就
  {
    id: 'consistency_week',
    name: '坚持一周',
    description: '连续7天都有专注记录',
    type: AchievementType.STREAK,
    category: AchievementCategory.CONSISTENCY,
    icon: '📅',
    experienceReward: 200,
    requirement: {
      type: 'daily_streak',
      value: 7,
      description: '连续7天都有专注记录'
    }
  },
  {
    id: 'consistency_month',
    name: '月度专注',
    description: '连续30天都有专注记录',
    type: AchievementType.STREAK,
    category: AchievementCategory.CONSISTENCY,
    icon: '🗓️',
    experienceReward: 1000,
    requirement: {
      type: 'daily_streak',
      value: 30,
      description: '连续30天都有专注记录'
    },
    prerequisite: ['consistency_week']
  },

  // 改进类成就
  {
    id: 'improvement_focus',
    name: '专注进步',
    description: '比昨天专注时间增加50%',
    type: AchievementType.DAILY,
    category: AchievementCategory.IMPROVEMENT,
    icon: '📈',
    experienceReward: 60,
    requirement: {
      type: 'daily_improvement_percent',
      value: 50,
      description: '专注时间比前一天增加50%'
    }
  },
  {
    id: 'improvement_posture',
    name: '姿态改善',
    description: '姿态分数比昨天提高10分',
    type: AchievementType.DAILY,
    category: AchievementCategory.IMPROVEMENT,
    icon: '⬆️',
    experienceReward: 50,
    requirement: {
      type: 'posture_improvement',
      value: 10,
      description: '平均姿态分数比前一天提高10分'
    }
  },

  // 特殊成就
  {
    id: 'perfect_session',
    name: '完美会话',
    description: '专注度和姿态都超过95分',
    type: AchievementType.SPECIAL,
    category: AchievementCategory.FOCUS,
    icon: '⭐',
    experienceReward: 300,
    requirement: {
      type: 'perfect_session',
      value: 95,
      description: '专注度和姿态分数都超过95分'
    }
  },
  {
    id: 'marathon_session',
    name: '马拉松专注',
    description: '单次专注超过2小时',
    type: AchievementType.SPECIAL,
    category: AchievementCategory.TIME,
    icon: '🏃',
    experienceReward: 500,
    requirement: {
      type: 'session_duration',
      value: 120,
      description: '单次专注会话超过2小时'
    }
  },
  {
    id: 'multitasker',
    name: '多任务专家',
    description: '一天内完成5个专注会话',
    type: AchievementType.DAILY,
    category: AchievementCategory.TIME,
    icon: '🔄',
    experienceReward: 100,
    requirement: {
      type: 'daily_session_count',
      value: 5,
      description: '单日内完成5个专注会话'
    }
  },

  // 周常特殊成就
  {
    id: 'weekly_warrior',
    name: '周常战士',
    description: '本周专注时间超过10小时',
    type: AchievementType.WEEKLY,
    category: AchievementCategory.TIME,
    icon: '⚔️',
    experienceReward: 300,
    requirement: {
      type: 'weekly_focus_time',
      value: 600,
      description: '本周总专注时间超过10小时'
    }
  },
  {
    id: 'posture_champion',
    name: '姿态冠军',
    description: '本周平均姿态分数超过85分',
    type: AchievementType.WEEKLY,
    category: AchievementCategory.POSTURE,
    icon: '🏆',
    experienceReward: 250,
    requirement: {
      type: 'weekly_avg_posture',
      value: 85,
      description: '本周平均姿态分数超过85分'
    }
  }
];

// 日常任务配置
export const DAILY_TASKS: DailyTask[] = [
  {
    id: 'daily_focus_15min',
    name: '专注15分钟',
    description: '今天专注至少15分钟',
    experienceReward: 50,
    requirement: {
      type: 'daily_focus_time',
      value: 15
    },
    resetTime: '00:00'
  },
  {
    id: 'daily_good_posture',
    name: '保持良好姿态',
    description: '今天姿态平均分超过75分',
    experienceReward: 40,
    requirement: {
      type: 'daily_avg_posture',
      value: 75
    },
    resetTime: '00:00'
  },
  {
    id: 'daily_three_sessions',
    name: '三次专注',
    description: '今天完成3次专注会话',
    experienceReward: 60,
    requirement: {
      type: 'daily_session_count',
      value: 3
    },
    resetTime: '00:00'
  },
  {
    id: 'daily_improvement',
    name: '今日进步',
    description: '专注时间比昨天增加',
    experienceReward: 45,
    requirement: {
      type: 'daily_improvement',
      value: 1
    },
    resetTime: '00:00'
  },
  {
    id: 'daily_perfect_score',
    name: '完美分数',
    description: '至少一次会话专注度超过90分',
    experienceReward: 70,
    requirement: {
      type: 'daily_max_focus_score',
      value: 90
    },
    resetTime: '00:00'
  }
];

// 周常任务配置
export const WEEKLY_TASKS: WeeklyTask[] = [
  {
    id: 'weekly_total_5hours',
    name: '周度专注',
    description: '本周总专注时间超过5小时',
    experienceReward: 200,
    requirement: {
      type: 'weekly_focus_time',
      value: 300
    },
    resetDay: 1 // 周一
  },
  {
    id: 'weekly_consistency',
    name: '坚持一周',
    description: '本周每天都有专注记录',
    experienceReward: 250,
    requirement: {
      type: 'weekly_daily_streak',
      value: 7
    },
    resetDay: 1
  },
  {
    id: 'weekly_posture_excellence',
    name: '姿态卓越',
    description: '本周平均姿态分数超过80分',
    experienceReward: 180,
    requirement: {
      type: 'weekly_avg_posture',
      value: 80
    },
    resetDay: 1
  }
];

// 根据类别获取成就
export function getAchievementsByCategory(category: AchievementCategory): Achievement[] {
  return ACHIEVEMENTS.filter(achievement => achievement.category === category);
}

// 根据类型获取成就
export function getAchievementsByType(type: AchievementType): Achievement[] {
  return ACHIEVEMENTS.filter(achievement => achievement.type === type);
}

// 获取每日成就
export function getDailyAchievements(): Achievement[] {
  return getAchievementsByType(AchievementType.DAILY);
}

// 获取周常成就
export function getWeeklyAchievements(): Achievement[] {
  return getAchievementsByType(AchievementType.WEEKLY);
}

// 获取里程碑成就
export function getMilestoneAchievements(): Achievement[] {
  return getAchievementsByType(AchievementType.MILESTONE);
}

// 获取特殊成就
export function getSpecialAchievements(): Achievement[] {
  return getAchievementsByType(AchievementType.SPECIAL);
}

// 获取连续成就
export function getStreakAchievements(): Achievement[] {
  return getAchievementsByType(AchievementType.STREAK);
}

// 根据ID获取成就
export function getAchievementById(id: string): Achievement | undefined {
  return ACHIEVEMENTS.find(achievement => achievement.id === id);
}

// 根据ID获取日常任务
export function getDailyTaskById(id: string): DailyTask | undefined {
  return DAILY_TASKS.find(task => task.id === id);
}

// 根据ID获取周常任务
export function getWeeklyTaskById(id: string): WeeklyTask | undefined {
  return WEEKLY_TASKS.find(task => task.id === id);
}

// 获取所有成就统计
export function getAchievementStats(): {
  totalCount: number;
  categoryCount: Record<AchievementCategory, number>;
  typeCount: Record<AchievementType, number>;
  totalExperienceReward: number;
} {
  const categoryCount: Record<AchievementCategory, number> = {
    [AchievementCategory.FOCUS]: 0,
    [AchievementCategory.POSTURE]: 0,
    [AchievementCategory.TIME]: 0,
    [AchievementCategory.CONSISTENCY]: 0,
    [AchievementCategory.IMPROVEMENT]: 0
  };

  const typeCount: Record<AchievementType, number> = {
    [AchievementType.DAILY]: 0,
    [AchievementType.WEEKLY]: 0,
    [AchievementType.MILESTONE]: 0,
    [AchievementType.STREAK]: 0,
    [AchievementType.SPECIAL]: 0
  };

  let totalExperienceReward = 0;

  ACHIEVEMENTS.forEach(achievement => {
    categoryCount[achievement.category]++;
    typeCount[achievement.type]++;
    totalExperienceReward += achievement.experienceReward;
  });

  return {
    totalCount: ACHIEVEMENTS.length,
    categoryCount,
    typeCount,
    totalExperienceReward
  };
} 