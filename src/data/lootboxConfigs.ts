import { LootboxConfig, LootboxType, ItemRarity, ItemCategory } from '../types/lootbox'
import { CurrencyType } from '../types/currency'
import { AgriculturalVariety, IndustrialVariety, EquipmentType } from '../types/enhanced-items'

// 新的期货道具池定义
const AGRICULTURAL_ITEM_POOL = [
  AgriculturalVariety.CORN,
  AgriculturalVariety.WHEAT,
  AgriculturalVariety.SOYBEAN,
  AgriculturalVariety.SOYBEAN_OIL,
  AgriculturalVariety.PALM_OIL,
  AgriculturalVariety.RAPESEED_OIL,
  AgriculturalVariety.SOYBEAN_MEAL,
  AgriculturalVariety.COTTON,
  AgriculturalVariety.WHITE_SUGAR,
  AgriculturalVariety.APPLE,
  AgriculturalVariety.RED_DATES,
  AgriculturalVariety.LIVE_HOG
]

const INDUSTRIAL_ITEM_POOL = [
  IndustrialVariety.COPPER,
  IndustrialVariety.ALUMINUM,
  IndustrialVariety.LEAD,
  IndustrialVariety.ZINC,
  IndustrialVariety.NICKEL,
  IndustrialVariety.TIN,
  IndustrialVariety.GOLD,
  IndustrialVariety.SILVER,
  IndustrialVariety.REBAR,
  IndustrialVariety.HOT_ROLLED_COIL,
  IndustrialVariety.GLASS,
  IndustrialVariety.THERMAL_COAL,
  IndustrialVariety.COKE,
  IndustrialVariety.COKING_COAL,
  IndustrialVariety.CRUDE_OIL,
  IndustrialVariety.ASPHALT,
  IndustrialVariety.LPG
]

const EQUIPMENT_ITEM_POOL = [
  EquipmentType.FOCUS_GLASSES,
  EquipmentType.FOCUS_HEADPHONES,
  EquipmentType.ENERGY_BRACELET,
  EquipmentType.DISCIPLINE_CLOCK
]

// 盲盒配置
export const LOOTBOX_CONFIGS: Record<LootboxType, LootboxConfig> = {
  // 农产品系列盲盒
  [LootboxType.BASIC_FARM]: {
    id: LootboxType.BASIC_FARM,
    name: '基础农场盒',
    description: '包含所有农业期货品种，适合刚开始投资的新手',
    category: ItemCategory.AGRICULTURAL,
    icon: '📦',
    price: {
      currency: CurrencyType.FOCUS_COIN,
      amount: 100
    },
    guaranteedRarity: ItemRarity.GRAY,
    dropRates: {
      [ItemRarity.GRAY]: 0.65,      // 65%
      [ItemRarity.GREEN]: 0.28,     // 28%
      [ItemRarity.BLUE]: 0.06,      // 6%
      [ItemRarity.ORANGE]: 0.01,    // 1%
      [ItemRarity.GOLD]: 0,         // 0%
      [ItemRarity.GOLD_RED]: 0      // 0%
    },
    itemPool: AGRICULTURAL_ITEM_POOL
  },

  [LootboxType.PREMIUM_FARM]: {
    id: LootboxType.PREMIUM_FARM,
    name: '高级农场盒',
    description: '农业期货品种升级版，更高的品质掉落率和数量',
    category: ItemCategory.AGRICULTURAL,
    icon: '🎁',
    price: {
      currency: CurrencyType.FOCUS_COIN,
      amount: 500
    },
    guaranteedRarity: ItemRarity.GREEN,
    dropRates: {
      [ItemRarity.GRAY]: 0.30,      // 30% (从40%降低)
      [ItemRarity.GREEN]: 0.40,     // 40% (从35%提高)
      [ItemRarity.BLUE]: 0.25,      // 25% (从20%提高)
      [ItemRarity.ORANGE]: 0.04,    // 4%
      [ItemRarity.GOLD]: 0.01,      // 1%
      [ItemRarity.GOLD_RED]: 0      // 0%
    },
    itemPool: AGRICULTURAL_ITEM_POOL,  // 也使用完整道具池
    specialFeatures: {
      guaranteedCount: 3,
      bonusChance: 0.15
    }
  },

  [LootboxType.LEGENDARY_FARM]: {
    id: LootboxType.LEGENDARY_FARM,
    name: '传说农场盒',
    description: '蕴含最稀有农业期货品种的珍贵宝盒',
    category: ItemCategory.AGRICULTURAL,
    icon: '🏆',
    price: {
      currency: CurrencyType.DISCIPLINE_TOKEN,
      amount: 50
    },
    guaranteedRarity: ItemRarity.BLUE,
    dropRates: {
      [ItemRarity.GRAY]: 0.10,      // 10%
      [ItemRarity.GREEN]: 0.25,     // 25%
      [ItemRarity.BLUE]: 0.40,      // 40%
      [ItemRarity.ORANGE]: 0.20,    // 20%
      [ItemRarity.GOLD]: 0.04,      // 4%
      [ItemRarity.GOLD_RED]: 0.01   // 1%
    },
    itemPool: AGRICULTURAL_ITEM_POOL,
    specialFeatures: {
      guaranteedCount: 5,
      pityTimer: 20,
      bonusChance: 0.30
    }
  },

  // 工业品系列盲盒
  [LootboxType.BASIC_INDUSTRIAL]: {
    id: LootboxType.BASIC_INDUSTRIAL,
    name: '基础工业盒',
    description: '包含基础工业期货品种和金属原料',
    category: ItemCategory.INDUSTRIAL,
    icon: '🔧',
    price: {
      currency: CurrencyType.FOCUS_COIN,
      amount: 120
    },
    guaranteedRarity: ItemRarity.GRAY,
    dropRates: {
      [ItemRarity.GRAY]: 0.70,      // 70%
      [ItemRarity.GREEN]: 0.25,     // 25%
      [ItemRarity.BLUE]: 0.04,      // 4%
      [ItemRarity.ORANGE]: 0.01,    // 1%
      [ItemRarity.GOLD]: 0,         // 0%
      [ItemRarity.GOLD_RED]: 0      // 0%
    },
    itemPool: [
      IndustrialVariety.COPPER,
      IndustrialVariety.ALUMINUM,
      IndustrialVariety.LEAD,
      IndustrialVariety.ZINC,
      IndustrialVariety.CRUDE_OIL
    ]
  },

  [LootboxType.PREMIUM_INDUSTRIAL]: {
    id: LootboxType.PREMIUM_INDUSTRIAL,
    name: '高级工业盒',
    description: '先进的工业期货品种和贵金属',
    category: ItemCategory.INDUSTRIAL,
    icon: '⚙️',
    price: {
      currency: CurrencyType.FOCUS_COIN,
      amount: 600
    },
    guaranteedRarity: ItemRarity.GREEN,
    dropRates: {
      [ItemRarity.GRAY]: 0.35,      // 35%
      [ItemRarity.GREEN]: 0.40,     // 40%
      [ItemRarity.BLUE]: 0.20,      // 20%
      [ItemRarity.ORANGE]: 0.04,    // 4%
      [ItemRarity.GOLD]: 0.01,      // 1%
      [ItemRarity.GOLD_RED]: 0      // 0%
    },
    itemPool: [
      IndustrialVariety.SILVER,
      IndustrialVariety.GOLD,
      IndustrialVariety.NICKEL,
      IndustrialVariety.TIN,
      IndustrialVariety.ASPHALT,
      IndustrialVariety.LPG,
      IndustrialVariety.THERMAL_COAL,
      IndustrialVariety.COKING_COAL,
      IndustrialVariety.COKE,
      IndustrialVariety.REBAR,
      IndustrialVariety.HOT_ROLLED_COIL,
      IndustrialVariety.GLASS
    ],
    specialFeatures: {
      guaranteedCount: 4,
      bonusChance: 0.20
    }
  },

  [LootboxType.LEGENDARY_INDUSTRIAL]: {
    id: LootboxType.LEGENDARY_INDUSTRIAL,
    name: '传说工业盒',
    description: '顶级工业期货品种和贵金属的结晶',
    category: ItemCategory.INDUSTRIAL,
    icon: '🏭',
    price: {
      currency: CurrencyType.DISCIPLINE_TOKEN,
      amount: 60
    },
    guaranteedRarity: ItemRarity.BLUE,
    dropRates: {
      [ItemRarity.GRAY]: 0.05,      // 5%
      [ItemRarity.GREEN]: 0.20,     // 20%
      [ItemRarity.BLUE]: 0.45,      // 45%
      [ItemRarity.ORANGE]: 0.25,    // 25%
      [ItemRarity.GOLD]: 0.04,      // 4%
      [ItemRarity.GOLD_RED]: 0.01   // 1%
    },
    itemPool: INDUSTRIAL_ITEM_POOL,
    specialFeatures: {
      guaranteedCount: 5,
      pityTimer: 15,
      bonusChance: 0.35
    }
  },

  // 装备系列盲盒
  [LootboxType.SYNTHESIS_BOX]: {
    id: LootboxType.SYNTHESIS_BOX,
    name: '装备宝盒',
    description: '专门用于获取专注装备的特殊宝盒',
    category: 'mixed',
    icon: '🧪',
    price: {
      currency: CurrencyType.FOCUS_COIN,
      amount: 800
    },
    guaranteedRarity: ItemRarity.GREEN,
    dropRates: {
      [ItemRarity.GRAY]: 0.35,      // 35%
      [ItemRarity.GREEN]: 0.40,     // 40%
      [ItemRarity.BLUE]: 0.20,      // 20%
      [ItemRarity.ORANGE]: 0.04,    // 4%
      [ItemRarity.GOLD]: 0.01,      // 1%
      [ItemRarity.GOLD_RED]: 0      // 0%
    },
    itemPool: EQUIPMENT_ITEM_POOL,
    specialFeatures: {
      guaranteedCount: 2,
      bonusChance: 0.25
    }
  },

  // 混合系列盲盒
  [LootboxType.FUTURES_MYSTERY]: {
    id: LootboxType.FUTURES_MYSTERY,
    name: '期货神秘盒',
    description: '基于期货市场波动的神秘宝盒，内容随市场变化',
    category: 'mixed',
    icon: '📊',
    price: {
      currency: CurrencyType.FUTURES_CRYSTAL,
      amount: 10
    },
    guaranteedRarity: ItemRarity.GREEN,
    dropRates: {
      [ItemRarity.GRAY]: 0.30,      // 30%
      [ItemRarity.GREEN]: 0.30,     // 30%
      [ItemRarity.BLUE]: 0.25,      // 25%
      [ItemRarity.ORANGE]: 0.12,    // 12%
      [ItemRarity.GOLD]: 0.02,      // 2%
      [ItemRarity.GOLD_RED]: 0.01   // 1%
    },
    itemPool: [
      ...AGRICULTURAL_ITEM_POOL.slice(0, 6),
      ...INDUSTRIAL_ITEM_POOL.slice(0, 8),
      ...EQUIPMENT_ITEM_POOL
    ],
    specialFeatures: {
      guaranteedCount: 3,
      bonusChance: 0.25,
      limitedTime: true
    }
  },

  [LootboxType.GOLDEN_TREASURE]: {
    id: LootboxType.GOLDEN_TREASURE,
    name: '金色宝藏盒',
    description: '传说中的金色宝藏，蕴含最珍贵的期货品种和装备',
    category: 'mixed',
    icon: '💎',
    price: {
      currency: CurrencyType.GOLDEN_HARVEST,
      amount: 5
    },
    guaranteedRarity: ItemRarity.ORANGE,
    dropRates: {
      [ItemRarity.GRAY]: 0,         // 0%
      [ItemRarity.GREEN]: 0.05,     // 5%
      [ItemRarity.BLUE]: 0.20,      // 20%
      [ItemRarity.ORANGE]: 0.50,    // 50%
      [ItemRarity.GOLD]: 0.20,      // 20%
      [ItemRarity.GOLD_RED]: 0.05   // 5%
    },
    itemPool: [
      ...AGRICULTURAL_ITEM_POOL,
      ...INDUSTRIAL_ITEM_POOL,
      ...EQUIPMENT_ITEM_POOL
    ],
    specialFeatures: {
      guaranteedCount: 7,
      pityTimer: 10,
      bonusChance: 0.50
    }
  }
} 