import {
  Achievement,
  AchievementType,
  AchievementCategory
} from '../types/achievements'
import { WeatherType } from '../types/weather'

/**
 * 天气相关成就配置
 * 基于不同天气条件下的专注表现给予用户奖励
 */
export const WEATHER_ACHIEVEMENTS: Achievement[] = [
  // 晴天成就系列
  {
    id: 'sunny_focus_master',
    name: '阳光专注大师',
    description: '在晴天条件下完成30分钟专注训练',
    type: AchievementType.SPECIAL,
    category: AchievementCategory.FOCUS,
    icon: '☀️',
    experienceReward: 120,
    requirement: {
      type: 'weather_focus_time',
      value: 30,
      description: '在晴天(SUNNY)天气下连续专注30分钟'
    }
  },
  {
    id: 'sunny_streak',
    name: '阳光连击',
    description: '连续3天在晴天完成专注训练',
    type: AchievementType.STREAK,
    category: AchievementCategory.CONSISTENCY,
    icon: '🌟',
    experienceReward: 200,
    requirement: {
      type: 'weather_streak',
      value: 3,
      description: '连续3天在晴天完成专注训练'
    }
  },

  // 雨天成就系列
  {
    id: 'rainy_meditation',
    name: '雨中冥想',
    description: '在雨天完成20分钟专注训练',
    type: AchievementType.SPECIAL,
    category: AchievementCategory.FOCUS,
    icon: '🌧️',
    experienceReward: 100,
    requirement: {
      type: 'weather_focus_time',
      value: 20,
      description: '在雨天(RAINY)天气下连续专注20分钟'
    }
  },
  {
    id: 'storm_warrior',
    name: '暴雨战士',
    description: '在雷暴天气下完成专注训练',
    type: AchievementType.SPECIAL,
    category: AchievementCategory.FOCUS,
    icon: '⛈️',
    experienceReward: 300,
    requirement: {
      type: 'weather_session_complete',
      value: 1,
      description: '在雷暴(THUNDERSTORM)天气下完成任意时长的专注训练'
    }
  },
  {
    id: 'heavy_rain_challenger',
    name: '大雨挑战者',
    description: '在大雨天气下专注训练超过15分钟',
    type: AchievementType.SPECIAL,
    category: AchievementCategory.FOCUS,
    icon: '🌊',
    experienceReward: 180,
    requirement: {
      type: 'weather_focus_time',
      value: 15,
      description: '在大雨(HEAVY_RAIN)天气下连续专注15分钟'
    }
  },

  // 多云天气成就
  {
    id: 'cloudy_concentrator',
    name: '云中专注者',
    description: '在多云天气下完成完美专注会话',
    type: AchievementType.SPECIAL,
    category: AchievementCategory.FOCUS,
    icon: '☁️',
    experienceReward: 150,
    requirement: {
      type: 'weather_perfect_session',
      value: 1,
      description: '在多云天气下完成专注分数>90分的会话'
    }
  },

  // 雪天成就
  {
    id: 'snow_serenity',
    name: '雪中宁静',
    description: '在雪天完成专注训练',
    type: AchievementType.SPECIAL,
    category: AchievementCategory.FOCUS,
    icon: '❄️',
    experienceReward: 200,
    requirement: {
      type: 'weather_session_complete',
      value: 1,
      description: '在雪天(SNOWY)天气下完成专注训练'
    }
  },

  // 雾天成就
  {
    id: 'foggy_mindfulness',
    name: '雾中正念',
    description: '在雾天保持15分钟专注',
    type: AchievementType.SPECIAL,
    category: AchievementCategory.FOCUS,
    icon: '🌫️',
    experienceReward: 160,
    requirement: {
      type: 'weather_focus_time',
      value: 15,
      description: '在雾天(FOGGY)天气下连续专注15分钟'
    }
  },

  // 风天成就
  {
    id: 'windy_warrior',
    name: '风中勇士',
    description: '在大风天气下完成专注挑战',
    type: AchievementType.SPECIAL,
    category: AchievementCategory.FOCUS,
    icon: '💨',
    experienceReward: 140,
    requirement: {
      type: 'weather_focus_time',
      value: 10,
      description: '在大风(WINDY)天气下连续专注10分钟'
    }
  },

  // 天气适应性成就
  {
    id: 'weather_adaptability',
    name: '天气适应大师',
    description: '在5种不同天气下都完成过专注训练',
    type: AchievementType.MILESTONE,
    category: AchievementCategory.IMPROVEMENT,
    icon: '🌈',
    experienceReward: 500,
    requirement: {
      type: 'weather_variety',
      value: 5,
      description: '在5种不同天气类型下分别完成专注训练'
    }
  },
  {
    id: 'weather_master',
    name: '全天候专注大师',
    description: '在所有9种天气下都完成过专注训练',
    type: AchievementType.MILESTONE,
    category: AchievementCategory.IMPROVEMENT,
    icon: '🏆',
    experienceReward: 1000,
    requirement: {
      type: 'weather_variety',
      value: 9,
      description: '在所有9种天气类型下分别完成专注训练'
    },
    prerequisite: ['weather_adaptability']
  },

  // 天气挑战成就
  {
    id: 'adverse_weather_champion',
    name: '恶劣天气冠军',
    description: '在雷暴、大雨、雪天中任选其二完成专注',
    type: AchievementType.SPECIAL,
    category: AchievementCategory.FOCUS,
    icon: '💪',
    experienceReward: 400,
    requirement: {
      type: 'adverse_weather_sessions',
      value: 2,
      description: '在恶劣天气条件下完成2次不同类型的专注训练'
    }
  },

  // 天气预报成就
  {
    id: 'weather_planner',
    name: '天气规划师',
    description: '根据天气预报安排专注训练5次',
    type: AchievementType.SPECIAL,
    category: AchievementCategory.TIME,
    icon: '📅',
    experienceReward: 250,
    requirement: {
      type: 'weather_planned_sessions',
      value: 5,
      description: '使用天气预报功能安排并完成5次专注训练'
    }
  },

  // 最佳天气时机成就
  {
    id: 'optimal_timing',
    name: '最佳时机专家',
    description: '在预报推荐的最佳专注时间完成训练',
    type: AchievementType.DAILY,
    category: AchievementCategory.TIME,
    icon: '🎯',
    experienceReward: 80,
    requirement: {
      type: 'optimal_weather_session',
      value: 1,
      description: '在系统推荐的最佳专注时机完成训练'
    }
  },

  // 天气连击成就
  {
    id: 'weather_combo_5',
    name: '天气连击达人',
    description: '连续5次在不同天气下完成专注',
    type: AchievementType.STREAK,
    category: AchievementCategory.CONSISTENCY,
    icon: '🔥',
    experienceReward: 300,
    requirement: {
      type: 'weather_variety_streak',
      value: 5,
      description: '连续5次专注训练使用不同的天气类型'
    }
  },

  // 季节性成就
  {
    id: 'seasonal_dedication',
    name: '季节专注者',
    description: '在每个季节都完成过100分钟专注',
    type: AchievementType.MILESTONE,
    category: AchievementCategory.TIME,
    icon: '🍃',
    experienceReward: 600,
    requirement: {
      type: 'seasonal_focus_time',
      value: 100,
      description: '在每个季节都累计完成100分钟专注训练'
    }
  },

  // 天气影响抗性成就
  {
    id: 'weather_resistance',
    name: '天气影响免疫',
    description: '在负面天气影响下仍保持90%以上专注度',
    type: AchievementType.SPECIAL,
    category: AchievementCategory.FOCUS,
    icon: '🛡️',
    experienceReward: 350,
    requirement: {
      type: 'negative_weather_high_score',
      value: 90,
      description: '在负面天气影响下专注分数仍达到90分以上'
    }
  },

  // 天气同步成就
  {
    id: 'weather_harmony',
    name: '天气和谐者',
    description: '专注训练与天气变化完美同步10次',
    type: AchievementType.SPECIAL,
    category: AchievementCategory.CONSISTENCY,
    icon: '🌿',
    experienceReward: 280,
    requirement: {
      type: 'weather_sync_count',
      value: 10,
      description: '专注训练开始时间与天气变化同步10次'
    }
  }
]

/**
 * 天气成就类别映射
 */
export const WEATHER_ACHIEVEMENT_CATEGORIES = {
  SUNNY_SERIES: ['sunny_focus_master', 'sunny_streak'],
  RAINY_SERIES: ['rainy_meditation', 'storm_warrior', 'heavy_rain_challenger'],
  CLOUDY_SERIES: ['cloudy_concentrator'],
  SNOW_SERIES: ['snow_serenity'],
  FOG_SERIES: ['foggy_mindfulness'],
  WIND_SERIES: ['windy_warrior'],
  ADAPTABILITY: ['weather_adaptability', 'weather_master'],
  CHALLENGES: ['adverse_weather_champion', 'weather_resistance'],
  PLANNING: ['weather_planner', 'optimal_timing'],
  STREAKS: ['weather_combo_5', 'weather_variety_streak'],
  SEASONAL: ['seasonal_dedication'],
  SPECIAL: ['weather_harmony']
}

/**
 * 获取指定天气类型的相关成就
 */
export function getWeatherTypeAchievements(weatherType: WeatherType): Achievement[] {
  return WEATHER_ACHIEVEMENTS.filter(achievement => {
    const req = achievement.requirement
    
    // 检查是否为特定天气类型的成就
    if (req.type.includes('weather_')) {
      // 根据成就ID或描述判断是否与指定天气类型相关
      const achievementId = achievement.id.toLowerCase()
      const description = achievement.description.toLowerCase()
      
      switch (weatherType) {
        case WeatherType.SUNNY:
          return achievementId.includes('sunny') || description.includes('晴天')
        case WeatherType.RAINY:
          return achievementId.includes('rainy') || description.includes('雨天')
        case WeatherType.HEAVY_RAIN:
          return achievementId.includes('heavy') || description.includes('大雨')
        case WeatherType.THUNDERSTORM:
          return achievementId.includes('storm') || description.includes('雷暴')
        case WeatherType.CLOUDY:
        case WeatherType.PARTLY_CLOUDY:
          return achievementId.includes('cloudy') || description.includes('多云')
        case WeatherType.SNOWY:
          return achievementId.includes('snow') || description.includes('雪天')
        case WeatherType.FOGGY:
          return achievementId.includes('foggy') || description.includes('雾天')
        case WeatherType.WINDY:
          return achievementId.includes('windy') || description.includes('风')
        default:
          return false
      }
    }
    
    return false
  })
}

/**
 * 获取天气适应性相关成就
 */
export function getWeatherAdaptabilityAchievements(): Achievement[] {
  return WEATHER_ACHIEVEMENTS.filter(achievement => 
    WEATHER_ACHIEVEMENT_CATEGORIES.ADAPTABILITY.includes(achievement.id)
  )
}

/**
 * 获取天气挑战类成就
 */
export function getWeatherChallengeAchievements(): Achievement[] {
  return WEATHER_ACHIEVEMENTS.filter(achievement => 
    WEATHER_ACHIEVEMENT_CATEGORIES.CHALLENGES.includes(achievement.id)
  )
}

/**
 * 获取季节性成就
 */
export function getSeasonalAchievements(): Achievement[] {
  return WEATHER_ACHIEVEMENTS.filter(achievement => 
    WEATHER_ACHIEVEMENT_CATEGORIES.SEASONAL.includes(achievement.id)
  )
}

/**
 * 获取所有天气相关成就的统计信息
 */
export function getWeatherAchievementStats() {
  const totalCount = WEATHER_ACHIEVEMENTS.length
  const totalExperience = WEATHER_ACHIEVEMENTS.reduce((sum, achievement) => 
    sum + achievement.experienceReward, 0
  )
  
  const typeCount = WEATHER_ACHIEVEMENTS.reduce((counts, achievement) => {
    counts[achievement.type] = (counts[achievement.type] || 0) + 1
    return counts
  }, {} as Record<AchievementType, number>)
  
  const categoryCount = Object.keys(WEATHER_ACHIEVEMENT_CATEGORIES).reduce((counts, category) => {
    counts[category] = WEATHER_ACHIEVEMENT_CATEGORIES[category as keyof typeof WEATHER_ACHIEVEMENT_CATEGORIES].length
    return counts
  }, {} as Record<string, number>)
  
  return {
    totalCount,
    totalExperience,
    typeCount,
    categoryCount,
    averageExperience: Math.round(totalExperience / totalCount)
  }
}

export default WEATHER_ACHIEVEMENTS 