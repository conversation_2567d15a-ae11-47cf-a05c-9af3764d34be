import { ItemRarity } from '../types/lootbox'
import { PlantingItem, ItemTool, CropGrowthConfig, Achievement } from '../types/planting'

// 种植道具数据 - 精心设计的上瘾机制
export const PLANTING_ITEMS: PlantingItem[] = [
  // ================ 种子类 ================
  {
    id: 'basic_seed',
    name: '普通种子',
    icon: '🌱',
    type: ItemTool.SEED,
    rarity: ItemRarity.GRAY,
    description: '最基础的种子，适合新手使用',
    effects: {},
    price: 10,
    unlockLevel: 1
  },
  {
    id: 'premium_seed',
    name: '优质种子',
    icon: '🌟',
    type: ItemTool.SEED,
    rarity: ItemRarity.GREEN,
    description: '经过精心培育的种子，成长更快',
    effects: {
      speedBoost: 15,
      qualityBoost: 10
    },
    price: 50,
    unlockLevel: 5
  },
  {
    id: 'rare_seed',
    name: '稀有种子',
    icon: '✨',
    type: ItemTool.SEED,
    rarity: ItemRarity.BLUE,
    description: '稀有品种种子，有机会产出高品质作物',
    effects: {
      speedBoost: 25,
      qualityBoost: 20,
      rarityBoost: 15
    },
    price: 200,
    unlockLevel: 10
  },

  // ================ 肥料类 ================
  {
    id: 'organic_fertilizer',
    name: '有机肥料',
    icon: '🌿',
    type: ItemTool.FERTILIZER,
    rarity: ItemRarity.GREEN,
    description: '天然有机肥料，提升作物产量',
    effects: {
      yieldBoost: 25,
      qualityBoost: 5
    },
    price: 30,
    unlockLevel: 3
  },
  {
    id: 'miracle_fertilizer',
    name: '奇迹肥料',
    icon: '💎',
    type: ItemTool.FERTILIZER,
    rarity: ItemRarity.ORANGE,
    description: '传说中的神奇肥料，大幅提升产量和品质',
    effects: {
      yieldBoost: 50,
      qualityBoost: 30,
      rarityBoost: 10
    },
    price: 500,
    unlockLevel: 15
  },

  // ================ 生长加速剂类 ================
  {
    id: 'time_crystal',
    name: '时间水晶',
    icon: '⏰',
    type: ItemTool.ACCELERATOR,
    rarity: ItemRarity.GOLD,
    description: '神秘的时间水晶，可以极大加速作物生长',
    effects: {
      speedBoost: 75
    },
    price: 1000,
    unlockLevel: 20
  },

  // ================ 品质提升剂类 ================
  {
    id: 'rainbow_elixir',
    name: '彩虹药水',
    icon: '🌈',
    type: ItemTool.ENHANCER,
    rarity: ItemRarity.GOLD_RED,
    description: '传说级彩虹药水，几乎保证获得神话品质！',
    effects: {
      qualityBoost: 80,
      rarityBoost: 60,
      yieldBoost: 100
    },
    price: 5000,
    unlockLevel: 30
  }
]

// 作物生长配置 - 每个作物都有独特的特性
export const CROP_GROWTH_CONFIGS: CropGrowthConfig[] = [
  {
    cropId: 'corn',
    baseGrowthTime: 5, // 5分钟
    rarityWeights: {
      [ItemRarity.GRAY]: 50,
      [ItemRarity.GREEN]: 30,
      [ItemRarity.BLUE]: 15,
      [ItemRarity.ORANGE]: 4,
      [ItemRarity.GOLD]: 0.9,
      [ItemRarity.GOLD_RED]: 0.1
    },
    visualEffects: {
      growthParticles: 'golden-sparkles',
      matureGlow: 'warm-yellow-glow',
      harvestEffect: 'corn-explosion'
    },
    environmentPreferences: {
      season: ['summer', 'spring'],
      weather: ['sunny', 'light-rain'],
      soilType: 'fertile'
    }
  },
  {
    cropId: 'wheat',
    baseGrowthTime: 4,
    rarityWeights: {
      [ItemRarity.GRAY]: 45,
      [ItemRarity.GREEN]: 35,
      [ItemRarity.BLUE]: 15,
      [ItemRarity.ORANGE]: 4,
      [ItemRarity.GOLD]: 0.8,
      [ItemRarity.GOLD_RED]: 0.2
    },
    visualEffects: {
      growthParticles: 'wheat-dust',
      matureGlow: 'golden-wheat-glow',
      harvestEffect: 'wheat-harvest-dance'
    },
    environmentPreferences: {
      season: ['spring', 'autumn'],
      weather: ['sunny', 'windy'],
      soilType: 'normal'
    }
  },
  {
    cropId: 'cotton',
    baseGrowthTime: 8,
    rarityWeights: {
      [ItemRarity.GRAY]: 40,
      [ItemRarity.GREEN]: 30,
      [ItemRarity.BLUE]: 20,
      [ItemRarity.ORANGE]: 8,
      [ItemRarity.GOLD]: 1.8,
      [ItemRarity.GOLD_RED]: 0.2
    },
    visualEffects: {
      growthParticles: 'cotton-clouds',
      matureGlow: 'soft-white-glow',
      harvestEffect: 'cotton-bloom'
    },
    environmentPreferences: {
      season: ['summer'],
      weather: ['sunny', 'hot'],
      soilType: 'sandy'
    }
  },
  {
    cropId: 'apple',
    baseGrowthTime: 12,
    rarityWeights: {
      [ItemRarity.GRAY]: 35,
      [ItemRarity.GREEN]: 30,
      [ItemRarity.BLUE]: 20,
      [ItemRarity.ORANGE]: 10,
      [ItemRarity.GOLD]: 4,
      [ItemRarity.GOLD_RED]: 1
    },
    visualEffects: {
      growthParticles: 'apple-blossoms',
      matureGlow: 'red-fruit-glow',
      harvestEffect: 'apple-shower'
    },
    environmentPreferences: {
      season: ['spring', 'autumn'],
      weather: ['cool', 'light-rain'],
      soilType: 'rich'
    }
  },
  {
    cropId: 'soybean',
    baseGrowthTime: 6,
    rarityWeights: {
      [ItemRarity.GRAY]: 55,
      [ItemRarity.GREEN]: 25,
      [ItemRarity.BLUE]: 15,
      [ItemRarity.ORANGE]: 4,
      [ItemRarity.GOLD]: 0.8,
      [ItemRarity.GOLD_RED]: 0.2
    },
    visualEffects: {
      growthParticles: 'green-pods',
      matureGlow: 'healthy-green-glow',
      harvestEffect: 'pod-burst'
    },
    environmentPreferences: {
      season: ['summer', 'spring'],
      weather: ['moderate', 'humid'],
      soilType: 'nitrogen-rich'
    }
  }
]

// 成就系统 - 激发收集欲望的关键
export const ACHIEVEMENTS: Achievement[] = [
  // ================ 入门成就 ================
  {
    id: 'first_harvest',
    name: '初次收获',
    description: '完成你的第一次收获',
    icon: '🌾',
    requirements: {
      type: 'harvest_count',
      target: 1
    },
    rewards: {
      experience: 50,
      coins: 100,
      items: [
        { id: 'organic_fertilizer', quantity: 3 }
      ]
    },
    isCompleted: false,
    progress: 0
  },
  {
    id: 'first_rare',
    name: '稀有收获',
    description: '获得第一个稀有品质的作物',
    icon: '💎',
    requirements: {
      type: 'rare_harvest',
      target: 1,
      quality: ItemRarity.BLUE
    },
    rewards: {
      experience: 200,
      coins: 500,
      items: [
        { id: 'rare_seed', quantity: 1 }
      ]
    },
    isCompleted: false,
    progress: 0
  },

  // ================ 收集成就 ================
  {
    id: 'corn_collector',
    name: '玉米大师',
    description: '收获100个玉米',
    icon: '🌽',
    requirements: {
      type: 'harvest_count',
      target: 100,
      cropId: 'corn'
    },
    rewards: {
      experience: 500,
      coins: 1000,
      items: [
        { id: 'miracle_fertilizer', quantity: 2 }
      ]
    },
    isCompleted: false,
    progress: 0
  },
  {
    id: 'legendary_farmer',
    name: '传说农夫',
    description: '获得第一个传说品质的作物',
    icon: '👑',
    requirements: {
      type: 'rare_harvest',
      target: 1,
      quality: ItemRarity.GOLD
    },
    rewards: {
      experience: 1000,
      coins: 2000,
      items: [
        { id: 'time_crystal', quantity: 1 }
      ]
    },
    isCompleted: false,
    progress: 0
  },

  // ================ 终极成就 ================
  {
    id: 'mythical_harvest',
    name: '神话收获者',
    description: '获得神话品质的作物！',
    icon: '🌟',
    requirements: {
      type: 'rare_harvest',
      target: 1,
      quality: ItemRarity.GOLD_RED
    },
    rewards: {
      experience: 5000,
      coins: 10000,
      items: [
        { id: 'rainbow_elixir', quantity: 1 }
      ]
    },
    isCompleted: false,
    progress: 0
  },
  {
    id: 'completionist',
    name: '完美收集家',
    description: '收集所有作物的神话品质版本',
    icon: '🏆',
    requirements: {
      type: 'collection_rate',
      target: 100
    },
    rewards: {
      experience: 10000,
      coins: 50000,
      items: [
        { id: 'rainbow_elixir', quantity: 5 }
      ]
    },
    isCompleted: false,
    progress: 0
  },

  // ================ 等级成就 ================
  {
    id: 'expert_farmer',
    name: '专家农夫',
    description: '达到等级20',
    icon: '🎖️',
    requirements: {
      type: 'level_reach',
      target: 20
    },
    rewards: {
      experience: 2000,
      coins: 5000,
      items: [
        { id: 'time_crystal', quantity: 2 }
      ]
    },
    isCompleted: false,
    progress: 0
  }
]

// 稀有度权重配置 - 控制整体获取概率
export const GLOBAL_RARITY_MODIFIERS = {
  // 基础概率修正器
  baseModifiers: {
    [ItemRarity.GRAY]: 1.0,
    [ItemRarity.GREEN]: 0.8,
    [ItemRarity.BLUE]: 0.6,
    [ItemRarity.ORANGE]: 0.3,
    [ItemRarity.GOLD]: 0.1,
    [ItemRarity.GOLD_RED]: 0.02
  },
  
  // 道具效果叠加规则
  stackingRules: {
    yieldBoost: 'additive',      // 产量加成：累加
    qualityBoost: 'multiplicative', // 品质加成：相乘
    speedBoost: 'additive',      // 速度加成：累加
    rarityBoost: 'multiplicative' // 稀有度加成：相乘
  }
}

// 经验值和等级配置
export const LEVEL_CONFIG = {
  baseExperience: 100,
  experienceMultiplier: 1.5,
  maxLevel: 50,
  
  // 等级奖励
  levelRewards: {
    5: { coins: 500, items: [{ id: 'premium_seed', quantity: 5 }] },
    10: { coins: 1000, items: [{ id: 'rare_seed', quantity: 3 }] },
    15: { coins: 2000, items: [{ id: 'miracle_fertilizer', quantity: 5 }] },
    20: { coins: 5000, items: [{ id: 'time_crystal', quantity: 1 }] },
    25: { coins: 10000, items: [{ id: 'rainbow_elixir', quantity: 1 }] },
    30: { coins: 20000, items: [{ id: 'rainbow_elixir', quantity: 2 }] }
  }
}

// 获取道具信息
export function getPlantingItem(itemId: string): PlantingItem | undefined {
  return PLANTING_ITEMS.find(item => item.id === itemId)
}

// 获取作物生长配置
export function getCropGrowthConfig(cropId: string): CropGrowthConfig | undefined {
  return CROP_GROWTH_CONFIGS.find(config => config.cropId === cropId)
}

// 计算经验值需求
export function getExperienceRequired(level: number): number {
  return Math.floor(LEVEL_CONFIG.baseExperience * Math.pow(LEVEL_CONFIG.experienceMultiplier, level - 1))
}

// 计算稀有度概率
export function calculateRarityProbability(
  baseWeights: { [key in ItemRarity]: number },
  appliedItems: PlantingItem[]
): { [key in ItemRarity]: number } {
  const modifiedWeights = { ...baseWeights }
  
  // 应用道具效果
  appliedItems.forEach(item => {
    if (item.effects.rarityBoost) {
      // 提升稀有品质的概率
      const boost = item.effects.rarityBoost / 100
      modifiedWeights[ItemRarity.BLUE] *= (1 + boost)
      modifiedWeights[ItemRarity.ORANGE] *= (1 + boost)
      modifiedWeights[ItemRarity.GOLD] *= (1 + boost)
      modifiedWeights[ItemRarity.GOLD_RED] *= (1 + boost * 2)
    }
  })
  
  return modifiedWeights
} 