import { ItemRarity, ItemCategory, ItemType } from '../types/lootbox'

// 中国期货市场农产品品种
export interface ChineseFuturesProduct {
  id: string
  name: string
  nameEn: string
  category: 'grain' | 'oilseed' | 'fiber' | 'sugar' | 'fruit' | 'livestock' | 'feed'
  exchange: 'DCE' | 'CZCE' | 'SHFE' | 'CFFEX' | 'INE' // 交易所
  icon: string
  basePrice: number
  description: string
  
  // 不同品质的产量范围
  yieldRanges: {
    [ItemRarity.GRAY]: { min: number; max: number }
    [ItemRarity.GREEN]: { min: number; max: number }
    [ItemRarity.BLUE]: { min: number; max: number }
    [ItemRarity.ORANGE]: { min: number; max: number }
    [ItemRarity.GOLD]: { min: number; max: number }
    [ItemRarity.GOLD_RED]: { min: number; max: number }
  }
}

// 中国期货市场主要农产品数据
export const CHINESE_FUTURES_PRODUCTS: ChineseFuturesProduct[] = [
  // ==================== 谷物类 (DCE大连商品交易所) ====================
  {
    id: 'corn',
    name: '玉米',
    nameEn: 'Corn',
    category: 'grain',
    exchange: 'DCE',
    icon: '🌽',
    basePrice: 2800,
    description: '中国第一大粮食作物，主要用于饲料和工业原料',
    yieldRanges: {
      [ItemRarity.GRAY]: { min: 400, max: 500 },     // 400-500公斤/亩
      [ItemRarity.GREEN]: { min: 500, max: 600 },    // 500-600公斤/亩
      [ItemRarity.BLUE]: { min: 600, max: 720 },     // 600-720公斤/亩
      [ItemRarity.ORANGE]: { min: 720, max: 850 },   // 720-850公斤/亩
      [ItemRarity.GOLD]: { min: 850, max: 1000 },    // 850-1000公斤/亩
      [ItemRarity.GOLD_RED]: { min: 1000, max: 1200 } // 1000-1200公斤/亩
    }
  },
  {
    id: 'soybean',
    name: '大豆',
    nameEn: 'Soybean',
    category: 'oilseed',
    exchange: 'DCE',
    icon: '🫘',
    basePrice: 4200,
    description: '重要的油料作物和蛋白质来源',
    yieldRanges: {
      [ItemRarity.GRAY]: { min: 120, max: 150 },
      [ItemRarity.GREEN]: { min: 150, max: 180 },
      [ItemRarity.BLUE]: { min: 180, max: 220 },
      [ItemRarity.ORANGE]: { min: 220, max: 260 },
      [ItemRarity.GOLD]: { min: 260, max: 300 },
      [ItemRarity.GOLD_RED]: { min: 300, max: 350 }
    }
  },

  // ==================== 谷物类 (CZCE郑州商品交易所) ====================
  {
    id: 'wheat',
    name: '小麦',
    nameEn: 'Wheat',
    category: 'grain',
    exchange: 'CZCE',
    icon: '🌾',
    basePrice: 2600,
    description: '主要的粮食作物，制作面粉的原料',
    yieldRanges: {
      [ItemRarity.GRAY]: { min: 300, max: 400 },
      [ItemRarity.GREEN]: { min: 400, max: 500 },
      [ItemRarity.BLUE]: { min: 500, max: 600 },
      [ItemRarity.ORANGE]: { min: 600, max: 720 },
      [ItemRarity.GOLD]: { min: 720, max: 850 },
      [ItemRarity.GOLD_RED]: { min: 850, max: 1000 }
    }
  },

  // ==================== 油料作物 ====================
  {
    id: 'rapeseed',
    name: '菜籽',
    nameEn: 'Rapeseed',
    category: 'oilseed',
    exchange: 'CZCE',
    icon: '🌻',
    basePrice: 5200,
    description: '重要的油料作物，菜籽油原料',
    yieldRanges: {
      [ItemRarity.GRAY]: { min: 100, max: 130 },
      [ItemRarity.GREEN]: { min: 130, max: 160 },
      [ItemRarity.BLUE]: { min: 160, max: 200 },
      [ItemRarity.ORANGE]: { min: 200, max: 240 },
      [ItemRarity.GOLD]: { min: 240, max: 280 },
      [ItemRarity.GOLD_RED]: { min: 280, max: 330 }
    }
  },
  {
    id: 'peanut',
    name: '花生',
    nameEn: 'Peanut',
    category: 'oilseed',
    exchange: 'CZCE',
    icon: '🥜',
    basePrice: 7800,
    description: '重要的经济作物，可榨油也可食用',
    yieldRanges: {
      [ItemRarity.GRAY]: { min: 200, max: 250 },
      [ItemRarity.GREEN]: { min: 250, max: 300 },
      [ItemRarity.BLUE]: { min: 300, max: 360 },
      [ItemRarity.ORANGE]: { min: 360, max: 430 },
      [ItemRarity.GOLD]: { min: 430, max: 500 },
      [ItemRarity.GOLD_RED]: { min: 500, max: 580 }
    }
  },

  // ==================== 纤维作物 ====================
  {
    id: 'cotton',
    name: '棉花',
    nameEn: 'Cotton',
    category: 'fiber',
    exchange: 'CZCE',
    icon: '☁️',
    basePrice: 15000,
    description: '重要的纤维作物，纺织工业原料',
    yieldRanges: {
      [ItemRarity.GRAY]: { min: 80, max: 100 },
      [ItemRarity.GREEN]: { min: 100, max: 120 },
      [ItemRarity.BLUE]: { min: 120, max: 145 },
      [ItemRarity.ORANGE]: { min: 145, max: 170 },
      [ItemRarity.GOLD]: { min: 170, max: 200 },
      [ItemRarity.GOLD_RED]: { min: 200, max: 240 }
    }
  },

  // ==================== 糖料作物 ====================
  {
    id: 'sugar',
    name: '白糖',
    nameEn: 'Sugar',
    category: 'sugar',
    exchange: 'CZCE',
    icon: '🍯',
    basePrice: 5800,
    description: '甘蔗和甜菜加工制成的食用糖',
    yieldRanges: {
      [ItemRarity.GRAY]: { min: 4000, max: 5000 },   // 甘蔗产量(公斤/亩)
      [ItemRarity.GREEN]: { min: 5000, max: 6000 },
      [ItemRarity.BLUE]: { min: 6000, max: 7200 },
      [ItemRarity.ORANGE]: { min: 7200, max: 8500 },
      [ItemRarity.GOLD]: { min: 8500, max: 10000 },
      [ItemRarity.GOLD_RED]: { min: 10000, max: 12000 }
    }
  },

  // ==================== 水果类 ====================
  {
    id: 'apple',
    name: '苹果',
    nameEn: 'Apple',
    category: 'fruit',
    exchange: 'CZCE',
    icon: '🍎',
    basePrice: 8500,
    description: '优质水果，营养丰富',
    yieldRanges: {
      [ItemRarity.GRAY]: { min: 2000, max: 2500 },
      [ItemRarity.GREEN]: { min: 2500, max: 3000 },
      [ItemRarity.BLUE]: { min: 3000, max: 3600 },
      [ItemRarity.ORANGE]: { min: 3600, max: 4200 },
      [ItemRarity.GOLD]: { min: 4200, max: 5000 },
      [ItemRarity.GOLD_RED]: { min: 5000, max: 6000 }
    }
  },
  {
    id: 'red_jujube',
    name: '红枣',
    nameEn: 'Red Jujube',
    category: 'fruit',
    exchange: 'CZCE',
    icon: '🔴',
    basePrice: 12000,
    description: '传统干果，药食同源',
    yieldRanges: {
      [ItemRarity.GRAY]: { min: 800, max: 1000 },
      [ItemRarity.GREEN]: { min: 1000, max: 1200 },
      [ItemRarity.BLUE]: { min: 1200, max: 1450 },
      [ItemRarity.ORANGE]: { min: 1450, max: 1700 },
      [ItemRarity.GOLD]: { min: 1700, max: 2000 },
      [ItemRarity.GOLD_RED]: { min: 2000, max: 2400 }
    }
  },

  // ==================== 畜牧类 ====================
  {
    id: 'live_pig',
    name: '生猪',
    nameEn: 'Live Pig',
    category: 'livestock',
    exchange: 'DCE',
    icon: '🐷',
    basePrice: 16000,
    description: '重要的畜牧产品，肉类供应主力',
    yieldRanges: {
      [ItemRarity.GRAY]: { min: 90, max: 110 },      // 出栏重量(公斤/头)
      [ItemRarity.GREEN]: { min: 110, max: 130 },
      [ItemRarity.BLUE]: { min: 130, max: 150 },
      [ItemRarity.ORANGE]: { min: 150, max: 175 },
      [ItemRarity.GOLD]: { min: 175, max: 200 },
      [ItemRarity.GOLD_RED]: { min: 200, max: 230 }
    }
  },
  {
    id: 'egg',
    name: '鸡蛋',
    nameEn: 'Egg',
    category: 'livestock',
    exchange: 'DCE',
    icon: '🥚',
    basePrice: 8800,
    description: '重要的蛋白质来源',
    yieldRanges: {
      [ItemRarity.GRAY]: { min: 180, max: 220 },     // 年产蛋量(个/只)
      [ItemRarity.GREEN]: { min: 220, max: 260 },
      [ItemRarity.BLUE]: { min: 260, max: 310 },
      [ItemRarity.ORANGE]: { min: 310, max: 360 },
      [ItemRarity.GOLD]: { min: 360, max: 420 },
      [ItemRarity.GOLD_RED]: { min: 420, max: 500 }
    }
  },

  // ==================== 油脂类 (DCE大连商品交易所) ====================
  {
    id: 'soybean_oil',
    name: '豆油',
    nameEn: 'Soybean Oil',
    category: 'oilseed',
    exchange: 'DCE',
    icon: '🫗',
    basePrice: 7800,
    description: '大豆压榨提取的植物油，重要的食用油品种',
    yieldRanges: {
      [ItemRarity.GRAY]: { min: 16, max: 18 },       // 出油率(%)
      [ItemRarity.GREEN]: { min: 18, max: 19 },
      [ItemRarity.BLUE]: { min: 19, max: 20 },
      [ItemRarity.ORANGE]: { min: 20, max: 21 },
      [ItemRarity.GOLD]: { min: 21, max: 22 },
      [ItemRarity.GOLD_RED]: { min: 22, max: 24 }
    }
  },
  {
    id: 'palm_oil',
    name: '棕榈油',
    nameEn: 'Palm Oil',
    category: 'oilseed',
    exchange: 'DCE',
    icon: '🌴',
    basePrice: 6200,
    description: '棕榈果压榨提取的植物油，世界产量最大的植物油',
    yieldRanges: {
      [ItemRarity.GRAY]: { min: 15, max: 18 },       // 出油率(%)
      [ItemRarity.GREEN]: { min: 18, max: 20 },
      [ItemRarity.BLUE]: { min: 20, max: 22 },
      [ItemRarity.ORANGE]: { min: 22, max: 24 },
      [ItemRarity.GOLD]: { min: 24, max: 26 },
      [ItemRarity.GOLD_RED]: { min: 26, max: 28 }
    }
  },

  // ==================== 饲料原料 ====================
  {
    id: 'soybean_meal',
    name: '豆粕',
    nameEn: 'Soybean Meal',
    category: 'feed',
    exchange: 'DCE',
    icon: '🌰',
    basePrice: 3200,
    description: '大豆榨油后的副产品，重要的蛋白饲料',
    yieldRanges: {
      [ItemRarity.GRAY]: { min: 75, max: 80 },       // 出粕率(%)
      [ItemRarity.GREEN]: { min: 80, max: 82 },
      [ItemRarity.BLUE]: { min: 82, max: 84 },
      [ItemRarity.ORANGE]: { min: 84, max: 86 },
      [ItemRarity.GOLD]: { min: 86, max: 88 },
      [ItemRarity.GOLD_RED]: { min: 88, max: 90 }
    }
  }
]

// 根据品种ID获取产品信息
export function getFuturesProductById(id: string): ChineseFuturesProduct | undefined {
  return CHINESE_FUTURES_PRODUCTS.find(product => product.id === id)
}

// 根据品种和品质获取随机产量
export function getRandomYield(productId: string, rarity: ItemRarity): number {
  const product = getFuturesProductById(productId)
  if (!product) return 0
  
  const range = product.yieldRanges[rarity]
  return Math.floor(Math.random() * (range.max - range.min + 1)) + range.min
}

// 获取品种的品质名称
export function getQualityName(rarity: ItemRarity): string {
  const qualityNames = {
    [ItemRarity.GRAY]: '普通',
    [ItemRarity.GREEN]: '优质',
    [ItemRarity.BLUE]: '稀有',
    [ItemRarity.ORANGE]: '史诗',
    [ItemRarity.GOLD]: '传说',
    [ItemRarity.GOLD_RED]: '神话'
  }
  return qualityNames[rarity]
}

// 获取品种的完整名称（包含品质）
export function getFullProductName(productId: string, rarity: ItemRarity): string {
  const product = getFuturesProductById(productId)
  if (!product) return '未知品种'
  
  const qualityName = getQualityName(rarity)
  return `${qualityName}${product.name}`
} 