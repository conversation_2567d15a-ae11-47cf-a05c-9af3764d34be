import { ItemRarity, ItemCategory, ItemType, LootboxItem } from '../types/lootbox'

// 农产品品质产量配置
export interface ProductionConfig {
  minDaily: number      // 每日最小产量
  maxDaily: number      // 每日最大产量
  baseValue: number     // 基础价值
  qualityMultiplier: number  // 品质乘数
  growthTime: number    // 生长时间（小时）
}

// 品质等级对应的产量配置
export const QUALITY_PRODUCTION_CONFIG: Record<ItemRarity, ProductionConfig> = {
  [ItemRarity.GRAY]: {
    minDaily: 1,
    maxDaily: 3,
    baseValue: 10,
    qualityMultiplier: 1.0,
    growthTime: 24
  },
  [ItemRarity.GREEN]: {
    minDaily: 4,
    maxDaily: 6,
    baseValue: 25,
    qualityMultiplier: 1.5,
    growthTime: 20
  },
  [ItemRarity.BLUE]: {
    minDaily: 7,
    maxDaily: 10,
    baseValue: 50,
    qualityMultiplier: 2.2,
    growthTime: 16
  },
  [ItemRarity.ORANGE]: {
    minDaily: 12,
    maxDaily: 16,
    baseValue: 100,
    qualityMultiplier: 3.5,
    growthTime: 12
  },
  [ItemRarity.GOLD]: {
    minDaily: 18,
    maxDaily: 25,
    baseValue: 200,
    qualityMultiplier: 5.5,
    growthTime: 8
  },
  [ItemRarity.GOLD_RED]: {
    minDaily: 30,
    maxDaily: 40,
    baseValue: 500,
    qualityMultiplier: 8.0,
    growthTime: 4
  }
}

// 农作物种类枚举（基于中国期货市场上市品种）
export enum CropVariety {
  // 大连商品交易所（DCE）
  CORN = 'corn',             // 玉米 C
  SOYBEAN = 'soybean',       // 大豆 A
  SOYBEAN_2 = 'soybean_2',   // 黄大豆2号 B
  SOYBEAN_MEAL = 'soybean_meal', // 豆粕 M
  SOYBEAN_OIL = 'soybean_oil',   // 豆油 Y
  PALM_OIL = 'palm_oil',     // 棕榈油 P
  CORN_STARCH = 'corn_starch', // 玉米淀粉 CS
  EGG = 'egg',               // 鸡蛋 JD
  
  // 郑州商品交易所（ZCE）
  WHEAT = 'wheat',           // 强筋小麦 WH
  COMMON_WHEAT = 'common_wheat', // 普通小麦 PM
  RICE = 'rice',             // 早籼稻 RI
  LATE_RICE = 'late_rice',   // 晚籼稻 LR
  JAPONICA_RICE = 'japonica_rice', // 粳稻 JR
  CANOLA = 'canola',         // 菜籽 RS
  CANOLA_MEAL = 'canola_meal', // 菜粕 RM
  CANOLA_OIL = 'canola_oil', // 菜籽油 OI
  COTTON = 'cotton',         // 棉花 CF
  COTTON_YARN = 'cotton_yarn', // 棉纱 CY
  WHITE_SUGAR = 'white_sugar', // 白糖 SR
  APPLE = 'apple',           // 苹果 AP
  RED_JUJUBE = 'red_jujube', // 红枣 CJ
  
  // 上海期货交易所（SHFE）
  NATURAL_RUBBER = 'natural_rubber', // 天然橡胶 RU
  
  // 广州期货交易所（GFEX）
  // 目前主要是工业品，农产品较少
  
  // 传统农产品（非期货品种）
  TOMATO = 'tomato',         // 番茄
  CARROT = 'carrot',         // 胡萝卜
  CABBAGE = 'cabbage',       // 卷心菜
  STRAWBERRY = 'strawberry', // 草莓
  GRAPE = 'grape',           // 葡萄
  TEA = 'tea',               // 茶叶
  COFFEE = 'coffee',         // 咖啡
  PEANUT = 'peanut',         // 花生
  SESAME = 'sesame',         // 芝麻
  SUNFLOWER = 'sunflower'    // 向日葵
}

// 牲畜种类枚举（包含期货品种）
export enum LivestockVariety {
  CHICKEN = 'chicken',       // 鸡（对应鸡蛋期货）
  PIG = 'pig',               // 猪（生猪期货）
  COW = 'cow',               // 牛
  SHEEP = 'sheep',           // 羊
  DUCK = 'duck',             // 鸭
  GOOSE = 'goose'            // 鹅
}

// 农产品基础配置
interface BaseAgriculturalConfig {
  variety: CropVariety | LivestockVariety
  name: string
  description: string
  category: 'crop' | 'livestock'
  baseIcon: string
  specialBonuses?: {
    weatherBonus?: number    // 天气加成
    seasonBonus?: number     // 季节加成
    skillBonus?: number      // 技能加成
  }
}

// 种子/幼畜配置
export const SEED_LIVESTOCK_CONFIGS: Record<string, BaseAgriculturalConfig> = {
  // 作物种子
  wheat_seed: {
    variety: CropVariety.WHEAT,
    name: '小麦种子',
    description: '基础粮食作物，生长稳定，适合新手',
    category: 'crop',
    baseIcon: '🌾'
  },
  rice_seed: {
    variety: CropVariety.RICE,
    name: '水稻种子',
    description: '需要充足水分，产量丰富',
    category: 'crop',
    baseIcon: '🌾'
  },
  corn_seed: {
    variety: CropVariety.CORN,
    name: '玉米种子',
    description: '高产作物，营养价值高',
    category: 'crop',
    baseIcon: '🌽'
  },
  tomato_seed: {
    variety: CropVariety.TOMATO,
    name: '番茄种子',
    description: '多汁美味，市场需求大',
    category: 'crop',
    baseIcon: '🍅'
  },
  carrot_seed: {
    variety: CropVariety.CARROT,
    name: '胡萝卜种子',
    description: '营养丰富，生长周期短',
    category: 'crop',
    baseIcon: '🥕'
  },
  cabbage_seed: {
    variety: CropVariety.CABBAGE,
    name: '卷心菜种子',
    description: '耐储存，四季可种',
    category: 'crop',
    baseIcon: '🥬'
  },
  apple_seed: {
    variety: CropVariety.APPLE,
    name: '苹果树苗',
    description: '果树类作物，产量持久',
    category: 'crop',
    baseIcon: '🍎',
    specialBonuses: {
      seasonBonus: 0.2
    }
  },
  strawberry_seed: {
    variety: CropVariety.STRAWBERRY,
    name: '草莓种子',
    description: '高价值浆果，生长迅速',
    category: 'crop',
    baseIcon: '🍓'
  },
  grape_seed: {
    variety: CropVariety.GRAPE,
    name: '葡萄藤苗',
    description: '可加工成高价值产品',
    category: 'crop',
    baseIcon: '🍇',
    specialBonuses: {
      skillBonus: 0.3
    }
  },
  cotton_seed: {
    variety: CropVariety.COTTON,
    name: '棉花种子',
    description: '经济作物，工业原料',
    category: 'crop',
    baseIcon: '🌱'
  },
  tea_seed: {
    variety: CropVariety.TEA,
    name: '茶树苗',
    description: '高端经济作物，需要技巧',
    category: 'crop',
    baseIcon: '🍃',
    specialBonuses: {
      skillBonus: 0.4,
      weatherBonus: 0.1
    }
  },
  coffee_seed: {
    variety: CropVariety.COFFEE,
    name: '咖啡树苗',
    description: '珍贵经济作物，极高价值',
    category: 'crop',
    baseIcon: '☕',
    specialBonuses: {
      skillBonus: 0.5,
      seasonBonus: 0.3
    }
  },
  
  // 幼畜
  chicken_chick: {
    variety: LivestockVariety.CHICKEN,
    name: '小鸡',
    description: '成长快速，产蛋稳定',
    category: 'livestock',
    baseIcon: '🐣'
  },
  cow_calf: {
    variety: LivestockVariety.COW,
    name: '小牛',
    description: '大型牲畜，产奶量高',
    category: 'livestock',
    baseIcon: '🐄',
    specialBonuses: {
      weatherBonus: 0.15
    }
  },
  pig_piglet: {
    variety: LivestockVariety.PIG,
    name: '小猪',
    description: '生长迅速，肉质优良',
    category: 'livestock',
    baseIcon: '🐷'
  },
  sheep_lamb: {
    variety: LivestockVariety.SHEEP,
    name: '羊羔',
    description: '产毛产肉双重收益',
    category: 'livestock',
    baseIcon: '🐑',
    specialBonuses: {
      seasonBonus: 0.25
    }
  },
  duck_duckling: {
    variety: LivestockVariety.DUCK,
    name: '小鸭',
    description: '水禽类，适应性强',
    category: 'livestock',
    baseIcon: '🦆'
  },
  goose_gosling: {
    variety: LivestockVariety.GOOSE,
    name: '小鹅',
    description: '大型水禽，高价值产品',
    category: 'livestock',
    baseIcon: '🪿',
    specialBonuses: {
      skillBonus: 0.2
    }
  }
}

// 生成完整的农产品物品配置
export function generateAgriculturalItems(): LootboxItem[] {
  const items: LootboxItem[] = []
  
  // 为每个品质等级和每种农产品生成配置
  Object.entries(SEED_LIVESTOCK_CONFIGS).forEach(([key, config]) => {
    Object.values(ItemRarity).forEach(rarity => {
      const production = QUALITY_PRODUCTION_CONFIG[rarity]
      const rarityName = getRarityName(rarity)
      
      // 生成种子/幼畜
      const seedItem: LootboxItem = {
        id: `${key}_${rarity}`,
        name: `${rarityName}${config.name}`,
        description: `${config.description} (品质: ${rarityName})`,
        category: ItemCategory.AGRICULTURAL,
        type: config.category === 'crop' ? ItemType.SEED : ItemType.LIVESTOCK,
        rarity: rarity,
        icon: config.baseIcon,
        value: production.baseValue,
        stackable: true,
        tradeable: true,
        synthesizable: true,
        metadata: {
          yieldMultiplier: production.qualityMultiplier,
          growthSpeed: 24 / production.growthTime, // 转换为速度倍数
          qualityBonus: (production.qualityMultiplier - 1) * 100,
          effects: [],
          duration: production.growthTime * 3600000, // 转换为毫秒
          synthesisRecipes: getUpgradeRecipes(rarity),
          futuresPrice: production.baseValue * production.qualityMultiplier,
          priceVolatility: getPriceVolatility(rarity),
          marketDemand: getMarketDemand(config.variety, rarity)
        }
      }
      
      items.push(seedItem)
      
      // 生成对应的产品（农作物/畜产品）
      const productName = getProductName(config)
      const productItem: LootboxItem = {
        id: `${key.replace('_seed', '').replace('_chick', '').replace('_calf', '').replace('_piglet', '').replace('_lamb', '').replace('_duckling', '').replace('_gosling', '')}_product_${rarity}`,
        name: `${rarityName}${productName}`,
        description: `由${rarityName}${config.name}生产的${productName}`,
        category: ItemCategory.AGRICULTURAL,
        type: ItemType.CROP,
        rarity: rarity,
        icon: getProductIcon(config),
        value: production.baseValue * 2, // 产品价值是种子的2倍
        stackable: true,
        tradeable: true,
        synthesizable: false, // 产品不能合成
        metadata: {
          yieldMultiplier: production.qualityMultiplier,
          effects: [`每日产量: ${production.minDaily}-${production.maxDaily}`],
          futuresPrice: production.baseValue * production.qualityMultiplier * 2,
          priceVolatility: getPriceVolatility(rarity) * 1.5, // 产品价格波动更大
          marketDemand: getMarketDemand(config.variety, rarity) * 1.2
        }
      }
      
      items.push(productItem)
    })
  })
  
  return items
}

// 辅助函数
function getRarityName(rarity: ItemRarity): string {
  const names = {
    [ItemRarity.GRAY]: '普通',
    [ItemRarity.GREEN]: '优良',
    [ItemRarity.BLUE]: '稀有',
    [ItemRarity.ORANGE]: '史诗',
    [ItemRarity.GOLD]: '传说',
    [ItemRarity.GOLD_RED]: '神话'
  }
  return names[rarity]
}

function getUpgradeRecipes(rarity: ItemRarity): string[] {
  // 根据品质返回可用的升级配方
  switch (rarity) {
    case ItemRarity.GRAY:
      return ['gray_to_green']
    case ItemRarity.GREEN:
      return ['green_to_blue']
    case ItemRarity.BLUE:
      return ['blue_to_orange']
    case ItemRarity.ORANGE:
      return ['orange_to_gold']
    case ItemRarity.GOLD:
      return ['gold_to_golden_red']
    default:
      return []
  }
}

function getPriceVolatility(rarity: ItemRarity): number {
  // 品质越高，价格波动越大
  const volatility = {
    [ItemRarity.GRAY]: 0.1,      // 10%
    [ItemRarity.GREEN]: 0.15,    // 15%
    [ItemRarity.BLUE]: 0.2,      // 20%
    [ItemRarity.ORANGE]: 0.3,    // 30%
    [ItemRarity.GOLD]: 0.4,      // 40%
    [ItemRarity.GOLD_RED]: 0.5   // 50%
  }
  return volatility[rarity]
}

function getMarketDemand(variety: CropVariety | LivestockVariety, rarity: ItemRarity): number {
  // 基础需求根据品种类型
  const baseDemand = {
    // 粮食作物 - 基础需求高
    [CropVariety.WHEAT]: 1.0,
    [CropVariety.RICE]: 1.0,
    [CropVariety.CORN]: 0.9,
    
    // 蔬菜作物 - 中等需求
    [CropVariety.TOMATO]: 0.8,
    [CropVariety.CARROT]: 0.7,
    [CropVariety.CABBAGE]: 0.6,
    
    // 水果作物 - 高价值需求
    [CropVariety.APPLE]: 0.9,
    [CropVariety.STRAWBERRY]: 1.2,
    [CropVariety.GRAPE]: 1.1,
    
    // 经济作物 - 特殊需求
    [CropVariety.COTTON]: 0.5,
    [CropVariety.TEA]: 1.5,
    [CropVariety.COFFEE]: 2.0,
    
    // 牲畜产品 - 稳定需求
    [LivestockVariety.CHICKEN]: 1.1,
    [LivestockVariety.COW]: 1.3,
    [LivestockVariety.PIG]: 1.0,
    [LivestockVariety.SHEEP]: 0.8,
    [LivestockVariety.DUCK]: 0.9,
    [LivestockVariety.GOOSE]: 1.2
  }
  
  // 品质加成
  const qualityMultiplier = {
    [ItemRarity.GRAY]: 1.0,
    [ItemRarity.GREEN]: 1.2,
    [ItemRarity.BLUE]: 1.5,
    [ItemRarity.ORANGE]: 2.0,
    [ItemRarity.GOLD]: 3.0,
    [ItemRarity.GOLD_RED]: 5.0
  }
  
  return (baseDemand[variety] || 1.0) * qualityMultiplier[rarity]
}

function getProductName(config: BaseAgriculturalConfig): string {
  if (config.category === 'livestock') {
    const productNames = {
      [LivestockVariety.CHICKEN]: '鸡蛋',
      [LivestockVariety.COW]: '牛奶',
      [LivestockVariety.PIG]: '猪肉',
      [LivestockVariety.SHEEP]: '羊毛',
      [LivestockVariety.DUCK]: '鸭蛋',
      [LivestockVariety.GOOSE]: '鹅蛋'
    }
    return productNames[config.variety as LivestockVariety] || '畜产品'
  } else {
    // 作物直接使用作物名
    const cropNames = {
      [CropVariety.WHEAT]: '小麦',
      [CropVariety.RICE]: '大米',
      [CropVariety.CORN]: '玉米',
      [CropVariety.TOMATO]: '番茄',
      [CropVariety.CARROT]: '胡萝卜',
      [CropVariety.CABBAGE]: '卷心菜',
      [CropVariety.APPLE]: '苹果',
      [CropVariety.STRAWBERRY]: '草莓',
      [CropVariety.GRAPE]: '葡萄',
      [CropVariety.COTTON]: '棉花',
      [CropVariety.TEA]: '茶叶',
      [CropVariety.COFFEE]: '咖啡豆'
    }
    return cropNames[config.variety as CropVariety] || '农产品'
  }
}

function getProductIcon(config: BaseAgriculturalConfig): string {
  if (config.category === 'livestock') {
    const productIcons = {
      [LivestockVariety.CHICKEN]: '🥚',
      [LivestockVariety.COW]: '🥛',
      [LivestockVariety.PIG]: '🥓',
      [LivestockVariety.SHEEP]: '🧶',
      [LivestockVariety.DUCK]: '🥚',
      [LivestockVariety.GOOSE]: '🥚'
    }
    return productIcons[config.variety as LivestockVariety] || '🥩'
  } else {
    // 作物产品图标
    const productIcons = {
      [CropVariety.WHEAT]: '🌾',
      [CropVariety.RICE]: '🍚',
      [CropVariety.CORN]: '🌽',
      [CropVariety.TOMATO]: '🍅',
      [CropVariety.CARROT]: '🥕',
      [CropVariety.CABBAGE]: '🥬',
      [CropVariety.APPLE]: '🍎',
      [CropVariety.STRAWBERRY]: '🍓',
      [CropVariety.GRAPE]: '🍇',
      [CropVariety.COTTON]: '☁️',
      [CropVariety.TEA]: '🍵',
      [CropVariety.COFFEE]: '☕'
    }
    return productIcons[config.variety as CropVariety] || '🌱'
  }
}

// 品质升级合成配置（2个同品质合成1个更高品质）
export const AGRICULTURAL_SYNTHESIS_RECIPES = [
  {
    id: 'agricultural_gray_to_green',
    name: '农产品品质提升（绿色）',
    description: '将2个普通农产品合成为1个优良农产品',
    requiredItems: [
      {
        rarity: ItemRarity.GRAY,
        quantity: 2,
        category: ItemCategory.AGRICULTURAL
      }
    ],
    resultRarity: ItemRarity.GREEN,
    successRate: 0.95,
    requiresSameType: true // 需要相同类型的物品
  },
  {
    id: 'agricultural_green_to_blue',
    name: '农产品品质提升（蓝色）',
    description: '将2个优良农产品合成为1个稀有农产品',
    requiredItems: [
      {
        rarity: ItemRarity.GREEN,
        quantity: 2,
        category: ItemCategory.AGRICULTURAL
      }
    ],
    resultRarity: ItemRarity.BLUE,
    successRate: 0.90,
    requiresSameType: true
  },
  {
    id: 'agricultural_blue_to_orange',
    name: '农产品品质提升（橙色）',
    description: '将2个稀有农产品合成为1个史诗农产品',
    requiredItems: [
      {
        rarity: ItemRarity.BLUE,
        quantity: 2,
        category: ItemCategory.AGRICULTURAL
      }
    ],
    resultRarity: ItemRarity.ORANGE,
    successRate: 0.85,
    requiresSameType: true
  },
  {
    id: 'agricultural_orange_to_gold',
    name: '农产品品质提升（金色）',
    description: '将2个史诗农产品合成为1个传说农产品',
    requiredItems: [
      {
        rarity: ItemRarity.ORANGE,
        quantity: 2,
        category: ItemCategory.AGRICULTURAL
      }
    ],
    resultRarity: ItemRarity.GOLD,
    successRate: 0.75,
    requiresSameType: true
  },
  {
    id: 'agricultural_gold_to_gold_red',
    name: '农产品品质提升（金红色）',
    description: '将2个传说农产品合成为1个神话农产品',
    requiredItems: [
      {
        rarity: ItemRarity.GOLD,
        quantity: 2,
        category: ItemCategory.AGRICULTURAL
      }
    ],
    resultRarity: ItemRarity.GOLD_RED,
    successRate: 0.60,
    requiresSameType: true
  }
]

// 导出所有农产品数据
export const AGRICULTURAL_ITEMS = generateAgriculturalItems() 