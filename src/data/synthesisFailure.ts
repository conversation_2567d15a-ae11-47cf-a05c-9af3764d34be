import { ItemRarity } from '../types/lootbox'

// 合成失败后果类型
export enum FailureConsequenceType {
  KEEP_MATERIALS = 'keep_materials',           // 保留原材料
  LOSE_ONE_ITEM = 'lose_one_item',            // 丢失一个物品
  LOSE_ALL_ITEMS = 'lose_all_items',          // 丢失所有物品
  GET_PROTECTION_STONE = 'get_protection_stone', // 获得保护石
  DOWNGRADE_QUALITY = 'downgrade_quality',     // 品质降级
  GET_COMPENSATION = 'get_compensation'        // 获得补偿物品
}

// 合成失败配置
export interface SynthesisFailureConfig {
  rarity: ItemRarity
  failureRate: number                          // 失败概率（对应成功率）
  consequences: {
    type: FailureConsequenceType
    probability: number                        // 该后果的概率
    description: string
    details?: {
      itemsLost?: number                      // 丢失物品数量
      compensationValue?: number              // 补偿价值百分比
      protectionStoneAmount?: number          // 保护石数量
      downgradeLevels?: number               // 降级等级数
    }
  }[]
  protectionMechanisms?: {
    protectionStoneRequired?: boolean         // 是否需要保护石
    protectionStoneConsumed?: boolean        // 保护石是否消耗
    alternativeProtection?: string[]         // 其他保护方式
  }
}

// 不同品质的合成失败配置
export const SYNTHESIS_FAILURE_CONFIGS: Record<ItemRarity, SynthesisFailureConfig> = {
  [ItemRarity.GRAY]: {
    rarity: ItemRarity.GRAY,
    failureRate: 0.05, // 5%失败率（95%成功率）
    consequences: [
      {
        type: FailureConsequenceType.KEEP_MATERIALS,
        probability: 1.0, // 100%保留原材料
        description: '合成失败，但所有材料都被保留',
        details: {}
      }
    ]
  },

  [ItemRarity.GREEN]: {
    rarity: ItemRarity.GREEN,
    failureRate: 0.10, // 10%失败率（90%成功率）
    consequences: [
      {
        type: FailureConsequenceType.KEEP_MATERIALS,
        probability: 0.8, // 80%保留原材料
        description: '合成失败，但所有材料都被保留'
      },
      {
        type: FailureConsequenceType.LOSE_ONE_ITEM,
        probability: 0.2, // 20%丢失一个物品
        description: '合成失败，丢失一个原材料',
        details: {
          itemsLost: 1
        }
      }
    ]
  },

  [ItemRarity.BLUE]: {
    rarity: ItemRarity.BLUE,
    failureRate: 0.15, // 15%失败率（85%成功率）
    consequences: [
      {
        type: FailureConsequenceType.KEEP_MATERIALS,
        probability: 0.6, // 60%保留原材料
        description: '合成失败，但所有材料都被保留'
      },
      {
        type: FailureConsequenceType.LOSE_ONE_ITEM,
        probability: 0.3, // 30%丢失一个物品
        description: '合成失败，丢失一个原材料',
        details: {
          itemsLost: 1
        }
      },
      {
        type: FailureConsequenceType.GET_COMPENSATION,
        probability: 0.1, // 10%获得补偿
        description: '合成失败，获得等值货币补偿',
        details: {
          compensationValue: 50 // 50%价值补偿
        }
      }
    ],
    protectionMechanisms: {
      protectionStoneRequired: false,
      alternativeProtection: ['幸运符咒', '合成保险']
    }
  },

  [ItemRarity.ORANGE]: {
    rarity: ItemRarity.ORANGE,
    failureRate: 0.25, // 25%失败率（75%成功率）
    consequences: [
      {
        type: FailureConsequenceType.KEEP_MATERIALS,
        probability: 0.4, // 40%保留原材料
        description: '合成失败，但所有材料都被保留'
      },
      {
        type: FailureConsequenceType.LOSE_ONE_ITEM,
        probability: 0.4, // 40%丢失一个物品
        description: '合成失败，丢失一个原材料',
        details: {
          itemsLost: 1
        }
      },
      {
        type: FailureConsequenceType.DOWNGRADE_QUALITY,
        probability: 0.15, // 15%品质降级
        description: '合成失败，获得降级品质的物品',
        details: {
          downgradeLevels: 1
        }
      },
      {
        type: FailureConsequenceType.GET_PROTECTION_STONE,
        probability: 0.05, // 5%获得保护石
        description: '合成失败，但获得保护石作为补偿',
        details: {
          protectionStoneAmount: 1
        }
      }
    ],
    protectionMechanisms: {
      protectionStoneRequired: true,
      protectionStoneConsumed: false, // 使用但不消耗
      alternativeProtection: ['高级幸运符咒', '贵族合成保险']
    }
  },

  [ItemRarity.GOLD]: {
    rarity: ItemRarity.GOLD,
    failureRate: 0.25, // 25%失败率（75%成功率）
    consequences: [
      {
        type: FailureConsequenceType.KEEP_MATERIALS,
        probability: 0.2, // 20%保留原材料
        description: '合成失败，但所有材料都被保留'
      },
      {
        type: FailureConsequenceType.LOSE_ONE_ITEM,
        probability: 0.5, // 50%丢失一个物品
        description: '合成失败，丢失一个原材料',
        details: {
          itemsLost: 1
        }
      },
      {
        type: FailureConsequenceType.LOSE_ALL_ITEMS,
        probability: 0.1, // 10%丢失所有物品
        description: '合成失败，丢失所有原材料',
        details: {
          itemsLost: 2
        }
      },
      {
        type: FailureConsequenceType.DOWNGRADE_QUALITY,
        probability: 0.15, // 15%品质降级
        description: '合成失败，获得降级品质的物品',
        details: {
          downgradeLevels: 2
        }
      },
      {
        type: FailureConsequenceType.GET_PROTECTION_STONE,
        probability: 0.05, // 5%获得保护石
        description: '合成失败，但获得保护石作为补偿',
        details: {
          protectionStoneAmount: 2
        }
      }
    ],
    protectionMechanisms: {
      protectionStoneRequired: true,
      protectionStoneConsumed: true, // 使用且消耗
      alternativeProtection: ['传说幸运符咒', '皇室合成保险']
    }
  },

  [ItemRarity.GOLD_RED]: {
    rarity: ItemRarity.GOLD_RED,
    failureRate: 0.40, // 40%失败率（60%成功率）
    consequences: [
      {
        type: FailureConsequenceType.KEEP_MATERIALS,
        probability: 0.1, // 10%保留原材料
        description: '合成失败，但所有材料都被保留'
      },
      {
        type: FailureConsequenceType.LOSE_ONE_ITEM,
        probability: 0.4, // 40%丢失一个物品
        description: '合成失败，丢失一个原材料',
        details: {
          itemsLost: 1
        }
      },
      {
        type: FailureConsequenceType.LOSE_ALL_ITEMS,
        probability: 0.3, // 30%丢失所有物品
        description: '合成失败，丢失所有原材料',
        details: {
          itemsLost: 2
        }
      },
      {
        type: FailureConsequenceType.DOWNGRADE_QUALITY,
        probability: 0.15, // 15%品质降级
        description: '合成失败，获得降级品质的物品',
        details: {
          downgradeLevels: 3
        }
      },
      {
        type: FailureConsequenceType.GET_PROTECTION_STONE,
        probability: 0.05, // 5%获得保护石
        description: '合成失败，但获得高级保护石作为补偿',
        details: {
          protectionStoneAmount: 3
        }
      }
    ],
    protectionMechanisms: {
      protectionStoneRequired: true,
      protectionStoneConsumed: true, // 使用且消耗
      alternativeProtection: ['神话幸运符咒', '帝王合成保险', '天道庇护']
    }
  }
}

// 保护物品类型
export enum ProtectionItemType {
  PROTECTION_STONE = 'protection_stone',       // 保护石
  LUCKY_CHARM = 'lucky_charm',                // 幸运符咒
  SYNTHESIS_INSURANCE = 'synthesis_insurance', // 合成保险
  DIVINE_BLESSING = 'divine_blessing'          // 天道庇护
}

// 保护物品配置
export interface ProtectionItemConfig {
  id: string
  name: string
  description: string
  type: ProtectionItemType
  rarity: ItemRarity
  effects: {
    preventFailure?: boolean                   // 防止失败
    preventItemLoss?: boolean                 // 防止物品丢失
    improveSucessRate?: number               // 提升成功率
    guaranteeKeepMaterials?: boolean         // 保证保留材料
  }
  cost: {
    currency: string
    amount: number
  }
  availability: {
    purchasable: boolean                     // 是否可购买
    eventReward: boolean                     // 是否活动奖励
    dailyLimit?: number                      // 每日限购
  }
}

// 保护物品配置列表
export const PROTECTION_ITEMS: ProtectionItemConfig[] = [
  {
    id: 'basic_protection_stone',
    name: '初级保护石',
    description: '防止合成失败时丢失所有材料',
    type: ProtectionItemType.PROTECTION_STONE,
    rarity: ItemRarity.GREEN,
    effects: {
      guaranteeKeepMaterials: true
    },
    cost: {
      currency: 'gold',
      amount: 100
    },
    availability: {
      purchasable: true,
      eventReward: false,
      dailyLimit: 10
    }
  },
  {
    id: 'advanced_protection_stone',
    name: '高级保护石',
    description: '防止合成失败时丢失材料，并提升成功率',
    type: ProtectionItemType.PROTECTION_STONE,
    rarity: ItemRarity.BLUE,
    effects: {
      guaranteeKeepMaterials: true,
      improveSucessRate: 0.1 // 提升10%成功率
    },
    cost: {
      currency: 'gold',
      amount: 500
    },
    availability: {
      purchasable: true,
      eventReward: true,
      dailyLimit: 5
    }
  },
  {
    id: 'perfect_protection_stone',
    name: '完美保护石',
    description: '完全防止合成失败',
    type: ProtectionItemType.PROTECTION_STONE,
    rarity: ItemRarity.GOLD,
    effects: {
      preventFailure: true
    },
    cost: {
      currency: 'diamond',
      amount: 10
    },
    availability: {
      purchasable: true,
      eventReward: true,
      dailyLimit: 1
    }
  },
  {
    id: 'lucky_charm_basic',
    name: '基础幸运符咒',
    description: '提升合成成功率15%',
    type: ProtectionItemType.LUCKY_CHARM,
    rarity: ItemRarity.GREEN,
    effects: {
      improveSucessRate: 0.15
    },
    cost: {
      currency: 'gold',
      amount: 200
    },
    availability: {
      purchasable: true,
      eventReward: true,
      dailyLimit: 5
    }
  },
  {
    id: 'synthesis_insurance_premium',
    name: '高级合成保险',
    description: '失败时返还70%材料价值',
    type: ProtectionItemType.SYNTHESIS_INSURANCE,
    rarity: ItemRarity.ORANGE,
    effects: {
      preventItemLoss: true
    },
    cost: {
      currency: 'gold',
      amount: 1000
    },
    availability: {
      purchasable: true,
      eventReward: false,
      dailyLimit: 3
    }
  }
]

// 合成失败处理函数类型
export interface FailureResult {
  consequence: FailureConsequenceType
  itemsLost: number
  itemsKept: number
  compensation?: {
    type: 'currency' | 'item'
    amount: number
    description: string
  }
  message: string
}

// 计算合成失败结果
export function calculateFailureResult(
  rarity: ItemRarity,
  hasProtection: boolean = false,
  protectionType?: ProtectionItemType
): FailureResult {
  const config = SYNTHESIS_FAILURE_CONFIGS[rarity]
  
  // 如果有完美保护，直接成功
  if (hasProtection && protectionType === ProtectionItemType.PROTECTION_STONE) {
    return {
      consequence: FailureConsequenceType.KEEP_MATERIALS,
      itemsLost: 0,
      itemsKept: 2,
      message: '保护石生效，所有材料保留'
    }
  }
  
  // 随机选择失败后果
  const random = Math.random()
  let accumulatedProbability = 0
  
  for (const consequence of config.consequences) {
    accumulatedProbability += consequence.probability
    if (random <= accumulatedProbability) {
      const result: FailureResult = {
        consequence: consequence.type,
        itemsLost: 0,
        itemsKept: 2,
        message: consequence.description
      }
      
      switch (consequence.type) {
        case FailureConsequenceType.KEEP_MATERIALS:
          result.itemsLost = 0
          result.itemsKept = 2
          break
          
        case FailureConsequenceType.LOSE_ONE_ITEM:
          result.itemsLost = consequence.details?.itemsLost || 1
          result.itemsKept = 2 - result.itemsLost
          break
          
        case FailureConsequenceType.LOSE_ALL_ITEMS:
          result.itemsLost = 2
          result.itemsKept = 0
          break
          
        case FailureConsequenceType.GET_COMPENSATION:
          result.itemsLost = 2
          result.itemsKept = 0
          result.compensation = {
            type: 'currency',
            amount: consequence.details?.compensationValue || 50,
            description: `获得${consequence.details?.compensationValue || 50}%价值的金币补偿`
          }
          break
          
        case FailureConsequenceType.DOWNGRADE_QUALITY:
          result.itemsLost = 2
          result.itemsKept = 0
          result.compensation = {
            type: 'item',
            amount: 1,
            description: `获得降级${consequence.details?.downgradeLevels || 1}级品质的物品`
          }
          break
          
        case FailureConsequenceType.GET_PROTECTION_STONE:
          result.itemsLost = 2
          result.itemsKept = 0
          result.compensation = {
            type: 'item',
            amount: consequence.details?.protectionStoneAmount || 1,
            description: `获得${consequence.details?.protectionStoneAmount || 1}个保护石`
          }
          break
      }
      
      return result
    }
  }
  
  // 默认情况（不应该到达这里）
  return {
    consequence: FailureConsequenceType.KEEP_MATERIALS,
    itemsLost: 0,
    itemsKept: 2,
    message: '合成失败，材料保留'
  }
}

// 获取保护建议
export function getProtectionAdvice(rarity: ItemRarity): string[] {
  const config = SYNTHESIS_FAILURE_CONFIGS[rarity]
  const advice: string[] = []
  
  advice.push(`${rarity}品质合成失败率: ${(config.failureRate * 100).toFixed(1)}%`)
  
  if (config.protectionMechanisms?.protectionStoneRequired) {
    advice.push('建议使用保护石以降低损失风险')
  }
  
  if (config.protectionMechanisms?.alternativeProtection) {
    advice.push(`推荐保护方式: ${config.protectionMechanisms.alternativeProtection.join(', ')}`)
  }
  
  // 计算最坏情况概率
  const worstCaseProb = config.consequences
    .filter(c => c.type === FailureConsequenceType.LOSE_ALL_ITEMS)
    .reduce((sum, c) => sum + c.probability, 0)
  
  if (worstCaseProb > 0) {
    advice.push(`注意: 有${(worstCaseProb * 100).toFixed(1)}%概率失去所有材料`)
  }
  
  return advice
} 