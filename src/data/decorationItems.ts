import { 
  DecorationItem, 
  DecorationType, 
  DecorationEffectType,
  UnlockConditionType,
  InteractionType,
  ThemeStyle,
  FarmTheme
} from '../types/decoration'
import { ItemRarity, ItemCategory } from '../types/lootbox'

// 基础装饰道具配置
export const DECORATION_ITEMS: DecorationItem[] = [
  // 围栏类装饰
  {
    id: 'wooden_fence_basic',
    name: '木质围栏',
    nameEn: 'Wooden Fence',
    description: '简单实用的木质围栏，为农场增添田园风情',
    type: DecorationType.FENCE,
    rarity: ItemRarity.GRAY,
    category: ItemCategory.INDUSTRIAL,
    
    size: { width: 1, height: 1 },
    beautyValue: 5,
    level: 1,
    maxLevel: 3,
    
    visual: {
      sprite: 'decoration/fences/wooden_fence_basic.png',
      variants: ['brown', 'white', 'natural'],
      seasonalSprites: {
        winter: 'decoration/fences/wooden_fence_basic_snow.png'
      }
    },
    
    effects: [
      {
        type: DecorationEffectType.BEAUTY_BOOST,
        value: 5,
        radius: 1,
        description: '提升周围1格范围的美观度'
      }
    ],
    
    placement: {
      canOverlap: false,
      needsOpenSpace: false,
      canPlaceOnWater: false,
      canPlaceOnPath: false
    },
    
    economy: {
      basePrice: 50,
      sellPrice: 25,
      unlockLevel: 1,
      unlockConditions: []
    },
    
    audio: {
      placementSound: 'audio/sfx/place_wood.wav'
    }
  },

  {
    id: 'stone_fence_elegant',
    name: '优雅石墙',
    nameEn: 'Elegant Stone Wall',
    description: '精心雕琢的石墙，展现主人的品味',
    type: DecorationType.WALL,
    rarity: ItemRarity.BLUE,
    category: ItemCategory.INDUSTRIAL,
    
    size: { width: 1, height: 1 },
    beautyValue: 15,
    level: 1,
    maxLevel: 5,
    
    visual: {
      sprite: 'decoration/fences/stone_wall_elegant.png',
      variants: ['grey', 'white', 'marble'],
      animation: ['sparkle_effect']
    },
    
    effects: [
      {
        type: DecorationEffectType.BEAUTY_BOOST,
        value: 15,
        radius: 2,
        description: '提升周围2格范围的美观度'
      },
      {
        type: DecorationEffectType.WEATHER_PROTECTION,
        value: 0.1,
        radius: 1,
        description: '为周围作物提供轻微的天气保护'
      }
    ],
    
    placement: {
      canOverlap: false,
      needsOpenSpace: false,
      canPlaceOnWater: false,
      canPlaceOnPath: false
    },
    
    economy: {
      basePrice: 200,
      sellPrice: 100,
      unlockLevel: 5,
      unlockConditions: [
        {
          type: UnlockConditionType.FARM_LEVEL,
          value: 5,
          description: '需要农场等级达到5级'
        }
      ]
    },
    
    audio: {
      placementSound: 'audio/sfx/place_stone.wav'
    }
  },

  // 植物装饰类
  {
    id: 'rose_garden',
    name: '玫瑰花园',
    nameEn: 'Rose Garden',
    description: '美丽的玫瑰花园，散发着迷人的香气',
    type: DecorationType.FLOWER_BED,
    rarity: ItemRarity.GREEN,
    category: ItemCategory.CROP,
    
    size: { width: 2, height: 2 },
    beautyValue: 25,
    level: 1,
    maxLevel: 4,
    
    visual: {
      sprite: 'decoration/plants/rose_garden.png',
      variants: ['red', 'pink', 'white', 'mixed'],
      seasonalSprites: {
        spring: 'decoration/plants/rose_garden_bloom.png',
        summer: 'decoration/plants/rose_garden_full.png',
        autumn: 'decoration/plants/rose_garden_autumn.png',
        winter: 'decoration/plants/rose_garden_dormant.png'
      },
      animation: ['butterfly_visit', 'petal_fall']
    },
    
    effects: [
      {
        type: DecorationEffectType.BEAUTY_BOOST,
        value: 25,
        radius: 3,
        description: '大幅提升周围美观度'
      },
      {
        type: DecorationEffectType.MOOD_BOOST,
        value: 0.15,
        radius: 2,
        description: '提升心情，增加专注代币获取'
      }
    ],
    
    placement: {
      canOverlap: false,
      needsOpenSpace: true,
      canPlaceOnWater: false,
      canPlaceOnPath: false
    },
    
    economy: {
      basePrice: 300,
      sellPrice: 180,
      maintenanceCost: 10,
      unlockLevel: 3,
      unlockConditions: [
        {
          type: UnlockConditionType.ITEM_COUNT,
          value: 10,
          description: '需要拥有10种不同的装饰道具'
        }
      ]
    },
    
    interaction: {
      canInteract: true,
      interactionType: InteractionType.ADMIRE,
      rewards: [
        {
          type: 'focus_token',
          amount: 2,
          probability: 0.8
        },
        {
          type: 'experience',
          amount: 5,
          probability: 1.0
        }
      ],
      cooldown: 3600 // 1小时
    },
    
    audio: {
      placementSound: 'audio/sfx/place_flower.wav',
      ambientSound: 'audio/ambient/garden_birds.wav',
      interactionSound: 'audio/sfx/flower_admire.wav'
    }
  },

  {
    id: 'sakura_tree',
    name: '樱花树',
    nameEn: 'Sakura Tree',
    description: '优雅的樱花树，春天时绽放粉色花朵',
    type: DecorationType.TREE,
    rarity: ItemRarity.ORANGE,
    category: ItemCategory.CROP,
    
    size: { width: 3, height: 3 },
    beautyValue: 50,
    level: 1,
    maxLevel: 6,
    
    visual: {
      sprite: 'decoration/plants/sakura_tree.png',
      seasonalSprites: {
        spring: 'decoration/plants/sakura_tree_bloom.png',
        summer: 'decoration/plants/sakura_tree_green.png',
        autumn: 'decoration/plants/sakura_tree_autumn.png',
        winter: 'decoration/plants/sakura_tree_bare.png'
      },
      animation: ['petal_shower', 'wind_sway', 'seasonal_change']
    },
    
    effects: [
      {
        type: DecorationEffectType.BEAUTY_BOOST,
        value: 50,
        radius: 4,
        description: '大范围美观度提升'
      },
      {
        type: DecorationEffectType.FOCUS_TOKEN_BONUS,
        value: 0.2,
        radius: 3,
        description: '在樱花树下工作获得额外专注代币'
      },
      {
        type: DecorationEffectType.GROWTH_SPEED,
        value: 0.1,
        radius: 2,
        description: '轻微提升周围作物生长速度'
      }
    ],
    
    placement: {
      canOverlap: false,
      needsOpenSpace: true,
      canPlaceOnWater: false,
      canPlaceOnPath: false
    },
    
    economy: {
      basePrice: 800,
      sellPrice: 500,
      maintenanceCost: 20,
      unlockLevel: 8,
      unlockConditions: [
        {
          type: UnlockConditionType.ACHIEVEMENT,
          value: 'garden_master',
          description: '需要解锁"园艺大师"成就'
        }
      ]
    },
    
    interaction: {
      canInteract: true,
      interactionType: InteractionType.COLLECT,
      rewards: [
        {
          type: 'item',
          amount: 1,
          itemId: 'sakura_petal',
          probability: 0.3
        },
        {
          type: 'focus_token',
          amount: 5,
          probability: 0.6
        }
      ],
      cooldown: 7200 // 2小时
    },
    
    temporal: {
      isTemporary: false,
      seasonalAvailability: ['spring']
    },
    
    audio: {
      placementSound: 'audio/sfx/place_tree.wav',
      ambientSound: 'audio/ambient/wind_leaves.wav',
      interactionSound: 'audio/sfx/collect_petals.wav'
    }
  },

  // 功能装饰类
  {
    id: 'crystal_fountain',
    name: '水晶喷泉',
    nameEn: 'Crystal Fountain',
    description: '神秘的水晶喷泉，传说能带来好运',
    type: DecorationType.FOUNTAIN,
    rarity: ItemRarity.GOLD,
    category: ItemCategory.AGRICULTURAL,
    
    size: { width: 2, height: 2 },
    beautyValue: 80,
    level: 1,
    maxLevel: 8,
    
    visual: {
      sprite: 'decoration/functional/crystal_fountain.png',
      animation: ['water_flow', 'crystal_glow', 'rainbow_effect'],
      variants: ['blue', 'purple', 'rainbow']
    },
    
    effects: [
      {
        type: DecorationEffectType.BEAUTY_BOOST,
        value: 80,
        radius: 5,
        description: '超大范围美观度提升'
      },
      {
        type: DecorationEffectType.SYNTHESIS_BONUS,
        value: 0.15,
        radius: 4,
        description: '提升附近合成工作台的成功率'
      },
      {
        type: DecorationEffectType.HARVEST_BONUS,
        value: 0.1,
        radius: 3,
        description: '增加周围作物的收获量'
      }
    ],
    
    placement: {
      canOverlap: false,
      needsOpenSpace: true,
      canPlaceOnWater: false,
      canPlaceOnPath: false
    },
    
    economy: {
      basePrice: 2000,
      sellPrice: 1200,
      maintenanceCost: 50,
      unlockLevel: 15,
      unlockConditions: [
        {
          type: UnlockConditionType.FOCUS_TOKENS,
          value: 10000,
          description: '需要累计获得10000专注代币'
        }
      ]
    },
    
    interaction: {
      canInteract: true,
      interactionType: InteractionType.COLLECT,
      rewards: [
        {
          type: 'focus_token',
          amount: 20,
          probability: 0.5
        },
        {
          type: 'item',
          amount: 1,
          itemId: 'crystal_water',
          probability: 0.2
        }
      ],
      cooldown: 14400 // 4小时
    },
    
    audio: {
      placementSound: 'audio/sfx/place_fountain.wav',
      ambientSound: 'audio/ambient/fountain_flow.wav',
      interactionSound: 'audio/sfx/crystal_chime.wav'
    }
  },

  // 路径装饰
  {
    id: 'stone_path_basic',
    name: '石板小径',
    nameEn: 'Stone Path',
    description: '整齐的石板小径，方便农场内的移动',
    type: DecorationType.STONE_PATH,
    rarity: ItemRarity.GRAY,
    category: ItemCategory.INDUSTRIAL,
    
    size: { width: 1, height: 1 },
    beautyValue: 3,
    level: 1,
    maxLevel: 3,
    
    visual: {
      sprite: 'decoration/paths/stone_path_basic.png',
      variants: ['straight', 'curve', 'intersection', 'corner']
    },
    
    effects: [
      {
        type: DecorationEffectType.BEAUTY_BOOST,
        value: 3,
        radius: 1,
        description: '轻微提升美观度'
      }
    ],
    
    placement: {
      canOverlap: false,
      needsOpenSpace: false,
      canPlaceOnWater: false,
      canPlaceOnPath: true
    },
    
    economy: {
      basePrice: 20,
      sellPrice: 10,
      unlockLevel: 1
    },
    
    audio: {
      placementSound: 'audio/sfx/place_stone.wav'
    }
  },

  // 季节装饰
  {
    id: 'christmas_tree',
    name: '圣诞树',
    nameEn: 'Christmas Tree',
    description: '节日专属装饰，只在冬季可用',
    type: DecorationType.HOLIDAY,
    rarity: ItemRarity.BLUE,
    category: ItemCategory.CROP,
    
    size: { width: 2, height: 2 },
    beautyValue: 40,
    level: 1,
    maxLevel: 5,
    
    visual: {
      sprite: 'decoration/seasonal/christmas_tree.png',
      animation: ['lights_twinkle', 'snow_fall'],
      variants: ['traditional', 'colorful', 'elegant']
    },
    
    effects: [
      {
        type: DecorationEffectType.BEAUTY_BOOST,
        value: 40,
        radius: 3,
        description: '节日氛围，大幅提升美观度'
      },
      {
        type: DecorationEffectType.MOOD_BOOST,
        value: 0.25,
        radius: 4,
        description: '节日快乐，显著提升心情'
      }
    ],
    
    placement: {
      canOverlap: false,
      needsOpenSpace: true,
      canPlaceOnWater: false,
      canPlaceOnPath: false
    },
    
    economy: {
      basePrice: 500,
      sellPrice: 300,
      unlockLevel: 5
    },
    
    temporal: {
      isTemporary: true,
      duration: 720, // 30天
      renewalCost: 100,
      seasonalAvailability: ['winter']
    },
    
    interaction: {
      canInteract: true,
      interactionType: InteractionType.ADMIRE,
      rewards: [
        {
          type: 'focus_token',
          amount: 10,
          probability: 1.0
        },
        {
          type: 'item',
          amount: 1,
          itemId: 'christmas_gift',
          probability: 0.1
        }
      ],
      cooldown: 86400 // 24小时
    },
    
    audio: {
      placementSound: 'audio/sfx/place_tree.wav',
      ambientSound: 'audio/ambient/christmas_music.wav',
      interactionSound: 'audio/sfx/jingle_bells.wav'
    }
  }
]

// 农场主题配置
export const FARM_THEMES: FarmTheme[] = [
  // 自然天堂主题（默认）
  {
    id: 'natural_paradise',
    name: '自然天堂',
    nameEn: 'Natural Paradise',
    description: '回归自然的宁静农场，绿草如茵，鸟语花香',
    category: ThemeStyle.NATURAL,
    price: 0,
    isDefault: true,
    
    visual: {
      backgroundTexture: '/themes/natural_paradise/background.jpg',
      skybox: '/themes/natural_paradise/skybox.jpg',
      groundTexture: '/themes/natural_paradise/grass.jpg',
      waterTexture: '/themes/natural_paradise/water.jpg',
      treeModels: [
        '/themes/natural_paradise/models/oak_tree.glb',
        '/themes/natural_paradise/models/pine_tree.glb'
      ],
      particleEffects: ['butterflies', 'pollen'],
      weather: ['sunny', 'light_rain', 'cloudy'],
      lightingPreset: 'natural_daylight',
      colorPalette: {
        primary: '#22c55e',
        secondary: '#86efac', 
        accent: '#fbbf24',
        background: '#dcfce7'
      }
    },
    
    audio: {
      ambientMusic: '/themes/natural_paradise/music/peaceful_nature.mp3',
      soundEffects: {
        bird_chirping: '/themes/natural_paradise/sounds/birds.mp3',
        wind_rustling: '/themes/natural_paradise/sounds/wind.mp3',
        water_flowing: '/themes/natural_paradise/sounds/stream.mp3'
      }
    },
    
    themeEffects: [
      {
        type: DecorationEffectType.PLANT_GROWTH_SPEED,
        value: 10,
        range: 0,
        description: '植物生长速度提升10%'
      }
    ],
    
    unlockConditions: [], // 默认解锁
    
    decorationPreferences: {
      recommendedTypes: [DecorationType.ORNAMENTAL_PLANT, DecorationType.TREE, DecorationType.BUSH],
      restrictedTypes: [DecorationType.MODERN_STYLE],
      seasonalVariations: {
        spring: { tint: '#bbf7d0', intensity: 1.2 },
        summer: { tint: '#22c55e', intensity: 1.0 },
        autumn: { tint: '#fbbf24', intensity: 0.9 },
        winter: { tint: '#e5e7eb', intensity: 0.7 }
      }
    },
    
    achievements: ['nature_lover', 'green_thumb'],
    tags: ['peaceful', 'natural', 'beginner-friendly']
  },

  // 禅意花园主题
  {
    id: 'zen_garden',
    name: '禅意花园',
    nameEn: 'Zen Garden',
    description: '东方禅意美学，枯山水与竹林相映，宁静致远',
    category: ThemeStyle.EASTERN,
    price: 500,
    isDefault: false,
    
    visual: {
      backgroundTexture: '/themes/zen_garden/background.jpg',
      skybox: '/themes/zen_garden/skybox.jpg',
      groundTexture: '/themes/zen_garden/sand_pattern.jpg',
      waterTexture: '/themes/zen_garden/koi_pond.jpg',
      treeModels: [
        '/themes/zen_garden/models/bamboo.glb',
        '/themes/zen_garden/models/cherry_blossom.glb',
        '/themes/zen_garden/models/bonsai.glb'
      ],
      particleEffects: ['falling_petals', 'incense_smoke'],
      weather: ['misty', 'clear', 'light_snow'],
      lightingPreset: 'soft_oriental',
      colorPalette: {
        primary: '#6b7280',
        secondary: '#f3f4f6',
        accent: '#ec4899',
        background: '#f9fafb'
      }
    },
    
    audio: {
      ambientMusic: '/themes/zen_garden/music/meditation.mp3',
      soundEffects: {
        bamboo_chimes: '/themes/zen_garden/sounds/wind_chimes.mp3',
        water_drops: '/themes/zen_garden/sounds/water_drops.mp3',
        temple_bell: '/themes/zen_garden/sounds/temple_bell.mp3'
      }
    },
    
    themeEffects: [
      {
        type: DecorationEffectType.FOCUS_BONUS,
        value: 15,
        range: 0,
        description: '专注效果提升15%'
      },
      {
        type: DecorationEffectType.BEAUTY_MULTIPLIER,
        value: 1.2,
        range: 0,
        description: '美观度加成20%'
      }
    ],
    
    unlockConditions: [
      {
        type: UnlockConditionType.FOCUS_TOKENS,
        value: 1000,
        description: '累计获得1000专注代币'
      }
    ],
    
    decorationPreferences: {
      recommendedTypes: [DecorationType.CHINESE_STYLE, DecorationType.STONE_PATH, DecorationType.FOUNTAIN],
      restrictedTypes: [DecorationType.MODERN_STYLE, DecorationType.WESTERN_STYLE],
      seasonalVariations: {
        spring: { tint: '#fecaca', intensity: 1.1 },
        summer: { tint: '#86efac', intensity: 1.0 },
        autumn: { tint: '#fde047', intensity: 0.95 },
        winter: { tint: '#e5e7eb', intensity: 0.8 }
      }
    },
    
    achievements: ['zen_master', 'meditation_expert'],
    tags: ['peaceful', 'cultural', 'meditation']
  },

  // 现代科技主题
  {
    id: 'tech_farm',
    name: '现代科技',
    nameEn: 'Tech Farm',
    description: '未来感十足的智能农场，科技与农业的完美融合',
    category: ThemeStyle.FUTURISTIC,
    price: 800,
    isDefault: false,
    
    visual: {
      backgroundTexture: '/themes/tech_farm/background.jpg',
      skybox: '/themes/tech_farm/neon_skybox.jpg',
      groundTexture: '/themes/tech_farm/metal_grid.jpg',
      waterTexture: '/themes/tech_farm/hydroponic_water.jpg',
      treeModels: [
        '/themes/tech_farm/models/hydroponic_tower.glb',
        '/themes/tech_farm/models/solar_tree.glb',
        '/themes/tech_farm/models/led_plant.glb'
      ],
      particleEffects: ['neon_sparks', 'hologram_effects'],
      weather: ['clear', 'fog', 'aurora'],
      lightingPreset: 'neon_cyberpunk',
      colorPalette: {
        primary: '#06b6d4',
        secondary: '#8b5cf6',
        accent: '#f59e0b',
        background: '#1f2937'
      }
    },
    
    audio: {
      ambientMusic: '/themes/tech_farm/music/cyberpunk_ambient.mp3',
      soundEffects: {
        machine_hum: '/themes/tech_farm/sounds/machinery.mp3',
        data_processing: '/themes/tech_farm/sounds/data_beeps.mp3',
        energy_flow: '/themes/tech_farm/sounds/electricity.mp3'
      }
    },
    
    themeEffects: [
      {
        type: DecorationEffectType.HARVEST_YIELD,
        value: 25,
        range: 0,
        description: '收获产量提升25%'
      },
      {
        type: DecorationEffectType.AUTOMATION_EFFICIENCY,
        value: 30,
        range: 0,
        description: '自动化效率提升30%'
      }
    ],
    
    unlockConditions: [
      {
        type: UnlockConditionType.FARM_LEVEL,
        value: 15,
        description: '农场等级达到15级'
      },
      {
        type: UnlockConditionType.ACHIEVEMENT,
        value: 'tech_innovator',
        description: '获得科技创新者成就'
      }
    ],
    
    decorationPreferences: {
      recommendedTypes: [DecorationType.MODERN_STYLE, DecorationType.LAMP, DecorationType.FOUNTAIN],
      restrictedTypes: [DecorationType.RUSTIC_STYLE, DecorationType.CHINESE_STYLE],
      seasonalVariations: {
        spring: { tint: '#10b981', intensity: 1.0 },
        summer: { tint: '#06b6d4', intensity: 1.1 },
        autumn: { tint: '#f59e0b', intensity: 1.0 },
        winter: { tint: '#8b5cf6', intensity: 0.9 }
      }
    },
    
    achievements: ['tech_pioneer', 'automation_master'],
    tags: ['futuristic', 'efficient', 'high-tech']
  },

  // 田园诗画主题
  {
    id: 'pastoral_poetry',
    name: '田园诗画',
    nameEn: 'Pastoral Poetry',
    description: '欧式田园风光，麦田金浪，风车悠转，诗意盎然',
    category: ThemeStyle.RUSTIC,
    price: 400,
    isDefault: false,
    
    visual: {
      backgroundTexture: '/themes/pastoral_poetry/background.jpg',
      skybox: '/themes/pastoral_poetry/countryside_sky.jpg',
      groundTexture: '/themes/pastoral_poetry/wheat_field.jpg',
      waterTexture: '/themes/pastoral_poetry/clear_stream.jpg',
      treeModels: [
        '/themes/pastoral_poetry/models/windmill.glb',
        '/themes/pastoral_poetry/models/apple_tree.glb',
        '/themes/pastoral_poetry/models/sunflower.glb'
      ],
      particleEffects: ['wheat_pollen', 'dandelion_seeds'],
      weather: ['sunny', 'gentle_breeze', 'golden_hour'],
      lightingPreset: 'warm_countryside',
      colorPalette: {
        primary: '#eab308',
        secondary: '#f97316',
        accent: '#dc2626',
        background: '#fef3c7'
      }
    },
    
    audio: {
      ambientMusic: '/themes/pastoral_poetry/music/country_folk.mp3',
      soundEffects: {
        windmill_turning: '/themes/pastoral_poetry/sounds/windmill.mp3',
        cow_bells: '/themes/pastoral_poetry/sounds/farm_animals.mp3',
        harvest_sounds: '/themes/pastoral_poetry/sounds/scythe.mp3'
      }
    },
    
    themeEffects: [
      {
        type: DecorationEffectType.CROP_QUALITY,
        value: 20,
        range: 0,
        description: '作物品质提升20%'
      },
      {
        type: DecorationEffectType.SEASONAL_BONUS,
        value: 15,
        range: 0,
        description: '季节收益加成15%'
      }
    ],
    
    unlockConditions: [
      {
        type: UnlockConditionType.ITEM_COUNT,
        value: 10,
        description: '拥有10种不同装饰道具'
      }
    ],
    
    decorationPreferences: {
      recommendedTypes: [DecorationType.WESTERN_STYLE, DecorationType.RUSTIC_STYLE, DecorationType.WOODEN_PATH],
      restrictedTypes: [DecorationType.MODERN_STYLE, DecorationType.CHINESE_STYLE],
      seasonalVariations: {
        spring: { tint: '#84cc16', intensity: 1.1 },
        summer: { tint: '#eab308', intensity: 1.2 },
        autumn: { tint: '#f97316', intensity: 1.3 },
        winter: { tint: '#64748b', intensity: 0.8 }
      }
    },
    
    achievements: ['country_living', 'harvest_master'],
    tags: ['rustic', 'cozy', 'traditional']
  },

  // 热带天堂主题
  {
    id: 'tropical_paradise',
    name: '热带天堂',
    nameEn: 'Tropical Paradise',
    description: '热带雨林风情，棕榈摇曳，蝴蝶飞舞，生机勃勃',
    category: ThemeStyle.TROPICAL,
    price: 600,
    isDefault: false,
    
    visual: {
      backgroundTexture: '/themes/tropical_paradise/background.jpg',
      skybox: '/themes/tropical_paradise/tropical_sky.jpg',
      groundTexture: '/themes/tropical_paradise/jungle_floor.jpg',
      waterTexture: '/themes/tropical_paradise/lagoon.jpg',
      treeModels: [
        '/themes/tropical_paradise/models/palm_tree.glb',
        '/themes/tropical_paradise/models/banana_tree.glb',
        '/themes/tropical_paradise/models/tropical_fern.glb'
      ],
      particleEffects: ['tropical_rain', 'exotic_butterflies'],
      weather: ['humid', 'tropical_rain', 'sunshine'],
      lightingPreset: 'tropical_bright',
      colorPalette: {
        primary: '#10b981',
        secondary: '#06d6a0',
        accent: '#ffd60a',
        background: '#d1fae5'
      }
    },
    
    audio: {
      ambientMusic: '/themes/tropical_paradise/music/island_vibes.mp3',
      soundEffects: {
        jungle_sounds: '/themes/tropical_paradise/sounds/jungle.mp3',
        ocean_waves: '/themes/tropical_paradise/sounds/waves.mp3',
        tropical_birds: '/themes/tropical_paradise/sounds/exotic_birds.mp3'
      }
    },
    
    themeEffects: [
      {
        type: DecorationEffectType.EXOTIC_CROP_YIELD,
        value: 35,
        range: 0,
        description: '异国作物产量提升35%'
      },
      {
        type: DecorationEffectType.BIODIVERSITY_BONUS,
        value: 25,
        range: 0,
        description: '生物多样性奖励25%'
      }
    ],
    
    unlockConditions: [
      {
        type: UnlockConditionType.FARM_LEVEL,
        value: 8,
        description: '农场等级达到8级'
      },
      {
        type: UnlockConditionType.FOCUS_TOKENS,
        value: 600,
        description: '累计获得600专注代币'
      }
    ],
    
    decorationPreferences: {
      recommendedTypes: [DecorationType.ORNAMENTAL_PLANT, DecorationType.FOUNTAIN, DecorationType.BUSH],
      restrictedTypes: [DecorationType.FENCE, DecorationType.WALL],
      seasonalVariations: {
        spring: { tint: '#10b981', intensity: 1.1 },
        summer: { tint: '#06d6a0', intensity: 1.2 },
        autumn: { tint: '#84cc16', intensity: 1.0 },
        winter: { tint: '#22c55e', intensity: 0.9 }
      }
    },
    
    achievements: ['tropical_explorer', 'biodiversity_champion'],
    tags: ['exotic', 'vibrant', 'lush']
  },

  // 冰雪仙境主题
  {
    id: 'winter_wonderland',
    name: '冰雪仙境',
    nameEn: 'Winter Wonderland',
    description: '银装素裹的梦幻世界，雪花纷飞，晶莹剔透',
    category: ThemeStyle.SEASONAL,
    price: 700,
    isDefault: false,
    
    visual: {
      backgroundTexture: '/themes/winter_wonderland/background.jpg',
      skybox: '/themes/winter_wonderland/snowy_sky.jpg',
      groundTexture: '/themes/winter_wonderland/snow_ground.jpg',
      waterTexture: '/themes/winter_wonderland/ice_lake.jpg',
      treeModels: [
        '/themes/winter_wonderland/models/snow_pine.glb',
        '/themes/winter_wonderland/models/ice_sculpture.glb',
        '/themes/winter_wonderland/models/frost_tree.glb'
      ],
      particleEffects: ['falling_snow', 'ice_crystals'],
      weather: ['snowing', 'blizzard', 'clear_cold'],
      lightingPreset: 'arctic_blue',
      colorPalette: {
        primary: '#3b82f6',
        secondary: '#e0e7ff',
        accent: '#c084fc',
        background: '#f1f5f9'
      }
    },
    
    audio: {
      ambientMusic: '/themes/winter_wonderland/music/winter_calm.mp3',
      soundEffects: {
        wind_howling: '/themes/winter_wonderland/sounds/winter_wind.mp3',
        snow_crunching: '/themes/winter_wonderland/sounds/snow_steps.mp3',
        ice_cracking: '/themes/winter_wonderland/sounds/ice.mp3'
      }
    },
    
    themeEffects: [
      {
        type: DecorationEffectType.PRESERVATION_BONUS,
        value: 40,
        range: 0,
        description: '作物保存时间延长40%'
      },
      {
        type: DecorationEffectType.WINTER_CROP_BONUS,
        value: 30,
        range: 0,
        description: '冬季作物产量提升30%'
      }
    ],
    
    unlockConditions: [
      {
        type: UnlockConditionType.ACHIEVEMENT,
        value: 'winter_survivor',
        description: '获得冬季生存者成就'
      },
      {
        type: UnlockConditionType.FARM_LEVEL,
        value: 12,
        description: '农场等级达到12级'
      }
    ],
    
    decorationPreferences: {
      recommendedTypes: [DecorationType.SEASONAL, DecorationType.LAMP, DecorationType.STATUE],
      restrictedTypes: [DecorationType.ORNAMENTAL_PLANT, DecorationType.FLOWER_BED],
      seasonalVariations: {
        spring: { tint: '#a7f3d0', intensity: 0.8 },
        summer: { tint: '#bfdbfe', intensity: 0.6 },
        autumn: { tint: '#ddd6fe', intensity: 0.7 },
        winter: { tint: '#e0e7ff', intensity: 1.2 }
      }
    },
    
    achievements: ['ice_sculptor', 'winter_gardener'],
    tags: ['seasonal', 'magical', 'serene']
  },

  // 沙漠绿洲主题
  {
    id: 'desert_oasis',
    name: '沙漠绿洲',
    nameEn: 'Desert Oasis',
    description: '沙漠中的生命奇迹，绿洲如翡翠，展现坚韧与美丽',
    category: ThemeStyle.EXOTIC,
    price: 750,
    isDefault: false,
    
    visual: {
      backgroundTexture: '/themes/desert_oasis/background.jpg',
      skybox: '/themes/desert_oasis/desert_sky.jpg',
      groundTexture: '/themes/desert_oasis/sand_dunes.jpg',
      waterTexture: '/themes/desert_oasis/oasis_water.jpg',
      treeModels: [
        '/themes/desert_oasis/models/date_palm.glb',
        '/themes/desert_oasis/models/cactus.glb',
        '/themes/desert_oasis/models/aloe_vera.glb'
      ],
      particleEffects: ['sand_particles', 'heat_shimmer'],
      weather: ['hot_sun', 'sandstorm', 'cool_night'],
      lightingPreset: 'desert_golden',
      colorPalette: {
        primary: '#f59e0b',
        secondary: '#fbbf24',
        accent: '#10b981',
        background: '#fef3c7'
      }
    },
    
    audio: {
      ambientMusic: '/themes/desert_oasis/music/middle_eastern.mp3',
      soundEffects: {
        wind_sand: '/themes/desert_oasis/sounds/desert_wind.mp3',
        camel_bells: '/themes/desert_oasis/sounds/camel_caravan.mp3',
        water_spring: '/themes/desert_oasis/sounds/oasis_spring.mp3'
      }
    },
    
    themeEffects: [
      {
        type: DecorationEffectType.DROUGHT_RESISTANCE,
        value: 50,
        range: 0,
        description: '抗旱能力提升50%'
      },
      {
        type: DecorationEffectType.RARE_RESOURCE_CHANCE,
        value: 20,
        range: 0,
        description: '稀有资源获得几率提升20%'
      }
    ],
    
    unlockConditions: [
      {
        type: UnlockConditionType.FARM_LEVEL,
        value: 20,
        description: '农场等级达到20级'
      },
      {
        type: UnlockConditionType.ACHIEVEMENT,
        value: 'desert_explorer',
        description: '获得沙漠探险家成就'
      }
    ],
    
    decorationPreferences: {
      recommendedTypes: [DecorationType.FOUNTAIN, DecorationType.STONE_PATH, DecorationType.STATUE],
      restrictedTypes: [DecorationType.BUSH, DecorationType.FLOWER_BED],
      seasonalVariations: {
        spring: { tint: '#84cc16', intensity: 1.0 },
        summer: { tint: '#f59e0b', intensity: 1.3 },
        autumn: { tint: '#dc2626', intensity: 1.1 },
        winter: { tint: '#6b7280', intensity: 0.8 }
      }
    },
    
    achievements: ['oasis_guardian', 'desert_bloom'],
    tags: ['exotic', 'challenging', 'unique']
  }
] 