import React, { useEffect, useRef, useState } from 'react'
import Phaser from 'phaser'
import { EnhancedFarmScene } from './game/scenes/EnhancedFarmScene'
import { UnifiedAgriculturalScene } from './game/scenes/UnifiedAgriculturalScene'
import { gameConfig } from './game/GameConfig'

// import { CameraView } from './components/CameraView'
// import { ApplicationMonitor } from './components/monitoring/ApplicationMonitor'
// import { FocusMode } from './components/focus/FocusMode'
// import { UserTesting } from './components/testing/UserTesting'
// import { TestPlan } from './components/testing/TestPlan'
import SimpleLivestockTest from './components/EnhancedFarmUI_simple'
// import FeedbackAnalysis from './components/FeedbackAnalysis'
// import ErrorBoundary from './components/ErrorBoundary'

import { GameProvider, useGame } from './contexts/GameContext'
import { TutorialProvider, useTutorial } from './contexts/TutorialContext'
// import { TutorialOverlay } from './components/tutorial/TutorialOverlay'
import { PoseResults, PostureAnalysis } from './types/pose'
import { 
  Container, 
  Flex, 
  Grid,
  Show,
  Breakpoint,
  ResponsiveGameCanvas,
  ResponsiveSidebar
} from './components/ResponsiveLayout'
import { useDeviceType, useOrientation, isTouchDevice } from './utils/responsive'
import { fullTutorialSteps } from './config/tutorialSteps'
import './App.css'
// import AgriculturalDemo from './pages/AgriculturalDemo'
import UnifiedGameSystem from './pages/UnifiedGameSystem'

// 简单的错误回退组件
const ErrorFallback: React.FC<{ error: any }> = ({ error }) => (
  <div style={{
    padding: '50px',
    textAlign: 'center',
    backgroundColor: '#ffebee',
    minHeight: '100vh',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center'
  }}>
    <h1 style={{ color: '#d32f2f', marginBottom: '20px' }}>
      🚨 应用加载遇到问题
    </h1>
    <p style={{ color: '#666', marginBottom: '30px' }}>
      项目存在一些技术问题，但盲盒系统演示仍然可�?    </p>
    <button
      onClick={() => window.location.reload()}
      style={{
        padding: '12px 24px',
        backgroundColor: '#1976d2',
        color: 'white',
        border: 'none',
        borderRadius: '6px',
        fontSize: '16px',
        cursor: 'pointer',
        marginBottom: '20px'
      }}
    >
      🔄 重新加载页面
    </button>
    <a
      href="/test-lootbox.html"
      style={{
        padding: '12px 24px',
        backgroundColor: '#ff9800',
        color: 'white',
        textDecoration: 'none',
        borderRadius: '6px',
        fontSize: '16px',
        display: 'inline-block'
      }}
    >
      🎁 访问独立演示页面
    </a>
    <details style={{ marginTop: '30px', textAlign: 'left', width: '100%', maxWidth: '600px' }}>
      <summary style={{ cursor: 'pointer', fontSize: '14px', color: '#666' }}>
        📋 技术详�?(点击展开)
      </summary>
      <pre style={{ 
        backgroundColor: '#f5f5f5', 
        padding: '10px', 
        fontSize: '12px', 
        overflow: 'auto',
        maxHeight: '200px'
      }}>
        {error?.toString()}
      </pre>
    </details>
  </div>
)

// 简单的加载回退组件
const SimpleFallback: React.FC = () => (
  <div style={{
    padding: '50px',
    textAlign: 'center',
    backgroundColor: '#e3f2fd',
    minHeight: '100vh',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center'
  }}>
    <h1 style={{ color: '#1976d2', marginBottom: '30px' }}>
      🎁 期货游戏盲盒系统
    </h1>
    <div style={{ fontSize: '48px', marginBottom: '30px' }}>
      📦🌾💰
    </div>
    <p style={{ color: '#666', fontSize: '18px', marginBottom: '30px' }}>
      正在加载完整系统...
    </p>
    <div style={{ 
      color: '#666', 
      fontSize: '16px',
      background: '#f8f9fa',
      padding: '20px',
      borderRadius: '8px',
      border: '1px solid #ddd'
    }}>
      系统正在加载�?..
    </div>
  </div>
)

// 主应用内容组�?const AppContent: React.FC = () => {
  const gameRef = useRef<Phaser.Game | null>(null)
  const phaserRef = useRef<HTMLDivElement>(null)
  const [cameraStatus, setCameraStatus] = useState<string>('idle')
  const [showCamera, setShowCamera] = useState(false)
  const [showAppMonitor, setShowAppMonitor] = useState(false)
  const [showFocusMode, setShowFocusMode] = useState(false)
  const [showUserTesting, setShowUserTesting] = useState(false)
  const [showTestPlan, setShowTestPlan] = useState(false)
  const [showFeedbackAnalysis, setShowFeedbackAnalysis] = useState(false)
  const [showUnifiedGame, setShowUnifiedGame] = useState(false)
  const [showPlantingSystem, setShowPlantingSystem] = useState(false)
  

  
  // 响应式状�?  const deviceType = useDeviceType()
  const orientation = useOrientation()
  const isTouch = isTouchDevice()
  
  // 使用游戏上下�?  const { 
    state, 
    updatePosture, 
    startFocusSession, 
    endFocusSession,
    getFocusTimeFormatted,
    getCurrentStreakFormatted,
    shouldTriggerGrowth,
    growPlant
  } = useGame()

  // 使用引导上下�?  const { 
    state: tutorialState, 
    startTutorial, 
    updateProgress,
    finishTutorial
  } = useTutorial()

  // 启动新手引导
  useEffect(() => {
    // 检查是否是首次用户且教程未完成
    if (tutorialState.isFirstTimeUser && !tutorialState.tutorialProgress.completed) {
      // 延迟启动引导，确保界面已完全加载
      const timer = setTimeout(() => {
        startTutorial(fullTutorialSteps)
      }, 1000)
      
      return () => clearTimeout(timer)
    }
  }, [tutorialState.isFirstTimeUser, tutorialState.tutorialProgress.completed, startTutorial])

  useEffect(() => {
    if (phaserRef.current && !gameRef.current) {
      // 根据设备类型调整游戏尺寸
      const getGameSize = () => {
        const baseWidth = 800
        const baseHeight = 600
        
        if (deviceType === 'mobile') {
          const scale = Math.min(window.innerWidth / baseWidth, 0.9)
          return {
            width: Math.floor(baseWidth * scale),
            height: Math.floor(baseHeight * scale)
          }
        }
        
        if (deviceType === 'tablet') {
          return {
            width: Math.min(baseWidth, window.innerWidth * 0.8),
            height: Math.min(baseHeight, window.innerHeight * 0.7)
          }
        }
        
        return { width: baseWidth, height: baseHeight }
      }
      
      const { width, height } = getGameSize()
      
      const config: Phaser.Types.Core.GameConfig = {
        type: Phaser.AUTO,
        width,
        height,
        parent: phaserRef.current,
        backgroundColor: '#87CEEB', // 天空蓝色
        scene: [EnhancedFarmScene],
        scale: {
          mode: Phaser.Scale.FIT,
          autoCenter: Phaser.Scale.CENTER_BOTH,
          width,
          height
        },
        physics: {
          default: 'arcade',
          arcade: {
            gravity: { x: 0, y: 0 },
            debug: false
          }
        }
      }

      gameRef.current = new Phaser.Game(config)
      
      // 将游戏状态传递给Phaser场景
      if (gameRef.current.scene.scenes[0]) {
        const farmScene = gameRef.current.scene.scenes[0] as EnhancedFarmScene
        // farmScene.setGameState?.(state)
        
        // 监听植物生长事件
        farmScene.events.on('plantGrown', (data: { type: string; position: { x: number; y: number } }) => {
          growPlant(data.type)
        })
      }
    }

    return () => {
      if (gameRef.current) {
        gameRef.current.destroy(true)
        gameRef.current = null
      }
    }
  }, [])

  // 当游戏状态改变时，更新Phaser场景
  useEffect(() => {
    if (gameRef.current?.scene.scenes[0]) {
      const farmScene = gameRef.current.scene.scenes[0] as EnhancedFarmScene
      // farmScene.updateGameState?.(state)
      
      // 如果应该触发生长，通知农场场景
      if (shouldTriggerGrowth()) {
        // farmScene.triggerPlantGrowth?.()
      }
    }
  }, [state, shouldTriggerGrowth])

  const handleCameraStatusChange = (status: string) => {
    setCameraStatus(status)
    
    // 当摄像头状态改变时，管理专注会�?    if (status === 'granted' && !state.focusSession.isActive) {
      startFocusSession()
    } else if (status !== 'granted' && state.focusSession.isActive) {
      endFocusSession()
    }
  }

  const handlePoseDetected = (_results: PoseResults, analysis: PostureAnalysis) => {
    // 将姿态分析数据传递给游戏上下�?    updatePosture(analysis)
  }



  // 如果显示统一游戏系统，直接返回该页面
  if (showUnifiedGame) {
    return (
      <div style={{ position: 'relative' }}>
        <button
          onClick={() => setShowUnifiedGame(false)}
          style={{
            position: 'fixed',
            top: '20px',
            left: '20px',
            zIndex: 10000,
            padding: '10px 20px',
            backgroundColor: '#4CAF50',
            color: 'white',
            border: 'none',
            borderRadius: '6px',
            cursor: 'pointer',
            fontSize: '1rem',
            fontWeight: 'bold',
            boxShadow: '0 2px 8px rgba(0,0,0,0.2)'
          }}
        >
          �?返回主页
        </button>
        <UnifiedGameSystem />
      </div>
    )
  }

  // 如果显示种植系统，直接返回该页面
  if (showPlantingSystem) {
    return (
      <div style={{ position: 'relative' }}>
        <button
          onClick={() => setShowPlantingSystem(false)}
          style={{
            position: 'fixed',
            top: '20px',
            left: '20px',
            zIndex: 10000,
            padding: '10px 20px',
            backgroundColor: '#4CAF50',
            color: 'white',
            border: 'none',
            borderRadius: '6px',
            cursor: 'pointer',
            fontSize: '1rem',
            fontWeight: 'bold',
            boxShadow: '0 2px 8px rgba(0,0,0,0.2)'
          }}
        >
          �?返回主页
        </button>
        <SimpleLivestockTest />
      </div>
    )
  }



  return (
    <div className="app">
      <Container>
        <header className="app-header">
          <Breakpoint
            mobile={
              <div>
                <h1 style={{ fontSize: '1.8rem' }}>🌱 自律农场</h1>
                <p style={{ fontSize: '1rem' }}>让自律变成快乐的游戏体验�?/p>
              </div>
            }
            tablet={
              <div>
                <h1 style={{ fontSize: '2.2rem' }}>🌱 自律农场 - 习惯养成</h1>
                <p style={{ fontSize: '1.1rem' }}>通过摄像头监测，让自律变成快乐的游戏体验�?/p>
              </div>
            }
            desktop={
              <div>
                <h1>🌱 自律农场 - 习惯养成游戏</h1>
                <p>通过摄像头监测，让自律变成快乐的游戏体验�?/p>
              </div>
            }
          >
            <div>
              <h1>🌱 自律农场 - 习惯养成游戏</h1>
              <p>通过摄像头监测，让自律变成快乐的游戏体验�?/p>
            </div>
          </Breakpoint>
          
          <Flex 
            className="camera-status-bar"
            direction={deviceType === 'mobile' ? 'column' : 'row'}
            align="center"
            justify={deviceType === 'mobile' ? 'center' : 'between'}
            gap={deviceType === 'mobile' ? 8 : 16}
          >
            <Flex align="center" gap={8}>
              <span className={`status-indicator status-${cameraStatus}`}>
                {cameraStatus === 'granted' ? '🟢' : 
                 cameraStatus === 'requesting' ? '🟡' : 
                 cameraStatus === 'denied' || cameraStatus === 'error' ? '🔴' : '�?}
              </span>
              <span style={{ fontSize: deviceType === 'mobile' ? '0.9rem' : '1rem' }}>
                摄像头状�? {cameraStatus === 'idle' ? '未启�? :
                            cameraStatus === 'requesting' ? '请求�?..' :
                            cameraStatus === 'granted' ? '已连�? :
                            cameraStatus === 'denied' ? '权限被拒�? :
                            cameraStatus === 'error' ? '错误' : '不可�?}
              </span>
            </Flex>
            <Flex gap={8}>
              <button 
                className="toggle-camera-btn"
                onClick={() => setShowCamera(!showCamera)}
                style={{ 
                  padding: isTouch ? '12px 16px' : '8px 16px',
                  fontSize: deviceType === 'mobile' ? '0.9rem' : '1rem'
                }}
              >
                {showCamera ? '隐藏摄像�? : '显示摄像�?}
              </button>
              <button 
                onClick={() => setShowUnifiedGame(true)}
                style={{ 
                  padding: isTouch ? '12px 16px' : '8px 16px',
                  fontSize: deviceType === 'mobile' ? '0.9rem' : '1rem',
                  backgroundColor: '#FF9800',
                  color: 'white',
                  border: 'none',
                  borderRadius: '4px',
                  cursor: 'pointer',
                  fontWeight: 'bold'
                }}
              >
                🎮 期货游戏系统
              </button>
            </Flex>
          </Flex>
        </header>

        {/* 顶部功能按钮�?*/}
        <div className="top-action-bar">
          <div className="action-bar-container">
            <div className="action-bar-section">
              <h4>🎯 专注功能</h4>
              <div className="action-bar-buttons">
                <button 
                  className={`top-action-btn focus-btn ${state.focusSession.isActive ? 'active' : ''}`}
                  onClick={state.focusSession.isActive ? endFocusSession : startFocusSession}
                >
                  💡 {state.focusSession.isActive ? '结束专注' : '开始专注学�?}
                </button>
                <button 
                  className={`top-action-btn focus-mode-btn ${showFocusMode ? 'active' : ''}`}
                  onClick={() => setShowFocusMode(!showFocusMode)}
                >
                  🎯 {showFocusMode ? '关闭专注模式' : '手机专注模式'}
                </button>
              </div>
            </div>

            <div className="action-bar-section">
              <h4>🛠�?系统工具</h4>
              <div className="action-bar-buttons">
                <button 
                  className={`top-action-btn monitor-btn ${showAppMonitor ? 'active' : ''}`}
                  onClick={() => setShowAppMonitor(!showAppMonitor)}
                >
                  🖥�?{showAppMonitor ? '关闭应用监控' : '打开应用监控'}
                </button>
                <button 
                  className={`top-action-btn testing-btn ${showUserTesting ? 'active' : ''}`}
                  onClick={() => setShowUserTesting(!showUserTesting)}
                >
                  👥 {showUserTesting ? '关闭用户测试' : '用户测试中心'}
                </button>
                <button 
                  className={`top-action-btn plan-btn ${showTestPlan ? 'active' : ''}`}
                  onClick={() => setShowTestPlan(!showTestPlan)}
                >
                  📋 {showTestPlan ? '关闭测试计划' : '测试计划管理'}
                </button>
                <button 
                  className={`top-action-btn analysis-btn ${showFeedbackAnalysis ? 'active' : ''}`}
                  onClick={() => setShowFeedbackAnalysis(!showFeedbackAnalysis)}
                >
                  📊 {showFeedbackAnalysis ? '关闭反馈分析' : '反馈分析报告'}
                </button>
              </div>
            </div>



            <div className="action-bar-section">
              <h4>💪 生活管理</h4>
              <div className="action-bar-buttons">
                <button 
                  className={`top-action-btn planting-btn ${showPlantingSystem ? 'active' : ''}`}
                  onClick={() => setShowPlantingSystem(!showPlantingSystem)}
                  style={{
                    backgroundColor: showPlantingSystem ? '#4CAF50' : '#FF9800',
                    color: 'white'
                  }}
                >
                  🌾 {showPlantingSystem ? '关闭农场收集' : '农场收集系统'}
                </button>
                <button className="top-action-btn exercise-btn">
                  💪 开始运动打�?                </button>
                <button className="top-action-btn sleep-btn">
                  😴 设置作息时间
                </button>
                <button className="top-action-btn meditate-btn">
                  🧘 开始冥想练�?                </button>
                {!tutorialState.tutorialProgress.completed && (
                  <button 
                    className="top-action-btn tutorial-skip-btn"
                    onClick={() => {
                      finishTutorial();
                      updateProgress({ completed: true });
                      console.log('从操作中心跳过教�?);
                    }}
                  >
                    ⏭️ 跳过新手引导
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
        
        <main className="app-main">
          {/* 应用监控面板 - 全屏覆盖显示 */}
          {showAppMonitor && (
            <div className="app-monitor-overlay">
              <div className="monitor-header">
                <h2>🖥�?应用监控系统</h2>
                <button 
                  className="close-monitor-btn"
                  onClick={() => setShowAppMonitor(false)}
                >
                  �?关闭
                </button>
              </div>
              {/* <ApplicationMonitor /> */}
            </div>
          )}
          
          {/* 专注模式面板 - 全屏覆盖显示 */}
          {showFocusMode && (
            <div className="focus-mode-overlay">
              <div className="focus-mode-header">
                <h2>🎯 手机专注模式</h2>
                <button 
                  className="close-focus-btn"
                  onClick={() => setShowFocusMode(false)}
                >
                  �?关闭
                </button>
              </div>
              {/* <FocusMode /> */}
            </div>
          )}

          {/* 用户测试面板 - 全屏覆盖显示 */}
          {showUserTesting && (
            <div className="user-testing-overlay">
              <div className="testing-header">
                <h2>👥 用户测试中心</h2>
                <button 
                  className="close-testing-btn"
                  onClick={() => setShowUserTesting(false)}
                >
                  �?关闭
                </button>
              </div>
              {/* <UserTesting /> */}
            </div>
          )}

          {/* 测试计划面板 - 全屏覆盖显示 */}
          {showTestPlan && (
            <div className="test-plan-overlay">
              <div className="plan-header">
                <h2>📋 测试计划管理</h2>
                <button 
                  className="close-plan-btn"
                  onClick={() => setShowTestPlan(false)}
                >
                  �?关闭
                </button>
              </div>
              {/* <TestPlan /> */}
            </div>
          )}

          {/* 反馈分析面板 - 全屏覆盖显示 */}
          {showFeedbackAnalysis && (
            <div className="feedback-analysis-overlay">
              <div className="analysis-header">
                <h2>📊 反馈分析报告</h2>
                <button 
                  className="close-analysis-btn"
                  onClick={() => setShowFeedbackAnalysis(false)}
                >
                  �?关闭
                </button>
              </div>
              {/* <FeedbackAnalysis /> */}
            </div>
          )}


          
          <Flex 
            direction="row"
            gap={deviceType === 'mobile' ? 16 : deviceType === 'tablet' ? 24 : 32}
            align="start"
          >
            {/* 左侧控制面板 - 移动到最前面并置�?*/}
            <ResponsiveSidebar 
              collapsible={true}
              defaultCollapsed={deviceType === 'mobile'}
              className="control-panel left-panel"
            >
            <div className="panel-header">
              <h2 style={{ 
                margin: 0, 
                fontSize: '1.3rem', 
                color: '#2C5530',
                textAlign: 'center',
                padding: '10px 0',
                borderBottom: '2px solid #e0e0e0',
                background: 'linear-gradient(135deg, #f8f9fa, #e9ecef)'
              }}>
                📊 状态监控面�?              </h2>
            </div>
            <div className="panel-section">
              <h3>🎯 专注状�?/h3>
              <div className="focus-indicator">
                <div className={`focus-status ${state.isPostureGood ? 'good' : 'poor'}`}>
                  <div className="focus-score">
                    专注�? {Math.round(state.currentFocusScore)}%
                  </div>
                  <div className="focus-streak">
                    连续专注: {getCurrentStreakFormatted()}
                  </div>
                  <div className="focus-average">
                    平均分数: {Math.round(state.averageFocusScore)}%
                  </div>
                </div>
                {state.focusSession.isActive && (
                  <div className="session-info">
                    <span className="session-indicator">🔥</span>
                    <span>专注会话进行�?/span>
                  </div>
                )}
              </div>
            </div>

            <div className="panel-section">
              <h3>📊 农场统计</h3>
              <div className="stats-grid">
                <div className="stat-item">
                  <span className="stat-label">知识�?/span>
                  <span className="stat-value">{state.farmStats.knowledgeFlowers}</span>
                </div>
                <div className="stat-item">
                  <span className="stat-label">力量�?/span>
                  <span className="stat-value">{state.farmStats.strengthTrees}</span>
                </div>
                <div className="stat-item">
                  <span className="stat-label">时间�?/span>
                  <span className="stat-value">{state.farmStats.timeVeggies}</span>
                </div>
                <div className="stat-item">
                  <span className="stat-label">冥想�?/span>
                  <span className="stat-value">{state.farmStats.meditationLotus}</span>
                </div>
              </div>
              <div className="growth-points">
                <span>成长积分: {state.farmStats.totalGrowthPoints}</span>
              </div>
            </div>



            <div className="panel-section">
              <h3>📈 今日数据</h3>
              <div className="daily-stats">
                <div className="stat-row">
                  <span>专注时长:</span>
                  <span>{getFocusTimeFormatted()}</span>
                </div>
                <div className="stat-row">
                  <span>良好姿�?</span>
                  <span>{Math.round(state.dailyStats.goodPosturePercentage)}%</span>
                </div>
                <div className="stat-row">
                  <span>植物生长:</span>
                  <span>{state.dailyStats.plantsGrown}�?/span>
                </div>
              </div>
            </div>

            <div className="panel-section">
              <h3>🎁 今日奖励</h3>
              <div className="reward-container">
                <div className={`reward-item ${state.currentStreak >= 1800 ? 'completed' : 'pending'}`}>
                  <span>🏆</span>
                  <span>连续专注30分钟</span>
                </div>
                <div className={`reward-item ${state.dailyStats.plantsGrown >= 5 ? 'completed' : 'pending'}`}>
                  <span>�?/span>
                  <span>培养5株植�?/span>
                </div>
                <div className={`reward-item ${state.averageFocusScore >= 80 ? 'completed' : 'pending'}`}>
                  <span>💎</span>
                  <span>平均专注�?0%+</span>
                </div>
              </div>
            </div>
            </ResponsiveSidebar>

            {/* 右侧内容区域 */}
            <div style={{ flex: 1, display: 'flex', flexDirection: 'column', gap: '20px' }}>
              {/* 摄像头区�?- 在移动端时可隐藏或折�?*/}
              <Show on={showCamera ? ['mobile', 'tablet', 'desktop'] : []}>
                <section className="camera-section">
                  <h2 style={{ fontSize: deviceType === 'mobile' ? '1.2rem' : '1.5rem' }}>
                    📹 行为监测
                  </h2>
                  <div style={{
                    width: deviceType === 'mobile' ? 280 : 320,
                    height: deviceType === 'mobile' ? 210 : 240,
                    backgroundColor: '#f0f0f0',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    border: '2px dashed #ccc',
                    borderRadius: '8px'
                  }}>
                    📹 摄像头组件开发中...
                  </div>
                </section>
              </Show>

              {/* 游戏区域 */}
              <section className="game-section">
                <h2 style={{ fontSize: deviceType === 'mobile' ? '1.2rem' : '1.5rem' }}>
                  🎮 农场管理
                </h2>
                <ResponsiveGameCanvas
                  baseWidth={800}
                  baseHeight={600}
                  maintainAspectRatio={true}
                >
                  <div ref={phaserRef} className="phaser-container" />
                </ResponsiveGameCanvas>
              </section>
            </div>
          </Flex>
        </main>

        <footer className="app-footer">
          <p>🌟 坚持每一天，收获更好的自己！</p>
          <div className="debug-info">
            <small>
              Debug: 专注 {state.isPostureGood ? '�? : '�?} | 
              分数 {Math.round(state.currentFocusScore)} | 
              连续 {Math.round(state.currentStreak)}s |
              应该生长 {shouldTriggerGrowth() ? '�? : '�?}
            </small>
            {/* 开发模式：引导控制按钮 */}
            {true && (
              <div style={{ marginTop: '8px', display: 'flex', gap: '8px', flexWrap: 'wrap' }}>
                <button 
                  onClick={() => startTutorial(fullTutorialSteps)}
                  style={{ 
                    padding: '4px 8px', 
                    fontSize: '11px', 
                    background: '#007bff', 
                    color: 'white', 
                    border: 'none', 
                    borderRadius: '4px',
                    cursor: 'pointer'
                  }}
                >
                  开始引�?                </button>
                <button 
                  onClick={() => finishTutorial()}
                  style={{ 
                    padding: '4px 8px', 
                    fontSize: '11px', 
                    background: '#dc3545', 
                    color: 'white', 
                    border: 'none', 
                    borderRadius: '4px',
                    cursor: 'pointer'
                  }}
                >
                  结束引导
                </button>
                <button 
                  onClick={() => updateProgress({ completed: false })}
                  style={{ 
                    padding: '4px 8px', 
                    fontSize: '11px', 
                    background: '#ffc107', 
                    color: 'black', 
                    border: 'none', 
                    borderRadius: '4px',
                    cursor: 'pointer'
                  }}
                >
                  重置进度
                </button>
                <button 
                  onClick={() => {
                    finishTutorial();
                    updateProgress({ completed: true });
                    console.log('跳过所有教�?);
                  }}
                  style={{ 
                    padding: '4px 8px', 
                    fontSize: '11px', 
                    background: '#28a745', 
                    color: 'white', 
                    border: 'none', 
                    borderRadius: '4px',
                    cursor: 'pointer'
                  }}
                >
                  跳过教程
                </button>
              </div>
            )}
          </div>
        </footer>
      </Container>
      

      
      {/* 引导覆盖�?*/}
      {/* <TutorialOverlay /> */}
    </div>
  )
}

// 主App组件，使用Provider包装
const App: React.FC = () => {
  // 尝试渲染主应用，如果失败则显示简单回退
  try {
  return (
    <TutorialProvider>
      <GameProvider>
        <React.Fragment>
          <AppContent />
        </React.Fragment>
      </GameProvider>
    </TutorialProvider>
  )
  } catch (error) {
    console.error('App rendering error:', error)
    return <SimpleFallback />
  }
}

export default App 
