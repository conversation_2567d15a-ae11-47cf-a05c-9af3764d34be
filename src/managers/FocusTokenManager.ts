import { FocusToken } from '../types/agriculture'
import { EventEmitter } from 'eventemitter3'

export class FocusTokenManager extends EventEmitter {
  private focusToken: FocusToken
  private gameConfig: any
  private sessionStartTime: number
  private focusCheckInterval: number = 30 * 1000 // 30秒检查一次
  private intervalId?: NodeJS.Timeout

  constructor(initialData?: Partial<FocusToken>, gameConfig?: any) {
    super()
    
    this.gameConfig = gameConfig || {
      dailyLimit: 500,
      earnRate: 1, // 每分钟专注获得1代币
      bonusMultiplier: 1.5,
      focusThreshold: 60 // 需要连续专注60秒才开始获得代币
    }

    this.focusToken = {
      id: 'player_focus_token',
      amount: initialData?.amount || 0,
      earnedToday: initialData?.earnedToday || 0,
      totalEarned: initialData?.totalEarned || 0,
      lastEarnTime: initialData?.lastEarnTime || Date.now(),
      dailyLimit: this.gameConfig.dailyLimit,
      ...initialData
    }

    this.sessionStartTime = Date.now()
    this.resetDailyLimitIfNeeded()
  }

  // 开始专注会话
  startFocusSession(): void {
    this.sessionStartTime = Date.now()
    this.startEarningTokens()
    this.emit('focusSessionStarted', { startTime: this.sessionStartTime })
  }

  // 结束专注会话
  endFocusSession(): { tokensEarned: number; sessionDuration: number } {
    const sessionDuration = Date.now() - this.sessionStartTime
    this.stopEarningTokens()
    
    const sessionData = {
      tokensEarned: this.getSessionEarnings(),
      sessionDuration
    }
    
    this.emit('focusSessionEnded', sessionData)
    return sessionData
  }

  // 开始自动获得代币
  private startEarningTokens(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId)
    }

    this.intervalId = setInterval(() => {
      this.checkAndEarnTokens()
    }, this.focusCheckInterval)
  }

  // 停止自动获得代币
  private stopEarningTokens(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId)
      this.intervalId = undefined
    }
  }

  // 检查并获得代币
  private checkAndEarnTokens(): void {
    const now = Date.now()
    const sessionDuration = now - this.sessionStartTime
    
    // 只有专注时间超过阈值才开始获得代币
    if (sessionDuration < this.gameConfig.focusThreshold * 1000) {
      return
    }

    // 检查是否到达每日限制
    if (this.focusToken.earnedToday >= this.focusToken.dailyLimit) {
      this.emit('dailyLimitReached', { 
        earnedToday: this.focusToken.earnedToday,
        dailyLimit: this.focusToken.dailyLimit 
      })
      return
    }

    // 计算应该获得的代币数量
    const timeSinceLastEarn = now - this.focusToken.lastEarnTime
    const minutesPassed = timeSinceLastEarn / (60 * 1000)
    const tokensToEarn = Math.floor(minutesPassed * this.gameConfig.earnRate)

    if (tokensToEarn > 0) {
      this.earnTokens(tokensToEarn)
    }
  }

  // 获得代币
  private earnTokens(amount: number): void {
    const actualAmount = Math.min(
      amount, 
      this.focusToken.dailyLimit - this.focusToken.earnedToday
    )

    if (actualAmount <= 0) return

    this.focusToken.amount += actualAmount
    this.focusToken.earnedToday += actualAmount
    this.focusToken.totalEarned += actualAmount
    this.focusToken.lastEarnTime = Date.now()

    this.emit('tokensEarned', {
      amount: actualAmount,
      totalAmount: this.focusToken.amount,
      earnedToday: this.focusToken.earnedToday
    })

    // 保存到本地存储
    this.saveToStorage()
  }

  // 使用代币
  spendTokens(amount: number): boolean {
    if (this.focusToken.amount < amount) {
      this.emit('insufficientTokens', {
        required: amount,
        available: this.focusToken.amount
      })
      return false
    }

    this.focusToken.amount -= amount
    this.emit('tokensSpent', {
      amount,
      remaining: this.focusToken.amount
    })

    this.saveToStorage()
    return true
  }

  // 获取当前代币数量
  getTokenAmount(): number {
    return this.focusToken.amount
  }

  // 获取今日获得的代币数量
  getTodayEarned(): number {
    return this.focusToken.earnedToday
  }

  // 获取每日限制
  getDailyLimit(): number {
    return this.focusToken.dailyLimit
  }

  // 获取当前会话获得的代币
  getSessionEarnings(): number {
    const sessionDuration = Date.now() - this.sessionStartTime
    const minutesFocused = Math.max(0, sessionDuration - this.gameConfig.focusThreshold * 1000) / (60 * 1000)
    return Math.floor(minutesFocused * this.gameConfig.earnRate)
  }

  // 检查是否可以获得奖励倍数
  getBonusMultiplier(): number {
    const sessionDuration = Date.now() - this.sessionStartTime
    const hoursOfFocus = sessionDuration / (60 * 60 * 1000)
    
    // 专注超过1小时获得1.5倍奖励
    if (hoursOfFocus >= 1) {
      return this.gameConfig.bonusMultiplier
    }
    
    return 1
  }

  // 重置每日限制（如果需要）
  private resetDailyLimitIfNeeded(): void {
    const now = new Date()
    const lastEarnDate = new Date(this.focusToken.lastEarnTime)
    
    // 如果是新的一天，重置每日获得数量
    if (now.getDate() !== lastEarnDate.getDate() || 
        now.getMonth() !== lastEarnDate.getMonth() || 
        now.getFullYear() !== lastEarnDate.getFullYear()) {
      
      this.focusToken.earnedToday = 0
      this.emit('dailyLimitReset', {
        date: now.toDateString(),
        dailyLimit: this.focusToken.dailyLimit
      })
      
      this.saveToStorage()
    }
  }

  // 获取剩余的每日代币限制
  getRemainingDailyLimit(): number {
    return Math.max(0, this.focusToken.dailyLimit - this.focusToken.earnedToday)
  }

  // 检查是否到达每日限制
  isAtDailyLimit(): boolean {
    return this.focusToken.earnedToday >= this.focusToken.dailyLimit
  }

  // 获取完整的代币数据
  getTokenData(): FocusToken {
    return { ...this.focusToken }
  }

  // 设置游戏配置
  setGameConfig(config: any): void {
    this.gameConfig = { ...this.gameConfig, ...config }
  }

  // 保存到本地存储
  private saveToStorage(): void {
    try {
      localStorage.setItem('focus_token_data', JSON.stringify(this.focusToken))
    } catch (error) {
      console.error('无法保存专注代币数据:', error)
    }
  }

  // 从本地存储加载
  static loadFromStorage(): Partial<FocusToken> | null {
    try {
      const data = localStorage.getItem('focus_token_data')
      return data ? JSON.parse(data) : null
    } catch (error) {
      console.error('无法加载专注代币数据:', error)
      return null
    }
  }

  // 添加奖励代币（完成任务、成就等）
  addBonusTokens(amount: number, reason: string): void {
    this.focusToken.amount += amount
    this.focusToken.totalEarned += amount
    
    this.emit('bonusTokensAdded', {
      amount,
      reason,
      totalAmount: this.focusToken.amount
    })
    
    this.saveToStorage()
  }

  // 获取统计信息
  getStatistics() {
    const now = Date.now()
    const sessionDuration = now - this.sessionStartTime
    
    return {
      totalTokens: this.focusToken.amount,
      earnedToday: this.focusToken.earnedToday,
      totalEarned: this.focusToken.totalEarned,
      dailyLimit: this.focusToken.dailyLimit,
      remainingDaily: this.getRemainingDailyLimit(),
      currentSessionDuration: sessionDuration,
      currentSessionEarnings: this.getSessionEarnings(),
      bonusMultiplier: this.getBonusMultiplier(),
      isAtDailyLimit: this.isAtDailyLimit()
    }
  }

  // 销毁管理器
  destroy(): void {
    this.stopEarningTokens()
    this.removeAllListeners()
  }
} 