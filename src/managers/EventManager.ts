// 简单的EventEmitter实现（适用于浏览器环境）
class SimpleEventEmitter {
  private listeners: Map<string, Array<(...args: any[]) => void>> = new Map()
  
  on(event: string, listener: (...args: any[]) => void): this {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, [])
    }
    this.listeners.get(event)!.push(listener)
    return this
  }
  
  once(event: string, listener: (...args: any[]) => void): this {
    const onceWrapper = (...args: any[]) => {
      this.off(event, onceWrapper)
      listener(...args)
    }
    return this.on(event, onceWrapper)
  }
  
  off(event: string, listener: (...args: any[]) => void): this {
    const listeners = this.listeners.get(event)
    if (listeners) {
      const index = listeners.indexOf(listener)
      if (index !== -1) {
        listeners.splice(index, 1)
      }
    }
    return this
  }
  
  emit(event: string, ...args: any[]): boolean {
    const listeners = this.listeners.get(event)
    if (listeners && listeners.length > 0) {
      listeners.forEach(listener => {
        try {
          listener(...args)
        } catch (error) {
          console.error(`Event listener error for ${event}:`, error)
        }
      })
      return true
    }
    return false
  }
  
  listenerCount(event: string): number {
    return this.listeners.get(event)?.length || 0
  }
  
  removeAllListeners(event?: string): this {
    if (event) {
      this.listeners.delete(event)
    } else {
      this.listeners.clear()
    }
    return this
  }
}

// 游戏事件类型
export enum GameEventType {
  // 作物相关事件
  CROP_PLANTED = 'crop_planted',
  CROP_HARVESTED = 'crop_harvested',
  CROP_STAGE_CHANGED = 'crop_stage_changed',
  CROP_QUALITY_UPGRADED = 'crop_quality_upgraded',
  
  // 玩家进度事件
  LEVEL_UP = 'level_up',
  EXPERIENCE_GAINED = 'experience_gained',
  RESOURCE_CHANGED = 'resource_changed',
  ACHIEVEMENT_UNLOCKED = 'achievement_unlocked',
  
  // 专注时间事件
  FOCUS_SESSION_STARTED = 'focus_session_started',
  FOCUS_SESSION_COMPLETED = 'focus_session_completed',
  FOCUS_TIME_ADDED = 'focus_time_added',
  
  // 游戏状态事件
  GAME_LOADED = 'game_loaded',
  GAME_SAVED = 'game_saved',
  GAME_RESET = 'game_reset',
  SETTINGS_CHANGED = 'settings_changed',
  
  // UI事件
  NOTIFICATION_SHOWN = 'notification_shown',
  POPUP_OPENED = 'popup_opened',
  POPUP_CLOSED = 'popup_closed',
  
  // 错误事件
  ERROR_OCCURRED = 'error_occurred',
  WARNING_ISSUED = 'warning_issued'
}

// 游戏事件数据接口
export interface GameEvent<T = any> {
  type: GameEventType
  data: T
  timestamp: number
  source: string
  priority: 'low' | 'normal' | 'high' | 'critical'
  id: string
}

// 事件监听器配置
export interface EventListenerConfig {
  once?: boolean
  priority?: number
  filter?: (event: GameEvent) => boolean
}

// 通知配置
export interface NotificationConfig {
  title: string
  message: string
  type: 'info' | 'success' | 'warning' | 'error'
  duration?: number
  persistent?: boolean
  actions?: NotificationAction[]
}

// 通知动作
export interface NotificationAction {
  label: string
  action: () => void
  primary?: boolean
}

// 事件历史记录
export interface EventHistory {
  event: GameEvent
  listeners: number
  processingTime: number
  handled: boolean
}

/**
 * 事件管理器
 * 负责游戏内事件的发布、订阅、处理和通知系统
 */
export class EventManager extends SimpleEventEmitter {
  private eventHistory: EventHistory[] = []
  private maxHistorySize: number = 1000
  private notificationQueue: NotificationConfig[] = []
  private isProcessingNotifications: boolean = false
  private eventFilters: Map<string, (event: GameEvent) => boolean> = new Map()
  private listenerStats: Map<string, { count: number; totalTime: number }> = new Map()
  private debugMode: boolean = false
  
  constructor(debugMode: boolean = false) {
    super()
    this.debugMode = debugMode
    this.setupDefaultListeners()
  }
  
  /**
   * 设置默认事件监听器
   */
  private setupDefaultListeners(): void {
    // 监听作物相关事件
    this.on(GameEventType.CROP_PLANTED, (event: GameEvent) => {
      this.showNotification({
        title: '作物种植成功',
        message: `在 (${event.data.position.gridX}, ${event.data.position.gridY}) 种植了 ${event.data.crop.type}`,
        type: 'success',
        duration: 3000
      })
    })
    
    this.on(GameEventType.CROP_HARVESTED, (event: GameEvent) => {
      const { crop, rewards } = event.data
      this.showNotification({
        title: '收获成功！',
        message: `收获了 ${crop.type}，获得 ${rewards.experience} 经验值`,
        type: 'success',
        duration: 4000
      })
    })
    
    this.on(GameEventType.LEVEL_UP, (event: GameEvent) => {
      const { newLevel } = event.data
      this.showNotification({
        title: '恭喜升级！',
        message: `你已升级到 ${newLevel} 级！`,
        type: 'success',
        duration: 5000,
        persistent: true
      })
    })
    
    this.on(GameEventType.ACHIEVEMENT_UNLOCKED, (event: GameEvent) => {
      this.showNotification({
        title: '成就解锁！',
        message: `解锁了新成就: ${event.data.achievement}`,
        type: 'success',
        duration: 6000,
        persistent: true
      })
    })
    
    // 错误和警告事件
    this.on(GameEventType.ERROR_OCCURRED, (event: GameEvent) => {
      this.showNotification({
        title: '发生错误',
        message: event.data.message || '发生了未知错误',
        type: 'error',
        duration: 8000,
        persistent: true
      })
    })
    
    this.on(GameEventType.WARNING_ISSUED, (event: GameEvent) => {
      this.showNotification({
        title: '警告',
        message: event.data.message || '发生了警告',
        type: 'warning',
        duration: 5000
      })
    })
  }
  
  /**
   * 发布游戏事件
   */
  publishEvent<T>(
    type: GameEventType, 
    data: T, 
    source: string = 'unknown',
    priority: GameEvent['priority'] = 'normal'
  ): string {
    const event: GameEvent<T> = {
      type,
      data,
      timestamp: Date.now(),
      source,
      priority,
      id: this.generateEventId()
    }
    
    // 记录事件开始时间
    const startTime = performance.now()
    
    // 检查事件过滤器
    if (this.shouldFilterEvent(event)) {
      if (this.debugMode) {
        console.log(`事件 ${type} 被过滤器阻止`)
      }
      return event.id
    }
    
    // 发射事件
    const listenerCount = this.listenerCount(type)
    let handled = false
    
    try {
      this.emit(type, event)
      handled = true
      
      if (this.debugMode) {
        console.log(`📢 发布事件: ${type}`, event)
      }
    } catch (error) {
      console.error(`处理事件 ${type} 时发生错误:`, error)
      
      // 发布错误事件
      this.publishEvent(GameEventType.ERROR_OCCURRED, {
        originalEvent: event,
        error: error instanceof Error ? error.message : String(error)
      }, 'EventManager', 'critical')
    }
    
    // 记录处理时间
    const processingTime = performance.now() - startTime
    
    // 更新统计信息
    this.updateListenerStats(type, processingTime)
    
    // 添加到历史记录
    this.addToHistory({
      event,
      listeners: listenerCount,
      processingTime,
      handled
    })
    
    return event.id
  }
  
  /**
   * 订阅游戏事件
   */
  subscribeToEvent<T>(
    type: GameEventType,
    listener: (event: GameEvent<T>) => void,
    config: EventListenerConfig = {}
  ): () => void {
    const wrappedListener = (event: GameEvent<T>) => {
      // 应用过滤器
      if (config.filter && !config.filter(event)) {
        return
      }
      
      // 执行监听器
      try {
        listener(event)
      } catch (error) {
        console.error(`事件监听器执行失败 (${type}):`, error)
      }
    }
    
    // 注册监听器
    if (config.once) {
      this.once(type, wrappedListener)
    } else {
      this.on(type, wrappedListener)
    }
    
    // 返回取消订阅函数
    return () => {
      this.off(type, wrappedListener)
    }
  }
  
  /**
   * 批量订阅事件
   */
  subscribeToEvents<T>(
    types: GameEventType[],
    listener: (event: GameEvent<T>) => void,
    config: EventListenerConfig = {}
  ): () => void {
    const unsubscribeFunctions = types.map(type => 
      this.subscribeToEvent(type, listener, config)
    )
    
    // 返回批量取消订阅函数
    return () => {
      unsubscribeFunctions.forEach(unsub => unsub())
    }
  }
  
  /**
   * 添加事件过滤器
   */
  addEventFilter(name: string, filter: (event: GameEvent) => boolean): void {
    this.eventFilters.set(name, filter)
  }
  
  /**
   * 移除事件过滤器
   */
  removeEventFilter(name: string): void {
    this.eventFilters.delete(name)
  }
  
  /**
   * 检查事件是否应该被过滤
   */
  private shouldFilterEvent(event: GameEvent): boolean {
    for (const [name, filter] of this.eventFilters) {
      try {
        if (!filter(event)) {
          return true
        }
      } catch (error) {
        console.error(`事件过滤器 ${name} 执行失败:`, error)
      }
    }
    return false
  }
  
  /**
   * 显示通知
   */
  showNotification(config: NotificationConfig): void {
    // 添加到通知队列
    this.notificationQueue.push(config)
    
    // 处理通知队列
    this.processNotificationQueue()
    
    // 发布通知事件
    this.publishEvent(GameEventType.NOTIFICATION_SHOWN, config, 'EventManager')
  }
  
  /**
   * 处理通知队列
   */
  private async processNotificationQueue(): Promise<void> {
    if (this.isProcessingNotifications || this.notificationQueue.length === 0) {
      return
    }
    
    this.isProcessingNotifications = true
    
    while (this.notificationQueue.length > 0) {
      const notification = this.notificationQueue.shift()
      if (notification) {
        await this.displayNotification(notification)
      }
    }
    
    this.isProcessingNotifications = false
  }
  
  /**
   * 显示单个通知
   */
  private async displayNotification(config: NotificationConfig): Promise<void> {
    return new Promise((resolve) => {
      // 创建通知元素
      const notificationElement = this.createNotificationElement(config)
      
      // 添加到DOM
      document.body.appendChild(notificationElement)
      
      // 设置动画
      requestAnimationFrame(() => {
        notificationElement.classList.add('show')
      })
      
      // 设置自动隐藏
      if (!config.persistent && config.duration) {
        setTimeout(() => {
          this.hideNotification(notificationElement, resolve)
        }, config.duration)
      } else if (!config.persistent) {
        // 默认持续时间
        setTimeout(() => {
          this.hideNotification(notificationElement, resolve)
        }, 4000)
      } else {
        // 持久通知需要手动关闭
        resolve()
      }
    })
  }
  
  /**
   * 创建通知元素
   */
  private createNotificationElement(config: NotificationConfig): HTMLElement {
    const notification = document.createElement('div')
    notification.className = `game-notification notification-${config.type}`
    
    const title = document.createElement('div')
    title.className = 'notification-title'
    title.textContent = config.title
    
    const message = document.createElement('div')
    message.className = 'notification-message'
    message.textContent = config.message
    
    notification.appendChild(title)
    notification.appendChild(message)
    
    // 添加动作按钮
    if (config.actions && config.actions.length > 0) {
      const actionsContainer = document.createElement('div')
      actionsContainer.className = 'notification-actions'
      
      config.actions.forEach(action => {
        const button = document.createElement('button')
        button.className = `notification-action ${action.primary ? 'primary' : 'secondary'}`
        button.textContent = action.label
        button.onclick = () => {
          action.action()
          this.hideNotification(notification)
        }
        actionsContainer.appendChild(button)
      })
      
      notification.appendChild(actionsContainer)
    }
    
    // 添加关闭按钮
    const closeButton = document.createElement('button')
    closeButton.className = 'notification-close'
    closeButton.innerHTML = '✕'
    closeButton.onclick = () => this.hideNotification(notification)
    notification.appendChild(closeButton)
    
    return notification
  }
  
  /**
   * 隐藏通知
   */
  private hideNotification(element: HTMLElement, callback?: () => void): void {
    element.classList.add('hide')
    
    setTimeout(() => {
      if (element.parentNode) {
        element.parentNode.removeChild(element)
      }
      callback?.()
    }, 300) // 动画持续时间
  }
  
  /**
   * 生成事件ID
   */
  private generateEventId(): string {
    return 'event_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
  }
  
  /**
   * 添加到历史记录
   */
  private addToHistory(historyItem: EventHistory): void {
    this.eventHistory.push(historyItem)
    
    // 限制历史记录大小
    if (this.eventHistory.length > this.maxHistorySize) {
      this.eventHistory.shift()
    }
  }
  
  /**
   * 更新监听器统计
   */
  private updateListenerStats(eventType: string, processingTime: number): void {
    const stats = this.listenerStats.get(eventType) || { count: 0, totalTime: 0 }
    stats.count++
    stats.totalTime += processingTime
    this.listenerStats.set(eventType, stats)
  }
  
  /**
   * 获取事件历史
   */
  getEventHistory(limit?: number): EventHistory[] {
    const history = [...this.eventHistory].reverse()
    return limit ? history.slice(0, limit) : history
  }
  
  /**
   * 获取事件统计
   */
  getEventStats(): {
    totalEvents: number
    eventsByType: Record<string, number>
    averageProcessingTime: Record<string, number>
    recentEvents: EventHistory[]
  } {
    const eventsByType: Record<string, number> = {}
    const averageProcessingTime: Record<string, number> = {}
    
    // 统计事件类型
    this.eventHistory.forEach(item => {
      const type = item.event.type
      eventsByType[type] = (eventsByType[type] || 0) + 1
    })
    
    // 计算平均处理时间
    this.listenerStats.forEach((stats, type) => {
      averageProcessingTime[type] = stats.totalTime / stats.count
    })
    
    return {
      totalEvents: this.eventHistory.length,
      eventsByType,
      averageProcessingTime,
      recentEvents: this.getEventHistory(10)
    }
  }
  
  /**
   * 清除事件历史
   */
  clearEventHistory(): void {
    this.eventHistory = []
    this.listenerStats.clear()
  }
  
  /**
   * 设置调试模式
   */
  setDebugMode(enabled: boolean): void {
    this.debugMode = enabled
  }
  
  /**
   * 获取活跃监听器数量
   */
  getActiveListenersCount(): Record<string, number> {
    const counts: Record<string, number> = {}
    
    Object.values(GameEventType).forEach(eventType => {
      counts[eventType] = this.listenerCount(eventType)
    })
    
    return counts
  }
  
  /**
   * 清理资源
   */
  destroy(): void {
    // 移除所有监听器
    this.removeAllListeners()
    
    // 清除历史记录
    this.clearEventHistory()
    
    // 清除过滤器
    this.eventFilters.clear()
    
    // 清除通知队列
    this.notificationQueue = []
    
    // 移除所有通知元素
    const notifications = document.querySelectorAll('.game-notification')
    notifications.forEach(notification => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification)
      }
    })
  }
  
  /**
   * 便利方法：发布作物种植事件
   */
  publishCropPlanted(crop: any, position: { gridX: number; gridY: number }): void {
    this.publishEvent(GameEventType.CROP_PLANTED, { crop, position }, 'CropSystem')
  }
  
  /**
   * 便利方法：发布作物收获事件
   */
  publishCropHarvested(crop: any, rewards: any, position: { gridX: number; gridY: number }): void {
    this.publishEvent(GameEventType.CROP_HARVESTED, { crop, rewards, position }, 'CropSystem')
  }
  
  /**
   * 便利方法：发布升级事件
   */
  publishLevelUp(oldLevel: number, newLevel: number, remainingExp: number): void {
    this.publishEvent(GameEventType.LEVEL_UP, { oldLevel, newLevel, remainingExp }, 'PlayerSystem')
  }
  
  /**
   * 便利方法：发布资源变化事件
   */
  publishResourceChanged(resources: Record<string, number>): void {
    this.publishEvent(GameEventType.RESOURCE_CHANGED, { resources }, 'ResourceSystem')
  }
  
  /**
   * 便利方法：发布错误事件
   */
  publishError(message: string, error?: Error, source: string = 'unknown'): void {
    this.publishEvent(GameEventType.ERROR_OCCURRED, { 
      message, 
      error: error?.message,
      stack: error?.stack 
    }, source, 'critical')
  }
  
  /**
   * 便利方法：发布警告事件
   */
  publishWarning(message: string, data?: any, source: string = 'unknown'): void {
    this.publishEvent(GameEventType.WARNING_ISSUED, { message, data }, source, 'normal')
  }
} 