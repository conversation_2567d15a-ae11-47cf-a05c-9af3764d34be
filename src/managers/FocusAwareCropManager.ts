import { CropInstance } from '../types/crop'
import { FocusState } from './GameStateManager'
import { CropTimeManager } from './CropTimeManager'
import { growthCalculator } from '../utils/cropGrowth'

// 专注度影响配置
export interface FocusImpactConfig {
  // 基础增长倍率范围
  minGrowthMultiplier: number // 最小增长倍率（分心时）
  maxGrowthMultiplier: number // 最大增长倍率（专注时）
  
  // 专注度阈值
  focusThreshold: number // 开始获得增长奖励的专注度阈值
  excellentFocusThreshold: number // 获得最大奖励的专注度阈值
  
  // 时间影响
  minFocusTimeForBonus: number // 获得奖励的最小专注时间(ms)
  focusStreakBonus: number // 专注连击奖励倍率
  
  // 分心惩罚
  distractionPenalty: number // 分心时的生长速度减缓倍率
  maxPenaltyTime: number // 最大惩罚时间(ms)
  recoveryTime: number // 从分心恢复到正常的时间(ms)
}

// 专注度状态历史记录
export interface FocusHistoryEntry {
  timestamp: number
  focusScore: number
  isFocused: boolean
  duration: number
}

// 作物专注度增强数据
export interface CropFocusEnhancement {
  // 专注度历史
  focusHistory: FocusHistoryEntry[]
  
  // 专注状态统计
  totalFocusTime: number
  totalDistractedTime: number
  bestFocusStreak: number
  currentStreak: number
  
  // 增长加成
  currentGrowthMultiplier: number
  accumulatedBonus: number
  streakBonus: number
  
  // 状态时间戳
  lastFocusTime: number
  lastDistractionTime: number
  recoveryStartTime: number
  
  // 品质影响
  qualityBoostChance: number
  perfectFocusTime: number
}

/**
 * 专注度感知作物管理器
 * 将用户的专注状态与作物生长深度集成
 */
export class FocusAwareCropManager {
  private cropFocusData: Map<string, CropFocusEnhancement> = new Map()
  private currentFocusState: FocusState | null = null
  private config: FocusImpactConfig
  
  constructor(
    private cropTimeManager: CropTimeManager,
    config?: Partial<FocusImpactConfig>
  ) {
    this.config = {
      minGrowthMultiplier: 0.3, // 分心时生长速度降到30%
      maxGrowthMultiplier: 2.5, // 专注时生长速度提升到250%
      focusThreshold: 60, // 60分以上开始获得奖励
      excellentFocusThreshold: 85, // 85分以上获得最大奖励
      minFocusTimeForBonus: 30000, // 30秒专注后开始获得奖励
      focusStreakBonus: 0.1, // 每次连击增加10%奖励
      distractionPenalty: 0.5, // 分心时速度减半
      maxPenaltyTime: 300000, // 最大惩罚5分钟
      recoveryTime: 60000, // 1分钟恢复时间
      ...config
    }
  }
  
  /**
   * 更新专注状态
   */
  updateFocusState(focusState: FocusState): void {
    const previousState = this.currentFocusState
    this.currentFocusState = focusState
    const now = Date.now()
    
    // 更新所有作物的专注度数据
    this.cropFocusData.forEach((enhancement, cropId) => {
      this.updateCropFocusData(cropId, enhancement, previousState, focusState, now)
    })
    
    // 更新作物时间管理器的全局专注度
    this.cropTimeManager.setGlobalFocusScore(focusState.focusScore)
  }
  
  /**
   * 添加作物到专注度管理
   */
  addCrop(crop: CropInstance): void {
    if (this.cropFocusData.has(crop.id)) {
      return // 已存在
    }
    
    const enhancement: CropFocusEnhancement = {
      focusHistory: [],
      totalFocusTime: 0,
      totalDistractedTime: 0,
      bestFocusStreak: 0,
      currentStreak: 0,
      currentGrowthMultiplier: 1.0,
      accumulatedBonus: 0,
      streakBonus: 0,
      lastFocusTime: 0,
      lastDistractionTime: 0,
      recoveryStartTime: 0,
      qualityBoostChance: 0,
      perfectFocusTime: 0
    }
    
    this.cropFocusData.set(crop.id, enhancement)
    console.log(`✨ 作物 ${crop.id} 已添加到专注度管理`)
  }
  
  /**
   * 移除作物的专注度管理
   */
  removeCrop(cropId: string): void {
    this.cropFocusData.delete(cropId)
    console.log(`🗑️ 作物 ${cropId} 已从专注度管理移除`)
  }
  
  /**
   * 更新作物的专注度数据
   */
  private updateCropFocusData(
    cropId: string,
    enhancement: CropFocusEnhancement,
    previousState: FocusState | null,
    currentState: FocusState,
    timestamp: number
  ): void {
    // 添加历史记录
    if (previousState) {
      const duration = timestamp - previousState.lastUpdateTime
      enhancement.focusHistory.push({
        timestamp: previousState.lastUpdateTime,
        focusScore: previousState.focusScore,
        isFocused: previousState.isFocused,
        duration
      })
      
      // 保持最近100条记录
      if (enhancement.focusHistory.length > 100) {
        enhancement.focusHistory.shift()
      }
      
      // 更新时间统计
      if (previousState.isFocused) {
        enhancement.totalFocusTime += duration
        enhancement.lastFocusTime = timestamp
        
        // 检查是否是完美专注
        if (previousState.focusScore >= this.config.excellentFocusThreshold) {
          enhancement.perfectFocusTime += duration
        }
      } else {
        enhancement.totalDistractedTime += duration
        enhancement.lastDistractionTime = timestamp
      }
    }
    
    // 更新专注连击
    this.updateFocusStreak(enhancement, previousState, currentState, timestamp)
    
    // 计算生长倍率
    this.calculateGrowthMultiplier(enhancement, currentState, timestamp)
    
    // 更新品质提升机会
    this.updateQualityBoost(enhancement, currentState)
  }
  
  /**
   * 更新专注连击
   */
  private updateFocusStreak(
    enhancement: CropFocusEnhancement,
    previousState: FocusState | null,
    currentState: FocusState,
    timestamp: number
  ): void {
    if (!previousState) return
    
    // 专注状态变化
    if (!previousState.isFocused && currentState.isFocused) {
      // 开始专注
      enhancement.currentStreak = 0
    } else if (previousState.isFocused && !currentState.isFocused) {
      // 中断专注
      if (enhancement.currentStreak > enhancement.bestFocusStreak) {
        enhancement.bestFocusStreak = enhancement.currentStreak
      }
      enhancement.currentStreak = 0
      enhancement.recoveryStartTime = timestamp
    } else if (currentState.isFocused) {
      // 持续专注
      const deltaTime = timestamp - previousState.lastUpdateTime
      enhancement.currentStreak += deltaTime
    }
  }
  
  /**
   * 计算生长倍率
   */
  private calculateGrowthMultiplier(
    enhancement: CropFocusEnhancement,
    currentState: FocusState,
    timestamp: number
  ): void {
    let multiplier = 1.0
    
    if (currentState.isFocused && currentState.focusScore >= this.config.focusThreshold) {
      // 专注状态下的奖励
      const focusRatio = Math.min(
        (currentState.focusScore - this.config.focusThreshold) / 
        (this.config.excellentFocusThreshold - this.config.focusThreshold),
        1.0
      )
      
      const baseMultiplier = 1.0 + (this.config.maxGrowthMultiplier - 1.0) * focusRatio
      
      // 连击奖励
      const streakBonus = Math.min(
        enhancement.currentStreak / this.config.minFocusTimeForBonus,
        5.0 // 最大5倍连击奖励
      ) * this.config.focusStreakBonus
      
      multiplier = baseMultiplier + streakBonus
      enhancement.streakBonus = streakBonus
      
    } else if (!currentState.isFocused) {
      // 分心状态下的惩罚
      const timeSinceDistraction = timestamp - enhancement.lastDistractionTime
      const recoveryProgress = Math.min(timeSinceDistraction / this.config.recoveryTime, 1.0)
      
      // 从惩罚状态逐渐恢复到正常
      const penaltyMultiplier = this.config.distractionPenalty + 
        (1.0 - this.config.distractionPenalty) * recoveryProgress
      
      multiplier = Math.max(penaltyMultiplier, this.config.minGrowthMultiplier)
      
    } else {
      // 低专注度但未完全分心
      const penaltyRatio = 1.0 - (currentState.focusScore / this.config.focusThreshold)
      multiplier = 1.0 - penaltyRatio * 0.3 // 最多减少30%
    }
    
    enhancement.currentGrowthMultiplier = Math.max(
      Math.min(multiplier, this.config.maxGrowthMultiplier),
      this.config.minGrowthMultiplier
    )
    
    // 累积奖励（用于品质提升）
    if (multiplier > 1.0) {
      enhancement.accumulatedBonus += (multiplier - 1.0) * 0.1
    }
  }
  
  /**
   * 更新品质提升机会
   */
  private updateQualityBoost(
    enhancement: CropFocusEnhancement,
    currentState: FocusState
  ): void {
    // 基于完美专注时间计算品质提升机会
    const perfectFocusRatio = enhancement.perfectFocusTime / Math.max(enhancement.totalFocusTime, 1)
    const baseChance = perfectFocusRatio * 0.3 // 最大30%基础机会
    
    // 连击奖励
    const streakRatio = Math.min(enhancement.bestFocusStreak / (5 * 60 * 1000), 1.0) // 5分钟连击=100%
    const streakChance = streakRatio * 0.2 // 最大20%连击奖励
    
    // 累积奖励
    const bonusChance = Math.min(enhancement.accumulatedBonus / 10.0, 0.1) // 最大10%累积奖励
    
    enhancement.qualityBoostChance = Math.min(baseChance + streakChance + bonusChance, 0.6) // 最大60%
  }
  
  /**
   * 获取作物的专注度增强数据
   */
  getCropFocusData(cropId: string): CropFocusEnhancement | null {
    return this.cropFocusData.get(cropId) || null
  }
  
  /**
   * 获取作物的当前生长倍率
   */
  getCropGrowthMultiplier(cropId: string): number {
    const enhancement = this.cropFocusData.get(cropId)
    return enhancement?.currentGrowthMultiplier || 1.0
  }
  
  /**
   * 检查作物是否应该获得品质提升
   */
  shouldBoostCropQuality(cropId: string): boolean {
    const enhancement = this.cropFocusData.get(cropId)
    if (!enhancement) return false
    
    return Math.random() < enhancement.qualityBoostChance
  }
  
  /**
   * 获取专注度统计信息
   */
  getFocusStatistics(): {
    totalCrops: number
    averageGrowthMultiplier: number
    totalFocusTime: number
    totalDistractedTime: number
    averageQualityBoostChance: number
    bestStreaks: { cropId: string; streak: number }[]
  } {
    const crops = Array.from(this.cropFocusData.entries())
    
    if (crops.length === 0) {
      return {
        totalCrops: 0,
        averageGrowthMultiplier: 1.0,
        totalFocusTime: 0,
        totalDistractedTime: 0,
        averageQualityBoostChance: 0,
        bestStreaks: []
      }
    }
    
    const totalGrowthMultiplier = crops.reduce((sum, [, enhancement]) => 
      sum + enhancement.currentGrowthMultiplier, 0
    )
    const totalFocusTime = crops.reduce((sum, [, enhancement]) => 
      sum + enhancement.totalFocusTime, 0
    )
    const totalDistractedTime = crops.reduce((sum, [, enhancement]) => 
      sum + enhancement.totalDistractedTime, 0
    )
    const totalQualityChance = crops.reduce((sum, [, enhancement]) => 
      sum + enhancement.qualityBoostChance, 0
    )
    
    const bestStreaks = crops
      .map(([cropId, enhancement]) => ({ cropId, streak: enhancement.bestFocusStreak }))
      .sort((a, b) => b.streak - a.streak)
      .slice(0, 5)
    
    return {
      totalCrops: crops.length,
      averageGrowthMultiplier: totalGrowthMultiplier / crops.length,
      totalFocusTime,
      totalDistractedTime,
      averageQualityBoostChance: totalQualityChance / crops.length,
      bestStreaks
    }
  }
  
  /**
   * 重置作物的专注度数据
   */
  resetCropFocusData(cropId: string): void {
    const enhancement = this.cropFocusData.get(cropId)
    if (!enhancement) return
    
    enhancement.focusHistory = []
    enhancement.totalFocusTime = 0
    enhancement.totalDistractedTime = 0
    enhancement.currentStreak = 0
    enhancement.accumulatedBonus = 0
    enhancement.streakBonus = 0
    enhancement.currentGrowthMultiplier = 1.0
    enhancement.qualityBoostChance = 0
    enhancement.perfectFocusTime = 0
  }
  
  /**
   * 获取配置
   */
  getConfig(): FocusImpactConfig {
    return { ...this.config }
  }
  
  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<FocusImpactConfig>): void {
    this.config = { ...this.config, ...newConfig }
    console.log('📝 专注度影响配置已更新')
  }
  
  /**
   * 清理资源
   */
  destroy(): void {
    this.cropFocusData.clear()
    this.currentFocusState = null
    console.log('🧹 专注度感知作物管理器已销毁')
  }
}

export default FocusAwareCropManager 