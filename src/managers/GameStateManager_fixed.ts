import { 
  CropInstance, 
  CropType, 
  CropStage, 
  CropQuality,
  FarmGrid,
  CropGrowthEvent 
} from '../types/crop'
import { CropTimeManager } from './CropTimeManager'

// 简单的EventEmitter实现（适用于浏览器环境）
class SimpleEventEmitter {
  private listeners: Map<string, Array<(...args: any[]) => void>> = new Map()
  
  on(event: string, listener: (...args: any[]) => void): this {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, [])
    }
    this.listeners.get(event)!.push(listener)
    return this
  }
  
  once(event: string, listener: (...args: any[]) => void): this {
    const onceWrapper = (...args: any[]) => {
      this.off(event, onceWrapper)
      listener(...args)
    }
    return this.on(event, onceWrapper)
  }
  
  off(event: string, listener: (...args: any[]) => void): this {
    const listeners = this.listeners.get(event)
    if (listeners) {
      const index = listeners.indexOf(listener)
      if (index !== -1) {
        listeners.splice(index, 1)
      }
    }
    return this
  }
  
  emit(event: string, ...args: any[]): boolean {
    const listeners = this.listeners.get(event)
    if (listeners && listeners.length > 0) {
      listeners.forEach(listener => {
        try {
          listener(...args)
        } catch (error) {
          console.error(`Event listener error for ${event}:`, error)
        }
      })
      return true
    }
    return false
  }
  
  listenerCount(event: string): number {
    return this.listeners.get(event)?.length || 0
  }
  
  removeAllListeners(event?: string): this {
    if (event) {
      this.listeners.delete(event)
    } else {
      this.listeners.clear()
    }
    return this
  }
}

// 游戏状态接口
export interface GameState {
  // 基本信息
  version: string
  playerName: string
  playerId: string
  
  // 游戏统计
  level: number
  experience: number
  totalFocusTime: number
  totalCropsHarvested: number
  
  // 资源系统
  resources: {
    knowledge: number
    strength: number
    time: number
    meditation: number
  }
  
  // 农场状态
  farmGrid: FarmGrid
  gridSize: { width: number; height: number }
  
  // 作物集合
  crops: Map<string, CropInstance>
  
  // 游戏配置
  settings: {
    autoSave: boolean
    autoSaveInterval: number // 毫秒
    enableNotifications: boolean
    soundEnabled: boolean
    musicEnabled: boolean
  }
  
  // 时间统计
  gameTime: {
    totalPlayTime: number
    sessionStartTime: number
    lastSaveTime: number
  }
  
  // 解锁系统
  unlockedFeatures: Set<string>
  achievements: Set<string>
}

// 状态变化事件
export interface StateChangeEvent {
  type: 'crop_planted' | 'crop_harvested' | 'level_up' | 'resource_changed' | 'achievement_unlocked'
  data: any
  timestamp: number
}

// 保存/加载选项
export interface SaveOptions {
  compress?: boolean
  encrypt?: boolean
  backup?: boolean
}

/**
 * 游戏状态管理器
 * 负责管理游戏的整体状态、数据持久化、事件系统等
 */
export class GameStateManager extends SimpleEventEmitter {
  private state: GameState
  private cropTimeManager: CropTimeManager
  private saveKey: string = 'selfgame_save_data'
  private autoSaveTimer: number | null = null
  private isLoading: boolean = false
  private isSaving: boolean = false
  
  constructor() {
    super()
    
    // 初始化默认状态
    this.state = this.createDefaultState()
    
    // 初始化作物时间管理器
    this.cropTimeManager = new CropTimeManager()
    
    // 设置事件监听
    this.setupEventListeners()
    
    // 启动自动保存
    this.startAutoSave()
  }
  
  /**
   * 创建默认游戏状态
   */
  private createDefaultState(): GameState {
    const now = Date.now()
    
    return {
      version: '1.0.0',
      playerName: '',
      playerId: this.generatePlayerId(),
      
      level: 1,
      experience: 0,
      totalFocusTime: 0,
      totalCropsHarvested: 0,
      
      resources: {
        knowledge: 0,
        strength: 0,
        time: 0,
        meditation: 0
      },
      
      farmGrid: {
        width: 8,
        height: 6,
        plots: Array(6).fill(null).map(() => Array(8).fill(null))
      },
      gridSize: { width: 8, height: 6 },
      
      crops: new Map(),
      
      settings: {
        autoSave: true,
        autoSaveInterval: 30000, // 30秒
        enableNotifications: true,
        soundEnabled: true,
        musicEnabled: true
      },
      
      gameTime: {
        totalPlayTime: 0,
        sessionStartTime: now,
        lastSaveTime: now
      },
      
      unlockedFeatures: new Set(['basic_farming']),
      achievements: new Set()
    }
  }
  
  /**
   * 生成玩家ID
   */
  private generatePlayerId(): string {
    return 'player_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
  }
  
  /**
   * 设置事件监听
   */
  private setupEventListeners(): void {
    // 注意：暂时跳过CropTimeManager的事件监听，因为接口不匹配
    // 将在CropTimeManager修复后重新启用
    
    // 监听窗口关闭事件，进行紧急保存
    if (typeof window !== 'undefined') {
      window.addEventListener('beforeunload', () => {
        this.saveGameState({ compress: false })
      })
    }
  }
  
  /**
   * 获取当前游戏状态（只读）
   */
  getGameState(): Readonly<GameState> {
    return Object.freeze(JSON.parse(JSON.stringify(this.state)))
  }
  
  /**
   * 获取玩家信息
   */
  getPlayerInfo(): { 
    name: string
    id: string
    level: number
    experience: number
    totalFocusTime: number
    totalCropsHarvested: number
  } {
    return {
      name: this.state.playerName,
      id: this.state.playerId,
      level: this.state.level,
      experience: this.state.experience,
      totalFocusTime: this.state.totalFocusTime,
      totalCropsHarvested: this.state.totalCropsHarvested
    }
  }
  
  /**
   * 设置玩家名称
   */
  setPlayerName(name: string): void {
    this.state.playerName = name
    this.emitStateChange('resource_changed', { playerName: name })
  }
  
  /**
   * 获取农场网格
   */
  getFarmGrid(): FarmGrid {
    return {
      width: this.state.farmGrid.width,
      height: this.state.farmGrid.height,
      plots: this.state.farmGrid.plots.map(row => [...row])
    }
  }
  
  /**
   * 获取所有作物
   */
  getAllCrops(): Map<string, CropInstance> {
    return new Map(this.state.crops)
  }
  
  /**
   * 获取指定位置的作物
   */
  getCropAt(gridX: number, gridY: number): CropInstance | undefined {
    if (gridY >= 0 && gridY < this.state.farmGrid.height && 
        gridX >= 0 && gridX < this.state.farmGrid.width) {
      return this.state.farmGrid.plots[gridY][gridX] || undefined
    }
    return undefined
  }
  
  /**
   * 种植作物
   */
  plantCrop(gridX: number, gridY: number, cropType: CropType): boolean {
    // 检查边界
    if (gridX < 0 || gridX >= this.state.gridSize.width || 
        gridY < 0 || gridY >= this.state.gridSize.height) {
      console.warn(`位置 (${gridX}, ${gridY}) 超出农场边界`)
      return false
    }
    
    // 检查位置是否已被占用
    if (this.state.farmGrid.plots[gridY] && this.state.farmGrid.plots[gridY][gridX] !== null) {
      console.warn(`位置 (${gridX}, ${gridY}) 已被占用`)
      return false
    }
    
    const now = Date.now()
    const cropId = this.generateCropId()
    
    // 创建作物实例
    const crop: CropInstance = {
      id: cropId,
      type: cropType,
      stage: CropStage.SEED,
      quality: CropQuality.COMMON,
      plantedAt: now,
      stageStartTime: now,
      totalGrowthTime: 0,
      focusTimeContributed: 0,
      averageFocusScore: 0,
      position: {
        x: gridX * 64, // 假设每个网格64像素
        y: gridY * 64,
        gridX,
        gridY
      },
      isGrowing: true,
      isPaused: false,
      harvestable: false,
      metadata: {
        sessionsContributed: 0,
        bestFocusStreak: 0,
        growthBoosts: 0
      }
    }
    
    // 存储作物
    this.state.crops.set(cropId, crop)
    this.state.farmGrid.plots[gridY][gridX] = crop
    
    // 发射事件
    this.emitStateChange('crop_planted', { crop, position: { gridX, gridY } })
    
    console.log(`🌱 在 (${gridX}, ${gridY}) 种植了 ${cropType}`)
    return true
  }
  
  /**
   * 收获作物
   */
  harvestCrop(gridX: number, gridY: number): {
    success: boolean
    rewards?: {
      experience: number
      resources: Record<string, number>
    }
    crop?: CropInstance
  } {
    const crop = this.getCropAt(gridX, gridY)
    
    if (!crop) {
      return { success: false }
    }
    
    if (!crop.harvestable || crop.stage !== CropStage.READY_TO_HARVEST) {
      console.warn(`作物 ${crop.id} 尚未准备好收获`)
      return { success: false }
    }
    
    // 计算奖励
    const rewards = this.calculateHarvestRewards(crop)
    
    // 应用奖励
    this.state.experience += rewards.experience
    Object.entries(rewards.resources).forEach(([resource, amount]) => {
      this.state.resources[resource as keyof typeof this.state.resources] += amount
    })
    
    // 更新统计
    this.state.totalCropsHarvested++
    
    // 检查升级
    this.checkLevelUp()
    
    // 移除作物
    this.state.crops.delete(crop.id)
    this.state.farmGrid.plots[gridY][gridX] = null
    
    // 更新作物状态为已收获
    crop.stage = CropStage.HARVESTED
    
    // 发射事件
    this.emitStateChange('crop_harvested', { crop, rewards, position: { gridX, gridY } })
    
    console.log(`🎯 收获了作物 ${crop.id}，获得:`, rewards)
    return { success: true, rewards, crop }
  }
  
  /**
   * 计算收获奖励
   */
  private calculateHarvestRewards(crop: CropInstance): {
    experience: number
    resources: Record<string, number>
  } {
    const baseExp = 10
    const qualityMultiplier = this.getQualityMultiplier(crop.quality)
    
    const experience = Math.floor(baseExp * qualityMultiplier)
    
    // 根据作物类型计算资源奖励
    const baseAmount = 5
    const amount = Math.floor(baseAmount * qualityMultiplier)
    
    const resources: Record<string, number> = {}
    
    switch (crop.type) {
      case CropType.KNOWLEDGE_FLOWER:
        resources.knowledge = amount
        break
      case CropType.STRENGTH_TREE:
        resources.strength = amount
        break
      case CropType.TIME_VEGGIE:
        resources.time = amount
        break
      case CropType.MEDITATION_LOTUS:
        resources.meditation = amount
        break
    }
    
    return { experience, resources }
  }
  
  /**
   * 获取品质倍数
   */
  private getQualityMultiplier(quality: CropQuality): number {
    const multipliers = {
      [CropQuality.COMMON]: 1.0,
      [CropQuality.UNCOMMON]: 1.2,
      [CropQuality.RARE]: 1.5,
      [CropQuality.EPIC]: 2.0,
      [CropQuality.LEGENDARY]: 3.0
    }
    return multipliers[quality] || 1.0
  }
  
  /**
   * 检查升级
   */
  private checkLevelUp(): void {
    const expRequired = this.getExperienceRequired(this.state.level)
    
    if (this.state.experience >= expRequired) {
      const oldLevel = this.state.level
      this.state.level++
      this.state.experience -= expRequired
      
      // 解锁新功能
      this.checkFeatureUnlocks()
      
      // 发射升级事件
      this.emitStateChange('level_up', { 
        oldLevel, 
        newLevel: this.state.level, 
        remainingExp: this.state.experience 
      })
      
      console.log(`🎉 升级到 ${this.state.level} 级！`)
    }
  }
  
  /**
   * 获取升级所需经验
   */
  private getExperienceRequired(level: number): number {
    return Math.floor(100 * Math.pow(1.5, level - 1))
  }
  
  /**
   * 检查功能解锁
   */
  private checkFeatureUnlocks(): void {
    const unlocks: Record<number, string[]> = {
      2: ['advanced_farming'],
      3: ['crop_breeding'],
      5: ['automation'],
      10: ['master_farming']
    }
    
    const newUnlocks = unlocks[this.state.level] || []
    newUnlocks.forEach(feature => {
      if (!this.state.unlockedFeatures.has(feature)) {
        this.state.unlockedFeatures.add(feature)
        console.log(`🔓 解锁新功能: ${feature}`)
      }
    })
  }
  
  /**
   * 添加专注时间
   */
  addFocusTime(minutes: number): void {
    this.state.totalFocusTime += minutes
    // 注意：暂时跳过CropTimeManager调用，接口不匹配
    this.emitStateChange('resource_changed', { totalFocusTime: this.state.totalFocusTime })
  }
  
  /**
   * 获取资源
   */
  getResources(): Record<string, number> {
    return { ...this.state.resources }
  }
  
  /**
   * 消耗资源
   */
  consumeResources(costs: Record<string, number>): boolean {
    // 检查是否有足够资源
    for (const [resource, cost] of Object.entries(costs)) {
      if (this.state.resources[resource as keyof typeof this.state.resources] < cost) {
        return false
      }
    }
    
    // 消耗资源
    Object.entries(costs).forEach(([resource, cost]) => {
      this.state.resources[resource as keyof typeof this.state.resources] -= cost
    })
    
    this.emitStateChange('resource_changed', this.state.resources)
    return true
  }
  
  /**
   * 处理作物阶段变化事件
   */
  private handleCropStageChanged(event: any): void {
    const crop = this.state.crops.get(event.cropId)
    if (crop && event.data && event.data.toStage) {
      crop.stage = event.data.toStage
      crop.stageStartTime = Date.now()
      
      if (event.data.toStage === CropStage.READY_TO_HARVEST) {
        crop.harvestable = true
      }
      
      console.log(`作物 ${crop.id} 进入 ${event.data.toStage} 阶段`)
    }
  }
  
  /**
   * 处理作物准备收获事件
   */
  private handleCropReadyToHarvest(event: any): void {
    const crop = this.state.crops.get(event.cropId)
    if (crop) {
      crop.harvestable = true
      crop.stage = CropStage.READY_TO_HARVEST
      console.log(`作物 ${crop.id} 准备收获`)
    }
  }
  
  /**
   * 生成作物ID
   */
  private generateCropId(): string {
    return 'crop_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
  }
  
  /**
   * 发射状态变化事件
   */
  private emitStateChange(type: StateChangeEvent['type'], data: any): void {
    const event: StateChangeEvent = {
      type,
      data,
      timestamp: Date.now()
    }
    
    this.emit('stateChange', event)
    this.emit(type, event)
  }
  
  /**
   * 保存游戏状态
   */
  async saveGameState(options: SaveOptions = {}): Promise<boolean> {
    if (this.isSaving) {
      console.warn('正在保存中，跳过本次保存')
      return false
    }
    
    this.isSaving = true
    
    try {
      const saveData = this.prepareSaveData()
      
      // 序列化数据
      const serializedData = JSON.stringify(saveData)
      
      // 保存到localStorage
      localStorage.setItem(this.saveKey, serializedData)
      
      // 更新保存时间
      this.state.gameTime.lastSaveTime = Date.now()
      
      this.emit('gameSaved', { timestamp: Date.now() })
      console.log('💾 游戏状态已保存')
      return true
      
    } catch (error) {
      console.error('保存游戏失败:', error)
      this.emit('saveError', { error })
      return false
    } finally {
      this.isSaving = false
    }
  }
  
  /**
   * 准备保存数据
   */
  private prepareSaveData(): any {
    // 序列化Maps和Sets
    const saveData = {
      ...this.state,
      crops: Array.from(this.state.crops.entries()),
      unlockedFeatures: Array.from(this.state.unlockedFeatures),
      achievements: Array.from(this.state.achievements)
    }
    
    return saveData
  }
  
  /**
   * 加载游戏状态
   */
  async loadGameState(): Promise<boolean> {
    if (this.isLoading) {
      console.warn('正在加载中')
      return false
    }
    
    this.isLoading = true
    
    try {
      const serializedData = localStorage.getItem(this.saveKey)
      
      if (!serializedData) {
        console.log('没有找到保存数据')
        return false
      }
      
      const saveData = JSON.parse(serializedData)
      
      if (!this.validateSaveData(saveData)) {
        console.error('保存数据验证失败')
        return false
      }
      
      this.restoreGameState(saveData)
      this.emit('gameLoaded', { timestamp: Date.now() })
      console.log('📂 游戏状态已加载')
      return true
      
    } catch (error) {
      console.error('加载游戏失败:', error)
      this.emit('loadError', { error })
      return false
    } finally {
      this.isLoading = false
    }
  }
  
  /**
   * 验证保存数据
   */
  private validateSaveData(data: any): boolean {
    return data && 
           typeof data.version === 'string' &&
           typeof data.level === 'number' &&
           typeof data.experience === 'number' &&
           Array.isArray(data.crops)
  }
  
  /**
   * 恢复游戏状态
   */
  private restoreGameState(data: any): void {
    // 恢复基本状态
    this.state = {
      ...this.state,
      ...data,
      crops: new Map(data.crops),
      unlockedFeatures: new Set(data.unlockedFeatures || []),
      achievements: new Set(data.achievements || [])
    }
    
    // 确保farmGrid结构正确
    if (!this.state.farmGrid.plots) {
      this.state.farmGrid = {
        width: this.state.gridSize.width,
        height: this.state.gridSize.height,
        plots: Array(this.state.gridSize.height).fill(null).map(() => Array(this.state.gridSize.width).fill(null))
      }
    }
    
    // 重新建立作物在网格中的位置
    this.state.crops.forEach((crop) => {
      if (crop.position && crop.position.gridX !== undefined && crop.position.gridY !== undefined) {
        const { gridX, gridY } = crop.position
        if (gridY < this.state.farmGrid.height && gridX < this.state.farmGrid.width) {
          this.state.farmGrid.plots[gridY][gridX] = crop
        }
      }
    })
  }
  
  /**
   * 启动自动保存
   */
  private startAutoSave(): void {
    if (this.autoSaveTimer) {
      clearInterval(this.autoSaveTimer)
    }
    
    if (this.state.settings.autoSave) {
      this.autoSaveTimer = window.setInterval(() => {
        this.saveGameState({ compress: true })
      }, this.state.settings.autoSaveInterval)
    }
  }
  
  /**
   * 重置游戏状态
   */
  resetGameState(): void {
    this.state = this.createDefaultState()
    this.emit('gameReset', { timestamp: Date.now() })
    console.log('🔄 游戏状态已重置')
  }
  
  /**
   * 更新游戏设置
   */
  updateSettings(newSettings: Partial<GameState['settings']>): void {
    this.state.settings = { ...this.state.settings, ...newSettings }
    
    // 重新配置自动保存
    if ('autoSave' in newSettings || 'autoSaveInterval' in newSettings) {
      this.startAutoSave()
    }
    
    this.emitStateChange('resource_changed', { settings: this.state.settings })
  }
  
  /**
   * 获取游戏统计
   */
  getGameStats(): {
    totalPlayTime: number
    totalCropsHarvested: number
    totalFocusTime: number
    level: number
    experience: number
    nextLevelExp: number
    cropsCount: number
    resourcesTotal: number
  } {
    const resourcesTotal = Object.values(this.state.resources).reduce((sum, val) => sum + val, 0)
    
    return {
      totalPlayTime: this.state.gameTime.totalPlayTime,
      totalCropsHarvested: this.state.totalCropsHarvested,
      totalFocusTime: this.state.totalFocusTime,
      level: this.state.level,
      experience: this.state.experience,
      nextLevelExp: this.getExperienceRequired(this.state.level),
      cropsCount: this.state.crops.size,
      resourcesTotal
    }
  }
  
  /**
   * 销毁管理器
   */
  destroy(): void {
    if (this.autoSaveTimer) {
      clearInterval(this.autoSaveTimer)
      this.autoSaveTimer = null
    }
    
    this.removeAllListeners()
    console.log('🧹 GameStateManager 已销毁')
  }
} 