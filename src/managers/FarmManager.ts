import { 
  Farm, 
  FarmSlot, 
  AgriculturalItem, 
  ItemCategory, 
  GrowthStage, 
  Season, 
  Weather 
} from '../types/agriculture'
import { ItemRarity } from '../types/lootbox'
import { EventEmitter } from 'eventemitter3'

export class FarmManager extends EventEmitter {
  private farm: Farm
  private currentSeason: Season = Season.SPRING
  private currentWeather: Weather = Weather.SUNNY
  private gameTime: number = Date.now()

  constructor(initialFarm?: Partial<Farm>) {
    super()
    
    this.farm = {
      id: 'player_farm',
      name: '我的农场',
      level: 1,
      experience: 0,
      slots: [],
      maxSlots: 9,
      unlockCost: 1000,
      properties: {
        wateringEfficiency: 1.0,
        fertilizerEfficiency: 1.0,
        harvestBonus: 1.0,
        growthSpeedMultiplier: 1.0
      },
      buildings: [],
      decorations: [],
      ...initialFarm
    }

    this.initializeFarmSlots()
  }

  // 初始化农田槽位
  private initializeFarmSlots(): void {
    const gridSize = Math.ceil(Math.sqrt(this.farm.maxSlots))
    
    for (let i = 0; i < this.farm.maxSlots; i++) {
      const x = (i % gridSize) * 100 + 50
      const y = Math.floor(i / gridSize) * 100 + 50
      
      const slot: FarmSlot = {
        id: `slot_${i}`,
        x,
        y,
        isUnlocked: i < 6, // 初始解锁6个槽位
        isOccupied: false,
        soilQuality: 50 + Math.random() * 30, // 50-80质量
        moistureLevel: 100,
        fertilizerLevel: 0
      }
      
      this.farm.slots.push(slot)
    }
  }

  // 种植作物
  async plantCrop(slotId: string, item: AgriculturalItem): Promise<{ success: boolean; error?: string }> {
    const slot = this.farm.slots.find(s => s.id === slotId)
    
    if (!slot) {
      return { success: false, error: '农田槽位不存在' }
    }

    if (!slot.isUnlocked) {
      return { success: false, error: '农田槽位未解锁' }
    }

    if (slot.isOccupied) {
      return { success: false, error: '农田槽位已被占用' }
    }

    if (item.category !== ItemCategory.SEED && item.category !== ItemCategory.CROP) {
      return { success: false, error: '只能种植种子或作物' }
    }

    // 准备种植的作物
    const plantedItem: AgriculturalItem = {
      ...item,
      growth: {
        ...item.growth,
        plantedTime: this.gameTime,
        currentStage: GrowthStage.SEED,
        isReady: false,
        needsWater: true,
        needsFertilizer: false
      },
      location: {
        farmSlotId: slotId,
        x: slot.x,
        y: slot.y
      }
    }

    // 计算实际生长时间（受环境和农场属性影响）
    const growthTime = this.calculateActualGrowthTime(plantedItem)
    plantedItem.production.nextHarvestTime = this.gameTime + growthTime * 1000

    // 更新槽位
    slot.isOccupied = true
    slot.currentItem = plantedItem

    this.emit('cropPlanted', {
      slotId,
      item: plantedItem,
      estimatedHarvestTime: plantedItem.production.nextHarvestTime
    })

    return { success: true }
  }

  // 收获作物
  async harvestCrop(slotId: string): Promise<{ success: boolean; items?: AgriculturalItem[]; error?: string }> {
    const slot = this.farm.slots.find(s => s.id === slotId)
    
    if (!slot || !slot.currentItem) {
      return { success: false, error: '该槽位没有作物' }
    }

    const item = slot.currentItem
    
    // 检查是否成熟
    if (!this.isCropReady(item)) {
      const remainingTime = (item.production.nextHarvestTime || 0) - this.gameTime
      const remainingHours = Math.ceil(remainingTime / (1000 * 60 * 60))
      return { success: false, error: `作物还需要 ${remainingHours} 小时才能收获` }
    }

    // 计算收获量
    const harvestItems = this.calculateHarvest(item, slot)
    
    // 增加农场经验
    const experience = this.calculateExperience(item)
    this.addExperience(experience)

    // 清理槽位
    slot.isOccupied = false
    slot.currentItem = undefined
    slot.moistureLevel = Math.max(0, slot.moistureLevel - 20) // 收获后土壤失水
    slot.fertilizerLevel = Math.max(0, slot.fertilizerLevel - 10) // 消耗肥料

    this.emit('cropHarvested', {
      slotId,
      originalItem: item,
      harvestItems,
      experienceGained: experience
    })

    return { success: true, items: harvestItems }
  }

  // 浇水
  async waterSlot(slotId: string): Promise<{ success: boolean; error?: string }> {
    const slot = this.farm.slots.find(s => s.id === slotId)
    
    if (!slot) {
      return { success: false, error: '农田槽位不存在' }
    }

    if (!slot.currentItem) {
      return { success: false, error: '该槽位没有作物需要浇水' }
    }

    if (slot.moistureLevel >= 80) {
      return { success: false, error: '土壤湿度充足，无需浇水' }
    }

    // 浇水效果
    const wateringAmount = 40 * this.farm.properties.wateringEfficiency
    slot.moistureLevel = Math.min(100, slot.moistureLevel + wateringAmount)
    slot.lastWatered = this.gameTime

    // 更新作物需水状态
    if (slot.currentItem.growth.needsWater) {
      slot.currentItem.growth.needsWater = false
    }

    this.emit('slotWatered', {
      slotId,
      newMoistureLevel: slot.moistureLevel,
      wateringEfficiency: this.farm.properties.wateringEfficiency
    })

    return { success: true }
  }

  // 施肥
  async fertilizeSlot(slotId: string, fertilizerType: string = 'basic'): Promise<{ success: boolean; error?: string }> {
    const slot = this.farm.slots.find(s => s.id === slotId)
    
    if (!slot) {
      return { success: false, error: '农田槽位不存在' }
    }

    if (!slot.currentItem) {
      return { success: false, error: '该槽位没有作物需要施肥' }
    }

    if (slot.fertilizerLevel >= 100) {
      return { success: false, error: '土壤肥力充足，无需施肥' }
    }

    // 施肥效果
    const fertilizerAmount = this.getFertilizerAmount(fertilizerType) * this.farm.properties.fertilizerEfficiency
    slot.fertilizerLevel = Math.min(100, slot.fertilizerLevel + fertilizerAmount)
    slot.lastFertilized = this.gameTime

    // 更新作物施肥状态
    if (slot.currentItem.growth.needsFertilizer) {
      slot.currentItem.growth.needsFertilizer = false
    }

    // 施肥可能影响生长速度
    this.updateCropGrowth(slot.currentItem)

    this.emit('slotFertilized', {
      slotId,
      fertilizerType,
      newFertilizerLevel: slot.fertilizerLevel,
      fertilizerEfficiency: this.farm.properties.fertilizerEfficiency
    })

    return { success: true }
  }

  // 解锁新槽位
  async unlockSlot(slotId: string): Promise<{ success: boolean; cost?: number; error?: string }> {
    const slot = this.farm.slots.find(s => s.id === slotId)
    
    if (!slot) {
      return { success: false, error: '农田槽位不存在' }
    }

    if (slot.isUnlocked) {
      return { success: false, error: '该槽位已解锁' }
    }

    const cost = this.getSlotUnlockCost()
    
    // 这里需要检查玩家是否有足够的货币，暂时跳过
    
    slot.isUnlocked = true
    
    this.emit('slotUnlocked', {
      slotId,
      cost,
      totalUnlockedSlots: this.farm.slots.filter(s => s.isUnlocked).length
    })

    return { success: true, cost }
  }

  // 更新游戏时间和环境
  updateGameTime(newTime: number, season?: Season, weather?: Weather): void {
    this.gameTime = newTime
    
    if (season && season !== this.currentSeason) {
      this.currentSeason = season
      this.emit('seasonChanged', { season })
    }
    
    if (weather && weather !== this.currentWeather) {
      this.currentWeather = weather
      this.emit('weatherChanged', { weather })
    }

    // 更新所有作物的生长状态
    this.updateAllCrops()
  }

  // 更新所有作物状态
  private updateAllCrops(): void {
    for (const slot of this.farm.slots) {
      if (slot.currentItem) {
        this.updateCropGrowth(slot.currentItem)
        this.updateSlotConditions(slot)
      }
    }
  }

  // 更新作物生长
  private updateCropGrowth(item: AgriculturalItem): void {
    if (!item.growth.plantedTime || !item.production.nextHarvestTime) return

    const elapsedTime = this.gameTime - item.growth.plantedTime
    const totalGrowthTime = item.production.nextHarvestTime - item.growth.plantedTime
    const growthProgress = elapsedTime / totalGrowthTime

    // 更新生长阶段
    if (growthProgress >= 1) {
      item.growth.currentStage = GrowthStage.READY
      item.growth.isReady = true
    } else if (growthProgress >= 0.8) {
      item.growth.currentStage = GrowthStage.MATURE
    } else if (growthProgress >= 0.6) {
      item.growth.currentStage = GrowthStage.FLOWERING
    } else if (growthProgress >= 0.3) {
      item.growth.currentStage = GrowthStage.GROWING
    } else if (growthProgress >= 0.1) {
      item.growth.currentStage = GrowthStage.SPROUT
    }
  }

  // 更新槽位条件
  private updateSlotConditions(slot: FarmSlot): void {
    const timeDiff = this.gameTime - (slot.lastWatered || this.gameTime)
    const hoursPassed = timeDiff / (1000 * 60 * 60)

    // 土壤湿度自然减少
    if (hoursPassed > 0) {
      const moistureLoss = Math.min(hoursPassed * 2, 30) // 每小时失水2%，最多30%
      slot.moistureLevel = Math.max(0, slot.moistureLevel - moistureLoss)
    }

    // 检查作物是否需要浇水
    if (slot.currentItem && slot.moistureLevel < 30) {
      slot.currentItem.growth.needsWater = true
    }

    // 检查作物是否需要施肥
    if (slot.currentItem && slot.fertilizerLevel < 20) {
      slot.currentItem.growth.needsFertilizer = true
    }
  }

  // 计算实际生长时间
  private calculateActualGrowthTime(item: AgriculturalItem): number {
    let baseTime = item.growth.growthTime * 3600 // 转换为秒

    // 应用农场生长速度加成
    baseTime *= (1 / this.farm.properties.growthSpeedMultiplier)

    // 季节影响
    const seasonMultiplier = this.getSeasonMultiplier(item, this.currentSeason)
    baseTime *= seasonMultiplier

    // 天气影响
    const weatherMultiplier = this.getWeatherMultiplier(item, this.currentWeather)
    baseTime *= weatherMultiplier

    return baseTime
  }

  // 季节影响系数
  private getSeasonMultiplier(item: AgriculturalItem, season: Season): number {
    // 不同作物在不同季节有不同的生长速度
    if (item.special?.seasonBonus) {
      return 1 - item.special.seasonBonus * 0.01 // 季节加成转换为时间减少
    }

    switch (season) {
      case Season.SPRING: return 0.9  // 春季生长快
      case Season.SUMMER: return 1.0  // 夏季正常
      case Season.AUTUMN: return 1.1  // 秋季稍慢
      case Season.WINTER: return 1.3  // 冬季生长慢
      default: return 1.0
    }
  }

  // 天气影响系数
  private getWeatherMultiplier(item: AgriculturalItem, weather: Weather): number {
    if (item.special?.weatherBonus) {
      return 1 - item.special.weatherBonus * 0.01
    }

    switch (weather) {
      case Weather.SUNNY: return 1.0    // 晴天正常
      case Weather.CLOUDY: return 1.05  // 阴天稍慢
      case Weather.RAINY: return 0.95   // 雨天稍快
      case Weather.STORMY: return 1.2   // 暴风雨生长慢
      case Weather.SNOWY: return 1.4    // 下雪生长很慢
      default: return 1.0
    }
  }

  // 检查作物是否成熟
  private isCropReady(item: AgriculturalItem): boolean {
    return this.gameTime >= (item.production.nextHarvestTime || 0)
  }

  // 计算收获量
  private calculateHarvest(item: AgriculturalItem, slot: FarmSlot): AgriculturalItem[] {
    const baseYield = item.production.currentDaily || 
                     Math.floor(Math.random() * (item.production.maxDaily - item.production.minDaily + 1)) + 
                     item.production.minDaily

    // 应用各种加成
    let finalYield = baseYield

    // 土壤质量影响
    finalYield *= (0.5 + slot.soilQuality / 100)

    // 湿度影响
    if (slot.moistureLevel > 60) {
      finalYield *= 1.1
    } else if (slot.moistureLevel < 30) {
      finalYield *= 0.8
    }

    // 肥料影响
    if (slot.fertilizerLevel > 50) {
      finalYield *= 1.2
    }

    // 农场收获加成
    finalYield *= this.farm.properties.harvestBonus

    // 随机变化
    finalYield *= (0.8 + Math.random() * 0.4)

    const harvestCount = Math.max(1, Math.floor(finalYield))
    const items: AgriculturalItem[] = []

    for (let i = 0; i < harvestCount; i++) {
      const harvestItem: AgriculturalItem = {
        ...item,
        id: `${item.variety}_harvest_${Date.now()}_${i}`,
        category: ItemCategory.PRODUCT,
        growth: {
          growthTime: 0,
          currentStage: GrowthStage.READY,
          isReady: true
        },
        location: undefined
      }

      items.push(harvestItem)
    }

    return items
  }

  // 计算收获经验
  private calculateExperience(item: AgriculturalItem): number {
    const baseExp = 10
    const rarityMultiplier = this.getRarityExperienceMultiplier(item.rarity)
    const qualityBonus = item.quality * 0.1
    
    return Math.floor(baseExp * rarityMultiplier + qualityBonus)
  }

  // 稀有度经验倍数
  private getRarityExperienceMultiplier(rarity: ItemRarity): number {
    switch (rarity) {
      case ItemRarity.GRAY: return 1
      case ItemRarity.GREEN: return 1.5
      case ItemRarity.BLUE: return 2.5
      case ItemRarity.ORANGE: return 4
      case ItemRarity.GOLD: return 6
      case ItemRarity.GOLD_RED: return 10
      default: return 1
    }
  }

  // 添加农场经验
  private addExperience(amount: number): void {
    this.farm.experience += amount
    
    // 检查升级
    const requiredExp = this.getRequiredExperience(this.farm.level)
    if (this.farm.experience >= requiredExp) {
      this.levelUp()
    }

    this.emit('experienceGained', {
      amount,
      totalExperience: this.farm.experience,
      currentLevel: this.farm.level,
      nextLevelExp: this.getRequiredExperience(this.farm.level)
    })
  }

  // 农场升级
  private levelUp(): void {
    this.farm.level += 1
    
    // 升级奖励
    this.farm.properties.harvestBonus += 0.05
    this.farm.properties.growthSpeedMultiplier += 0.02
    
    // 解锁新槽位
    if (this.farm.level % 5 === 0 && this.farm.maxSlots < 20) {
      this.farm.maxSlots += 2
      this.addNewSlots(2)
    }

    this.emit('farmLevelUp', {
      newLevel: this.farm.level,
      bonusesGained: {
        harvestBonus: 0.05,
        growthSpeedMultiplier: 0.02
      },
      newSlotsUnlocked: this.farm.level % 5 === 0 ? 2 : 0
    })
  }

  // 添加新槽位
  private addNewSlots(count: number): void {
    const currentSlotCount = this.farm.slots.length
    const gridSize = Math.ceil(Math.sqrt(this.farm.maxSlots))
    
    for (let i = 0; i < count; i++) {
      const slotIndex = currentSlotCount + i
      const x = (slotIndex % gridSize) * 100 + 50
      const y = Math.floor(slotIndex / gridSize) * 100 + 50
      
      const slot: FarmSlot = {
        id: `slot_${slotIndex}`,
        x,
        y,
        isUnlocked: false,
        isOccupied: false,
        soilQuality: 50 + Math.random() * 30,
        moistureLevel: 100,
        fertilizerLevel: 0
      }
      
      this.farm.slots.push(slot)
    }
  }

  // 获取升级所需经验
  private getRequiredExperience(level: number): number {
    return level * 100 + Math.pow(level, 2) * 50
  }

  // 获取槽位解锁费用
  private getSlotUnlockCost(): number {
    const unlockedSlots = this.farm.slots.filter(s => s.isUnlocked).length
    return Math.floor(1000 * Math.pow(1.5, unlockedSlots - 6))
  }

  // 获取肥料效果
  private getFertilizerAmount(type: string): number {
    switch (type) {
      case 'basic': return 30
      case 'advanced': return 50
      case 'premium': return 80
      default: return 20
    }
  }

  // 获取农场状态
  getFarmState(): Farm {
    return { ...this.farm }
  }

  // 获取特定槽位状态
  getSlotState(slotId: string): FarmSlot | null {
    return this.farm.slots.find(s => s.id === slotId) || null
  }

  // 获取所有成熟的作物
  getReadyCrops(): Array<{ slotId: string; item: AgriculturalItem }> {
    return this.farm.slots
      .filter(slot => slot.currentItem && this.isCropReady(slot.currentItem))
      .map(slot => ({ slotId: slot.id, item: slot.currentItem! }))
  }

  // 获取需要照料的作物
  getCropsNeedingCare(): Array<{ slotId: string; item: AgriculturalItem; needs: string[] }> {
    return this.farm.slots
      .filter(slot => slot.currentItem)
      .map(slot => {
        const needs: string[] = []
        if (slot.currentItem!.growth.needsWater) needs.push('water')
        if (slot.currentItem!.growth.needsFertilizer) needs.push('fertilizer')
        return { slotId: slot.id, item: slot.currentItem!, needs }
      })
      .filter(item => item.needs.length > 0)
  }

  // 获取农场统计信息
  getFarmStatistics() {
    const slots = this.farm.slots
    const unlockedSlots = slots.filter(s => s.isUnlocked)
    const occupiedSlots = slots.filter(s => s.isOccupied)
    const readyCrops = this.getReadyCrops()
    const cropsNeedingCare = this.getCropsNeedingCare()

    return {
      level: this.farm.level,
      experience: this.farm.experience,
      nextLevelExp: this.getRequiredExperience(this.farm.level),
      totalSlots: slots.length,
      unlockedSlots: unlockedSlots.length,
      occupiedSlots: occupiedSlots.length,
      readyCrops: readyCrops.length,
      cropsNeedingCare: cropsNeedingCare.length,
      averageSoilQuality: unlockedSlots.reduce((sum, slot) => sum + slot.soilQuality, 0) / unlockedSlots.length,
      properties: this.farm.properties
    }
  }
} 