import { GameState } from './GameStateManager'

// 存储配置接口
export interface StorageConfig {
  storageType: 'localStorage' | 'indexedDB' | 'file'
  enableCompression: boolean
  enableEncryption: boolean
  autoBackup: boolean
  maxBackups: number
  backupInterval: number // 毫秒
}

// 存储键定义
export interface StorageKeys {
  GAME_DATA: string
  SETTINGS: string
  PLAYER_PROFILE: string
  BACKUP_PREFIX: string
  METADATA: string
}

// 存储元数据
export interface StorageMetadata {
  version: string
  lastSaved: number
  totalSaves: number
  gameVersion: string
  playerData: {
    playerId: string
    playerName: string
    level: number
  }
}

// 备份数据结构
export interface BackupData {
  id: string
  timestamp: number
  gameData: any
  metadata: StorageMetadata
  checksum: string
}

/**
 * 存储管理器
 * 负责游戏数据的持久化、压缩、加密、备份等功能
 */
export class StorageManager {
  private config: StorageConfig
  private keys: StorageKeys
  private backupTimer: NodeJS.Timeout | null = null
  
  constructor(config: Partial<StorageConfig> = {}) {
    this.config = {
      storageType: 'localStorage',
      enableCompression: false,
      enableEncryption: false,
      autoBackup: true,
      maxBackups: 5,
      backupInterval: 300000, // 5分钟
      ...config
    }
    
    this.keys = {
      GAME_DATA: 'selfgame_data',
      SETTINGS: 'selfgame_settings',
      PLAYER_PROFILE: 'selfgame_player',
      BACKUP_PREFIX: 'selfgame_backup_',
      METADATA: 'selfgame_metadata'
    }
    
    this.initializeStorage()
    this.startAutoBackup()
  }
  
  /**
   * 初始化存储系统
   */
  private initializeStorage(): void {
    try {
      // 检查存储可用性
      if (!this.isStorageAvailable()) {
        console.warn('本地存储不可用，将使用内存存储')
        this.config.storageType = 'localStorage' // 降级到localStorage
      }
      
      // 检查现有数据版本
      this.checkDataVersion()
      
      console.log('存储系统初始化完成')
    } catch (error) {
      console.error('存储系统初始化失败:', error)
    }
  }
  
  /**
   * 检查存储可用性
   */
  private isStorageAvailable(): boolean {
    try {
      const testKey = '__storage_test__'
      localStorage.setItem(testKey, 'test')
      localStorage.removeItem(testKey)
      return true
    } catch {
      return false
    }
  }
  
  /**
   * 检查数据版本兼容性
   */
  private checkDataVersion(): void {
    const metadata = this.loadMetadata()
    if (metadata && metadata.version !== '1.0.0') {
      console.log(`检测到数据版本变化: ${metadata.version} -> 1.0.0`)
      // 这里可以实现数据迁移逻辑
      this.migrateData(metadata.version, '1.0.0')
    }
  }
  
  /**
   * 数据迁移
   */
  private migrateData(fromVersion: string, toVersion: string): void {
    console.log(`正在迁移数据从 ${fromVersion} 到 ${toVersion}`)
    
    try {
      // 备份旧数据
      const oldData = this.loadRawData(this.keys.GAME_DATA)
      if (oldData) {
        this.createBackup(oldData, `migration_${fromVersion}_${Date.now()}`)
      }
      
      // 执行迁移逻辑
      switch (fromVersion) {
        case '0.9.0':
          this.migrateFrom090(oldData)
          break
        // 可以添加更多版本的迁移逻辑
        default:
          console.warn(`没有找到从 ${fromVersion} 的迁移路径`)
      }
      
      console.log('数据迁移完成')
    } catch (error) {
      console.error('数据迁移失败:', error)
    }
  }
  
  /**
   * 从0.9.0版本迁移数据
   */
  private migrateFrom090(oldData: any): void {
    if (!oldData) return
    
    // 示例迁移逻辑：添加新字段
    if (oldData.resources && !oldData.resources.meditation) {
      oldData.resources.meditation = 0
    }
    
    // 保存迁移后的数据
    this.saveRawData(this.keys.GAME_DATA, oldData)
  }
  
  /**
   * 保存游戏状态
   */
  async saveGameState(gameState: GameState): Promise<boolean> {
    try {
      console.log('开始保存游戏状态...')
      
      // 准备保存数据
      const dataToSave = this.prepareGameData(gameState)
      
      // 压缩数据（如果启用）
      let processedData = dataToSave
      if (this.config.enableCompression) {
        processedData = await this.compressData(processedData)
      }
      
      // 加密数据（如果启用）
      if (this.config.enableEncryption) {
        processedData = await this.encryptData(processedData)
      }
      
      // 保存主数据
      const success = this.saveRawData(this.keys.GAME_DATA, processedData)
      
      if (success) {
        // 更新元数据
        this.updateMetadata(gameState)
        
        // 创建备份（如果启用）
        if (this.config.autoBackup) {
          this.createBackup(processedData)
        }
        
        console.log('✅ 游戏状态保存成功')
        return true
      } else {
        throw new Error('保存数据失败')
      }
    } catch (error) {
      console.error('❌ 保存游戏状态失败:', error)
      return false
    }
  }
  
  /**
   * 加载游戏状态
   */
  async loadGameState(): Promise<GameState | null> {
    try {
      console.log('开始加载游戏状态...')
      
      // 加载原始数据
      let rawData = this.loadRawData(this.keys.GAME_DATA)
      
      if (!rawData) {
        console.log('没有找到保存的游戏数据')
        return null
      }
      
      // 解密数据（如果启用）
      if (this.config.enableEncryption) {
        rawData = await this.decryptData(rawData)
      }
      
      // 解压数据（如果启用）
      if (this.config.enableCompression) {
        rawData = await this.decompressData(rawData)
      }
      
      // 恢复游戏状态
      const gameState = this.restoreGameData(rawData)
      
      console.log('✅ 游戏状态加载成功')
      return gameState
    } catch (error) {
      console.error('❌ 加载游戏状态失败:', error)
      
      // 尝试从备份恢复
      console.log('尝试从备份恢复...')
      return this.restoreFromBackup()
    }
  }
  
  /**
   * 准备游戏数据保存
   */
  private prepareGameData(gameState: GameState): any {
    // 转换Map和Set为可序列化的格式
    const farmGridObj: Record<string, string> = {}
    gameState.farmGrid.forEach((value, key) => {
      farmGridObj[key] = value
    })
    
    const cropsObj: Record<string, any> = {}
    gameState.crops.forEach((value, key) => {
      cropsObj[key] = value
    })
    
    return {
      ...gameState,
      farmGrid: farmGridObj,
      crops: cropsObj,
      unlockedFeatures: Array.from(gameState.unlockedFeatures),
      achievements: Array.from(gameState.achievements),
      _saveTime: Date.now(),
      _version: '1.0.0'
    }
  }
  
  /**
   * 恢复游戏数据
   */
  private restoreGameData(rawData: any): GameState {
    // 重建Map和Set对象
    const farmGrid = new Map(Object.entries(rawData.farmGrid || {}))
    const crops = new Map(Object.entries(rawData.crops || {}))
    const unlockedFeatures = new Set(rawData.unlockedFeatures || [])
    const achievements = new Set(rawData.achievements || [])
    
    return {
      ...rawData,
      farmGrid,
      crops,
      unlockedFeatures,
      achievements
    }
  }
  
  /**
   * 保存原始数据
   */
  private saveRawData(key: string, data: any): boolean {
    try {
      const serializedData = JSON.stringify(data)
      
      switch (this.config.storageType) {
        case 'localStorage':
          localStorage.setItem(key, serializedData)
          break
        case 'indexedDB':
          // 暂未实现IndexedDB
          console.warn('IndexedDB暂未实现，降级到localStorage')
          localStorage.setItem(key, serializedData)
          break
        default:
          throw new Error(`不支持的存储类型: ${this.config.storageType}`)
      }
      
      return true
    } catch (error) {
      console.error('保存原始数据失败:', error)
      return false
    }
  }
  
  /**
   * 加载原始数据
   */
  private loadRawData(key: string): any {
    try {
      let serializedData: string | null = null
      
      switch (this.config.storageType) {
        case 'localStorage':
          serializedData = localStorage.getItem(key)
          break
        case 'indexedDB':
          // 暂未实现IndexedDB
          console.warn('IndexedDB暂未实现，降级到localStorage')
          serializedData = localStorage.getItem(key)
          break
        default:
          throw new Error(`不支持的存储类型: ${this.config.storageType}`)
      }
      
      return serializedData ? JSON.parse(serializedData) : null
    } catch (error) {
      console.error('加载原始数据失败:', error)
      return null
    }
  }
  
  /**
   * 更新存储元数据
   */
  private updateMetadata(gameState: GameState): void {
    const metadata: StorageMetadata = {
      version: '1.0.0',
      lastSaved: Date.now(),
      totalSaves: (this.loadMetadata()?.totalSaves || 0) + 1,
      gameVersion: gameState.version,
      playerData: {
        playerId: gameState.playerId,
        playerName: gameState.playerName,
        level: gameState.level
      }
    }
    
    this.saveRawData(this.keys.METADATA, metadata)
  }
  
  /**
   * 加载存储元数据
   */
  private loadMetadata(): StorageMetadata | null {
    return this.loadRawData(this.keys.METADATA)
  }
  
  /**
   * 创建数据备份
   */
  private createBackup(data: any, backupId?: string): void {
    try {
      const id = backupId || `auto_${Date.now()}`
      const backup: BackupData = {
        id,
        timestamp: Date.now(),
        gameData: data,
        metadata: this.loadMetadata() || {} as StorageMetadata,
        checksum: this.calculateChecksum(data)
      }
      
      const backupKey = this.keys.BACKUP_PREFIX + id
      this.saveRawData(backupKey, backup)
      
      // 清理旧备份
      this.cleanupOldBackups()
      
      console.log(`✅ 创建备份: ${id}`)
    } catch (error) {
      console.error('创建备份失败:', error)
    }
  }
  
  /**
   * 从备份恢复数据
   */
  private async restoreFromBackup(): Promise<GameState | null> {
    try {
      const backups = this.listBackups()
      
      if (backups.length === 0) {
        console.log('没有找到可用的备份')
        return null
      }
      
      // 按时间戳排序，使用最新的备份
      backups.sort((a, b) => b.timestamp - a.timestamp)
      
      for (const backup of backups) {
        try {
          // 验证备份完整性
          if (this.validateBackup(backup)) {
            console.log(`从备份 ${backup.id} 恢复数据`)
            return this.restoreGameData(backup.gameData)
          }
        } catch (error) {
          console.warn(`备份 ${backup.id} 损坏，尝试下一个`)
        }
      }
      
      console.error('所有备份都无法使用')
      return null
    } catch (error) {
      console.error('从备份恢复失败:', error)
      return null
    }
  }
  
  /**
   * 列出所有备份
   */
  private listBackups(): BackupData[] {
    const backups: BackupData[] = []
    
    try {
      // 遍历localStorage查找备份
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i)
        if (key && key.startsWith(this.keys.BACKUP_PREFIX)) {
          const backup = this.loadRawData(key)
          if (backup && this.isValidBackup(backup)) {
            backups.push(backup)
          }
        }
      }
    } catch (error) {
      console.error('列出备份失败:', error)
    }
    
    return backups
  }
  
  /**
   * 验证备份是否有效
   */
  private isValidBackup(backup: any): backup is BackupData {
    return backup &&
           typeof backup.id === 'string' &&
           typeof backup.timestamp === 'number' &&
           backup.gameData &&
           backup.metadata &&
           typeof backup.checksum === 'string'
  }
  
  /**
   * 验证备份完整性
   */
  private validateBackup(backup: BackupData): boolean {
    try {
      const currentChecksum = this.calculateChecksum(backup.gameData)
      return currentChecksum === backup.checksum
    } catch {
      return false
    }
  }
  
  /**
   * 计算数据校验和
   */
  private calculateChecksum(data: any): string {
    // 简单的校验和计算，实际项目中可以使用更复杂的算法
    const str = JSON.stringify(data)
    let hash = 0
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // 转换为32位整数
    }
    return hash.toString(16)
  }
  
  /**
   * 清理旧备份
   */
  private cleanupOldBackups(): void {
    try {
      const backups = this.listBackups()
      
      if (backups.length > this.config.maxBackups) {
        // 按时间戳排序
        backups.sort((a, b) => b.timestamp - a.timestamp)
        
        // 删除多余的备份
        const toDelete = backups.slice(this.config.maxBackups)
        toDelete.forEach(backup => {
          const key = this.keys.BACKUP_PREFIX + backup.id
          localStorage.removeItem(key)
          console.log(`删除旧备份: ${backup.id}`)
        })
      }
    } catch (error) {
      console.error('清理旧备份失败:', error)
    }
  }
  
  /**
   * 数据压缩（简化实现）
   */
  private async compressData(data: any): Promise<any> {
    // 简化实现：仅移除空格
    // 实际项目中可以使用LZ77、gzip等压缩算法
    const json = JSON.stringify(data)
    return { _compressed: true, data: json }
  }
  
  /**
   * 数据解压（简化实现）
   */
  private async decompressData(compressedData: any): Promise<any> {
    if (compressedData._compressed) {
      return JSON.parse(compressedData.data)
    }
    return compressedData
  }
  
  /**
   * 数据加密（简化实现）
   */
  private async encryptData(data: any): Promise<any> {
    // 简化实现：Base64编码
    // 实际项目中应该使用真正的加密算法
    const json = JSON.stringify(data)
    const encoded = btoa(json)
    return { _encrypted: true, data: encoded }
  }
  
  /**
   * 数据解密（简化实现）
   */
  private async decryptData(encryptedData: any): Promise<any> {
    if (encryptedData._encrypted) {
      const decoded = atob(encryptedData.data)
      return JSON.parse(decoded)
    }
    return encryptedData
  }
  
  /**
   * 启动自动备份
   */
  private startAutoBackup(): void {
    if (this.backupTimer) {
      clearInterval(this.backupTimer)
    }
    
    if (this.config.autoBackup) {
      this.backupTimer = setInterval(() => {
        const gameData = this.loadRawData(this.keys.GAME_DATA)
        if (gameData) {
          this.createBackup(gameData)
        }
      }, this.config.backupInterval)
    }
  }
  
  /**
   * 导出游戏数据
   */
  exportGameData(): string | null {
    try {
      const gameData = this.loadRawData(this.keys.GAME_DATA)
      const metadata = this.loadMetadata()
      
      if (!gameData) {
        console.log('没有找到游戏数据可导出')
        return null
      }
      
      const exportData = {
        gameData,
        metadata,
        exportTime: Date.now(),
        version: '1.0.0'
      }
      
      return JSON.stringify(exportData, null, 2)
    } catch (error) {
      console.error('导出游戏数据失败:', error)
      return null
    }
  }
  
  /**
   * 导入游戏数据
   */
  async importGameData(jsonData: string): Promise<boolean> {
    try {
      const importData = JSON.parse(jsonData)
      
      // 验证导入数据格式
      if (!importData.gameData || !importData.version) {
        throw new Error('导入数据格式无效')
      }
      
      // 创建当前数据的备份
      const currentData = this.loadRawData(this.keys.GAME_DATA)
      if (currentData) {
        this.createBackup(currentData, `before_import_${Date.now()}`)
      }
      
      // 保存导入的数据
      const success = this.saveRawData(this.keys.GAME_DATA, importData.gameData)
      
      if (success) {
        // 更新元数据
        if (importData.metadata) {
          this.saveRawData(this.keys.METADATA, importData.metadata)
        }
        
        console.log('✅ 游戏数据导入成功')
        return true
      } else {
        throw new Error('保存导入数据失败')
      }
    } catch (error) {
      console.error('❌ 导入游戏数据失败:', error)
      return false
    }
  }
  
  /**
   * 清除所有数据
   */
  clearAllData(): void {
    try {
      // 创建最后的备份
      const gameData = this.loadRawData(this.keys.GAME_DATA)
      if (gameData) {
        this.createBackup(gameData, `final_backup_${Date.now()}`)
      }
      
      // 删除主要数据
      localStorage.removeItem(this.keys.GAME_DATA)
      localStorage.removeItem(this.keys.SETTINGS)
      localStorage.removeItem(this.keys.PLAYER_PROFILE)
      localStorage.removeItem(this.keys.METADATA)
      
      console.log('✅ 所有游戏数据已清除')
    } catch (error) {
      console.error('清除数据失败:', error)
    }
  }
  
  /**
   * 获取存储使用情况
   */
  getStorageUsage(): {
    used: number
    total: number
    percentage: number
    details: Record<string, number>
  } {
    const details: Record<string, number> = {}
    let totalUsed = 0
    
    try {
      // 计算各部分数据大小
      Object.values(this.keys).forEach(key => {
        const data = localStorage.getItem(key)
        if (data) {
          const size = new Blob([data]).size
          details[key] = size
          totalUsed += size
        }
      })
      
      // 计算备份大小
      const backups = this.listBackups()
      let backupSize = 0
      backups.forEach(backup => {
        const key = this.keys.BACKUP_PREFIX + backup.id
        const data = localStorage.getItem(key)
        if (data) {
          backupSize += new Blob([data]).size
        }
      })
      details['backups'] = backupSize
      totalUsed += backupSize
      
      // 估算总容量（localStorage通常是5-10MB）
      const estimatedTotal = 5 * 1024 * 1024 // 5MB
      const percentage = (totalUsed / estimatedTotal) * 100
      
      return {
        used: totalUsed,
        total: estimatedTotal,
        percentage: Math.min(percentage, 100),
        details
      }
    } catch (error) {
      console.error('获取存储使用情况失败:', error)
      return {
        used: 0,
        total: 0,
        percentage: 0,
        details: {}
      }
    }
  }
  
  /**
   * 销毁存储管理器
   */
  destroy(): void {
    if (this.backupTimer) {
      clearInterval(this.backupTimer)
      this.backupTimer = null
    }
  }
} 