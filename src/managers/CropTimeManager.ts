import {
  CropInstance,
  CropStage,
  CropGrowthEvent,
  CropManager,
  FarmGrid,
  CropStats
} from '../types/crop'
import {
  GrowthCalculator,
  growthCalculator,
  GROWTH_CONFIG,
  formatTimeRemaining,
  getGrowthSpeedDescription
} from '../utils/cropGrowth'

// 时间管理器事件
export interface TimeManagerEvent {
  type: 'batch_update' | 'stage_change' | 'harvest_ready' | 'focus_warning'
  timestamp: number
  data: any
}

// 批量更新结果
export interface BatchUpdateResult {
  updatedCrops: CropInstance[]
  stageChanges: { cropId: string; fromStage: CropStage; toStage: CropStage }[]
  harvestReady: string[]
  warnings: { cropId: string; message: string }[]
}

// 作物时间管理器
export class CropTimeManager {
  private crops: Map<string, CropInstance> = new Map()
  private updateInterval: number | null = null
  private lastUpdateTime: number = Date.now()
  private eventCallbacks: ((event: TimeManagerEvent) => void)[] = []
  private isPaused: boolean = false
  private globalFocusScore: number = 50
  
  constructor(
    private calculator: GrowthCalculator = growthCalculator
  ) {}
  
  /**
   * 启动时间管理器
   */
  start(): void {
    if (this.updateInterval) return
    
    this.updateInterval = window.setInterval(() => {
      if (!this.isPaused) {
        this.performBatchUpdate()
      }
    }, GROWTH_CONFIG.MIN_UPDATE_INTERVAL)
    
    console.log('作物时间管理器已启动')
  }
  
  /**
   * 停止时间管理器
   */
  stop(): void {
    if (this.updateInterval) {
      clearInterval(this.updateInterval)
      this.updateInterval = null
    }
    
    console.log('作物时间管理器已停止')
  }
  
  /**
   * 暂停/恢复时间管理器
   */
  setPaused(paused: boolean): void {
    this.isPaused = paused
    
    // 暂停时记录所有作物的暂停时间
    if (paused) {
      const now = Date.now()
      this.crops.forEach(crop => {
        if (!crop.isPaused) {
          this.updateCrop(crop.id, { 
            isPaused: true,
            metadata: {
              ...crop.metadata,
              pauseStartTime: now
            } as any
          })
        }
      })
    } else {
      // 恢复时计算暂停惩罚
      const now = Date.now()
      this.crops.forEach(crop => {
        if (crop.isPaused && (crop.metadata as any).pauseStartTime) {
          const pauseDuration = now - (crop.metadata as any).pauseStartTime
          const penalty = this.calculator.calculatePausePenalty(crop, pauseDuration)
          
          this.updateCrop(crop.id, {
            isPaused: false,
            metadata: {
              ...crop.metadata,
              pauseStartTime: undefined,
              pausePenalty: (crop.metadata as any).pausePenalty + penalty
            } as any
          })
        }
      })
    }
    
    this.emitEvent({
      type: 'batch_update',
      timestamp: Date.now(),
      data: { paused }
    })
  }
  
  /**
   * 设置全局专注度分数
   */
  setGlobalFocusScore(score: number): void {
    this.globalFocusScore = Math.max(0, Math.min(100, score))
  }
  
  /**
   * 添加作物到管理器
   */
  addCrop(crop: CropInstance): void {
    this.crops.set(crop.id, { ...crop })
    console.log(`作物 ${crop.id} 已添加到时间管理器`)
  }
  
  /**
   * 从管理器移除作物
   */
  removeCrop(cropId: string): void {
    this.crops.delete(cropId)
    this.calculator.clearFocusHistory(cropId)
    console.log(`作物 ${cropId} 已从时间管理器移除`)
  }
  
  /**
   * 更新作物信息
   */
  updateCrop(cropId: string, updates: Partial<CropInstance>): void {
    const crop = this.crops.get(cropId)
    if (!crop) return
    
    this.crops.set(cropId, { ...crop, ...updates })
  }
  
  /**
   * 获取作物
   */
  getCrop(cropId: string): CropInstance | null {
    return this.crops.get(cropId) || null
  }
  
  /**
   * 获取所有作物
   */
  getAllCrops(): CropInstance[] {
    return Array.from(this.crops.values())
  }
  
  /**
   * 执行批量更新
   */
  private performBatchUpdate(): BatchUpdateResult {
    const now = Date.now()
    const deltaTime = now - this.lastUpdateTime
    this.lastUpdateTime = now
    
    const result: BatchUpdateResult = {
      updatedCrops: [],
      stageChanges: [],
      harvestReady: [],
      warnings: []
    }
    
    this.crops.forEach((crop, cropId) => {
      if (crop.isPaused || crop.stage === CropStage.HARVESTED) {
        return
      }
      
      try {
        const growthResult = this.calculator.calculateProgress(
          crop,
          this.globalFocusScore,
          deltaTime
        )
        
        let updatedCrop = { ...crop }
        
        // 更新生长数据
        updatedCrop.totalGrowthTime += deltaTime
        updatedCrop.focusTimeContributed += deltaTime
        updatedCrop.averageFocusScore = this.calculator.getAverageFocusScore(cropId)
        
        // 检查阶段变化
        if (growthResult.shouldAdvanceStage && growthResult.newStage) {
          const fromStage = crop.stage
          const toStage = growthResult.newStage
          
          updatedCrop.stage = toStage
          updatedCrop.stageStartTime = now
          
          // 如果进入可收获阶段
          if (toStage === CropStage.READY_TO_HARVEST) {
            updatedCrop.harvestable = true
            result.harvestReady.push(cropId)
            
            this.emitEvent({
              type: 'harvest_ready',
              timestamp: now,
              data: { cropId, crop: updatedCrop }
            })
          }
          
          result.stageChanges.push({ cropId, fromStage, toStage })
          
          this.emitEvent({
            type: 'stage_change',
            timestamp: now,
            data: { cropId, fromStage, toStage, crop: updatedCrop }
          })
        }
        
        // 检查专注度警告
        this.checkFocusWarnings(updatedCrop, result)
        
        this.crops.set(cropId, updatedCrop)
        result.updatedCrops.push(updatedCrop)
        
      } catch (error) {
        console.error(`更新作物 ${cropId} 时出错:`, error)
        result.warnings.push({
          cropId,
          message: `更新失败: ${error instanceof Error ? error.message : '未知错误'}`
        })
      }
    })
    
    // 发送批量更新事件
    if (result.updatedCrops.length > 0 || result.stageChanges.length > 0) {
      this.emitEvent({
        type: 'batch_update',
        timestamp: now,
        data: result
      })
    }
    
    return result
  }
  
  /**
   * 检查专注度警告
   */
  private checkFocusWarnings(crop: CropInstance, result: BatchUpdateResult): void {
    // 这里可以添加各种警告检查逻辑
    // 例如：专注度过低、生长停滞等
    
    if (this.globalFocusScore < 30) {
      result.warnings.push({
        cropId: crop.id,
        message: '专注度过低，作物生长缓慢'
      })
    }
    
    const stats = this.calculator.getGrowthStats(crop)
    if (stats.growthEfficiency < 0.5) {
      result.warnings.push({
        cropId: crop.id,
        message: '生长效率较低，建议提高专注度'
      })
    }
  }
  
  /**
   * 手动触发生长加速
   */
  boostGrowth(cropId: string, multiplier: number = 2.0, duration: number = 60000): void {
    const crop = this.crops.get(cropId)
    if (!crop) return
    
    const boostedCrop = this.calculator.applyGrowthBoost(crop, multiplier, duration)
    this.crops.set(cropId, boostedCrop)
    
    console.log(`作物 ${cropId} 获得 ${multiplier}x 生长加速，持续 ${duration/1000} 秒`)
  }
  
  /**
   * 获取农场统计信息
   */
  getFarmStats(): {
    totalCrops: number
    activeCrops: number
    harvestableCrops: number
    averageGrowthProgress: number
    totalFocusTime: number
    averageFocusScore: number
  } {
    const crops = Array.from(this.crops.values())
    const activeCrops = crops.filter(c => !c.isPaused && c.stage !== CropStage.HARVESTED)
    const harvestableCrops = crops.filter(c => c.harvestable)
    
    const totalProgress = crops.reduce((sum, crop) => {
      const progress = this.getProgressPercentage(crop.id)
      return sum + progress
    }, 0)
    
    const totalFocusTime = crops.reduce((sum, crop) => sum + crop.focusTimeContributed, 0)
    const totalFocusScore = crops.reduce((sum, crop) => sum + crop.averageFocusScore, 0)
    
    return {
      totalCrops: crops.length,
      activeCrops: activeCrops.length,
      harvestableCrops: harvestableCrops.length,
      averageGrowthProgress: crops.length > 0 ? totalProgress / crops.length : 0,
      totalFocusTime,
      averageFocusScore: crops.length > 0 ? totalFocusScore / crops.length : 0
    }
  }
  
  /**
   * 获取作物生长进度百分比
   */
  getProgressPercentage(cropId: string): number {
    const crop = this.crops.get(cropId)
    if (!crop) return 0
    
    return Math.round(this.calculator.getCropProgress(cropId) * 100)
  }
  
  /**
   * 获取估计收获时间
   */
  getEstimatedHarvestTime(cropId: string): string {
    const crop = this.crops.get(cropId)
    if (!crop) return '未知'
    
    if (crop.harvestable) return '可立即收获'
    
    const timeRemaining = this.calculator.estimateTimeToHarvest(crop, this.globalFocusScore)
    return formatTimeRemaining(timeRemaining)
  }
  
  /**
   * 获取生长速度描述
   */
  getGrowthSpeedDescription(cropId: string): string {
    const crop = this.crops.get(cropId)
    if (!crop) return '未知'
    
    const result = this.calculator.calculateProgress(crop, this.globalFocusScore, 1000)
    const multiplier = result.progressDelta * 1000 // 转换为每秒进度
    
    return getGrowthSpeedDescription(multiplier)
  }
  
  /**
   * 批量暂停/恢复作物
   */
  batchSetPaused(cropIds: string[], paused: boolean): void {
    cropIds.forEach(cropId => {
      const crop = this.crops.get(cropId)
      if (crop) {
        this.updateCrop(cropId, { isPaused: paused })
      }
    })
  }
  
  /**
   * 清理已收获的作物
   */
  clearHarvestedCrops(): void {
    const harvestedIds: string[] = []
    
    this.crops.forEach((crop, cropId) => {
      if (crop.stage === CropStage.HARVESTED) {
        harvestedIds.push(cropId)
      }
    })
    
    harvestedIds.forEach(id => this.removeCrop(id))
    
    console.log(`清理了 ${harvestedIds.length} 个已收获的作物`)
  }
  
  /**
   * 重置所有专注度历史
   */
  resetAllFocusHistory(): void {
    this.calculator.clearFocusHistory()
    console.log('已重置所有作物的专注度历史')
  }
  
  /**
   * 注册事件监听器
   */
  addEventListener(callback: (event: TimeManagerEvent) => void): void {
    this.eventCallbacks.push(callback)
  }
  
  /**
   * 移除事件监听器
   */
  removeEventListener(callback: (event: TimeManagerEvent) => void): void {
    const index = this.eventCallbacks.indexOf(callback)
    if (index > -1) {
      this.eventCallbacks.splice(index, 1)
    }
  }
  
  /**
   * 发送事件
   */
  private emitEvent(event: TimeManagerEvent): void {
    this.eventCallbacks.forEach(callback => {
      try {
        callback(event)
      } catch (error) {
        console.error('事件回调执行失败:', error)
      }
    })
  }
  
  /**
   * 获取调试信息
   */
  getDebugInfo(): {
    isRunning: boolean
    isPaused: boolean
    cropsCount: number
    lastUpdateTime: number
    globalFocusScore: number
    memoryUsage: {
      focusHistorySize: number
      eventCallbacksCount: number
    }
  } {
    return {
      isRunning: this.updateInterval !== null,
      isPaused: this.isPaused,
      cropsCount: this.crops.size,
      lastUpdateTime: this.lastUpdateTime,
      globalFocusScore: this.globalFocusScore,
      memoryUsage: {
        focusHistorySize: (this.calculator as any).focusHistory?.size || 0,
        eventCallbacksCount: this.eventCallbacks.length
      }
    }
  }
  
  /**
   * 销毁管理器
   */
  destroy(): void {
    this.stop()
    this.crops.clear()
    this.eventCallbacks.length = 0
    this.calculator.clearFocusHistory()
    console.log('作物时间管理器已销毁')
  }
}

// 扩展 GrowthCalculator 类以支持进度查询
declare module '../utils/cropGrowth' {
  interface GrowthCalculator {
    getCropProgress(cropId: string): number
  }
}

// 为 GrowthCalculator 添加进度查询方法
Object.defineProperty(GrowthCalculator.prototype, 'getCropProgress', {
  value: function(cropId: string): number {
    // 这里需要实际的作物实例来计算进度
    // 在实际使用中，这个方法会被时间管理器调用
    return 0
  },
  writable: true,
  configurable: true
})

// 全局时间管理器实例
export const cropTimeManager = new CropTimeManager() 