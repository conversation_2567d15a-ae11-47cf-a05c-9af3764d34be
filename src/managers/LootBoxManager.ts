import { EventEmitter } from 'eventemitter3'
import { LootboxGenerator } from '../utils/lootboxGenerator'
import { LootboxType, LootboxResult, LootboxConfig } from '../types/lootbox'
import { LOOTBOX_CONFIGS } from '../data/lootboxConfigs'
import { GameItem } from '../types/enhanced-items'
import { CurrencyType } from '../types/currency'

// 简化的LootBox接口，用于兼容性
interface LootBox {
  id: string
  name: string
  description: string
  price: number
  currency: string
  rarity: string
  animation: string
  openSound: string
  rarityGlow: string
}

export class LootBoxManager extends EventEmitter {
  private availableLootBoxes: LootBox[]
  private userCurrency: Record<CurrencyType, number> = {
    [CurrencyType.FOCUS_COIN]: 1000,
    [CurrencyType.DISCIPLINE_TOKEN]: 10,
    [CurrencyType.FUTURES_CRYSTAL]: 5,
    [CurrencyType.GOLDEN_HARVEST]: 2
  }

  constructor() {
    super()
    this.availableLootBoxes = this.initializeLootBoxes()
  }

  // 初始化盲盒配置（转换为兼容格式）
  private initializeLootBoxes(): LootBox[] {
    return Object.values(LOOTBOX_CONFIGS).map(config => ({
      id: config.id,
      name: config.name,
      description: config.description,
      price: config.price.amount,
      currency: config.price.currency,
      rarity: config.guaranteedRarity || 'gray',
      animation: 'box_open_' + config.id,
      openSound: 'box_open_sound',
      rarityGlow: this.getRarityGlow(config.guaranteedRarity || 'gray')
    }))
  }

  // 获取品质对应的光晕颜色
  private getRarityGlow(rarity: string): string {
    const glowMap: Record<string, string> = {
      'gray': '#9E9E9E',
      'green': '#4CAF50',
      'blue': '#2196F3',
      'orange': '#FF9800',
      'gold': '#FFD700',
      'gold_red': '#FF6B6B'
    }
    return glowMap[rarity] || '#9E9E9E'
  }

  // 开启盲盒
  async openLootBox(lootBoxId: string): Promise<{
    success: boolean
    result?: LootboxResult
    items?: GameItem[]
    error?: string
  }> {
    try {
      const lootboxType = lootBoxId as LootboxType
      const config = LOOTBOX_CONFIGS[lootboxType]
    
      if (!config) {
      throw new Error('盲盒不存在')
    }

      // 检查货币是否足够
      const requiredAmount = config.price.amount
      const userAmount = this.userCurrency[config.price.currency] || 0
      
      if (userAmount < requiredAmount) {
        throw new Error(`货币不足！需要 ${requiredAmount} ${config.price.currency}，但只有 ${userAmount}`)
      }

      // 生成盲盒结果
      const result = LootboxGenerator.generateLootbox(lootboxType, this.userCurrency)
      
      // 扣除货币
      this.userCurrency[config.price.currency] -= requiredAmount

      // 提取实际的游戏道具
      const gameItems: GameItem[] = []
      
      // 由于盲盒结果包含的是转换后的格式，我们需要重新生成实际的GameItem
      // 这里简化处理，实际项目中可能需要更复杂的逻辑
      result.items.forEach(resultItem => {
        // 这里需要根据resultItem的信息重新创建GameItem
        // 由于数据已经转换过，我们创建一个简化的GameItem结构
        const gameItem: any = {
          id: resultItem.item.id,
          name: resultItem.item.name,
          description: resultItem.item.description,
          category: resultItem.item.category,
          quality: this.convertRarityToQuality(resultItem.item.rarity),
          icon: resultItem.item.icon,
          baseValue: resultItem.item.value,
          stackable: resultItem.item.stackable,
          tradeable: resultItem.item.tradeable,
          obtainedAt: Date.now()
    }

                 // 添加特定类别的属性
         if (resultItem.item.metadata) {
           if (resultItem.item.category.toString() === 'agricultural') {
             gameItem.production = {
               minDaily: 100,
               maxDaily: 120,
               currentRate: resultItem.item.metadata.yieldMultiplier || 1.0
             }
             gameItem.futuresPrice = resultItem.item.metadata.futuresPrice
           } else if (resultItem.item.category.toString() === 'industrial') {
             gameItem.properties = {
               durability: 100,
               efficiency: resultItem.item.metadata.efficiency || 100,
               capacity: resultItem.item.metadata.capacity || 100
             }
             gameItem.futuresPrice = resultItem.item.metadata.futuresPrice
           } else if (resultItem.item.category.toString() === 'equipment') {
             gameItem.attributes = {
               focusBonus: 0,
               productionBonus: 0,
               qualityBonus: 0,
               duration: resultItem.item.metadata.duration || 24
             }
           }
         }
        
        gameItems.push(gameItem)
      })

      // 发送事件
    this.emit('lootBoxOpened', {
      lootBoxId,
      result,
        items: gameItems
      })

    return {
        success: true,
        result,
        items: gameItems
      }
    } catch (error) {
        return {
        success: false,
        error: error instanceof Error ? error.message : '开启盲盒失败'
      }
    }
  }

  // 转换品稀有度为品质
  private convertRarityToQuality(rarity: string): string {
    const rarityMap: Record<string, string> = {
      'gray': 'common',
      'green': 'good',
      'blue': 'rare',
      'orange': 'epic',
      'gold': 'legendary',
      'gold_red': 'legendary'
    }
    return rarityMap[rarity] || 'common'
  }

  // 获取可用盲盒列表
  getAvailableLootBoxes(): LootBox[] {
    return this.availableLootBoxes
  }

  // 获取盲盒配置
  getLootBoxConfig(lootBoxId: string): LootboxConfig | null {
    return LOOTBOX_CONFIGS[lootBoxId as LootboxType] || null
  }

  // 获取盲盒预览
  getLootBoxPreview(lootBoxId: string): { items: string[]; probabilities: number[] } | null {
    const preview = LootboxGenerator.getLootboxPreview(lootBoxId as LootboxType)
    if (!preview) return null

    return {
      items: preview.possibleItems,
      probabilities: Object.values(preview.config.dropRates)
    }
  }

  // 获取用户货币
  getUserCurrency(): Record<CurrencyType, number> {
    return { ...this.userCurrency }
  }

  // 添加货币
  addCurrency(currency: CurrencyType, amount: number): void {
    this.userCurrency[currency] = (this.userCurrency[currency] || 0) + amount
    this.emit('currencyChanged', { currency, amount: this.userCurrency[currency] })
  }

  // 设置货币
  setCurrency(currency: CurrencyType, amount: number): void {
    this.userCurrency[currency] = amount
    this.emit('currencyChanged', { currency, amount })
  }
} 