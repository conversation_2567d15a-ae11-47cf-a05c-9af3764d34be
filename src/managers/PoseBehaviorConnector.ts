import { PostureAnalysis, EnhancedPostureAnalysis } from '../types/pose'
import { GameStateManager, FocusState } from './GameStateManager'
import { FocusAwareCropManager } from './FocusAwareCropManager'

// 行为事件类型
export interface BehaviorEvent {
  type: 'focus_change' | 'focus_start' | 'focus_break' | 'posture_warning'
  timestamp: number
  data: {
    focusScore: number
    isFocused: boolean
    previousScore?: number
    duration?: number
    warning?: string
  }
}

// 连接状态
export interface ConnectionStatus {
  isConnected: boolean
  isReceivingData: boolean
  lastDataTime: number
  latency: number
  errorCount: number
}

/**
 * 行为连接器管理器
 * 负责连接姿态检测和游戏逻辑的桥梁，采用单例模式
 */
export class PoseBehaviorConnector {
  private static instance: PoseBehaviorConnector | null = null
  
  // 游戏状态管理器引用
  private gameStateManager: GameStateManager | null = null
  
  // 专注度感知作物管理器引用
  private focusAwareCropManager: FocusAwareCropManager | null = null
  
  // 当前专注状态
  private currentFocusState: FocusState = {
    focusScore: 0,
    isFocused: false,
    sessionDuration: 0,
    consecutiveFocusTime: 0,
    lastUpdateTime: Date.now()
  }
  
  // 连接状态
  private connectionStatus: ConnectionStatus = {
    isConnected: false,
    isReceivingData: false,
    lastDataTime: 0,
    latency: 0,
    errorCount: 0
  }
  
  // 事件回调
  private eventCallbacks: ((event: BehaviorEvent) => void)[] = []
  
  // 配置参数
  private config = {
    focusThreshold: 70, // 专注度阈值
    focusBreakThreshold: 50, // 专注中断阈值
    updateInterval: 500, // 更新间隔(ms)
    latencyWarningThreshold: 500, // 延迟警告阈值(ms)
    maxErrorCount: 10 // 最大错误次数
  }
  
  // 内部状态
  private isActive: boolean = false
  private updateTimer: number | null = null
  private latencyTestStartTime: number = 0
  
  private constructor() {
    // 私有构造函数，确保单例模式
  }
  
  /**
   * 获取单例实例
   */
  public static getInstance(): PoseBehaviorConnector {
    if (!PoseBehaviorConnector.instance) {
      PoseBehaviorConnector.instance = new PoseBehaviorConnector()
    }
    return PoseBehaviorConnector.instance
  }
  
  /**
   * 连接游戏状态管理器
   */
  public connectGameStateManager(gameStateManager: GameStateManager): void {
    if (this.gameStateManager) {
      console.warn('GameStateManager already connected')
      return
    }
    
    this.gameStateManager = gameStateManager
    this.connectionStatus.isConnected = true
    
    console.log('✅ PoseBehaviorConnector connected to GameStateManager')
  }

  /**
   * 连接专注度感知作物管理器
   */
  public connectFocusAwareCropManager(focusAwareCropManager: FocusAwareCropManager): void {
    if (this.focusAwareCropManager) {
      console.warn('FocusAwareCropManager already connected')
      return
    }
    
    this.focusAwareCropManager = focusAwareCropManager
    
    console.log('✅ PoseBehaviorConnector connected to FocusAwareCropManager')
  }
  
  /**
   * 断开连接
   */
  public disconnect(): void {
    this.stop()
    this.gameStateManager = null
    this.focusAwareCropManager = null
    this.connectionStatus.isConnected = false
    this.eventCallbacks = []
    
    console.log('❌ PoseBehaviorConnector disconnected')
  }
  
  /**
   * 启动行为检测连接
   */
  public start(): void {
    if (this.isActive) {
      console.warn('PoseBehaviorConnector is already active')
      return
    }
    
    if (!this.gameStateManager) {
      throw new Error('GameStateManager must be connected before starting')
    }
    
    this.isActive = true
    this.connectionStatus.errorCount = 0
    
    // 启动定期更新检查
    this.startUpdateTimer()
    
    console.log('🚀 PoseBehaviorConnector started')
  }
  
  /**
   * 停止行为检测连接
   */
  public stop(): void {
    this.isActive = false
    this.connectionStatus.isReceivingData = false
    
    if (this.updateTimer) {
      clearInterval(this.updateTimer)
      this.updateTimer = null
    }
    
    console.log('⏹️ PoseBehaviorConnector stopped')
  }
  
  /**
   * 接收姿态分析数据
   */
  public receivePoseData(analysis: PostureAnalysis | EnhancedPostureAnalysis): void {
    if (!this.isActive || !this.gameStateManager) {
      return
    }
    
    try {
      // 计算延迟
      if (this.latencyTestStartTime > 0) {
        const latency = Date.now() - this.latencyTestStartTime
        this.connectionStatus.latency = latency
        this.latencyTestStartTime = 0
        
        if (latency > this.config.latencyWarningThreshold) {
          console.warn(`⚠️ High latency detected: ${latency}ms`)
        }
      }
      
      const now = Date.now()
      const previousState = { ...this.currentFocusState }
      
      // 更新专注状态
      this.updateFocusState(analysis, now)
      
      // 更新连接状态
      this.connectionStatus.isReceivingData = true
      this.connectionStatus.lastDataTime = now
      this.connectionStatus.errorCount = 0
      
      // 检测状态变化并触发事件
      this.checkAndEmitEvents(previousState, now)

            // 如果使用增强版分析，可以获得更多信息
      if ('confidence' in analysis) {
        // 处理增强版分析的额外数据
        this.handleEnhancedAnalysis(analysis as EnhancedPostureAnalysis)
      }

      // 同步到游戏状态管理器
      this.syncToGameState()
      
    } catch (error) {
      this.handleError(error)
    }
  }
  
  /**
   * 更新专注状态
   */
  private updateFocusState(analysis: PostureAnalysis, timestamp: number): void {
    const deltaTime = timestamp - this.currentFocusState.lastUpdateTime
    
    this.currentFocusState.focusScore = analysis.focusScore
    this.currentFocusState.sessionDuration += deltaTime
    this.currentFocusState.lastUpdateTime = timestamp
    
    // 判断是否专注
    const wasFocused = this.currentFocusState.isFocused
    const isFocused = analysis.isFocused && analysis.focusScore >= this.config.focusThreshold
    
    this.currentFocusState.isFocused = isFocused
    
    // 更新连续专注时间
    if (isFocused) {
      this.currentFocusState.consecutiveFocusTime += deltaTime
    } else {
      this.currentFocusState.consecutiveFocusTime = 0
    }
  }
  
  /**
   * 检查状态变化并触发事件
   */
  private checkAndEmitEvents(previousState: FocusState, timestamp: number): void {
    const current = this.currentFocusState
    
    // 专注状态变化事件
    if (previousState.isFocused !== current.isFocused) {
      const eventType = current.isFocused ? 'focus_start' : 'focus_break'
      
      this.emitEvent({
        type: eventType,
        timestamp,
        data: {
          focusScore: current.focusScore,
          isFocused: current.isFocused,
          previousScore: previousState.focusScore,
          duration: current.isFocused ? 0 : previousState.consecutiveFocusTime
        }
      })
    }
    
    // 专注度变化事件（每次都触发，用于实时更新）
    this.emitEvent({
      type: 'focus_change',
      timestamp,
      data: {
        focusScore: current.focusScore,
        isFocused: current.isFocused,
        previousScore: previousState.focusScore
      }
    })
  }
  
  /**
   * 同步到游戏状态管理器
   */
    private syncToGameState(): void {
    if (!this.gameStateManager) return

    // 同步到游戏状态管理器
    if (typeof this.gameStateManager.updateFocusState === 'function') {
      this.gameStateManager.updateFocusState(this.currentFocusState)
    }
    
    // 同步到专注度感知作物管理器
    if (this.focusAwareCropManager && typeof this.focusAwareCropManager.updateFocusState === 'function') {
      this.focusAwareCropManager.updateFocusState(this.currentFocusState)
    }
  }
  
  /**
   * 启动更新定时器
   */
  private startUpdateTimer(): void {
    if (this.updateTimer) {
      clearInterval(this.updateTimer)
    }
    
    this.updateTimer = window.setInterval(() => {
      this.checkConnection()
    }, this.config.updateInterval)
  }
  
  /**
   * 检查连接状态
   */
  private checkConnection(): void {
    const now = Date.now()
    const timeSinceLastData = now - this.connectionStatus.lastDataTime
    
    // 检查数据流是否正常
    if (timeSinceLastData > this.config.updateInterval * 3) {
      this.connectionStatus.isReceivingData = false
      console.warn('⚠️ No pose data received for', timeSinceLastData, 'ms')
    }
    
    // 测试延迟
    if (this.connectionStatus.isReceivingData && this.latencyTestStartTime === 0) {
      this.latencyTestStartTime = now
    }
  }
  
  /**
   * 处理错误
   */
  private handleError(error: any): void {
    this.connectionStatus.errorCount++
    console.error('PoseBehaviorConnector error:', error)
    
    if (this.connectionStatus.errorCount >= this.config.maxErrorCount) {
      console.error('❌ Too many errors, stopping connector')
      this.stop()
    }
  }
  
  /**
   * 触发事件
   */
  private emitEvent(event: BehaviorEvent): void {
    this.eventCallbacks.forEach(callback => {
      try {
        callback(event)
      } catch (error) {
        console.error('Error in event callback:', error)
      }
    })
  }
  
  /**
   * 添加事件监听器
   */
  public addEventListener(callback: (event: BehaviorEvent) => void): void {
    this.eventCallbacks.push(callback)
  }
  
  /**
   * 移除事件监听器
   */
  public removeEventListener(callback: (event: BehaviorEvent) => void): void {
    const index = this.eventCallbacks.indexOf(callback)
    if (index > -1) {
      this.eventCallbacks.splice(index, 1)
    }
  }
  
  /**
   * 获取当前专注状态
   */
  public getCurrentFocusState(): FocusState {
    return { ...this.currentFocusState }
  }
  
  /**
   * 获取连接状态
   */
  public getConnectionStatus(): ConnectionStatus {
    return { ...this.connectionStatus }
  }
  
  /**
   * 更新配置
   */
  public updateConfig(newConfig: Partial<typeof this.config>): void {
    this.config = { ...this.config, ...newConfig }
    console.log('📝 PoseBehaviorConnector config updated:', newConfig)
  }
  
  /**
   * 获取状态摘要
   */
  public getStatusSummary(): {
    isActive: boolean
    isConnected: boolean
    isReceivingData: boolean
    focusScore: number
    isFocused: boolean
    latency: number
    errorCount: number
  } {
    return {
      isActive: this.isActive,
      isConnected: this.connectionStatus.isConnected,
      isReceivingData: this.connectionStatus.isReceivingData,
      focusScore: this.currentFocusState.focusScore,
      isFocused: this.currentFocusState.isFocused,
      latency: this.connectionStatus.latency,
      errorCount: this.connectionStatus.errorCount
    }
  }
  
  /**
   * 重置连接器
   */
  public reset(): void {
    this.stop()
    
    this.currentFocusState = {
      focusScore: 0,
      isFocused: false,
      sessionDuration: 0,
      consecutiveFocusTime: 0,
      lastUpdateTime: Date.now()
    }
    
    this.connectionStatus.errorCount = 0
    this.connectionStatus.latency = 0
    
    console.log('🔄 PoseBehaviorConnector reset')
  }

  /**
   * 处理增强版分析数据
   */
  private handleEnhancedAnalysis(analysis: EnhancedPostureAnalysis): void {
    // 处理置信度信息
    if (analysis.confidence < 0.5) {
      console.warn('⚠️ Low confidence in pose analysis:', analysis.confidence)
    }

    // 处理稳定性信息
    if (analysis.stability < 0.3) {
      console.info('📊 Pose analysis stability is low, data may be noisy')
    }

    // 处理趋势信息
    if (analysis.trend === 'declining' && analysis.focusScore > 50) {
      this.emitEvent({
        type: 'posture_warning',
        timestamp: Date.now(),
        data: {
          focusScore: analysis.focusScore,
          isFocused: analysis.isFocused,
          warning: '专注度呈下降趋势，请调整坐姿'
        }
      })
    }

    // 处理建议信息
    if (analysis.recommendations && analysis.recommendations.length > 0) {
      // 可以将建议发送给UI显示
      console.info('💡 Posture recommendations:', analysis.recommendations)
    }

    // 处理环境因素
    if (analysis.environmentalFactors.lightLevel && analysis.environmentalFactors.lightLevel < 0.5) {
      console.warn('🔅 Low light detected, pose detection may be affected')
    }
  }
}

// 导出单例实例
export const poseBehaviorConnector = PoseBehaviorConnector.getInstance() 