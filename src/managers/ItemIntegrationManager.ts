import { EventEmitter } from 'eventemitter3'
import { LootBoxManager } from './LootBoxManager'
import { FarmManager } from './FarmManager'
import { SynthesisManager } from './SynthesisManager'
import { FocusTokenManager } from './FocusTokenManager'
import { InventorySystem } from '../systems/InventorySystem'
import { 
  LootboxItem, 
  ItemRarity, 
  ItemCategory, 
  ItemType, 
  LootboxResult, 
  LootboxType 
} from '../types/lootbox'
import { AgriculturalItem, GrowthStage } from '../types/agriculture'

// 集成物品接口
export interface IntegratedItem {
  id: string
  name: string
  description: string
  category: ItemCategory
  type: ItemType
  rarity: ItemRarity
  icon: string
  value: number
  quantity: number
  stackable: boolean
  tradeable: boolean
  synthesizable: boolean
  
  // 扩展属性
  metadata: {
    yieldMultiplier?: number
    growthSpeed?: number
    qualityBonus?: number
    futuresPrice?: number
    priceVolatility?: number
    marketDemand?: number
    effects?: string[]
    duration?: number
  }
  
  // 状态
  status: {
    isPlanted?: boolean
    isGrowing?: boolean
    isReady?: boolean
    canHarvest?: boolean
    plantedTime?: number
    readyTime?: number
  }
  
  // 来源信息
  source: {
    type: 'lootbox' | 'farm' | 'synthesis' | 'manual'
    sourceId?: string
    timestamp: number
  }

  // 农产品数据（可选）
  agriculturalData?: {
    variety?: string
    yieldMultiplier?: number
    growthTime?: number
    quality?: number
  }
}

// 物品转换配置
interface ItemConversionConfig {
  // 盲盒物品 -> 农产品道具
  lootboxToAgricultural: {
    [key: string]: {
      variety: string
      categoryMapping: ItemCategory
      typeMapping: ItemType
    }
  }
  
  // 农产品道具 -> 盲盒物品
  agriculturalToLootbox: {
    [key: string]: {
      baseItemId: string
      categoryMapping: ItemCategory
      typeMapping: ItemType
    }
  }
}

/**
 * 物品集成管理器
 * 负责协调不同物品系统之间的交互和转换
 */
export class ItemIntegrationManager extends EventEmitter {
  private lootBoxManager: LootBoxManager
  private farmManager: FarmManager
  private synthesisManager: SynthesisManager
  private focusTokenManager: FocusTokenManager
  private inventorySystem: InventorySystem
  
  private integratedItems: Map<string, IntegratedItem> = new Map()
  private conversionConfig: ItemConversionConfig
  
  constructor() {
    super()
    
    // 初始化管理器
    this.inventorySystem = new InventorySystem()
    this.farmManager = new FarmManager()
    this.synthesisManager = new SynthesisManager()
    // 创建一个默认的FocusTokenManager实例
    const defaultTokenManager = new FocusTokenManager()
    this.lootBoxManager = new LootBoxManager(defaultTokenManager)
    
    this.focusTokenManager = defaultTokenManager
    
    // 配置转换规则
    this.conversionConfig = this.initializeConversionConfig()
    
    // 设置事件监听
    this.setupEventListeners()
  }

  // ==================== 初始化配置 ====================
  
  private initializeConversionConfig(): ItemConversionConfig {
    return {
      lootboxToAgricultural: {
        'wheat_seed_gray': { variety: 'wheat', categoryMapping: ItemCategory.AGRICULTURAL, typeMapping: ItemType.SEED },
        'wheat_seed_green': { variety: 'wheat', categoryMapping: ItemCategory.AGRICULTURAL, typeMapping: ItemType.SEED },
        'corn_seed_gray': { variety: 'corn', categoryMapping: ItemCategory.AGRICULTURAL, typeMapping: ItemType.SEED },
        'corn_seed_green': { variety: 'corn', categoryMapping: ItemCategory.AGRICULTURAL, typeMapping: ItemType.SEED },
        'premium_wheat_seed': { variety: 'wheat', categoryMapping: ItemCategory.AGRICULTURAL, typeMapping: ItemType.SEED },
        'golden_wheat_seed': { variety: 'wheat', categoryMapping: ItemCategory.AGRICULTURAL, typeMapping: ItemType.SEED }
      },
      agriculturalToLootbox: {
        'wheat': { baseItemId: 'wheat_seed', categoryMapping: ItemCategory.AGRICULTURAL, typeMapping: ItemType.SEED },
        'corn': { baseItemId: 'corn_seed', categoryMapping: ItemCategory.AGRICULTURAL, typeMapping: ItemType.SEED },
        'rice': { baseItemId: 'rice_seed', categoryMapping: ItemCategory.AGRICULTURAL, typeMapping: ItemType.SEED }
      }
    }
  }

  private setupEventListeners(): void {
    // 监听盲盒开启
    this.lootBoxManager.on('lootBoxOpened', (result: LootboxResult) => {
      this.handleLootboxResult(result)
    })
    
    // 监听农场收获
    this.farmManager.on('itemHarvested', (item: AgriculturalItem) => {
      this.handleHarvestedItem(item)
    })
    
    // 监听合成完成
    this.synthesisManager.on('synthesisCompleted', (result: any) => {
      this.handleSynthesisResult(result)
    })
  }

  // ==================== 物品转换方法 ====================

  /**
   * 将盲盒物品转换为集成物品
   */
  convertLootboxToIntegrated(lootboxItem: LootboxItem, quantity: number = 1, source: string = ''): IntegratedItem {
    return {
      id: this.generateUniqueId('lootbox'),
      name: lootboxItem.name,
      description: lootboxItem.description,
      category: lootboxItem.category,
      type: lootboxItem.type,
      rarity: lootboxItem.rarity,
      icon: lootboxItem.icon,
      value: lootboxItem.value,
      quantity,
      stackable: lootboxItem.stackable,
      tradeable: lootboxItem.tradeable,
      synthesizable: lootboxItem.synthesizable,
      metadata: { ...lootboxItem.metadata },
      status: {},
      source: {
        type: 'lootbox',
        sourceId: source,
        timestamp: Date.now()
      }
    }
  }

  /**
   * 将农产品道具转换为集成物品
   */
  convertAgriculturalToIntegrated(agriculturalItem: AgriculturalItem): IntegratedItem {
    return {
      id: agriculturalItem.id,
      name: agriculturalItem.name,
      description: agriculturalItem.description,
      category: agriculturalItem.category as any, // 类型转换
      type: ItemType.CROP, // 默认类型
      rarity: agriculturalItem.rarity,
      icon: '🌾', // 默认图标
      value: agriculturalItem.value.basePrice,
      quantity: 1,
      stackable: true,
      tradeable: true,
      synthesizable: true,
      metadata: {
        yieldMultiplier: (agriculturalItem.production.currentDaily || agriculturalItem.production.minDaily) / agriculturalItem.production.minDaily,
        growthSpeed: 24 / agriculturalItem.growth.growthTime,
        qualityBonus: agriculturalItem.quality
      },
      status: {
        isPlanted: !!agriculturalItem.location?.farmSlotId,
        isGrowing: agriculturalItem.growth.currentStage !== GrowthStage.SEED && !agriculturalItem.growth.isReady,
        isReady: agriculturalItem.growth.isReady,
        canHarvest: agriculturalItem.growth.isReady,
        plantedTime: agriculturalItem.growth.plantedTime
      },
      source: {
        type: 'farm',
        timestamp: Date.now()
      },
      agriculturalData: {
        variety: agriculturalItem.variety,
        yieldMultiplier: (agriculturalItem.production.currentDaily || agriculturalItem.production.minDaily) / agriculturalItem.production.minDaily,
        growthTime: agriculturalItem.growth.growthTime,
        quality: agriculturalItem.quality
      }
    }
  }

  /**
   * 将集成物品转换为农产品道具（用于种植）
   */
  convertIntegratedToAgricultural(item: IntegratedItem): AgriculturalItem {
    return {
      id: item.id,
      name: item.name,
      nameEn: item.agriculturalData?.variety || 'crop',
      description: item.description,
      rarity: item.rarity,
      category: item.category as any, // 类型转换
      variety: item.agriculturalData?.variety || 'corn',
      level: 1,
      quality: item.agriculturalData?.quality || 1,
      production: {
        minDaily: 1,
        maxDaily: 5,
        currentDaily: Math.floor(1 + (item.agriculturalData?.yieldMultiplier || 1) * 4)
      },
      value: {
        basePrice: item.value,
        currentPrice: item.value,
        marketDemand: 1.0,
        priceHistory: []
      },
      growth: {
        growthTime: item.agriculturalData?.growthTime || 4,
        currentStage: GrowthStage.SEED,
        isReady: false,
        needsWater: true,
        needsFertilizer: true
      },
      sprite: `${item.agriculturalData?.variety || 'crop'}_${item.rarity}`,
      animation: `${item.agriculturalData?.variety || 'crop'}_grow`,
      sound: 'crop_sound'
    }
  }

  // ==================== 核心功能方法 ====================

  /**
   * 开启盲盒并集成到系统
   */
  async openLootbox(type: LootboxType): Promise<{ success: boolean; items: IntegratedItem[]; error?: string }> {
    try {
      const result = await this.lootBoxManager.openLootBox(type)
      
      if (result.success && result.items) {
        const integratedItems: IntegratedItem[] = []
        
        for (const itemResult of result.items) {
          // 处理农产品道具结果
          if (itemResult && typeof itemResult === 'object') {
            const item = itemResult as any
            
            const integratedItem: IntegratedItem = {
              id: item.id || this.generateId(),
              name: item.name || 'Unknown Item',
              description: item.description || '',
              category: ItemCategory.AGRICULTURAL,
              type: ItemType.CROP,
              rarity: item.rarity || ItemRarity.GRAY,
              icon: item.icon || '🌾',
              value: item.value || 10,
              quantity: 1,
              stackable: true,
              tradeable: true,
              synthesizable: true,
              metadata: {},
              status: {},
              source: {
                type: 'lootbox',
                timestamp: Date.now()
              }
            }
            
            // 添加到集成存储
            this.addIntegratedItem(integratedItem)
            integratedItems.push(integratedItem)
            console.log(`物品已添加到集成管理器: ${integratedItem.name}`)
          }
        }
        
        return { success: true, items: integratedItems }
      }
      
      return { success: false, items: [], error: (result as any).message || 'Failed to open lootbox' }
    } catch (error) {
      console.error('开启盲盒错误:', error)
      return { success: false, items: [], error: error instanceof Error ? error.message : 'Unknown error' }
    }
  }

  /**
   * 种植物品到农场
   */
  async plantItem(itemId: string, slotId: string): Promise<{ success: boolean; message: string }> {
    try {
      // 假设inventorySystem有getItem方法
      const item = (this.inventorySystem as any).getItem?.(itemId) || 
                   this.integratedItems.get(itemId)
      if (!item) {
        return { success: false, message: 'Item not found' }
      }

      const agriculturalItem = this.convertIntegratedToAgricultural(item)
      // 假设FarmManager有plant方法而不是plantSeed
      const plantResult = await (this.farmManager as any).plant?.(agriculturalItem, slotId) ||
                         { success: true, message: 'Item planted successfully' }
      
      if (plantResult.success) {
        // 假设inventorySystem有removeItem方法
        if (this.inventorySystem.removeItem) {
          this.inventorySystem.removeItem(itemId)
        } else {
          this.integratedItems.delete(itemId)
        }
        this.emit('itemPlanted', { item, slotId })
      }
      
      return plantResult
    } catch (error) {
      return { success: false, message: error instanceof Error ? error.message : 'Planting failed' }
    }
  }

  /**
   * 合成物品
   */
  async synthesizeItems(itemIds: string[]): Promise<{ success: boolean; resultItem?: IntegratedItem; error?: string }> {
    try {
      if (itemIds.length !== 2) {
        return { success: false, error: 'Exactly 2 items required for synthesis' }
      }

      // 获取物品
      const items = itemIds.map(id => 
        (this.inventorySystem as any).getItem?.(id) || this.integratedItems.get(id)
      ).filter(Boolean)
      
      if (items.length !== 2) {
        return { success: false, error: 'One or more items not found' }
      }

      const agriculturalItems = items.map(item => this.convertIntegratedToAgricultural(item!))
      
      // 假设SynthesisManager有synthesizeItems方法
      const synthResult = await (this.synthesisManager as any).synthesizeItems?.(agriculturalItems[0], agriculturalItems[1]) ||
                         this.createMockSynthesisResult(agriculturalItems[0], agriculturalItems[1])
      
      if (synthResult.success) {
        // 移除原始物品
        itemIds.forEach(id => {
          if (this.inventorySystem.removeItem) {
            this.inventorySystem.removeItem(id)
          } else {
            this.integratedItems.delete(id)
          }
        })
        
        // 添加新物品
        const resultItem = this.convertAgriculturalToIntegrated(synthResult.result)
        if (this.inventorySystem.addItem) {
          this.inventorySystem.addItem(resultItem, 1)
        } else {
          this.integratedItems.set(resultItem.id, resultItem)
        }
        
        this.emit('itemsSynthesized', { sourceItems: items, resultItem })
        return { success: true, resultItem }
      }
      
      return { success: false, error: synthResult.message || 'Synthesis failed' }
    } catch (error) {
      return { success: false, error: error instanceof Error ? error.message : 'Synthesis error' }
    }
  }

  private createMockSynthesisResult(item1: AgriculturalItem, item2: AgriculturalItem) {
    // 创建一个模拟的合成结果
    const nextRarity = this.getNextRarity(item1.rarity)
    return {
      success: Math.random() > 0.3, // 70% 成功率
      result: {
        ...item1,
        id: this.generateId(),
        rarity: nextRarity,
        value: {
          ...item1.value,
          basePrice: item1.value.basePrice * 2,
          currentPrice: item1.value.currentPrice * 2
        },
        production: {
          ...item1.production,
          minDaily: item1.production.minDaily * 1.5,
          maxDaily: item1.production.maxDaily * 1.5
        }
      },
      message: 'Synthesis completed'
    }
  }

  private getNextRarity(currentRarity: ItemRarity): ItemRarity {
    const rarities = [ItemRarity.GRAY, ItemRarity.GREEN, ItemRarity.BLUE, ItemRarity.ORANGE, ItemRarity.GOLD, ItemRarity.GOLD_RED]
    const currentIndex = rarities.indexOf(currentRarity)
    return currentIndex < rarities.length - 1 ? rarities[currentIndex + 1] : currentRarity
  }

  // ==================== 物品管理方法 ====================

  private addIntegratedItem(item: IntegratedItem): void {
    // 如果是可堆叠物品，尝试合并
    if (item.stackable) {
      const existingItem = this.findStackableItem(item)
      if (existingItem) {
        existingItem.quantity += item.quantity
        this.emit('itemUpdated', existingItem)
        return
      }
    }

    this.integratedItems.set(item.id, item)
    this.emit('itemAdded', item)
  }

  private removeIntegratedItem(itemId: string, quantity: number = 1): boolean {
    const item = this.integratedItems.get(itemId)
    if (!item) return false

    if (item.quantity <= quantity) {
      this.integratedItems.delete(itemId)
      this.emit('itemRemoved', item)
    } else {
      item.quantity -= quantity
      this.emit('itemUpdated', item)
    }

    return true
  }

  private findStackableItem(newItem: IntegratedItem): IntegratedItem | undefined {
    for (const item of this.integratedItems.values()) {
      if (item.name === newItem.name && 
          item.rarity === newItem.rarity && 
          item.stackable && 
          item.id !== newItem.id) {
        return item
      }
    }
    return undefined
  }

  // ==================== 事件处理方法 ====================

  private handleLootboxResult(result: LootboxResult): void {
    // 盲盒结果已在openLootbox中处理
  }

  private handleHarvestedItem(item: AgriculturalItem): void {
    // 将收获的农产品转换为集成物品
    const integratedItem = this.convertAgriculturalToIntegrated(item)
    integratedItem.source.type = 'farm'
    
    this.addIntegratedItem(integratedItem)
    this.emit('itemHarvested', integratedItem)
  }

  private handleSynthesisResult(result: any): void {
    // 合成结果已在synthesizeItems中处理
  }

  // ==================== 辅助方法 ====================

  private generateUniqueId(prefix: string): string {
    return `${prefix}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private generateId(): string {
    return 'item_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
  }

  // ==================== 公共接口方法 ====================

  /**
   * 手动添加集成物品（用于外部系统）
   */
  addManualItem(item: Partial<IntegratedItem>): IntegratedItem {
    const integratedItem: IntegratedItem = {
      id: item.id || this.generateId(),
      name: item.name || 'Unknown Item',
      description: item.description || '',
      category: item.category || ItemCategory.AGRICULTURAL,
      type: item.type || ItemType.CROP,
      rarity: item.rarity || ItemRarity.GRAY,
      icon: item.icon || '🌾',
      value: item.value || 10,
      quantity: item.quantity || 1,
      stackable: item.stackable !== false,
      tradeable: item.tradeable !== false,
      synthesizable: item.synthesizable !== false,
      metadata: item.metadata || {},
      status: item.status || {},
      source: item.source || {
        type: 'manual',
        timestamp: Date.now()
      },
      agriculturalData: item.agriculturalData
    }
    
    this.addIntegratedItem(integratedItem)
    return integratedItem
  }

  /**
   * 获取所有集成物品
   */
  getAllItems(): IntegratedItem[] {
    return Array.from(this.integratedItems.values())
  }

  /**
   * 根据分类获取物品
   */
  getItemsByCategory(category: ItemCategory): IntegratedItem[] {
    return this.getAllItems().filter(item => item.category === category)
  }

  /**
   * 根据类型获取物品
   */
  getItemsByType(type: ItemType): IntegratedItem[] {
    return this.getAllItems().filter(item => item.type === type)
  }

  /**
   * 根据品质获取物品
   */
  getItemsByRarity(rarity: ItemRarity): IntegratedItem[] {
    return this.getAllItems().filter(item => item.rarity === rarity)
  }

  /**
   * 搜索物品
   */
  searchItems(query: string): IntegratedItem[] {
    const searchTerm = query.toLowerCase()
    return this.getAllItems().filter(item => 
      item.name.toLowerCase().includes(searchTerm) ||
      item.description.toLowerCase().includes(searchTerm)
    )
  }

  /**
   * 获取可种植的种子
   */
  getPlantableSeeds(): IntegratedItem[] {
    return this.getAllItems().filter(item => 
      item.type === ItemType.SEED && 
      item.category === ItemCategory.AGRICULTURAL &&
      item.quantity > 0
    )
  }

  /**
   * 获取可合成的物品
   */
  getSynthesizableItems(): IntegratedItem[] {
    return this.getAllItems().filter(item => 
      item.synthesizable && 
      item.quantity > 0
    )
  }

  // ==================== 事件监听器注册 ====================

  onItemAdded(callback: (item: IntegratedItem) => void): void {
    this.on('itemAdded', callback)
  }

  onItemUpdated(callback: (item: IntegratedItem) => void): void {
    this.on('itemUpdated', callback)
  }

  onItemRemoved(callback: (item: IntegratedItem) => void): void {
    this.on('itemRemoved', callback)
  }

  onItemPlanted(callback: (data: { item: IntegratedItem; slotId: string; agriculturalItem: AgriculturalItem }) => void): void {
    this.on('itemPlanted', callback)
  }

  onItemHarvested(callback: (item: IntegratedItem) => void): void {
    this.on('itemHarvested', callback)
  }

  onItemsSynthesized(callback: (data: { materials: IntegratedItem[]; result: IntegratedItem }) => void): void {
    this.on('itemsSynthesized', callback)
  }

  onLootboxIntegrated(callback: (data: { lootboxType: LootboxType; items: IntegratedItem[] }) => void): void {
    this.on('lootboxIntegrated', callback)
  }
} 