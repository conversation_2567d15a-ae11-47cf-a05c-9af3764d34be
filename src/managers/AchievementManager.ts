import {
  Achievement,
  AchievementType,
  AchievementCategory,
  UserAchievementProgress,
  AchievementStats,
  AchievementNotification,
  AchievementEvent,
  DailyTask,
  WeeklyTask,
  UserDailyTaskProgress,
  UserWeeklyTaskProgress
} from '../types/achievements';
import {
  ACHIEVEMENTS,
  DAILY_TASKS,
  WEEKLY_TASKS,
  getAchievementById,
  getDailyTaskById,
  getWeeklyTaskById
} from '../data/achievementsConfig';

export interface AchievementCheckResult {
  newAchievements: Achievement[];
  updatedProgress: UserAchievementProgress[];
  notifications: AchievementNotification[];
  experienceGained: number;
}

export class AchievementManager {
  private userProgress: Map<string, UserAchievementProgress> = new Map();
  private userAchievements: Set<string> = new Set();
  private dailyTaskProgress: Map<string, UserDailyTaskProgress> = new Map();
  private weeklyTaskProgress: Map<string, UserWeeklyTaskProgress> = new Map();
  private achievementCallbacks: Array<(achievement: Achievement) => void> = [];

  constructor() {
    this.initializeProgress();
  }

  /**
   * 初始化用户进度
   */
  private initializeProgress(): void {
    // 初始化所有成就的进度
    ACHIEVEMENTS.forEach(achievement => {
      if (!this.userProgress.has(achievement.id)) {
        this.userProgress.set(achievement.id, {
          achievementId: achievement.id,
          progress: 0,
          maxProgress: achievement.requirement.value,
          isCompleted: false,
          lastUpdated: new Date()
        });
      }
    });

    // 初始化日常任务进度
    this.initializeDailyTasks();
    
    // 初始化周常任务进度
    this.initializeWeeklyTasks();
  }

  /**
   * 注册成就解锁回调
   */
  onAchievementUnlocked(callback: (achievement: Achievement) => void): void {
    this.achievementCallbacks.push(callback);
  }

  /**
   * 处理成就事件
   */
  async processAchievementEvent(event: AchievementEvent): Promise<AchievementCheckResult> {
    const results: AchievementCheckResult = {
      newAchievements: [],
      updatedProgress: [],
      notifications: [],
      experienceGained: 0
    };

    // 检查所有相关成就
    const relevantAchievements = this.getRelevantAchievements(event);
    
    for (const achievement of relevantAchievements) {
      const checkResult = await this.checkAchievement(achievement, event);
      
      if (checkResult.completed && !this.userAchievements.has(achievement.id)) {
        // 新成就解锁
        this.userAchievements.add(achievement.id);
        results.newAchievements.push(achievement);
        results.experienceGained += achievement.experienceReward;
        
        // 创建通知
        const notification = this.createAchievementNotification(achievement);
        results.notifications.push(notification);
        
        // 触发回调
        this.achievementCallbacks.forEach(callback => callback(achievement));
      }
      
      if (checkResult.progressUpdated) {
        results.updatedProgress.push(checkResult.progress);
      }
    }

    return results;
  }

  /**
   * 获取与事件相关的成就
   */
  private getRelevantAchievements(event: AchievementEvent): Achievement[] {
    return ACHIEVEMENTS.filter(achievement => {
      // 过滤已完成的成就
      if (this.userAchievements.has(achievement.id)) {
        return false;
      }

      // 检查前置条件
      if (achievement.prerequisite) {
        const hasPrerequisites = achievement.prerequisite.every(prereqId => 
          this.userAchievements.has(prereqId)
        );
        if (!hasPrerequisites) {
          return false;
        }
      }

      // 根据事件类型过滤相关成就
      return this.isEventRelevantToAchievement(event, achievement);
    });
  }

  /**
   * 检查事件是否与成就相关
   */
  private isEventRelevantToAchievement(event: AchievementEvent, achievement: Achievement): boolean {
    const eventType = event.type;
    const requirementType = achievement.requirement.type;

    // 专注时间相关
    if (requirementType.includes('focus_time') && 
        (eventType === 'session_completed' || eventType === 'focus_time_updated')) {
      return true;
    }

    // 姿态相关
    if (requirementType.includes('posture') && 
        (eventType === 'session_completed' || eventType === 'posture_score_updated')) {
      return true;
    }

    // 会话相关
    if (requirementType.includes('session') && eventType === 'session_completed') {
      return true;
    }

    return false;
  }

  /**
   * 检查单个成就
   */
  private async checkAchievement(
    achievement: Achievement, 
    event: AchievementEvent
  ): Promise<{
    completed: boolean;
    progressUpdated: boolean;
    progress: UserAchievementProgress;
  }> {
    const currentProgress = this.userProgress.get(achievement.id)!;
    let newProgress = currentProgress.progress;
    let completed = false;

    // 根据成就类型计算进度
    switch (achievement.requirement.type) {
      case 'continuous_focus_time':
        newProgress = this.calculateContinuousFocusProgress(event.data);
        break;
      
      case 'daily_focus_time':
        newProgress = this.calculateDailyFocusProgress(event.data);
        break;
      
      case 'session_posture_score':
        newProgress = this.calculatePostureScoreProgress(event.data);
        break;
      
      case 'perfect_session':
        newProgress = this.checkPerfectSession(event.data) ? achievement.requirement.value : 0;
        break;
      
      default:
        // 其他类型的进度计算
        newProgress = this.calculateGenericProgress(achievement, event.data);
    }

    // 检查是否完成
    if (newProgress >= achievement.requirement.value) {
      completed = true;
      newProgress = achievement.requirement.value;
    }

    // 更新进度
    const progressUpdated = newProgress !== currentProgress.progress;
    if (progressUpdated) {
      currentProgress.progress = newProgress;
      currentProgress.lastUpdated = new Date();
      
      if (completed) {
        currentProgress.isCompleted = true;
        currentProgress.completedAt = new Date();
      }
    }

    return {
      completed,
      progressUpdated,
      progress: currentProgress
    };
  }

  /**
   * 初始化日常任务
   */
  private initializeDailyTasks(): void {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    DAILY_TASKS.forEach(task => {
      const existing = this.dailyTaskProgress.get(task.id);
      
      // 检查是否需要重置（新的一天）
      if (!existing || existing.resetAt < today) {
        const resetAt = new Date(today);
        resetAt.setDate(resetAt.getDate() + 1); // 明天重置

        this.dailyTaskProgress.set(task.id, {
          taskId: task.id,
          progress: 0,
          maxProgress: task.requirement.value,
          isCompleted: false,
          resetAt,
          lastUpdated: new Date()
        });
      }
    });
  }

  /**
   * 初始化周常任务
   */
  private initializeWeeklyTasks(): void {
    const now = new Date();
    
    WEEKLY_TASKS.forEach(task => {
      const existing = this.weeklyTaskProgress.get(task.id);
      
      // 计算下次重置时间（基于任务的重置日）
      const resetDate = this.calculateWeeklyResetDate(task.resetDay);
      
      // 检查是否需要重置
      if (!existing || existing.resetAt < now) {
        this.weeklyTaskProgress.set(task.id, {
          taskId: task.id,
          progress: 0,
          maxProgress: task.requirement.value,
          isCompleted: false,
          resetAt: resetDate,
          lastUpdated: new Date()
        });
      }
    });
  }

  /**
   * 计算周常任务重置日期
   */
  private calculateWeeklyResetDate(resetDay: number): Date {
    const now = new Date();
    const currentDay = now.getDay(); // 0=周日, 1=周一...
    
    let daysUntilReset = resetDay - currentDay;
    if (daysUntilReset <= 0) {
      daysUntilReset += 7; // 下周的重置日
    }
    
    const resetDate = new Date(now);
    resetDate.setDate(now.getDate() + daysUntilReset);
    resetDate.setHours(0, 0, 0, 0);
    
    return resetDate;
  }

  /**
   * 计算连续专注时间进度
   */
  private calculateContinuousFocusProgress(data: any): number {
    return data.continuousFocusTime || 0;
  }

  /**
   * 计算每日专注时间进度
   */
  private calculateDailyFocusProgress(data: any): number {
    return data.dailyFocusTime || 0;
  }

  /**
   * 计算姿态分数进度
   */
  private calculatePostureScoreProgress(data: any): number {
    return data.averagePostureScore || 0;
  }

  /**
   * 检查完美会话
   */
  private checkPerfectSession(data: any): boolean {
    const focusScore = data.focusScore || 0;
    const postureScore = data.postureScore || 0;
    
    return focusScore >= 95 && postureScore >= 95;
  }

  /**
   * 通用进度计算
   */
  private calculateGenericProgress(achievement: Achievement, data: any): number {
    // 根据不同的需求类型计算进度
    const requirementType = achievement.requirement.type;
    
    if (data[requirementType] !== undefined) {
      return data[requirementType];
    }
    
    return 0;
  }

  /**
   * 获取用户成就统计
   */
  getAchievementStats(): AchievementStats {
    const totalAchievements = ACHIEVEMENTS.length;
    const completedAchievements = this.userAchievements.size;
    const completionRate = (completedAchievements / totalAchievements) * 100;

    // 按类别统计
    const categoryCounts: Record<AchievementCategory, { total: number; completed: number }> = {
      [AchievementCategory.FOCUS]: { total: 0, completed: 0 },
      [AchievementCategory.POSTURE]: { total: 0, completed: 0 },
      [AchievementCategory.TIME]: { total: 0, completed: 0 },
      [AchievementCategory.CONSISTENCY]: { total: 0, completed: 0 },
      [AchievementCategory.IMPROVEMENT]: { total: 0, completed: 0 }
    };

    ACHIEVEMENTS.forEach(achievement => {
      categoryCounts[achievement.category].total++;
      if (this.userAchievements.has(achievement.id)) {
        categoryCounts[achievement.category].completed++;
      }
    });

    return {
      totalAchievements,
      completedAchievements,
      completionRate,
      categoryCounts
    };
  }

  /**
   * 创建成就通知
   */
  private createAchievementNotification(achievement: Achievement): AchievementNotification {
    return {
      id: this.generateId(),
      type: 'achievement',
      title: '成就解锁！',
      message: `恭喜你获得成就：${achievement.name}`,
      icon: achievement.icon,
      achievement,
      experience: achievement.experienceReward,
      createdAt: new Date(),
      isRead: false
    };
  }

  /**
   * 生成唯一ID
   */
  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }
} 