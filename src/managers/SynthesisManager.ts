import { 
  AgriculturalItem, 
  SynthesisRecipe, 
  SynthesisSession, 
  SynthesisResult, 
  SynthesisStatus,
  ItemCategory
} from '../types/agriculture'
import { ItemRarity } from '../types/lootbox'
import { EventEmitter } from 'eventemitter3'
import { SYNTHESIS_FAILURE_CONFIGS, FailureConsequenceType } from '../data/synthesisFailure'

export class SynthesisManager extends EventEmitter {
  private activeSessions: Map<string, SynthesisSession> = new Map()
  private completedSessions: SynthesisSession[] = []
  private playerLevel: number

  constructor(playerLevel: number = 1) {
    super()
    this.playerLevel = playerLevel
  }

  // 创建合成会话
  async createSynthesisSession(
    inputItems: AgriculturalItem[],
    protectionItems: string[] = []
  ): Promise<{ success: boolean; sessionId?: string; error?: string }> {
    
    // 验证输入物品
    if (inputItems.length !== 2) {
      return { success: false, error: '合成需要恰好2个物品' }
    }

    const [item1, item2] = inputItems

    // 检查物品是否同品质同品种
    if (item1.rarity !== item2.rarity) {
      return { success: false, error: '只能合成相同品质的物品' }
    }

    if (item1.variety !== item2.variety) {
      return { success: false, error: '只能合成相同品种的物品' }
    }

    // 检查是否是可合成的物品类型
    if (item1.category !== ItemCategory.CROP && item1.category !== ItemCategory.LIVESTOCK) {
      return { success: false, error: '该类型物品无法合成' }
    }

    // 创建会话
    const sessionId = this.generateSessionId()
    const recipe = this.createSynthesisRecipe(inputItems)
    
    const session: SynthesisSession = {
      id: sessionId,
      recipeId: recipe.id,
      inputItems,
      startTime: Date.now(),
      endTime: Date.now() + recipe.craftTime * 1000,
      status: SynthesisStatus.PREPARING,
      hasProtection: protectionItems.length > 0,
      protectionItems
    }

    this.activeSessions.set(sessionId, session)

    this.emit('synthesisSessionCreated', {
      sessionId,
      inputItems: inputItems.map(item => ({ id: item.id, name: item.name, rarity: item.rarity })),
      estimatedTime: recipe.craftTime,
      successRate: recipe.successRate
    })

    return { success: true, sessionId }
  }

  // 开始合成
  async startSynthesis(sessionId: string): Promise<{ success: boolean; error?: string }> {
    const session = this.activeSessions.get(sessionId)
    
    if (!session) {
      return { success: false, error: '合成会话不存在' }
    }

    if (session.status !== SynthesisStatus.PREPARING) {
      return { success: false, error: '会话状态错误' }
    }

    // 更新会话状态
    session.status = SynthesisStatus.IN_PROGRESS
    session.startTime = Date.now()
    
    // 计算实际合成时间（可以有随机变化）
    const baseTime = this.getSynthesisTime(session.inputItems[0].rarity)
    const actualTime = baseTime + (Math.random() - 0.5) * baseTime * 0.2 // ±20% 变化
    
    session.endTime = session.startTime + actualTime * 1000

    this.emit('synthesisStarted', {
      sessionId,
      startTime: session.startTime,
      estimatedEndTime: session.endTime
    })

    // 设置定时器完成合成
    setTimeout(() => {
      this.completeSynthesis(sessionId)
    }, actualTime * 1000)

    return { success: true }
  }

  // 完成合成
  private async completeSynthesis(sessionId: string): Promise<void> {
    const session = this.activeSessions.get(sessionId)
    
    if (!session || session.status !== SynthesisStatus.IN_PROGRESS) {
      return
    }

    const recipe = this.createSynthesisRecipe(session.inputItems)
    const result = this.performSynthesis(session, recipe)
    
    session.result = result
    session.status = result.success ? SynthesisStatus.COMPLETED : SynthesisStatus.FAILED
    
    // 移动到完成会话列表
    this.activeSessions.delete(sessionId)
    this.completedSessions.push(session)

    this.emit('synthesisCompleted', {
      sessionId,
      result,
      inputItems: session.inputItems,
      hadProtection: session.hasProtection
    })
  }

  // 执行合成逻辑
  private performSynthesis(session: SynthesisSession, recipe: SynthesisRecipe): SynthesisResult {
    const inputRarity = session.inputItems[0].rarity
    const targetRarity = this.getTargetRarity(inputRarity)
    
    if (!targetRarity) {
      return {
        success: false,
        outputItems: [],
        failureReason: '已达到最高品质',
        experienceGained: 10
      }
    }

    // 计算成功率（包括保护加成）
    let successRate = recipe.successRate
    if (session.hasProtection) {
      successRate = this.applyProtectionBonus(successRate, session.protectionItems)
    }

    const isSuccess = Math.random() < successRate

    if (isSuccess) {
      // 合成成功
      const outputItem = this.createUpgradedItem(session.inputItems[0], targetRarity)
      return {
        success: true,
        outputItems: [outputItem],
        experienceGained: this.getExperienceReward(targetRarity)
      }
    } else {
      // 合成失败
      return this.handleSynthesisFailure(session, inputRarity)
    }
  }

  // 处理合成失败
  private handleSynthesisFailure(session: SynthesisSession, inputRarity: ItemRarity): SynthesisResult {
    const failureConfig = SYNTHESIS_FAILURE_CONFIGS[inputRarity]
    
    if (!failureConfig) {
      return {
        success: false,
        outputItems: session.inputItems, // 默认保留所有物品
        failureReason: '未知错误',
        experienceGained: 5
      }
    }

    // 如果有保护，优先触发保护效果
    if (session.hasProtection) {
      return {
        success: false,
        outputItems: session.inputItems, // 保护机制保留所有物品
        failureReason: '合成失败，但保护机制生效',
        experienceGained: 8
      }
    }

    // 随机选择失败后果
    const random = Math.random()
    let cumulativeProbability = 0

    for (const consequence of failureConfig.consequences) {
      cumulativeProbability += consequence.probability
      if (random <= cumulativeProbability) {
        return this.applyFailureConsequence(session, consequence)
      }
    }

    // 默认后果
    return {
      success: false,
      outputItems: session.inputItems,
      failureReason: '合成失败',
      experienceGained: 5
    }
  }

  // 应用失败后果
  private applyFailureConsequence(session: SynthesisSession, consequence: any): SynthesisResult {
    const inputItems = session.inputItems

    switch (consequence.type) {
      case FailureConsequenceType.KEEP_MATERIALS:
        return {
          success: false,
          outputItems: inputItems,
          failureReason: consequence.description,
          experienceGained: 5
        }

      case FailureConsequenceType.LOSE_ONE_ITEM:
        const remainingItems = inputItems.slice(0, inputItems.length - (consequence.details?.itemsLost || 1))
        return {
          success: false,
          outputItems: remainingItems,
          failureReason: consequence.description,
          experienceGained: 3
        }

      case FailureConsequenceType.LOSE_ALL_ITEMS:
        return {
          success: false,
          outputItems: [],
          failureReason: consequence.description,
          experienceGained: 1
        }

      case FailureConsequenceType.DOWNGRADE_QUALITY:
        const downgradedItems = inputItems.map(item => this.downgradeItem(item))
        return {
          success: false,
          outputItems: downgradedItems,
          failureReason: consequence.description,
          experienceGained: 4
        }

      case FailureConsequenceType.GET_COMPENSATION:
        const compensationValue = (consequence.details?.compensationValue || 50) / 100
        const compensationItems = this.createCompensationItems(inputItems, compensationValue)
        return {
          success: false,
          outputItems: inputItems,
          compensationItems,
          failureReason: consequence.description,
          experienceGained: 6
        }

      default:
        return {
          success: false,
          outputItems: inputItems,
          failureReason: '未知失败类型',
          experienceGained: 5
        }
    }
  }

  // 创建补偿物品
  private createCompensationItems(originalItems: AgriculturalItem[], compensationRate: number): AgriculturalItem[] {
    // 这里可以创建一些补偿性的材料或低级物品
    // 简化实现，返回一些基础材料
    return []
  }

  // 降级物品
  private downgradeItem(item: AgriculturalItem): AgriculturalItem {
    const lowerRarity = this.getLowerRarity(item.rarity)
    if (lowerRarity && lowerRarity !== item.rarity) {
      return this.createUpgradedItem(item, lowerRarity)
    }
    return item
  }

  // 获取更低的稀有度
  private getLowerRarity(rarity: ItemRarity): ItemRarity | null {
    switch (rarity) {
      case ItemRarity.GREEN: return ItemRarity.GRAY
      case ItemRarity.BLUE: return ItemRarity.GREEN
      case ItemRarity.ORANGE: return ItemRarity.BLUE
      case ItemRarity.GOLD: return ItemRarity.ORANGE
      case ItemRarity.GOLD_RED: return ItemRarity.GOLD
      default: return null
    }
  }

  // 应用保护加成
  private applyProtectionBonus(baseSuccessRate: number, protectionItems: string[]): number {
    let bonusRate = 0
    
    for (const protectionItem of protectionItems) {
      // 根据保护物品类型增加成功率
      if (protectionItem.includes('basic_protection')) {
        bonusRate += 0.15
      } else if (protectionItem.includes('advanced_protection')) {
        bonusRate += 0.25
      } else if (protectionItem.includes('perfect_protection')) {
        bonusRate += 0.4
      }
    }

    return Math.min(baseSuccessRate + bonusRate, 0.95) // 最高95%成功率
  }

  // 创建升级后的物品
  private createUpgradedItem(originalItem: AgriculturalItem, targetRarity: ItemRarity): AgriculturalItem {
    const upgradedItem: AgriculturalItem = {
      ...originalItem,
      id: `${originalItem.variety}_${targetRarity}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      rarity: targetRarity,
      level: originalItem.level + 1,
      quality: Math.min(originalItem.quality + Math.floor(Math.random() * 20) + 10, 100)
    }

    // 更新生产数据
    // 这里需要根据新的稀有度重新计算产量
    // 简化实现，增加30-50%的产量
    const productionMultiplier = 1.3 + Math.random() * 0.2
    upgradedItem.production.minDaily = Math.floor(originalItem.production.minDaily * productionMultiplier)
    upgradedItem.production.maxDaily = Math.floor(originalItem.production.maxDaily * productionMultiplier)
    upgradedItem.production.currentDaily = Math.floor((originalItem.production.currentDaily || 0) * productionMultiplier)

    // 更新价值
    const valueMultiplier = this.getValueMultiplier(targetRarity)
    upgradedItem.value.basePrice = Math.floor(originalItem.value.basePrice * valueMultiplier)
    upgradedItem.value.currentPrice = Math.floor(originalItem.value.currentPrice * valueMultiplier)

    // 更新生长时间（高品质生长更快）
    upgradedItem.growth.growthTime = Math.floor(originalItem.growth.growthTime * 0.8)

    // 标记为合成产物
    if (!upgradedItem.special) {
      upgradedItem.special = {}
    }
    upgradedItem.special.parentItems = [originalItem.id]
    upgradedItem.special.isHybrid = true

    return upgradedItem
  }

  // 获取价值倍数
  private getValueMultiplier(rarity: ItemRarity): number {
    switch (rarity) {
      case ItemRarity.GRAY: return 1
      case ItemRarity.GREEN: return 2.5
      case ItemRarity.BLUE: return 5
      case ItemRarity.ORANGE: return 10
      case ItemRarity.GOLD: return 20
      case ItemRarity.GOLD_RED: return 50
      default: return 1
    }
  }

  // 获取目标稀有度
  private getTargetRarity(currentRarity: ItemRarity): ItemRarity | null {
    switch (currentRarity) {
      case ItemRarity.GRAY: return ItemRarity.GREEN
      case ItemRarity.GREEN: return ItemRarity.BLUE
      case ItemRarity.BLUE: return ItemRarity.ORANGE
      case ItemRarity.ORANGE: return ItemRarity.GOLD
      case ItemRarity.GOLD: return ItemRarity.GOLD_RED
      case ItemRarity.GOLD_RED: return null
      default: return null
    }
  }

  // 创建合成配方
  private createSynthesisRecipe(inputItems: AgriculturalItem[]): SynthesisRecipe {
    const inputRarity = inputItems[0].rarity
    const variety = inputItems[0].variety
    
    return {
      id: `synthesis_${variety}_${inputRarity}_${Date.now()}`,
      name: `合成 ${inputItems[0].name}`,
      description: `将两个${inputItems[0].name}合成为更高品质`,
      inputItems: [
        {
          itemId: inputItems[0].id,
          quantity: 2,
          rarity: inputRarity,
          mustBeSameVariety: true
        }
      ],
      outputItem: {
        itemId: '',
        quantity: 1,
        rarity: this.getTargetRarity(inputRarity) || inputRarity,
        variety,
        qualityRange: { min: 60, max: 100 }
      },
      successRate: this.getBaseSuccessRate(inputRarity),
      cost: { gold: this.getSynthesisCost(inputRarity) },
      requiredLevel: this.getRequiredLevel(inputRarity),
      craftTime: this.getSynthesisTime(inputRarity)
    }
  }

  // 获取基础成功率
  private getBaseSuccessRate(rarity: ItemRarity): number {
    switch (rarity) {
      case ItemRarity.GRAY: return 0.95
      case ItemRarity.GREEN: return 0.90
      case ItemRarity.BLUE: return 0.85
      case ItemRarity.ORANGE: return 0.75
      case ItemRarity.GOLD: return 0.60
      default: return 0.50
    }
  }

  // 获取合成费用
  private getSynthesisCost(rarity: ItemRarity): number {
    switch (rarity) {
      case ItemRarity.GRAY: return 100
      case ItemRarity.GREEN: return 500
      case ItemRarity.BLUE: return 2000
      case ItemRarity.ORANGE: return 8000
      case ItemRarity.GOLD: return 30000
      default: return 100000
    }
  }

  // 获取所需等级
  private getRequiredLevel(rarity: ItemRarity): number {
    switch (rarity) {
      case ItemRarity.GRAY: return 1
      case ItemRarity.GREEN: return 10
      case ItemRarity.BLUE: return 25
      case ItemRarity.ORANGE: return 40
      case ItemRarity.GOLD: return 60
      default: return 80
    }
  }

  // 获取合成时间（秒）
  private getSynthesisTime(rarity: ItemRarity): number {
    switch (rarity) {
      case ItemRarity.GRAY: return 30
      case ItemRarity.GREEN: return 60
      case ItemRarity.BLUE: return 180
      case ItemRarity.ORANGE: return 600
      case ItemRarity.GOLD: return 1800
      default: return 3600
    }
  }

  // 获取经验奖励
  private getExperienceReward(rarity: ItemRarity): number {
    switch (rarity) {
      case ItemRarity.GRAY: return 10
      case ItemRarity.GREEN: return 25
      case ItemRarity.BLUE: return 50
      case ItemRarity.ORANGE: return 100
      case ItemRarity.GOLD: return 200
      case ItemRarity.GOLD_RED: return 500
      default: return 10
    }
  }

  // 生成会话ID
  private generateSessionId(): string {
    return `synthesis_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  // 获取活跃会话
  getActiveSessions(): SynthesisSession[] {
    return Array.from(this.activeSessions.values())
  }

  // 获取已完成会话
  getCompletedSessions(): SynthesisSession[] {
    return this.completedSessions.slice(-20) // 只返回最近20个
  }

  // 获取会话详情
  getSession(sessionId: string): SynthesisSession | null {
    return this.activeSessions.get(sessionId) || 
           this.completedSessions.find(s => s.id === sessionId) || 
           null
  }

  // 取消合成
  cancelSynthesis(sessionId: string): boolean {
    const session = this.activeSessions.get(sessionId)
    
    if (!session || session.status !== SynthesisStatus.IN_PROGRESS) {
      return false
    }

    session.status = SynthesisStatus.CANCELLED
    this.activeSessions.delete(sessionId)
    this.completedSessions.push(session)

    this.emit('synthesisCancelled', { sessionId })
    return true
  }

  // 检查合成资格
  canSynthesize(item1: AgriculturalItem, item2: AgriculturalItem): { canSynthesize: boolean; reason?: string } {
    if (item1.rarity !== item2.rarity) {
      return { canSynthesize: false, reason: '物品品质必须相同' }
    }

    if (item1.variety !== item2.variety) {
      return { canSynthesize: false, reason: '物品品种必须相同' }
    }

    if (item1.category !== ItemCategory.CROP && item1.category !== ItemCategory.LIVESTOCK) {
      return { canSynthesize: false, reason: '该类型物品无法合成' }
    }

    if (item1.rarity === ItemRarity.GOLD_RED) {
      return { canSynthesize: false, reason: '已达到最高品质，无法继续合成' }
    }

    const requiredLevel = this.getRequiredLevel(item1.rarity)
    if (this.playerLevel < requiredLevel) {
      return { canSynthesize: false, reason: `需要等级 ${requiredLevel}` }
    }

    return { canSynthesize: true }
  }

  // 设置玩家等级
  setPlayerLevel(level: number): void {
    this.playerLevel = level
  }

  // 获取合成统计
  getSynthesisStatistics() {
    const completed = this.completedSessions
    const successful = completed.filter(s => s.result?.success)
    const failed = completed.filter(s => s.result?.success === false)

    return {
      totalSyntheses: completed.length,
      successfulSyntheses: successful.length,
      failedSyntheses: failed.length,
      successRate: completed.length > 0 ? successful.length / completed.length : 0,
      activeSessions: this.activeSessions.size,
      totalExperienceGained: completed.reduce((sum, s) => sum + (s.result?.experienceGained || 0), 0)
    }
  }
} 