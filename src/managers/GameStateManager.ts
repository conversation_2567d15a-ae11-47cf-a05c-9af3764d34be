import { 
  CropInstance, 
  CropType, 
  CropStage, 
  CropQuality,
  FarmGrid 
} from '../types/crop'
import { DatabaseManager } from '../storage/DatabaseManager'
import { GameProgress, CropProgressData } from '../types/user'

// 简单的EventEmitter实现（适用于浏览器环境）
class SimpleEventEmitter {
  private listeners: Map<string, Array<(...args: any[]) => void>> = new Map()
  
  on(event: string, listener: (...args: any[]) => void): this {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, [])
    }
    this.listeners.get(event)!.push(listener)
    return this
  }
  
  emit(event: string, ...args: any[]): boolean {
    const listeners = this.listeners.get(event)
    if (listeners && listeners.length > 0) {
      listeners.forEach(listener => {
        try {
          listener(...args)
        } catch (error) {
          console.error(`Event listener error for ${event}:`, error)
        }
      })
      return true
    }
    return false
  }
  
  removeAllListeners(): this {
    this.listeners.clear()
    return this
  }
}

// 游戏状态接口
// 专注度状态接口（来自PoseBehaviorConnector）
export interface FocusState {
  focusScore: number
  isFocused: boolean
  sessionDuration: number
  consecutiveFocusTime: number
  lastUpdateTime: number
}

export interface GameState {
  version: string
  playerName: string
  playerId: string
  level: number
  experience: number
  totalFocusTime: number
  totalCropsHarvested: number
  resources: {
    knowledge: number
    strength: number
    time: number
    meditation: number
  }
  farmGrid: FarmGrid
  gridSize: { width: number; height: number }
  crops: Map<string, CropInstance>
  settings: {
    autoSave: boolean
    autoSaveInterval: number
    enableNotifications: boolean
    soundEnabled: boolean
    musicEnabled: boolean
  }
  gameTime: {
    totalPlayTime: number
    sessionStartTime: number
    lastSaveTime: number
  }
  unlockedFeatures: Set<string>
  achievements: Set<string>
  // 新增：专注度系统状态
  focusSystem: {
    currentFocusState: FocusState
    sessionStats: {
      bestFocusStreak: number
      totalFocusBreaks: number
      averageSessionFocus: number
      focusStartTime: number | null
    }
    isConnectedToPoseDetection: boolean
    lastPoseDataTime: number
  }
}

/**
 * 游戏状态管理器
 */
export class GameStateManager extends SimpleEventEmitter {
  private static instance: GameStateManager
  private gameState: GameState
  private dbManager: DatabaseManager | null = null
  private saveTimer: number | null = null
  private autoSaveEnabled: boolean = true
  private autoSaveInterval: number = 5 * 60 * 1000 // 5分钟

  private constructor() {
    super()
    this.gameState = this.createDefaultState()
  }
  
  private createDefaultState(): GameState {
    return {
      version: '1.0.0',
      playerName: 'Player',
      playerId: 'player_' + Date.now(),
      level: 1,
      experience: 0,
      gameTime: {
        totalPlayTime: 0,
        lastSaveTime: Date.now(),
        sessionStartTime: Date.now()
      },
      crops: new Map<string, CropInstance>(),
      farmGrid: {
        height: 8,
        width: 8,
        plots: Array(8).fill(null).map(() => Array(8).fill(null))
      },
      gridSize: { width: 8, height: 8 },
      resources: {
        knowledge: 10,
        strength: 100,
        time: 5,
        meditation: 0
      },
      totalCropsHarvested: 0,
      totalFocusTime: 0,
      unlockedFeatures: new Set<string>(),
      achievements: new Set<string>(),
      settings: {
        autoSave: true,
        autoSaveInterval: 5 * 60 * 1000,
        enableNotifications: true,
        soundEnabled: true,
        musicEnabled: true
      },
      focusSystem: {
        currentFocusState: {
          isFocused: false,
          focusScore: 0,
          sessionDuration: 0,
          consecutiveFocusTime: 0,
          lastUpdateTime: Date.now()
        },
        sessionStats: {
          bestFocusStreak: 0,
          totalFocusBreaks: 0,
          averageSessionFocus: 0,
          focusStartTime: null
        },
        lastPoseDataTime: 0,
        isConnectedToPoseDetection: false
      }
    }
  }
  
  getGameState(): Readonly<GameState> {
    return Object.freeze(JSON.parse(JSON.stringify(this.gameState)))
  }
  
  plantCrop(gridX: number, gridY: number, cropType: CropType): boolean {
    if (gridX < 0 || gridX >= this.gameState.gridSize.width || 
        gridY < 0 || gridY >= this.gameState.gridSize.height) {
      console.warn(`位置 (${gridX}, ${gridY}) 超出农场边界`)
      return false
    }
    
    if (this.gameState.farmGrid.plots[gridY][gridX] !== null) {
      console.warn(`位置 (${gridX}, ${gridY}) 已被占用`)
      return false
    }
    
    const now = Date.now()
    const cropId = 'crop_' + now + '_' + Math.random().toString(36).substr(2, 9)
    
    const crop: CropInstance = {
      id: cropId,
      type: cropType,
      stage: CropStage.SEED,
      quality: CropQuality.COMMON,
      plantedAt: now,
      stageStartTime: now,
      totalGrowthTime: 0,
      focusTimeContributed: 0,
      averageFocusScore: 0,
      position: {
        x: gridX * 64,
        y: gridY * 64,
        gridX,
        gridY
      },
      isGrowing: true,
      isPaused: false,
      harvestable: false,
      metadata: {
        sessionsContributed: 0,
        bestFocusStreak: 0,
        growthBoosts: 0
      }
    }
    
    this.gameState.crops.set(cropId, crop)
    this.gameState.farmGrid.plots[gridY][gridX] = crop
    
    this.emit('crop_planted', { crop, position: { gridX, gridY } })
    return true
  }
  
  harvestCrop(gridX: number, gridY: number): {
    success: boolean
    rewards?: { experience: number; resources: Record<string, number> }
    crop?: CropInstance
  } {
    const crop = this.gameState.farmGrid.plots[gridY]?.[gridX]
    
    if (!crop || crop.stage !== CropStage.READY_TO_HARVEST) {
      return { success: false }
    }
    
    const rewards = {
      experience: 10,
      resources: { [crop.type]: 5 }
    }
    
    this.gameState.experience += rewards.experience
    this.gameState.totalCropsHarvested++
    
    this.gameState.crops.delete(crop.id)
    this.gameState.farmGrid.plots[gridY][gridX] = null
    
    this.emit('crop_harvested', { crop, rewards, position: { gridX, gridY } })
    return { success: true, rewards, crop }
  }
  
  async saveGameState(): Promise<boolean> {
    try {
      const saveData = {
        ...this.gameState,
        crops: Array.from(this.gameState.crops.entries()),
        unlockedFeatures: Array.from(this.gameState.unlockedFeatures),
        achievements: Array.from(this.gameState.achievements),
        // 保存专注系统状态，但不保存实时连接状态
        focusSystem: {
          ...this.gameState.focusSystem,
          isConnectedToPoseDetection: false, // 重启后需要重新连接
          lastPoseDataTime: 0
        }
      }
      
      localStorage.setItem('selfgame_save_data', JSON.stringify(saveData))
      this.gameState.gameTime.lastSaveTime = Date.now()
      return true
    } catch (error) {
      console.error('保存失败:', error)
      return false
    }
  }
  
  async loadGameState(): Promise<boolean> {
    try {
      const data = localStorage.getItem('selfgame_save_data')
      if (!data) return false
      
      const saveData = JSON.parse(data)
      this.gameState = {
        ...saveData,
        crops: new Map(saveData.crops),
        unlockedFeatures: new Set(saveData.unlockedFeatures || []),
        achievements: new Set(saveData.achievements || []),
        // 恢复专注系统状态，设置默认值以防数据不完整
        focusSystem: {
          currentFocusState: saveData.focusSystem?.currentFocusState || {
            focusScore: 0,
            isFocused: false,
            sessionDuration: 0,
            consecutiveFocusTime: 0,
            lastUpdateTime: Date.now()
          },
          sessionStats: saveData.focusSystem?.sessionStats || {
            bestFocusStreak: 0,
            totalFocusBreaks: 0,
            averageSessionFocus: 0,
            focusStartTime: null
          },
          isConnectedToPoseDetection: false, // 重启后需要重新连接
          lastPoseDataTime: 0
        }
      }
      
      // 重建farmGrid中的作物引用
      this.gameState.farmGrid.plots = Array(this.gameState.gridSize.height).fill(null).map(() => Array(this.gameState.gridSize.width).fill(null))
      this.gameState.crops.forEach((crop) => {
        const { gridX, gridY } = crop.position
        if (gridY < this.gameState.farmGrid.height && gridX < this.gameState.farmGrid.width) {
          this.gameState.farmGrid.plots[gridY][gridX] = crop
        }
      })
      
      return true
    } catch (error) {
      console.error('加载失败:', error)
      return false
    }
  }
  
  resetGameState(): void {
    this.gameState = this.createDefaultState()
    this.emit('game_reset', { timestamp: Date.now() })
  }
  
  /**
   * 更新专注度状态（从PoseBehaviorConnector调用）
   */
  updateFocusState(focusState: FocusState): void {
    const previousState = this.gameState.focusSystem.currentFocusState
    const now = Date.now()
    
    // 更新当前专注状态
    this.gameState.focusSystem.currentFocusState = { ...focusState }
    this.gameState.focusSystem.lastPoseDataTime = now
    
    // 更新总专注时间
    if (focusState.isFocused) {
      const deltaTime = now - previousState.lastUpdateTime
      this.gameState.totalFocusTime += deltaTime
    }
    
    // 更新会话统计
    this.updateFocusSessionStats(previousState, focusState, now)
    
    // 发射专注状态变化事件
    this.emit('focus_state_changed', {
      previousState,
      currentState: focusState,
      timestamp: now
    })
    
    // 更新作物生长系统（如果有作物时间管理器）
    this.updateCropGrowthWithFocus(focusState)
  }
  
  /**
   * 更新专注会话统计
   */
  private updateFocusSessionStats(previousState: FocusState, currentState: FocusState, timestamp: number): void {
    const stats = this.gameState.focusSystem.sessionStats
    
    // 专注开始
    if (!previousState.isFocused && currentState.isFocused) {
      stats.focusStartTime = timestamp
    }
    
    // 专注中断
    if (previousState.isFocused && !currentState.isFocused) {
      stats.totalFocusBreaks++
      
      // 更新最佳专注连续时间
      if (previousState.consecutiveFocusTime > stats.bestFocusStreak) {
        stats.bestFocusStreak = previousState.consecutiveFocusTime
      }
      
      stats.focusStartTime = null
    }
    
    // 更新平均专注度
    if (currentState.sessionDuration > 0) {
      stats.averageSessionFocus = (this.gameState.totalFocusTime / currentState.sessionDuration) * 100
    }
  }
  
  /**
   * 更新作物生长与专注度的关系
   */
  private updateCropGrowthWithFocus(focusState: FocusState): void {
    // 更新所有作物的专注度相关属性
    this.gameState.crops.forEach((crop) => {
      if (crop.isGrowing && !crop.isPaused) {
        // 更新作物的平均专注分数（移动平均）
        const weight = 0.1 // 新数据权重
        crop.averageFocusScore = crop.averageFocusScore * (1 - weight) + focusState.focusScore * weight
        
        // 如果专注度高，贡献专注时间
        if (focusState.isFocused) {
          const deltaTime = Date.now() - focusState.lastUpdateTime
          crop.focusTimeContributed += deltaTime
        }
      }
    })
  }
  
  /**
   * 设置姿态检测连接状态
   */
  setPoseDetectionConnectionStatus(isConnected: boolean): void {
    this.gameState.focusSystem.isConnectedToPoseDetection = isConnected
    
    this.emit('pose_detection_connection_changed', {
      isConnected,
      timestamp: Date.now()
    })
  }
  
  /**
   * 获取专注系统状态
   */
  getFocusSystemState(): Readonly<typeof this.gameState.focusSystem> {
    return Object.freeze(JSON.parse(JSON.stringify(this.gameState.focusSystem)))
  }
  
  /**
   * 获取专注统计信息
   */
  getFocusStats(): {
    currentFocusScore: number
    isFocused: boolean
    consecutiveFocusTime: number
    bestFocusStreak: number
    totalFocusTime: number
    totalFocusBreaks: number
    averageSessionFocus: number
    isConnectedToPoseDetection: boolean
    sessionDuration: number
  } {
    const focus = this.gameState.focusSystem
    return {
      currentFocusScore: focus.currentFocusState.focusScore,
      isFocused: focus.currentFocusState.isFocused,
      consecutiveFocusTime: focus.currentFocusState.consecutiveFocusTime,
      bestFocusStreak: focus.sessionStats.bestFocusStreak,
      totalFocusTime: this.gameState.totalFocusTime,
      totalFocusBreaks: focus.sessionStats.totalFocusBreaks,
      averageSessionFocus: focus.sessionStats.averageSessionFocus,
      isConnectedToPoseDetection: focus.isConnectedToPoseDetection,
      sessionDuration: focus.currentFocusState.sessionDuration
    }
  }
  
  /**
   * 重置专注系统状态
   */
  resetFocusSystem(): void {
    const now = Date.now()
    this.gameState.focusSystem = {
      currentFocusState: {
        focusScore: 0,
        isFocused: false,
        sessionDuration: 0,
        consecutiveFocusTime: 0,
        lastUpdateTime: now
      },
      sessionStats: {
        bestFocusStreak: 0,
        totalFocusBreaks: 0,
        averageSessionFocus: 0,
        focusStartTime: null
      },
      isConnectedToPoseDetection: false,
      lastPoseDataTime: 0
    }
    
    this.emit('focus_system_reset', { timestamp: now })
  }

  getGameStats() {
    return {
      totalPlayTime: this.gameState.gameTime.totalPlayTime,
      totalCropsHarvested: this.gameState.totalCropsHarvested,
      totalFocusTime: this.gameState.totalFocusTime,
      level: this.gameState.level,
      experience: this.gameState.experience,
      nextLevelExp: Math.floor(100 * Math.pow(1.5, this.gameState.level - 1)),
      cropsCount: this.gameState.crops.size,
      resourcesTotal: Object.values(this.gameState.resources).reduce((sum, val) => sum + val, 0)
    }
  }
  
  private startAutoSave(): void {
    if (this.autoSaveTimer) {
      clearInterval(this.autoSaveTimer)
    }
    
    if (this.gameState.settings.autoSave) {
      this.autoSaveTimer = window.setInterval(() => {
        this.saveGameState()
      }, this.gameState.settings.autoSaveInterval)
    }
  }
  
  destroy(): void {
    if (this.autoSaveTimer) {
      clearInterval(this.autoSaveTimer)
      this.autoSaveTimer = null
    }
    this.removeAllListeners()
  }
} 