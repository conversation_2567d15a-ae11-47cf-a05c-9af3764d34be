import { 
  CropInstance, 
  CropType, 
  <PERSON>ropStage, 
  <PERSON>ropQuality,
  CROP_CONFIGS,
  CropManager,
  FarmGrid,
  CropGrowthEvent,
  CropStats
} from '../types/crop'
import { 
  SelfDisciplineType,
  getCropDetectionConfig,
  BehaviorDetectionCriteria 
} from '../data/cropSpecifications'
import { 
  EnhancedGrowthCalculator,
  BehaviorDetectionResult,
  BehaviorSession,
  MockBehaviorDetector
} from '../services/EnhancedGrowthSystem'
import { 
  CropRewardSystem,
  HarvestResult,
  RewardBundle
} from '../systems/CropRewardSystem'
import { EventEmitter } from 'eventemitter3'

// 增强农场事件类型
export enum EnhancedFarmEvent {
  CROP_PLANTED = 'crop_planted',
  CROP_HARVESTED = 'crop_harvested',
  CROP_STAGE_CHANGED = 'crop_stage_changed',
  BEHAVIOR_DETECTED = 'behavior_detected',
  REWARD_EARNED = 'reward_earned',
  STREAK_ACHIEVED = 'streak_achieved',
  ACHIEVEMENT_UNLOCKED = 'achievement_unlocked',
  FARM_STATE_CHANGED = 'farm_state_changed'
}

// 增强农场状态接口
export interface EnhancedFarmState {
  grid: FarmGrid
  activeCrops: Map<string, CropInstance>
  recentSessions: BehaviorSession[]
  currentDetectedBehavior?: BehaviorDetectionResult
  streakCounters: Record<CropType, number>
  unlockedCropTypes: Set<CropType>
  userLevel: number
  totalExperience: number
  inventory: Map<string, number>
  farmStats: CropStats
}

// 会话配置接口
export interface SessionConfig {
  behaviorType: SelfDisciplineType
  minDuration: number
  targetFocusScore: number
  autoDetection: boolean
  notificationSettings: {
    enabled: boolean
    streakReminders: boolean
    harvestAlerts: boolean
  }
}

/**
 * 增强的农场管理器
 * 集成新作物类型、行为检测、智能生长系统和奖励机制
 */
export class EnhancedFarmManager extends EventEmitter implements CropManager {
  private farmState: EnhancedFarmState
  private growthCalculator: EnhancedGrowthCalculator
  private rewardSystem: CropRewardSystem
  private behaviorDetector: MockBehaviorDetector
  private updateInterval: NodeJS.Timeout | null = null
  private sessionConfig: SessionConfig
  private isActive: boolean = false
  
  constructor(
    gridWidth: number = 6,
    gridHeight: number = 4,
    sessionConfig?: Partial<SessionConfig>
  ) {
    super()
    
    this.farmState = {
      grid: {
        width: gridWidth,
        height: gridHeight,
        plots: Array(gridHeight).fill(null).map(() => Array(gridWidth).fill(null))
      },
      activeCrops: new Map(),
      recentSessions: [],
      streakCounters: {} as Record<CropType, number>,
      unlockedCropTypes: new Set([CropType.KNOWLEDGE_FLOWER, CropType.STRENGTH_TREE]), // 默认解锁基础作物
      userLevel: 1,
      totalExperience: 0,
      inventory: new Map(),
      farmStats: this.initializeFarmStats()
    }
    
    this.growthCalculator = new EnhancedGrowthCalculator()
    this.rewardSystem = new CropRewardSystem()
    this.behaviorDetector = new MockBehaviorDetector()
    
    this.sessionConfig = {
      behaviorType: SelfDisciplineType.LEARNING,
      minDuration: 10 * 60 * 1000, // 10分钟
      targetFocusScore: 70,
      autoDetection: true,
      notificationSettings: {
        enabled: true,
        streakReminders: true,
        harvestAlerts: true
      },
      ...sessionConfig
    }
    
    // 初始化连击计数器
    Object.values(CropType).forEach(type => {
      this.farmState.streakCounters[type] = 0
    })
  }
  
  // ================== 基础农场操作 ==================
  
  async plantCrop(type: CropType, gridX: number, gridY: number): Promise<CropInstance> {
    // 检查作物是否已解锁
    if (!this.farmState.unlockedCropTypes.has(type)) {
      throw new Error(`作物类型 ${type} 尚未解锁`)
    }
    
    // 检查位置是否有效
    if (gridX < 0 || gridX >= this.farmState.grid.width || 
        gridY < 0 || gridY >= this.farmState.grid.height) {
      throw new Error('无效的种植位置')
    }
    
    // 检查位置是否已被占用
    if (this.farmState.grid.plots[gridY][gridX] !== null) {
      throw new Error('该位置已被占用')
    }
    
    // 检查前置条件
    const config = CROP_CONFIGS[type]
    if (config.requirements?.minLevel && this.farmState.userLevel < config.requirements.minLevel) {
      throw new Error(`需要等级 ${config.requirements.minLevel} 才能种植此作物`)
    }
    
    if (config.requirements?.prerequisites) {
      const unmetPrereqs = config.requirements.prerequisites.filter(
        prereq => !this.farmState.unlockedCropTypes.has(prereq)
      )
      if (unmetPrereqs.length > 0) {
        throw new Error(`需要先解锁前置作物: ${unmetPrereqs.join(', ')}`)
      }
    }
    
    // 创建作物实例
    const cropId = this.generateCropId()
    const crop: CropInstance = {
      id: cropId,
      type,
      stage: CropStage.SEED,
      quality: CropQuality.COMMON,
      plantedAt: Date.now(),
      stageStartTime: Date.now(),
      totalGrowthTime: 0,
      focusTimeContributed: 0,
      averageFocusScore: 0,
      position: {
        x: gridX * 64, // 假设每个格子64像素
        y: gridY * 64,
        gridX,
        gridY
      },
      isGrowing: true,
      isPaused: false,
      harvestable: false,
      metadata: {
        sessionsContributed: 0,
        bestFocusStreak: 0,
        growthBoosts: 0
      }
    }
    
    // 放置到农场网格
    this.farmState.grid.plots[gridY][gridX] = crop
    this.farmState.activeCrops.set(cropId, crop)
    
    // 添加到增长计算器
    this.growthCalculator.addCrop(crop)
    
    // 更新统计
    this.farmState.farmStats.totalPlanted++
    this.farmState.farmStats.typeStats[type].planted++
    
    // 发出事件
    this.emit(EnhancedFarmEvent.CROP_PLANTED, { crop, position: { gridX, gridY } })
    
    console.log(`🌱 种植作物: ${config.name} 在位置 (${gridX}, ${gridY})`)
    
    return crop
  }
  
  async harvestCrop(cropId: string): Promise<{ rewards: HarvestResult; quality: CropQuality }> {
    const crop = this.farmState.activeCrops.get(cropId)
    if (!crop) {
      throw new Error('作物不存在')
    }
    
    if (crop.stage !== CropStage.READY_TO_HARVEST) {
      throw new Error('作物尚未成熟，无法收获')
    }
    
    // 计算最终品质
    const behaviorStats = this.growthCalculator.getBehaviorStats(cropId)
    crop.quality = this.calculateFinalQuality(crop, behaviorStats.averageQuality)
    
    // 计算奖励
    const rewards = this.rewardSystem.calculateHarvestRewards(crop, {
      averageFocusScore: behaviorStats.averageQuality,
      sessionsContributed: behaviorStats.totalSessions,
      behaviorConsistency: this.calculateBehaviorConsistency(cropId),
      totalGrowthTime: crop.totalGrowthTime,
      streakCount: behaviorStats.currentStreak
    })
    
    // 更新库存
    this.rewardSystem.updateInventory(rewards)
    
    // 更新经验和等级
    this.farmState.totalExperience += rewards.totalExperience
    this.checkLevelUp()
    
    // 检查新作物解锁
    this.checkCropUnlocks()
    
    // 从农场移除
    this.farmState.grid.plots[crop.position.gridY][crop.position.gridX] = null
    this.farmState.activeCrops.delete(cropId)
    
    // 清理数据
    this.growthCalculator.clearFocusHistory(cropId)
    
    // 更新统计
    this.updateHarvestStats(crop, rewards)
    
    // 发出事件
    this.emit(EnhancedFarmEvent.CROP_HARVESTED, { crop, rewards })
    this.emit(EnhancedFarmEvent.REWARD_EARNED, rewards)
    
    if (rewards.achievements.length > 0) {
      this.emit(EnhancedFarmEvent.ACHIEVEMENT_UNLOCKED, rewards.achievements)
    }
    
    console.log(`🌾 收获作物: ${crop.id}, 品质: ${crop.quality}, 总经验: ${rewards.totalExperience}`)
    
    return { rewards, quality: crop.quality }
  }
  
  async removeCrop(cropId: string): Promise<void> {
    const crop = this.farmState.activeCrops.get(cropId)
    if (!crop) {
      throw new Error('作物不存在')
    }
    
    // 从网格移除
    this.farmState.grid.plots[crop.position.gridY][crop.position.gridX] = null
    this.farmState.activeCrops.delete(cropId)
    
    // 清理数据
    this.growthCalculator.clearFocusHistory(cropId)
    
    console.log(`🗑️ 移除作物: ${cropId}`)
  }
  
  // ================== 生长管理 ==================
  
  updateGrowth(deltaTime: number, focusScore: number): void {
    if (!this.isActive) return
    
    const now = Date.now()
    
    this.farmState.activeCrops.forEach(crop => {
      if (!crop.isGrowing || crop.isPaused) return
      
      // 检测当前行为
      const currentBehavior = this.detectCurrentBehavior(crop.type, focusScore, deltaTime)
      
      // 计算基于行为的专注度
      const behaviorBasedFocus = this.growthCalculator.calculateBehaviorBasedFocus(crop, currentBehavior)
      
      // 计算生长进度
      const growthResult = this.growthCalculator.calculateProgress(crop, behaviorBasedFocus, deltaTime)
      
      // 更新作物状态
      crop.totalGrowthTime += deltaTime
      crop.focusTimeContributed += deltaTime * (behaviorBasedFocus / 100)
      
      // 检查阶段变化
      if (growthResult.shouldAdvanceStage && growthResult.newStage) {
        this.advanceCropStage(crop, growthResult.newStage)
      }
      
      // 更新平均专注度
      const totalFocusTime = crop.focusTimeContributed
      if (totalFocusTime > 0) {
        crop.averageFocusScore = (crop.averageFocusScore * crop.metadata.sessionsContributed + behaviorBasedFocus) 
                                / (crop.metadata.sessionsContributed + 1)
      }
    })
  }
  
  pauseGrowth(cropId?: string): void {
    if (cropId) {
      const crop = this.farmState.activeCrops.get(cropId)
      if (crop) {
        crop.isPaused = true
        console.log(`⏸️ 暂停作物生长: ${cropId}`)
      }
    } else {
      this.farmState.activeCrops.forEach(crop => {
        crop.isPaused = true
      })
      console.log('⏸️ 暂停所有作物生长')
    }
  }
  
  resumeGrowth(cropId?: string): void {
    if (cropId) {
      const crop = this.farmState.activeCrops.get(cropId)
      if (crop) {
        crop.isPaused = false
        console.log(`▶️ 恢复作物生长: ${cropId}`)
      }
    } else {
      this.farmState.activeCrops.forEach(crop => {
        crop.isPaused = false
      })
      console.log('▶️ 恢复所有作物生长')
    }
  }
  
  boostGrowth(cropId: string, multiplier: number): void {
    const crop = this.farmState.activeCrops.get(cropId)
    if (crop) {
      crop.metadata.growthBoosts++
      // 这里可以实现临时增长加速逻辑
      console.log(`🚀 加速作物生长: ${cropId}, 倍数: ${multiplier}x`)
    }
  }
  
  // ================== 查询方法 ==================
  
  getCrop(cropId: string): CropInstance | null {
    return this.farmState.activeCrops.get(cropId) || null
  }
  
  getCropsAtStage(stage: CropStage): CropInstance[] {
    return Array.from(this.farmState.activeCrops.values()).filter(crop => crop.stage === stage)
  }
  
  getHarvestableCrops(): CropInstance[] {
    return this.getCropsAtStage(CropStage.READY_TO_HARVEST)
  }
  
  getFarmGrid(): FarmGrid {
    return this.farmState.grid
  }
  
  getStats(): CropStats {
    return { ...this.farmState.farmStats }
  }
  
  getCropProgress(cropId: string): number {
    const crop = this.farmState.activeCrops.get(cropId)
    if (!crop) return 0
    
    const config = CROP_CONFIGS[crop.type]
    const stageIndex = config.stages.findIndex(s => s.stage === crop.stage)
    const totalStages = config.stages.length
    
    if (stageIndex === -1) return 0
    
    // 计算当前阶段内的进度
    const currentStage = config.stages[stageIndex]
    const stageElapsed = Date.now() - crop.stageStartTime
    const stageProgress = Math.min(stageElapsed / currentStage.duration, 1)
    
    // 总进度 = (已完成阶段 + 当前阶段进度) / 总阶段数
    return (stageIndex + stageProgress) / totalStages
  }
  
  getEstimatedTimeToHarvest(cropId: string): number {
    const crop = this.farmState.activeCrops.get(cropId)
    if (!crop) return 0
    
    return this.growthCalculator.estimateTimeToHarvest(crop, crop.averageFocusScore)
  }
  
  // ================== 行为检测和会话管理 ==================
  
  startBehaviorSession(behaviorType: SelfDisciplineType): string {
    const sessionId = this.generateSessionId()
    
    this.sessionConfig.behaviorType = behaviorType
    this.farmState.currentDetectedBehavior = undefined
    
    console.log(`🎯 开始行为会话: ${behaviorType}, ID: ${sessionId}`)
    
    return sessionId
  }
  
  endBehaviorSession(sessionId: string, focusData: { score: number; duration: number; timestamp: number }[]): void {
    if (focusData.length === 0) return
    
    const totalDuration = focusData.reduce((sum, data) => sum + data.duration, 0)
    const averageFocusScore = focusData.reduce((sum, data) => sum + data.score, 0) / focusData.length
    
    // 创建会话记录
    const session: BehaviorSession = {
      id: sessionId,
      cropId: '', // 会根据相关作物分配
      behaviorType: this.sessionConfig.behaviorType,
      startTime: focusData[0].timestamp,
      endTime: focusData[focusData.length - 1].timestamp,
      focusScores: focusData.map(d => d.score),
      averageFocusScore,
      qualityMetrics: {
        consistency: this.calculateConsistency(focusData),
        intensity: averageFocusScore / 100,
        duration: totalDuration / (30 * 60 * 1000) // 标准化到30分钟
      },
      isValid: this.validateSessionQuality(totalDuration, averageFocusScore),
      bonusFactors: {
        timeOfDay: this.getTimeOfDayBonus(),
        streak: 0, // 会在后续计算
        weather: this.getWeatherBonus()
      }
    }
    
    // 为相关作物分配会话
    this.assignSessionToCrops(session)
    
    // 添加到最近会话记录
    this.farmState.recentSessions.push(session)
    if (this.farmState.recentSessions.length > 20) {
      this.farmState.recentSessions.shift()
    }
    
    console.log(`✅ 结束行为会话: ${sessionId}, 有效: ${session.isValid}, 平均专注度: ${averageFocusScore.toFixed(1)}`)
  }
  
  // ================== 事件处理 ==================
  
  onGrowthEvent(callback: (event: CropGrowthEvent) => void): void {
    this.on(EnhancedFarmEvent.CROP_STAGE_CHANGED, callback)
  }
  
  offGrowthEvent(callback: (event: CropGrowthEvent) => void): void {
    this.off(EnhancedFarmEvent.CROP_STAGE_CHANGED, callback)
  }
  
  // ================== 系统控制 ==================
  
  start(): void {
    if (this.isActive) return
    
    this.isActive = true
    this.updateInterval = setInterval(() => {
      this.updateGrowth(1000, 75) // 每秒更新，默认75分专注度
    }, 1000)
    
    console.log('🚀 增强农场系统启动')
  }
  
  stop(): void {
    if (!this.isActive) return
    
    this.isActive = false
    if (this.updateInterval) {
      clearInterval(this.updateInterval)
      this.updateInterval = null
    }
    
    console.log('⏹️ 增强农场系统停止')
  }
  
  // ================== 私有辅助方法 ==================
  
  private detectCurrentBehavior(cropType: CropType, focusScore: number, duration: number): BehaviorDetectionResult | undefined {
    const config = getCropDetectionConfig(cropType)
    const behaviorType = config.behaviorType
    
    const focusData = [{
      score: focusScore,
      duration,
      timestamp: Date.now()
    }]
    
    return this.behaviorDetector.detectBehavior(behaviorType, focusData)
  }
  
  private advanceCropStage(crop: CropInstance, newStage: CropStage): void {
    const oldStage = crop.stage
    crop.stage = newStage
    crop.stageStartTime = Date.now()
    
    // 检查是否可收获
    if (newStage === CropStage.READY_TO_HARVEST) {
      crop.harvestable = true
      crop.isGrowing = false
      
      if (this.sessionConfig.notificationSettings.harvestAlerts) {
        console.log(`🌟 作物已成熟，可以收获了: ${crop.id}`)
      }
    }
    
    // 发出阶段变化事件
    this.emit(EnhancedFarmEvent.CROP_STAGE_CHANGED, {
      cropId: crop.id,
      type: 'stage_change',
      timestamp: Date.now(),
      data: {
        fromStage: oldStage,
        toStage: newStage
      }
    })
  }
  
  private calculateFinalQuality(crop: CropInstance, averageFocusScore: number): CropQuality {
    // 基于平均专注度计算品质
    if (averageFocusScore >= 95) return CropQuality.LEGENDARY
    if (averageFocusScore >= 85) return CropQuality.EPIC
    if (averageFocusScore >= 75) return CropQuality.RARE
    if (averageFocusScore >= 65) return CropQuality.UNCOMMON
    return CropQuality.COMMON
  }
  
  private calculateBehaviorConsistency(cropId: string): number {
    const stats = this.growthCalculator.getBehaviorStats(cropId)
    const config = getCropDetectionConfig(this.farmState.activeCrops.get(cropId)!.type)
    
    const matchingBehaviors = stats.behaviorTypes[config.behaviorType] || 0
    return stats.totalSessions > 0 ? matchingBehaviors / stats.totalSessions : 0
  }
  
  private checkLevelUp(): void {
    const newLevel = Math.floor(this.farmState.totalExperience / 1000) + 1 // 每1000经验一级
    
    if (newLevel > this.farmState.userLevel) {
      this.farmState.userLevel = newLevel
      console.log(`🎉 等级提升! 当前等级: ${newLevel}`)
      
      // 检查新作物解锁
      this.checkCropUnlocks()
    }
  }
  
  private checkCropUnlocks(): void {
    const level = this.farmState.userLevel
    const newUnlocks: CropType[] = []
    
    Object.entries(CROP_CONFIGS).forEach(([type, config]) => {
      const cropType = type as CropType
      if (!this.farmState.unlockedCropTypes.has(cropType) && 
          (!config.requirements?.minLevel || level >= config.requirements.minLevel)) {
        
        // 检查前置条件
        if (!config.requirements?.prerequisites || 
            config.requirements.prerequisites.every(prereq => this.farmState.unlockedCropTypes.has(prereq))) {
          this.farmState.unlockedCropTypes.add(cropType)
          newUnlocks.push(cropType)
        }
      }
    })
    
    if (newUnlocks.length > 0) {
      console.log(`🔓 解锁新作物: ${newUnlocks.join(', ')}`)
    }
  }
  
  private updateHarvestStats(crop: CropInstance, rewards: HarvestResult): void {
    this.farmState.farmStats.totalHarvested++
    this.farmState.farmStats.totalGrowthTime += crop.totalGrowthTime
    this.farmState.farmStats.typeStats[crop.type].harvested++
    this.farmState.farmStats.typeStats[crop.type].averageGrowthTime = 
      (this.farmState.farmStats.typeStats[crop.type].averageGrowthTime + crop.totalGrowthTime) / 2
    
    // 更新最佳作物记录
    if (crop.quality > this.farmState.farmStats.bestCrop.quality || 
        (crop.quality === this.farmState.farmStats.bestCrop.quality && 
         crop.totalGrowthTime < this.farmState.farmStats.bestCrop.growthTime)) {
      this.farmState.farmStats.bestCrop = {
        type: crop.type,
        quality: crop.quality,
        growthTime: crop.totalGrowthTime
      }
    }
  }
  
  private assignSessionToCrops(session: BehaviorSession): void {
    const relevantCrops = Array.from(this.farmState.activeCrops.values()).filter(crop => {
      const config = getCropDetectionConfig(crop.type)
      return config.behaviorType === session.behaviorType
    })
    
    relevantCrops.forEach(crop => {
      const sessionWithCrop = { ...session, cropId: crop.id }
      this.growthCalculator.recordBehaviorSession(sessionWithCrop)
      crop.metadata.sessionsContributed++
    })
  }
  
  private validateSessionQuality(duration: number, averageScore: number): boolean {
    return duration >= this.sessionConfig.minDuration && 
           averageScore >= this.sessionConfig.targetFocusScore
  }
  
  private calculateConsistency(focusData: { score: number; duration: number; timestamp: number }[]): number {
    if (focusData.length < 2) return 1.0
    
    const scores = focusData.map(d => d.score)
    const average = scores.reduce((sum, score) => sum + score, 0) / scores.length
    const variance = scores.reduce((sum, score) => sum + Math.pow(score - average, 2), 0) / scores.length
    const standardDeviation = Math.sqrt(variance)
    
    return Math.max(0, 1 - standardDeviation / 100)
  }
  
  private getTimeOfDayBonus(): number {
    const hour = new Date().getHours()
    if ((hour >= 6 && hour <= 10) || (hour >= 19 && hour <= 22)) return 0.1
    return 0
  }
  
  private getWeatherBonus(): number {
    // 这里可以集成天气系统
    return 0.05 // 默认小幅奖励
  }
  
  private initializeFarmStats(): CropStats {
    const typeStats = {} as any
    Object.values(CropType).forEach(type => {
      typeStats[type] = {
        planted: 0,
        harvested: 0,
        averageGrowthTime: 0
      }
    })
    
    return {
      totalPlanted: 0,
      totalHarvested: 0,
      totalGrowthTime: 0,
      averageQuality: CropQuality.COMMON,
      bestCrop: {
        type: CropType.KNOWLEDGE_FLOWER,
        quality: CropQuality.COMMON,
        growthTime: Infinity
      },
      typeStats
    }
  }
  
  private generateCropId(): string {
    return `crop_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }
  
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }
  
  // ================== 公共API方法 ==================
  
  /**
   * 获取农场状态
   */
  getFarmState(): EnhancedFarmState {
    return { ...this.farmState }
  }
  
  /**
   * 设置会话配置
   */
  setSessionConfig(config: Partial<SessionConfig>): void {
    this.sessionConfig = { ...this.sessionConfig, ...config }
  }
  
  /**
   * 获取解锁的作物类型
   */
  getUnlockedCropTypes(): CropType[] {
    return Array.from(this.farmState.unlockedCropTypes)
  }
  
  /**
   * 强制解锁作物类型（调试用）
   */
  unlockCropType(type: CropType): void {
    this.farmState.unlockedCropTypes.add(type)
    console.log(`🔓 强制解锁作物: ${type}`)
  }
  
  /**
   * 导出农场数据
   */
  exportFarmData(): any {
    return {
      farmState: this.farmState,
      sessionConfig: this.sessionConfig,
      timestamp: Date.now()
    }
  }
  
  /**
   * 导入农场数据
   */
  importFarmData(data: any): void {
    if (data.farmState) {
      this.farmState = { ...this.farmState, ...data.farmState }
    }
    if (data.sessionConfig) {
      this.sessionConfig = { ...this.sessionConfig, ...data.sessionConfig }
    }
    
    console.log('📥 农场数据导入完成')
  }
}

// 导出单例实例
export const enhancedFarmManager = new EnhancedFarmManager() 