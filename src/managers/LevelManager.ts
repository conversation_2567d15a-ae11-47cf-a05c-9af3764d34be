import { ExperienceSystem } from '../utils/ExperienceSystem';
import {
  UserExperience,
  Level,
  ExperienceSource,
  AchievementNotification
} from '../types/achievements';

export interface LevelUpResult {
  success: boolean;
  oldLevel: number;
  newLevel: number;
  levelInfo: Level;
  bonusExperience: number;
  rewards?: any[];
  notification: AchievementNotification;
}

export interface LevelProgressInfo {
  currentLevel: number;
  levelName: string;
  currentExp: number;
  expToNext: number;
  totalExpForLevel: number;
  progressPercent: number;
  nextLevelInfo?: Level;
}

export class LevelManager {
  private experienceSystem: ExperienceSystem;
  private levelUpCallbacks: Array<(result: LevelUpResult) => void> = [];

  constructor(experienceSystem: ExperienceSystem) {
    this.experienceSystem = experienceSystem;
  }

  /**
   * 注册等级提升回调
   */
  onLevelUp(callback: (result: LevelUpResult) => void): void {
    this.levelUpCallbacks.push(callback);
  }

  /**
   * 移除等级提升回调
   */
  removeLevelUpCallback(callback: (result: LevelUpResult) => void): void {
    const index = this.levelUpCallbacks.indexOf(callback);
    if (index > -1) {
      this.levelUpCallbacks.splice(index, 1);
    }
  }

  /**
   * 处理经验值增加并检查等级提升
   */
  async processExperienceGain(
    source: ExperienceSource,
    description: string,
    options: {
      multiplier?: number;
      customAmount?: number;
      achievementId?: string;
      sessionId?: string;
    } = {}
  ): Promise<{
    experienceGained: number;
    levelUpResult?: LevelUpResult;
  }> {
    // 获取当前经验值状态
    const currentExperience = this.experienceSystem.calculateUserExperience();
    const previousTotalExp = currentExperience.totalExperience;

    // 添加经验值
    const expRecord = this.experienceSystem.addExperience(source, description, options);
    
    // 获取更新后的经验值状态
    const newExperience = this.experienceSystem.calculateUserExperience();
    const newTotalExp = newExperience.totalExperience;

    // 检查是否升级
    const levelUpCheck = this.experienceSystem.checkLevelUp(previousTotalExp, newTotalExp);
    
    let levelUpResult: LevelUpResult | undefined;

    if (levelUpCheck.leveledUp) {
      levelUpResult = await this.handleLevelUp(
        levelUpCheck.oldLevel,
        levelUpCheck.newLevel,
        levelUpCheck.bonusExperience || 0
      );

      // 触发回调
      this.levelUpCallbacks.forEach(callback => callback(levelUpResult!));
    }

    return {
      experienceGained: expRecord.amount,
      levelUpResult
    };
  }

  /**
   * 处理等级提升
   */
  private async handleLevelUp(
    oldLevel: number,
    newLevel: number,
    bonusExperience: number
  ): Promise<LevelUpResult> {
    const levelInfo = this.experienceSystem.getLevelInfo(newLevel);
    
    if (!levelInfo) {
      throw new Error(`Level ${newLevel} not found`);
    }

    // 生成等级提升通知
    const notification = this.createLevelUpNotification(oldLevel, newLevel, levelInfo);

    // 检查等级奖励
    const rewards = await this.processLevelRewards(levelInfo);

    const result: LevelUpResult = {
      success: true,
      oldLevel,
      newLevel,
      levelInfo,
      bonusExperience,
      rewards,
      notification
    };

    return result;
  }

  /**
   * 处理等级奖励
   */
  private async processLevelRewards(levelInfo: Level): Promise<any[]> {
    const rewards: any[] = [];

    if (levelInfo.rewards) {
      for (const reward of levelInfo.rewards) {
        switch (reward.type) {
          case 'experience_multiplier':
            // 经验值倍数奖励
            rewards.push({
              type: 'experience_multiplier',
              value: reward.value,
              description: `经验值获取倍数提升至 ${reward.value}x`
            });
            break;

          case 'new_feature':
            // 解锁新功能
            rewards.push({
              type: 'feature_unlock',
              value: reward.value,
              description: `解锁新功能: ${reward.value}`
            });
            break;

          case 'title':
            // 称号奖励
            rewards.push({
              type: 'title',
              value: reward.value,
              description: `获得称号: ${reward.value}`
            });
            break;

          case 'badge':
            // 徽章奖励
            rewards.push({
              type: 'badge',
              value: reward.value,
              description: `获得徽章: ${reward.value}`
            });
            break;

          default:
            rewards.push(reward);
        }
      }
    }

    return rewards;
  }

  /**
   * 创建等级提升通知
   */
  private createLevelUpNotification(
    oldLevel: number,
    newLevel: number,
    levelInfo: Level
  ): AchievementNotification {
    return {
      id: this.generateId(),
      type: 'level_up',
      title: '等级提升！',
      message: `恭喜你升级到 ${levelInfo.name}（等级 ${newLevel}）！`,
      level: levelInfo,
      createdAt: new Date(),
      isRead: false
    };
  }

  /**
   * 获取当前等级进度信息
   */
  getLevelProgress(): LevelProgressInfo {
    const userExp = this.experienceSystem.calculateUserExperience();
    const currentLevelInfo = this.experienceSystem.getLevelInfo(userExp.currentLevel);
    const nextLevelInfo = this.experienceSystem.getLevelInfo(userExp.currentLevel + 1);

    if (!currentLevelInfo) {
      throw new Error(`Current level ${userExp.currentLevel} not found`);
    }

    // 计算当前等级需要的总经验值
    const currentLevelTotalExp = nextLevelInfo 
      ? nextLevelInfo.requiredExperience - currentLevelInfo.requiredExperience
      : userExp.currentExperience;

    // 计算进度百分比
    const progressPercent = currentLevelTotalExp > 0 
      ? Math.min(100, (userExp.currentExperience / currentLevelTotalExp) * 100)
      : 100;

    return {
      currentLevel: userExp.currentLevel,
      levelName: currentLevelInfo.name,
      currentExp: userExp.currentExperience,
      expToNext: userExp.experienceToNextLevel,
      totalExpForLevel: currentLevelTotalExp,
      progressPercent,
      nextLevelInfo
    };
  }

  /**
   * 获取等级排行榜信息
   */
  getLevelRanking(): {
    currentLevel: number;
    levelName: string;
    totalExperience: number;
    rank: string;
    nextMilestone?: {
      level: number;
      name: string;
      expRequired: number;
    };
  } {
    const userExp = this.experienceSystem.calculateUserExperience();
    const currentLevelInfo = this.experienceSystem.getLevelInfo(userExp.currentLevel);
    
    if (!currentLevelInfo) {
      throw new Error(`Current level ${userExp.currentLevel} not found`);
    }

    // 确定排名等级
    let rank = '新手';
    if (userExp.currentLevel >= 8) rank = '传奇';
    else if (userExp.currentLevel >= 6) rank = '大师';
    else if (userExp.currentLevel >= 4) rank = '专家';
    else if (userExp.currentLevel >= 2) rank = '进阶';

    // 查找下一个里程碑等级
    const milestones = [3, 5, 7, 10];
    const nextMilestone = milestones.find(level => level > userExp.currentLevel);
    let nextMilestoneInfo;

    if (nextMilestone) {
      const milestoneLevel = this.experienceSystem.getLevelInfo(nextMilestone);
      if (milestoneLevel) {
        nextMilestoneInfo = {
          level: nextMilestone,
          name: milestoneLevel.name,
          expRequired: milestoneLevel.requiredExperience - userExp.totalExperience
        };
      }
    }

    return {
      currentLevel: userExp.currentLevel,
      levelName: currentLevelInfo.name,
      totalExperience: userExp.totalExperience,
      rank,
      nextMilestone: nextMilestoneInfo
    };
  }

  /**
   * 预测升级所需时间
   */
  predictLevelUpTime(averageDailyExp?: number): {
    daysToNextLevel: number;
    estimatedDate: Date;
    recommendation: string;
  } {
    const userExp = this.experienceSystem.calculateUserExperience();
    const stats = this.experienceSystem.getExperienceStats();
    
    // 使用提供的平均值或计算的平均值
    const dailyExp = averageDailyExp || stats.averageDailyExperience || 1;
    
    const daysToNextLevel = Math.ceil(userExp.experienceToNextLevel / dailyExp);
    const estimatedDate = new Date();
    estimatedDate.setDate(estimatedDate.getDate() + daysToNextLevel);

    let recommendation = '';
    if (daysToNextLevel <= 3) {
      recommendation = '即将升级！保持当前节奏';
    } else if (daysToNextLevel <= 7) {
      recommendation = '一周内可以升级，加油！';
    } else if (daysToNextLevel <= 14) {
      recommendation = '建议增加日常任务完成度';
    } else {
      recommendation = '尝试完成更多成就来加速升级';
    }

    return {
      daysToNextLevel,
      estimatedDate,
      recommendation
    };
  }

  /**
   * 获取等级历史
   */
  getLevelHistory(): Array<{
    level: number;
    levelName: string;
    achievedAt: Date;
    daysToAchieve: number;
  }> {
    const history: Array<{
      level: number;
      levelName: string;
      achievedAt: Date;
      daysToAchieve: number;
    }> = [];

    const expRecords = this.experienceSystem.getExperienceRecords();
    if (expRecords.length === 0) return history;

    let currentExp = 0;
    let currentLevel = 1;
    const startDate = expRecords[expRecords.length - 1].earnedAt;

    // 按时间顺序处理记录
    const sortedRecords = expRecords.sort((a, b) => a.earnedAt.getTime() - b.earnedAt.getTime());

    for (const record of sortedRecords) {
      currentExp += record.amount;
      const newLevel = this.experienceSystem.calculateLevel(currentExp);

      if (newLevel > currentLevel) {
        const levelInfo = this.experienceSystem.getLevelInfo(newLevel);
        if (levelInfo) {
          const daysToAchieve = Math.ceil(
            (record.earnedAt.getTime() - startDate.getTime()) / (24 * 60 * 60 * 1000)
          );

          history.push({
            level: newLevel,
            levelName: levelInfo.name,
            achievedAt: record.earnedAt,
            daysToAchieve
          });
        }
        currentLevel = newLevel;
      }
    }

    return history;
  }

  /**
   * 验证等级系统完整性
   */
  validateLevelSystem(): {
    isValid: boolean;
    errors: string[];
    warnings: string[];
  } {
    const errors: string[] = [];
    const warnings: string[] = [];
    const levels = this.experienceSystem.getAllLevels();

    // 检查等级连续性
    for (let i = 0; i < levels.length - 1; i++) {
      const current = levels[i];
      const next = levels[i + 1];

      if (next.level !== current.level + 1) {
        errors.push(`Level gap found: Level ${current.level} to ${next.level}`);
      }

      if (next.requiredExperience <= current.requiredExperience) {
        errors.push(`Invalid experience progression: Level ${next.level} requires ${next.requiredExperience} but level ${current.level} requires ${current.requiredExperience}`);
      }
    }

    // 检查等级名称唯一性
    const levelNames = levels.map(l => l.name);
    const uniqueNames = new Set(levelNames);
    if (levelNames.length !== uniqueNames.size) {
      errors.push('Duplicate level names found');
    }

    // 检查经验值增长率
    for (let i = 1; i < levels.length; i++) {
      const current = levels[i];
      const previous = levels[i - 1];
      const growth = current.requiredExperience - previous.requiredExperience;
      
      if (i > 1) {
        const previousGrowth = previous.requiredExperience - levels[i - 2].requiredExperience;
        const growthRatio = growth / previousGrowth;
        
        if (growthRatio > 3) {
          warnings.push(`Large experience jump at level ${current.level}: ${growthRatio.toFixed(2)}x increase`);
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * 生成唯一ID
   */
  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }
} 