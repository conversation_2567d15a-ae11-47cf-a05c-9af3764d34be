import { EventEmitter } from 'eventemitter3'
import { LootboxItem, ItemRarity, ItemCategory, ItemType, LootboxResult, LootboxType } from '../types/lootbox'
import { AgriculturalItem, GrowthStage } from '../types/agriculture'
import { AGRICULTURAL_ITEMS } from '../data/agriculturalItems'
import { ALL_LOOTBOX_ITEMS } from '../data/lootboxItems'
import { LootboxGenerator } from '../utils/lootboxGenerator'
import { FocusTokenManager } from './FocusTokenManager'

// 统一物品系统接口
export interface UnifiedItem {
  // 基础属性
  id: string
  name: string
  description: string
  category: ItemCategory
  type: ItemType
  rarity: ItemRarity
  icon: string
  value: number
  
  // 堆叠和交易属性
  stackable: boolean
  tradeable: boolean
  synthesizable: boolean
  
  // 数量和位置
  quantity: number
  slotId?: string
  position?: { x: number; y: number }
  
  // 扩展属性（根据类型动态使用）
  metadata: {
    // 农产品特有
    yieldMultiplier?: number
    growthSpeed?: number
    qualityBonus?: number
    
    // 工业品特有
    productionSpeed?: number
    efficiency?: number
    capacity?: number
    
    // 期货相关
    futuresPrice?: number
    priceVolatility?: number
    marketDemand?: number
    
    // 特殊效果
    effects?: string[]
    duration?: number
    synthesisRecipes?: string[]
  }
  
  // 状态信息
  status: {
    isPlanted?: boolean
    isGrowing?: boolean
    isReady?: boolean
    plantedTime?: number
    readyTime?: number
    stage?: GrowthStage
  }
}

// 物品转换结果
export interface ItemConversionResult {
  success: boolean
  item?: UnifiedItem
  error?: string
}

/**
 * 统一物品管理器
 * 负责整合农产品道具系统和期货盲盒系统
 */
export class UnifiedItemManager extends EventEmitter {
  private items: Map<string, UnifiedItem> = new Map()
  private categories: Map<ItemCategory, UnifiedItem[]> = new Map()
  private focusTokenManager: FocusTokenManager

  constructor() {
    super()
    this.focusTokenManager = FocusTokenManager.getInstance()
    this.initializeCategories()
  }

  // 初始化分类系统
  private initializeCategories(): void {
    Object.values(ItemCategory).forEach(category => {
      this.categories.set(category, [])
    })
  }

  // ==================== 物品转换方法 ====================

  /**
   * 将盲盒物品转换为统一物品格式
   */
  convertLootboxItem(lootboxItem: LootboxItem, quantity: number = 1): UnifiedItem {
    return {
      id: this.generateUniqueId(lootboxItem.id),
      name: lootboxItem.name,
      description: lootboxItem.description,
      category: lootboxItem.category,
      type: lootboxItem.type,
      rarity: lootboxItem.rarity,
      icon: lootboxItem.icon,
      value: lootboxItem.value,
      stackable: lootboxItem.stackable,
      tradeable: lootboxItem.tradeable,
      synthesizable: lootboxItem.synthesizable,
      quantity,
      metadata: { ...lootboxItem.metadata },
      status: {}
    }
  }

  /**
   * 将农产品道具转换为统一物品格式
   */
  convertAgriculturalItem(agriculturalItem: AgriculturalItem): UnifiedItem {
    return {
      id: agriculturalItem.id,
      name: agriculturalItem.name,
      description: agriculturalItem.description,
      category: agriculturalItem.category,
      type: this.mapAgriculturalToItemType(agriculturalItem),
      rarity: agriculturalItem.rarity,
      icon: this.getAgriculturalIcon(agriculturalItem),
      value: agriculturalItem.value.basePrice,
      stackable: true,
      tradeable: true,
      synthesizable: true,
      quantity: 1,
      metadata: {
        yieldMultiplier: agriculturalItem.production.currentDaily / agriculturalItem.production.minDaily,
        growthSpeed: 24 / agriculturalItem.growth.growthTime,
        qualityBonus: agriculturalItem.quality,
        futuresPrice: agriculturalItem.value.currentPrice,
        priceVolatility: agriculturalItem.value.marketDemand * 0.1,
        marketDemand: agriculturalItem.value.marketDemand,
        effects: agriculturalItem.special?.isHybrid ? ['hybrid_bonus'] : []
      },
      status: {
        isPlanted: agriculturalItem.location?.farmSlotId !== undefined,
        isGrowing: agriculturalItem.growth.currentStage !== GrowthStage.SEED && !agriculturalItem.growth.isReady,
        isReady: agriculturalItem.growth.isReady,
        plantedTime: agriculturalItem.growth.plantedTime,
        stage: agriculturalItem.growth.currentStage
      },
      position: agriculturalItem.location ? { x: agriculturalItem.location.x || 0, y: agriculturalItem.location.y || 0 } : undefined
    }
  }

  /**
   * 将统一物品转换为农产品道具（用于种植系统）
   */
  convertToAgriculturalItem(unifiedItem: UnifiedItem): ItemConversionResult {
    if (unifiedItem.category !== ItemCategory.AGRICULTURAL) {
      return { success: false, error: '非农产品类物品无法转换为农产品道具' }
    }

    try {
      // 从农产品数据库中查找对应的配置
      const baseConfig = this.findAgriculturalConfig(unifiedItem.name, unifiedItem.rarity)
      
      if (!baseConfig) {
        return { success: false, error: '找不到对应的农产品配置' }
      }

      const agriculturalItem: AgriculturalItem = {
        id: unifiedItem.id,
        name: unifiedItem.name,
        nameEn: this.generateEnglishName(unifiedItem.name),
        description: unifiedItem.description,
        rarity: unifiedItem.rarity,
        category: unifiedItem.category,
        variety: this.extractVariety(unifiedItem.name),
        level: 1,
        quality: unifiedItem.metadata.qualityBonus || Math.floor(Math.random() * 100) + 1,
        
        production: {
          minDaily: baseConfig.production[unifiedItem.rarity].minDaily,
          maxDaily: baseConfig.production[unifiedItem.rarity].maxDaily,
          currentDaily: Math.floor(
            Math.random() * (baseConfig.production[unifiedItem.rarity].maxDaily - baseConfig.production[unifiedItem.rarity].minDaily + 1)
          ) + baseConfig.production[unifiedItem.rarity].minDaily
        },
        
        value: {
          basePrice: unifiedItem.value,
          currentPrice: unifiedItem.metadata.futuresPrice || unifiedItem.value,
          marketDemand: unifiedItem.metadata.marketDemand || 1.0,
          priceHistory: []
        },
        
        growth: {
          growthTime: baseConfig.production[unifiedItem.rarity].growthTime,
          currentStage: unifiedItem.status.stage || GrowthStage.SEED,
          isReady: unifiedItem.status.isReady || false,
          plantedTime: unifiedItem.status.plantedTime,
          needsWater: unifiedItem.type === ItemType.SEED || unifiedItem.type === ItemType.CROP,
          needsFertilizer: unifiedItem.type === ItemType.SEED || unifiedItem.type === ItemType.CROP
        },
        
        special: {
          weatherBonus: baseConfig.special?.weatherBonus || 0,
          seasonBonus: baseConfig.special?.seasonBonus || 0,
          skillBonus: 0,
          isHybrid: unifiedItem.metadata.effects?.includes('hybrid_bonus') || false
        },
        
        sprite: `${this.extractVariety(unifiedItem.name)}_${unifiedItem.rarity}`,
        animation: `${this.extractVariety(unifiedItem.name)}_grow`,
        sound: `${unifiedItem.category}_sound`,
        location: unifiedItem.position ? { x: unifiedItem.position.x, y: unifiedItem.position.y } : undefined
      }

      return { success: true, item: this.convertAgriculturalItem(agriculturalItem) }
    } catch (error) {
      return { success: false, error: `转换失败: ${error instanceof Error ? error.message : '未知错误'}` }
    }
  }

  // ==================== 物品管理方法 ====================

  /**
   * 添加物品到系统
   */
  addItem(item: UnifiedItem): boolean {
    try {
      // 如果是可堆叠物品，尝试与现有物品合并
      if (item.stackable) {
        const existingItem = this.findStackableItem(item)
        if (existingItem) {
          existingItem.quantity += item.quantity
          this.emit('itemUpdated', existingItem)
          return true
        }
      }

      // 添加新物品
      this.items.set(item.id, item)
      this.addToCategory(item)
      this.emit('itemAdded', item)
      return true
    } catch (error) {
      console.error('添加物品失败:', error)
      return false
    }
  }

  /**
   * 从盲盒结果添加物品
   */
  addItemsFromLootbox(lootboxResult: LootboxResult): UnifiedItem[] {
    const addedItems: UnifiedItem[] = []

    lootboxResult.items.forEach(itemResult => {
      const unifiedItem = this.convertLootboxItem(itemResult.item, itemResult.quantity)
      if (this.addItem(unifiedItem)) {
        addedItems.push(unifiedItem)
      }
    })

    this.emit('lootboxItemsAdded', { items: addedItems, lootboxResult })
    return addedItems
  }

  /**
   * 移除物品
   */
  removeItem(itemId: string, quantity: number = 1): boolean {
    const item = this.items.get(itemId)
    if (!item) return false

    if (item.quantity <= quantity) {
      // 完全移除
      this.items.delete(itemId)
      this.removeFromCategory(item)
      this.emit('itemRemoved', item)
    } else {
      // 减少数量
      item.quantity -= quantity
      this.emit('itemUpdated', item)
    }

    return true
  }

  /**
   * 获取物品
   */
  getItem(itemId: string): UnifiedItem | undefined {
    return this.items.get(itemId)
  }

  /**
   * 获取分类物品
   */
  getItemsByCategory(category: ItemCategory): UnifiedItem[] {
    return this.categories.get(category) || []
  }

  /**
   * 获取所有物品
   */
  getAllItems(): UnifiedItem[] {
    return Array.from(this.items.values())
  }

  /**
   * 搜索物品
   */
  searchItems(query: string): UnifiedItem[] {
    const searchTerm = query.toLowerCase()
    return this.getAllItems().filter(item => 
      item.name.toLowerCase().includes(searchTerm) ||
      item.description.toLowerCase().includes(searchTerm)
    )
  }

  // ==================== 盲盒集成方法 ====================

  /**
   * 开启盲盒并添加物品到系统
   */
  async openLootbox(lootboxType: LootboxType): Promise<{ 
    success: boolean; 
    items: UnifiedItem[]; 
    result?: LootboxResult;
    error?: string 
  }> {
    try {
      // 使用LootBoxManager开启盲盒
      const lootBoxManager = new (await import('./LootBoxManager')).LootBoxManager()
      const openResult = await lootBoxManager.openLootBox(lootboxType)
      
      if (!openResult.success || !openResult.items) {
        return {
          success: false,
          items: [],
          error: openResult.error || '开启盲盒失败'
        }
      }

      // 将获得的GameItem转换为UnifiedItem并添加到背包
      const addedItems: UnifiedItem[] = []
      
      for (const gameItem of openResult.items) {
        const unifiedItem = this.convertGameItemToUnified(gameItem)
        
        // 添加到背包
        const addResult = this.addItem(unifiedItem)
        if (addResult.success && addResult.item) {
          addedItems.push(addResult.item)
        }
      }

      // 发送事件
      this.emit('lootboxOpened', {
        lootboxType,
        items: addedItems,
        result: openResult.result
      })
      
      return {
        success: true,
        items: addedItems,
        result: openResult.result
      }
    } catch (error) {
      return {
        success: false,
        items: [],
        error: error instanceof Error ? error.message : '开启盲盒失败'
      }
    }
  }

  /**
   * 将GameItem转换为UnifiedItem
   */
  private convertGameItemToUnified(gameItem: any): UnifiedItem {
    const baseItem: UnifiedItem = {
      id: gameItem.id,
      name: gameItem.name,
      description: gameItem.description,
      category: gameItem.category,
      quality: gameItem.quality,
      type: this.inferItemType(gameItem),
      icon: gameItem.icon,
      baseValue: gameItem.baseValue,
      currentValue: gameItem.baseValue,
      stackable: gameItem.stackable,
      tradeable: gameItem.tradeable,
      quantity: 1,
      maxStack: gameItem.stackable ? 999 : 1,
      obtainedAt: gameItem.obtainedAt || Date.now(),
      
      // 默认市场数据
      market: {
        avgPrice: gameItem.baseValue,
        priceHistory: [],
        demand: 1.0,
        supply: 1.0,
        volatility: 0.1,
        lastUpdated: Date.now()
      },
      
      // 默认属性
      attributes: {},
      effects: [],
      tags: []
    }

    // 根据类别添加特定属性
    if (gameItem.category === 'agricultural' && gameItem.production) {
      baseItem.attributes.production = gameItem.production
      baseItem.attributes.futuresPrice = gameItem.futuresPrice
    } else if (gameItem.category === 'industrial' && gameItem.properties) {
      baseItem.attributes.properties = gameItem.properties
      baseItem.attributes.futuresPrice = gameItem.futuresPrice
    } else if (gameItem.category === 'equipment' && gameItem.attributes) {
      baseItem.attributes.equipment = gameItem.attributes
    }

    return baseItem
  }

  /**
   * 根据GameItem推断类型
   */
  private inferItemType(gameItem: any): string {
    if (gameItem.category === 'agricultural') {
      return 'futures_agricultural'
    } else if (gameItem.category === 'industrial') {
      return 'futures_industrial'
    } else if (gameItem.category === 'equipment') {
      return 'equipment'
    }
    return 'misc'
  }

  // ==================== 合成系统集成 ====================

  /**
   * 检查合成条件
   */
  canSynthesize(itemIds: string[]): { canSynthesize: boolean; reason?: string } {
    if (itemIds.length !== 2) {
      return { canSynthesize: false, reason: '需要选择2个物品进行合成' }
    }

    const items = itemIds.map(id => this.getItem(id)).filter(item => item !== undefined) as UnifiedItem[]
    
    if (items.length !== 2) {
      return { canSynthesize: false, reason: '选择的物品无效' }
    }

    // 检查是否可合成
    if (!items.every(item => item.synthesizable)) {
      return { canSynthesize: false, reason: '选择的物品不能参与合成' }
    }

    // 检查品质是否相同
    if (items[0].rarity !== items[1].rarity) {
      return { canSynthesize: false, reason: '只能合成相同品质的物品' }
    }

    // 检查数量
    if (!items.every(item => item.quantity >= 1)) {
      return { canSynthesize: false, reason: '物品数量不足' }
    }

    return { canSynthesize: true }
  }

  /**
   * 执行物品合成
   */
  synthesizeItems(itemIds: string[]): { success: boolean; resultItem?: UnifiedItem; error?: string } {
    const checkResult = this.canSynthesize(itemIds)
    if (!checkResult.canSynthesize) {
      return { success: false, error: checkResult.reason }
    }

    const items = itemIds.map(id => this.getItem(id)).filter(item => item !== undefined) as UnifiedItem[]
    const baseItem = items[0]
    
    // 计算合成成功率
    const successRates = {
      [ItemRarity.GRAY]: 0.95,
      [ItemRarity.GREEN]: 0.90,
      [ItemRarity.BLUE]: 0.85,
      [ItemRarity.ORANGE]: 0.75,
      [ItemRarity.GOLD]: 0.60
    }

    const successRate = successRates[baseItem.rarity] || 0.50
    const isSuccess = Math.random() < successRate

    if (!isSuccess) {
      // 合成失败，消耗材料
      itemIds.forEach(id => this.removeItem(id, 1))
      this.emit('synthesisFailed', { items, reason: '合成失败' })
      return { success: false, error: '合成失败，材料已消耗' }
    }

    // 合成成功，生成新物品
    const nextRarity = this.getNextRarity(baseItem.rarity)
    if (!nextRarity) {
      return { success: false, error: '已达到最高品质，无法继续合成' }
    }

    // 消耗材料
    itemIds.forEach(id => this.removeItem(id, 1))

    // 创建合成结果
    const resultItem: UnifiedItem = {
      ...baseItem,
      id: this.generateUniqueId(baseItem.id),
      rarity: nextRarity,
      name: baseItem.name.replace(/^(普通|优质|稀有|史诗|传说)/, this.getRarityPrefix(nextRarity)),
      value: Math.floor(baseItem.value * 2.5),
      quantity: 1,
      metadata: {
        ...baseItem.metadata,
        yieldMultiplier: (baseItem.metadata.yieldMultiplier || 1) * 1.5,
        qualityBonus: (baseItem.metadata.qualityBonus || 0) + 20,
        futuresPrice: baseItem.metadata.futuresPrice ? baseItem.metadata.futuresPrice * 2 : undefined
      }
    }

    // 添加到系统
    this.addItem(resultItem)
    this.emit('synthesisSuccess', { materials: items, result: resultItem })

    return { success: true, resultItem }
  }

  // ==================== 辅助方法 ====================

  private generateUniqueId(baseId: string): string {
    return `${baseId}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private mapAgriculturalToItemType(item: AgriculturalItem): ItemType {
    if (item.category === ItemCategory.LIVESTOCK) {
      return ItemType.LIVESTOCK
    }
    
    switch (item.growth.currentStage) {
      case GrowthStage.SEED:
        return ItemType.SEED
      case GrowthStage.READY:
        return ItemType.CROP
      default:
        return ItemType.CROP
    }
  }

  private getAgriculturalIcon(item: AgriculturalItem): string {
    // 基于品种和阶段返回图标
    const icons = {
      wheat: '🌾', rice: '🌾', corn: '🌽', tomato: '🍅',
      carrot: '🥕', apple: '🍎', grape: '🍇'
    }
    return icons[item.variety as keyof typeof icons] || '🌱'
  }

  private findStackableItem(newItem: UnifiedItem): UnifiedItem | undefined {
    for (const item of this.items.values()) {
      if (item.name === newItem.name && 
          item.rarity === newItem.rarity && 
          item.stackable && 
          item.id !== newItem.id) {
        return item
      }
    }
    return undefined
  }

  private addToCategory(item: UnifiedItem): void {
    const categoryItems = this.categories.get(item.category) || []
    categoryItems.push(item)
    this.categories.set(item.category, categoryItems)
  }

  private removeFromCategory(item: UnifiedItem): void {
    const categoryItems = this.categories.get(item.category) || []
    const index = categoryItems.findIndex(i => i.id === item.id)
    if (index > -1) {
      categoryItems.splice(index, 1)
    }
  }

  private findAgriculturalConfig(name: string, rarity: ItemRarity): any {
    // 在农产品配置中查找匹配的项目
    return Object.values(AGRICULTURAL_ITEMS).find(config => 
      config.name === name && config.production && config.production[rarity]
    )
  }

  private generateEnglishName(chineseName: string): string {
    const nameMap: Record<string, string> = {
      '小麦': 'wheat', '玉米': 'corn', '大米': 'rice',
      '番茄': 'tomato', '胡萝卜': 'carrot', '苹果': 'apple'
    }
    
    for (const [chinese, english] of Object.entries(nameMap)) {
      if (chineseName.includes(chinese)) {
        return english
      }
    }
    return 'unknown'
  }

  private extractVariety(name: string): string {
    const varieties = ['wheat', 'corn', 'rice', 'tomato', 'carrot', 'apple', 'grape']
    const lowerName = name.toLowerCase()
    
    for (const variety of varieties) {
      if (lowerName.includes(variety) || name.includes(variety)) {
        return variety
      }
    }
    
    // 中文品种映射
    if (name.includes('小麦')) return 'wheat'
    if (name.includes('玉米')) return 'corn'
    if (name.includes('大米') || name.includes('水稻')) return 'rice'
    if (name.includes('番茄')) return 'tomato'
    if (name.includes('胡萝卜')) return 'carrot'
    if (name.includes('苹果')) return 'apple'
    if (name.includes('葡萄')) return 'grape'
    
    return 'unknown'
  }

  private getNextRarity(currentRarity: ItemRarity): ItemRarity | null {
    const rarityOrder = [
      ItemRarity.GRAY,
      ItemRarity.GREEN,
      ItemRarity.BLUE,
      ItemRarity.ORANGE,
      ItemRarity.GOLD,
      ItemRarity.GOLD_RED
    ]
    
    const currentIndex = rarityOrder.indexOf(currentRarity)
    return currentIndex < rarityOrder.length - 1 ? rarityOrder[currentIndex + 1] : null
  }

  private getRarityPrefix(rarity: ItemRarity): string {
    const prefixes = {
      [ItemRarity.GRAY]: '普通',
      [ItemRarity.GREEN]: '优质',
      [ItemRarity.BLUE]: '稀有',
      [ItemRarity.ORANGE]: '史诗',
      [ItemRarity.GOLD]: '传说',
      [ItemRarity.GOLD_RED]: '神话'
    }
    return prefixes[rarity] || '普通'
  }

  // ==================== 事件监听器注册 ====================

  onItemAdded(callback: (item: UnifiedItem) => void): void {
    this.on('itemAdded', callback)
  }

  onItemUpdated(callback: (item: UnifiedItem) => void): void {
    this.on('itemUpdated', callback)
  }

  onItemRemoved(callback: (item: UnifiedItem) => void): void {
    this.on('itemRemoved', callback)
  }

  onSynthesisSuccess(callback: (data: { materials: UnifiedItem[]; result: UnifiedItem }) => void): void {
    this.on('synthesisSuccess', callback)
  }

  onSynthesisFailed(callback: (data: { items: UnifiedItem[]; reason: string }) => void): void {
    this.on('synthesisFailed', callback)
  }
} 