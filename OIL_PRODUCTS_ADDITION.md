# 豆油和棕榈油产量显示功能添加

## 🎯 功能概述

根据用户需求，为期货游戏系统添加了豆油和棕榈油的产量信息显示。这两个品种也属于重要的农产品期货，现在在工具提示中能够正确显示其产量信息。

## 🔧 实施的更改

### 1. 期货产品数据扩展 📊

在 `src/data/chineseFuturesProducts.ts` 中新增了两个油脂类期货品种：

#### 豆油 (Soybean Oil)
```javascript
{
  id: 'soybean_oil',
  name: '豆油',
  nameEn: 'Soybean Oil',
  category: 'oilseed',
  exchange: 'DCE',
  icon: '🫗',
  basePrice: 7800,
  description: '大豆压榨提取的植物油，重要的食用油品种',
  yieldRanges: {
    [ItemRarity.GRAY]: { min: 16, max: 18 },       // 出油率(%)
    [ItemRarity.GREEN]: { min: 18, max: 19 },
    [ItemRarity.BLUE]: { min: 19, max: 20 },
    [ItemRarity.ORANGE]: { min: 20, max: 21 },
    [ItemRarity.GOLD]: { min: 21, max: 22 },
    [ItemRarity.GOLD_RED]: { min: 22, max: 24 }
  }
}
```

#### 棕榈油 (Palm Oil)
```javascript
{
  id: 'palm_oil',
  name: '棕榈油',
  nameEn: 'Palm Oil',
  category: 'oilseed',
  exchange: 'DCE',
  icon: '🌴',
  basePrice: 6200,
  description: '棕榈果压榨提取的植物油，世界产量最大的植物油',
  yieldRanges: {
    [ItemRarity.GRAY]: { min: 15, max: 18 },       // 出油率(%)
    [ItemRarity.GREEN]: { min: 18, max: 20 },
    [ItemRarity.BLUE]: { min: 20, max: 22 },
    [ItemRarity.ORANGE]: { min: 22, max: 24 },
    [ItemRarity.GOLD]: { min: 24, max: 26 },
    [ItemRarity.GOLD_RED]: { min: 26, max: 28 }
  }
}
```

### 2. 名称映射更新 🗺️

在工具提示组件 `FuturesProductTooltip.tsx` 中扩展了名称映射：

```javascript
const nameMap = {
  // ... 原有映射
  '豆油': 'soybean_oil',
  '棕榈油': 'palm_oil'
}
```

### 3. 产量单位差异化 📏

更新了 `getYieldUnit` 函数，为不同类型的产品使用正确的单位：

```javascript
const getYieldUnit = (category: string, productId?: string) => {
  // 特殊处理油脂类产品
  if (productId === 'soybean_oil' || productId === 'palm_oil') {
    return '%'  // 出油率
  }
  
  switch (category) {
    case 'grain':
    case 'oilseed':
    case 'fiber':
      return '公斤/亩'
    case 'livestock':
      return '公斤/头'
    case 'feed':
      return '%'  // 出粕率
    default:
      return '单位/亩'
  }
}
```

## 📊 产量信息特性

### 豆油产量特点
- **产量含义**: 出油率（从大豆中提取豆油的比例）
- **单位**: 百分比 (%)
- **产量范围**: 16%-24%（根据品质递增）
- **实际意义**: 反映大豆加工效率和油脂提取技术水平

### 棕榈油产量特点
- **产量含义**: 出油率（从棕榈果中提取油脂的比例）
- **单位**: 百分比 (%)
- **产量范围**: 15%-28%（根据品质递增）
- **实际意义**: 反映棕榈果的油脂含量和加工工艺水平

## 🎮 用户体验提升

### 工具提示显示示例

#### 普通豆油
```
┌─────────────────────────────────┐
│ 🫗 普通豆油                    │
│ [普通] (灰色背景)              │
├─────────────────────────────────┤
│ 类型: 油料作物                 │
│                                │
│ 📈 产量范围: 16-18 %           │
│    (灰色边框高亮)              │
│                                │
│ 🎯 该物品产量: 17 %            │
│    (绿色边框高亮)              │
└─────────────────────────────────┘
```

#### 优质棕榈油
```
┌─────────────────────────────────┐
│ 🌴 优质棕榈油                  │
│ [优质] (绿色背景)              │
├─────────────────────────────────┤
│ 类型: 油料作物                 │
│                                │
│ 📈 产量范围: 18-20 %           │
│    (绿色边框高亮)              │
│                                │
│ 🎯 该物品产量: 19 %            │
│    (深绿色边框高亮)            │
└─────────────────────────────────┘
```

## 🏭 行业背景知识

### 豆油在期货市场的重要性
- **全球地位**: 世界第二大植物油品种
- **用途**: 食用油、生物柴油原料
- **交易所**: 大连商品交易所 (DCE)
- **合约规格**: 10吨/手

### 棕榈油在期货市场的重要性
- **全球地位**: 世界产量最大的植物油
- **用途**: 食用油、工业用油、生物燃料
- **交易所**: 大连商品交易所 (DCE)
- **合约规格**: 10吨/手

## ✅ 功能验证要点

测试以下内容确认新功能正常：

1. **数据识别**: 豆油和棕榈油物品能正确识别品种
2. **产量显示**: 工具提示显示正确的出油率范围和具体值
3. **单位正确**: 显示"%"单位而不是"公斤/亩"
4. **稳定性**: 同一物品多次悬停显示相同的产量值
5. **视觉效果**: 与其他农产品保持一致的工具提示样式

## 🚀 扩展性

该实现方案具有良好的扩展性：
- 可轻松添加更多油脂类期货品种
- 支持不同产品类型的单位差异化
- 名称映射系统便于维护和扩展

通过这次更新，期货游戏系统现在支持完整的农产品期货品种，包括重要的油脂类产品，为用户提供更全面的市场信息！🎯✨ 