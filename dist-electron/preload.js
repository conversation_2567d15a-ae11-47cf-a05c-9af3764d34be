"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const electron_1 = require("electron");
// 暴露安全的API到渲染进程
const electronAPI = {
    // 获取平台信息
    getPlatform: () => electron_1.ipcRenderer.invoke('get-platform'),
    // 监控控制
    startMonitoring: () => electron_1.ipcRenderer.invoke('start-monitoring'),
    stopMonitoring: () => electron_1.ipcRenderer.invoke('stop-monitoring'),
    getMonitoringStatus: () => electron_1.ipcRenderer.invoke('get-monitoring-status'),
    // 白名单管理
    setWhitelist: (apps) => electron_1.ipcRenderer.invoke('set-whitelist', apps),
    getWhitelist: () => electron_1.ipcRenderer.invoke('get-whitelist'),
    getInstalledApps: () => electron_1.ipcRenderer.invoke('get-installed-apps'),
    // 事件监听
    onMonitoringUpdate: (callback) => {
        electron_1.ipcRenderer.on('monitoring-update', (event, data) => callback(data));
    },
    onViolationDetected: (callback) => {
        electron_1.ipcRenderer.on('violation-detected', (event, data) => callback(data));
    },
    // 移除事件监听
    removeAllListeners: (channel) => {
        electron_1.ipcRenderer.removeAllListeners(channel);
    },
    // 应用历史记录
    getAppHistory: (hours) => electron_1.ipcRenderer.invoke('get-app-history', hours),
    // 奖励控制相关
    getRewardControlState: () => electron_1.ipcRenderer.invoke('get-reward-control-state'),
    isRewardBlocked: (rewardType) => electron_1.ipcRenderer.invoke('is-reward-blocked', rewardType),
    getViolationStats: () => electron_1.ipcRenderer.invoke('get-violation-stats'),
    forceUnblockRewards: () => electron_1.ipcRenderer.invoke('force-unblock-rewards'),
    updateViolationConfig: (config) => electron_1.ipcRenderer.invoke('update-violation-config', config)
};
// 使用contextBridge安全地暴露API
electron_1.contextBridge.exposeInMainWorld('electronAPI', electronAPI);
