"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.RewardControlService = exports.RewardType = void 0;
const events_1 = require("events");
// 奖励类型枚举
var RewardType;
(function (RewardType) {
    RewardType["GROWTH_POINTS"] = "growth_points";
    RewardType["EXPERIENCE"] = "experience";
    RewardType["COINS"] = "coins";
    RewardType["ACHIEVEMENTS"] = "achievements";
    RewardType["LEVEL_UP"] = "level_up";
    RewardType["CROP_HARVEST"] = "crop_harvest";
    RewardType["BONUS_REWARDS"] = "bonus_rewards";
})(RewardType || (exports.RewardType = RewardType = {}));
class RewardControlService extends events_1.EventEmitter {
    constructor() {
        super();
        this.violationTimer = null;
        this.recoveryTimer = null;
        this.persistencePath = 'reward-control-state.json';
        // 默认配置
        this.config = {
            warningThreshold: 3 * 60 * 1000, // 3分钟警告
            blockThreshold: 5 * 60 * 1000, // 5分钟阻止奖励
            severeThreshold: 15 * 60 * 1000, // 15分钟严重违规
            maxDailyViolations: 5, // 每日最多5次违规
            recoveryPenaltyMultiplier: 2.0 // 恢复需要双倍时间
        };
        // 初始状态
        this.state = {
            isRewardBlocked: false,
            violationStartTime: null,
            violationDuration: 0,
            totalViolationTime: 0,
            blockedRewards: [],
            violationHistory: []
        };
        this.loadState();
    }
    static getInstance() {
        if (!RewardControlService.instance) {
            RewardControlService.instance = new RewardControlService();
        }
        return RewardControlService.instance;
    }
    /**
     * 开始违规检测
     * @param application 违规应用名称
     */
    startViolation(application) {
        if (this.state.violationStartTime) {
            return; // 已经在违规状态中
        }
        const now = Date.now();
        this.state.violationStartTime = now;
        this.state.violationDuration = 0;
        console.log(`🚨 开始违规检测: ${application}`);
        // 创建新的违规记录
        const violationRecord = {
            id: this.generateViolationId(),
            startTime: now,
            endTime: null,
            duration: 0,
            application,
            severity: 'minor',
            recovered: false
        };
        this.state.violationHistory.push(violationRecord);
        // 启动违规计时器
        this.startViolationTimer(application);
        this.emit('violation-started', {
            application,
            startTime: now,
            recordId: violationRecord.id
        });
        this.saveState();
    }
    /**
     * 结束违规检测
     */
    endViolation() {
        if (!this.state.violationStartTime) {
            return; // 没有正在进行的违规
        }
        const now = Date.now();
        const violationDuration = now - this.state.violationStartTime;
        // 更新最后一个违规记录
        const lastViolation = this.state.violationHistory[this.state.violationHistory.length - 1];
        if (lastViolation && !lastViolation.endTime) {
            lastViolation.endTime = now;
            lastViolation.duration = violationDuration;
        }
        console.log(`✅ 结束违规检测，持续时间: ${Math.round(violationDuration / 1000)}秒`);
        // 清除违规计时器
        if (this.violationTimer) {
            clearTimeout(this.violationTimer);
            this.violationTimer = null;
        }
        // 如果奖励被阻止，启动恢复过程
        if (this.state.isRewardBlocked) {
            this.startRecoveryProcess(violationDuration);
        }
        // 重置违规状态
        this.state.violationStartTime = null;
        this.state.violationDuration = 0;
        this.emit('violation-ended', {
            duration: violationDuration,
            totalViolationTime: this.state.totalViolationTime,
            isRewardBlocked: this.state.isRewardBlocked
        });
        this.saveState();
    }
    /**
     * 检查特定奖励类型是否被阻止
     * @param rewardType 奖励类型
     * @returns 是否被阻止
     */
    isRewardBlocked(rewardType) {
        if (!this.state.isRewardBlocked) {
            return false;
        }
        return this.state.blockedRewards.includes(rewardType);
    }
    /**
     * 获取当前状态
     */
    getState() {
        return { ...this.state };
    }
    /**
     * 获取违规配置
     */
    getConfig() {
        return { ...this.config };
    }
    /**
     * 更新违规配置
     * @param newConfig 新的配置
     */
    updateConfig(newConfig) {
        this.config = { ...this.config, ...newConfig };
        this.saveState();
        this.emit('config-updated', this.config);
    }
    /**
     * 获取今日违规统计
     */
    getTodayViolationStats() {
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const todayStart = today.getTime();
        const todayViolations = this.state.violationHistory.filter(v => v.startTime >= todayStart);
        const totalDuration = todayViolations.reduce((sum, v) => sum + v.duration, 0);
        const severity = {
            minor: todayViolations.filter(v => v.severity === 'minor').length,
            moderate: todayViolations.filter(v => v.severity === 'moderate').length,
            severe: todayViolations.filter(v => v.severity === 'severe').length
        };
        return {
            count: todayViolations.length,
            totalDuration,
            averageDuration: todayViolations.length > 0 ? totalDuration / todayViolations.length : 0,
            severity
        };
    }
    /**
     * 强制解除奖励阻止（管理员功能）
     */
    forceUnblockRewards() {
        if (this.recoveryTimer) {
            clearTimeout(this.recoveryTimer);
            this.recoveryTimer = null;
        }
        this.state.isRewardBlocked = false;
        this.state.blockedRewards = [];
        console.log('🔓 强制解除奖励阻止');
        this.emit('rewards-unblocked', { forced: true });
        this.saveState();
    }
    /**
     * 启动违规计时器
     * @param application 应用名称
     */
    startViolationTimer(application) {
        // 警告阶段检查
        const warningTimer = setTimeout(() => {
            if (this.state.violationStartTime) {
                console.log(`⚠️ 违规警告: 使用 ${application} 已超过 ${this.config.warningThreshold / 1000} 秒`);
                this.emit('violation-warning', {
                    application,
                    duration: this.config.warningThreshold
                });
            }
        }, this.config.warningThreshold);
        // 阻止奖励阶段
        this.violationTimer = setTimeout(() => {
            if (this.state.violationStartTime) {
                this.blockRewards(application);
            }
        }, this.config.blockThreshold);
    }
    /**
     * 阻止奖励
     * @param application 违规应用名称
     */
    blockRewards(application) {
        this.state.isRewardBlocked = true;
        this.state.blockedRewards = Object.values(RewardType);
        this.state.totalViolationTime += this.config.blockThreshold;
        // 更新违规记录的严重程度
        const lastViolation = this.state.violationHistory[this.state.violationHistory.length - 1];
        if (lastViolation) {
            const currentDuration = Date.now() - lastViolation.startTime;
            if (currentDuration >= this.config.severeThreshold) {
                lastViolation.severity = 'severe';
            }
            else if (currentDuration >= this.config.blockThreshold) {
                lastViolation.severity = 'moderate';
            }
        }
        console.log(`🚫 奖励已被阻止，违规应用: ${application}`);
        this.emit('rewards-blocked', {
            application,
            blockedRewards: this.state.blockedRewards,
            violationDuration: this.config.blockThreshold
        });
        this.saveState();
    }
    /**
     * 启动恢复过程
     * @param violationDuration 违规持续时间
     */
    startRecoveryProcess(violationDuration) {
        // 计算恢复时间（违规时间的倍数）
        const recoveryTime = Math.min(violationDuration * this.config.recoveryPenaltyMultiplier, 30 * 60 * 1000 // 最长30分钟恢复时间
        );
        console.log(`🔄 开始恢复过程，需要 ${Math.round(recoveryTime / 1000)} 秒`);
        this.recoveryTimer = setTimeout(() => {
            this.unblockRewards();
        }, recoveryTime);
        this.emit('recovery-started', {
            recoveryTime,
            violationDuration
        });
    }
    /**
     * 解除奖励阻止
     */
    unblockRewards() {
        this.state.isRewardBlocked = false;
        this.state.blockedRewards = [];
        // 标记最后一个违规记录为已恢复
        const lastViolation = this.state.violationHistory[this.state.violationHistory.length - 1];
        if (lastViolation) {
            lastViolation.recovered = true;
        }
        console.log('🎉 奖励恢复正常');
        this.emit('rewards-unblocked', { forced: false });
        this.saveState();
    }
    /**
     * 生成违规记录ID
     */
    generateViolationId() {
        return `violation_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    /**
     * 保存状态到文件
     */
    async saveState() {
        try {
            const fs = await Promise.resolve().then(() => __importStar(require('fs'))).then(m => m.promises);
            const data = JSON.stringify({
                state: this.state,
                config: this.config,
                lastSaved: Date.now()
            }, null, 2);
            await fs.writeFile(this.persistencePath, data, 'utf8');
        }
        catch (error) {
            console.error('保存奖励控制状态失败:', error);
        }
    }
    /**
     * 从文件加载状态
     */
    async loadState() {
        try {
            const fs = await Promise.resolve().then(() => __importStar(require('fs'))).then(m => m.promises);
            const data = await fs.readFile(this.persistencePath, 'utf8');
            const saved = JSON.parse(data);
            if (saved.state) {
                this.state = { ...this.state, ...saved.state };
            }
            if (saved.config) {
                this.config = { ...this.config, ...saved.config };
            }
            console.log('✅ 奖励控制状态加载完成');
        }
        catch (error) {
            console.log('📝 创建新的奖励控制状态文件');
            // 文件不存在或损坏，使用默认状态
            await this.saveState();
        }
    }
    /**
     * 清理历史记录（保留最近30天）
     */
    cleanupHistory() {
        const thirtyDaysAgo = Date.now() - (30 * 24 * 60 * 60 * 1000);
        const originalCount = this.state.violationHistory.length;
        this.state.violationHistory = this.state.violationHistory.filter(record => record.startTime > thirtyDaysAgo);
        const cleanedCount = originalCount - this.state.violationHistory.length;
        if (cleanedCount > 0) {
            console.log(`🧹 清理了 ${cleanedCount} 条历史违规记录`);
            this.saveState();
        }
    }
    /**
     * 销毁服务实例
     */
    destroy() {
        if (this.violationTimer) {
            clearTimeout(this.violationTimer);
            this.violationTimer = null;
        }
        if (this.recoveryTimer) {
            clearTimeout(this.recoveryTimer);
            this.recoveryTimer = null;
        }
        this.removeAllListeners();
        RewardControlService.instance = null;
    }
}
exports.RewardControlService = RewardControlService;
RewardControlService.instance = null;
