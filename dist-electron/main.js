"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const electron_1 = require("electron");
const path_1 = require("path");
const SystemMonitorService_1 = require("./services/SystemMonitorService");
// 保持对窗口对象的全局引用，避免被垃圾回收
let mainWindow = null;
// 系统监控服务实例
let systemMonitor;
const isDevelopment = process.env.NODE_ENV === 'development';
function createWindow() {
    // 创建主窗口
    mainWindow = new electron_1.BrowserWindow({
        width: 1200,
        height: 800,
        webPreferences: {
            nodeIntegration: false,
            contextIsolation: true,
            preload: (0, path_1.join)(__dirname, 'preload.js'),
            webSecurity: !isDevelopment,
        },
        icon: (0, path_1.join)(__dirname, '../assets/icon.png'), // 应用图标
        title: '自律农场 - 专注力训练游戏',
        show: false, // 先不显示，等准备完毕再显示
    });
    // 加载应用
    if (isDevelopment) {
        mainWindow.loadURL('http://localhost:5173');
        // 开发模式下打开开发者工具
        mainWindow.webContents.openDevTools();
    }
    else {
        mainWindow.loadFile((0, path_1.join)(__dirname, '../dist/index.html'));
    }
    // 窗口准备显示时显示
    mainWindow.once('ready-to-show', () => {
        mainWindow?.show();
    });
    // 关闭窗口时的处理
    mainWindow.on('closed', () => {
        mainWindow = null;
        if (systemMonitor) {
            systemMonitor.stopMonitoring();
        }
    });
    // 阻止新窗口打开，改为在默认浏览器中打开
    mainWindow.webContents.setWindowOpenHandler(({ url }) => {
        electron_1.shell.openExternal(url);
        return { action: 'deny' };
    });
}
// 应用准备就绪
electron_1.app.whenReady().then(async () => {
    createWindow();
    // 初始化系统监控服务
    systemMonitor = SystemMonitorService_1.SystemMonitorService.getInstance({
        interval: 1000,
        enableLogging: true,
        maxLogEntries: 1000,
        trackInactiveApps: false
    });
    // 加载白名单
    await systemMonitor.loadWhitelistFromFile();
    // 设置监控事件监听器
    systemMonitor.on('app-changed', (data) => {
        if (mainWindow) {
            mainWindow.webContents.send('monitoring-update', {
                activeApp: data.current.name,
                isWhitelisted: systemMonitor.getWhitelistApps().some(app => data.current.name.toLowerCase().includes(app.toLowerCase())),
                appInfo: data.current
            });
        }
    });
    systemMonitor.on('violation-detected', (data) => {
        if (mainWindow) {
            mainWindow.webContents.send('violation-detected', {
                activeApp: data.app.name,
                violationTime: data.duration
            });
        }
        // 显示系统通知
        electron_1.dialog.showMessageBox(mainWindow, {
            type: 'warning',
            title: '专注提醒',
            message: '检测到您使用非工作应用超过5分钟',
            detail: `当前应用: ${data.app.name}\n游戏奖励已暂停，请回到工作状态`,
            buttons: ['确定']
        });
    });
    // macOS上重新激活应用时重新创建窗口
    electron_1.app.on('activate', () => {
        if (electron_1.BrowserWindow.getAllWindows().length === 0) {
            createWindow();
        }
    });
    // 设置应用菜单
    const template = [
        {
            label: '文件',
            submenu: [
                {
                    label: '退出',
                    accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
                    click: () => {
                        electron_1.app.quit();
                    }
                }
            ]
        },
        {
            label: '查看',
            submenu: [
                { role: 'reload', label: '重新加载' },
                { role: 'forceReload', label: '强制重新加载' },
                { role: 'toggleDevTools', label: '切换开发者工具' },
                { type: 'separator' },
                { role: 'resetZoom', label: '重置缩放' },
                { role: 'zoomIn', label: '放大' },
                { role: 'zoomOut', label: '缩小' },
                { type: 'separator' },
                { role: 'togglefullscreen', label: '切换全屏' }
            ]
        },
        {
            label: '窗口',
            submenu: [
                { role: 'minimize', label: '最小化' },
                { role: 'close', label: '关闭' }
            ]
        }
    ];
    const menu = electron_1.Menu.buildFromTemplate(template);
    electron_1.Menu.setApplicationMenu(menu);
});
// 所有窗口关闭时退出应用（macOS除外）
electron_1.app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
        electron_1.app.quit();
    }
});
// 应用退出前清理
electron_1.app.on('before-quit', async () => {
    if (systemMonitor) {
        await systemMonitor.cleanup();
    }
});
// IPC 事件处理
electron_1.ipcMain.handle('get-platform', () => {
    return {
        platform: process.platform,
        arch: process.arch,
        version: process.version
    };
});
electron_1.ipcMain.handle('start-monitoring', async () => {
    try {
        await systemMonitor.startMonitoring();
        return { success: true, message: '应用监控已启动' };
    }
    catch (error) {
        return { success: false, message: `启动监控失败: ${error}` };
    }
});
electron_1.ipcMain.handle('stop-monitoring', async () => {
    try {
        await systemMonitor.stopMonitoring();
        return { success: true, message: '应用监控已停止' };
    }
    catch (error) {
        return { success: false, message: `停止监控失败: ${error}` };
    }
});
electron_1.ipcMain.handle('set-whitelist', async (event, apps) => {
    try {
        await systemMonitor.setWhitelistApps(apps);
        return { success: true, message: '白名单已更新' };
    }
    catch (error) {
        return { success: false, message: `保存白名单失败: ${error}` };
    }
});
electron_1.ipcMain.handle('get-whitelist', async () => {
    try {
        const apps = systemMonitor.getWhitelistApps();
        return { success: true, data: apps };
    }
    catch (error) {
        return { success: false, data: [] };
    }
});
electron_1.ipcMain.handle('get-monitoring-status', () => {
    return systemMonitor.getMonitoringStatus();
});
electron_1.ipcMain.handle('get-installed-apps', async () => {
    try {
        const apps = await systemMonitor.getInstalledApps();
        return apps;
    }
    catch (error) {
        console.error('获取应用列表失败:', error);
        return [];
    }
});
electron_1.ipcMain.handle('get-app-history', (event, hours = 24) => {
    try {
        return systemMonitor.getAppHistory(hours);
    }
    catch (error) {
        console.error('获取应用历史失败:', error);
        return [];
    }
});
// 奖励控制相关IPC处理
electron_1.ipcMain.handle('get-reward-control-state', () => {
    try {
        return systemMonitor.getRewardControlState();
    }
    catch (error) {
        console.error('获取奖励控制状态失败:', error);
        return null;
    }
});
electron_1.ipcMain.handle('is-reward-blocked', (event, rewardType) => {
    try {
        return systemMonitor.isRewardBlocked(rewardType);
    }
    catch (error) {
        console.error('检查奖励阻止状态失败:', error);
        return false;
    }
});
electron_1.ipcMain.handle('get-violation-stats', () => {
    try {
        return systemMonitor.getTodayViolationStats();
    }
    catch (error) {
        console.error('获取违规统计失败:', error);
        return null;
    }
});
electron_1.ipcMain.handle('force-unblock-rewards', () => {
    try {
        systemMonitor.forceUnblockRewards();
        return { success: true, message: '奖励已强制解除阻止' };
    }
    catch (error) {
        return { success: false, message: `强制解除失败: ${error}` };
    }
});
electron_1.ipcMain.handle('update-violation-config', (event, config) => {
    try {
        systemMonitor.updateViolationConfig(config);
        return { success: true, message: '违规配置已更新' };
    }
    catch (error) {
        return { success: false, message: `配置更新失败: ${error}` };
    }
});
// 防止应用多重启动
const gotTheLock = electron_1.app.requestSingleInstanceLock();
if (!gotTheLock) {
    electron_1.app.quit();
}
else {
    electron_1.app.on('second-instance', () => {
        // 当运行第二个实例时，将会聚焦到主窗口
        if (mainWindow) {
            if (mainWindow.isMinimized())
                mainWindow.restore();
            mainWindow.focus();
        }
    });
}
