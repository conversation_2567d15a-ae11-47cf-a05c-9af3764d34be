if(!self.define){let e,s={};const i=(i,c)=>(i=new URL(i+".js",c).href,s[i]||new Promise(s=>{if("document"in self){const e=document.createElement("script");e.src=i,e.onload=s,document.head.appendChild(e)}else e=i,importScripts(i),s()}).then(()=>{let e=s[i];if(!e)throw new Error(`Module ${i} didn’t register its module`);return e}));self.define=(c,n)=>{const r=e||("document"in self?document.currentScript.src:"")||location.href;if(s[r])return;let a={};const d=e=>i(e,r),o={module:{uri:r},exports:a,require:d};s[r]=Promise.all(c.map(e=>o[e]||d(e))).then(e=>(n(...e),a))}}define(["./workbox-239d0d27"],function(e){"use strict";self.skipWaiting(),e.clients<PERSON>laim(),e.precacheAndRoute([{url:"assets/index-CWu4k5YM.css",revision:null},{url:"cdn-manifest.json",revision:"6cf864372c268a9b1e64bb442c08b3f6"},{url:"health-check.json",revision:"c796c897713c494c09e29f060479a3a1"},{url:"index.html",revision:"168a29ec516962a41635eba46607b6c4"},{url:"js/index-J4Z6k7J7.js",revision:"b7398cd212150ac27e7690db39c6b4ba"},{url:"js/index-legacy-V1H-lHLx.js",revision:"b54c279a030cc68fdbe73cf7057f0a3d"},{url:"js/media-vendor-BhxCJHOa.js",revision:"04cf707e7ed8a84d259ab6cf911a2600"},{url:"js/media-vendor-legacy-B2DjrSff.js",revision:"3a83d6137a7ec0fb245e71d77018cfc7"},{url:"js/phaser-vendor-legacy-C4FxzGcX.js",revision:"21b13de6a6f9b049ec6c7602bd315a04"},{url:"js/phaser-vendor-xnwUt526.js",revision:"0b5b4554233c78c6f05d2a689c727be4"},{url:"js/polyfills-legacy-D5giwYdm.js",revision:"885633c9142964dfde842099ff8f46c7"},{url:"js/react-vendor-BXA9EqPX.js",revision:"9b9be8790245d0e18177898244bb0ca5"},{url:"js/react-vendor-legacy-Byj9Hlz-.js",revision:"5b9ead00976d17e84825e919ed907e2e"},{url:"js/ui-vendor-Buq4lKd2.js",revision:"ebdf33a4ea797daba19b38041a3fd967"},{url:"js/ui-vendor-legacy-Bf-HC24J.js",revision:"ee0478d46c623ae2f1cafe9b1b39952a"},{url:"js/utils-vendor-BFXTHynT.js",revision:"fef950d84c54875962e95abdfe422ca2"},{url:"js/utils-vendor-legacy-DAXaZ0Zh.js",revision:"65c1248f745211d6a0dce6a7df9b27df"},{url:"manifest.json",revision:"8c5e35e047ede7e0cc902db4469ac190"},{url:"registerSW.js",revision:"402b66900e731ca748771b6fc5e7a068"},{url:"manifest.webmanifest",revision:"f67d4d158dbe71134117b67956a59484"}],{}),e.cleanupOutdatedCaches(),e.registerRoute(new e.NavigationRoute(e.createHandlerBoundToURL("index.html"))),e.registerRoute(/^https:\/\/fonts\.googleapis\.com\/.*/i,new e.CacheFirst({cacheName:"google-fonts-cache",plugins:[new e.ExpirationPlugin({maxEntries:10,maxAgeSeconds:31536e3})]}),"GET"),e.registerRoute(/^https:\/\/api\.selfgame\.com\/.*/i,new e.NetworkFirst({cacheName:"api-cache",plugins:[new e.ExpirationPlugin({maxEntries:100,maxAgeSeconds:86400})]}),"GET")});
