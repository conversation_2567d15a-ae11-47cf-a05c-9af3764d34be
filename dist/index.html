<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/farm-icon.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="自律农场 - 通过摄像头监测行为帮助用户建立自律习惯的农场经营游戏" />
    <title>🌱 自律农场 - Self Discipline Farm</title>
    <script type="module" crossorigin src="./js/index-J4Z6k7J7.js"></script>
    <link rel="modulepreload" crossorigin href="./js/react-vendor-BXA9EqPX.js">
    <link rel="modulepreload" crossorigin href="./js/ui-vendor-Buq4lKd2.js">
    <link rel="modulepreload" crossorigin href="./js/phaser-vendor-xnwUt526.js">
    <link rel="modulepreload" crossorigin href="./js/media-vendor-BhxCJHOa.js">
    <link rel="modulepreload" crossorigin href="./js/utils-vendor-BFXTHynT.js">
    <link rel="stylesheet" crossorigin href="./assets/index-CWu4k5YM.css">
    <script type="module">import.meta.url;import("_").catch(()=>1);(async function*(){})().next();if(location.protocol!="file:"){window.__vite_is_modern_browser=true}</script>
    <script type="module">!function(){if(window.__vite_is_modern_browser)return;console.warn("vite: loading legacy chunks, syntax error above and the same error below should be ignored");var e=document.getElementById("vite-legacy-polyfill"),n=document.createElement("script");n.src=e.src,n.onload=function(){System.import(document.getElementById('vite-legacy-entry').getAttribute('data-src'))},document.body.appendChild(n)}();</script>
  <link rel="manifest" href="./manifest.webmanifest"><script id="vite-plugin-pwa:register-sw" src="./registerSW.js"></script></head>
  <body>
    <div id="root"></div>

    <script nomodule>!function(){var e=document,t=e.createElement("script");if(!("noModule"in t)&&"onbeforeload"in t){var n=!1;e.addEventListener("beforeload",(function(e){if(e.target===t)n=!0;else if(!e.target.hasAttribute("nomodule")||!n)return;e.preventDefault()}),!0),t.type="module",t.src=".",e.head.appendChild(t),t.remove()}}();</script>
    <script nomodule crossorigin id="vite-legacy-polyfill" src="./js/polyfills-legacy-D5giwYdm.js"></script>
    <script nomodule crossorigin id="vite-legacy-entry" data-src="./js/index-legacy-V1H-lHLx.js">System.import(document.getElementById('vite-legacy-entry').getAttribute('data-src'))</script>
  </body>
</html> 