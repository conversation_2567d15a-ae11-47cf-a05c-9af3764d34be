import{R as t}from"./ui-vendor-Buq4lKd2.js";import{r as e,g as n}from"./react-vendor-BXA9EqPX.js";const r=t=>{let e;const n=new Set,r=(t,r)=>{const u="function"==typeof t?t(e):t;if(!Object.is(u,e)){const t=e;e=(null!=r?r:"object"!=typeof u||null===u)?u:Object.assign({},e,u),n.forEach(n=>n(e,t))}},u=()=>e,o={setState:r,getState:u,getInitialState:()=>i,subscribe:t=>(n.add(t),()=>n.delete(t)),destroy:()=>{n.clear()}},i=e=t(r,u,o);return o};var u,o,i,c,s={exports:{}},a={},f={exports:{}},l={};const v=n((c||(c=1,s.exports=function(){if(i)return a;i=1;var t=e(),n=(o||(o=1,f.exports=function(){if(u)return l;u=1;var t=e(),n="function"==typeof Object.is?Object.is:function(t,e){return t===e&&(0!==t||1/t==1/e)||t!=t&&e!=e},r=t.useState,o=t.useEffect,i=t.useLayoutEffect,c=t.useDebugValue;function s(t){var e=t.getSnapshot;t=t.value;try{var r=e();return!n(t,r)}catch(u){return!0}}var a="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(t,e){return e()}:function(t,e){var n=e(),u=r({inst:{value:n,getSnapshot:e}}),a=u[0].inst,f=u[1];return i(function(){a.value=n,a.getSnapshot=e,s(a)&&f({inst:a})},[t,n,e]),o(function(){return s(a)&&f({inst:a}),t(function(){s(a)&&f({inst:a})})},[t]),c(n),n};return l.useSyncExternalStore=void 0!==t.useSyncExternalStore?t.useSyncExternalStore:a,l}()),f.exports),r="function"==typeof Object.is?Object.is:function(t,e){return t===e&&(0!==t||1/t==1/e)||t!=t&&e!=e},c=n.useSyncExternalStore,s=t.useRef,v=t.useEffect,S=t.useMemo,d=t.useDebugValue;return a.useSyncExternalStoreWithSelector=function(t,e,n,u,o){var i=s(null);if(null===i.current){var a={hasValue:!1,value:null};i.current=a}else a=i.current;i=S(function(){function t(t){if(!s){if(s=!0,i=t,t=u(t),void 0!==o&&a.hasValue){var e=a.value;if(o(e,t))return c=e}return c=t}if(e=c,r(i,t))return e;var n=u(t);return void 0!==o&&o(e,n)?(i=t,e):(i=t,c=n)}var i,c,s=!1,f=void 0===n?null:n;return[function(){return t(e())},null===f?void 0:function(){return t(f())}]},[e,n,u,o]);var f=c(t,i[0],i[1]);return v(function(){a.hasValue=!0,a.value=f},[f]),d(f),f},a}()),s.exports)),S={LEGACY:!1},{useDebugValue:d}=t,{useSyncExternalStoreWithSelector:p}=v;let b=!1;const y=t=>t,g=t=>{const e="function"==typeof t?(t=>t?r(t):r)(t):t,n=(t,n)=>function(t,e=y,n){"production"!==(S?"production":void 0)&&n&&!b&&(b=!0);const r=p(t.subscribe,t.getState,t.getServerState||t.getInitialState,e,n);return d(r),r}(e,t,n);return Object.assign(n,e),n},x=t=>g;export{x as c};
