System.register(["./react-vendor-legacy-Byj9Hlz-.js"],function(t,e){"use strict";var n;return{setters:[t=>{n=t.c}],execute:function(){t({a:function(){return r||(r=1,function(){function t(t){var e=0;return function(){return e<t.length?{done:!1,value:t[e++]}:{done:!0}}}var e="function"==typeof Object.defineProperties?Object.defineProperty:function(t,e,n){return t==Array.prototype||t==Object.prototype||(t[e]=n.value),t},r=function(t){t=["object"==typeof globalThis&&globalThis,t,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof n&&n];for(var e=0;e<t.length;++e){var r=t[e];if(r&&r.Math==Math)return r}throw Error("Cannot find global object")}(this);function i(t,n){if(n)t:{var i=r;t=t.split(".");for(var o=0;o<t.length-1;o++){var a=t[o];if(!(a in i))break t;i=i[a]}(n=n(o=i[t=t[t.length-1]]))!=o&&null!=n&&e(i,t,{configurable:!0,writable:!0,value:n})}}function o(t){return(t={next:t})[Symbol.iterator]=function(){return this},t}function a(e){var n="undefined"!=typeof Symbol&&Symbol.iterator&&e[Symbol.iterator];return n?n.call(e):{next:t(e)}}function u(){this.i=!1,this.g=null,this.o=void 0,this.j=1,this.m=0,this.h=null}function s(t){if(t.i)throw new TypeError("Generator is already running");t.i=!0}function c(t,e){t.h={F:e,G:!0},t.j=t.m}function l(t){this.g=new u,this.h=t}function f(t,e,n,r){try{var i=e.call(t.g.g,n);if(!(i instanceof Object))throw new TypeError("Iterator result "+i+" is not an object");if(!i.done)return t.g.i=!1,i;var o=i.value}catch(a){return t.g.g=null,c(t.g,a),h(t)}return t.g.g=null,r.call(t.g,o),h(t)}function h(t){for(;t.g.j;)try{var e=t.h(t.g);if(e)return t.g.i=!1,{value:e.value,done:!1}}catch(n){t.g.o=void 0,c(t.g,n)}if(t.g.i=!1,t.g.h){if(e=t.g.h,t.g.h=null,e.G)throw e.F;return{value:e.return,done:!0}}return{value:void 0,done:!0}}function p(t){this.next=function(e){return s(t.g),t.g.g?e=f(t,t.g.g.next,e,t.g.l):(t.g.l(e),e=h(t)),e},this.throw=function(e){return s(t.g),t.g.g?e=f(t,t.g.g.throw,e,t.g.l):(c(t.g,e),e=h(t)),e},this.return=function(e){return function(t,e){s(t.g);var n=t.g.g;return n?f(t,"return"in n?n.return:function(t){return{value:t,done:!0}},e,t.g.return):(t.g.return(e),h(t))}(t,e)},this[Symbol.iterator]=function(){return this}}function v(t){function e(e){return t.next(e)}function n(e){return t.throw(e)}return new Promise(function(r,i){!function t(o){o.done?r(o.value):Promise.resolve(o.value).then(e,n).then(t,i)}(t.next())})}i("Symbol",function(t){function n(t,n){this.g=t,e(this,"description",{configurable:!0,writable:!0,value:n})}if(t)return t;n.prototype.toString=function(){return this.g};var r="jscomp_symbol_"+(1e9*Math.random()>>>0)+"_",i=0;return function t(e){if(this instanceof t)throw new TypeError("Symbol is not a constructor");return new n(r+(e||"")+"_"+i++,e)}}),i("Symbol.iterator",function(n){if(n)return n;n=Symbol("Symbol.iterator");for(var i="Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array".split(" "),a=0;a<i.length;a++){var u=r[i[a]];"function"==typeof u&&"function"!=typeof u.prototype[n]&&e(u.prototype,n,{configurable:!0,writable:!0,value:function(){return o(t(this))}})}return n}),u.prototype.l=function(t){this.o=t},u.prototype.return=function(t){this.h={return:t},this.j=this.m},i("Promise",function(t){function e(t){this.h=0,this.i=void 0,this.g=[],this.o=!1;var e=this.j();try{t(e.resolve,e.reject)}catch(n){e.reject(n)}}function n(){this.g=null}function i(t){return t instanceof e?t:new e(function(e){e(t)})}if(t)return t;n.prototype.h=function(t){if(null==this.g){this.g=[];var e=this;this.i(function(){e.l()})}this.g.push(t)};var o=r.setTimeout;n.prototype.i=function(t){o(t,0)},n.prototype.l=function(){for(;this.g&&this.g.length;){var t=this.g;this.g=[];for(var e=0;e<t.length;++e){var n=t[e];t[e]=null;try{n()}catch(r){this.j(r)}}}this.g=null},n.prototype.j=function(t){this.i(function(){throw t})},e.prototype.j=function(){function t(t){return function(r){n||(n=!0,t.call(e,r))}}var e=this,n=!1;return{resolve:t(this.A),reject:t(this.l)}},e.prototype.A=function(t){if(t===this)this.l(new TypeError("A Promise cannot resolve to itself"));else if(t instanceof e)this.C(t);else{t:switch(typeof t){case"object":var n=null!=t;break t;case"function":n=!0;break t;default:n=!1}n?this.v(t):this.m(t)}},e.prototype.v=function(t){var e=void 0;try{e=t.then}catch(n){return void this.l(n)}"function"==typeof e?this.D(e,t):this.m(t)},e.prototype.l=function(t){this.u(2,t)},e.prototype.m=function(t){this.u(1,t)},e.prototype.u=function(t,e){if(0!=this.h)throw Error("Cannot settle("+t+", "+e+"): Promise already settled in state"+this.h);this.h=t,this.i=e,2===this.h&&this.B(),this.H()},e.prototype.B=function(){var t=this;o(function(){if(t.I()){var e=r.console;void 0!==e&&e.error(t.i)}},1)},e.prototype.I=function(){if(this.o)return!1;var t=r.CustomEvent,e=r.Event,n=r.dispatchEvent;return void 0===n||("function"==typeof t?t=new t("unhandledrejection",{cancelable:!0}):"function"==typeof e?t=new e("unhandledrejection",{cancelable:!0}):(t=r.document.createEvent("CustomEvent")).initCustomEvent("unhandledrejection",!1,!0,t),t.promise=this,t.reason=this.i,n(t))},e.prototype.H=function(){if(null!=this.g){for(var t=0;t<this.g.length;++t)u.h(this.g[t]);this.g=null}};var u=new n;return e.prototype.C=function(t){var e=this.j();t.s(e.resolve,e.reject)},e.prototype.D=function(t,e){var n=this.j();try{t.call(e,n.resolve,n.reject)}catch(r){n.reject(r)}},e.prototype.then=function(t,n){function r(t,e){return"function"==typeof t?function(e){try{i(t(e))}catch(n){o(n)}}:e}var i,o,a=new e(function(t,e){i=t,o=e});return this.s(r(t,i),r(n,o)),a},e.prototype.catch=function(t){return this.then(void 0,t)},e.prototype.s=function(t,e){function n(){switch(r.h){case 1:t(r.i);break;case 2:e(r.i);break;default:throw Error("Unexpected state: "+r.h)}}var r=this;null==this.g?u.h(n):this.g.push(n),this.o=!0},e.resolve=i,e.reject=function(t){return new e(function(e,n){n(t)})},e.race=function(t){return new e(function(e,n){for(var r=a(t),o=r.next();!o.done;o=r.next())i(o.value).s(e,n)})},e.all=function(t){var n=a(t),r=n.next();return r.done?i([]):new e(function(t,e){function o(e){return function(n){a[e]=n,0==--u&&t(a)}}var a=[],u=0;do{a.push(void 0),u++,i(r.value).s(o(a.length-1),e),r=n.next()}while(!r.done)})},e});var d="function"==typeof Object.assign?Object.assign:function(t,e){for(var n=1;n<arguments.length;n++){var r=arguments[n];if(r)for(var i in r)Object.prototype.hasOwnProperty.call(r,i)&&(t[i]=r[i])}return t};i("Object.assign",function(t){return t||d});var y=this||self,g={facingMode:"user",width:640,height:480};function m(t,e){this.video=t,this.i=0,this.h=Object.assign(Object.assign({},g),e)}function b(t){window.requestAnimationFrame(function(){!function(t){var e=null;t.video.paused||t.video.currentTime===t.i||(t.i=t.video.currentTime,e=t.h.onFrame()),e?e.then(function(){b(t)}):b(t)}(t)})}m.prototype.stop=function(){var t,e,n,r=this;return v(new p(new l(function(i){if(r.g){for(t=r.g.getTracks(),e=a(t),n=e.next();!n.done;n=e.next())n.value.stop();r.g=void 0}i.j=0})))},m.prototype.start=function(){var t,e=this;return v(new p(new l(function(n){return navigator.mediaDevices&&navigator.mediaDevices.getUserMedia||alert("No navigator.mediaDevices.getUserMedia exists."),t=e.h,n.return(navigator.mediaDevices.getUserMedia({video:{facingMode:t.facingMode,width:t.width,height:t.height}}).then(function(t){!function(t,e){t.g=e,t.video.srcObject=e,t.video.onloadedmetadata=function(){t.video.play(),b(t)}}(e,t)}).catch(function(t){throw alert("Failed to acquire camera feed: "+t),t}))})))};var w,E=["Camera"],A=y;E[0]in A||void 0===A.execScript||A.execScript("var "+E[0]);for(;E.length&&(w=E.shift());)E.length||void 0===m?A=A[w]&&A[w]!==Object.prototype[w]?A[w]:A[w]={}:A[w]=m}.call(o)),o},r:function(){return e||(e=1,function(){var t;function e(t){var e=0;return function(){return e<t.length?{done:!1,value:t[e++]}:{done:!0}}}var r="function"==typeof Object.defineProperties?Object.defineProperty:function(t,e,n){return t==Array.prototype||t==Object.prototype||(t[e]=n.value),t},i=function(t){t=["object"==typeof globalThis&&globalThis,t,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof n&&n];for(var e=0;e<t.length;++e){var r=t[e];if(r&&r.Math==Math)return r}throw Error("Cannot find global object")}(this);function o(t,e){if(e)t:{var n=i;t=t.split(".");for(var o=0;o<t.length-1;o++){var a=t[o];if(!(a in n))break t;n=n[a]}(e=e(o=n[t=t[t.length-1]]))!=o&&null!=e&&r(n,t,{configurable:!0,writable:!0,value:e})}}function a(t){return(t={next:t})[Symbol.iterator]=function(){return this},t}function u(t){var n="undefined"!=typeof Symbol&&Symbol.iterator&&t[Symbol.iterator];return n?n.call(t):{next:e(t)}}function s(t){if(!(t instanceof Array)){t=u(t);for(var e,n=[];!(e=t.next()).done;)n.push(e.value);t=n}return t}o("Symbol",function(t){function e(t,e){this.h=t,r(this,"description",{configurable:!0,writable:!0,value:e})}if(t)return t;e.prototype.toString=function(){return this.h};var n="jscomp_symbol_"+(1e9*Math.random()>>>0)+"_",i=0;return function t(r){if(this instanceof t)throw new TypeError("Symbol is not a constructor");return new e(n+(r||"")+"_"+i++,r)}}),o("Symbol.iterator",function(t){if(t)return t;t=Symbol("Symbol.iterator");for(var n="Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array".split(" "),o=0;o<n.length;o++){var u=i[n[o]];"function"==typeof u&&"function"!=typeof u.prototype[t]&&r(u.prototype,t,{configurable:!0,writable:!0,value:function(){return a(e(this))}})}return t});var c="function"==typeof Object.assign?Object.assign:function(t,e){for(var n=1;n<arguments.length;n++){var r=arguments[n];if(r)for(var i in r)Object.prototype.hasOwnProperty.call(r,i)&&(t[i]=r[i])}return t};o("Object.assign",function(t){return t||c});var l,f="function"==typeof Object.create?Object.create:function(t){function e(){}return e.prototype=t,new e};if("function"==typeof Object.setPrototypeOf)l=Object.setPrototypeOf;else{var h;t:{var p={};try{p.__proto__={a:!0},h=p.a;break t}catch(gn){}h=!1}l=h?function(t,e){if(t.__proto__=e,t.__proto__!==e)throw new TypeError(t+" is not extensible");return t}:null}var v=l;function d(t,e){if(t.prototype=f(e.prototype),t.prototype.constructor=t,v)v(t,e);else for(var n in e)if("prototype"!=n)if(Object.defineProperties){var r=Object.getOwnPropertyDescriptor(e,n);r&&Object.defineProperty(t,n,r)}else t[n]=e[n];t.za=e.prototype}function y(){this.m=!1,this.j=null,this.i=void 0,this.h=1,this.v=this.s=0,this.l=null}function g(t){if(t.m)throw new TypeError("Generator is already running");t.m=!0}function m(t,e){t.l={ma:e,na:!0},t.h=t.s||t.v}function b(t,e,n){return t.h=n,{value:e}}function w(t){this.h=new y,this.i=t}function E(t,e,n,r){try{var i=e.call(t.h.j,n);if(!(i instanceof Object))throw new TypeError("Iterator result "+i+" is not an object");if(!i.done)return t.h.m=!1,i;var o=i.value}catch(a){return t.h.j=null,m(t.h,a),A(t)}return t.h.j=null,r.call(t.h,o),A(t)}function A(t){for(;t.h.h;)try{var e=t.i(t.h);if(e)return t.h.m=!1,{value:e.value,done:!1}}catch(n){t.h.i=void 0,m(t.h,n)}if(t.h.m=!1,t.h.l){if(e=t.h.l,t.h.l=null,e.na)throw e.ma;return{value:e.return,done:!0}}return{value:void 0,done:!0}}function _(t){this.next=function(e){return g(t.h),t.h.j?e=E(t,t.h.j.next,e,t.h.u):(t.h.u(e),e=A(t)),e},this.throw=function(e){return g(t.h),t.h.j?e=E(t,t.h.j.throw,e,t.h.u):(m(t.h,e),e=A(t)),e},this.return=function(e){return function(t,e){g(t.h);var n=t.h.j;return n?E(t,"return"in n?n.return:function(t){return{value:t,done:!0}},e,t.h.return):(t.h.return(e),A(t))}(t,e)},this[Symbol.iterator]=function(){return this}}function T(t){return function(t){function e(e){return t.next(e)}function n(e){return t.throw(e)}return new Promise(function(r,i){!function t(o){o.done?r(o.value):Promise.resolve(o.value).then(e,n).then(t,i)}(t.next())})}(new _(new w(t)))}function j(t){return t||Array.prototype.fill}y.prototype.u=function(t){this.i=t},y.prototype.return=function(t){this.l={return:t},this.h=this.v},o("Promise",function(t){function e(t){this.i=0,this.j=void 0,this.h=[],this.u=!1;var e=this.l();try{t(e.resolve,e.reject)}catch(n){e.reject(n)}}function n(){this.h=null}function r(t){return t instanceof e?t:new e(function(e){e(t)})}if(t)return t;n.prototype.i=function(t){if(null==this.h){this.h=[];var e=this;this.j(function(){e.m()})}this.h.push(t)};var o=i.setTimeout;n.prototype.j=function(t){o(t,0)},n.prototype.m=function(){for(;this.h&&this.h.length;){var t=this.h;this.h=[];for(var e=0;e<t.length;++e){var n=t[e];t[e]=null;try{n()}catch(r){this.l(r)}}}this.h=null},n.prototype.l=function(t){this.j(function(){throw t})},e.prototype.l=function(){function t(t){return function(r){n||(n=!0,t.call(e,r))}}var e=this,n=!1;return{resolve:t(this.I),reject:t(this.m)}},e.prototype.I=function(t){if(t===this)this.m(new TypeError("A Promise cannot resolve to itself"));else if(t instanceof e)this.L(t);else{t:switch(typeof t){case"object":var n=null!=t;break t;case"function":n=!0;break t;default:n=!1}n?this.F(t):this.s(t)}},e.prototype.F=function(t){var e=void 0;try{e=t.then}catch(n){return void this.m(n)}"function"==typeof e?this.M(e,t):this.s(t)},e.prototype.m=function(t){this.v(2,t)},e.prototype.s=function(t){this.v(1,t)},e.prototype.v=function(t,e){if(0!=this.i)throw Error("Cannot settle("+t+", "+e+"): Promise already settled in state"+this.i);this.i=t,this.j=e,2===this.i&&this.K(),this.H()},e.prototype.K=function(){var t=this;o(function(){if(t.D()){var e=i.console;void 0!==e&&e.error(t.j)}},1)},e.prototype.D=function(){if(this.u)return!1;var t=i.CustomEvent,e=i.Event,n=i.dispatchEvent;return void 0===n||("function"==typeof t?t=new t("unhandledrejection",{cancelable:!0}):"function"==typeof e?t=new e("unhandledrejection",{cancelable:!0}):(t=i.document.createEvent("CustomEvent")).initCustomEvent("unhandledrejection",!1,!0,t),t.promise=this,t.reason=this.j,n(t))},e.prototype.H=function(){if(null!=this.h){for(var t=0;t<this.h.length;++t)a.i(this.h[t]);this.h=null}};var a=new n;return e.prototype.L=function(t){var e=this.l();t.T(e.resolve,e.reject)},e.prototype.M=function(t,e){var n=this.l();try{t.call(e,n.resolve,n.reject)}catch(r){n.reject(r)}},e.prototype.then=function(t,n){function r(t,e){return"function"==typeof t?function(e){try{i(t(e))}catch(n){o(n)}}:e}var i,o,a=new e(function(t,e){i=t,o=e});return this.T(r(t,i),r(n,o)),a},e.prototype.catch=function(t){return this.then(void 0,t)},e.prototype.T=function(t,e){function n(){switch(r.i){case 1:t(r.j);break;case 2:e(r.j);break;default:throw Error("Unexpected state: "+r.i)}}var r=this;null==this.h?a.i(n):this.h.push(n),this.u=!0},e.resolve=r,e.reject=function(t){return new e(function(e,n){n(t)})},e.race=function(t){return new e(function(e,n){for(var i=u(t),o=i.next();!o.done;o=i.next())r(o.value).T(e,n)})},e.all=function(t){var n=u(t),i=n.next();return i.done?r([]):new e(function(t,e){function o(e){return function(n){a[e]=n,0==--u&&t(a)}}var a=[],u=0;do{a.push(void 0),u++,r(i.value).T(o(a.length-1),e),i=n.next()}while(!i.done)})},e}),o("Array.prototype.keys",function(t){return t||function(){return function(t,e){t instanceof String&&(t+="");var n=0,r=!1,i={next:function(){if(!r&&n<t.length){var i=n++;return{value:e(i,t[i]),done:!1}}return r=!0,{done:!0,value:void 0}}};return i[Symbol.iterator]=function(){return i},i}(this,function(t){return t})}}),o("Array.prototype.fill",function(t){return t||function(t,e,n){var r=this.length||0;for(0>e&&(e=Math.max(0,r+e)),(null==n||n>r)&&(n=r),0>(n=Number(n))&&(n=Math.max(0,r+n)),e=Number(e||0);e<n;e++)this[e]=t;return this}}),o("Int8Array.prototype.fill",j),o("Uint8Array.prototype.fill",j),o("Uint8ClampedArray.prototype.fill",j),o("Int16Array.prototype.fill",j),o("Uint16Array.prototype.fill",j),o("Int32Array.prototype.fill",j),o("Uint32Array.prototype.fill",j),o("Float32Array.prototype.fill",j),o("Float64Array.prototype.fill",j),o("Object.is",function(t){return t||function(t,e){return t===e?0!==t||1/t==1/e:t!=t&&e!=e}}),o("Array.prototype.includes",function(t){return t||function(t,e){var n=this;n instanceof String&&(n=String(n));var r=n.length;for(0>(e=e||0)&&(e=Math.max(e+r,0));e<r;e++){var i=n[e];if(i===t||Object.is(i,t))return!0}return!1}}),o("String.prototype.includes",function(t){return t||function(t,e){if(null==this)throw new TypeError("The 'this' value for String.prototype.includes must not be null or undefined");if(t instanceof RegExp)throw new TypeError("First argument to String.prototype.includes must not be a regular expression");return-1!==this.indexOf(t,e||0)}});var O=this||self;function x(t,e){t=t.split(".");var n,r=O;t[0]in r||void 0===r.execScript||r.execScript("var "+t[0]);for(;t.length&&(n=t.shift());)t.length||void 0===e?r=r[n]&&r[n]!==Object.prototype[n]?r[n]:r[n]={}:r[n]=e}function F(t){var e;return(e=O.navigator)&&(e=e.userAgent)||(e=""),-1!=e.indexOf(t)}var R=Array.prototype.map?function(t,e){return Array.prototype.map.call(t,e,void 0)}:function(t,e){for(var n=t.length,r=Array(n),i="string"==typeof t?t.split(""):t,o=0;o<n;o++)o in i&&(r[o]=e.call(void 0,i[o],o,t));return r},S={},I=null;function L(){if(!I){I={};for(var t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),e=["+/=","+/","-_=","-_.","-_"],n=0;5>n;n++){var r=t.concat(e[n].split(""));S[n]=r;for(var i=0;i<r.length;i++){var o=r[i];void 0===I[o]&&(I[o]=i)}}}}var k="undefined"!=typeof Uint8Array,N=!(F("Trident")||F("MSIE"))&&"function"==typeof O.btoa;function C(t){if(!N){var e;void 0===e&&(e=0),L(),e=S[e];for(var n=Array(Math.floor(t.length/3)),r=e[64]||"",i=0,o=0;i<t.length-2;i+=3){var a=t[i],u=t[i+1],s=t[i+2],c=e[a>>2];a=e[(3&a)<<4|u>>4],u=e[(15&u)<<2|s>>6],s=e[63&s],n[o++]=c+a+u+s}switch(c=0,s=r,t.length-i){case 2:s=e[(15&(c=t[i+1]))<<2]||r;case 1:t=t[i],n[o]=e[t>>2]+e[(3&t)<<4|c>>4]+s+r}return n.join("")}for(e="";10240<t.length;)e+=String.fromCharCode.apply(null,t.subarray(0,10240)),t=t.subarray(10240);return e+=String.fromCharCode.apply(null,t),btoa(e)}var P,U=RegExp("[-_.]","g");function M(t){switch(t){case"-":return"+";case"_":return"/";case".":return"=";default:return""}}function B(t){if(!N)return function(t){var e=t.length,n=3*e/4;n%3?n=Math.floor(n):-1!="=.".indexOf(t[e-1])&&(n=-1!="=.".indexOf(t[e-2])?n-2:n-1);var r=new Uint8Array(n),i=0;return function(t,e){function n(e){for(;r<t.length;){var n=t.charAt(r++),i=I[n];if(null!=i)return i;if(!/^[\s\xa0]*$/.test(n))throw Error("Unknown base64 encoding at char: "+n)}return e}L();for(var r=0;;){var i=n(-1),o=n(0),a=n(64),u=n(64);if(64===u&&-1===i)break;e(i<<2|o>>4),64!=a&&(e(o<<4&240|a>>2),64!=u&&e(a<<6&192|u))}}(t,function(t){r[i++]=t}),i!==n?r.subarray(0,i):r}(t);U.test(t)&&(t=t.replace(U,M)),t=atob(t);for(var e=new Uint8Array(t.length),n=0;n<t.length;n++)e[n]=t.charCodeAt(n);return e}function H(){return P||(P=new Uint8Array(0))}var D={},G="function"==typeof Uint8Array.prototype.slice,Y=0,W=0;function X(t){var e=0>t,n=(t=Math.abs(t))>>>0;t=Math.floor((t-n)/4294967296),e&&(e=(n=u(V(n,t))).next().value,t=n.next().value,n=e),Y=n>>>0,W=t>>>0}var K,z="function"==typeof BigInt;function V(t,e){return e=~e,t?t=1+~t:e+=1,[t,e]}function J(t,e){this.i=t>>>0,this.h=e>>>0}function q(t){if(!t)return K||(K=new J(0,0));if(!/^-?\d+$/.test(t))return null;if(16>t.length)X(Number(t));else if(z)t=BigInt(t),Y=Number(t&BigInt(4294967295))>>>0,W=Number(t>>BigInt(32)&BigInt(4294967295));else{var e=+("-"===t[0]);W=Y=0;for(var n=t.length,r=e,i=(n-e)%6+e;i<=n;r=i,i+=6)r=Number(t.slice(r,i)),W*=1e6,4294967296<=(Y=1e6*Y+r)&&(W+=Y/4294967296|0,Y%=4294967296);e&&(t=(e=u(V(Y,W))).next().value,e=e.next().value,Y=t,W=e)}return new J(Y,W)}function Z(t,e){return Error("Invalid wire type: "+t+" (at position "+e+")")}function $(){return Error("Failed to read varint, encoding is invalid.")}function Q(t,e){return Error("Tried to read past the end of the data "+e+" > "+t)}function tt(){throw Error("Invalid UTF8")}function et(t,e){return e=String.fromCharCode.apply(null,e),null==t?e:t+e}var nt,rt,it,ot=void 0,at="undefined"!=typeof TextDecoder,ut="undefined"!=typeof TextEncoder;function st(t){if(t!==D)throw Error("illegal external caller")}function ct(t,e){if(st(e),this.V=t,null!=t&&0===t.length)throw Error("ByteString should be constructed with non-empty values")}function lt(){return it||(it=new ct(null,D))}function ft(t){st(D);var e=t.V;return null==(e=null==e||k&&null!=e&&e instanceof Uint8Array?e:"string"==typeof e?B(e):null)?e:t.V=e}function ht(t,e){this.i=null,this.m=!1,this.h=this.j=this.l=0,pt(this,t,e)}function pt(t,e,n){n=void 0===n?{}:n,t.S=void 0!==n.S&&n.S,e&&(e=function(t){if("string"==typeof t)return{buffer:B(t),C:!1};if(Array.isArray(t))return{buffer:new Uint8Array(t),C:!1};if(t.constructor===Uint8Array)return{buffer:t,C:!1};if(t.constructor===ArrayBuffer)return{buffer:new Uint8Array(t),C:!1};if(t.constructor===ct)return{buffer:ft(t)||H(),C:!0};if(t instanceof Uint8Array)return{buffer:new Uint8Array(t.buffer,t.byteOffset,t.byteLength),C:!1};throw Error("Type not convertible to a Uint8Array, expected a Uint8Array, an ArrayBuffer, a base64 encoded string, a ByteString or an Array of numbers")}(e),t.i=e.buffer,t.m=e.C,t.l=0,t.j=t.i.length,t.h=t.l)}function vt(t,e){if(t.h=e,e>t.j)throw Q(t.j,e)}function dt(t){var e=t.i,n=t.h,r=e[n++],i=127&r;if(128&r&&(i|=(127&(r=e[n++]))<<7,128&r&&(i|=(127&(r=e[n++]))<<14,128&r&&(i|=(127&(r=e[n++]))<<21,128&r&&(i|=(r=e[n++])<<28,128&r&&128&e[n++]&&128&e[n++]&&128&e[n++]&&128&e[n++]&&128&e[n++])))))throw $();return vt(t,n),i}function yt(t,e){if(0>e)throw Error("Tried to read a negative byte length: "+e);var n=t.h,r=n+e;if(r>t.j)throw Q(e,t.j-n);return t.h=r,n}ht.prototype.reset=function(){this.h=this.l};var gt=[];function mt(){this.h=[]}function bt(t,e,n){for(;0<n||127<e;)t.h.push(127&e|128),e=(e>>>7|n<<25)>>>0,n>>>=7;t.h.push(e)}function wt(t,e){for(;127<e;)t.h.push(127&e|128),e>>>=7;t.h.push(e)}function Et(t,e){if(gt.length){var n=gt.pop();pt(n,t,e),t=n}else t=new ht(t,e);this.h=t,this.j=this.h.h,this.i=this.l=-1,this.setOptions(e)}function At(t){var e=t.h;if(e.h==e.j)return!1;t.j=t.h.h;var n=dt(t.h)>>>0;if(e=n>>>3,!(0<=(n&=7)&&5>=n))throw Z(n,t.j);if(1>e)throw Error("Invalid field number: "+e+" (at position "+t.j+")");return t.l=e,t.i=n,!0}function _t(t){switch(t.i){case 0:if(0!=t.i)_t(t);else t:{for(var e=(t=t.h).h,n=e+10,r=t.i;e<n;)if(!(128&r[e++])){vt(t,e);break t}throw $()}break;case 1:vt(t=t.h,t.h+8);break;case 2:2!=t.i?_t(t):(e=dt(t.h)>>>0,vt(t=t.h,t.h+e));break;case 5:vt(t=t.h,t.h+4);break;case 3:for(e=t.l;;){if(!At(t))throw Error("Unmatched start-group tag: stream EOF");if(4==t.i){if(t.l!=e)throw Error("Unmatched end-group tag");break}_t(t)}break;default:throw Z(t.i,t.j)}}mt.prototype.length=function(){return this.h.length},mt.prototype.end=function(){var t=this.h;return this.h=[],t},Et.prototype.setOptions=function(t){t=void 0===t?{}:t,this.ca=void 0!==t.ca&&t.ca},Et.prototype.reset=function(){this.h.reset(),this.j=this.h.h,this.i=this.l=-1};var Tt=[];function jt(){this.j=[],this.i=0,this.h=new mt}function Ot(t,e){0!==e.length&&(t.j.push(e),t.i+=e.length)}var xt="function"==typeof Symbol&&"symbol"==typeof Symbol()?Symbol():void 0;function Ft(t,e){return xt?t[xt]|=e:void 0!==t.A?t.A|=e:(Object.defineProperties(t,{A:{value:e,configurable:!0,writable:!0,enumerable:!1}}),e)}function Rt(t,e){xt?t[xt]&&(t[xt]&=~e):void 0!==t.A&&(t.A&=~e)}function St(t){var e;return null==(e=xt?t[xt]:t.A)?0:e}function It(t,e){xt?t[xt]=e:void 0!==t.A?t.A=e:Object.defineProperties(t,{A:{value:e,configurable:!0,writable:!0,enumerable:!1}})}function Lt(t){return Ft(t,1),t}function kt(t,e){It(e,-51&t)}function Nt(t,e){It(e,-41&t|18)}var Ct={};function Pt(t){return null!==t&&"object"==typeof t&&!Array.isArray(t)&&t.constructor===Object}var Ut,Mt,Bt=[];function Ht(t){if(2&St(t.o))throw Error("Cannot mutate an immutable Message")}function Dt(t){var e=t.length;(e=e?t[e-1]:void 0)&&Pt(e)?e.g=1:(e={},t.push((e.g=1,e)))}function Gt(t){var e=t.i+t.G;return t.B||(t.B=t.o[e]={})}function Yt(t,e){return-1===e?null:e>=t.i?t.B?t.B[e]:void 0:t.o[e+t.G]}function Wt(t,e,n,r){Ht(t),Xt(t,e,n,r)}function Xt(t,e,n,r){t.j&&(t.j=void 0),e>=t.i||r?Gt(t)[e]=n:(t.o[e+t.G]=n,(t=t.B)&&e in t&&delete t[e])}function Kt(t,e,n,r){var i=Yt(t,e);Array.isArray(i)||(i=Ut);var o=St(i);if(1&o||Lt(i),r)2&o||Ft(i,2),1&n||Object.freeze(i);else{r=!(2&n);var a=2&o;1&n||!a?r&&16&o&&!a&&Rt(i,16):Xt(t,e,i=Lt(Array.prototype.slice.call(i)))}return i}function zt(t,e){var n=Yt(t,e),r=null==n?n:"number"==typeof n||"NaN"===n||"Infinity"===n||"-Infinity"===n?Number(n):void 0;return null!=r&&r!==n&&Xt(t,e,r),r}function Vt(t,e,n,r,i){t.h||(t.h={});var o=t.h[n],a=Kt(t,n,3,i);if(!o){var u=a;o=[];var s=!!(16&St(t.o));a=!!(2&St(u));var c=u;!i&&a&&(u=Array.prototype.slice.call(u));for(var l=a,f=0;f<u.length;f++){var h=u[f],p=e,v=!1;if(v=void 0!==v&&v,void 0!==(h=Array.isArray(h)?new p(h):v?new p:void 0)){var d=v=St(p=h.o);a&&(d|=2),s&&(d|=16),d!=v&&It(p,d),p=d,l=l||!!(2&p),o.push(h)}}return t.h[n]=o,e=33|(s=St(u)),s!=(e=l?-9&e:8|e)&&(l=u,Object.isFrozen(l)&&(l=Array.prototype.slice.call(l)),It(l,e),u=l),c!==u&&Xt(t,n,u),(i||r&&a)&&Ft(o,2),r&&Object.freeze(o),o}return i||(i=Object.isFrozen(o),r&&!i?Object.freeze(o):!r&&i&&(o=Array.prototype.slice.call(o),t.h[n]=o)),o}function Jt(t,e,n){var r=!!(2&St(t.o));if(e=Vt(t,e,n,r,r),t=Kt(t,n,3,r),!(r||8&St(t))){for(r=0;r<e.length;r++){if(2&St((n=e[r]).o)){var i=ue(n,!1);i.j=n}else i=n;n!==i&&(e[r]=i,t[r]=i.o)}Ft(t,8)}return e}function qt(t,e,n){if(null!=n&&"number"!=typeof n)throw Error("Value of float/double field must be a number|null|undefined, found "+typeof n+": "+n);Wt(t,e,n)}function Zt(t,e,n,r,i){Ht(t);var o=Vt(t,n,e,!1,!1);return n=null!=r?r:new n,t=Kt(t,e,2,!1),null!=i?(o.splice(i,0,n),t.splice(i,0,n.o)):(o.push(n),t.push(n.o)),n.C()&&Rt(t,8),n}function $t(t,e){return null==t?e:t}function Qt(t,e,n){return n=void 0===n?0:n,$t(zt(t,e),n)}function te(t,e,n,r){if(null!=t){if(Array.isArray(t))t=ee(t,e,n,void 0!==r);else if(Pt(t)){var i,o={};for(i in t)o[i]=te(t[i],e,n,r);t=o}else t=e(t,r);return t}}function ee(t,e,n,r){var i=St(t);r=r?!!(16&i):void 0,t=Array.prototype.slice.call(t);for(var o=0;o<t.length;o++)t[o]=te(t[o],e,n,r);return n(i,t),t}function ne(t){return t.ja===Ct?t.toJSON():function(t){switch(typeof t){case"number":return isFinite(t)?t:String(t);case"object":if(t)if(Array.isArray(t)){if(128&St(t))return Dt(t=Array.prototype.slice.call(t)),t}else{if(k&&null!=t&&t instanceof Uint8Array)return C(t);if(t instanceof ct){var e=t.V;return null==e?"":"string"==typeof e?e:t.V=C(e)}}}return t}(t)}function re(t,e){128&t&&Dt(e)}function ie(t,e,n){if(n=void 0===n?Nt:n,null!=t){if(k&&t instanceof Uint8Array)return t.length?new ct(new Uint8Array(t),D):lt();if(Array.isArray(t)){var r=St(t);return 2&r?t:!e||32&r||!(16&r||0===r)?(4&(e=St(t=ee(t,ie,4&r?Nt:n,!0)))&&2&e&&Object.freeze(t),t):(It(t,2|r),t)}return t.ja===Ct?ae(t):t}}function oe(t,e,n,r,i,o,a){if(t=t.h&&t.h[n]){if(2&(r=St(t))?r=t:(Nt(r,o=R(t,ae)),Object.freeze(o),r=o),Ht(e),a=null==r?Ut:Lt([]),null!=r){for(o=!!r.length,t=0;t<r.length;t++){var u=r[t];o=o&&!(2&St(u.o)),a[t]=u.o}o=1|(o?8:0),((t=St(a))&o)!==o&&(Object.isFrozen(a)&&(a=Array.prototype.slice.call(a)),It(a,t|o)),e.h||(e.h={}),e.h[n]=r}else e.h&&(e.h[n]=void 0);Xt(e,n,a,i)}else Wt(e,n,ie(r,o,a),i)}function ae(t){return 2&St(t.o)||Ft((t=ue(t,!0)).o,2),t}function ue(t,e){var n=t.o,r=[];Ft(r,16);var i=t.constructor.h;if(i&&r.push(i),i=t.B){r.length=n.length,r.fill(void 0,r.length,n.length);var o={};r[r.length-1]=o}128&St(n)&&Dt(r),e=e||t.C()?Nt:kt,o=t.constructor,Mt=r,r=new o(r),Mt=void 0,t.R&&(r.R=t.R.slice()),o=!!(16&St(n));for(var a=i?n.length-1:n.length,u=0;u<a;u++)oe(t,r,u-t.G,n[u],!1,o,e);if(i)for(var s in i)oe(t,r,+s,i[s],!0,o,e);return r}function se(t,e,n){null==t&&(t=Mt),Mt=void 0;var r,i=this.constructor.i||0,o=0<i,a=this.constructor.h,u=!1;if(null==t){var s=48,c=!0;o&&(i=0,s|=128),It(t=a?[a]:[],s)}else{if(!Array.isArray(t))throw Error();if(a&&a!==t[0])throw Error();var l=s=Ft(t,0);if((c=!!(16&l))&&((u=!!(32&l))||(l|=32)),o){if(128&l)i=0;else if(0<t.length){var f=t[t.length-1];if(Pt(f)&&"g"in f){i=0,l|=128,delete f.g;var h,p=!0;for(h in f){p=!1;break}p&&t.pop()}}}else if(128&l)throw Error();s!==l&&It(t,l)}if(this.G=(a?0:-1)-i,this.h=void 0,this.o=t,i=(a=this.o.length)-1,a&&Pt(a=this.o[i])?(this.B=a,this.i=i-this.G):void 0!==e&&-1<e?(this.i=Math.max(e,i+1-this.G),this.B=void 0):this.i=Number.MAX_VALUE,!o&&this.B&&"g"in this.B)throw Error('Unexpected "g" flag in sparse object of message that is not a group type.');if(n)for(e=c&&!u&&!0,o=this.i,c=0;c<n.length;c++)(u=n[c])<o?(i=t[u+=this.G])?ce(i,e):t[u]=Ut:(r||(r=Gt(this)),(i=r[u])?ce(i,e):r[u]=Ut)}function ce(t,e){if(Array.isArray(t)){var n=St(t),r=1;!e||2&n||(r|=16),(n&r)!==r&&It(t,n|r)}}function le(t,e,n){if(n){var r,i={};for(r in n){var o=n[r],a=o.ra;a||(i.J=o.xa||o.oa.W,o.ia?(i.aa=me(o.ia),a=function(t){return function(e,n,r){return t.J(e,n,r,t.aa)}}(i)):o.ka?(i.Z=be(o.da.P,o.ka),a=function(t){return function(e,n,r){return t.J(e,n,r,t.Z)}}(i)):a=i.J,o.ra=a),a(e,t,o.da),i={J:i.J,aa:i.aa,Z:i.Z}}}!function(t,e){if(e=e.R){Ot(t,t.h.end());for(var n=0;n<e.length;n++)Ot(t,ft(e[n])||H())}}(e,t)}It(Bt,23),Ut=Object.freeze(Bt),se.prototype.toJSON=function(){return ee(this.o,ne,re)},se.prototype.C=function(){return!!(2&St(this.o))},se.prototype.ja=Ct,se.prototype.toString=function(){return this.o.toString()};var fe=Symbol();function he(t,e,n){return t[fe]||(t[fe]=function(t,r){return e(t,r,n)})}function pe(t){var e=t[fe];if(!e){var n=Ie(t);e=function(t,e){return Le(t,e,n)},t[fe]=e}return e}function ve(t){var e=function(t){var e=t.ia;return e?pe(e):(e=t.wa)?he(t.da.P,e,t.ka):void 0}(t),n=t.da,r=t.oa.U;return e?function(t,i){return r(t,i,n,e)}:function(t,e){return r(t,e,n)}}function de(t,e){var n=t[e];return"function"==typeof n&&0===n.length&&(n=n(),t[e]=n),Array.isArray(n)&&(Oe in n||we in n||0<n.length&&"function"==typeof n[0])?n:void 0}function ye(t,e,n,r,i,o){e.P=t[0];var a=1;if(t.length>a&&"number"!=typeof t[a]){var u=t[a++];n(e,u)}for(;a<t.length;){n=t[a++];for(var s=a+1;s<t.length&&"number"!=typeof t[s];)s++;switch(u=t[a++],s-=a){case 0:r(e,n,u);break;case 1:(s=de(t,a))?(a++,i(e,n,u,s)):r(e,n,u,t[a++]);break;case 2:i(e,n,u,s=de(t,s=a++),t[a++]);break;case 3:o(e,n,u,t[a++],t[a++],t[a++]);break;case 4:o(e,n,u,t[a++],t[a++],t[a++],t[a++]);break;default:throw Error("unexpected number of binary field arguments: "+s)}}return e}var ge=Symbol();function me(t){var e=t[ge];if(!e){var n=je(t);e=function(t,e){return ke(t,e,n)},t[ge]=e}return e}function be(t,e){var n=t[ge];return n||(n=function(t,n){return le(t,n,e)},t[ge]=n),n}var we=Symbol();function Ee(t,e){t.push(e)}function Ae(t,e,n){t.push(e,n.W)}function _e(t,e,n,r){var i=me(r),o=je(r).P,a=n.W;t.push(e,function(t,e,n){return a(t,e,n,o,i)})}function Te(t,e,n,r,i,o){var a=be(r,o),u=n.W;t.push(e,function(t,e,n){return u(t,e,n,r,a)})}function je(t){var e=t[we];return e||(e=ye(t,t[we]=[],Ee,Ae,_e,Te),Oe in t&&we in t&&(t.length=0),e)}var Oe=Symbol();function xe(t,e){t[0]=e}function Fe(t,e,n,r){var i=n.U;t[e]=r?function(t,e,n){return i(t,e,n,r)}:i}function Re(t,e,n,r,i){var o=n.U,a=pe(r),u=Ie(r).P;t[e]=function(t,e,n){return o(t,e,n,u,a,i)}}function Se(t,e,n,r,i,o,a){var u=n.U,s=he(r,i,o);t[e]=function(t,e,n){return u(t,e,n,r,s,a)}}function Ie(t){var e=t[Oe];return e||(e=ye(t,t[Oe]={},xe,Fe,Re,Se),Oe in t&&we in t&&(t.length=0),e)}function Le(t,e,n){for(;At(e)&&4!=e.i;){var r=e.l,i=n[r];if(!i){var o=n[0];o&&(o=o[r])&&(i=n[r]=ve(o))}if(!i||!i(e,t,r)){r=t,o=(i=e).j,_t(i);var a=i;if(!a.ca){if(i=a.h.h-o,a.h.h=o,a=a.h,0==i)i=lt();else{if(o=yt(a,i),a.S&&a.m)i=a.i.subarray(o,o+i);else{a=a.i;var u=o;i=u===(i=o+i)?H():G?a.slice(u,i):new Uint8Array(a.subarray(u,i))}i=0==i.length?lt():new ct(i,D)}(o=r.R)?o.push(i):r.R=[i]}}}return t}function ke(t,e,n){for(var r=n.length,i=1==r%2,o=i?1:0;o<r;o+=2)(0,n[o+1])(e,t,n[o]);le(t,e,i?n[0]:void 0)}function Ne(t,e){return{U:t,W:e}}var Ce=Ne(function(t,e,n){if(5!==t.i)return!1;var r=(t=t.h).i,i=t.h,o=r[i],a=r[i+1],u=r[i+2];return r=r[i+3],vt(t,t.h+4),t=2*((a=(o|a<<8|u<<16|r<<24)>>>0)>>31)+1,o=a>>>23&255,a&=8388607,Wt(e,n,255==o?a?NaN:1/0*t:0==o?t*Math.pow(2,-149)*a:t*Math.pow(2,o-150)*(a+Math.pow(2,23))),!0},function(t,e,n){if(null!=(e=zt(e,n))){wt(t.h,8*n+5),t=t.h;var r=+e;0===r?0<1/r?Y=W=0:(W=0,Y=2147483648):isNaN(r)?(W=0,Y=2147483647):34028234663852886e22<(r=(n=0>r?-2147483648:0)?-r:r)?(W=0,Y=(2139095040|n)>>>0):11754943508222875e-54>r?(r=Math.round(r/Math.pow(2,-149)),W=0,Y=(n|r)>>>0):(e=Math.floor(Math.log(r)/Math.LN2),r*=Math.pow(2,-e),16777216<=(r=Math.round(8388608*r))&&++e,W=0,Y=(n|e+127<<23|8388607&r)>>>0),n=Y,t.h.push(n>>>0&255),t.h.push(n>>>8&255),t.h.push(n>>>16&255),t.h.push(n>>>24&255)}}),Pe=Ne(function(t,e,n){if(0!==t.i)return!1;var r=t.h,i=0,o=t=0,a=r.i,u=r.h;do{var s=a[u++];i|=(127&s)<<o,o+=7}while(32>o&&128&s);for(32<o&&(t|=(127&s)>>4),o=3;32>o&&128&s;o+=7)t|=(127&(s=a[u++]))<<o;if(vt(r,u),!(128>s))throw $();return r=i>>>0,(t=2147483648&(s=t>>>0))&&(s=~s>>>0,0==(r=1+~r>>>0)&&(s=s+1>>>0)),r=4294967296*s+(r>>>0),Wt(e,n,t?-r:r),!0},function(t,e,n){null!=(e=Yt(e,n))&&("string"==typeof e&&q(e),null!=e&&(wt(t.h,8*n),"number"==typeof e?(t=t.h,X(e),bt(t,Y,W)):(n=q(e),bt(t.h,n.i,n.h))))}),Ue=Ne(function(t,e,n){return 0===t.i&&(Wt(e,n,dt(t.h)),!0)},function(t,e,n){if(null!=(e=Yt(e,n))&&null!=e)if(wt(t.h,8*n),t=t.h,0<=(n=e))wt(t,n);else{for(e=0;9>e;e++)t.h.push(127&n|128),n>>=7;t.h.push(1)}}),Me=Ne(function(t,e,n){if(2!==t.i)return!1;var r=dt(t.h)>>>0,i=yt(t=t.h,r);if(t=t.i,at){var o,a=t;(o=nt)||(o=nt=new TextDecoder("utf-8",{fatal:!0})),t=i+r,a=0===i&&t===a.length?a:a.subarray(i,t);try{var u=o.decode(a)}catch(f){if(void 0===ot){try{o.decode(new Uint8Array([128]))}catch(h){}try{o.decode(new Uint8Array([97])),ot=!0}catch(h){ot=!1}}throw!ot&&(nt=void 0),f}}else{r=(u=i)+r,i=[];for(var s,c,l=null;u<r;)128>(s=t[u++])?i.push(s):224>s?u>=r?tt():(c=t[u++],194>s||128!=(192&c)?(u--,tt()):i.push((31&s)<<6|63&c)):240>s?u>=r-1?tt():128!=(192&(c=t[u++]))||224===s&&160>c||237===s&&160<=c||128!=(192&(a=t[u++]))?(u--,tt()):i.push((15&s)<<12|(63&c)<<6|63&a):244>=s?u>=r-2?tt():128!=(192&(c=t[u++]))||c-144+(s<<28)>>30||128!=(192&(a=t[u++]))||128!=(192&(o=t[u++]))?(u--,tt()):(s=(7&s)<<18|(63&c)<<12|(63&a)<<6|63&o,s-=65536,i.push(55296+(s>>10&1023),56320+(1023&s))):tt(),8192<=i.length&&(l=et(l,i),i.length=0);u=et(l,i)}return Wt(e,n,u),!0},function(t,e,n){if(null!=(e=Yt(e,n))){var r=!1;if(r=void 0!==r&&r,ut){if(r&&/(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])/.test(e))throw Error("Found an unpaired surrogate");e=(rt||(rt=new TextEncoder)).encode(e)}else{for(var i=0,o=new Uint8Array(3*e.length),a=0;a<e.length;a++){var u=e.charCodeAt(a);if(128>u)o[i++]=u;else{if(2048>u)o[i++]=u>>6|192;else{if(55296<=u&&57343>=u){if(56319>=u&&a<e.length){var s=e.charCodeAt(++a);if(56320<=s&&57343>=s){u=1024*(u-55296)+s-56320+65536,o[i++]=u>>18|240,o[i++]=u>>12&63|128,o[i++]=u>>6&63|128,o[i++]=63&u|128;continue}a--}if(r)throw Error("Found an unpaired surrogate");u=65533}o[i++]=u>>12|224,o[i++]=u>>6&63|128}o[i++]=63&u|128}}e=i===o.length?o:o.subarray(0,i)}wt(t.h,8*n+2),wt(t.h,e.length),Ot(t,t.h.end()),Ot(t,e)}}),Be=Ne(function(t,e,n,r,i){if(2!==t.i)return!1;e=Zt(e,n,r),n=t.h.j,r=dt(t.h)>>>0;var o=t.h.h+r,a=o-n;if(0>=a&&(t.h.j=o,i(e,t,void 0,void 0,void 0),a=o-t.h.h),a)throw Error("Message parsing ended unexpectedly. Expected to read "+r+" bytes, instead read "+(r-a)+" bytes, either the data ended unexpectedly or the message misreported its own length");return t.h.h=o,t.h.j=n,!0},function(t,e,n,r,i){if(null!=(e=Jt(e,r,n)))for(r=0;r<e.length;r++){var o=t;wt(o.h,8*n+2);var a=o.h.end();Ot(o,a),a.push(o.i),o=a,i(e[r],t),a=t;var u=o.pop();for(u=a.i+a.h.length()-u;127<u;)o.push(127&u|128),u>>>=7,a.i++;o.push(u),a.i++}});function He(t){return function(e,n){t:{if(Tt.length){var r=Tt.pop();r.setOptions(n),pt(r.h,e,n),e=r}else e=new Et(e,n);try{var i=Ie(t),o=Le(new i.P,e,i);break t}finally{(i=e.h).i=null,i.m=!1,i.l=0,i.j=0,i.h=0,i.S=!1,e.l=-1,e.i=-1,100>Tt.length&&Tt.push(e)}o=void 0}return o}}function De(t){return function(){var e=new jt;ke(this,e,je(t)),Ot(e,e.h.end());for(var n=new Uint8Array(e.i),r=e.j,i=r.length,o=0,a=0;a<i;a++){var u=r[a];n.set(u,o),o+=u.length}return e.j=[n],n}}function Ge(t){se.call(this,t)}d(Ge,se);var Ye=[Ge,1,Ue,2,Ce,3,Me,4,Me];function We(t){se.call(this,t,-1,Xe)}Ge.prototype.l=De(Ye),d(We,se),We.prototype.addClassification=function(t,e){return Zt(this,1,Ge,t,e),this};var Xe=[1],Ke=He([We,1,Be,Ye]);function ze(t){se.call(this,t)}d(ze,se);var Ve=[ze,1,Ce,2,Ce,3,Ce,4,Ce,5,Ce];function Je(t){se.call(this,t,-1,qe)}ze.prototype.l=De(Ve),d(Je,se);var qe=[1],Ze=He([Je,1,Be,Ve]);function $e(t){se.call(this,t)}d($e,se);var Qe=[$e,1,Ce,2,Ce,3,Ce,4,Ce,5,Ce,6,Pe],tn=He(Qe);function en(t,e,n){if(n=t.createShader(0===n?t.VERTEX_SHADER:t.FRAGMENT_SHADER),t.shaderSource(n,e),t.compileShader(n),!t.getShaderParameter(n,t.COMPILE_STATUS))throw Error("Could not compile WebGL shader.\n\n"+t.getShaderInfoLog(n));return n}function nn(t){return Jt(t,Ge,1).map(function(t){var e=Yt(t,1);return{index:null==e?0:e,qa:Qt(t,2),label:null!=Yt(t,3)?$t(Yt(t,3),""):void 0,displayName:null!=Yt(t,4)?$t(Yt(t,4),""):void 0}})}function rn(t){return{x:Qt(t,1),y:Qt(t,2),z:Qt(t,3),visibility:null!=zt(t,4)?Qt(t,4):void 0}}function on(t){return Jt(Ze(t),ze,1).map(rn)}function an(t,e){this.i=t,this.h=e,this.m=0}function un(t,e,n){return function(t,e){var n=t.h;if(void 0===t.s){var r=en(n,"\n  attribute vec2 aVertex;\n  attribute vec2 aTex;\n  varying vec2 vTex;\n  void main(void) {\n    gl_Position = vec4(aVertex, 0.0, 1.0);\n    vTex = aTex;\n  }",0),i=en(n,"\n  precision mediump float;\n  varying vec2 vTex;\n  uniform sampler2D sampler0;\n  void main(){\n    gl_FragColor = texture2D(sampler0, vTex);\n  }",1),o=n.createProgram();if(n.attachShader(o,r),n.attachShader(o,i),n.linkProgram(o),!n.getProgramParameter(o,n.LINK_STATUS))throw Error("Could not compile WebGL program.\n\n"+n.getProgramInfoLog(o));r=t.s=o,n.useProgram(r),i=n.getUniformLocation(r,"sampler0"),t.l={O:n.getAttribLocation(r,"aVertex"),N:n.getAttribLocation(r,"aTex"),ya:i},t.v=n.createBuffer(),n.bindBuffer(n.ARRAY_BUFFER,t.v),n.enableVertexAttribArray(t.l.O),n.vertexAttribPointer(t.l.O,2,n.FLOAT,!1,0,0),n.bufferData(n.ARRAY_BUFFER,new Float32Array([-1,-1,-1,1,1,1,1,-1]),n.STATIC_DRAW),n.bindBuffer(n.ARRAY_BUFFER,null),t.u=n.createBuffer(),n.bindBuffer(n.ARRAY_BUFFER,t.u),n.enableVertexAttribArray(t.l.N),n.vertexAttribPointer(t.l.N,2,n.FLOAT,!1,0,0),n.bufferData(n.ARRAY_BUFFER,new Float32Array([0,1,0,0,1,0,1,1]),n.STATIC_DRAW),n.bindBuffer(n.ARRAY_BUFFER,null),n.uniform1i(i,0)}r=t.l,n.useProgram(t.s),n.canvas.width=e.width,n.canvas.height=e.height,n.viewport(0,0,e.width,e.height),n.activeTexture(n.TEXTURE0),t.i.bindTexture2d(e.glName),n.enableVertexAttribArray(r.O),n.bindBuffer(n.ARRAY_BUFFER,t.v),n.vertexAttribPointer(r.O,2,n.FLOAT,!1,0,0),n.enableVertexAttribArray(r.N),n.bindBuffer(n.ARRAY_BUFFER,t.u),n.vertexAttribPointer(r.N,2,n.FLOAT,!1,0,0),n.bindFramebuffer(n.DRAW_FRAMEBUFFER?n.DRAW_FRAMEBUFFER:n.FRAMEBUFFER,null),n.clearColor(0,0,0,0),n.clear(n.COLOR_BUFFER_BIT),n.colorMask(!0,!0,!0,!0),n.drawArrays(n.TRIANGLE_FAN,0,4),n.disableVertexAttribArray(r.O),n.disableVertexAttribArray(r.N),n.bindBuffer(n.ARRAY_BUFFER,null),t.i.bindTexture2d(0)}(t,e),"function"==typeof t.h.canvas.transferToImageBitmap?Promise.resolve(t.h.canvas.transferToImageBitmap()):n?Promise.resolve(t.h.canvas):"function"==typeof createImageBitmap?createImageBitmap(t.h.canvas):(void 0===t.j&&(t.j=document.createElement("canvas")),new Promise(function(e){t.j.height=t.h.canvas.height,t.j.width=t.h.canvas.width,t.j.getContext("2d",{}).drawImage(t.h.canvas,0,0,t.h.canvas.width,t.h.canvas.height),e(t.j)}))}function sn(t){this.h=t}$e.prototype.l=De(Qe);var cn=new Uint8Array([0,97,115,109,1,0,0,0,1,4,1,96,0,0,3,2,1,0,10,9,1,7,0,65,0,253,15,26,11]);function ln(t,e){return e+t}function fn(t,e){window[t]=e}function hn(t){if(this.h=t,this.listeners={},this.l={},this.L={},this.s={},this.v={},this.M=this.u=this.ga=!0,this.I=Promise.resolve(),this.fa="",this.D={},this.locateFile=t&&t.locateFile||ln,"object"==typeof window)var e=window.location.pathname.toString().substring(0,window.location.pathname.toString().lastIndexOf("/"))+"/";else{if("undefined"==typeof location)throw Error("solutions can only be loaded on a web page or in a web worker");e=location.pathname.toString().substring(0,location.pathname.toString().lastIndexOf("/"))+"/"}if(this.ha=e,t.options)for(var n=(e=u(Object.keys(t.options))).next();!n.done;n=e.next()){n=n.value;var r=t.options[n].default;void 0!==r&&(this.l[n]="function"==typeof r?r():r)}}function pn(t,e){var n,r;return T(function(i){return e in t.L?i.return(t.L[e]):(n=t.locateFile(e,""),r=fetch(n).then(function(t){return t.arrayBuffer()}),t.L[e]=r,i.return(r))})}function vn(t,e){for(var n=e.name||"$",r=[].concat(s(e.wants)),i=new t.i.StringList,o=u(e.wants),a=o.next();!a.done;a=o.next())i.push_back(a.value);o=t.i.PacketListener.implement({onResults:function(i){for(var o={},a=0;a<e.wants.length;++a)o[r[a]]=i.get(a);var s=t.listeners[n];s&&(t.I=function(t,e,n){var r,i,o,a,s,c,l,f,h,p,v,d,y,g;return T(function(m){switch(m.h){case 1:if(!n)return m.return(e);for(r={},i=0,o=u(Object.keys(n)),a=o.next();!a.done;a=o.next())s=a.value,"string"!=typeof(c=n[s])&&"texture"===c.type&&void 0!==e[c.stream]&&++i;1<i&&(t.M=!1),l=u(Object.keys(n)),a=l.next();case 2:if(a.done){m.h=4;break}if(f=a.value,"string"==typeof(h=n[f]))return y=r,g=f,b(m,function(t,e,n){var r;return T(function(i){return"number"==typeof n||n instanceof Uint8Array||n instanceof t.i.Uint8BlobList?i.return(n):n instanceof t.i.Texture2dDataOut?((r=t.v[e])||(r=new an(t.i,t.K),t.v[e]=r),i.return(un(r,n,t.M))):i.return(void 0)})}(t,f,e[h]),14);if(p=e[h.stream],"detection_list"===h.type){if(p){for(var w=p.getRectList(),E=p.getLandmarksList(),A=p.getClassificationsList(),_=[],j=0;j<w.size();++j){var O=tn(w.get(j)),x=void 0;x=void 0===x?0:x,O={la:{sa:Qt(O,1),ta:Qt(O,2),height:Qt(O,3),width:Qt(O,4),rotation:Qt(O,5,0),pa:$t(Yt(O,6),x)},ea:on(E.get(j)),ba:nn(Ke(A.get(j)))},_.push(O)}w=_}else w=[];r[f]=w,m.h=7;break}if("proto_list"===h.type){if(p){for(w=Array(p.size()),E=0;E<p.size();E++)w[E]=p.get(E);p.delete()}else w=[];r[f]=w,m.h=7;break}if(void 0===p){m.h=3;break}if("float_list"===h.type){r[f]=p,m.h=7;break}if("proto"===h.type){r[f]=p,m.h=7;break}if("texture"!==h.type)throw Error("Unknown output config type: '"+h.type+"'");return(v=t.v[f])||(v=new an(t.i,t.K),t.v[f]=v),b(m,un(v,p,t.M),13);case 13:d=m.i,r[f]=d;case 7:h.transform&&r[f]&&(r[f]=h.transform(r[f])),m.h=3;break;case 14:y[g]=m.i;case 3:a=l.next(),m.h=2;break;case 4:return m.return(r)}})}(t,o,e.outs).then(function(n){n=s(n);for(var i=0;i<e.wants.length;++i){var a=o[r[i]];"object"==typeof a&&a.hasOwnProperty&&a.hasOwnProperty("delete")&&a.delete()}n&&(t.I=n)}))}}),t.j.attachMultiListener(i,o),i.delete()}function dn(t){switch(void 0===t&&(t=0),t){case 1:return"pose_landmark_full.tflite";case 2:return"pose_landmark_heavy.tflite";default:return"pose_landmark_lite.tflite"}}function yn(t){var e=this;t=t||{},this.h=new hn({locateFile:t.locateFile,files:function(t){return[{url:"pose_solution_packed_assets_loader.js"},{simd:!1,url:"pose_solution_wasm_bin.js"},{simd:!0,url:"pose_solution_simd_wasm_bin.js"},{data:!0,url:dn(t.modelComplexity)}]},graph:{url:"pose_web.binarypb"},listeners:[{wants:["pose_landmarks","world_landmarks","segmentation_mask","image_transformed"],outs:{image:{type:"texture",stream:"image_transformed"},poseLandmarks:{type:"proto",stream:"pose_landmarks",transform:on},poseWorldLandmarks:{type:"proto",stream:"world_landmarks",transform:on},segmentationMask:{type:"texture",stream:"segmentation_mask"}}}],inputs:{image:{type:"video",stream:"input_frames_gpu"}},options:{useCpuInference:{type:0,graphOptionXref:{calculatorType:"InferenceCalculator",fieldName:"use_cpu_inference"},default:"object"==typeof window&&void 0!==window.navigator&&("iPad Simulator;iPhone Simulator;iPod Simulator;iPad;iPhone;iPod".split(";").includes(navigator.platform)||navigator.userAgent.includes("Mac")&&"ontouchend"in document)},selfieMode:{type:0,graphOptionXref:{calculatorType:"GlScalerCalculator",calculatorIndex:1,fieldName:"flip_horizontal"}},modelComplexity:{type:1,graphOptionXref:{calculatorType:"ConstantSidePacketCalculator",calculatorName:"ConstantSidePacketCalculatorModelComplexity",fieldName:"int_value"},onChange:function(t){var n,r,i;return T(function(o){return 1==o.h?(n=dn(t),r="third_party/mediapipe/modules/pose_landmark/"+n,b(o,pn(e.h,n),2)):(i=o.i,e.h.overrideFile(r,i),o.return(!0))})}},smoothLandmarks:{type:0,graphOptionXref:{calculatorType:"ConstantSidePacketCalculator",calculatorName:"ConstantSidePacketCalculatorSmoothLandmarks",fieldName:"bool_value"}},enableSegmentation:{type:0,graphOptionXref:{calculatorType:"ConstantSidePacketCalculator",calculatorName:"ConstantSidePacketCalculatorEnableSegmentation",fieldName:"bool_value"}},smoothSegmentation:{type:0,graphOptionXref:{calculatorType:"ConstantSidePacketCalculator",calculatorName:"ConstantSidePacketCalculatorSmoothSegmentation",fieldName:"bool_value"}},minDetectionConfidence:{type:1,graphOptionXref:{calculatorType:"TensorsToDetectionsCalculator",calculatorName:"poselandmarkgpu__posedetectiongpu__TensorsToDetectionsCalculator",fieldName:"min_score_thresh"}},minTrackingConfidence:{type:1,graphOptionXref:{calculatorType:"ThresholdingCalculator",calculatorName:"poselandmarkgpu__poselandmarkbyroigpu__tensorstoposelandmarksandsegmentation__ThresholdingCalculator",fieldName:"threshold"}}}})}(t=hn.prototype).close=function(){return this.j&&this.j.delete(),Promise.resolve()},t.reset=function(){var t=this;return T(function(e){t.j&&(t.j.reset(),t.s={},t.v={}),e.h=0})},t.setOptions=function(t,e){var n=this;if(e=e||this.h.options){for(var r=[],i=[],o={},a=u(Object.keys(t)),s=a.next();!s.done;o={X:o.X,Y:o.Y},s=a.next())if(!((s=s.value)in this.l)||this.l[s]!==t[s]){this.l[s]=t[s];var c=e[s];void 0!==c&&(c.onChange&&(o.X=c.onChange,o.Y=t[s],r.push(function(t){return function(){return T(function(e){if(1==e.h)return b(e,t.X(t.Y),2);!0===e.i&&(n.u=!0),e.h=0})}}(o))),c.graphOptionXref&&(s=Object.assign({},{calculatorName:"",calculatorIndex:0},c.graphOptionXref,{valueNumber:1===c.type?t[s]:0,valueBoolean:0===c.type&&t[s],valueString:2===c.type?t[s]:""}),i.push(s)))}0===r.length&&0===i.length||(this.u=!0,this.H=(void 0===this.H?[]:this.H).concat(i),this.F=(void 0===this.F?[]:this.F).concat(r))}},t.initialize=function(){var t=this;return T(function(e){return 1==e.h?b(e,function(t){var e,n,r,i,o,a,u,c,l,f,h;return T(function(p){switch(p.h){case 1:return t.ga?(e=void 0===t.h.files?[]:"function"==typeof t.h.files?t.h.files(t.l):t.h.files,b(p,T(function(t){switch(t.h){case 1:return t.s=2,b(t,WebAssembly.instantiate(cn),4);case 4:t.h=3,t.s=0;break;case 2:return t.s=0,t.l=null,t.return(!1);case 3:return t.return(!0)}}),2)):p.return();case 2:if(n=p.i,"object"==typeof window)return fn("createMediapipeSolutionsWasm",{locateFile:t.locateFile}),fn("createMediapipeSolutionsPackedAssets",{locateFile:t.locateFile}),a=e.filter(function(t){return void 0!==t.data}),u=e.filter(function(t){return void 0===t.data}),c=Promise.all(a.map(function(e){var n=pn(t,e.url);if(void 0!==e.path){var r=e.path;n=n.then(function(e){return t.overrideFile(r,e),Promise.resolve(e)})}return n})),l=Promise.all(u.map(function(e){return void 0===e.simd||e.simd&&n||!e.simd&&!n?function(t){var e=document.createElement("script");return e.setAttribute("src",t),e.setAttribute("crossorigin","anonymous"),new Promise(function(t){e.addEventListener("load",function(){t()},!1),e.addEventListener("error",function(){t()},!1),document.body.appendChild(e)})}(t.locateFile(e.url,t.ha)):Promise.resolve()})).then(function(){var e,n,r;return T(function(i){if(1==i.h)return e=window.createMediapipeSolutionsWasm,n=window.createMediapipeSolutionsPackedAssets,r=t,b(i,e(n),2);r.i=i.i,i.h=0})}),f=T(function(e){return t.h.graph&&t.h.graph.url?e=b(e,pn(t,t.h.graph.url),0):(e.h=0,e=void 0),e}),b(p,Promise.all([l,c,f]),7);if("function"!=typeof importScripts)throw Error("solutions can only be loaded on a web page or in a web worker");return r=e.filter(function(t){return void 0===t.simd||t.simd&&n||!t.simd&&!n}).map(function(e){return t.locateFile(e.url,t.ha)}),importScripts.apply(null,s(r)),i=t,b(p,createMediapipeSolutionsWasm(Module),6);case 6:i.i=p.i,t.m=new OffscreenCanvas(1,1),t.i.canvas=t.m,o=t.i.GL.createContext(t.m,{antialias:!1,alpha:!1,va:"undefined"!=typeof WebGL2RenderingContext?2:1}),t.i.GL.makeContextCurrent(o),p.h=4;break;case 7:if(t.m=document.createElement("canvas"),!(h=t.m.getContext("webgl2",{}))&&!(h=t.m.getContext("webgl",{})))return alert("Failed to create WebGL canvas context when passing video frame."),p.return();t.K=h,t.i.canvas=t.m,t.i.createContext(t.m,!0,!0,{});case 4:t.j=new t.i.SolutionWasm,t.ga=!1,p.h=0}})}(t),2):3!=e.h?b(e,function(t){var e,n,r,i,o,a,s,c;return T(function(l){if(1==l.h)return t.h.graph&&t.h.graph.url&&t.fa===t.h.graph.url?l.return():(t.u=!0,t.h.graph&&t.h.graph.url?(t.fa=t.h.graph.url,b(l,pn(t,t.h.graph.url),3)):void(l.h=2));for(2!=l.h&&(e=l.i,t.j.loadGraph(e)),n=u(Object.keys(t.D)),r=n.next();!r.done;r=n.next())i=r.value,t.j.overrideFile(i,t.D[i]);if(t.D={},t.h.listeners)for(o=u(t.h.listeners),a=o.next();!a.done;a=o.next())s=a.value,vn(t,s);c=t.l,t.l={},t.setOptions(c),l.h=0})}(t),3):b(e,function(t){var e,n,r,i,o,a;return T(function(s){switch(s.h){case 1:if(!t.u)return s.return();if(!t.F){s.h=2;break}e=u(t.F),n=e.next();case 3:if(n.done){s.h=5;break}return b(s,(0,n.value)(),4);case 4:n=e.next(),s.h=3;break;case 5:t.F=void 0;case 2:if(t.H){for(r=new t.i.GraphOptionChangeRequestList,i=u(t.H),o=i.next();!o.done;o=i.next())a=o.value,r.push_back(a);t.j.changeOptions(r),r.delete(),t.H=void 0}t.u=!1,s.h=0}})}(t),0)})},t.overrideFile=function(t,e){this.j?this.j.overrideFile(t,e):this.D[t]=e},t.clearOverriddenFiles=function(){this.D={},this.j&&this.j.clearOverriddenFiles()},t.send=function(t,e){var n,r,i,o,a,s,c,l,f,h=this;return T(function(p){switch(p.h){case 1:return h.h.inputs?(n=1e3*(null==e?performance.now():e),b(p,h.I,2)):p.return();case 2:return b(p,h.initialize(),3);case 3:for(r=new h.i.PacketDataList,i=u(Object.keys(t)),o=i.next();!o.done;o=i.next())if(a=o.value,s=h.h.inputs[a]){t:{var v=t[a];switch(s.type){case"video":var d=h.s[s.stream];if(d||(d=new an(h.i,h.K),h.s[s.stream]=d),0===d.m&&(d.m=d.i.createTexture()),"undefined"!=typeof HTMLVideoElement&&v instanceof HTMLVideoElement)var y=v.videoWidth,g=v.videoHeight;else"undefined"!=typeof HTMLImageElement&&v instanceof HTMLImageElement?(y=v.naturalWidth,g=v.naturalHeight):(y=v.width,g=v.height);g={glName:d.m,width:y,height:g},(y=d.h).canvas.width=g.width,y.canvas.height=g.height,y.activeTexture(y.TEXTURE0),d.i.bindTexture2d(d.m),y.texImage2D(y.TEXTURE_2D,0,y.RGBA,y.RGBA,y.UNSIGNED_BYTE,v),d.i.bindTexture2d(0),d=g;break t;case"detections":for((d=h.s[s.stream])||(d=new sn(h.i),h.s[s.stream]=d),d.data||(d.data=new d.h.DetectionListData),d.data.reset(v.length),g=0;g<v.length;++g){y=v[g];var m=d.data,w=m.setBoundingBox,E=g,A=y.la,_=new $e;if(qt(_,1,A.sa),qt(_,2,A.ta),qt(_,3,A.height),qt(_,4,A.width),qt(_,5,A.rotation),Wt(_,6,A.pa),A=_.l(),w.call(m,E,A),y.ea)for(m=0;m<y.ea.length;++m){_=y.ea[m],E=(w=d.data).addNormalizedLandmark,A=g,_=Object.assign({},_,{visibility:_.visibility?_.visibility:0});var T=new ze;qt(T,1,_.x),qt(T,2,_.y),qt(T,3,_.z),_.visibility&&qt(T,4,_.visibility),_=T.l(),E.call(w,A,_)}if(y.ba)for(m=0;m<y.ba.length;++m)E=(w=d.data).addClassification,A=g,_=y.ba[m],qt(T=new Ge,2,_.qa),_.index&&Wt(T,1,_.index),_.label&&Wt(T,3,_.label),_.displayName&&Wt(T,4,_.displayName),_=T.l(),E.call(w,A,_)}d=d.data;break t;default:d={}}}switch(c=d,l=s.stream,s.type){case"video":r.pushTexture2d(Object.assign({},c,{stream:l,timestamp:n}));break;case"detections":(f=c).stream=l,f.timestamp=n,r.pushDetectionList(f);break;default:throw Error("Unknown input config type: '"+s.type+"'")}}return h.j.send(r),b(p,h.I,4);case 4:r.delete(),p.h=0}})},t.onResults=function(t,e){this.listeners[e||"$"]=t},x("Solution",hn),x("OptionType",{BOOL:0,NUMBER:1,ua:2,0:"BOOL",1:"NUMBER",2:"STRING"}),(t=yn.prototype).reset=function(){this.h.reset()},t.close=function(){return this.h.close(),Promise.resolve()},t.onResults=function(t){this.h.onResults(t)},t.initialize=function(){var t=this;return T(function(e){return b(e,t.h.initialize(),0)})},t.send=function(t,e){var n=this;return T(function(r){return b(r,n.h.send(t,e),0)})},t.setOptions=function(t){this.h.setOptions(t)},x("Pose",yn),x("POSE_CONNECTIONS",[[0,1],[1,2],[2,3],[3,7],[0,4],[4,5],[5,6],[6,8],[9,10],[11,12],[11,13],[13,15],[15,17],[15,19],[15,21],[17,19],[12,14],[14,16],[16,18],[16,20],[16,22],[18,20],[11,23],[12,24],[23,24],[23,25],[24,26],[25,27],[26,28],[27,29],[28,30],[29,31],[30,32],[27,31],[28,32]]),x("POSE_LANDMARKS",{NOSE:0,LEFT_EYE_INNER:1,LEFT_EYE:2,LEFT_EYE_OUTER:3,RIGHT_EYE_INNER:4,RIGHT_EYE:5,RIGHT_EYE_OUTER:6,LEFT_EAR:7,RIGHT_EAR:8,LEFT_RIGHT:9,RIGHT_LEFT:10,LEFT_SHOULDER:11,RIGHT_SHOULDER:12,LEFT_ELBOW:13,RIGHT_ELBOW:14,LEFT_WRIST:15,RIGHT_WRIST:16,LEFT_PINKY:17,RIGHT_PINKY:18,LEFT_INDEX:19,RIGHT_INDEX:20,LEFT_THUMB:21,RIGHT_THUMB:22,LEFT_HIP:23,RIGHT_HIP:24,LEFT_KNEE:25,RIGHT_KNEE:26,LEFT_ANKLE:27,RIGHT_ANKLE:28,LEFT_HEEL:29,RIGHT_HEEL:30,LEFT_FOOT_INDEX:31,RIGHT_FOOT_INDEX:32}),x("POSE_LANDMARKS_LEFT",{LEFT_EYE_INNER:1,LEFT_EYE:2,LEFT_EYE_OUTER:3,LEFT_EAR:7,LEFT_RIGHT:9,LEFT_SHOULDER:11,LEFT_ELBOW:13,LEFT_WRIST:15,LEFT_PINKY:17,LEFT_INDEX:19,LEFT_THUMB:21,LEFT_HIP:23,LEFT_KNEE:25,LEFT_ANKLE:27,LEFT_HEEL:29,LEFT_FOOT_INDEX:31}),x("POSE_LANDMARKS_RIGHT",{RIGHT_EYE_INNER:4,RIGHT_EYE:5,RIGHT_EYE_OUTER:6,RIGHT_EAR:8,RIGHT_LEFT:10,RIGHT_SHOULDER:12,RIGHT_ELBOW:14,RIGHT_WRIST:16,RIGHT_PINKY:18,RIGHT_INDEX:20,RIGHT_THUMB:22,RIGHT_HIP:24,RIGHT_KNEE:26,RIGHT_ANKLE:28,RIGHT_HEEL:30,RIGHT_FOOT_INDEX:32}),x("POSE_LANDMARKS_NEUTRAL",{NOSE:0}),x("VERSION","0.5.1675469404")}.call(i)),i}});var e,r,i={},o={}}}});
