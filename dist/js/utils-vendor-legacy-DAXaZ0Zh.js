System.register(["./ui-vendor-legacy-Bf-HC24J.js","./react-vendor-legacy-Byj9Hlz-.js"],function(t,e){"use strict";var n,r,u;return{setters:[t=>{n=t.R},t=>{r=t.r,u=t.g}],execute:function(){const e=t=>{let e;const n=new Set,r=(t,r)=>{const u="function"==typeof t?t(e):t;if(!Object.is(u,e)){const t=e;e=(null!=r?r:"object"!=typeof u||null===u)?u:Object.assign({},e,u),n.forEach(n=>n(e,t))}},u=()=>e,o={setState:r,getState:u,getInitialState:()=>c,subscribe:t=>(n.add(t),()=>n.delete(t)),destroy:()=>{n.clear()}},c=e=t(r,u,o);return o};var o,c,i,s,a={exports:{}},f={},l={exports:{}},v={};function S(){return c||(c=1,l.exports=function(){if(o)return v;o=1;var t=r(),e="function"==typeof Object.is?Object.is:function(t,e){return t===e&&(0!==t||1/t==1/e)||t!=t&&e!=e},n=t.useState,u=t.useEffect,c=t.useLayoutEffect,i=t.useDebugValue;function s(t){var n=t.getSnapshot;t=t.value;try{var r=n();return!e(t,r)}catch(u){return!0}}var a="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(t,e){return e()}:function(t,e){var r=e(),o=n({inst:{value:r,getSnapshot:e}}),a=o[0].inst,f=o[1];return c(function(){a.value=r,a.getSnapshot=e,s(a)&&f({inst:a})},[t,r,e]),u(function(){return s(a)&&f({inst:a}),t(function(){s(a)&&f({inst:a})})},[t]),i(r),r};return v.useSyncExternalStore=void 0!==t.useSyncExternalStore?t.useSyncExternalStore:a,v}()),l.exports}var d=(s||(s=1,a.exports=function(){if(i)return f;i=1;var t=r(),e=S(),n="function"==typeof Object.is?Object.is:function(t,e){return t===e&&(0!==t||1/t==1/e)||t!=t&&e!=e},u=e.useSyncExternalStore,o=t.useRef,c=t.useEffect,s=t.useMemo,a=t.useDebugValue;return f.useSyncExternalStoreWithSelector=function(t,e,r,i,f){var l=o(null);if(null===l.current){var v={hasValue:!1,value:null};l.current=v}else v=l.current;l=s(function(){function t(t){if(!c){if(c=!0,u=t,t=i(t),void 0!==f&&v.hasValue){var e=v.value;if(f(e,t))return o=e}return o=t}if(e=o,n(u,t))return e;var r=i(t);return void 0!==f&&f(e,r)?(u=t,e):(u=t,o=r)}var u,o,c=!1,s=void 0===r?null:r;return[function(){return t(e())},null===s?void 0:function(){return t(s())}]},[e,r,i,f]);var S=u(t,l[0],l[1]);return c(function(){v.hasValue=!0,v.value=S},[S]),a(S),S},f}()),a.exports);const y=u(d),g={LEGACY:!0},{useDebugValue:p}=n,{useSyncExternalStoreWithSelector:b}=y;let x=!1;const E=t=>t,h=t=>{const n="function"==typeof t?(t=>t?e(t):e)(t):t,r=(t,e)=>function(t,e=E,n){"production"!==(g?"production":void 0)&&n&&!x&&(x=!0);const r=b(t.subscribe,t.getState,t.getServerState||t.getInitialState,e,n);return p(r),r}(n,t,e);return Object.assign(r,n),r};t("c",t=>h)}}});
