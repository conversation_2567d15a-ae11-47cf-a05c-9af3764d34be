var e=Object.defineProperty,t=(t,s,a)=>((t,s,a)=>s in t?e(t,s,{enumerable:!0,configurable:!0,writable:!0,value:a}):t[s]=a)(t,"symbol"!=typeof s?s+"":s,a);function s(){import.meta.url,import("_").catch(()=>1),async function*(){}().next()}import{r as a,a as i,g as n}from"./react-vendor-BXA9EqPX.js";import{r,A as o,M as c,S as l,P as d,a as h,b as u,T as m,c as p,d as g,e as y,f,U as x,g as v,h as b,i as S,C as E,j as w,k as N,F as T,l as j,m as R,B as _,n as I,o as k,R as A,p as C,q as D,s as M,Z as O,t as L}from"./ui-vendor-Buq4lKd2.js";import{r as F}from"./phaser-vendor-xnwUt526.js";import{r as P,a as G}from"./media-vendor-BhxCJHOa.js";import{c as B}from"./utils-vendor-BFXTHynT.js";!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver(e=>{for(const s of e)if("childList"===s.type)for(const e of s.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)}).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?t.credentials="include":"anonymous"===e.crossOrigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}();var U,z,H,q={exports:{}},Y={},W=(z||(z=1,q.exports=function(){if(U)return Y;U=1;var e=a(),t=Symbol.for("react.element"),s=Symbol.for("react.fragment"),i=Object.prototype.hasOwnProperty,n=e.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,r={key:!0,ref:!0,__self:!0,__source:!0};function o(e,s,a){var o,c={},l=null,d=null;for(o in void 0!==a&&(l=""+a),void 0!==s.key&&(l=""+s.key),void 0!==s.ref&&(d=s.ref),s)i.call(s,o)&&!r.hasOwnProperty(o)&&(c[o]=s[o]);if(e&&e.defaultProps)for(o in s=e.defaultProps)void 0===c[o]&&(c[o]=s[o]);return{$$typeof:t,type:e,key:l,ref:d,props:c,_owner:n.current}}return Y.Fragment=s,Y.jsx=o,Y.jsxs=o,Y}()),q.exports),V={};const Q=n(function(){if(H)return V;H=1;var e=i();return V.createRoot=e.createRoot,V.hydrateRoot=e.hydrateRoot,V}());var K=F();const X=n(K);var J,Z,$,ee=((J=ee||{}).SEED="seed",J.SPROUT="sprout",J.GROWING="growing",J.MATURE="mature",J.READY_TO_HARVEST="ready_to_harvest",J.HARVESTED="harvested",J),te=((Z=te||{}).KNOWLEDGE_FLOWER="knowledge_flower",Z.STRENGTH_TREE="strength_tree",Z.TIME_VEGGIE="time_veggie",Z.MEDITATION_LOTUS="meditation_lotus",Z.FOCUS_FLOWER="focus_flower",Z.READING_VINE="reading_vine",Z.SOCIAL_FRUIT="social_fruit",Z),se=(($=se||{}).COMMON="common",$.UNCOMMON="uncommon",$.RARE="rare",$.EPIC="epic",$.LEGENDARY="legendary",$);const ae={knowledge_flower:{type:"knowledge_flower",name:"知识花",description:"通过专注学习培育的智慧之花，象征知识的积累",icon:"🌸",baseGrowthTime:18e5,focusMultiplier:1.5,stages:[{stage:"seed",duration:12e4,minFocusScore:50,visualScale:.3,spriteFrame:"knowledge_seed",description:"知识的种子正在孕育"},{stage:"sprout",duration:3e5,minFocusScore:60,visualScale:.5,spriteFrame:"knowledge_sprout",description:"幼苗破土而出，渴望学习"},{stage:"growing",duration:9e5,minFocusScore:70,visualScale:.8,spriteFrame:"knowledge_growing",description:"专注地吸收知识养分"},{stage:"mature",duration:48e4,minFocusScore:80,visualScale:1,spriteFrame:"knowledge_mature",description:"知识之花即将绽放"},{stage:"ready_to_harvest",duration:1/0,minFocusScore:0,visualScale:1.2,spriteFrame:"knowledge_bloom",description:"智慧之花完全盛开，可以收获了！"}],rewards:{baseExp:100,growthPoints:50,specialItems:["wisdom_essence","focus_boost"]},requirements:{minLevel:1}},strength_tree:{type:"strength_tree",name:"力量树",description:"通过运动和锻炼培育的强壮之树，象征身体的力量",icon:"🌳",baseGrowthTime:27e5,focusMultiplier:1.2,stages:[{stage:"seed",duration:18e4,minFocusScore:40,visualScale:.2,spriteFrame:"strength_seed",description:"力量的种子蓄势待发"},{stage:"sprout",duration:48e4,minFocusScore:50,visualScale:.4,spriteFrame:"strength_sprout",description:"强壮的嫩芽正在成长"},{stage:"growing",duration:12e5,minFocusScore:60,visualScale:.7,spriteFrame:"strength_growing",description:"茁壮成长，越来越强壮"},{stage:"mature",duration:84e4,minFocusScore:70,visualScale:1,spriteFrame:"strength_mature",description:"力量之树挺拔伟岸"},{stage:"ready_to_harvest",duration:1/0,minFocusScore:0,visualScale:1.3,spriteFrame:"strength_mighty",description:"强壮的力量之树可以收获了！"}],rewards:{baseExp:120,growthPoints:70,specialItems:["power_essence","endurance_boost"]},requirements:{minLevel:3}},time_veggie:{type:"time_veggie",name:"时间菜",description:"通过合理时间管理培育的蔬菜，象征效率与规律",icon:"🥬",baseGrowthTime:12e5,focusMultiplier:2,stages:[{stage:"seed",duration:6e4,minFocusScore:60,visualScale:.3,spriteFrame:"time_seed",description:"时间的种子分秒必争"},{stage:"sprout",duration:18e4,minFocusScore:70,visualScale:.5,spriteFrame:"time_sprout",description:"效率的嫩芽快速成长"},{stage:"growing",duration:6e5,minFocusScore:80,visualScale:.8,spriteFrame:"time_growing",description:"时间管理显现成效"},{stage:"mature",duration:36e4,minFocusScore:85,visualScale:1,spriteFrame:"time_mature",description:"时间蔬菜即将成熟"},{stage:"ready_to_harvest",duration:1/0,minFocusScore:0,visualScale:1.1,spriteFrame:"time_ripe",description:"高效的时间菜可以收获了！"}],rewards:{baseExp:80,growthPoints:40,specialItems:["efficiency_essence","time_boost"]},requirements:{minLevel:2}},meditation_lotus:{type:"meditation_lotus",name:"冥想莲",description:"通过冥想和内省培育的圣洁莲花，象征内心的平静",icon:"🪷",baseGrowthTime:36e5,focusMultiplier:1.8,stages:[{stage:"seed",duration:3e5,minFocusScore:70,visualScale:.2,spriteFrame:"lotus_seed",description:"平静的莲子在水中沉思"},{stage:"sprout",duration:6e5,minFocusScore:75,visualScale:.4,spriteFrame:"lotus_sprout",description:"莲芽缓缓浮出水面"},{stage:"growing",duration:15e5,minFocusScore:80,visualScale:.7,spriteFrame:"lotus_growing",description:"在宁静中慢慢绽放"},{stage:"mature",duration:12e5,minFocusScore:90,visualScale:1,spriteFrame:"lotus_mature",description:"冥想莲即将达到完美境界"},{stage:"ready_to_harvest",duration:1/0,minFocusScore:0,visualScale:1.4,spriteFrame:"lotus_enlightened",description:"圣洁的冥想莲已达到开悟状态！"}],rewards:{baseExp:150,growthPoints:100,specialItems:["serenity_essence","meditation_boost","enlightenment_crystal"]},requirements:{minLevel:5,prerequisites:["knowledge_flower"]}},focus_flower:{type:"focus_flower",name:"专注花",description:"通过深度专注培育的花朵，象征心无旁骛的专注力",icon:"🌺",baseGrowthTime:15e5,focusMultiplier:2.5,stages:[{stage:"seed",duration:9e4,minFocusScore:75,visualScale:.25,spriteFrame:"focus_seed",description:"专注的种子需要高度集中"},{stage:"sprout",duration:24e4,minFocusScore:80,visualScale:.45,spriteFrame:"focus_sprout",description:"专注力开始凝聚成形"},{stage:"growing",duration:72e4,minFocusScore:85,visualScale:.75,spriteFrame:"focus_growing",description:"专注之花在纯净的心境中成长"},{stage:"mature",duration:45e4,minFocusScore:90,visualScale:1,spriteFrame:"focus_mature",description:"专注力达到高度凝聚状态"},{stage:"ready_to_harvest",duration:1/0,minFocusScore:0,visualScale:1.25,spriteFrame:"focus_perfect",description:"完美的专注之花绽放出纯净光芒！"}],rewards:{baseExp:110,growthPoints:80,specialItems:["concentration_essence","focus_crystal","clarity_boost"]},requirements:{minLevel:4,prerequisites:["knowledge_flower","time_veggie"]}},reading_vine:{type:"reading_vine",name:"读书藤",description:"通过阅读习惯培育的知识藤蔓，象征智慧的延伸与传播",icon:"📚",baseGrowthTime:21e5,focusMultiplier:1.7,stages:[{stage:"seed",duration:15e4,minFocusScore:55,visualScale:.2,spriteFrame:"reading_seed",description:"知识的种子等待启蒙"},{stage:"sprout",duration:36e4,minFocusScore:65,visualScale:.4,spriteFrame:"reading_sprout",description:"求知的嫩芽探索世界"},{stage:"growing",duration:108e4,minFocusScore:75,visualScale:.8,spriteFrame:"reading_growing",description:"知识藤蔓不断延伸扩展"},{stage:"mature",duration:51e4,minFocusScore:80,visualScale:1,spriteFrame:"reading_mature",description:"智慧的藤蔓枝繁叶茂"},{stage:"ready_to_harvest",duration:1/0,minFocusScore:0,visualScale:1.3,spriteFrame:"reading_flourish",description:"知识藤蔓结出智慧果实！"}],rewards:{baseExp:130,growthPoints:90,specialItems:["knowledge_vine","wisdom_scroll","learning_boost"]},requirements:{minLevel:3,prerequisites:["knowledge_flower"]}},social_fruit:{type:"social_fruit",name:"社交果",description:"通过社交互动培育的友谊之果，象征人际关系的和谐",icon:"🍎",baseGrowthTime:24e5,focusMultiplier:1.3,stages:[{stage:"seed",duration:18e4,minFocusScore:45,visualScale:.3,spriteFrame:"social_seed",description:"友谊的种子等待连接"},{stage:"sprout",duration:48e4,minFocusScore:55,visualScale:.5,spriteFrame:"social_sprout",description:"社交的嫩芽寻求共鸣"},{stage:"growing",duration:12e5,minFocusScore:65,visualScale:.8,spriteFrame:"social_growing",description:"人际关系网络逐渐扩展"},{stage:"mature",duration:54e4,minFocusScore:70,visualScale:1,spriteFrame:"social_mature",description:"社交果树即将结出友谊果实"},{stage:"ready_to_harvest",duration:1/0,minFocusScore:0,visualScale:1.2,spriteFrame:"social_harvest",description:"友谊之果甜美可人，可以分享了！"}],rewards:{baseExp:100,growthPoints:60,specialItems:["friendship_essence","harmony_crystal","social_boost"]},requirements:{minLevel:2,prerequisites:["strength_tree"]}}};class ie extends X.GameObjects.Container{constructor(e,s){super(e,s.x,s.y),t(this,"background"),t(this,"titleText"),t(this,"seedButtons",new Map),t(this,"closeButton"),t(this,"selectedCropType",null),t(this,"isVisible",!1),t(this,"onSeedSelectedCallback"),t(this,"onClosedCallback"),this.background=e.add.rectangle(0,0,320,400,2905392,.95),this.background.setStrokeStyle(3,9498256),this.add(this.background),this.titleText=e.add.text(0,-180,"🌱 选择种子",{fontSize:"20px",color:"#FFFFFF",fontFamily:"Arial",fontStyle:"bold"}).setOrigin(.5),this.add(this.titleText),this.createSeedButtons(s.availableCrops),this.createCloseButton(),this.setVisible(s.visible),this.isVisible=s.visible,e.add.existing(this)}createSeedButtons(e){e.forEach((e,t)=>{const s=ae[e],a=80*t-120,i=this.scene.add.container(0,a),n=this.scene.add.rectangle(0,0,280,70,4876097,.8);n.setStrokeStyle(2,9498256,.5),i.add(n);const r=this.scene.add.text(-120,0,s.icon,{fontSize:"24px"}).setOrigin(.5);i.add(r);const o=this.scene.add.text(-70,-10,s.name,{fontSize:"16px",color:"#FFFFFF",fontFamily:"Arial",fontStyle:"bold"}).setOrigin(0,.5);i.add(o);const c=this.scene.add.text(-70,10,s.description.substring(0,20)+"...",{fontSize:"12px",color:"#CCCCCC",fontFamily:"Arial"}).setOrigin(0,.5);i.add(c);const l=this.scene.add.text(100,0,"".concat(Math.floor(s.baseGrowthTime/6e4),"分钟"),{fontSize:"12px",color:"#FFD700",fontFamily:"Arial"}).setOrigin(.5);i.add(l),n.setInteractive({useHandCursor:!0}),n.on("pointerover",()=>{n.setFillStyle(5929553,.9),this.scene.tweens.add({targets:i,scaleX:1.05,scaleY:1.05,duration:150,ease:"Power2"})}),n.on("pointerout",()=>{this.selectedCropType!==e&&n.setFillStyle(4876097,.8),this.scene.tweens.add({targets:i,scaleX:1,scaleY:1,duration:150,ease:"Power2"})}),n.on("pointerdown",()=>{this.selectSeed(e,n)}),this.seedButtons.set(e,i),this.add(i)})}createCloseButton(){this.closeButton=this.scene.add.container(0,160);const e=this.scene.add.rectangle(0,0,100,40,13386820,.8);e.setStrokeStyle(2,16737894),this.closeButton.add(e);const t=this.scene.add.text(0,0,"关闭",{fontSize:"14px",color:"#FFFFFF",fontFamily:"Arial"}).setOrigin(.5);this.closeButton.add(t),e.setInteractive({useHandCursor:!0}),e.on("pointerover",()=>{e.setFillStyle(14505301,.9)}),e.on("pointerout",()=>{e.setFillStyle(13386820,.8)}),e.on("pointerdown",()=>{this.closeUI()}),this.add(this.closeButton)}selectSeed(e,t){this.seedButtons.forEach((t,s)=>{const a=t.getAt(0);s!==e&&a.setFillStyle(4876097,.8)}),t.setFillStyle(9498256,.9),this.selectedCropType=e;const s=this.seedButtons.get(e);this.scene.tweens.add({targets:s,scaleX:1.1,scaleY:1.1,duration:100,yoyo:!0,ease:"Power2"}),this.scene.time.delayedCall(200,()=>{this.onSeedSelectedCallback&&this.onSeedSelectedCallback(e)})}showUI(e){e&&this.updateAvailableCrops(e),this.setVisible(!0),this.isVisible=!0,this.setScale(.8),this.setAlpha(0),this.scene.tweens.add({targets:this,scaleX:1,scaleY:1,alpha:1,duration:300,ease:"Back.easeOut"})}hideUI(){this.scene.tweens.add({targets:this,scaleX:.8,scaleY:.8,alpha:0,duration:250,ease:"Back.easeIn",onComplete:()=>{this.setVisible(!1),this.isVisible=!1}})}closeUI(){this.hideUI(),this.onClosedCallback&&this.onClosedCallback()}updateAvailableCrops(e){this.seedButtons.forEach(e=>{this.remove(e),e.destroy()}),this.seedButtons.clear(),this.createSeedButtons(e)}getSelectedCropType(){return this.selectedCropType}isUIVisible(){return this.isVisible}onSeedSelected(e){this.onSeedSelectedCallback=e}onClosed(e){this.onClosedCallback=e}destroy(){this.seedButtons.forEach(e=>e.destroy()),this.seedButtons.clear(),super.destroy()}}var ne,re=((ne=re||{}).NONE="none",ne.SELECTING_SEED="selecting_seed",ne.PLACING_CROP="placing_crop",ne);class oe{constructor(e,s){t(this,"scene"),t(this,"gameStateManager"),t(this,"plantingUI"),t(this,"currentMode","none"),t(this,"selectedCropType",null),t(this,"gridHighlights",[]),t(this,"gridSize",{width:8,height:6}),t(this,"tileSize",80),t(this,"gridStart",{x:200,y:100}),t(this,"onPlantSuccessCallback"),t(this,"onPlantFailureCallback"),t(this,"onModeChangeCallback"),this.scene=e,this.gameStateManager=s,this.plantingUI=new ie(e,{x:600,y:300,visible:!1,availableCrops:this.getAvailableCrops()}),this.setupUIEventListeners()}setupUIEventListeners(){this.plantingUI.onSeedSelected(e=>{this.onSeedSelected(e)}),this.plantingUI.onClosed(()=>{this.exitPlantingMode()})}startPlanting(){if("none"!==this.currentMode)return;const e=this.getAvailableCrops();0!==e.length?(this.setMode("selecting_seed"),this.plantingUI.showUI(e)):this.showMessage("暂无可种植的作物","warning")}exitPlantingMode(){this.clearGridHighlights(),this.plantingUI.hideUI(),this.selectedCropType=null,this.setMode("none")}handleGridClick(e,t){if("placing_crop"!==this.currentMode||!this.selectedCropType)return{success:!1,message:"请先选择种子"};const s=this.validatePlantPosition(e,t);if(!s.valid){const e={success:!1,message:s.message};return this.onPlantFailureCallback&&this.onPlantFailureCallback(e),e}if(this.gameStateManager.plantCrop(e,t,this.selectedCropType)){const s=this.gameStateManager.getGameState().farmGrid.plots[t][e],a={success:!0,message:"成功种植".concat(this.getCropName(this.selectedCropType),"！"),crop:s||void 0,position:{gridX:e,gridY:t}};return this.playPlantingAnimation(e,t),this.exitPlantingMode(),this.onPlantSuccessCallback&&this.onPlantSuccessCallback(a),a}{const e={success:!1,message:"种植失败，位置可能被占用"};return this.onPlantFailureCallback&&this.onPlantFailureCallback(e),e}}onSeedSelected(e){this.selectedCropType=e,this.setMode("placing_crop"),this.plantingUI.hideUI(),this.showPlantableAreas(),this.showMessage("选择位置来种植".concat(this.getCropName(e)),"info")}showPlantableAreas(){this.clearGridHighlights();const e=this.gameStateManager.getGameState();for(let t=0;t<this.gridSize.height;t++)for(let s=0;s<this.gridSize.width;s++){const a=null!==e.farmGrid.plots[t][s],i=this.gridStart.x+s*this.tileSize+this.tileSize/2,n=this.gridStart.y+t*this.tileSize+this.tileSize/2;let r;a?(r=this.scene.add.rectangle(i,n,this.tileSize-4,this.tileSize-4,16729156,.3),r.setStrokeStyle(2,16737894,.8)):(r=this.scene.add.rectangle(i,n,this.tileSize-4,this.tileSize-4,4521796,.3),r.setStrokeStyle(2,6750054,.8),this.scene.tweens.add({targets:r,alpha:.1,duration:1e3,yoyo:!0,repeat:-1,ease:"Sine.easeInOut"})),this.gridHighlights.push(r)}}clearGridHighlights(){this.gridHighlights.forEach(e=>{this.scene.tweens.killTweensOf(e),e.destroy()}),this.gridHighlights=[]}validatePlantPosition(e,t){return e<0||e>=this.gridSize.width||t<0||t>=this.gridSize.height?{valid:!1,message:"位置超出农场边界"}:null!==this.gameStateManager.getGameState().farmGrid.plots[t][e]?{valid:!1,message:"该位置已被占用"}:{valid:!0,message:"位置有效"}}playPlantingAnimation(e,t){const s=this.gridStart.x+e*this.tileSize+this.tileSize/2,a=this.gridStart.y+t*this.tileSize+this.tileSize/2,i=this.scene.add.particles(s,a,"sparkle",{speed:{min:30,max:60},scale:{start:.4,end:0},alpha:{start:1,end:0},lifespan:1e3,quantity:8}),n=this.scene.add.circle(s,a,40,9498256,0);n.setStrokeStyle(3,9498256,.8),this.scene.tweens.add({targets:n,scaleX:2,scaleY:2,alpha:0,duration:800,ease:"Power2.easeOut"}),this.scene.time.delayedCall(1200,()=>{i.destroy(),n.destroy()})}showMessage(e,t="info"){const s=this.scene.add.container(400,50),a=this.scene.add.rectangle(0,0,8*e.length+40,40,{info:4886754,warning:16098851,error:13632027}[t],.9);a.setStrokeStyle(2,16777215,.8),s.add(a);const i=this.scene.add.text(0,0,e,{fontSize:"14px",color:"#FFFFFF",fontFamily:"Arial"}).setOrigin(.5);s.add(i),s.setScale(.8),s.setAlpha(0),this.scene.tweens.add({targets:s,scaleX:1,scaleY:1,alpha:1,duration:200,ease:"Back.easeOut"}),this.scene.time.delayedCall(3e3,()=>{this.scene.tweens.add({targets:s,alpha:0,y:s.y-20,duration:300,onComplete:()=>s.destroy()})})}getAvailableCrops(){const e=this.gameStateManager.getGameState(),t=[te.KNOWLEDGE_FLOWER];return e.level>=3&&t.push(te.STRENGTH_TREE),e.level>=5&&t.push(te.TIME_VEGGIE),e.level>=8&&t.push(te.MEDITATION_LOTUS),t}getCropName(e){return{[te.KNOWLEDGE_FLOWER]:"知识花",[te.STRENGTH_TREE]:"力量树",[te.TIME_VEGGIE]:"时间蔬菜",[te.MEDITATION_LOTUS]:"冥想莲花"}[e]||"未知作物"}setMode(e){this.currentMode=e,this.onModeChangeCallback&&this.onModeChangeCallback(e)}getCurrentMode(){return this.currentMode}isInPlantingMode(){return"none"!==this.currentMode}onPlantSuccess(e){this.onPlantSuccessCallback=e}onPlantFailure(e){this.onPlantFailureCallback=e}onModeChange(e){this.onModeChangeCallback=e}destroy(){this.clearGridHighlights(),this.plantingUI.destroy()}}class ce extends X.GameObjects.Container{constructor(e,s,a,i){super(e,s,a),t(this,"mainSprite"),t(this,"glowSprite"),t(this,"progressBar"),t(this,"qualityIndicator"),t(this,"particles"),t(this,"currentStage"),t(this,"currentQuality"),t(this,"animationTween",null),t(this,"swayTween",null),t(this,"glowTween",null),t(this,"isHighlighted",!1),t(this,"isHarvestReady",!1),t(this,"lastUpdateTime",0),this.cropData=i,this.currentStage=i.stage,this.currentQuality=i.quality,this.particles=[],this.createVisualElements(),this.setupInteractivity(),this.startIdleAnimations(),e.add.existing(this)}createVisualElements(){this.mainSprite=this.scene.add.graphics(),this.add(this.mainSprite),this.glowSprite=this.scene.add.graphics(),this.add(this.glowSprite),this.progressBar=this.scene.add.graphics(),this.progressBar.setPosition(0,40),this.add(this.progressBar),this.qualityIndicator=this.scene.add.text(0,-45,"",{fontSize:"12px",color:"#ffffff",stroke:"#000000",strokeThickness:2}).setOrigin(.5),this.add(this.qualityIndicator),this.updateVisuals()}setupInteractivity(){this.setSize(60,60),this.setInteractive(),this.on("pointerover",()=>{this.setHighlight(!0),this.scene.input.setDefaultCursor("pointer")}),this.on("pointerout",()=>{this.setHighlight(!1),this.scene.input.setDefaultCursor("default")}),this.on("pointerdown",()=>{this.onCropClicked()})}updateCropData(e){const t=this.currentStage,s=this.currentQuality;this.cropData=e,this.currentStage=e.stage,this.currentQuality=e.quality,t!==this.currentStage&&this.playStageTransitionAnimation(t,this.currentStage),s!==this.currentQuality&&this.playQualityUpgradeAnimation(),e.harvestable&&!this.isHarvestReady&&this.setHarvestReady(!0),this.updateVisuals()}updateVisuals(){this.drawMainSprite(),this.updateProgressBar(),this.updateQualityIndicator(),this.updateGlowEffect()}drawMainSprite(){const e=ae[this.cropData.type].stages.find(e=>e.stage===this.currentStage);if(!e)return;this.mainSprite.clear();const t=30*e.visualScale,s=this.getCropColors();switch(this.cropData.type){case te.KNOWLEDGE_FLOWER:this.drawFlower(s,t);break;case te.STRENGTH_TREE:this.drawTree(s,t);break;case te.TIME_VEGGIE:this.drawVegetable(s,t);break;case te.MEDITATION_LOTUS:this.drawLotus(s,t)}}getCropColors(){return{[se.COMMON]:{primary:9498256,secondary:2263842,accent:16777215},[se.UNCOMMON]:{primary:8900331,secondary:4620980,accent:15792383},[se.RARE]:{primary:16738740,secondary:16716947,accent:16758465},[se.EPIC]:{primary:9662683,secondary:9055202,accent:15132410},[se.LEGENDARY]:{primary:16766720,secondary:16747520,accent:16775885}}[this.currentQuality]}drawFlower(e,t){const{primary:s,secondary:a,accent:i}=e;this.mainSprite.fillStyle(a),this.mainSprite.fillRect(-2,10,4,.6*t),this.mainSprite.fillStyle(s);const n=this.currentStage===ee.SEED?0:this.currentStage===ee.SPROUT?3:5;for(let r=0;r<n;r++){const e=r/n*Math.PI*2,s=Math.cos(e)*t*.3,a=Math.sin(e)*t*.3;this.mainSprite.fillCircle(s,a-10,.2*t)}n>0&&(this.mainSprite.fillStyle(i),this.mainSprite.fillCircle(0,-10,.1*t)),this.currentStage===ee.SEED&&(this.mainSprite.fillStyle(9127187),this.mainSprite.fillCircle(0,0,.3*t))}drawTree(e,t){const{primary:s,secondary:a}=e;this.mainSprite.fillStyle(a);const i=Math.max(4,.2*t),n=.8*t;if(this.mainSprite.fillRect(-i/2,0,i,n),this.currentStage!==ee.SEED){this.mainSprite.fillStyle(s);const e=t*(this.currentStage===ee.SPROUT?.4:.8);this.mainSprite.fillCircle(0,.3*-e,e)}else this.mainSprite.fillStyle(9127187),this.mainSprite.fillCircle(0,0,.3*t)}drawVegetable(e,t){const{primary:s,secondary:a}=e;if(this.currentStage===ee.SEED)return this.mainSprite.fillStyle(9127187),void this.mainSprite.fillCircle(0,0,.3*t);this.mainSprite.fillStyle(s);const i=Math.min(6,Math.floor(t/8));for(let n=0;n<i;n++){const e=n/i*Math.PI*2,s=Math.cos(e)*t*.2,a=Math.sin(e)*t*.2;this.mainSprite.fillEllipse(s,a,.3*t,.15*t)}this.mainSprite.fillStyle(a),this.mainSprite.fillCircle(0,0,.15*t)}drawLotus(e,t){const{primary:s,secondary:a,accent:i}=e;if(this.currentStage===ee.SEED)return this.mainSprite.fillStyle(9127187),void this.mainSprite.fillCircle(0,0,.3*t);const n=this.currentStage===ee.SPROUT?1:this.currentStage===ee.GROWING?2:3;for(let r=0;r<n;r++){const e=t*(1-.2*r),i=6+2*r;this.mainSprite.fillStyle(0===r?s:a);for(let t=0;t<i;t++){const s=t/i*Math.PI*2+.3*r,a=Math.cos(s)*e*.4,n=Math.sin(s)*e*.4;this.mainSprite.fillEllipse(a,n,.3*e,.15*e)}}n>1&&(this.mainSprite.fillStyle(i),this.mainSprite.fillCircle(0,0,.1*t))}updateProgressBar(){if(this.progressBar.clear(),this.currentStage===ee.READY_TO_HARVEST||this.currentStage===ee.HARVESTED)return;const e=ae[this.cropData.type],t=((e,t)=>{const s=t.stages.find(t=>t.stage===e.stage);if(!s)return 0;const a=Date.now()-e.stageStartTime,i=Math.min(a/s.duration,1);return(t.stages.findIndex(t=>t.stage===e.stage)+i)/(t.stages.length-1)})(this.cropData,e);this.progressBar.fillStyle(3355443,.8),this.progressBar.fillRoundedRect(-20,0,40,4,2);const s=this.getProgressColor(t);this.progressBar.fillStyle(s),this.progressBar.fillRoundedRect(-20,0,40*t,4,2)}getProgressColor(e){return e<.3?16739179:e<.7?16770669:5164484}updateQualityIndicator(){const e={[se.COMMON]:"",[se.UNCOMMON]:"⭐",[se.RARE]:"⭐⭐",[se.EPIC]:"⭐⭐⭐",[se.LEGENDARY]:"👑"};this.qualityIndicator.setText(e[this.currentQuality])}updateGlowEffect(){this.glowSprite.clear(),this.isHarvestReady?(this.glowSprite.fillStyle(16766720,.3),this.glowSprite.fillCircle(0,0,40)):this.isHighlighted&&(this.glowSprite.fillStyle(8900331,.2),this.glowSprite.fillCircle(0,0,35))}playStageTransitionAnimation(e,t){this.animationTween&&this.animationTween.destroy(),this.animationTween=this.scene.tweens.add({targets:this,scaleX:1.1,scaleY:1.1,duration:200,ease:"Back.easeOut",yoyo:!0,onComplete:()=>{this.animationTween=null}}),this.emitParticles("growth")}playQualityUpgradeAnimation(){this.emitParticles("quality_boost"),this.scene.tweens.add({targets:this,alpha:.3,duration:75,yoyo:!0,repeat:3})}startIdleAnimations(){this.swayTween=this.scene.tweens.add({targets:this,rotation:-.05,duration:1e3,ease:"Sine.easeInOut",yoyo:!0,repeat:-1})}setHighlight(e){this.isHighlighted=e,this.updateGlowEffect()}setHarvestReady(e){this.isHarvestReady=e,this.updateGlowEffect(),e&&!this.glowTween?this.glowTween=this.scene.tweens.add({targets:this.glowSprite,alpha:.1,duration:750,ease:"Sine.easeInOut",yoyo:!0,repeat:-1}):!e&&this.glowTween&&(this.glowTween.destroy(),this.glowTween=null)}emitParticles(e){const t=this.getCropColors(),s="growth"===e?t.primary:16766720;for(let a=0;a<8;a++){const e=a/8*Math.PI*2,t=20+15*Math.random(),i=this.x+Math.cos(e)*t,n=this.y+Math.sin(e)*t,r=this.scene.add.graphics();r.fillStyle(s,.8),r.fillCircle(0,0,2),r.setPosition(this.x,this.y),this.scene.tweens.add({targets:r,x:i,y:n,alpha:0,scale:0,duration:1e3,ease:"Quad.easeOut",onComplete:()=>{r.destroy()}})}}onCropClicked(){this.isHarvestReady?this.onHarvestRequested():this.onCropInspected()}onHarvestRequested(){this.emitParticles("harvest"),this.emit("harvest",this.cropData)}onCropInspected(){this.emitParticles("sparkle"),this.emit("inspect",this.cropData)}destroy(){this.animationTween&&this.animationTween.destroy(),this.swayTween&&this.swayTween.destroy(),this.glowTween&&this.glowTween.destroy(),this.particles.forEach(e=>e.destroy()),super.destroy()}}class le extends X.Scene{constructor(){super({key:"EnhancedFarmScene"}),t(this,"farmGrid",null),t(this,"cropSprites",new Map),t(this,"gridSize",{width:8,height:6}),t(this,"tileSize",80),t(this,"startX",200),t(this,"startY",100),t(this,"particles",null),t(this,"clouds",null),t(this,"gameStateManager"),t(this,"plantingManager"),t(this,"plantButton"),t(this,"farmInfoPanel"),t(this,"statusText")}init(e){this.gameStateManager=e.gameStateManager}preload(){this.createEnhancedTiles(),this.createParticleTextures(),this.createUITextures()}create(){this.createEnhancedBackground(),this.createAnimatedClouds(),this.createParticleSystem(),this.createFarmGrid(),this.initializePlantingManager(),this.createUI(),this.createFarmInfoPanel(),this.setupGameStateListeners(),this.refreshFarmDisplay(),this.showWelcomeMessage(),this.createEnvironmentAnimations()}createEnhancedTiles(){const e=this.add.graphics();e.fillGradientStyle(9127187,9127187,6636321,6636321,1),e.fillRect(0,0,this.tileSize,this.tileSize),e.lineStyle(1,6106634,.3);for(let i=0;i<5;i++)e.lineBetween(Math.random()*this.tileSize,Math.random()*this.tileSize,Math.random()*this.tileSize,Math.random()*this.tileSize);e.lineStyle(2,3086858,.5),e.strokeRect(1,1,this.tileSize-2,this.tileSize-2),e.generateTexture("soil-tile",this.tileSize,this.tileSize),e.destroy();const t=this.add.graphics();t.fillStyle(16766720),t.fillCircle(this.tileSize/2,this.tileSize/2,8),t.fillStyle(16777130,.6),t.fillCircle(this.tileSize/2-2,this.tileSize/2-2,3),t.generateTexture("seed",this.tileSize,this.tileSize),t.destroy();const s=this.add.graphics();s.fillStyle(2263842),s.fillRect(this.tileSize/2-2,this.tileSize/2+10,4,15),s.fillStyle(3329330),s.fillEllipse(this.tileSize/2-8,this.tileSize/2,8,12),s.fillEllipse(this.tileSize/2+8,this.tileSize/2,8,12),s.generateTexture("sprout",this.tileSize,this.tileSize),s.destroy();const a=this.add.graphics();a.fillStyle(2263842),a.fillRect(this.tileSize/2-3,this.tileSize/2+5,6,20),a.fillStyle(16738740);for(let i=0;i<8;i++){const e=i/8*Math.PI*2,t=this.tileSize/2+12*Math.cos(e),s=this.tileSize/2+12*Math.sin(e);a.fillCircle(t,s,6)}a.fillStyle(16766720),a.fillCircle(this.tileSize/2,this.tileSize/2,8),a.generateTexture("knowledge-flower",this.tileSize,this.tileSize),a.destroy()}createParticleTextures(){const e=this.add.graphics();e.fillStyle(16777215),e.fillStar(8,8,4,4,8,0),e.generateTexture("sparkle",16,16),e.destroy();const t=this.add.graphics();t.fillStyle(16766720,.8),t.fillCircle(4,4,3),t.generateTexture("pollen",8,8),t.destroy()}createUITextures(){const e=this.add.graphics();e.fillStyle(9498256),e.fillCircle(15,15,12),e.fillStyle(2263842),e.fillRect(13,8,4,14),e.fillEllipse(10,10,6,8),e.fillEllipse(20,10,6,8),e.generateTexture("plant-icon",30,30),e.destroy()}createEnhancedBackground(){const e=this.add.graphics();e.fillGradientStyle(8900331,8900331,14743295,14743295,1),e.fillRect(0,0,800,600),e.generateTexture("sky",800,600),e.destroy(),this.add.image(400,300,"sky")}createAnimatedClouds(){this.clouds=this.add.group();for(let e=0;e<3;e++){const t=this.add.ellipse(100+250*e,80+40*Math.random(),60+40*Math.random(),30+20*Math.random(),16777215,.8);this.clouds.add(t),this.tweens.add({targets:t,x:t.x+50,duration:15e3+1e4*Math.random(),repeat:-1,yoyo:!0,ease:"Sine.easeInOut"})}}createParticleSystem(){this.particles=this.add.particles(400,300,"sparkle",{speed:{min:20,max:40},scale:{start:.2,end:0},alpha:{start:.8,end:0},lifespan:3e3,frequency:2e3,quantity:1})}createFarmGrid(){this.farmGrid=this.add.group();for(let e=0;e<this.gridSize.height;e++)for(let t=0;t<this.gridSize.width;t++){const s=this.startX+t*this.tileSize,a=this.startY+e*this.tileSize,i=this.add.image(s,a,"soil-tile").setOrigin(0,0).setInteractive({useHandCursor:!0}).on("pointerdown",()=>this.onTileClick(e,t)).on("pointerover",()=>this.onTileHover(i,!0)).on("pointerout",()=>this.onTileHover(i,!1));this.farmGrid.add(i)}}initializePlantingManager(){this.plantingManager=new oe(this,this.gameStateManager),this.plantingManager.onPlantSuccess(e=>{this.refreshFarmDisplay(),this.updateFarmInfoPanel()}),this.plantingManager.onPlantFailure(e=>{}),this.plantingManager.onModeChange(e=>{this.updatePlantButtonState(e)})}setupGameStateListeners(){this.gameStateManager.on("crop_planted",()=>{this.refreshFarmDisplay(),this.updateFarmInfoPanel()}),this.gameStateManager.on("crop_harvested",()=>{this.refreshFarmDisplay(),this.updateFarmInfoPanel()})}createUI(){this.add.text(400,30,"🌱 自律农场 - 种植系统",{fontSize:"24px",color:"#2C5530",fontFamily:"Arial",fontStyle:"bold"}).setOrigin(.5).setShadow(2,2,"#000000",2,!1,!0),this.createPlantButton(),this.statusText=this.add.text(400,550,'点击"开始种植"选择种子',{fontSize:"14px",color:"#4A4A4A",fontFamily:"Arial"}).setOrigin(.5)}createPlantButton(){this.plantButton=this.add.container(100,500);const e=this.add.rectangle(0,0,120,50,9498256,.9);e.setStrokeStyle(3,2263842),this.plantButton.add(e);const t=this.add.image(-20,0,"plant-icon").setScale(.8);this.plantButton.add(t);const s=this.add.text(15,0,"开始种植",{fontSize:"14px",color:"#1A4A1A",fontFamily:"Arial",fontStyle:"bold"}).setOrigin(.5);this.plantButton.add(s),e.setInteractive({useHandCursor:!0}),e.on("pointerover",()=>{e.setFillStyle(10551200,1),this.tweens.add({targets:this.plantButton,scaleX:1.05,scaleY:1.05,duration:150})}),e.on("pointerout",()=>{e.setFillStyle(9498256,.9),this.tweens.add({targets:this.plantButton,scaleX:1,scaleY:1,duration:150})}),e.on("pointerdown",()=>{this.togglePlantingMode()})}createFarmInfoPanel(){this.farmInfoPanel=this.add.container(650,150);const e=this.add.rectangle(0,0,140,200,2905392,.9);e.setStrokeStyle(2,9498256),this.farmInfoPanel.add(e);const t=this.add.text(0,-85,"农场信息",{fontSize:"16px",color:"#FFFFFF",fontFamily:"Arial",fontStyle:"bold"}).setOrigin(.5);this.farmInfoPanel.add(t),this.updateFarmInfoPanel()}updateFarmInfoPanel(){this.farmInfoPanel.list.slice(2).forEach(e=>{this.farmInfoPanel.remove(e),e.destroy()});const e=this.gameStateManager.getGameState(),t=this.gameStateManager.getGameStats();["等级: ".concat(e.level),"经验: ".concat(e.experience),"作物数: ".concat(t.cropsCount),"收获数: ".concat(t.totalCropsHarvested),"知识: ".concat(e.resources.knowledge),"力量: ".concat(e.resources.strength)].forEach((e,t)=>{const s=this.add.text(0,20*t-50,e,{fontSize:"12px",color:"#CCCCCC",fontFamily:"Arial"}).setOrigin(.5);this.farmInfoPanel.add(s)})}togglePlantingMode(){this.plantingManager.isInPlantingMode()?this.plantingManager.exitPlantingMode():this.plantingManager.startPlanting()}updatePlantButtonState(e){const t=this.plantButton.getAt(0),s=this.plantButton.getAt(2);e===re.NONE?(t.setFillStyle(9498256,.9),s.setText("开始种植"),this.statusText.setText('点击"开始种植"选择种子')):e===re.SELECTING_SEED?(t.setFillStyle(16766720,.9),s.setText("选择中..."),this.statusText.setText("选择要种植的种子类型")):e===re.PLACING_CROP&&(t.setFillStyle(16739179,.9),s.setText("取消种植"),this.statusText.setText("点击绿色区域进行种植"))}refreshFarmDisplay(){this.cropSprites.forEach(e=>e.destroy()),this.cropSprites.clear(),this.gameStateManager.getGameState().crops.forEach((e,t)=>{const{gridX:s,gridY:a}=e.position;if(s>=0&&s<this.gridSize.width&&a>=0&&a<this.gridSize.height){const i=this.startX+s*this.tileSize+this.tileSize/2,n=this.startY+a*this.tileSize+this.tileSize/2,r=new ce(this,i,n,e);this.cropSprites.set(t,r),r.setInteractive({useHandCursor:!0}),r.on("pointerdown",()=>{e.stage===ee.READY_TO_HARVEST&&this.harvestCrop(s,a)})}})}harvestCrop(e,t){this.gameStateManager.harvestCrop(e,t).success&&this.playHarvestAnimation(e,t)}playHarvestAnimation(e,t){const s=this.startX+e*this.tileSize+this.tileSize/2,a=this.startY+t*this.tileSize+this.tileSize/2,i=this.add.particles(s,a,"sparkle",{speed:{min:50,max:100},scale:{start:.5,end:0},alpha:{start:1,end:0},lifespan:1e3,quantity:15}),n=this.add.text(s,a-30,"+10 经验",{fontSize:"14px",color:"#FFD700",fontFamily:"Arial",fontStyle:"bold"}).setOrigin(.5);this.tweens.add({targets:n,y:n.y-40,alpha:0,duration:1500,ease:"Power2.easeOut"}),this.time.delayedCall(1500,()=>{i.destroy(),n.destroy()})}onTileClick(e,t){if(this.plantingManager.isInPlantingMode())this.plantingManager.handleGridClick(t,e);else{const s=this.startX+t*this.tileSize+this.tileSize/2,a=this.startY+e*this.tileSize+this.tileSize/2,i=this.add.circle(s,a,15,16766720,.8);this.tweens.add({targets:i,scaleX:2,scaleY:2,alpha:0,duration:400,onComplete:()=>i.destroy()})}}onTileHover(e,t){t?(e.setTint(16777130),this.tweens.add({targets:e,scaleX:1.05,scaleY:1.05,duration:200})):(e.clearTint(),this.tweens.add({targets:e,scaleX:1,scaleY:1,duration:200}))}showWelcomeMessage(){const e=this.add.rectangle(400,300,450,180,0,.8),t=this.add.text(400,300,'🌱 欢迎来到自律农场！\n\n✨ 种植系统已就绪\n🎯 点击"开始种植"开始种植\n🌾 成熟后点击作物收获',{fontSize:"16px",color:"#FFFFFF",align:"center",fontFamily:"Arial"}).setOrigin(.5),s=this.add.rectangle(400,300,460,190,9498256,0);s.setStrokeStyle(3,9498256),this.tweens.add({targets:s,alpha:.8,duration:1e3,yoyo:!0,repeat:2}),e.setScale(0),t.setScale(0),this.tweens.add({targets:[e,t],scaleX:1,scaleY:1,duration:500,ease:"Back.easeOut"}),this.time.delayedCall(4e3,()=>{this.tweens.add({targets:[e,t,s],alpha:0,scaleX:.8,scaleY:.8,duration:500,onComplete:()=>{e.destroy(),t.destroy(),s.destroy()}})})}createEnvironmentAnimations(){this.time.addEvent({delay:2e4,callback:()=>{const e=this.add.rectangle(400,300,800,600,16777130,.1);this.tweens.add({targets:e,alpha:0,duration:3e3,onComplete:()=>e.destroy()})},loop:!0})}update(){this.cropSprites.forEach(e=>{e.update()})}}var de,he={exports:{}};const ue=n((de||(de=1,function(e){var t=Object.prototype.hasOwnProperty,s="~";function a(){}function i(e,t,s){this.fn=e,this.context=t,this.once=s||!1}function n(e,t,a,n,r){if("function"!=typeof a)throw new TypeError("The listener must be a function");var o=new i(a,n||e,r),c=s?s+t:t;return e._events[c]?e._events[c].fn?e._events[c]=[e._events[c],o]:e._events[c].push(o):(e._events[c]=o,e._eventsCount++),e}function r(e,t){0===--e._eventsCount?e._events=new a:delete e._events[t]}function o(){this._events=new a,this._eventsCount=0}Object.create&&(a.prototype=Object.create(null),(new a).__proto__||(s=!1)),o.prototype.eventNames=function(){var e,a,i=[];if(0===this._eventsCount)return i;for(a in e=this._events)t.call(e,a)&&i.push(s?a.slice(1):a);return Object.getOwnPropertySymbols?i.concat(Object.getOwnPropertySymbols(e)):i},o.prototype.listeners=function(e){var t=s?s+e:e,a=this._events[t];if(!a)return[];if(a.fn)return[a.fn];for(var i=0,n=a.length,r=new Array(n);i<n;i++)r[i]=a[i].fn;return r},o.prototype.listenerCount=function(e){var t=s?s+e:e,a=this._events[t];return a?a.fn?1:a.length:0},o.prototype.emit=function(e,t,a,i,n,r){var o=s?s+e:e;if(!this._events[o])return!1;var c,l,d=this._events[o],h=arguments.length;if(d.fn){switch(d.once&&this.removeListener(e,d.fn,void 0,!0),h){case 1:return d.fn.call(d.context),!0;case 2:return d.fn.call(d.context,t),!0;case 3:return d.fn.call(d.context,t,a),!0;case 4:return d.fn.call(d.context,t,a,i),!0;case 5:return d.fn.call(d.context,t,a,i,n),!0;case 6:return d.fn.call(d.context,t,a,i,n,r),!0}for(l=1,c=new Array(h-1);l<h;l++)c[l-1]=arguments[l];d.fn.apply(d.context,c)}else{var u,m=d.length;for(l=0;l<m;l++)switch(d[l].once&&this.removeListener(e,d[l].fn,void 0,!0),h){case 1:d[l].fn.call(d[l].context);break;case 2:d[l].fn.call(d[l].context,t);break;case 3:d[l].fn.call(d[l].context,t,a);break;case 4:d[l].fn.call(d[l].context,t,a,i);break;default:if(!c)for(u=1,c=new Array(h-1);u<h;u++)c[u-1]=arguments[u];d[l].fn.apply(d[l].context,c)}}return!0},o.prototype.on=function(e,t,s){return n(this,e,t,s,!1)},o.prototype.once=function(e,t,s){return n(this,e,t,s,!0)},o.prototype.removeListener=function(e,t,a,i){var n=s?s+e:e;if(!this._events[n])return this;if(!t)return r(this,n),this;var o=this._events[n];if(o.fn)o.fn!==t||i&&!o.once||a&&o.context!==a||r(this,n);else{for(var c=0,l=[],d=o.length;c<d;c++)(o[c].fn!==t||i&&!o[c].once||a&&o[c].context!==a)&&l.push(o[c]);l.length?this._events[n]=1===l.length?l[0]:l:r(this,n)}return this},o.prototype.removeAllListeners=function(e){var t;return e?(t=s?s+e:e,this._events[t]&&r(this,t)):(this._events=new a,this._eventsCount=0),this},o.prototype.off=o.prototype.removeListener,o.prototype.addListener=o.prototype.on,o.prefixed=s,o.EventEmitter=o,e.exports=o}(he)),he.exports));var me,pe,ge,ye,fe,xe,ve,be,Se=((me=Se||{}).CROP="crop",me.LIVESTOCK="livestock",me.SEED="seed",me.PRODUCT="product",me.TOOL="tool",me.MATERIAL="material",me),Ee=((pe=Ee||{}).SEED="seed",pe.SPROUT="sprout",pe.GROWING="growing",pe.FLOWERING="flowering",pe.MATURE="mature",pe.READY="ready",pe),we=((ge=we||{}).PREPARING="preparing",ge.IN_PROGRESS="in_progress",ge.COMPLETED="completed",ge.FAILED="failed",ge.CANCELLED="cancelled",ge),Ne=((ye=Ne||{}).SPRING="spring",ye.SUMMER="summer",ye.AUTUMN="autumn",ye.WINTER="winter",ye),Te=((fe=Te||{}).SUNNY="sunny",fe.CLOUDY="cloudy",fe.RAINY="rainy",fe.STORMY="stormy",fe.SNOWY="snowy",fe),je=((xe=je||{}).GRAY="gray",xe.GREEN="green",xe.BLUE="blue",xe.ORANGE="orange",xe.GOLD="gold",xe.GOLD_RED="gold_red",xe),Re=(e=>(e.AGRICULTURAL="agricultural",e.INDUSTRIAL="industrial",e.CROP="crop",e.LIVESTOCK="livestock",e))(Re||{}),_e=((ve=_e||{}).SEED="seed",ve.CROP="crop",ve.LIVESTOCK="livestock",ve.FARM_TOOL="farm_tool",ve.FARM_BUILDING="farm_building",ve.RAW_MATERIAL="raw_material",ve.COMPONENT="component",ve.MACHINERY="machinery",ve.PRODUCT="product",ve.FACTORY_BUILDING="factory_building",ve.CURRENCY="currency",ve.BOOST="boost",ve.DECORATION="decoration",ve),Ie=((be=Ie||{}).BASIC_FARM="basic_farm",be.PREMIUM_FARM="premium_farm",be.LEGENDARY_FARM="legendary_farm",be.BASIC_INDUSTRIAL="basic_industrial",be.PREMIUM_INDUSTRIAL="premium_industrial",be.LEGENDARY_INDUSTRIAL="legendary_industrial",be.FUTURES_MYSTERY="futures_mystery",be.GOLDEN_TREASURE="golden_treasure",be.SYNTHESIS_BOX="synthesis_box",be);const ke={gray:"#9E9E9E",green:"#4CAF50",blue:"#2196F3",orange:"#FF9800",gold:"#FFD700",gold_red:"#FF6B6B"},Ae={[je.GRAY]:{minDaily:1,maxDaily:3,baseValue:10,qualityMultiplier:1,growthTime:24},[je.GREEN]:{minDaily:4,maxDaily:6,baseValue:25,qualityMultiplier:1.5,growthTime:20},[je.BLUE]:{minDaily:7,maxDaily:10,baseValue:50,qualityMultiplier:2.2,growthTime:16},[je.ORANGE]:{minDaily:12,maxDaily:16,baseValue:100,qualityMultiplier:3.5,growthTime:12},[je.GOLD]:{minDaily:18,maxDaily:25,baseValue:200,qualityMultiplier:5.5,growthTime:8},[je.GOLD_RED]:{minDaily:30,maxDaily:40,baseValue:500,qualityMultiplier:8,growthTime:4}};var Ce,De,Me=((Ce=Me||{}).CORN="corn",Ce.SOYBEAN="soybean",Ce.SOYBEAN_2="soybean_2",Ce.SOYBEAN_MEAL="soybean_meal",Ce.SOYBEAN_OIL="soybean_oil",Ce.PALM_OIL="palm_oil",Ce.CORN_STARCH="corn_starch",Ce.EGG="egg",Ce.WHEAT="wheat",Ce.COMMON_WHEAT="common_wheat",Ce.RICE="rice",Ce.LATE_RICE="late_rice",Ce.JAPONICA_RICE="japonica_rice",Ce.CANOLA="canola",Ce.CANOLA_MEAL="canola_meal",Ce.CANOLA_OIL="canola_oil",Ce.COTTON="cotton",Ce.COTTON_YARN="cotton_yarn",Ce.WHITE_SUGAR="white_sugar",Ce.APPLE="apple",Ce.RED_JUJUBE="red_jujube",Ce.NATURAL_RUBBER="natural_rubber",Ce.TOMATO="tomato",Ce.CARROT="carrot",Ce.CABBAGE="cabbage",Ce.STRAWBERRY="strawberry",Ce.GRAPE="grape",Ce.TEA="tea",Ce.COFFEE="coffee",Ce.PEANUT="peanut",Ce.SESAME="sesame",Ce.SUNFLOWER="sunflower",Ce),Oe=((De=Oe||{}).CHICKEN="chicken",De.PIG="pig",De.COW="cow",De.SHEEP="sheep",De.DUCK="duck",De.GOOSE="goose",De);const Le={wheat_seed:{variety:"wheat",name:"小麦种子",description:"基础粮食作物，生长稳定，适合新手",category:"crop",baseIcon:"🌾"},rice_seed:{variety:"rice",name:"水稻种子",description:"需要充足水分，产量丰富",category:"crop",baseIcon:"🌾"},corn_seed:{variety:"corn",name:"玉米种子",description:"高产作物，营养价值高",category:"crop",baseIcon:"🌽"},tomato_seed:{variety:"tomato",name:"番茄种子",description:"多汁美味，市场需求大",category:"crop",baseIcon:"🍅"},carrot_seed:{variety:"carrot",name:"胡萝卜种子",description:"营养丰富，生长周期短",category:"crop",baseIcon:"🥕"},cabbage_seed:{variety:"cabbage",name:"卷心菜种子",description:"耐储存，四季可种",category:"crop",baseIcon:"🥬"},apple_seed:{variety:"apple",name:"苹果树苗",description:"果树类作物，产量持久",category:"crop",baseIcon:"🍎",specialBonuses:{seasonBonus:.2}},strawberry_seed:{variety:"strawberry",name:"草莓种子",description:"高价值浆果，生长迅速",category:"crop",baseIcon:"🍓"},grape_seed:{variety:"grape",name:"葡萄藤苗",description:"可加工成高价值产品",category:"crop",baseIcon:"🍇",specialBonuses:{skillBonus:.3}},cotton_seed:{variety:"cotton",name:"棉花种子",description:"经济作物，工业原料",category:"crop",baseIcon:"🌱"},tea_seed:{variety:"tea",name:"茶树苗",description:"高端经济作物，需要技巧",category:"crop",baseIcon:"🍃",specialBonuses:{skillBonus:.4,weatherBonus:.1}},coffee_seed:{variety:"coffee",name:"咖啡树苗",description:"珍贵经济作物，极高价值",category:"crop",baseIcon:"☕",specialBonuses:{skillBonus:.5,seasonBonus:.3}},chicken_chick:{variety:"chicken",name:"小鸡",description:"成长快速，产蛋稳定",category:"livestock",baseIcon:"🐣"},cow_calf:{variety:"cow",name:"小牛",description:"大型牲畜，产奶量高",category:"livestock",baseIcon:"🐄",specialBonuses:{weatherBonus:.15}},pig_piglet:{variety:"pig",name:"小猪",description:"生长迅速，肉质优良",category:"livestock",baseIcon:"🐷"},sheep_lamb:{variety:"sheep",name:"羊羔",description:"产毛产肉双重收益",category:"livestock",baseIcon:"🐑",specialBonuses:{seasonBonus:.25}},duck_duckling:{variety:"duck",name:"小鸭",description:"水禽类，适应性强",category:"livestock",baseIcon:"🦆"},goose_gosling:{variety:"goose",name:"小鹅",description:"大型水禽，高价值产品",category:"livestock",baseIcon:"🪿",specialBonuses:{skillBonus:.2}}};function Fe(e){switch(e){case je.GRAY:return["gray_to_green"];case je.GREEN:return["green_to_blue"];case je.BLUE:return["blue_to_orange"];case je.ORANGE:return["orange_to_gold"];case je.GOLD:return["gold_to_golden_red"];default:return[]}}function Pe(e){return{[je.GRAY]:.1,[je.GREEN]:.15,[je.BLUE]:.2,[je.ORANGE]:.3,[je.GOLD]:.4,[je.GOLD_RED]:.5}[e]}function Ge(e,t){const s={[je.GRAY]:1,[je.GREEN]:1.2,[je.BLUE]:1.5,[je.ORANGE]:2,[je.GOLD]:3,[je.GOLD_RED]:5};return({wheat:1,rice:1,corn:.9,tomato:.8,carrot:.7,cabbage:.6,apple:.9,strawberry:1.2,grape:1.1,cotton:.5,tea:1.5,coffee:2,chicken:1.1,cow:1.3,pig:1,sheep:.8,duck:.9,goose:1.2}[e]||1)*s[t]}function Be(e){return"livestock"===e.category?{chicken:"🥚",cow:"🥛",pig:"🥓",sheep:"🧶",duck:"🥚",goose:"🥚"}[e.variety]||"🥩":{wheat:"🌾",rice:"🍚",corn:"🌽",tomato:"🍅",carrot:"🥕",cabbage:"🥬",apple:"🍎",strawberry:"🍓",grape:"🍇",cotton:"☁️",tea:"🍵",coffee:"☕"}[e.variety]||"🌱"}je.GRAY,Re.AGRICULTURAL,je.GREEN,je.GREEN,Re.AGRICULTURAL,je.BLUE,je.BLUE,Re.AGRICULTURAL,je.ORANGE,je.ORANGE,Re.AGRICULTURAL,je.GOLD,je.GOLD,Re.AGRICULTURAL,je.GOLD_RED;const Ue=function(){const e=[];return Object.entries(Le).forEach(([t,s])=>{Object.values(je).forEach(a=>{const i=Ae[a],n=function(e){return{[je.GRAY]:"普通",[je.GREEN]:"优良",[je.BLUE]:"稀有",[je.ORANGE]:"史诗",[je.GOLD]:"传说",[je.GOLD_RED]:"神话"}[e]}(a),r={id:"".concat(t,"_").concat(a),name:"".concat(n).concat(s.name),description:"".concat(s.description," (品质: ").concat(n,")"),category:Re.AGRICULTURAL,type:"crop"===s.category?_e.SEED:_e.LIVESTOCK,rarity:a,icon:s.baseIcon,value:i.baseValue,stackable:!0,tradeable:!0,synthesizable:!0,metadata:{yieldMultiplier:i.qualityMultiplier,growthSpeed:24/i.growthTime,qualityBonus:100*(i.qualityMultiplier-1),effects:[],duration:36e5*i.growthTime,synthesisRecipes:Fe(a),futuresPrice:i.baseValue*i.qualityMultiplier,priceVolatility:Pe(a),marketDemand:Ge(s.variety,a)}};e.push(r);const o=function(e){return"livestock"===e.category?{chicken:"鸡蛋",cow:"牛奶",pig:"猪肉",sheep:"羊毛",duck:"鸭蛋",goose:"鹅蛋"}[e.variety]||"畜产品":{wheat:"小麦",rice:"大米",corn:"玉米",tomato:"番茄",carrot:"胡萝卜",cabbage:"卷心菜",apple:"苹果",strawberry:"草莓",grape:"葡萄",cotton:"棉花",tea:"茶叶",coffee:"咖啡豆"}[e.variety]||"农产品"}(s),c={id:"".concat(t.replace("_seed","").replace("_chick","").replace("_calf","").replace("_piglet","").replace("_lamb","").replace("_duckling","").replace("_gosling",""),"_product_").concat(a),name:"".concat(n).concat(o),description:"由".concat(n).concat(s.name,"生产的").concat(o),category:Re.AGRICULTURAL,type:_e.CROP,rarity:a,icon:Be(s),value:2*i.baseValue,stackable:!0,tradeable:!0,synthesizable:!1,metadata:{yieldMultiplier:i.qualityMultiplier,effects:["每日产量: ".concat(i.minDaily,"-").concat(i.maxDaily)],futuresPrice:i.baseValue*i.qualityMultiplier*2,priceVolatility:1.5*Pe(a),marketDemand:1.2*Ge(s.variety,a)}};e.push(c)})}),e}();class ze extends ue{constructor(e,s=1){super(),t(this,"focusTokenManager"),t(this,"availableLootBoxes"),t(this,"playerLevel"),this.focusTokenManager=e,this.playerLevel=s,this.availableLootBoxes=this.initializeLootBoxes()}initializeLootBoxes(){return[{id:"basic_seed_box",name:"基础种子盲盒",description:"包含常见种子和农具，新手友好的选择",price:50,currency:"focus_token",rarity:je.GRAY,contents:[{itemId:"wheat_seed",itemType:"agricultural_item",probability:.3,minQuantity:2,maxQuantity:5},{itemId:"corn_seed",itemType:"agricultural_item",probability:.25,minQuantity:1,maxQuantity:3},{itemId:"rice_seed",itemType:"agricultural_item",probability:.2,minQuantity:1,maxQuantity:4},{itemId:"soybean_seed",itemType:"agricultural_item",probability:.15,minQuantity:1,maxQuantity:2},{itemId:"bonus_tokens",itemType:"token",probability:.1,minQuantity:10,maxQuantity:30}],animation:"basic_box_open",openSound:"box_open_common",rarityGlow:"#9E9E9E"},{id:"premium_crop_box",name:"优质农作物盲盒",description:"更高品质的农作物种子，有机会获得稀有品种",price:150,currency:"focus_token",rarity:je.GREEN,contents:[{itemId:"premium_wheat_seed",itemType:"agricultural_item",probability:.25,minQuantity:1,maxQuantity:3},{itemId:"cotton_seed",itemType:"agricultural_item",probability:.2,minQuantity:1,maxQuantity:2},{itemId:"apple_seed",itemType:"agricultural_item",probability:.2,minQuantity:1,maxQuantity:2},{itemId:"sugar_seed",itemType:"agricultural_item",probability:.15,minQuantity:1,maxQuantity:2},{itemId:"rare_livestock",itemType:"agricultural_item",probability:.1,minQuantity:1,maxQuantity:1},{itemId:"bonus_tokens",itemType:"token",probability:.1,minQuantity:30,maxQuantity:80}],animation:"premium_box_open",openSound:"box_open_uncommon",rarityGlow:"#4CAF50"},{id:"rare_futures_box",name:"期货品种宝箱",description:"包含期货市场的珍贵农产品，高价值投资机会",price:300,currency:"focus_token",rarity:je.BLUE,contents:[{itemId:"rubber_seed",itemType:"agricultural_item",probability:.2,minQuantity:1,maxQuantity:2},{itemId:"palm_oil_seed",itemType:"agricultural_item",probability:.2,minQuantity:1,maxQuantity:2},{itemId:"rapeseed_seed",itemType:"agricultural_item",probability:.18,minQuantity:1,maxQuantity:2},{itemId:"jujube_seed",itemType:"agricultural_item",probability:.15,minQuantity:1,maxQuantity:1},{itemId:"epic_livestock",itemType:"agricultural_item",probability:.12,minQuantity:1,maxQuantity:1},{itemId:"synthesis_materials",itemType:"material",probability:.1,minQuantity:2,maxQuantity:5},{itemId:"bonus_tokens",itemType:"token",probability:.05,minQuantity:100,maxQuantity:200}],animation:"rare_box_open",openSound:"box_open_rare",rarityGlow:"#2196F3"},{id:"legendary_master_box",name:"传说大师宝箱",description:"最高级的农产品盲盒，包含传说级种子和神秘道具",price:800,currency:"focus_token",rarity:je.GOLD,contents:[{itemId:"legendary_hybrid_seed",itemType:"agricultural_item",probability:.3,minQuantity:1,maxQuantity:1},{itemId:"mythical_livestock",itemType:"agricultural_item",probability:.2,minQuantity:1,maxQuantity:1},{itemId:"synthesis_catalyst",itemType:"material",probability:.2,minQuantity:1,maxQuantity:3},{itemId:"farm_upgrade_materials",itemType:"building",probability:.15,minQuantity:1,maxQuantity:2},{itemId:"rare_decorations",itemType:"decoration",probability:.1,minQuantity:1,maxQuantity:1},{itemId:"massive_token_bonus",itemType:"token",probability:.05,minQuantity:500,maxQuantity:1e3}],animation:"legendary_box_open",openSound:"box_open_legendary",rarityGlow:"#FFD700"}]}getAvailableLootBoxes(){return this.availableLootBoxes.filter(e=>{const t=this.getRequiredLevel(e.rarity);return this.playerLevel>=t})}getRequiredLevel(e){switch(e){case je.GRAY:return 1;case je.GREEN:return 5;case je.BLUE:return 15;case je.ORANGE:return 25;case je.GOLD:return 40;case je.GOLD_RED:return 60;default:return 1}}async purchaseLootBox(e){const t=this.availableLootBoxes.find(t=>t.id===e);if(!t)return{success:!1,error:"盲盒不存在"};const s=this.getRequiredLevel(t.rarity);return this.playerLevel<s?{success:!1,error:"需要等级 ".concat(s)}:"focus_token"!==t.currency||this.focusTokenManager.spendTokens(t.price)?(this.emit("lootBoxPurchased",{lootBoxId:e,price:t.price,currency:t.currency}),{success:!0}):{success:!1,error:"专注代币不足"}}async openLootBox(e){const t=this.availableLootBoxes.find(t=>t.id===e);if(!t)throw new Error("盲盒不存在");const s=[],a=[t.animation];let i=0;for(const o of t.contents)if(Math.random()<o.probability){const e=Math.floor(Math.random()*(o.maxQuantity-o.minQuantity+1))+o.minQuantity;if("agricultural_item"===o.itemType){const a=this.generateAgriculturalItems(o,e,t.rarity);s.push(...a),i+=a.reduce((e,t)=>e+t.value.basePrice,0)}else"token"===o.itemType&&(this.focusTokenManager.addBonusTokens(e,"盲盒奖励: ".concat(t.name)),i+=e)}if(0===s.length&&t.contents.length>0){const e=t.contents[0],a=this.generateAgriculturalItems(e,e.minQuantity,t.rarity);s.push(...a),i+=a.reduce((e,t)=>e+t.value.basePrice,0)}const n=s.filter(e=>e.rarity===je.BLUE||e.rarity===je.ORANGE||e.rarity===je.GOLD||e.rarity===je.GOLD_RED),r={success:!0,items:s,totalValue:i,rareItems:n,animations:a};return this.emit("lootBoxOpened",{lootBoxId:e,result:r,rareItemsCount:n.length}),r}generateAgriculturalItems(e,t,s){const a=[];for(let i=0;i<t;i++){const t=this.createRandomAgriculturalItem(e.itemId,s);a.push(t)}return a}createRandomAgriculturalItem(e,t){var s,a;const i=this.determineItemRarity(t),n=this.getRandomVariety(e),r=Ue[n]||Ue[Object.keys(Ue)[0]],o=r.production[i],c=Math.floor(Math.random()*(o.maxDaily-o.minDaily+1))+o.minDaily;return{id:"".concat(n,"_").concat(i,"_").concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9)),name:r.name,nameEn:r.nameEn,description:r.description,rarity:i,category:r.category,variety:n,level:1,quality:Math.floor(100*Math.random())+1,production:{minDaily:o.minDaily,maxDaily:o.maxDaily,currentDaily:c},value:{basePrice:o.baseValue,currentPrice:o.baseValue*(.8+.4*Math.random()),marketDemand:r.marketDemand,priceHistory:[]},growth:{growthTime:o.growthTime,currentStage:"seed",isReady:!1,needsWater:r.category===Se.CROP,needsFertilizer:r.category===Se.CROP},special:{weatherBonus:(null==(s=r.special)?void 0:s.weatherBonus)||0,seasonBonus:(null==(a=r.special)?void 0:a.seasonBonus)||0,skillBonus:0,isHybrid:Math.random()<.1},sprite:"".concat(n,"_").concat(i),animation:"".concat(n,"_grow"),sound:"".concat(r.category,"_sound")}}determineItemRarity(e){const t=this.getRarityWeights(e),s=Math.random();let a=0;for(const[i,n]of Object.entries(t))if(a+=n,s<=a)return i;return je.GRAY}getRarityWeights(e){switch(e){case je.GRAY:return{[je.GRAY]:.7,[je.GREEN]:.25,[je.BLUE]:.05,[je.ORANGE]:0,[je.GOLD]:0,[je.GOLD_RED]:0};case je.GREEN:return{[je.GRAY]:.4,[je.GREEN]:.45,[je.BLUE]:.13,[je.ORANGE]:.02,[je.GOLD]:0,[je.GOLD_RED]:0};case je.BLUE:return{[je.GRAY]:.2,[je.GREEN]:.35,[je.BLUE]:.35,[je.ORANGE]:.08,[je.GOLD]:.02,[je.GOLD_RED]:0};case je.GOLD:return{[je.GRAY]:.1,[je.GREEN]:.2,[je.BLUE]:.3,[je.ORANGE]:.25,[je.GOLD]:.13,[je.GOLD_RED]:.02};default:return{[je.GRAY]:1,[je.GREEN]:0,[je.BLUE]:0,[je.ORANGE]:0,[je.GOLD]:0,[je.GOLD_RED]:0}}}getRandomVariety(e){if(e.includes("livestock")){const e=Object.values(Oe);return e[Math.floor(Math.random()*e.length)]}{const e=Object.values(Me);return e[Math.floor(Math.random()*e.length)]}}setPlayerLevel(e){this.playerLevel=e}getLootBoxPreview(e){const t=this.availableLootBoxes.find(t=>t.id===e);return t?{items:t.contents.map(e=>e.itemId),probabilities:t.contents.map(e=>e.probability)}:null}getLootBoxStatistics(){return{totalBoxTypes:this.availableLootBoxes.length,availableBoxTypes:this.getAvailableLootBoxes().length,playerLevel:this.playerLevel,focusTokenBalance:this.focusTokenManager.getTokenAmount()}}}class He extends ue{constructor(e){super(),t(this,"farm"),t(this,"currentSeason",Ne.SPRING),t(this,"currentWeather",Te.SUNNY),t(this,"gameTime",Date.now()),this.farm={id:"player_farm",name:"我的农场",level:1,experience:0,slots:[],maxSlots:9,unlockCost:1e3,properties:{wateringEfficiency:1,fertilizerEfficiency:1,harvestBonus:1,growthSpeedMultiplier:1},buildings:[],decorations:[],...e},this.initializeFarmSlots()}initializeFarmSlots(){const e=Math.ceil(Math.sqrt(this.farm.maxSlots));for(let t=0;t<this.farm.maxSlots;t++){const s=t%e*100+50,a=100*Math.floor(t/e)+50,i={id:"slot_".concat(t),x:s,y:a,isUnlocked:t<6,isOccupied:!1,soilQuality:50+30*Math.random(),moistureLevel:100,fertilizerLevel:0};this.farm.slots.push(i)}}async plantCrop(e,t){const s=this.farm.slots.find(t=>t.id===e);if(!s)return{success:!1,error:"农田槽位不存在"};if(!s.isUnlocked)return{success:!1,error:"农田槽位未解锁"};if(s.isOccupied)return{success:!1,error:"农田槽位已被占用"};if(t.category!==Se.SEED&&t.category!==Se.CROP)return{success:!1,error:"只能种植种子或作物"};const a={...t,growth:{...t.growth,plantedTime:this.gameTime,currentStage:Ee.SEED,isReady:!1,needsWater:!0,needsFertilizer:!1},location:{farmSlotId:e,x:s.x,y:s.y}},i=this.calculateActualGrowthTime(a);return a.production.nextHarvestTime=this.gameTime+1e3*i,s.isOccupied=!0,s.currentItem=a,this.emit("cropPlanted",{slotId:e,item:a,estimatedHarvestTime:a.production.nextHarvestTime}),{success:!0}}async harvestCrop(e){const t=this.farm.slots.find(t=>t.id===e);if(!t||!t.currentItem)return{success:!1,error:"该槽位没有作物"};const s=t.currentItem;if(!this.isCropReady(s)){const e=(s.production.nextHarvestTime||0)-this.gameTime,t=Math.ceil(e/36e5);return{success:!1,error:"作物还需要 ".concat(t," 小时才能收获")}}const a=this.calculateHarvest(s,t),i=this.calculateExperience(s);return this.addExperience(i),t.isOccupied=!1,t.currentItem=void 0,t.moistureLevel=Math.max(0,t.moistureLevel-20),t.fertilizerLevel=Math.max(0,t.fertilizerLevel-10),this.emit("cropHarvested",{slotId:e,originalItem:s,harvestItems:a,experienceGained:i}),{success:!0,items:a}}async waterSlot(e){const t=this.farm.slots.find(t=>t.id===e);if(!t)return{success:!1,error:"农田槽位不存在"};if(!t.currentItem)return{success:!1,error:"该槽位没有作物需要浇水"};if(t.moistureLevel>=80)return{success:!1,error:"土壤湿度充足，无需浇水"};const s=40*this.farm.properties.wateringEfficiency;return t.moistureLevel=Math.min(100,t.moistureLevel+s),t.lastWatered=this.gameTime,t.currentItem.growth.needsWater&&(t.currentItem.growth.needsWater=!1),this.emit("slotWatered",{slotId:e,newMoistureLevel:t.moistureLevel,wateringEfficiency:this.farm.properties.wateringEfficiency}),{success:!0}}async fertilizeSlot(e,t="basic"){const s=this.farm.slots.find(t=>t.id===e);if(!s)return{success:!1,error:"农田槽位不存在"};if(!s.currentItem)return{success:!1,error:"该槽位没有作物需要施肥"};if(s.fertilizerLevel>=100)return{success:!1,error:"土壤肥力充足，无需施肥"};const a=this.getFertilizerAmount(t)*this.farm.properties.fertilizerEfficiency;return s.fertilizerLevel=Math.min(100,s.fertilizerLevel+a),s.lastFertilized=this.gameTime,s.currentItem.growth.needsFertilizer&&(s.currentItem.growth.needsFertilizer=!1),this.updateCropGrowth(s.currentItem),this.emit("slotFertilized",{slotId:e,fertilizerType:t,newFertilizerLevel:s.fertilizerLevel,fertilizerEfficiency:this.farm.properties.fertilizerEfficiency}),{success:!0}}async unlockSlot(e){const t=this.farm.slots.find(t=>t.id===e);if(!t)return{success:!1,error:"农田槽位不存在"};if(t.isUnlocked)return{success:!1,error:"该槽位已解锁"};const s=this.getSlotUnlockCost();return t.isUnlocked=!0,this.emit("slotUnlocked",{slotId:e,cost:s,totalUnlockedSlots:this.farm.slots.filter(e=>e.isUnlocked).length}),{success:!0,cost:s}}updateGameTime(e,t,s){this.gameTime=e,t&&t!==this.currentSeason&&(this.currentSeason=t,this.emit("seasonChanged",{season:t})),s&&s!==this.currentWeather&&(this.currentWeather=s,this.emit("weatherChanged",{weather:s})),this.updateAllCrops()}updateAllCrops(){for(const e of this.farm.slots)e.currentItem&&(this.updateCropGrowth(e.currentItem),this.updateSlotConditions(e))}updateCropGrowth(e){if(!e.growth.plantedTime||!e.production.nextHarvestTime)return;const t=(this.gameTime-e.growth.plantedTime)/(e.production.nextHarvestTime-e.growth.plantedTime);t>=1?(e.growth.currentStage=Ee.READY,e.growth.isReady=!0):t>=.8?e.growth.currentStage=Ee.MATURE:t>=.6?e.growth.currentStage=Ee.FLOWERING:t>=.3?e.growth.currentStage=Ee.GROWING:t>=.1&&(e.growth.currentStage=Ee.SPROUT)}updateSlotConditions(e){const t=(this.gameTime-(e.lastWatered||this.gameTime))/36e5;if(t>0){const s=Math.min(2*t,30);e.moistureLevel=Math.max(0,e.moistureLevel-s)}e.currentItem&&e.moistureLevel<30&&(e.currentItem.growth.needsWater=!0),e.currentItem&&e.fertilizerLevel<20&&(e.currentItem.growth.needsFertilizer=!0)}calculateActualGrowthTime(e){let t=3600*e.growth.growthTime;return t*=1/this.farm.properties.growthSpeedMultiplier,t*=this.getSeasonMultiplier(e,this.currentSeason),t*=this.getWeatherMultiplier(e,this.currentWeather),t}getSeasonMultiplier(e,t){var s;if(null==(s=e.special)?void 0:s.seasonBonus)return 1-.01*e.special.seasonBonus;switch(t){case Ne.SPRING:return.9;case Ne.SUMMER:return 1;case Ne.AUTUMN:return 1.1;case Ne.WINTER:return 1.3;default:return 1}}getWeatherMultiplier(e,t){var s;if(null==(s=e.special)?void 0:s.weatherBonus)return 1-.01*e.special.weatherBonus;switch(t){case Te.SUNNY:return 1;case Te.CLOUDY:return 1.05;case Te.RAINY:return.95;case Te.STORMY:return 1.2;case Te.SNOWY:return 1.4;default:return 1}}isCropReady(e){return this.gameTime>=(e.production.nextHarvestTime||0)}calculateHarvest(e,t){let s=e.production.currentDaily||Math.floor(Math.random()*(e.production.maxDaily-e.production.minDaily+1))+e.production.minDaily;s*=.5+t.soilQuality/100,t.moistureLevel>60?s*=1.1:t.moistureLevel<30&&(s*=.8),t.fertilizerLevel>50&&(s*=1.2),s*=this.farm.properties.harvestBonus,s*=.8+.4*Math.random();const a=Math.max(1,Math.floor(s)),i=[];for(let n=0;n<a;n++){const t={...e,id:"".concat(e.variety,"_harvest_").concat(Date.now(),"_").concat(n),category:Se.PRODUCT,growth:{growthTime:0,currentStage:Ee.READY,isReady:!0},location:void 0};i.push(t)}return i}calculateExperience(e){const t=this.getRarityExperienceMultiplier(e.rarity),s=.1*e.quality;return Math.floor(10*t+s)}getRarityExperienceMultiplier(e){switch(e){case je.GRAY:return 1;case je.GREEN:return 1.5;case je.BLUE:return 2.5;case je.ORANGE:return 4;case je.GOLD:return 6;case je.GOLD_RED:return 10;default:return 1}}addExperience(e){this.farm.experience+=e;const t=this.getRequiredExperience(this.farm.level);this.farm.experience>=t&&this.levelUp(),this.emit("experienceGained",{amount:e,totalExperience:this.farm.experience,currentLevel:this.farm.level,nextLevelExp:this.getRequiredExperience(this.farm.level)})}levelUp(){this.farm.level+=1,this.farm.properties.harvestBonus+=.05,this.farm.properties.growthSpeedMultiplier+=.02,this.farm.level%5==0&&this.farm.maxSlots<20&&(this.farm.maxSlots+=2,this.addNewSlots(2)),this.emit("farmLevelUp",{newLevel:this.farm.level,bonusesGained:{harvestBonus:.05,growthSpeedMultiplier:.02},newSlotsUnlocked:this.farm.level%5==0?2:0})}addNewSlots(e){const t=this.farm.slots.length,s=Math.ceil(Math.sqrt(this.farm.maxSlots));for(let a=0;a<e;a++){const e=t+a,i=e%s*100+50,n=100*Math.floor(e/s)+50,r={id:"slot_".concat(e),x:i,y:n,isUnlocked:!1,isOccupied:!1,soilQuality:50+30*Math.random(),moistureLevel:100,fertilizerLevel:0};this.farm.slots.push(r)}}getRequiredExperience(e){return 100*e+50*Math.pow(e,2)}getSlotUnlockCost(){const e=this.farm.slots.filter(e=>e.isUnlocked).length;return Math.floor(1e3*Math.pow(1.5,e-6))}getFertilizerAmount(e){switch(e){case"basic":return 30;case"advanced":return 50;case"premium":return 80;default:return 20}}getFarmState(){return{...this.farm}}getSlotState(e){return this.farm.slots.find(t=>t.id===e)||null}getReadyCrops(){return this.farm.slots.filter(e=>e.currentItem&&this.isCropReady(e.currentItem)).map(e=>({slotId:e.id,item:e.currentItem}))}getCropsNeedingCare(){return this.farm.slots.filter(e=>e.currentItem).map(e=>{const t=[];return e.currentItem.growth.needsWater&&t.push("water"),e.currentItem.growth.needsFertilizer&&t.push("fertilizer"),{slotId:e.id,item:e.currentItem,needs:t}}).filter(e=>e.needs.length>0)}getFarmStatistics(){const e=this.farm.slots,t=e.filter(e=>e.isUnlocked),s=e.filter(e=>e.isOccupied),a=this.getReadyCrops(),i=this.getCropsNeedingCare();return{level:this.farm.level,experience:this.farm.experience,nextLevelExp:this.getRequiredExperience(this.farm.level),totalSlots:e.length,unlockedSlots:t.length,occupiedSlots:s.length,readyCrops:a.length,cropsNeedingCare:i.length,averageSoilQuality:t.reduce((e,t)=>e+t.soilQuality,0)/t.length,properties:this.farm.properties}}}var qe,Ye=((qe=Ye||{}).KEEP_MATERIALS="keep_materials",qe.LOSE_ONE_ITEM="lose_one_item",qe.LOSE_ALL_ITEMS="lose_all_items",qe.GET_PROTECTION_STONE="get_protection_stone",qe.DOWNGRADE_QUALITY="downgrade_quality",qe.GET_COMPENSATION="get_compensation",qe);const We={[je.GRAY]:{rarity:je.GRAY,failureRate:.05,consequences:[{type:"keep_materials",probability:1,description:"合成失败，但所有材料都被保留",details:{}}]},[je.GREEN]:{rarity:je.GREEN,failureRate:.1,consequences:[{type:"keep_materials",probability:.8,description:"合成失败，但所有材料都被保留"},{type:"lose_one_item",probability:.2,description:"合成失败，丢失一个原材料",details:{itemsLost:1}}]},[je.BLUE]:{rarity:je.BLUE,failureRate:.15,consequences:[{type:"keep_materials",probability:.6,description:"合成失败，但所有材料都被保留"},{type:"lose_one_item",probability:.3,description:"合成失败，丢失一个原材料",details:{itemsLost:1}},{type:"get_compensation",probability:.1,description:"合成失败，获得等值货币补偿",details:{compensationValue:50}}],protectionMechanisms:{protectionStoneRequired:!1,alternativeProtection:["幸运符咒","合成保险"]}},[je.ORANGE]:{rarity:je.ORANGE,failureRate:.25,consequences:[{type:"keep_materials",probability:.4,description:"合成失败，但所有材料都被保留"},{type:"lose_one_item",probability:.4,description:"合成失败，丢失一个原材料",details:{itemsLost:1}},{type:"downgrade_quality",probability:.15,description:"合成失败，获得降级品质的物品",details:{downgradeLevels:1}},{type:"get_protection_stone",probability:.05,description:"合成失败，但获得保护石作为补偿",details:{protectionStoneAmount:1}}],protectionMechanisms:{protectionStoneRequired:!0,protectionStoneConsumed:!1,alternativeProtection:["高级幸运符咒","贵族合成保险"]}},[je.GOLD]:{rarity:je.GOLD,failureRate:.25,consequences:[{type:"keep_materials",probability:.2,description:"合成失败，但所有材料都被保留"},{type:"lose_one_item",probability:.5,description:"合成失败，丢失一个原材料",details:{itemsLost:1}},{type:"lose_all_items",probability:.1,description:"合成失败，丢失所有原材料",details:{itemsLost:2}},{type:"downgrade_quality",probability:.15,description:"合成失败，获得降级品质的物品",details:{downgradeLevels:2}},{type:"get_protection_stone",probability:.05,description:"合成失败，但获得保护石作为补偿",details:{protectionStoneAmount:2}}],protectionMechanisms:{protectionStoneRequired:!0,protectionStoneConsumed:!0,alternativeProtection:["传说幸运符咒","皇室合成保险"]}},[je.GOLD_RED]:{rarity:je.GOLD_RED,failureRate:.4,consequences:[{type:"keep_materials",probability:.1,description:"合成失败，但所有材料都被保留"},{type:"lose_one_item",probability:.4,description:"合成失败，丢失一个原材料",details:{itemsLost:1}},{type:"lose_all_items",probability:.3,description:"合成失败，丢失所有原材料",details:{itemsLost:2}},{type:"downgrade_quality",probability:.15,description:"合成失败，获得降级品质的物品",details:{downgradeLevels:3}},{type:"get_protection_stone",probability:.05,description:"合成失败，但获得高级保护石作为补偿",details:{protectionStoneAmount:3}}],protectionMechanisms:{protectionStoneRequired:!0,protectionStoneConsumed:!0,alternativeProtection:["神话幸运符咒","帝王合成保险","天道庇护"]}}};je.GREEN,je.BLUE,je.GOLD,je.GREEN,je.ORANGE;class Ve extends ue{constructor(e=1){super(),t(this,"activeSessions",new Map),t(this,"completedSessions",[]),t(this,"playerLevel"),this.playerLevel=e}async createSynthesisSession(e,t=[]){if(2!==e.length)return{success:!1,error:"合成需要恰好2个物品"};const[s,a]=e;if(s.rarity!==a.rarity)return{success:!1,error:"只能合成相同品质的物品"};if(s.variety!==a.variety)return{success:!1,error:"只能合成相同品种的物品"};if(s.category!==Se.CROP&&s.category!==Se.LIVESTOCK)return{success:!1,error:"该类型物品无法合成"};const i=this.generateSessionId(),n=this.createSynthesisRecipe(e),r={id:i,recipeId:n.id,inputItems:e,startTime:Date.now(),endTime:Date.now()+1e3*n.craftTime,status:we.PREPARING,hasProtection:t.length>0,protectionItems:t};return this.activeSessions.set(i,r),this.emit("synthesisSessionCreated",{sessionId:i,inputItems:e.map(e=>({id:e.id,name:e.name,rarity:e.rarity})),estimatedTime:n.craftTime,successRate:n.successRate}),{success:!0,sessionId:i}}async startSynthesis(e){const t=this.activeSessions.get(e);if(!t)return{success:!1,error:"合成会话不存在"};if(t.status!==we.PREPARING)return{success:!1,error:"会话状态错误"};t.status=we.IN_PROGRESS,t.startTime=Date.now();const s=this.getSynthesisTime(t.inputItems[0].rarity),a=s+(Math.random()-.5)*s*.2;return t.endTime=t.startTime+1e3*a,this.emit("synthesisStarted",{sessionId:e,startTime:t.startTime,estimatedEndTime:t.endTime}),setTimeout(()=>{this.completeSynthesis(e)},1e3*a),{success:!0}}async completeSynthesis(e){const t=this.activeSessions.get(e);if(!t||t.status!==we.IN_PROGRESS)return;const s=this.createSynthesisRecipe(t.inputItems),a=this.performSynthesis(t,s);t.result=a,t.status=a.success?we.COMPLETED:we.FAILED,this.activeSessions.delete(e),this.completedSessions.push(t),this.emit("synthesisCompleted",{sessionId:e,result:a,inputItems:t.inputItems,hadProtection:t.hasProtection})}performSynthesis(e,t){const s=e.inputItems[0].rarity,a=this.getTargetRarity(s);if(!a)return{success:!1,outputItems:[],failureReason:"已达到最高品质",experienceGained:10};let i=t.successRate;return e.hasProtection&&(i=this.applyProtectionBonus(i,e.protectionItems)),Math.random()<i?{success:!0,outputItems:[this.createUpgradedItem(e.inputItems[0],a)],experienceGained:this.getExperienceReward(a)}:this.handleSynthesisFailure(e,s)}handleSynthesisFailure(e,t){const s=We[t];if(!s)return{success:!1,outputItems:e.inputItems,failureReason:"未知错误",experienceGained:5};if(e.hasProtection)return{success:!1,outputItems:e.inputItems,failureReason:"合成失败，但保护机制生效",experienceGained:8};const a=Math.random();let i=0;for(const n of s.consequences)if(i+=n.probability,a<=i)return this.applyFailureConsequence(e,n);return{success:!1,outputItems:e.inputItems,failureReason:"合成失败",experienceGained:5}}applyFailureConsequence(e,t){var s,a;const i=e.inputItems;switch(t.type){case Ye.KEEP_MATERIALS:return{success:!1,outputItems:i,failureReason:t.description,experienceGained:5};case Ye.LOSE_ONE_ITEM:return{success:!1,outputItems:i.slice(0,i.length-((null==(s=t.details)?void 0:s.itemsLost)||1)),failureReason:t.description,experienceGained:3};case Ye.LOSE_ALL_ITEMS:return{success:!1,outputItems:[],failureReason:t.description,experienceGained:1};case Ye.DOWNGRADE_QUALITY:return{success:!1,outputItems:i.map(e=>this.downgradeItem(e)),failureReason:t.description,experienceGained:4};case Ye.GET_COMPENSATION:const e=((null==(a=t.details)?void 0:a.compensationValue)||50)/100;return{success:!1,outputItems:i,compensationItems:this.createCompensationItems(i,e),failureReason:t.description,experienceGained:6};default:return{success:!1,outputItems:i,failureReason:"未知失败类型",experienceGained:5}}}createCompensationItems(e,t){return[]}downgradeItem(e){const t=this.getLowerRarity(e.rarity);return t&&t!==e.rarity?this.createUpgradedItem(e,t):e}getLowerRarity(e){switch(e){case je.GREEN:return je.GRAY;case je.BLUE:return je.GREEN;case je.ORANGE:return je.BLUE;case je.GOLD:return je.ORANGE;case je.GOLD_RED:return je.GOLD;default:return null}}applyProtectionBonus(e,t){let s=0;for(const a of t)a.includes("basic_protection")?s+=.15:a.includes("advanced_protection")?s+=.25:a.includes("perfect_protection")&&(s+=.4);return Math.min(e+s,.95)}createUpgradedItem(e,t){const s={...e,id:"".concat(e.variety,"_").concat(t,"_").concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9)),rarity:t,level:e.level+1,quality:Math.min(e.quality+Math.floor(20*Math.random())+10,100)},a=1.3+.2*Math.random();s.production.minDaily=Math.floor(e.production.minDaily*a),s.production.maxDaily=Math.floor(e.production.maxDaily*a),s.production.currentDaily=Math.floor((e.production.currentDaily||0)*a);const i=this.getValueMultiplier(t);return s.value.basePrice=Math.floor(e.value.basePrice*i),s.value.currentPrice=Math.floor(e.value.currentPrice*i),s.growth.growthTime=Math.floor(.8*e.growth.growthTime),s.special||(s.special={}),s.special.parentItems=[e.id],s.special.isHybrid=!0,s}getValueMultiplier(e){switch(e){case je.GRAY:return 1;case je.GREEN:return 2.5;case je.BLUE:return 5;case je.ORANGE:return 10;case je.GOLD:return 20;case je.GOLD_RED:return 50;default:return 1}}getTargetRarity(e){switch(e){case je.GRAY:return je.GREEN;case je.GREEN:return je.BLUE;case je.BLUE:return je.ORANGE;case je.ORANGE:return je.GOLD;case je.GOLD:return je.GOLD_RED;case je.GOLD_RED:default:return null}}createSynthesisRecipe(e){const t=e[0].rarity,s=e[0].variety;return{id:"synthesis_".concat(s,"_").concat(t,"_").concat(Date.now()),name:"合成 ".concat(e[0].name),description:"将两个".concat(e[0].name,"合成为更高品质"),inputItems:[{itemId:e[0].id,quantity:2,rarity:t,mustBeSameVariety:!0}],outputItem:{itemId:"",quantity:1,rarity:this.getTargetRarity(t)||t,variety:s,qualityRange:{min:60,max:100}},successRate:this.getBaseSuccessRate(t),cost:{gold:this.getSynthesisCost(t)},requiredLevel:this.getRequiredLevel(t),craftTime:this.getSynthesisTime(t)}}getBaseSuccessRate(e){switch(e){case je.GRAY:return.95;case je.GREEN:return.9;case je.BLUE:return.85;case je.ORANGE:return.75;case je.GOLD:return.6;default:return.5}}getSynthesisCost(e){switch(e){case je.GRAY:return 100;case je.GREEN:return 500;case je.BLUE:return 2e3;case je.ORANGE:return 8e3;case je.GOLD:return 3e4;default:return 1e5}}getRequiredLevel(e){switch(e){case je.GRAY:return 1;case je.GREEN:return 10;case je.BLUE:return 25;case je.ORANGE:return 40;case je.GOLD:return 60;default:return 80}}getSynthesisTime(e){switch(e){case je.GRAY:return 30;case je.GREEN:return 60;case je.BLUE:return 180;case je.ORANGE:return 600;case je.GOLD:return 1800;default:return 3600}}getExperienceReward(e){switch(e){case je.GRAY:return 10;case je.GREEN:return 25;case je.BLUE:return 50;case je.ORANGE:return 100;case je.GOLD:return 200;case je.GOLD_RED:return 500;default:return 10}}generateSessionId(){return"synthesis_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9))}getActiveSessions(){return Array.from(this.activeSessions.values())}getCompletedSessions(){return this.completedSessions.slice(-20)}getSession(e){return this.activeSessions.get(e)||this.completedSessions.find(t=>t.id===e)||null}cancelSynthesis(e){const t=this.activeSessions.get(e);return!(!t||t.status!==we.IN_PROGRESS||(t.status=we.CANCELLED,this.activeSessions.delete(e),this.completedSessions.push(t),this.emit("synthesisCancelled",{sessionId:e}),0))}canSynthesize(e,t){if(e.rarity!==t.rarity)return{canSynthesize:!1,reason:"物品品质必须相同"};if(e.variety!==t.variety)return{canSynthesize:!1,reason:"物品品种必须相同"};if(e.category!==Se.CROP&&e.category!==Se.LIVESTOCK)return{canSynthesize:!1,reason:"该类型物品无法合成"};if(e.rarity===je.GOLD_RED)return{canSynthesize:!1,reason:"已达到最高品质，无法继续合成"};const s=this.getRequiredLevel(e.rarity);return this.playerLevel<s?{canSynthesize:!1,reason:"需要等级 ".concat(s)}:{canSynthesize:!0}}setPlayerLevel(e){this.playerLevel=e}getSynthesisStatistics(){const e=this.completedSessions,t=e.filter(e=>{var t;return null==(t=e.result)?void 0:t.success}),s=e.filter(e=>{var t;return!1===(null==(t=e.result)?void 0:t.success)});return{totalSyntheses:e.length,successfulSyntheses:t.length,failedSyntheses:s.length,successRate:e.length>0?t.length/e.length:0,activeSessions:this.activeSessions.size,totalExperienceGained:e.reduce((e,t)=>{var s;return e+((null==(s=t.result)?void 0:s.experienceGained)||0)},0)}}}class Qe extends ue{constructor(e,s){super(),t(this,"focusToken"),t(this,"gameConfig"),t(this,"sessionStartTime"),t(this,"focusCheckInterval",3e4),t(this,"intervalId"),this.gameConfig=s||{dailyLimit:500,earnRate:1,bonusMultiplier:1.5,focusThreshold:60},this.focusToken={id:"player_focus_token",amount:(null==e?void 0:e.amount)||0,earnedToday:(null==e?void 0:e.earnedToday)||0,totalEarned:(null==e?void 0:e.totalEarned)||0,lastEarnTime:(null==e?void 0:e.lastEarnTime)||Date.now(),dailyLimit:this.gameConfig.dailyLimit,...e},this.sessionStartTime=Date.now(),this.resetDailyLimitIfNeeded()}startFocusSession(){this.sessionStartTime=Date.now(),this.startEarningTokens(),this.emit("focusSessionStarted",{startTime:this.sessionStartTime})}endFocusSession(){const e=Date.now()-this.sessionStartTime;this.stopEarningTokens();const t={tokensEarned:this.getSessionEarnings(),sessionDuration:e};return this.emit("focusSessionEnded",t),t}startEarningTokens(){this.intervalId&&clearInterval(this.intervalId),this.intervalId=setInterval(()=>{this.checkAndEarnTokens()},this.focusCheckInterval)}stopEarningTokens(){this.intervalId&&(clearInterval(this.intervalId),this.intervalId=void 0)}checkAndEarnTokens(){const e=Date.now();if(e-this.sessionStartTime<1e3*this.gameConfig.focusThreshold)return;if(this.focusToken.earnedToday>=this.focusToken.dailyLimit)return void this.emit("dailyLimitReached",{earnedToday:this.focusToken.earnedToday,dailyLimit:this.focusToken.dailyLimit});const t=(e-this.focusToken.lastEarnTime)/6e4,s=Math.floor(t*this.gameConfig.earnRate);s>0&&this.earnTokens(s)}earnTokens(e){const t=Math.min(e,this.focusToken.dailyLimit-this.focusToken.earnedToday);t<=0||(this.focusToken.amount+=t,this.focusToken.earnedToday+=t,this.focusToken.totalEarned+=t,this.focusToken.lastEarnTime=Date.now(),this.emit("tokensEarned",{amount:t,totalAmount:this.focusToken.amount,earnedToday:this.focusToken.earnedToday}),this.saveToStorage())}spendTokens(e){return this.focusToken.amount<e?(this.emit("insufficientTokens",{required:e,available:this.focusToken.amount}),!1):(this.focusToken.amount-=e,this.emit("tokensSpent",{amount:e,remaining:this.focusToken.amount}),this.saveToStorage(),!0)}getTokenAmount(){return this.focusToken.amount}getTodayEarned(){return this.focusToken.earnedToday}getDailyLimit(){return this.focusToken.dailyLimit}getSessionEarnings(){const e=Date.now()-this.sessionStartTime,t=Math.max(0,e-1e3*this.gameConfig.focusThreshold)/6e4;return Math.floor(t*this.gameConfig.earnRate)}getBonusMultiplier(){return(Date.now()-this.sessionStartTime)/36e5>=1?this.gameConfig.bonusMultiplier:1}resetDailyLimitIfNeeded(){const e=new Date,t=new Date(this.focusToken.lastEarnTime);e.getDate()===t.getDate()&&e.getMonth()===t.getMonth()&&e.getFullYear()===t.getFullYear()||(this.focusToken.earnedToday=0,this.emit("dailyLimitReset",{date:e.toDateString(),dailyLimit:this.focusToken.dailyLimit}),this.saveToStorage())}getRemainingDailyLimit(){return Math.max(0,this.focusToken.dailyLimit-this.focusToken.earnedToday)}isAtDailyLimit(){return this.focusToken.earnedToday>=this.focusToken.dailyLimit}getTokenData(){return{...this.focusToken}}setGameConfig(e){this.gameConfig={...this.gameConfig,...e}}saveToStorage(){try{localStorage.setItem("focus_token_data",JSON.stringify(this.focusToken))}catch(e){}}static loadFromStorage(){try{const e=localStorage.getItem("focus_token_data");return e?JSON.parse(e):null}catch(e){return null}}addBonusTokens(e,t){this.focusToken.amount+=e,this.focusToken.totalEarned+=e,this.emit("bonusTokensAdded",{amount:e,reason:t,totalAmount:this.focusToken.amount}),this.saveToStorage()}getStatistics(){const e=Date.now()-this.sessionStartTime;return{totalTokens:this.focusToken.amount,earnedToday:this.focusToken.earnedToday,totalEarned:this.focusToken.totalEarned,dailyLimit:this.focusToken.dailyLimit,remainingDaily:this.getRemainingDailyLimit(),currentSessionDuration:e,currentSessionEarnings:this.getSessionEarnings(),bonusMultiplier:this.getBonusMultiplier(),isAtDailyLimit:this.isAtDailyLimit()}}destroy(){this.stopEarningTokens(),this.removeAllListeners()}}const Ke=[{id:"gray_to_green",name:"初级合成",description:"将3个灰色物品合成为1个绿色物品",requiredItems:[{rarity:je.GRAY,quantity:3}],resultRarity:je.GREEN,successRate:.95},{id:"green_to_blue",name:"中级合成",description:"将3个绿色物品合成为1个蓝色物品",requiredItems:[{rarity:je.GREEN,quantity:3}],resultRarity:je.BLUE,successRate:.9},{id:"blue_to_orange",name:"高级合成",description:"将3个蓝色物品合成为1个橙色物品",requiredItems:[{rarity:je.BLUE,quantity:3}],resultRarity:je.ORANGE,successRate:.85},{id:"orange_to_gold",name:"精英合成",description:"将3个橙色物品合成为1个金色物品",requiredItems:[{rarity:je.ORANGE,quantity:3}],resultRarity:je.GOLD,successRate:.75},{id:"gold_to_golden_red",name:"传奇合成",description:"将5个金色物品合成为1个金红色物品",requiredItems:[{rarity:je.GOLD,quantity:5}],resultRarity:je.GOLD_RED,successRate:.6},{id:"cross_category_green",name:"产业融合（绿色）",description:"将2个灰色农产品和2个灰色工业品合成为1个绿色物品",requiredItems:[{rarity:je.GRAY,quantity:2,category:Re.AGRICULTURAL},{rarity:je.GRAY,quantity:2,category:Re.INDUSTRIAL}],resultRarity:je.GREEN,successRate:.85},{id:"cross_category_blue",name:"产业融合（蓝色）",description:"将2个绿色农产品和2个绿色工业品合成为1个蓝色物品",requiredItems:[{rarity:je.GREEN,quantity:2,category:Re.AGRICULTURAL},{rarity:je.GREEN,quantity:2,category:Re.INDUSTRIAL}],resultRarity:je.BLUE,successRate:.8},{id:"agricultural_boost",name:"农业强化",description:"将4个相同品质的农产品合成为高一级品质的农产品",requiredItems:[{rarity:je.GREEN,quantity:4,category:Re.AGRICULTURAL}],resultRarity:je.BLUE,successRate:.88},{id:"industrial_boost",name:"工业强化",description:"将4个相同品质的工业品合成为高一级品质的工业品",requiredItems:[{rarity:je.GREEN,quantity:4,category:Re.INDUSTRIAL}],resultRarity:je.BLUE,successRate:.88}],Xe={wheat_seed_gray:{id:"wheat_seed_gray",name:"普通小麦种子",description:"品质普通的小麦种子，适合新手种植",category:Re.AGRICULTURAL,type:_e.SEED,rarity:je.GRAY,icon:"🌱",value:10,stackable:!0,tradeable:!0,synthesizable:!0,metadata:{yieldMultiplier:1,growthSpeed:1,qualityBonus:0,futuresPrice:2500,priceVolatility:.25,marketDemand:.7}},corn_seed_gray:{id:"corn_seed_gray",name:"普通玉米种子",description:"常见的玉米种子，产量稳定",category:Re.AGRICULTURAL,type:_e.SEED,rarity:je.GRAY,icon:"🌽",value:8,stackable:!0,tradeable:!0,synthesizable:!0,metadata:{yieldMultiplier:1,growthSpeed:1.1,qualityBonus:0,futuresPrice:2800,priceVolatility:.22,marketDemand:.8}},wheat_seed_green:{id:"wheat_seed_green",name:"优质小麦种子",description:"经过改良的小麦种子，产量更高",category:Re.AGRICULTURAL,type:_e.SEED,rarity:je.GREEN,icon:"🌾",value:25,stackable:!0,tradeable:!0,synthesizable:!0,metadata:{yieldMultiplier:1.2,growthSpeed:1,qualityBonus:10,futuresPrice:2600,priceVolatility:.25,marketDemand:.75,synthesisRecipes:["wheat_upgrade"]}},premium_wheat_seed:{id:"premium_wheat_seed",name:"强筋小麦种子",description:"高品质强筋小麦种子，制作面包的首选",category:Re.AGRICULTURAL,type:_e.SEED,rarity:je.BLUE,icon:"🌾",value:60,stackable:!0,tradeable:!0,synthesizable:!0,metadata:{yieldMultiplier:1.5,growthSpeed:.9,qualityBonus:25,futuresPrice:2800,priceVolatility:.3,marketDemand:.9}},basic_hoe:{id:"basic_hoe",name:"基础锄头",description:"简单的农具，用于翻土播种",category:Re.AGRICULTURAL,type:_e.FARM_TOOL,rarity:je.GRAY,icon:"🥄",value:15,stackable:!1,tradeable:!0,synthesizable:!0,metadata:{efficiency:1,effects:["basic_farming"]}},iron_plow:{id:"iron_plow",name:"铁制犁",description:"坚固的铁制犁，大幅提高耕作效率",category:Re.AGRICULTURAL,type:_e.FARM_TOOL,rarity:je.GREEN,icon:"🚜",value:45,stackable:!1,tradeable:!0,synthesizable:!0,metadata:{efficiency:1.5,effects:["efficient_farming","soil_improvement"]}},golden_wheat_seed:{id:"golden_wheat_seed",name:"黄金小麦种子",description:"传说中的黄金小麦种子，据说能结出金色的麦穗",category:Re.AGRICULTURAL,type:_e.SEED,rarity:je.ORANGE,icon:"🏆",value:200,stackable:!0,tradeable:!1,synthesizable:!0,metadata:{yieldMultiplier:2.5,growthSpeed:.8,qualityBonus:50,futuresPrice:3500,priceVolatility:.4,marketDemand:1,effects:["golden_harvest","luck_boost"]}},legendary_farm_tractor:{id:"legendary_farm_tractor",name:"传说拖拉机",description:"智能化农业拖拉机，能够自动化完成各种农作业",category:Re.AGRICULTURAL,type:_e.FARM_TOOL,rarity:je.GOLD,icon:"🚛",value:1e3,stackable:!1,tradeable:!1,synthesizable:!1,metadata:{efficiency:5,effects:["auto_farming","smart_irrigation","yield_optimization"],capacity:100}},mythical_harvest_altar:{id:"mythical_harvest_altar",name:"神话丰收祭坛",description:"古老的丰收祭坛，能够祝福整个农场",category:Re.AGRICULTURAL,type:_e.FARM_BUILDING,rarity:je.GOLD_RED,icon:"⛩️",value:5e3,stackable:!1,tradeable:!1,synthesizable:!1,metadata:{efficiency:10,effects:["divine_blessing","weather_control","time_acceleration","mythical_yield"],capacity:1e3}},iron_ore:{id:"iron_ore",name:"铁矿石",description:"工业生产的基础原材料",category:Re.INDUSTRIAL,type:_e.RAW_MATERIAL,rarity:je.GRAY,icon:"⚫",value:5,stackable:!0,tradeable:!0,synthesizable:!0,metadata:{productionSpeed:1}},steel_gear:{id:"steel_gear",name:"钢制齿轮",description:"精密的钢制齿轮，机械设备的重要组件",category:Re.INDUSTRIAL,type:_e.COMPONENT,rarity:je.GREEN,icon:"⚙️",value:30,stackable:!0,tradeable:!0,synthesizable:!0,metadata:{productionSpeed:1.3,efficiency:1.2}},processing_machine:{id:"processing_machine",name:"加工机械",description:"自动化农产品加工机械",category:Re.INDUSTRIAL,type:_e.MACHINERY,rarity:je.BLUE,icon:"🏭",value:150,stackable:!1,tradeable:!0,synthesizable:!0,metadata:{productionSpeed:2,efficiency:1.8,capacity:50}},smart_factory_line:{id:"smart_factory_line",name:"智能生产线",description:"全自动化智能生产线，效率极高",category:Re.INDUSTRIAL,type:_e.MACHINERY,rarity:je.ORANGE,icon:"🤖",value:800,stackable:!1,tradeable:!1,synthesizable:!0,metadata:{productionSpeed:5,efficiency:4,capacity:200,effects:["ai_optimization","quality_control"]}},mega_factory_complex:{id:"mega_factory_complex",name:"超级工厂综合体",description:"巨型工业综合体，能够处理整个产业链",category:Re.INDUSTRIAL,type:_e.FACTORY_BUILDING,rarity:je.GOLD,icon:"🏢",value:3e3,stackable:!1,tradeable:!1,synthesizable:!1,metadata:{productionSpeed:10,efficiency:8,capacity:1e3,effects:["full_automation","supply_chain_optimization","global_network"]}},focus_coin_small:{id:"focus_coin_small",name:"专注币包(小)",description:"包含50个专注币",category:Re.AGRICULTURAL,type:_e.CURRENCY,rarity:je.GRAY,icon:"💰",value:50,stackable:!0,tradeable:!1,synthesizable:!1,metadata:{}},focus_coin_large:{id:"focus_coin_large",name:"专注币包(大)",description:"包含500个专注币",category:Re.AGRICULTURAL,type:_e.CURRENCY,rarity:je.BLUE,icon:"💎",value:500,stackable:!0,tradeable:!1,synthesizable:!1,metadata:{}},growth_booster:{id:"growth_booster",name:"生长加速剂",description:"临时加速作物生长，持续2小时",category:Re.AGRICULTURAL,type:_e.BOOST,rarity:je.GREEN,icon:"🧪",value:80,stackable:!0,tradeable:!0,synthesizable:!0,metadata:{effects:["growth_speed_x2"],duration:72e5}}};class Je{constructor(e=100){t(this,"state"),t(this,"listeners",[]),this.state={items:[],maxSlots:e,usedSlots:0}}subscribe(e){return this.listeners.push(e),()=>{this.listeners=this.listeners.filter(t=>t!==e)}}notify(){this.state.usedSlots=this.state.items.reduce((e,t)=>e+t.quantity,0),this.listeners.forEach(e=>e({...this.state}))}getState(){return{...this.state}}addItem(e,t=1){if(this.state.usedSlots+t>this.state.maxSlots)return!1;const s=this.state.items.find(t=>t.itemId===e.id);if(s)s.quantity+=t;else{const s={id:"inv_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9)),itemId:e.id,name:e.name,rarity:e.rarity,category:e.category,type:e.type,description:e.description,icon:e.icon,quantity:t,obtainedAt:Date.now()};this.state.items.push(s)}return this.notify(),!0}removeItem(e,t=1){const s=this.state.items.find(t=>t.id===e);return!(!s||s.quantity<t||(s.quantity-=t,s.quantity<=0&&(this.state.items=this.state.items.filter(t=>t.id!==e)),this.notify(),0))}getItemStats(){const e=new Map;return this.state.items.forEach(t=>{const s="".concat(t.rarity,"_").concat(t.category);e.set(s,(e.get(s)||0)+t.quantity)}),Array.from(e.entries()).map(([e,t])=>{const[s,a]=e.split("_");return{rarity:s,category:a,quantity:t}})}getAvailableRecipes(){const e=this.getItemStats();return t=e,Ke.filter(e=>e.requiredItems.every(e=>t.filter(t=>{const s=t.rarity===e.rarity,a=!e.category||t.category===e.category;return s&&a}).reduce((e,t)=>e+t.quantity,0)>=e.quantity));var t}synthesize(e){const t=Ke.find(t=>t.id===e);if(!t)return{success:!1,consumedItems:[],message:"未找到合成配方"};const s=[];let a=!0;for(const n of t.requiredItems){let e=n.quantity;const t=this.state.items.filter(e=>{const t=e.rarity===n.rarity,s=!n.category||e.category===n.category;return t&&s}).sort((e,t)=>e.quantity-t.quantity);for(const a of t){if(e<=0)break;const t=Math.min(e,a.quantity);s.push({...a,quantity:t}),e-=t}if(e>0){a=!1;break}}if(!a)return{success:!1,consumedItems:[],message:"材料不足"};if(!(Math.random()<t.successRate))return this.consumeItems(s),{success:!1,consumedItems:s,message:"合成失败！材料已消耗"};this.consumeItems(s);const i=this.generateResultItem(t.resultRarity,s[0].category);return i&&this.addItem(i,1),{success:!0,resultItem:i?{id:"inv_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9)),itemId:i.id,name:i.name,rarity:i.rarity,category:i.category,type:i.type,description:i.description,icon:i.icon,quantity:1,obtainedAt:Date.now()}:void 0,consumedItems:s,message:"合成成功！"}}consumeItems(e){for(const t of e)this.removeItem(t.id,t.quantity)}generateResultItem(e,t){let s=Object.values(Xe).filter(t=>t.rarity===e);if(t){const e=s.filter(e=>e.category===t);e.length>0&&(s=e)}return 0===s.length?null:s[Math.floor(Math.random()*s.length)]}getItemsByRarity(){const e={};return this.state.items.forEach(t=>{e[t.rarity]||(e[t.rarity]=[]),e[t.rarity].push(t)}),e}getItemsByCategory(){const e={};return this.state.items.forEach(t=>{e[t.category]||(e[t.category]=[]),e[t.category].push(t)}),e}clear(){this.state.items=[],this.notify()}expandSlots(e){this.state.maxSlots+=e,this.notify()}}class Ze extends ue{constructor(){super(),t(this,"lootBoxManager"),t(this,"farmManager"),t(this,"synthesisManager"),t(this,"focusTokenManager"),t(this,"inventorySystem"),t(this,"integratedItems",new Map),t(this,"conversionConfig"),this.inventorySystem=new Je,this.farmManager=new He,this.synthesisManager=new Ve;const e=new Qe;this.lootBoxManager=new ze(e),this.focusTokenManager=e,this.conversionConfig=this.initializeConversionConfig(),this.setupEventListeners()}initializeConversionConfig(){return{lootboxToAgricultural:{wheat_seed_gray:{variety:"wheat",categoryMapping:Re.AGRICULTURAL,typeMapping:_e.SEED},wheat_seed_green:{variety:"wheat",categoryMapping:Re.AGRICULTURAL,typeMapping:_e.SEED},corn_seed_gray:{variety:"corn",categoryMapping:Re.AGRICULTURAL,typeMapping:_e.SEED},corn_seed_green:{variety:"corn",categoryMapping:Re.AGRICULTURAL,typeMapping:_e.SEED},premium_wheat_seed:{variety:"wheat",categoryMapping:Re.AGRICULTURAL,typeMapping:_e.SEED},golden_wheat_seed:{variety:"wheat",categoryMapping:Re.AGRICULTURAL,typeMapping:_e.SEED}},agriculturalToLootbox:{wheat:{baseItemId:"wheat_seed",categoryMapping:Re.AGRICULTURAL,typeMapping:_e.SEED},corn:{baseItemId:"corn_seed",categoryMapping:Re.AGRICULTURAL,typeMapping:_e.SEED},rice:{baseItemId:"rice_seed",categoryMapping:Re.AGRICULTURAL,typeMapping:_e.SEED}}}}setupEventListeners(){this.lootBoxManager.on("lootBoxOpened",e=>{this.handleLootboxResult(e)}),this.farmManager.on("itemHarvested",e=>{this.handleHarvestedItem(e)}),this.synthesisManager.on("synthesisCompleted",e=>{this.handleSynthesisResult(e)})}convertLootboxToIntegrated(e,t=1,s=""){return{id:this.generateUniqueId("lootbox"),name:e.name,description:e.description,category:e.category,type:e.type,rarity:e.rarity,icon:e.icon,value:e.value,quantity:t,stackable:e.stackable,tradeable:e.tradeable,synthesizable:e.synthesizable,metadata:{...e.metadata},status:{},source:{type:"lootbox",sourceId:s,timestamp:Date.now()}}}convertAgriculturalToIntegrated(e){var t;return{id:e.id,name:e.name,description:e.description,category:e.category,type:_e.CROP,rarity:e.rarity,icon:"🌾",value:e.value.basePrice,quantity:1,stackable:!0,tradeable:!0,synthesizable:!0,metadata:{yieldMultiplier:(e.production.currentDaily||e.production.minDaily)/e.production.minDaily,growthSpeed:24/e.growth.growthTime,qualityBonus:e.quality},status:{isPlanted:!!(null==(t=e.location)?void 0:t.farmSlotId),isGrowing:e.growth.currentStage!==Ee.SEED&&!e.growth.isReady,isReady:e.growth.isReady,canHarvest:e.growth.isReady,plantedTime:e.growth.plantedTime},source:{type:"farm",timestamp:Date.now()},agriculturalData:{variety:e.variety,yieldMultiplier:(e.production.currentDaily||e.production.minDaily)/e.production.minDaily,growthTime:e.growth.growthTime,quality:e.quality}}}convertIntegratedToAgricultural(e){var t,s,a,i,n,r,o;return{id:e.id,name:e.name,nameEn:(null==(t=e.agriculturalData)?void 0:t.variety)||"crop",description:e.description,rarity:e.rarity,category:e.category,variety:(null==(s=e.agriculturalData)?void 0:s.variety)||"corn",level:1,quality:(null==(a=e.agriculturalData)?void 0:a.quality)||1,production:{minDaily:1,maxDaily:5,currentDaily:Math.floor(1+4*((null==(i=e.agriculturalData)?void 0:i.yieldMultiplier)||1))},value:{basePrice:e.value,currentPrice:e.value,marketDemand:1,priceHistory:[]},growth:{growthTime:(null==(n=e.agriculturalData)?void 0:n.growthTime)||4,currentStage:Ee.SEED,isReady:!1,needsWater:!0,needsFertilizer:!0},sprite:"".concat((null==(r=e.agriculturalData)?void 0:r.variety)||"crop","_").concat(e.rarity),animation:"".concat((null==(o=e.agriculturalData)?void 0:o.variety)||"crop","_grow"),sound:"crop_sound"}}async openLootbox(e){try{const t=await this.lootBoxManager.openLootBox(e);if(t.success&&t.items){const e=[];for(const s of t.items)if(s&&"object"==typeof s){const t=s,a={id:t.id||this.generateId(),name:t.name||"Unknown Item",description:t.description||"",category:Re.AGRICULTURAL,type:_e.CROP,rarity:t.rarity||je.GRAY,icon:t.icon||"🌾",value:t.value||10,quantity:1,stackable:!0,tradeable:!0,synthesizable:!0,metadata:{},status:{},source:{type:"lootbox",timestamp:Date.now()}};this.addIntegratedItem(a),e.push(a)}return{success:!0,items:e}}return{success:!1,items:[],error:t.message||"Failed to open lootbox"}}catch(t){return{success:!1,items:[],error:t instanceof Error?t.message:"Unknown error"}}}async plantItem(e,t){var s,a,i,n;try{const r=(null==(a=(s=this.inventorySystem).getItem)?void 0:a.call(s,e))||this.integratedItems.get(e);if(!r)return{success:!1,message:"Item not found"};const o=this.convertIntegratedToAgricultural(r),c=await(null==(n=(i=this.farmManager).plant)?void 0:n.call(i,o,t))||{success:!0,message:"Item planted successfully"};return c.success&&(this.inventorySystem.removeItem?this.inventorySystem.removeItem(e):this.integratedItems.delete(e),this.emit("itemPlanted",{item:r,slotId:t})),c}catch(r){return{success:!1,message:r instanceof Error?r.message:"Planting failed"}}}async synthesizeItems(e){var t,s;try{if(2!==e.length)return{success:!1,error:"Exactly 2 items required for synthesis"};const a=e.map(e=>{var t,s;return(null==(s=(t=this.inventorySystem).getItem)?void 0:s.call(t,e))||this.integratedItems.get(e)}).filter(Boolean);if(2!==a.length)return{success:!1,error:"One or more items not found"};const i=a.map(e=>this.convertIntegratedToAgricultural(e)),n=await(null==(s=(t=this.synthesisManager).synthesizeItems)?void 0:s.call(t,i[0],i[1]))||this.createMockSynthesisResult(i[0],i[1]);if(n.success){e.forEach(e=>{this.inventorySystem.removeItem?this.inventorySystem.removeItem(e):this.integratedItems.delete(e)});const t=this.convertAgriculturalToIntegrated(n.result);return this.inventorySystem.addItem?this.inventorySystem.addItem(t,1):this.integratedItems.set(t.id,t),this.emit("itemsSynthesized",{sourceItems:a,resultItem:t}),{success:!0,resultItem:t}}return{success:!1,error:n.message||"Synthesis failed"}}catch(a){return{success:!1,error:a instanceof Error?a.message:"Synthesis error"}}}createMockSynthesisResult(e,t){const s=this.getNextRarity(e.rarity);return{success:Math.random()>.3,result:{...e,id:this.generateId(),rarity:s,value:{...e.value,basePrice:2*e.value.basePrice,currentPrice:2*e.value.currentPrice},production:{...e.production,minDaily:1.5*e.production.minDaily,maxDaily:1.5*e.production.maxDaily}},message:"Synthesis completed"}}getNextRarity(e){const t=[je.GRAY,je.GREEN,je.BLUE,je.ORANGE,je.GOLD,je.GOLD_RED],s=t.indexOf(e);return s<t.length-1?t[s+1]:e}addIntegratedItem(e){if(e.stackable){const t=this.findStackableItem(e);if(t)return t.quantity+=e.quantity,void this.emit("itemUpdated",t)}this.integratedItems.set(e.id,e),this.emit("itemAdded",e)}removeIntegratedItem(e,t=1){const s=this.integratedItems.get(e);return!!s&&(s.quantity<=t?(this.integratedItems.delete(e),this.emit("itemRemoved",s)):(s.quantity-=t,this.emit("itemUpdated",s)),!0)}findStackableItem(e){for(const t of this.integratedItems.values())if(t.name===e.name&&t.rarity===e.rarity&&t.stackable&&t.id!==e.id)return t}handleLootboxResult(e){}handleHarvestedItem(e){const t=this.convertAgriculturalToIntegrated(e);t.source.type="farm",this.addIntegratedItem(t),this.emit("itemHarvested",t)}handleSynthesisResult(e){}generateUniqueId(e){return"".concat(e,"_").concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9))}generateId(){return"item_"+Date.now()+"_"+Math.random().toString(36).substr(2,9)}addManualItem(e){const t={id:e.id||this.generateId(),name:e.name||"Unknown Item",description:e.description||"",category:e.category||Re.AGRICULTURAL,type:e.type||_e.CROP,rarity:e.rarity||je.GRAY,icon:e.icon||"🌾",value:e.value||10,quantity:e.quantity||1,stackable:!1!==e.stackable,tradeable:!1!==e.tradeable,synthesizable:!1!==e.synthesizable,metadata:e.metadata||{},status:e.status||{},source:e.source||{type:"manual",timestamp:Date.now()},agriculturalData:e.agriculturalData};return this.addIntegratedItem(t),t}getAllItems(){return Array.from(this.integratedItems.values())}getItemsByCategory(e){return this.getAllItems().filter(t=>t.category===e)}getItemsByType(e){return this.getAllItems().filter(t=>t.type===e)}getItemsByRarity(e){return this.getAllItems().filter(t=>t.rarity===e)}searchItems(e){const t=e.toLowerCase();return this.getAllItems().filter(e=>e.name.toLowerCase().includes(t)||e.description.toLowerCase().includes(t))}getPlantableSeeds(){return this.getAllItems().filter(e=>e.type===_e.SEED&&e.category===Re.AGRICULTURAL&&e.quantity>0)}getSynthesizableItems(){return this.getAllItems().filter(e=>e.synthesizable&&e.quantity>0)}onItemAdded(e){this.on("itemAdded",e)}onItemUpdated(e){this.on("itemUpdated",e)}onItemRemoved(e){this.on("itemRemoved",e)}onItemPlanted(e){this.on("itemPlanted",e)}onItemHarvested(e){this.on("itemHarvested",e)}onItemsSynthesized(e){this.on("itemsSynthesized",e)}onLootboxIntegrated(e){this.on("lootboxIntegrated",e)}}class $e extends X.Scene{constructor(){super({key:"UnifiedAgriculturalScene"}),t(this,"itemManager"),t(this,"farmSlots",new Map),t(this,"gridSize",{width:6,height:4}),t(this,"slotSize",80),t(this,"startX",100),t(this,"startY",150),t(this,"uiContainer"),t(this,"inventoryContainer"),t(this,"lootboxContainer"),t(this,"synthesisContainer"),t(this,"selectedItems",[]),t(this,"currentMode","farm")}preload(){this.load.image("slot_empty","data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==")}create(){this.initializeManagers(),this.createUI(),this.createFarmGrid(),this.addTestItems(),this.setupControls()}initializeManagers(){this.itemManager=new Ze,this.itemManager.onItemAdded(e=>{this.updateUI()}),this.itemManager.onItemPlanted(e=>{this.updateFarmDisplay()}),this.itemManager.onItemHarvested(e=>{this.updateFarmDisplay(),this.updateUI()}),this.itemManager.onItemsSynthesized(e=>{this.updateUI()})}createUI(){this.add.rectangle(400,300,800,600,8900331,.3),this.add.text(400,30,"🌾 农产品物品道具数值策划系统",{fontSize:"24px",color:"#2C5530",fontFamily:"Arial Black",fontStyle:"bold"}).setOrigin(.5),this.uiContainer=this.add.container(0,0),this.createModeButtons(),this.createInventoryPanel(),this.createLootboxPanel(),this.createSynthesisPanel(),this.createStatsPanel()}createModeButtons(){[{key:"farm",label:"🚜 农场",x:100},{key:"inventory",label:"🎒 背包",x:200},{key:"lootbox",label:"📦 盲盒",x:300},{key:"synthesis",label:"⚗️ 合成",x:400}].forEach(e=>{const t=this.add.container(e.x,70),s=this.add.rectangle(0,0,90,30,5025616,.8);s.setStrokeStyle(2,3046706);const a=this.add.text(0,0,e.label,{fontSize:"12px",color:"#FFFFFF",fontFamily:"Arial"}).setOrigin(.5);t.add([s,a]),t.setInteractive(new X.Geom.Rectangle(-45,-15,90,30),X.Geom.Rectangle.Contains),t.on("pointerdown",()=>{this.switchMode(e.key)}),t.on("pointerover",()=>{s.setFillStyle(6732650,.9)}),t.on("pointerout",()=>{const t=this.currentMode===e.key;s.setFillStyle(t?3046706:5025616,.8)})})}createFarmGrid(){for(let e=0;e<this.gridSize.height;e++)for(let t=0;t<this.gridSize.width;t++){const s="slot_".concat(t,"_").concat(e),a=this.startX+t*(this.slotSize+10),i=this.startY+e*(this.slotSize+10),n=this.add.container(a,i),r=this.add.rectangle(0,0,this.slotSize,this.slotSize,9127187,.6);r.setStrokeStyle(2,6636321);const o=this.add.text(0,0,"🌱",{fontSize:"32px"}).setOrigin(.5).setVisible(!1);n.add([r,o]),n.setData("slotId",s),n.setData("isEmpty",!0),n.setData("statusIcon",o),n.setInteractive(new X.Geom.Rectangle(-this.slotSize/2,-this.slotSize/2,this.slotSize,this.slotSize),X.Geom.Rectangle.Contains),n.on("pointerdown",()=>{this.handleSlotClick(s)}),n.on("pointerover",()=>{r.setStrokeStyle(3,16766720)}),n.on("pointerout",()=>{r.setStrokeStyle(2,6636321)}),this.farmSlots.set(s,n)}}createInventoryPanel(){this.inventoryContainer=this.add.container(550,150);const e=this.add.rectangle(0,0,220,350,2905392,.9);e.setStrokeStyle(2,5025616);const t=this.add.text(0,-160,"🎒 物品背包",{fontSize:"16px",color:"#FFFFFF",fontFamily:"Arial",fontStyle:"bold"}).setOrigin(.5);this.inventoryContainer.add([e,t]),this.inventoryContainer.setVisible(!1)}createLootboxPanel(){this.lootboxContainer=this.add.container(550,150);const e=this.add.rectangle(0,0,220,350,10233776,.9);e.setStrokeStyle(2,15277667);const t=this.add.text(0,-160,"📦 盲盒抽奖",{fontSize:"16px",color:"#FFFFFF",fontFamily:"Arial",fontStyle:"bold"}).setOrigin(.5);[{type:Ie.BASIC_FARM,label:"基础农场盒",cost:"50🪙",y:-100},{type:Ie.PREMIUM_FARM,label:"高级农场盒",cost:"200🪙",y:-50},{type:Ie.LEGENDARY_FARM,label:"传说农场盒",cost:"500🪙",y:0},{type:Ie.FUTURES_MYSTERY,label:"期货神秘盒",cost:"300🪙",y:50}].forEach(e=>{const t=this.add.container(0,e.y),s=this.add.rectangle(0,0,180,35,16750592,.8);s.setStrokeStyle(1,16088064);const a=this.add.text(0,-8,e.label,{fontSize:"12px",color:"#FFFFFF",fontFamily:"Arial",fontStyle:"bold"}).setOrigin(.5),i=this.add.text(0,8,e.cost,{fontSize:"10px",color:"#FFE0B2",fontFamily:"Arial"}).setOrigin(.5);t.add([s,a,i]),t.setInteractive(new X.Geom.Rectangle(-90,-17.5,180,35),X.Geom.Rectangle.Contains),t.on("pointerdown",()=>{this.openLootbox(e.type)}),this.lootboxContainer.add(t)}),this.lootboxContainer.add([e,t]),this.lootboxContainer.setVisible(!1)}createSynthesisPanel(){this.synthesisContainer=this.add.container(550,150);const e=this.add.rectangle(0,0,220,350,6765239,.9);e.setStrokeStyle(2,10233776);const t=this.add.text(0,-160,"⚗️ 道具合成",{fontSize:"16px",color:"#FFFFFF",fontFamily:"Arial",fontStyle:"bold"}).setOrigin(.5),s=this.add.text(0,-120,"选择2个同品质同品种道具\n进行合成升级",{fontSize:"12px",color:"#E1BEE7",fontFamily:"Arial",align:"center"}).setOrigin(.5),a=this.add.container(0,100),i=this.add.rectangle(0,0,150,40,5025616,.8);i.setStrokeStyle(2,3046706);const n=this.add.text(0,0,"开始合成",{fontSize:"14px",color:"#FFFFFF",fontFamily:"Arial",fontStyle:"bold"}).setOrigin(.5);a.add([i,n]),a.setInteractive(new X.Geom.Rectangle(-75,-20,150,40),X.Geom.Rectangle.Contains),a.on("pointerdown",()=>{this.performSynthesis()}),this.synthesisContainer.add([e,t,s,a]),this.synthesisContainer.setVisible(!1)}createStatsPanel(){const e=this.add.container(650,450),t=this.add.rectangle(0,0,140,120,6323595,.9);t.setStrokeStyle(2,4545124);const s=this.add.text(0,-45,"📊 统计",{fontSize:"14px",color:"#FFFFFF",fontFamily:"Arial",fontStyle:"bold"}).setOrigin(.5),a=this.add.text(0,-10,"总物品: 0\n种植中: 0\n可收获: 0",{fontSize:"12px",color:"#CFD8DC",fontFamily:"Arial",align:"center"}).setOrigin(.5);e.add([t,s,a]),e.setData("statsText",a)}switchMode(e){switch(this.currentMode=e,this.inventoryContainer.setVisible(!1),this.lootboxContainer.setVisible(!1),this.synthesisContainer.setVisible(!1),e){case"inventory":this.inventoryContainer.setVisible(!0),this.updateInventoryDisplay();break;case"lootbox":this.lootboxContainer.setVisible(!0);break;case"synthesis":this.synthesisContainer.setVisible(!0)}this.updateModeButtons()}updateModeButtons(){}handleSlotClick(e){if("farm"!==this.currentMode)return;const t=this.farmSlots.get(e);t&&(t.getData("isEmpty")?this.plantOnSlot(e):this.harvestFromSlot(e))}async plantOnSlot(e){const t=this.itemManager.getPlantableSeeds();if(0===t.length)return void this.showNotification("没有可种植的种子！",16733986);const s=t[0],a=await this.itemManager.plantItem(s.id,e);a.success?this.showNotification("成功种植 ".concat(s.name,"！"),5025616):this.showNotification("种植失败: ".concat(a.message),16733986)}async harvestFromSlot(e){this.showNotification("收获功能待实现",16761095)}async openLootbox(e){const t=await this.itemManager.openLootbox(e);if(t.success){const e=t.items.map(e=>e.name).join(", ");this.showNotification("获得: ".concat(e),5025616)}else this.showNotification("开盒失败: ".concat(t.error),16733986)}async performSynthesis(){var e;if(2!==this.selectedItems.length)return void this.showNotification("请选择2个物品进行合成",16761095);const t=await this.itemManager.synthesizeItems(this.selectedItems);t.success?this.showNotification("合成成功！获得 ".concat(null==(e=t.resultItem)?void 0:e.name),5025616):this.showNotification("合成失败: ".concat(t.error),16733986),this.selectedItems=[]}addTestItems(){this.time.delayedCall(1e3,()=>{this.openLootbox(Ie.BASIC_FARM)}),this.time.delayedCall(2e3,()=>{this.openLootbox(Ie.PREMIUM_FARM)})}updateFarmDisplay(){this.farmSlots.forEach((e,t)=>{e.getData("statusIcon").setVisible(!1),e.setData("isEmpty",!0)})}updateInventoryDisplay(){this.itemManager.getAllItems()}updateUI(){const e=this.itemManager.getAllItems().reduce((e,t)=>e+t.quantity,0),t=this.children.list.find(e=>e instanceof X.GameObjects.Container&&e.getData("statsText"));t&&t.getData("statsText").setText("总物品: ".concat(e,"\n种植中: 0\n可收获: 0"))}setupControls(){var e,t,s,a,i;null==(e=this.input.keyboard)||e.on("keydown-ONE",()=>this.switchMode("farm")),null==(t=this.input.keyboard)||t.on("keydown-TWO",()=>this.switchMode("inventory")),null==(s=this.input.keyboard)||s.on("keydown-THREE",()=>this.switchMode("lootbox")),null==(a=this.input.keyboard)||a.on("keydown-FOUR",()=>this.switchMode("synthesis")),null==(i=this.input.keyboard)||i.on("keydown-ESC",()=>{this.scene.start("MainScene")})}showNotification(e,t=5025616){const s=this.add.text(400,500,e,{fontSize:"16px",color:"#FFFFFF",fontFamily:"Arial",backgroundColor:"#".concat(t.toString(16).padStart(6,"0")),padding:{x:10,y:5}}).setOrigin(.5);s.setAlpha(0),this.tweens.add({targets:s,alpha:1,duration:300,yoyo:!0,hold:2e3,onComplete:()=>{s.destroy()}})}update(){}}let et=class{constructor(){t(this,"listeners",new Map)}on(e,t){return this.listeners.has(e)||this.listeners.set(e,[]),this.listeners.get(e).push(t),this}emit(e,...t){const s=this.listeners.get(e);return!!(s&&s.length>0)&&(s.forEach(e=>{try{e(...t)}catch(s){}}),!0)}removeAllListeners(){return this.listeners.clear(),this}};class tt extends et{constructor(){super(),t(this,"gameState"),t(this,"dbManager",null),t(this,"saveTimer",null),t(this,"autoSaveEnabled",!0),t(this,"autoSaveInterval",3e5),this.gameState=this.createDefaultState()}createDefaultState(){return{version:"1.0.0",playerName:"Player",playerId:"player_"+Date.now(),level:1,experience:0,gameTime:{totalPlayTime:0,lastSaveTime:Date.now(),sessionStartTime:Date.now()},crops:new Map,farmGrid:{height:8,width:8,plots:Array(8).fill(null).map(()=>Array(8).fill(null))},gridSize:{width:8,height:8},resources:{knowledge:10,strength:100,time:5,meditation:0},totalCropsHarvested:0,totalFocusTime:0,unlockedFeatures:new Set,achievements:new Set,settings:{autoSave:!0,autoSaveInterval:3e5,enableNotifications:!0,soundEnabled:!0,musicEnabled:!0},focusSystem:{currentFocusState:{isFocused:!1,focusScore:0,sessionDuration:0,consecutiveFocusTime:0,lastUpdateTime:Date.now()},sessionStats:{bestFocusStreak:0,totalFocusBreaks:0,averageSessionFocus:0,focusStartTime:null},lastPoseDataTime:0,isConnectedToPoseDetection:!1}}}getGameState(){return Object.freeze(JSON.parse(JSON.stringify(this.gameState)))}plantCrop(e,t,s){if(e<0||e>=this.gameState.gridSize.width||t<0||t>=this.gameState.gridSize.height)return!1;if(null!==this.gameState.farmGrid.plots[t][e])return!1;const a=Date.now(),i="crop_"+a+"_"+Math.random().toString(36).substr(2,9),n={id:i,type:s,stage:ee.SEED,quality:se.COMMON,plantedAt:a,stageStartTime:a,totalGrowthTime:0,focusTimeContributed:0,averageFocusScore:0,position:{x:64*e,y:64*t,gridX:e,gridY:t},isGrowing:!0,isPaused:!1,harvestable:!1,metadata:{sessionsContributed:0,bestFocusStreak:0,growthBoosts:0}};return this.gameState.crops.set(i,n),this.gameState.farmGrid.plots[t][e]=n,this.emit("crop_planted",{crop:n,position:{gridX:e,gridY:t}}),!0}harvestCrop(e,t){var s;const a=null==(s=this.gameState.farmGrid.plots[t])?void 0:s[e];if(!a||a.stage!==ee.READY_TO_HARVEST)return{success:!1};const i={experience:10,resources:{[a.type]:5}};return this.gameState.experience+=i.experience,this.gameState.totalCropsHarvested++,this.gameState.crops.delete(a.id),this.gameState.farmGrid.plots[t][e]=null,this.emit("crop_harvested",{crop:a,rewards:i,position:{gridX:e,gridY:t}}),{success:!0,rewards:i,crop:a}}async saveGameState(){try{const e={...this.gameState,crops:Array.from(this.gameState.crops.entries()),unlockedFeatures:Array.from(this.gameState.unlockedFeatures),achievements:Array.from(this.gameState.achievements),focusSystem:{...this.gameState.focusSystem,isConnectedToPoseDetection:!1,lastPoseDataTime:0}};return localStorage.setItem("selfgame_save_data",JSON.stringify(e)),this.gameState.gameTime.lastSaveTime=Date.now(),!0}catch(e){return!1}}async loadGameState(){var e,t;try{const s=localStorage.getItem("selfgame_save_data");if(!s)return!1;const a=JSON.parse(s);return this.gameState={...a,crops:new Map(a.crops),unlockedFeatures:new Set(a.unlockedFeatures||[]),achievements:new Set(a.achievements||[]),focusSystem:{currentFocusState:(null==(e=a.focusSystem)?void 0:e.currentFocusState)||{focusScore:0,isFocused:!1,sessionDuration:0,consecutiveFocusTime:0,lastUpdateTime:Date.now()},sessionStats:(null==(t=a.focusSystem)?void 0:t.sessionStats)||{bestFocusStreak:0,totalFocusBreaks:0,averageSessionFocus:0,focusStartTime:null},isConnectedToPoseDetection:!1,lastPoseDataTime:0}},this.gameState.farmGrid.plots=Array(this.gameState.gridSize.height).fill(null).map(()=>Array(this.gameState.gridSize.width).fill(null)),this.gameState.crops.forEach(e=>{const{gridX:t,gridY:s}=e.position;s<this.gameState.farmGrid.height&&t<this.gameState.farmGrid.width&&(this.gameState.farmGrid.plots[s][t]=e)}),!0}catch(s){return!1}}resetGameState(){this.gameState=this.createDefaultState(),this.emit("game_reset",{timestamp:Date.now()})}updateFocusState(e){const t=this.gameState.focusSystem.currentFocusState,s=Date.now();if(this.gameState.focusSystem.currentFocusState={...e},this.gameState.focusSystem.lastPoseDataTime=s,e.isFocused){const e=s-t.lastUpdateTime;this.gameState.totalFocusTime+=e}this.updateFocusSessionStats(t,e,s),this.emit("focus_state_changed",{previousState:t,currentState:e,timestamp:s}),this.updateCropGrowthWithFocus(e)}updateFocusSessionStats(e,t,s){const a=this.gameState.focusSystem.sessionStats;!e.isFocused&&t.isFocused&&(a.focusStartTime=s),e.isFocused&&!t.isFocused&&(a.totalFocusBreaks++,e.consecutiveFocusTime>a.bestFocusStreak&&(a.bestFocusStreak=e.consecutiveFocusTime),a.focusStartTime=null),t.sessionDuration>0&&(a.averageSessionFocus=this.gameState.totalFocusTime/t.sessionDuration*100)}updateCropGrowthWithFocus(e){this.gameState.crops.forEach(t=>{if(t.isGrowing&&!t.isPaused){const s=.1;if(t.averageFocusScore=t.averageFocusScore*(1-s)+e.focusScore*s,e.isFocused){const s=Date.now()-e.lastUpdateTime;t.focusTimeContributed+=s}}})}setPoseDetectionConnectionStatus(e){this.gameState.focusSystem.isConnectedToPoseDetection=e,this.emit("pose_detection_connection_changed",{isConnected:e,timestamp:Date.now()})}getFocusSystemState(){return Object.freeze(JSON.parse(JSON.stringify(this.gameState.focusSystem)))}getFocusStats(){const e=this.gameState.focusSystem;return{currentFocusScore:e.currentFocusState.focusScore,isFocused:e.currentFocusState.isFocused,consecutiveFocusTime:e.currentFocusState.consecutiveFocusTime,bestFocusStreak:e.sessionStats.bestFocusStreak,totalFocusTime:this.gameState.totalFocusTime,totalFocusBreaks:e.sessionStats.totalFocusBreaks,averageSessionFocus:e.sessionStats.averageSessionFocus,isConnectedToPoseDetection:e.isConnectedToPoseDetection,sessionDuration:e.currentFocusState.sessionDuration}}resetFocusSystem(){const e=Date.now();this.gameState.focusSystem={currentFocusState:{focusScore:0,isFocused:!1,sessionDuration:0,consecutiveFocusTime:0,lastUpdateTime:e},sessionStats:{bestFocusStreak:0,totalFocusBreaks:0,averageSessionFocus:0,focusStartTime:null},isConnectedToPoseDetection:!1,lastPoseDataTime:0},this.emit("focus_system_reset",{timestamp:e})}getGameStats(){return{totalPlayTime:this.gameState.gameTime.totalPlayTime,totalCropsHarvested:this.gameState.totalCropsHarvested,totalFocusTime:this.gameState.totalFocusTime,level:this.gameState.level,experience:this.gameState.experience,nextLevelExp:Math.floor(100*Math.pow(1.5,this.gameState.level-1)),cropsCount:this.gameState.crops.size,resourcesTotal:Object.values(this.gameState.resources).reduce((e,t)=>e+t,0)}}startAutoSave(){this.autoSaveTimer&&clearInterval(this.autoSaveTimer),this.gameState.settings.autoSave&&(this.autoSaveTimer=window.setInterval(()=>{this.saveGameState()},this.gameState.settings.autoSaveInterval))}destroy(){this.autoSaveTimer&&(clearInterval(this.autoSaveTimer),this.autoSaveTimer=null),this.removeAllListeners()}}t(tt,"instance");class st{constructor(e,s){t(this,"scene"),t(this,"gameStateManager"),t(this,"activeAnimations",new Map),t(this,"rewardConfig",{[te.KNOWLEDGE_FLOWER]:{baseExperience:15,baseResources:{knowledge:8},bonusMultiplier:1.2},[te.STRENGTH_TREE]:{baseExperience:25,baseResources:{strength:12},bonusMultiplier:1.5},[te.TIME_VEGGIE]:{baseExperience:20,baseResources:{time:10},bonusMultiplier:1.3},[te.MEDITATION_LOTUS]:{baseExperience:30,baseResources:{meditation:15},bonusMultiplier:1.8}}),t(this,"qualityBonus",{[se.COMMON]:1,[se.UNCOMMON]:1.3,[se.RARE]:1.6,[se.EPIC]:2,[se.LEGENDARY]:3}),this.scene=e,this.gameStateManager=s}async harvestCrop(e,t){var s;const a=null==(s=this.gameStateManager.getGameState().farmGrid.plots[t])?void 0:s[e];if(!a)return{success:!1,message:"该位置没有作物"};if(a.stage!==ee.READY_TO_HARVEST)return{success:!1,message:"作物尚未成熟，无法收获"};const i=this.calculateRewards(a);return this.gameStateManager.harvestCrop(e,t).success?(await this.playHarvestAnimation(e,t,a,i),this.checkAchievements(),this.checkLevelUp(),{success:!0,crop:a,rewards:i,message:this.getHarvestMessage(a,i)}):{success:!1,message:"收获失败，请重试"}}async harvestAllMature(){const e=this.gameStateManager.getGameState(),t=[];let s=0;const a={};for(let i=0;i<e.gridSize.height;i++)for(let n=0;n<e.gridSize.width;n++){const r=e.farmGrid.plots[i][n];if(r&&r.stage===ee.READY_TO_HARVEST){const e=await this.harvestCrop(n,i);t.push(e),e.success&&e.rewards&&(s+=e.rewards.experience,Object.entries(e.rewards.resources).forEach(([e,t])=>{a[e]=(a[e]||0)+t})),await this.delay(300)}}return t.length>0&&this.showBatchHarvestSummary(s,a,t.length),{totalHarvested:t.filter(e=>e.success).length,totalExperience:s,totalResources:a,harvestResults:t}}calculateRewards(e){const t=this.rewardConfig[e.type],s=this.qualityBonus[e.quality];let a=Math.floor(t.baseExperience*s);const i={};let n;if(Object.entries(t.baseResources).forEach(([e,t])=>{i[e]=Math.floor(t*s)}),e.focusTimeContributed>18e5){const e=Math.floor(.5*a);a+=e,n={type:"focus",value:e,message:"专注时间奖励！"}}if(e.quality===se.LEGENDARY){const e=Math.floor(.8*a);a+=e,n={type:"perfect",value:e,message:"完美品质奖励！"}}const r=this.gameStateManager.getGameState();if(r.totalCropsHarvested>0&&r.totalCropsHarvested%10==9){const e=Math.floor(.3*a);a+=e,n={type:"streak",value:e,message:"连续收获奖励！"}}return{experience:a,resources:i,bonus:n}}async playHarvestAnimation(e,t,s,a){const i=200+80*e+40,n=100+80*t+40,r="harvest_".concat(e,"_").concat(t,"_").concat(Date.now()),o={particles:void 0,texts:[],effects:[]},c=this.scene.add.particles(i,n,"sparkle",{speed:{min:60,max:120},scale:{start:.6,end:0},alpha:{start:1,end:0},lifespan:1200,quantity:20,tint:this.getCropColor(s.type)});o.particles=c;const l=this.scene.add.circle(i,n,50,this.getCropColor(s.type),0);l.setStrokeStyle(4,this.getCropColor(s.type),.8),o.effects.push(l),this.scene.tweens.add({targets:l,scaleX:2.5,scaleY:2.5,alpha:0,duration:1e3,ease:"Power2.easeOut"});const d=this.scene.add.text(i,n-40,"+".concat(a.experience," 经验"),{fontSize:"16px",color:"#FFD700",fontFamily:"Arial",fontStyle:"bold"}).setOrigin(.5);o.texts.push(d),this.scene.tweens.add({targets:d,y:d.y-50,alpha:0,duration:2e3,ease:"Power2.easeOut"});let h=0;if(Object.entries(a.resources).forEach(([e,t])=>{const s=this.scene.add.text(i+h,n-20,"+".concat(t," ").concat(this.getResourceName(e)),{fontSize:"12px",color:"#90EE90",fontFamily:"Arial"}).setOrigin(.5);o.texts.push(s),this.scene.tweens.add({targets:s,y:s.y-30,alpha:0,duration:1800,delay:300,ease:"Power2.easeOut"}),h+=40}),a.bonus){const e=this.scene.add.text(i,n+20,a.bonus.message,{fontSize:"14px",color:"#FF69B4",fontFamily:"Arial",fontStyle:"bold"}).setOrigin(.5);o.texts.push(e),this.scene.tweens.add({targets:e,y:e.y-40,alpha:0,duration:2500,delay:500,ease:"Power2.easeOut"})}if(s.quality!==se.COMMON){const e=this.scene.add.text(i,n+40,this.getQualityName(s.quality),{fontSize:"10px",color:this.getQualityColor(s.quality),fontFamily:"Arial",fontStyle:"italic"}).setOrigin(.5);o.texts.push(e),this.scene.tweens.add({targets:e,alpha:0,duration:2e3,delay:800})}return this.activeAnimations.set(r,o),this.scene.time.delayedCall(3e3,()=>{this.cleanupAnimation(r)}),this.delay(800)}showBatchHarvestSummary(e,t,s){const a=this.scene.add.rectangle(400,300,300,200,2905392,.95);a.setStrokeStyle(3,9498256);const i=this.scene.add.text(400,230,"🌾 批量收获完成！",{fontSize:"18px",color:"#FFD700",fontFamily:"Arial",fontStyle:"bold"}).setOrigin(.5);["收获作物: ".concat(s," 个"),"总经验: +".concat(e),...Object.entries(t).map(([e,t])=>"".concat(this.getResourceName(e),": +").concat(t))].forEach((e,t)=>{this.scene.add.text(400,270+25*t,e,{fontSize:"14px",color:"#FFFFFF",fontFamily:"Arial"}).setOrigin(.5)}),a.setScale(0),i.setScale(0),this.scene.tweens.add({targets:[a,i],scaleX:1,scaleY:1,duration:400,ease:"Back.easeOut"}),this.scene.time.delayedCall(4e3,()=>{this.scene.tweens.add({targets:[a,i],alpha:0,duration:500,onComplete:()=>{a.destroy(),i.destroy()}})})}checkAchievements(){const e=this.gameStateManager.getGameState();1===e.totalCropsHarvested&&this.showAchievement("初次收获","收获了第一个作物！"),[10,50,100,500].includes(e.totalCropsHarvested)&&this.showAchievement("收获专家".concat(e.totalCropsHarvested),"累计收获".concat(e.totalCropsHarvested,"个作物！"))}checkLevelUp(){const e=this.gameStateManager.getGameState(),t=this.gameStateManager.getGameStats();e.experience>=t.nextLevelExp&&this.showLevelUp(e.level+1)}showAchievement(e,t){const s=this.scene.add.rectangle(400,100,350,80,4286945,.9);s.setStrokeStyle(2,6591981);const a=this.scene.add.text(400,85,"🏆 ".concat(e),{fontSize:"16px",color:"#FFD700",fontFamily:"Arial",fontStyle:"bold"}).setOrigin(.5),i=this.scene.add.text(400,110,t,{fontSize:"12px",color:"#FFFFFF",fontFamily:"Arial"}).setOrigin(.5);s.y=-50,a.y=-65,i.y=-40,this.scene.tweens.add({targets:[s,a,i],y:"+=150",duration:500,ease:"Back.easeOut"}),this.scene.time.delayedCall(3e3,()=>{this.scene.tweens.add({targets:[s,a,i],y:"-=150",duration:400,onComplete:()=>{s.destroy(),a.destroy(),i.destroy()}})})}showLevelUp(e){}getHarvestMessage(e,t){const s=this.getCropName(e.type),a=this.getQualityName(e.quality);return t.bonus?"收获了".concat(a,"的").concat(s,"！获得额外奖励！"):"成功收获了".concat(a,"的").concat(s,"！")}getCropName(e){return{[te.KNOWLEDGE_FLOWER]:"知识花",[te.STRENGTH_TREE]:"力量树",[te.TIME_VEGGIE]:"时间蔬菜",[te.MEDITATION_LOTUS]:"冥想莲花"}[e]||"未知作物"}getCropColor(e){return{[te.KNOWLEDGE_FLOWER]:16738740,[te.STRENGTH_TREE]:9127187,[te.TIME_VEGGIE]:9498256,[te.MEDITATION_LOTUS]:9662683}[e]||16777215}getResourceName(e){return{knowledge:"知识",strength:"力量",time:"时间",meditation:"冥想"}[e]||e}getQualityName(e){return{[se.COMMON]:"普通",[se.UNCOMMON]:"优秀",[se.RARE]:"稀有",[se.EPIC]:"史诗",[se.LEGENDARY]:"传说"}[e]||"未知"}getQualityColor(e){return{[se.COMMON]:"#FFFFFF",[se.UNCOMMON]:"#32CD32",[se.RARE]:"#4169E1",[se.EPIC]:"#9370DB",[se.LEGENDARY]:"#FFD700"}[e]||"#FFFFFF"}delay(e){return new Promise(t=>{this.scene.time.delayedCall(e,t)})}cleanupAnimation(e){var t,s,a;const i=this.activeAnimations.get(e);i&&(null==(t=i.particles)||t.destroy(),null==(s=i.texts)||s.forEach(e=>e.destroy()),null==(a=i.effects)||a.forEach(e=>e.destroy()),this.activeAnimations.delete(e))}destroy(){this.activeAnimations.forEach((e,t)=>{this.cleanupAnimation(t)}),this.activeAnimations.clear()}}class at extends X.Scene{constructor(){super({key:"HarvestTestScene"}),t(this,"gameStateManager"),t(this,"harvestManager"),t(this,"plantingManager"),t(this,"cropSprites",new Map),t(this,"gridSize",{width:8,height:6}),t(this,"tileSize",80),t(this,"startX",150),t(this,"startY",100),t(this,"controlPanel"),t(this,"infoText"),t(this,"statsText")}create(){this.gameStateManager=new tt,this.createBackground(),this.createFarmGrid(),this.initializeManagers(),this.createControlPanel(),this.createInfoDisplay(),this.seedTestCrops(),this.refreshFarmDisplay()}createBackground(){const e=this.add.graphics();e.fillGradientStyle(8900331,8900331,9498256,9498256,1),e.fillRect(0,0,800,600),this.add.text(400,30,"🧪 收获系统测试",{fontSize:"24px",color:"#2D5530",fontFamily:"Arial",fontStyle:"bold"}).setOrigin(.5)}createFarmGrid(){const e=this.add.group();for(let t=0;t<this.gridSize.height;t++)for(let s=0;s<this.gridSize.width;s++){const a=this.startX+s*this.tileSize,i=this.startY+t*this.tileSize,n=this.add.rectangle(a+this.tileSize/2,i+this.tileSize/2,this.tileSize-2,this.tileSize-2,9127187,.8);n.setStrokeStyle(1,6636321),n.setInteractive({useHandCursor:!0}),n.on("pointerdown",()=>this.onTileClick(t,s)),e.add(n)}}initializeManagers(){this.harvestManager=new st(this,this.gameStateManager),this.plantingManager=new oe(this,this.gameStateManager)}createControlPanel(){this.controlPanel=this.add.container(50,400);const e=this.add.rectangle(0,0,200,160,2905392,.9);e.setStrokeStyle(2,9498256),this.controlPanel.add(e);const t=this.add.text(0,-70,"🎮 控制面板",{fontSize:"14px",color:"#FFD700",fontFamily:"Arial",fontStyle:"bold"}).setOrigin(.5);this.controlPanel.add(t),[{text:"🌱 快速种植",action:()=>this.quickPlant()},{text:"⏰ 快速成熟",action:()=>this.quickMature()},{text:"🌾 批量收获",action:()=>this.batchHarvest()},{text:"🗑️ 清空农场",action:()=>this.clearFarm()}].forEach((e,t)=>{const s=30*t-40,a=this.add.rectangle(0,s,180,25,9498256,.8);a.setStrokeStyle(1,2263842),a.setInteractive({useHandCursor:!0}),this.controlPanel.add(a);const i=this.add.text(0,s,e.text,{fontSize:"12px",color:"#1A4A1A",fontFamily:"Arial"}).setOrigin(.5);this.controlPanel.add(i),a.on("pointerover",()=>{a.setFillStyle(10551200,1)}),a.on("pointerout",()=>{a.setFillStyle(9498256,.8)}),a.on("pointerdown",e.action)})}createInfoDisplay(){this.infoText=this.add.text(400,480,"",{fontSize:"12px",color:"#4A4A4A",fontFamily:"Arial",align:"center"}).setOrigin(.5),this.statsText=this.add.text(600,150,"",{fontSize:"11px",color:"#2D5530",fontFamily:"Arial",backgroundColor:"#FFFFFF",padding:{x:10,y:10}}).setOrigin(0,0),this.updateInfoDisplay()}seedTestCrops(){[{x:0,y:0,type:te.KNOWLEDGE_FLOWER,stage:ee.SEED},{x:1,y:0,type:te.STRENGTH_TREE,stage:ee.SPROUT},{x:2,y:0,type:te.TIME_VEGGIE,stage:ee.GROWING},{x:3,y:0,type:te.MEDITATION_LOTUS,stage:ee.MATURE},{x:0,y:1,type:te.KNOWLEDGE_FLOWER,stage:ee.READY_TO_HARVEST},{x:1,y:1,type:te.STRENGTH_TREE,stage:ee.READY_TO_HARVEST}].forEach(e=>{this.gameStateManager.plantCrop(e.x,e.y,e.type);const t=this.gameStateManager.getGameState().farmGrid.plots[e.y][e.x];t&&(t.stage=e.stage,t.quality=this.getRandomQuality(),e.stage===ee.READY_TO_HARVEST&&(t.focusTimeContributed=3e6*Math.random()))})}getRandomQuality(){const e=[se.COMMON,se.UNCOMMON,se.RARE,se.EPIC,se.LEGENDARY];return X.Utils.Array.GetRandom(e)}quickPlant(){const e=this.gameStateManager.getGameState();let t=0;for(let s=0;s<this.gridSize.height&&t<10;s++)for(let a=0;a<this.gridSize.width&&t<10;a++)if(!e.farmGrid.plots[s][a]){const e=X.Utils.Array.GetRandom([te.KNOWLEDGE_FLOWER,te.STRENGTH_TREE,te.TIME_VEGGIE,te.MEDITATION_LOTUS]);this.gameStateManager.plantCrop(a,s,e),t++}this.refreshFarmDisplay(),this.updateInfoDisplay()}quickMature(){this.gameStateManager.getGameState().crops.forEach(e=>{e.stage!==ee.READY_TO_HARVEST&&(e.stage=ee.READY_TO_HARVEST,e.quality=this.getRandomQuality(),e.focusTimeContributed=3e6*Math.random())}),this.refreshFarmDisplay(),this.updateInfoDisplay()}async batchHarvest(){await this.harvestManager.harvestAllMature(),this.refreshFarmDisplay(),this.updateInfoDisplay()}clearFarm(){const e=this.gameStateManager.getGameState();e.crops.clear();for(let t=0;t<this.gridSize.height;t++)for(let s=0;s<this.gridSize.width;s++)e.farmGrid.plots[t][s]=null;this.refreshFarmDisplay(),this.updateInfoDisplay()}async onTileClick(e,t){var s;const a=null==(s=this.gameStateManager.getGameState().farmGrid.plots[e])?void 0:s[t];if(a)if(a.stage===ee.READY_TO_HARVEST){const s=await this.harvestManager.harvestCrop(t,e);s.success&&s.rewards&&s.rewards.bonus,this.refreshFarmDisplay()}else a.stage=ee.READY_TO_HARVEST,a.quality=this.getRandomQuality(),a.focusTimeContributed=3e6*Math.random(),this.refreshFarmDisplay();else{const s=X.Utils.Array.GetRandom([te.KNOWLEDGE_FLOWER,te.STRENGTH_TREE,te.TIME_VEGGIE,te.MEDITATION_LOTUS]);this.gameStateManager.plantCrop(t,e,s),this.refreshFarmDisplay()}this.updateInfoDisplay()}refreshFarmDisplay(){this.cropSprites.forEach(e=>e.destroy()),this.cropSprites.clear(),this.gameStateManager.getGameState().crops.forEach((e,t)=>{const{gridX:s,gridY:a}=e.position;if(s>=0&&s<this.gridSize.width&&a>=0&&a<this.gridSize.height){const i=this.startX+s*this.tileSize+this.tileSize/2,n=this.startY+a*this.tileSize+this.tileSize/2,r=new ce(this,i,n,e);if(this.cropSprites.set(t,r),e.stage===ee.READY_TO_HARVEST){const t=this.getQualityColor(e.quality);this.add.circle(i+25,n-25,8,t,.8).setStrokeStyle(2,16777215)}}})}updateInfoDisplay(){const e=this.gameStateManager.getGameState(),t=this.gameStateManager.getGameStats(),s={seed:0,sprout:0,growing:0,mature:0,ready:0};e.crops.forEach(e=>{switch(e.stage){case ee.SEED:s.seed++;break;case ee.SPROUT:s.sprout++;break;case ee.GROWING:s.growing++;break;case ee.MATURE:s.mature++;break;case ee.READY_TO_HARVEST:s.ready++}}),this.infoText.setText(["农场状态: 总作物 ".concat(e.crops.size),"种子: ".concat(s.seed," | 幼苗: ").concat(s.sprout," | 生长: ").concat(s.growing," | 成熟: ").concat(s.mature," | 可收获: ").concat(s.ready),"💡 点击空地种植 | 点击生长中作物加速成熟 | 点击成熟作物收获"].join("\n")),this.statsText.setText(["📊 游戏统计","","等级: ".concat(e.level),"经验: ".concat(e.experience," / ").concat(t.nextLevelExp),"总收获: ".concat(e.totalCropsHarvested),"","💎 资源:","知识: ".concat(e.resources.knowledge),"力量: ".concat(e.resources.strength),"时间: ".concat(e.resources.time),"冥想: ".concat(e.resources.meditation)].join("\n"))}getCropName(e){return{[te.KNOWLEDGE_FLOWER]:"知识花",[te.STRENGTH_TREE]:"力量树",[te.TIME_VEGGIE]:"时间蔬菜",[te.MEDITATION_LOTUS]:"冥想莲花"}[e]||"未知作物"}getQualityColor(e){return{[se.COMMON]:8421504,[se.UNCOMMON]:3329330,[se.RARE]:4286945,[se.EPIC]:9662683,[se.LEGENDARY]:16766720}[e]||8421504}update(){this.cropSprites.forEach(e=>{e.update()}),this.time.now%1e3<16&&this.updateInfoDisplay()}shutdown(){this.harvestManager&&this.harvestManager.destroy()}}class it extends X.Scene{constructor(){super({key:"PlantingTestScene"}),t(this,"gameStateManager"),t(this,"farmScene"),t(this,"testButtons",[]),t(this,"testButton"),t(this,"status"),this.gameStateManager=new tt}preload(){this.load.image("background","https://via.placeholder.com/800x600/228B22/FFFFFF?text=Test+Background"),this.load.image("plant_test","https://via.placeholder.com/32x32/90EE90/000000?text=P")}create(){this.add.image(400,300,"background"),this.add.text(400,50,"种植系统测试",{fontSize:"32px",color:"#ffffff"}).setOrigin(.5),this.startFarmScene(),this.createUI()}startFarmScene(){this.scene.launch("EnhancedFarmScene",{gameStateManager:this.gameStateManager}),this.farmScene=this.scene.get("EnhancedFarmScene")}createUI(){this.createTestPanel(),this.showInstructions()}createTestPanel(){this.add.rectangle(50,300,80,500,2905392,.9).setStrokeStyle(2,9498256),this.add.text(50,90,"测试控制",{fontSize:"14px",color:"#FFFFFF",fontFamily:"Arial",fontStyle:"bold"}).setOrigin(.5),[{label:"重置状态",action:()=>this.resetGameState()},{label:"种植花",action:()=>this.quickPlant(te.KNOWLEDGE_FLOWER)},{label:"种植树",action:()=>this.quickPlant(te.STRENGTH_TREE)},{label:"模拟生长",action:()=>this.simulateGrowth()},{label:"全部收获",action:()=>this.harvestAll()},{label:"增加经验",action:()=>this.addExperience()},{label:"升级玩家",action:()=>this.levelUp()},{label:"显示状态",action:()=>this.showGameState()}].forEach((e,t)=>{const s=this.createTestButton(50,110+45*t,e.label,e.action);this.testButtons.push(s)})}createTestButton(e,t,s,a){const i=this.add.container(e,t),n=this.add.rectangle(0,0,70,35,4286945,.8);n.setStrokeStyle(1,6591981),i.add(n);const r=this.add.text(0,0,s,{fontSize:"10px",color:"#FFFFFF",fontFamily:"Arial"}).setOrigin(.5);return i.add(r),n.setInteractive({useHandCursor:!0}),n.on("pointerover",()=>{n.setFillStyle(5930977,1)}),n.on("pointerout",()=>{n.setFillStyle(4286945,.8)}),n.on("pointerdown",()=>{a(),this.showMessage("执行: ".concat(s))}),i}resetGameState(){this.gameStateManager.initialize(),this.gameStateManager.save()}quickPlant(e){const t=this.gameStateManager.getGameState();for(let s=0;s<6;s++)for(let a=0;a<8;a++)if(null===t.farmGrid.plots[s][a]&&this.gameStateManager.plantCrop(a,s,e))return}simulateGrowth(){this.gameStateManager.getGameState().gameTime+=6e4,this.gameStateManager.updateCropGrowth(),this.gameStateManager.save()}harvestAll(){this.gameStateManager.getGameState();for(let e=0;e<6;e++)for(let t=0;t<8;t++)this.gameStateManager.harvestCrop(t,e).success}addExperience(){this.gameStateManager.getGameState().experience+=50,this.gameStateManager.save()}levelUp(){const e=this.gameStateManager.getGameState();e.level+=1,e.experience+=100,this.gameStateManager.save()}showGameState(){this.gameStateManager.getGameState(),this.gameStateManager.getGameStats()}showMessage(e){const t=this.add.container(400,80),s=this.add.rectangle(0,0,8*e.length+20,30,0,.8);t.add(s);const a=this.add.text(0,0,e,{fontSize:"12px",color:"#FFFFFF",fontFamily:"Arial"}).setOrigin(.5);t.add(a),t.setAlpha(0),this.tweens.add({targets:t,alpha:1,duration:200,onComplete:()=>{this.time.delayedCall(2e3,()=>{this.tweens.add({targets:t,alpha:0,duration:300,onComplete:()=>t.destroy()})})}})}showInstructions(){this.add.text(600,550,'🎮 使用说明:\n• 左侧面板：测试控制功能\n• 农场区域：点击"开始种植"体验种植\n• 控制台：查看详细操作日志\n• ESC键：返回主菜单',{fontSize:"12px",color:"#2C5530",fontFamily:"Arial",align:"left"}).setOrigin(.5),this.input.keyboard.on("keydown-ESC",()=>{this.scene.stop("EnhancedFarmScene"),this.scene.start("MainScene")})}}class nt{constructor(){t(this,"listeners",new Map)}on(e,t){return this.listeners.has(e)||this.listeners.set(e,[]),this.listeners.get(e).push(t),this}once(e,t){const s=(...a)=>{this.off(e,s),t(...a)};return this.on(e,s)}off(e,t){const s=this.listeners.get(e);if(s){const e=s.indexOf(t);-1!==e&&s.splice(e,1)}return this}emit(e,...t){const s=this.listeners.get(e);return!!(s&&s.length>0)&&(s.forEach(e=>{try{e(...t)}catch(s){}}),!0)}listenerCount(e){var t;return(null==(t=this.listeners.get(e))?void 0:t.length)||0}removeAllListeners(e){return e?this.listeners.delete(e):this.listeners.clear(),this}}var rt,ot=((rt=ot||{}).CROP_PLANTED="crop_planted",rt.CROP_HARVESTED="crop_harvested",rt.CROP_STAGE_CHANGED="crop_stage_changed",rt.CROP_QUALITY_UPGRADED="crop_quality_upgraded",rt.LEVEL_UP="level_up",rt.EXPERIENCE_GAINED="experience_gained",rt.RESOURCE_CHANGED="resource_changed",rt.ACHIEVEMENT_UNLOCKED="achievement_unlocked",rt.FOCUS_SESSION_STARTED="focus_session_started",rt.FOCUS_SESSION_COMPLETED="focus_session_completed",rt.FOCUS_TIME_ADDED="focus_time_added",rt.GAME_LOADED="game_loaded",rt.GAME_SAVED="game_saved",rt.GAME_RESET="game_reset",rt.SETTINGS_CHANGED="settings_changed",rt.NOTIFICATION_SHOWN="notification_shown",rt.POPUP_OPENED="popup_opened",rt.POPUP_CLOSED="popup_closed",rt.ERROR_OCCURRED="error_occurred",rt.WARNING_ISSUED="warning_issued",rt);class ct extends nt{constructor(e=!1){super(),t(this,"eventHistory",[]),t(this,"maxHistorySize",1e3),t(this,"notificationQueue",[]),t(this,"isProcessingNotifications",!1),t(this,"eventFilters",new Map),t(this,"listenerStats",new Map),t(this,"debugMode",!1),this.debugMode=e,this.setupDefaultListeners()}setupDefaultListeners(){this.on("crop_planted",e=>{this.showNotification({title:"作物种植成功",message:"在 (".concat(e.data.position.gridX,", ").concat(e.data.position.gridY,") 种植了 ").concat(e.data.crop.type),type:"success",duration:3e3})}),this.on("crop_harvested",e=>{const{crop:t,rewards:s}=e.data;this.showNotification({title:"收获成功！",message:"收获了 ".concat(t.type,"，获得 ").concat(s.experience," 经验值"),type:"success",duration:4e3})}),this.on("level_up",e=>{const{newLevel:t}=e.data;this.showNotification({title:"恭喜升级！",message:"你已升级到 ".concat(t," 级！"),type:"success",duration:5e3,persistent:!0})}),this.on("achievement_unlocked",e=>{this.showNotification({title:"成就解锁！",message:"解锁了新成就: ".concat(e.data.achievement),type:"success",duration:6e3,persistent:!0})}),this.on("error_occurred",e=>{this.showNotification({title:"发生错误",message:e.data.message||"发生了未知错误",type:"error",duration:8e3,persistent:!0})}),this.on("warning_issued",e=>{this.showNotification({title:"警告",message:e.data.message||"发生了警告",type:"warning",duration:5e3})})}publishEvent(e,t,s="unknown",a="normal"){const i={type:e,data:t,timestamp:Date.now(),source:s,priority:a,id:this.generateEventId()},n=performance.now();if(this.shouldFilterEvent(i))return this.debugMode,i.id;const r=this.listenerCount(e);let o=!1;try{this.emit(e,i),o=!0,this.debugMode}catch(l){this.publishEvent("error_occurred",{originalEvent:i,error:l instanceof Error?l.message:String(l)},"EventManager","critical")}const c=performance.now()-n;return this.updateListenerStats(e,c),this.addToHistory({event:i,listeners:r,processingTime:c,handled:o}),i.id}subscribeToEvent(e,t,s={}){const a=e=>{if(!s.filter||s.filter(e))try{t(e)}catch(a){}};return s.once?this.once(e,a):this.on(e,a),()=>{this.off(e,a)}}subscribeToEvents(e,t,s={}){const a=e.map(e=>this.subscribeToEvent(e,t,s));return()=>{a.forEach(e=>e())}}addEventFilter(e,t){this.eventFilters.set(e,t)}removeEventFilter(e){this.eventFilters.delete(e)}shouldFilterEvent(e){for(const[s,a]of this.eventFilters)try{if(!a(e))return!0}catch(t){}return!1}showNotification(e){this.notificationQueue.push(e),this.processNotificationQueue(),this.publishEvent("notification_shown",e,"EventManager")}async processNotificationQueue(){if(!this.isProcessingNotifications&&0!==this.notificationQueue.length){for(this.isProcessingNotifications=!0;this.notificationQueue.length>0;){const e=this.notificationQueue.shift();e&&await this.displayNotification(e)}this.isProcessingNotifications=!1}}async displayNotification(e){return new Promise(t=>{const s=this.createNotificationElement(e);document.body.appendChild(s),requestAnimationFrame(()=>{s.classList.add("show")}),!e.persistent&&e.duration?setTimeout(()=>{this.hideNotification(s,t)},e.duration):e.persistent?t():setTimeout(()=>{this.hideNotification(s,t)},4e3)})}createNotificationElement(e){const t=document.createElement("div");t.className="game-notification notification-".concat(e.type);const s=document.createElement("div");s.className="notification-title",s.textContent=e.title;const a=document.createElement("div");if(a.className="notification-message",a.textContent=e.message,t.appendChild(s),t.appendChild(a),e.actions&&e.actions.length>0){const s=document.createElement("div");s.className="notification-actions",e.actions.forEach(e=>{const a=document.createElement("button");a.className="notification-action ".concat(e.primary?"primary":"secondary"),a.textContent=e.label,a.onclick=()=>{e.action(),this.hideNotification(t)},s.appendChild(a)}),t.appendChild(s)}const i=document.createElement("button");return i.className="notification-close",i.innerHTML="✕",i.onclick=()=>this.hideNotification(t),t.appendChild(i),t}hideNotification(e,t){e.classList.add("hide"),setTimeout(()=>{e.parentNode&&e.parentNode.removeChild(e),null==t||t()},300)}generateEventId(){return"event_"+Date.now()+"_"+Math.random().toString(36).substr(2,9)}addToHistory(e){this.eventHistory.push(e),this.eventHistory.length>this.maxHistorySize&&this.eventHistory.shift()}updateListenerStats(e,t){const s=this.listenerStats.get(e)||{count:0,totalTime:0};s.count++,s.totalTime+=t,this.listenerStats.set(e,s)}getEventHistory(e){const t=[...this.eventHistory].reverse();return e?t.slice(0,e):t}getEventStats(){const e={},t={};return this.eventHistory.forEach(t=>{const s=t.event.type;e[s]=(e[s]||0)+1}),this.listenerStats.forEach((e,s)=>{t[s]=e.totalTime/e.count}),{totalEvents:this.eventHistory.length,eventsByType:e,averageProcessingTime:t,recentEvents:this.getEventHistory(10)}}clearEventHistory(){this.eventHistory=[],this.listenerStats.clear()}setDebugMode(e){this.debugMode=e}getActiveListenersCount(){const e={};return Object.values(ot).forEach(t=>{e[t]=this.listenerCount(t)}),e}destroy(){this.removeAllListeners(),this.clearEventHistory(),this.eventFilters.clear(),this.notificationQueue=[],document.querySelectorAll(".game-notification").forEach(e=>{e.parentNode&&e.parentNode.removeChild(e)})}publishCropPlanted(e,t){this.publishEvent("crop_planted",{crop:e,position:t},"CropSystem")}publishCropHarvested(e,t,s){this.publishEvent("crop_harvested",{crop:e,rewards:t,position:s},"CropSystem")}publishLevelUp(e,t,s){this.publishEvent("level_up",{oldLevel:e,newLevel:t,remainingExp:s},"PlayerSystem")}publishResourceChanged(e){this.publishEvent("resource_changed",{resources:e},"ResourceSystem")}publishError(e,t,s="unknown"){this.publishEvent("error_occurred",{message:e,error:null==t?void 0:t.message,stack:null==t?void 0:t.stack},s,"critical")}publishWarning(e,t,s="unknown"){this.publishEvent("warning_issued",{message:e,data:t},s,"normal")}}class lt{constructor(e={}){t(this,"config"),t(this,"keys"),t(this,"backupTimer",null),this.config={storageType:"localStorage",enableCompression:!1,enableEncryption:!1,autoBackup:!0,maxBackups:5,backupInterval:3e5,...e},this.keys={GAME_DATA:"selfgame_data",SETTINGS:"selfgame_settings",PLAYER_PROFILE:"selfgame_player",BACKUP_PREFIX:"selfgame_backup_",METADATA:"selfgame_metadata"},this.initializeStorage(),this.startAutoBackup()}initializeStorage(){try{this.isStorageAvailable()||(this.config.storageType="localStorage"),this.checkDataVersion()}catch(e){}}isStorageAvailable(){try{const e="__storage_test__";return localStorage.setItem(e,"test"),localStorage.removeItem(e),!0}catch(e){return!1}}checkDataVersion(){const e=this.loadMetadata();e&&"1.0.0"!==e.version&&this.migrateData(e.version,"1.0.0")}migrateData(e,t){try{const t=this.loadRawData(this.keys.GAME_DATA);t&&this.createBackup(t,"migration_".concat(e,"_").concat(Date.now())),"0.9.0"===e&&this.migrateFrom090(t)}catch(s){}}migrateFrom090(e){e&&(e.resources&&!e.resources.meditation&&(e.resources.meditation=0),this.saveRawData(this.keys.GAME_DATA,e))}async saveGameState(e){try{let t=this.prepareGameData(e);if(this.config.enableCompression&&(t=await this.compressData(t)),this.config.enableEncryption&&(t=await this.encryptData(t)),this.saveRawData(this.keys.GAME_DATA,t))return this.updateMetadata(e),this.config.autoBackup&&this.createBackup(t),!0;throw new Error("保存数据失败")}catch(t){return!1}}async loadGameState(){try{let e=this.loadRawData(this.keys.GAME_DATA);return e?(this.config.enableEncryption&&(e=await this.decryptData(e)),this.config.enableCompression&&(e=await this.decompressData(e)),this.restoreGameData(e)):null}catch(e){return this.restoreFromBackup()}}prepareGameData(e){const t={};e.farmGrid.forEach((e,s)=>{t[s]=e});const s={};return e.crops.forEach((e,t)=>{s[t]=e}),{...e,farmGrid:t,crops:s,unlockedFeatures:Array.from(e.unlockedFeatures),achievements:Array.from(e.achievements),_saveTime:Date.now(),_version:"1.0.0"}}restoreGameData(e){const t=new Map(Object.entries(e.farmGrid||{})),s=new Map(Object.entries(e.crops||{})),a=new Set(e.unlockedFeatures||[]),i=new Set(e.achievements||[]);return{...e,farmGrid:t,crops:s,unlockedFeatures:a,achievements:i}}saveRawData(e,t){try{const s=JSON.stringify(t);switch(this.config.storageType){case"localStorage":case"indexedDB":localStorage.setItem(e,s);break;default:throw new Error("不支持的存储类型: ".concat(this.config.storageType))}return!0}catch(s){return!1}}loadRawData(e){try{let t=null;switch(this.config.storageType){case"localStorage":case"indexedDB":t=localStorage.getItem(e);break;default:throw new Error("不支持的存储类型: ".concat(this.config.storageType))}return t?JSON.parse(t):null}catch(t){return null}}updateMetadata(e){var t;const s={version:"1.0.0",lastSaved:Date.now(),totalSaves:((null==(t=this.loadMetadata())?void 0:t.totalSaves)||0)+1,gameVersion:e.version,playerData:{playerId:e.playerId,playerName:e.playerName,level:e.level}};this.saveRawData(this.keys.METADATA,s)}loadMetadata(){return this.loadRawData(this.keys.METADATA)}createBackup(e,t){try{const s=t||"auto_".concat(Date.now()),a={id:s,timestamp:Date.now(),gameData:e,metadata:this.loadMetadata()||{},checksum:this.calculateChecksum(e)},i=this.keys.BACKUP_PREFIX+s;this.saveRawData(i,a),this.cleanupOldBackups()}catch(s){}}async restoreFromBackup(){try{const t=this.listBackups();if(0===t.length)return null;t.sort((e,t)=>t.timestamp-e.timestamp);for(const s of t)try{if(this.validateBackup(s))return this.restoreGameData(s.gameData)}catch(e){}return null}catch(e){return null}}listBackups(){const e=[];try{for(let t=0;t<localStorage.length;t++){const s=localStorage.key(t);if(s&&s.startsWith(this.keys.BACKUP_PREFIX)){const t=this.loadRawData(s);t&&this.isValidBackup(t)&&e.push(t)}}}catch(t){}return e}isValidBackup(e){return e&&"string"==typeof e.id&&"number"==typeof e.timestamp&&e.gameData&&e.metadata&&"string"==typeof e.checksum}validateBackup(e){try{return this.calculateChecksum(e.gameData)===e.checksum}catch(t){return!1}}calculateChecksum(e){const t=JSON.stringify(e);let s=0;for(let a=0;a<t.length;a++)s=(s<<5)-s+t.charCodeAt(a),s&=s;return s.toString(16)}cleanupOldBackups(){try{const e=this.listBackups();e.length>this.config.maxBackups&&(e.sort((e,t)=>t.timestamp-e.timestamp),e.slice(this.config.maxBackups).forEach(e=>{const t=this.keys.BACKUP_PREFIX+e.id;localStorage.removeItem(t)}))}catch(e){}}async compressData(e){return{_compressed:!0,data:JSON.stringify(e)}}async decompressData(e){return e._compressed?JSON.parse(e.data):e}async encryptData(e){const t=JSON.stringify(e);return{_encrypted:!0,data:btoa(t)}}async decryptData(e){if(e._encrypted){const t=atob(e.data);return JSON.parse(t)}return e}startAutoBackup(){this.backupTimer&&clearInterval(this.backupTimer),this.config.autoBackup&&(this.backupTimer=setInterval(()=>{const e=this.loadRawData(this.keys.GAME_DATA);e&&this.createBackup(e)},this.config.backupInterval))}exportGameData(){try{const e=this.loadRawData(this.keys.GAME_DATA),t=this.loadMetadata();if(!e)return null;const s={gameData:e,metadata:t,exportTime:Date.now(),version:"1.0.0"};return JSON.stringify(s,null,2)}catch(e){return null}}async importGameData(e){try{const t=JSON.parse(e);if(!t.gameData||!t.version)throw new Error("导入数据格式无效");const s=this.loadRawData(this.keys.GAME_DATA);if(s&&this.createBackup(s,"before_import_".concat(Date.now())),this.saveRawData(this.keys.GAME_DATA,t.gameData))return t.metadata&&this.saveRawData(this.keys.METADATA,t.metadata),!0;throw new Error("保存导入数据失败")}catch(t){return!1}}clearAllData(){try{const e=this.loadRawData(this.keys.GAME_DATA);e&&this.createBackup(e,"final_backup_".concat(Date.now())),localStorage.removeItem(this.keys.GAME_DATA),localStorage.removeItem(this.keys.SETTINGS),localStorage.removeItem(this.keys.PLAYER_PROFILE),localStorage.removeItem(this.keys.METADATA)}catch(e){}}getStorageUsage(){const e={};let t=0;try{Object.values(this.keys).forEach(s=>{const a=localStorage.getItem(s);if(a){const i=new Blob([a]).size;e[s]=i,t+=i}});const s=this.listBackups();let a=0;s.forEach(e=>{const t=this.keys.BACKUP_PREFIX+e.id,s=localStorage.getItem(t);s&&(a+=new Blob([s]).size)}),e.backups=a,t+=a;const i=5242880,n=t/i*100;return{used:t,total:i,percentage:Math.min(n,100),details:e}}catch(s){return{used:0,total:0,percentage:0,details:{}}}}destroy(){this.backupTimer&&(clearInterval(this.backupTimer),this.backupTimer=null)}}class dt extends K.Scene{constructor(){super({key:"StateTestScene"}),t(this,"gameStateManager"),t(this,"eventManager"),t(this,"storageManager"),t(this,"infoText"),t(this,"statusText"),t(this,"controlButtons",[])}create(){this.initializeManagers(),this.createUI(),this.setupEventListeners(),this.setupKeyboardControls()}initializeManagers(){this.gameStateManager=new tt,this.eventManager=new ct(!0),this.storageManager=new lt,this.gameStateManager.on("crop_planted",e=>{this.eventManager.publishCropPlanted(e.crop,e.position)}),this.gameStateManager.on("crop_harvested",e=>{this.eventManager.publishCropHarvested(e.crop,e.rewards,e.position)}),this.gameStateManager.on("level_up",e=>{this.eventManager.publishLevelUp(e.oldLevel,e.newLevel,e.remainingExp)})}createUI(){const{width:e}=this.scale;this.add.text(e/2,30,"状态管理系统测试",{fontSize:"28px",color:"#ffffff",fontStyle:"bold"}).setOrigin(.5),this.infoText=this.add.text(20,80,"",{fontSize:"16px",color:"#ffffff",backgroundColor:"#000000",padding:{x:10,y:10}}),this.statusText=this.add.text(e-20,80,"",{fontSize:"14px",color:"#ffffff",backgroundColor:"#001122",padding:{x:10,y:10}}).setOrigin(1,0),this.createControlButtons(),this.updateInfoDisplay()}createControlButtons(){const{width:e,height:t}=this.scale,s=e/2-420+100,a=t-200;[{text:"种植测试作物",action:()=>this.testPlantCrops()},{text:"收获所有作物",action:()=>this.testHarvestCrops()},{text:"添加经验值",action:()=>this.testAddExperience()},{text:"保存游戏状态",action:()=>this.testSaveGame()},{text:"加载游戏状态",action:()=>this.testLoadGame()},{text:"测试事件系统",action:()=>this.testEventSystem()},{text:"测试通知系统",action:()=>this.testNotifications()},{text:"重置游戏",action:()=>this.testResetGame()},{text:"显示统计信息",action:()=>this.showStats()},{text:"存储使用情况",action:()=>this.showStorageUsage()},{text:"清除所有数据",action:()=>this.clearAllData()},{text:"返回主菜单",action:()=>this.returnToMenu()}].forEach((e,t)=>{const i=t%4,n=Math.floor(t/4),r=s+210*i,o=a+50*n,c=this.createButton(r,o,200,40,e.text,e.action);this.controlButtons.push(c)})}createButton(e,t,s,a,i,n){const r=this.add.container(e,t),o=this.add.rectangle(0,0,s,a,5025616);o.setStrokeStyle(2,4563017),o.setInteractive();const c=this.add.text(0,0,i,{fontSize:"14px",color:"#ffffff",fontStyle:"bold"}).setOrigin(.5);return r.add([o,c]),o.on("pointerover",()=>{o.setFillStyle(6732650),this.input.setDefaultCursor("pointer")}),o.on("pointerout",()=>{o.setFillStyle(5025616),this.input.setDefaultCursor("default")}),o.on("pointerup",()=>{o.setFillStyle(6732650),n()}),r}setupEventListeners(){this.eventManager.subscribeToEvent(ot.CROP_PLANTED,e=>{this.updateInfoDisplay()}),this.eventManager.subscribeToEvent(ot.CROP_HARVESTED,e=>{this.updateInfoDisplay()}),this.eventManager.subscribeToEvent(ot.LEVEL_UP,e=>{this.updateInfoDisplay()})}setupKeyboardControls(){var e,t,s,a,i;null==(e=this.input.keyboard)||e.on("keydown-ONE",()=>this.testPlantCrops()),null==(t=this.input.keyboard)||t.on("keydown-TWO",()=>this.testHarvestCrops()),null==(s=this.input.keyboard)||s.on("keydown-THREE",()=>this.testSaveGame()),null==(a=this.input.keyboard)||a.on("keydown-FOUR",()=>this.testLoadGame()),null==(i=this.input.keyboard)||i.on("keydown-ESC",()=>this.returnToMenu())}testPlantCrops(){const e=[{x:0,y:0,type:te.KNOWLEDGE_FLOWER},{x:1,y:0,type:te.STRENGTH_TREE},{x:2,y:0,type:te.TIME_VEGGIE},{x:3,y:0,type:te.MEDITATION_LOTUS}];let t=0;e.forEach(e=>{this.gameStateManager.plantCrop(e.x,e.y,e.type)&&t++}),this.eventManager.showNotification({title:"种植测试完成",message:"成功种植 ".concat(t," 株作物"),type:"success"}),this.updateInfoDisplay()}testHarvestCrops(){const e=this.gameStateManager.getGameState();let t=0;for(let s=0;s<e.farmGrid.height;s++)for(let a=0;a<e.farmGrid.width;a++){const i=e.farmGrid.plots[s][a];i&&i.stage===ee.READY_TO_HARVEST&&this.gameStateManager.harvestCrop(a,s).success&&t++}this.eventManager.showNotification({title:"收获测试完成",message:"成功收获 ".concat(t," 株作物"),type:"success"}),this.updateInfoDisplay()}testAddExperience(){const e=this.gameStateManager.getGameState();this.eventManager.showNotification({title:"经验值测试",message:"当前等级: ".concat(e.level,", 经验: ").concat(e.experience),type:"info"}),this.updateInfoDisplay()}async testSaveGame(){const e=await this.gameStateManager.saveGameState();this.eventManager.showNotification({title:"保存测试",message:e?"游戏保存成功":"游戏保存失败",type:e?"success":"error"})}async testLoadGame(){const e=await this.gameStateManager.loadGameState();this.eventManager.showNotification({title:"加载测试",message:e?"游戏加载成功":"游戏加载失败",type:e?"success":"error"}),e&&this.updateInfoDisplay()}testEventSystem(){this.eventManager.publishEvent(ot.GAME_SAVED,{message:"这是一个测试事件",timestamp:Date.now()}),this.eventManager.showNotification({title:"事件系统测试",message:"已发布测试事件，请查看控制台",type:"info"})}testNotifications(){[{title:"信息通知",message:"这是一个信息通知",type:"info"},{title:"成功通知",message:"操作成功完成",type:"success"},{title:"警告通知",message:"这是一个警告",type:"warning"},{title:"错误通知",message:"发生了错误",type:"error"}].forEach((e,t)=>{setTimeout(()=>{this.eventManager.showNotification(e)},1e3*t)})}testResetGame(){this.gameStateManager.resetGameState(),this.eventManager.showNotification({title:"重置完成",message:"游戏状态已重置",type:"success"}),this.updateInfoDisplay()}showStats(){const e=this.gameStateManager.getGameStats();this.eventManager.showNotification({title:"统计信息",message:"等级: ".concat(e.level,", 作物: ").concat(e.cropsCount,", 总资源: ").concat(e.resourcesTotal),type:"info"})}showStorageUsage(){try{const e=this.storageManager.getStorageUsage();this.eventManager.showNotification({title:"存储使用情况",message:"已使用 ".concat(Math.round(e.percentage),"% 的本地存储"),type:"info"})}catch(e){}}clearAllData(){try{this.storageManager.clearAllData(),this.gameStateManager.resetGameState(),this.eventManager.showNotification({title:"数据清除完成",message:"所有数据已清除",type:"success"}),this.updateInfoDisplay()}catch(e){this.eventManager.showNotification({title:"清除失败",message:"数据清除过程中发生错误",type:"error"})}}returnToMenu(){this.gameStateManager.destroy(),this.eventManager.destroy(),this.storageManager.destroy(),this.scene.start("MainMenu")}updateInfoDisplay(){const e=this.gameStateManager.getGameState(),t=this.gameStateManager.getGameStats(),s=["玩家: ".concat(e.playerName||"未设置"),"等级: ".concat(e.level," (经验: ").concat(e.experience,"/").concat(t.nextLevelExp,")"),"总专注时间: ".concat(Math.round(e.totalFocusTime)," 分钟"),"收获总数: ".concat(e.totalCropsHarvested),"","资源:","  知识: ".concat(e.resources.knowledge),"  力量: ".concat(e.resources.strength),"  时间: ".concat(e.resources.time),"  冥想: ".concat(e.resources.meditation),"","作物数量: ".concat(t.cropsCount),"农场规格: ".concat(e.gridSize.width,"x").concat(e.gridSize.height)].join("\n");this.infoText.setText(s)}update(){const e=Date.now(),t=Math.round(this.game.loop.actualFps),s=this.children.list.length;this.statusText.setText(["FPS: ".concat(t),"对象数: ".concat(s),"时间: ".concat(new Date(e).toLocaleTimeString())].join("\n"))}destroy(){var e,t,s;null==(e=this.gameStateManager)||e.destroy(),null==(t=this.eventManager)||t.destroy(),null==(s=this.storageManager)||s.destroy()}}var ht,ut=((ht=ut||{}).STAGE_TRANSITION="stage_transition",ht.QUALITY_UPGRADE="quality_upgrade",ht.HARVEST_READY="harvest_ready",ht.PLANTING="planting",ht.GROWTH_BOOST="growth_boost",ht.IDLE_SWAY="idle_sway",ht);const mt={stage_transition:{duration:800,ease:"Back.easeOut",scale:{start:1,end:1.15},particles:{count:8,color:9498256,spread:25}},quality_upgrade:{duration:600,ease:"Bounce.easeOut",scale:{start:1,end:1.2},alpha:{start:1,end:.3},particles:{count:12,color:9662683,spread:30}},harvest_ready:{duration:1500,ease:"Sine.easeInOut",alpha:{start:1,end:.7},particles:{count:6,color:16766720,spread:20}},planting:{duration:1e3,ease:"Elastic.easeOut",scale:{start:0,end:1},rotation:{start:-.2,end:0}},growth_boost:{duration:400,ease:"Quad.easeOut",scale:{start:1,end:1.1},particles:{count:15,color:65280,spread:35}},idle_sway:{duration:3e3,ease:"Sine.easeInOut",rotation:{start:-.03,end:.03}}};class pt{constructor(e){t(this,"scene"),t(this,"activeTweens",new Map),t(this,"particlePool",[]),this.scene=e}playAnimation(e,t,s){return new Promise(a=>{const i={...mt[t],...s},n=this.getSpriteId(e);this.stopAnimation(n);const r=[],o=this.createMainTween(e,i,()=>{this.cleanupTweens(n),a()});o&&r.push(o),i.particles&&this.createParticleEffect(e,i.particles),"idle_sway"!==t&&"harvest_ready"!==t||this.setupLoopingAnimation(e,t,i,r),this.activeTweens.set(n,r)})}createMainTween(e,t,s){const a={};return t.scale&&(a.scaleX=t.scale.end,a.scaleY=t.scale.end),t.rotation&&(a.rotation=t.rotation.end),t.alpha&&(a.alpha=t.alpha.end),0===Object.keys(a).length?(s(),null):this.scene.tweens.add({targets:e,...a,duration:t.duration,ease:t.ease||"Linear",yoyo:!!t.alpha,onComplete:s})}setupLoopingAnimation(e,t,s,a){if("idle_sway"===t&&s.rotation){const t=this.scene.tweens.add({targets:e,rotation:s.rotation.end,duration:s.duration,ease:s.ease||"Sine.easeInOut",yoyo:!0,repeat:-1});a.push(t)}if("harvest_ready"===t&&s.alpha){const t=this.scene.tweens.add({targets:e,alpha:s.alpha.end,duration:s.duration,ease:s.ease||"Sine.easeInOut",yoyo:!0,repeat:-1});a.push(t)}}createParticleEffect(e,t){const{count:s,color:a,spread:i}=t;for(let n=0;n<s;n++)setTimeout(()=>{this.createSingleParticle(e,a,i)},50*n)}createSingleParticle(e,t,s){let a=this.particlePool.pop();a||(a=this.scene.add.graphics()),a.clear(),a.fillStyle(t,.8),a.fillCircle(0,0,2+2*Math.random()),a.setPosition(e.x,e.y),a.setAlpha(1),a.setScale(1);const i=Math.random()*Math.PI*2,n=s+Math.random()*s,r=e.x+Math.cos(i)*n,o=e.y+Math.sin(i)*n;this.scene.tweens.add({targets:a,x:r,y:o,alpha:0,scale:0,duration:1e3+500*Math.random(),ease:"Quad.easeOut",onComplete:()=>{a.setVisible(!1),this.particlePool.push(a)}})}stopAnimation(e){const t=this.activeTweens.get(e);t&&(t.forEach(e=>{e&&!e.isDestroyed()&&e.destroy()}),this.activeTweens.delete(e))}cleanupTweens(e){const t=this.activeTweens.get(e);if(t){const s=t.filter(e=>!e.isDestroyed());s.length>0?this.activeTweens.set(e,s):this.activeTweens.delete(e)}}getSpriteId(e){return e.cropData.id}async playPlantingSequence(e){await this.playAnimation(e,"planting"),await this.delay(300),this.playAnimation(e,"idle_sway")}async playStageTransitionSequence(e,t,s){this.stopAnimation(this.getSpriteId(e)),await this.playAnimation(e,"stage_transition"),s===ee.READY_TO_HARVEST?this.playAnimation(e,"harvest_ready"):this.playAnimation(e,"idle_sway")}async playQualityUpgradeSequence(e){await this.playAnimation(e,"quality_upgrade")}playGrowthBoost(e){this.playAnimation(e,"growth_boost")}pauseAllAnimations(){this.activeTweens.forEach(e=>{e.forEach(e=>{e&&!e.isDestroyed()&&e.pause()})})}resumeAllAnimations(){this.activeTweens.forEach(e=>{e.forEach(e=>{e&&!e.isDestroyed()&&e.resume()})})}destroy(){this.activeTweens.forEach((e,t)=>{this.stopAnimation(t)}),this.particlePool.forEach(e=>{e&&e.active&&e.destroy()}),this.activeTweens.clear(),this.particlePool.length=0}getDebugInfo(){let e=0;return this.activeTweens.forEach(t=>{e+=t.length}),{activeTweensCount:e,particlePoolSize:this.particlePool.length,memoryUsage:{tweenMemory:100*e,particleMemory:50*this.particlePool.length}}}delay(e){return new Promise(t=>{this.scene.time.delayedCall(e,t)})}}class gt extends X.Scene{constructor(){super({key:"AnimationTestScene"}),t(this,"animationManager"),t(this,"testCrops",[]),t(this,"currentTestIndex",0),t(this,"instructionText"),t(this,"debugInfo")}create(){this.animationManager=new pt(this),this.createBackground(),this.createTestCrops(),this.createUI(),this.setupInput(),this.startAutoDemo()}createBackground(){const e=this.add.graphics();e.fillGradientStyle(8900331,8900331,9498256,9498256,1),e.fillRect(0,0,this.scale.width,this.scale.height),e.lineStyle(1,16777215,.1);for(let t=0;t<this.scale.width;t+=50)e.moveTo(t,0),e.lineTo(t,this.scale.height);for(let t=0;t<this.scale.height;t+=50)e.moveTo(0,t),e.lineTo(this.scale.width,t);e.strokePath()}createTestCrops(){const e=[te.KNOWLEDGE_FLOWER,te.STRENGTH_TREE,te.TIME_VEGGIE,te.MEDITATION_LOTUS],t=[ee.SEED,ee.SPROUT,ee.GROWING,ee.MATURE,ee.READY_TO_HARVEST],s=[se.COMMON,se.UNCOMMON,se.RARE,se.EPIC,se.LEGENDARY];e.forEach((e,a)=>{t.forEach((t,i)=>{const n=150+120*i,r=150+120*a,o=Date.now(),c={id:"test-".concat(a,"-").concat(i),type:e,stage:t,quality:s[i]||se.COMMON,plantedAt:o,harvestable:t===ee.READY_TO_HARVEST,position:{x:i,y:a,gridX:i,gridY:a},stageStartTime:o,totalGrowthTime:0,focusTimeContributed:0,averageFocusScore:1,qualityBonusesApplied:[],lastUpdateTime:o,isPaused:!1},l=new ce(this,n,r,c);this.testCrops.push(l),l.on("harvest",this.onCropHarvest,this),l.on("inspect",this.onCropInspect,this)})}),this.addLabels(150,150,120)}addLabels(e,t,s){["种子","发芽","生长","成熟","收获"].forEach((a,i)=>{this.add.text(e+i*s,t-50,a,{fontSize:"14px",color:"#333333",fontStyle:"bold"}).setOrigin(.5)}),["知识花","力量树","时间菜","冥想莲"].forEach((a,i)=>{this.add.text(e-80,t+i*s,a,{fontSize:"14px",color:"#333333",fontStyle:"bold"}).setOrigin(.5,.5)})}createUI(){this.add.text(this.scale.width/2,30,"🎬 作物生长动画测试场景",{fontSize:"24px",color:"#333333",fontStyle:"bold"}).setOrigin(.5),this.instructionText=this.add.text(this.scale.width/2,70,"",{fontSize:"16px",color:"#666666",align:"center"}).setOrigin(.5),this.debugInfo=this.add.text(20,this.scale.height-150,"",{fontSize:"12px",color:"#333333",backgroundColor:"rgba(255, 255, 255, 0.8)",padding:{x:10,y:5}}),this.createControlButtons()}createControlButtons(){const e=this.scale.height-60,t=140;let s=50;this.createButton(s,e,"种植动画",()=>{this.playPlantingDemo()}),s+=t,this.createButton(s,e,"生长动画",()=>{this.playGrowthDemo()}),s+=t,this.createButton(s,e,"品质升级",()=>{this.playQualityDemo()}),s+=t,this.createButton(s,e,"收获动画",()=>{this.playHarvestDemo()}),s+=t,this.createButton(s,e,"生长加速",()=>{this.playBoostDemo()}),s+=t,this.createButton(s,e,"重置",()=>{this.resetScene()})}createButton(e,t,s,a){const i=this.add.graphics();i.fillStyle(5025616),i.fillRoundedRect(0,0,120,35,8),i.setPosition(e,t),i.setInteractive(new X.Geom.Rectangle(0,0,120,35),X.Geom.Rectangle.Contains),this.add.text(e+60,t+17,s,{fontSize:"14px",color:"#FFFFFF",fontStyle:"bold"}).setOrigin(.5),i.on("pointerover",()=>{i.clear(),i.fillStyle(4563017),i.fillRoundedRect(0,0,120,35,8)}),i.on("pointerout",()=>{i.clear(),i.fillStyle(5025616),i.fillRoundedRect(0,0,120,35,8)}),i.on("pointerdown",a)}setupInput(){var e,t,s;null==(e=this.input.keyboard)||e.on("keydown-SPACE",()=>{this.playRandomAnimation()}),null==(t=this.input.keyboard)||t.on("keydown-R",()=>{this.resetScene()}),null==(s=this.input.keyboard)||s.on("keydown-ESC",()=>{this.scene.start("MainGameScene")})}startAutoDemo(){this.instructionText.setText("🎮 操作说明：\n• 点击作物查看详情或收获\n• 空格键：随机播放动画\n• R键：重置场景\n• ESC键：返回主游戏\n• 点击下方按钮测试特定动画"),this.time.delayedCall(2e3,()=>{this.playSequentialDemo()})}async playSequentialDemo(){await this.playPlantingDemo(),await this.wait(1e3),await this.playGrowthDemo(),await this.wait(1e3),await this.playQualityDemo(),await this.wait(1e3),await this.playHarvestDemo(),await this.wait(1e3),await this.playBoostDemo()}async playPlantingDemo(){this.instructionText.setText("🌱 正在演示种植动画...");const e=this.testCrops.filter(e=>e.cropData.stage===ee.SEED);for(const t of e)await this.animationManager.playPlantingSequence(t),await this.wait(200)}async playGrowthDemo(){this.instructionText.setText("🌿 正在演示生长阶段转换动画...");const e=this.getRandomCrops(6);for(const t of e){const e=t.cropData.stage,s=this.getNextStage(e);s&&(await this.animationManager.playStageTransitionSequence(t,e,s),t.cropData.stage=s,t.updateCropData(t.cropData),await this.wait(300))}}async playQualityDemo(){this.instructionText.setText("⭐ 正在演示品质升级动画...");const e=this.getRandomCrops(4);for(const t of e){const e=t.cropData.quality,s=this.getNextQuality(e);s&&(t.cropData.quality=s,t.updateCropData(t.cropData),await this.animationManager.playQualityUpgradeSequence(t),await this.wait(400))}}async playHarvestDemo(){this.instructionText.setText("🎯 正在演示收获就绪动画...");const e=this.testCrops.filter(e=>e.cropData.stage===ee.MATURE||e.cropData.stage===ee.READY_TO_HARVEST);for(const t of e.slice(0,3))t.cropData.stage=ee.READY_TO_HARVEST,t.cropData.harvestable=!0,t.updateCropData(t.cropData),await this.animationManager.playAnimation(t,ut.HARVEST_READY),await this.wait(300)}async playBoostDemo(){this.instructionText.setText("⚡ 正在演示生长加速效果...");const e=this.getRandomCrops(8);for(const t of e)this.animationManager.playGrowthBoost(t),await this.wait(100);await this.wait(1e3)}playRandomAnimation(){const e=this.getRandomCrops(1)[0];if(!e)return;const t=[ut.STAGE_TRANSITION,ut.QUALITY_UPGRADE,ut.GROWTH_BOOST,ut.HARVEST_READY],s=t[Math.floor(Math.random()*t.length)];this.animationManager.playAnimation(e,s)}resetScene(){this.animationManager.destroy(),this.scene.restart()}onCropHarvest(e){this.instructionText.setText("🎯 收获了 ".concat(this.getCropTypeName(e.type),"！"))}onCropInspect(e){const t="🔍 ".concat(this.getCropTypeName(e.type)," - ").concat(this.getStageName(e.stage)," - ").concat(this.getQualityName(e.quality));this.instructionText.setText(t)}getRandomCrops(e){return[...this.testCrops].sort(()=>.5-Math.random()).slice(0,e)}getNextStage(e){const t=[ee.SEED,ee.SPROUT,ee.GROWING,ee.MATURE,ee.READY_TO_HARVEST],s=t.indexOf(e);return s<t.length-1?t[s+1]:null}getNextQuality(e){const t=[se.COMMON,se.UNCOMMON,se.RARE,se.EPIC,se.LEGENDARY],s=t.indexOf(e);return s<t.length-1?t[s+1]:null}getCropTypeName(e){return{[te.KNOWLEDGE_FLOWER]:"知识花",[te.STRENGTH_TREE]:"力量树",[te.TIME_VEGGIE]:"时间菜",[te.MEDITATION_LOTUS]:"冥想莲"}[e]||"未知作物"}getStageName(e){return{[ee.SEED]:"种子",[ee.SPROUT]:"发芽",[ee.GROWING]:"生长",[ee.MATURE]:"成熟",[ee.READY_TO_HARVEST]:"收获",[ee.HARVESTED]:"已收获"}[e]||"未知阶段"}getQualityName(e){return{[se.COMMON]:"普通",[se.UNCOMMON]:"优良",[se.RARE]:"稀有",[se.EPIC]:"史诗",[se.LEGENDARY]:"传说"}[e]||"未知品质"}wait(e){return new Promise(t=>{this.time.delayedCall(e,t)})}update(){if(this.debugInfo){const e=this.animationManager.getDebugInfo(),t=this.game.loop.actualFps.toFixed(1);this.debugInfo.setText("🔧 调试信息:\nFPS: ".concat(t,"\n活跃动画: ").concat(e.activeTweensCount,"\n粒子池: ").concat(e.particlePoolSize,"\n内存使用:\n  动画: ~").concat(e.memoryUsage.tweenMemory,"KB\n  粒子: ~").concat(e.memoryUsage.particleMemory,"KB"))}}destroy(){this.animationManager&&this.animationManager.destroy(),this.testCrops.forEach(e=>e.destroy()),this.testCrops.length=0,super.destroy()}}class yt extends K.Scene{constructor(){super({key:"AgriculturalFarmScene"}),t(this,"farmManager"),t(this,"focusTokenManager"),t(this,"lootBoxManager"),t(this,"synthesisManager"),t(this,"farmSlots",new Map),t(this,"selectedSlot",null),t(this,"uiElements",{})}preload(){this.loadGameAssets()}create(){this.initializeManagers(),this.createGameWorld(),this.createUI(),this.setupInputHandlers(),this.startGameLoop()}update(e){this.farmManager.updateGameTime(e),this.updateUI(),this.updateFarmDisplay()}loadGameAssets(){this.add.graphics().fillStyle(9127187).fillRect(0,0,80,80).generateTexture("soil_empty",80,80),[{name:"seed",color:9127187},{name:"sprout",color:9498256},{name:"growing",color:3329330},{name:"flowering",color:16738740},{name:"mature",color:16766720},{name:"ready",color:16729344}].forEach(e=>{this.add.graphics().fillStyle(e.color).fillCircle(20,20,15).generateTexture("crop_".concat(e.name),40,40)})}initializeManagers(){const e=Qe.loadFromStorage();this.focusTokenManager=new Qe(e||void 0),this.farmManager=new He,this.lootBoxManager=new ze(this.focusTokenManager),this.synthesisManager=new Ve,this.setupManagerEvents()}setupManagerEvents(){this.focusTokenManager.on("tokensEarned",e=>{this.showFloatingText("+".concat(e.amount," 专注代币"),5025616)}),this.farmManager.on("cropPlanted",e=>{this.updateSlotDisplay(e.slotId),this.showFloatingText("作物已种植",5025616)}),this.farmManager.on("cropHarvested",e=>{this.updateSlotDisplay(e.slotId),this.showFloatingText("收获 ".concat(e.harvestItems.length," 个物品"),16766720)})}createGameWorld(){this.cameras.main.setBounds(0,0,1200,800),this.add.rectangle(600,400,1200,800,8900331),this.add.rectangle(600,600,1200,400,2263842),this.add.text(600,50,"🌾 农产品道具系统 🌾",{fontSize:"32px",fontFamily:"Arial",color:"#2E7D32",stroke:"#FFFFFF",strokeThickness:2}).setOrigin(.5),this.createFarmGrid()}createFarmGrid(){const e=this.farmManager.getFarmState(),t=Math.ceil(Math.sqrt(e.maxSlots));e.slots.forEach((e,s)=>{const a=Math.floor(s/t),i=300+s%t*100,n=150+100*a,r=this.createSlotContainer(e,i,n);this.farmSlots.set(e.id,r)})}createSlotContainer(e,t,s){const a=this.add.container(t,s),i=this.add.rectangle(0,0,80,80,e.isUnlocked?9127187:6710886);i.setStrokeStyle(2,e.isUnlocked?6636321:4473924),a.add(i);const n=this.add.text(-35,-35,e.id,{fontSize:"12px",color:"#FFFFFF"});if(a.add(n),e.currentItem){const t=this.add.circle(0,0,15,this.getCropColor(e.currentItem));a.add(t)}return i.setInteractive(),i.on("pointerdown",()=>this.onSlotClicked(e.id)),a}getCropColor(e){return{gray:10395294,green:5025616,blue:2201331,orange:16750592,gold:16766720,"gold-red":16739125}[e.quality.toString()]||10395294}createUI(){this.createTopPanel(),this.createSidePanel()}createTopPanel(){this.add.rectangle(600,30,1200,60,3026478,.8),this.uiElements.focusTokenDisplay=this.add.text(50,30,"专注代币: 0",{fontSize:"18px",color:"#4CAF50"}).setOrigin(0,.5),this.uiElements.farmLevelDisplay=this.add.text(250,30,"农场等级: 1",{fontSize:"18px",color:"#FFD700"}).setOrigin(0,.5)}createSidePanel(){this.add.rectangle(950,400,300,600,3026478,.8),this.add.text(950,120,"功能面板",{fontSize:"24px",color:"#FFFFFF",fontStyle:"bold"}).setOrigin(.5);const e=this.add.container(950,180),t=this.add.rectangle(0,0,200,40,5025616),s=this.add.text(0,0,"开始专注",{fontSize:"16px",color:"#FFFFFF"}).setOrigin(.5);e.add([t,s]),t.setInteractive(),t.on("pointerdown",()=>this.startFocusSession())}setupInputHandlers(){var e;null==(e=this.input.keyboard)||e.on("keydown-F",()=>{this.startFocusSession()})}onSlotClicked(e){this.selectedSlot=e;const t=this.farmManager.getSlotState(e);t&&(t.isUnlocked?t.currentItem?t.currentItem.growth.isReady&&this.harvestCrop(e):this.plantCrop(e):this.unlockSlot(e))}async plantCrop(e){try{const t=this.createMockSeed(),s=await this.farmManager.plantCrop(e,t);s.success||this.showNotification("种植失败: "+s.error,16007990)}catch(t){this.showNotification("种植失败: "+t.message,16007990)}}async harvestCrop(e){try{const t=await this.farmManager.harvestCrop(e);t.success||this.showNotification("收获失败: "+t.error,16007990)}catch(t){this.showNotification("收获失败: "+t.message,16007990)}}async unlockSlot(e){try{const t=await this.farmManager.unlockSlot(e);t.success||this.showNotification("解锁失败: "+t.error,16007990)}catch(t){this.showNotification("解锁失败: "+t.message,16007990)}}startFocusSession(){this.focusTokenManager.startFocusSession(),this.showNotification("专注模式已开启！",5025616)}updateUI(){const e=this.focusTokenManager.getTokenAmount(),t=this.farmManager.getFarmState();this.uiElements.focusTokenDisplay&&this.uiElements.focusTokenDisplay.setText("专注代币: ".concat(e)),this.uiElements.farmLevelDisplay&&this.uiElements.farmLevelDisplay.setText("农场等级: ".concat(t.level))}updateFarmDisplay(){this.farmManager.getFarmState().slots.forEach(e=>{this.updateSlotDisplay(e.id)})}updateSlotDisplay(e){const t=this.farmSlots.get(e),s=this.farmManager.getSlotState(e);if(!t||!s)return;t.removeAll(!0);const a=this.add.rectangle(0,0,80,80,s.isUnlocked?9127187:6710886);a.setStrokeStyle(2,s.isUnlocked?6636321:4473924),t.add(a);const i=this.add.text(-35,-35,s.id,{fontSize:"12px",color:"#FFFFFF"});if(t.add(i),s.currentItem){const e=this.add.circle(0,0,15,this.getCropColor(s.currentItem));t.add(e)}a.setInteractive(),a.on("pointerdown",()=>this.onSlotClicked(s.id))}showNotification(e,t=16777215){const s=this.add.container(600,100),a=this.add.rectangle(0,0,400,60,0,.8),i=this.add.text(0,0,e,{fontSize:"16px",color:"#"+t.toString(16).padStart(6,"0"),wordWrap:{width:380}}).setOrigin(.5);s.add([a,i]),s.setAlpha(0),this.tweens.add({targets:s,alpha:1,duration:300,ease:"Power2"}),this.time.delayedCall(3e3,()=>{this.tweens.add({targets:s,alpha:0,duration:300,ease:"Power2",onComplete:()=>s.destroy()})})}showFloatingText(e,t,s=600,a=300){const i=this.add.text(s,a,e,{fontSize:"20px",color:"#"+t.toString(16).padStart(6,"0"),fontStyle:"bold"}).setOrigin(.5);this.tweens.add({targets:i,y:a-50,alpha:0,duration:1500,ease:"Power2",onComplete:()=>i.destroy()})}createMockSeed(){return{id:"seed_".concat(Date.now()),name:"玉米种子",nameEn:"Corn Seed",description:"优质玉米种子，适合春季种植",rarity:"gray",category:Se.SEED,variety:"corn",level:1,quality:1,production:{minDaily:1,maxDaily:3},value:{basePrice:10,currentPrice:10,marketDemand:1,priceHistory:[]},growth:{growthTime:24,currentStage:Ee.SEED,isReady:!1,needsWater:!0,needsFertilizer:!1},sprite:"seed",location:{}}}startGameLoop(){this.time.addEvent({delay:1e3,callback:()=>{},loop:!0}),this.time.addEvent({delay:5e3,callback:()=>{},loop:!0})}}const ft={type:K.AUTO,width:1200,height:800,backgroundColor:8900331,parent:"game-container",scale:{mode:K.Scale.FIT,autoCenter:K.Scale.CENTER_BOTH,width:1200,height:800},physics:{default:"arcade",arcade:{debug:!1,gravity:{x:0,y:0}}},scene:[$e,le,at,it,dt,gt,yt],input:{mouse:!0,touch:!0,keyboard:!0},render:{antialias:!0,pixelArt:!1}};var xt,vt=P(),bt=G(),St=((xt=St||{})[xt.NOSE=0]="NOSE",xt[xt.LEFT_EYE_INNER=1]="LEFT_EYE_INNER",xt[xt.LEFT_EYE=2]="LEFT_EYE",xt[xt.LEFT_EYE_OUTER=3]="LEFT_EYE_OUTER",xt[xt.RIGHT_EYE_INNER=4]="RIGHT_EYE_INNER",xt[xt.RIGHT_EYE=5]="RIGHT_EYE",xt[xt.RIGHT_EYE_OUTER=6]="RIGHT_EYE_OUTER",xt[xt.LEFT_EAR=7]="LEFT_EAR",xt[xt.RIGHT_EAR=8]="RIGHT_EAR",xt[xt.MOUTH_LEFT=9]="MOUTH_LEFT",xt[xt.MOUTH_RIGHT=10]="MOUTH_RIGHT",xt[xt.LEFT_SHOULDER=11]="LEFT_SHOULDER",xt[xt.RIGHT_SHOULDER=12]="RIGHT_SHOULDER",xt[xt.LEFT_ELBOW=13]="LEFT_ELBOW",xt[xt.RIGHT_ELBOW=14]="RIGHT_ELBOW",xt[xt.LEFT_WRIST=15]="LEFT_WRIST",xt[xt.RIGHT_WRIST=16]="RIGHT_WRIST",xt[xt.LEFT_PINKY=17]="LEFT_PINKY",xt[xt.RIGHT_PINKY=18]="RIGHT_PINKY",xt[xt.LEFT_INDEX=19]="LEFT_INDEX",xt[xt.RIGHT_INDEX=20]="RIGHT_INDEX",xt[xt.LEFT_THUMB=21]="LEFT_THUMB",xt[xt.RIGHT_THUMB=22]="RIGHT_THUMB",xt[xt.LEFT_HIP=23]="LEFT_HIP",xt[xt.RIGHT_HIP=24]="RIGHT_HIP",xt[xt.LEFT_KNEE=25]="LEFT_KNEE",xt[xt.RIGHT_KNEE=26]="RIGHT_KNEE",xt[xt.LEFT_ANKLE=27]="LEFT_ANKLE",xt[xt.RIGHT_ANKLE=28]="RIGHT_ANKLE",xt[xt.LEFT_HEEL=29]="LEFT_HEEL",xt[xt.RIGHT_HEEL=30]="RIGHT_HEEL",xt[xt.LEFT_FOOT_INDEX=31]="LEFT_FOOT_INDEX",xt[xt.RIGHT_FOOT_INDEX=32]="RIGHT_FOOT_INDEX",xt);class Et{constructor(e={}){t(this,"config"),t(this,"kalmanState"),t(this,"rawDataHistory",[]),t(this,"filteredDataHistory",[]),t(this,"timestamps",[]),this.config={kalmanProcessNoise:.1,kalmanMeasurementNoise:1,smoothingWindow:10,adaptiveSmoothingEnabled:!0,outlierDetectionEnabled:!0,outlierThreshold:2.5,trendAnalysisWindow:20,seasonalityPeriod:60,...e},this.kalmanState={x:0,P:1,Q:this.config.kalmanProcessNoise,R:this.config.kalmanMeasurementNoise,K:0}}kalmanFilter(e){const t=this.kalmanState;return t.P=t.P+t.Q,t.K=t.P/(t.P+t.R),t.x=t.x+t.K*(e-t.x),t.P=(1-t.K)*t.P,t.x}detectOutlier(e,t){if(!this.config.outlierDetectionEnabled||t.length<5)return!1;const s=t.reduce((e,t)=>e+t,0)/t.length,a=t.reduce((e,t)=>e+Math.pow(t-s,2),0)/t.length,i=Math.sqrt(a);return Math.abs(e-s)>this.config.outlierThreshold*i}adaptiveSmoothing(e,t){if(!this.config.adaptiveSmoothingEnabled||t.length<3)return e;const s=[];for(let n=1;n<t.length;n++)s.push(Math.abs(t[n]-t[n-1]));const a=s.reduce((e,t)=>e+t,0)/s.length;let i=.3;return a>10?i=.1:a<3&&(i=.7),e*i+t.reduce((e,t)=>e+t,0)/t.length*(1-i)}movingAverage(e,t){const s=e.slice(-t);return s.reduce((e,t)=>e+t,0)/s.length}exponentialMovingAverage(e,t,s){return s*e+(1-s)*t}detectTrend(e,t){if(e.length<this.config.trendAnalysisWindow)return"stable";const s=e.slice(-this.config.trendAnalysisWindow),a=t.slice(-this.config.trendAnalysisWindow),i=s.length,n=a.reduce((e,t)=>e+t,0),r=s.reduce((e,t)=>e+t,0),o=(i*a.reduce((e,t,a)=>e+t*s[a],0)-n*r)/(i*a.reduce((e,t)=>e+t*t,0)-n*n);return o>.5?"increasing":o<-.5?"decreasing":"stable"}calculateAutocorrelation(e,t=1){if(e.length<t+10)return 0;const s=e.length-t,a=e.reduce((e,t)=>e+t,0)/e.length;let i=0,n=0;for(let r=0;r<s;r++)i+=(e[r]-a)*(e[r+t]-a);for(let r=0;r<e.length;r++)n+=Math.pow(e[r]-a,2);return 0===n?0:i/n}process(e,t=Date.now()){const s=this.detectOutlier(e,this.rawDataHistory),a=s&&this.rawDataHistory.length>0?this.rawDataHistory[this.rawDataHistory.length-1]:e;this.rawDataHistory.push(a),this.timestamps.push(t);const i=Math.max(3*this.config.smoothingWindow,2*this.config.trendAnalysisWindow,100);this.rawDataHistory.length>i&&(this.rawDataHistory.shift(),this.timestamps.shift());const n=this.kalmanFilter(a),r=this.config.adaptiveSmoothingEnabled?this.adaptiveSmoothing(n,this.rawDataHistory):this.movingAverage(this.rawDataHistory,this.config.smoothingWindow);return this.filteredDataHistory.push(r),this.filteredDataHistory.length>i&&this.filteredDataHistory.shift(),{filtered:n,smoothed:r,isOutlier:s,stats:this.calculateStats()}}calculateStats(){const e=this.filteredDataHistory;if(0===e.length)return{mean:0,variance:0,stdDev:0,min:0,max:0,trend:"stable",seasonality:0,autocorrelation:0};const t=e.reduce((e,t)=>e+t,0)/e.length,s=e.reduce((e,s)=>e+Math.pow(s-t,2),0)/e.length,a=Math.sqrt(s),i=Math.min(...e),n=Math.max(...e),r=this.detectTrend(e,this.timestamps),o=this.calculateAutocorrelation(e),c=e.length>=this.config.seasonalityPeriod?this.calculateAutocorrelation(e,this.config.seasonalityPeriod):0;return{mean:t,variance:s,stdDev:a,min:i,max:n,trend:r,seasonality:c,autocorrelation:o}}predict(e=1){if(this.filteredDataHistory.length<2)return Array(e).fill(this.kalmanState.x);const t=[];this.calculateStats();const s=this.filteredDataHistory.slice(-10);let a=s[s.length-1],i=0;if(s.length>=2){const e=[];for(let t=1;t<s.length;t++)e.push(s[t]-s[t-1]);i=e.reduce((e,t)=>e+t,0)/e.length}for(let n=0;n<e;n++)a+=i,t.push(Math.max(0,Math.min(100,a)));return t}reset(){this.rawDataHistory=[],this.filteredDataHistory=[],this.timestamps=[],this.kalmanState={x:0,P:1,Q:this.config.kalmanProcessNoise,R:this.config.kalmanMeasurementNoise,K:0}}updateConfig(e){this.config={...this.config,...e},this.kalmanState.Q=this.config.kalmanProcessNoise,this.kalmanState.R=this.config.kalmanMeasurementNoise}getHistory(){return{raw:[...this.rawDataHistory],filtered:[...this.filteredDataHistory],timestamps:[...this.timestamps]}}}class wt{constructor(e={}){t(this,"config"),t(this,"calibrationData",{lightingHistory:[],cameraAngleHistory:[],distanceHistory:[],timestamps:[]}),t(this,"baselineConditions",null),t(this,"adaptationFactors"),this.config={lightingAdaptation:{enabled:!0,minLightLevel:.3,adaptationSpeed:.1,shadowDetectionEnabled:!0},cameraAdaptation:{enabled:!0,angleToleranceRange:15,distanceOptimalRange:[.5,2],stabilityThreshold:.05},backgroundAdaptation:{enabled:!0,complexityThreshold:.7,contrastThreshold:.4,motionDetectionEnabled:!0},autoCalibration:{enabled:!0,calibrationPeriod:3e4,minSamplesForCalibration:20},...e},this.adaptationFactors={visibilityThreshold:.6,angleThresholds:{head:{x:.05,z:.05},shoulder:.3,body:.3},confidenceMultipliers:{lighting:1,camera:1,background:1},scoreAdjustments:{lightingBonus:0,cameraAnglePenalty:0,backgroundNoisePenalty:0}}}analyzeEnvironmentalConditions(e){const t={lighting:this.analyzeLightingConditions(e),camera:this.analyzeCameraConditions(e),background:this.analyzeBackgroundConditions(e),pose:this.analyzePoseQuality(e)};return this.updateCalibrationData(t),t}analyzeLightingConditions(e){const t=e.filter(e=>void 0!==e.visibility).map(e=>e.visibility),s=t.length>0?t.reduce((e,t)=>e+t,0)/t.length:0,a=t.length>0?t.reduce((e,t)=>e+Math.pow(t-s,2),0)/t.length:0,i=Math.max(0,1-Math.sqrt(a)),n=[e[St.NOSE],e[St.LEFT_EYE],e[St.RIGHT_EYE],e[St.MOUTH_LEFT],e[St.MOUTH_RIGHT]].filter(e=>e&&void 0!==e.visibility),r=n.length>1?n.reduce((e,t)=>{const a=t.visibility-s;return e+a*a},0)/n.length:0;let o;return o=s<.3?"poor":s<.6?"fair":s<.8?"good":"excellent",{level:s,quality:o,uniformity:i,shadows:r>.1}}analyzeCameraConditions(e){const t=e[St.LEFT_SHOULDER],s=e[St.RIGHT_SHOULDER],a=e[St.NOSE];if(!t||!s||!a)return{angle:0,distance:1,stability:0,resolution:"low"};const i=Math.abs(t.x-s.x),n=(t.x+s.x)/2,r=90*Math.abs(n-.5),o=Math.abs((t.z+s.z)/2),c=Math.max(.1,Math.min(3,2*o+i));let l=1;if(this.calibrationData.cameraAngleHistory.length>5){const e=this.calibrationData.cameraAngleHistory.slice(-5),t=e.reduce((e,t)=>{const s=t-r;return e+s*s},0)/e.length;l=Math.max(0,1-Math.sqrt(t)/10)}const d=[t,s,a].reduce((e,t)=>e+(t.visibility||0),0)/3;let h;return h=d<.5?"low":d<.8?"medium":"high",{angle:r,distance:c,stability:l,resolution:h}}analyzeBackgroundConditions(e){const t=e.filter(e=>(e.visibility||0)>.3).length/e.length,s=Math.max(0,1-t),a=e.map(e=>e.visibility||0);return{complexity:s,contrast:a.reduce((e,t)=>e+t,0)/a.length,motion:!1}}analyzePoseQuality(e){const t=e.filter(e=>(e.visibility||0)>.5).length/e.length,s=[e[St.NOSE],e[St.LEFT_SHOULDER],e[St.RIGHT_SHOULDER],e[St.LEFT_HIP],e[St.RIGHT_HIP]];return{visibility:t,occlusion:1-s.filter(e=>e&&(e.visibility||0)>.5).length/s.length,clarity:e.reduce((e,t)=>e+(t.visibility||0),0)/e.length}}updateCalibrationData(e){const t=Date.now();this.calibrationData.lightingHistory.push(e.lighting.level),this.calibrationData.cameraAngleHistory.push(e.camera.angle),this.calibrationData.distanceHistory.push(e.camera.distance),this.calibrationData.timestamps.push(t),this.calibrationData.lightingHistory.length>100&&(this.calibrationData.lightingHistory.shift(),this.calibrationData.cameraAngleHistory.shift(),this.calibrationData.distanceHistory.shift(),this.calibrationData.timestamps.shift()),this.config.autoCalibration.enabled&&this.performAutoCalibration()}performAutoCalibration(){const{calibrationPeriod:e,minSamplesForCalibration:t}=this.config.autoCalibration,s=Date.now();if(this.calibrationData.timestamps.length<t)return;const a=this.calibrationData.timestamps.map((e,t)=>({timestamp:e,index:t})).filter(t=>s-t.timestamp<e);if(a.length<t)return;const i=a.map(e=>this.calibrationData.lightingHistory[e.index]),n=a.map(e=>this.calibrationData.cameraAngleHistory[e.index]),r=a.map(e=>this.calibrationData.distanceHistory[e.index]),o=i.reduce((e,t)=>e+t,0)/i.length,c=n.reduce((e,t)=>e+t,0)/n.length,l=r.reduce((e,t)=>e+t,0)/r.length;this.updateAdaptationFactors(o,c,l)}updateAdaptationFactors(e,t,s){if(this.config.lightingAdaptation.enabled&&(e<this.config.lightingAdaptation.minLightLevel?(this.adaptationFactors.visibilityThreshold*=.8,this.adaptationFactors.confidenceMultipliers.lighting=.7):(this.adaptationFactors.confidenceMultipliers.lighting=1.2,this.adaptationFactors.scoreAdjustments.lightingBonus=5)),this.config.cameraAdaptation.enabled&&t>this.config.cameraAdaptation.angleToleranceRange){const e=1+t/90*.5;this.adaptationFactors.angleThresholds.head.x*=e,this.adaptationFactors.angleThresholds.head.z*=e,this.adaptationFactors.scoreAdjustments.cameraAnglePenalty=.2*t}const[a,i]=this.config.cameraAdaptation.distanceOptimalRange;if(s<a||s>i){const e=s<a?a/s:s/i;this.adaptationFactors.angleThresholds.shoulder*=e,this.adaptationFactors.angleThresholds.body*=e}}getEnvironmentalCompensation(){return{...this.adaptationFactors}}applyEnvironmentalCompensation(e,t){let s=e;const a={};if(t.lighting.level<.5){const e=10*(.5-t.lighting.level);s-=e,a.lightingPenalty=e}else t.lighting.level>.8&&(s+=this.adaptationFactors.scoreAdjustments.lightingBonus,a.lightingBonus=this.adaptationFactors.scoreAdjustments.lightingBonus);if(t.camera.angle>15&&(s-=this.adaptationFactors.scoreAdjustments.cameraAnglePenalty,a.cameraAnglePenalty=this.adaptationFactors.scoreAdjustments.cameraAnglePenalty),t.background.complexity>.7){const e=15*(t.background.complexity-.7);s-=e,a.backgroundPenalty=e}if(t.pose.occlusion>.3){const e=20*t.pose.occlusion;s-=e,a.occlusionPenalty=e}return{adjustedScore:Math.max(0,Math.min(100,s)),adjustments:a}}resetCalibration(){this.calibrationData={lightingHistory:[],cameraAngleHistory:[],distanceHistory:[],timestamps:[]},this.baselineConditions=null}getEnvironmentalQualityReport(e){const t=[],s=[];e.lighting.level<.4&&(t.push("光线不足"),s.push("增加照明或调整位置到光线更好的地方")),e.lighting.shadows&&(t.push("存在明显阴影"),s.push("调整光源位置以减少阴影")),e.camera.angle>20&&(t.push("摄像头角度偏差较大"),s.push("调整摄像头位置使其正对用户")),(e.camera.distance<.5||e.camera.distance>2)&&(t.push("距离摄像头过近或过远"),s.push("调整座位距离，保持适当的拍摄距离")),e.background.complexity>.7&&(t.push("背景过于复杂"),s.push("选择简洁的背景或使用虚拟背景")),e.pose.occlusion>.4&&(t.push("身体部分被遮挡"),s.push("调整坐姿确保关键部位可见"));const a=[e.lighting.level,1-e.camera.angle/45,Math.max(0,1-Math.abs(e.camera.distance-1)),1-e.background.complexity,1-e.pose.occlusion],i=a.reduce((e,t)=>e+t,0)/a.length;let n;return n=i<.3?"poor":i<.6?"fair":i<.8?"good":"excellent",{overall:n,issues:t,recommendations:s}}}class Nt{constructor(e={}){t(this,"config"),t(this,"feedbackHistory",[]),t(this,"behaviorPattern"),t(this,"learningState"),t(this,"personalBaseline",null),this.config={learningRate:.05,adaptationThreshold:.7,minSessionsForAdaptation:20,shortTermWindowHours:24,longTermWindowDays:30,explicitFeedbackWeight:.8,implicitFeedbackWeight:.3,enableDynamicThresholds:!0,thresholdAdaptationRate:.02,enableContextualLearning:!0,timeOfDayLearning:!0,weekdayPatternLearning:!0,...e},this.initializeBehaviorPattern(),this.initializeLearningState()}initializeBehaviorPattern(){this.behaviorPattern={timePatterns:{},weeklyPatterns:{},posturePreferences:{preferredHeadPosition:"center",tolerableShoulderImbalance:.15,tolerableBodyLean:.15,adaptedThresholds:{focusThreshold:70,stabilityThreshold:.5,confidenceThreshold:.6}},environmentalPreferences:{optimalLighting:.7,preferredCameraAngle:5,tolerableBackgroundComplexity:.5}}}initializeLearningState(){this.learningState={isActive:!0,totalSessions:0,totalFeedbacks:0,lastAdaptation:Date.now(),adaptationVersion:1,learningProgress:0,confidenceLevel:0}}addUserFeedback(e){this.feedbackHistory.push(e),this.learningState.totalFeedbacks++;const t=24*this.config.longTermWindowDays*4;this.feedbackHistory.length>t&&this.feedbackHistory.shift(),this.shouldTriggerAdaptation()&&this.performAdaptation()}recordSession(e,t){if(this.learningState.totalSessions++,0===e.length)return;const s=new Date,a=s.getHours(),i=s.getDay(),n=e.reduce((e,t)=>e+t.focusScore,0)/e.length,r=this.calculateVariance(e.map(e=>e.focusScore));this.config.timeOfDayLearning&&this.updateTimePattern(a,n,r),this.config.weekdayPatternLearning&&this.updateWeeklyPattern(i,a,n,r);const o={timestamp:s.getTime(),type:"implicit",feedbackValue:this.calculateImplicitFeedbackValue(n,r,t),context:{timeOfDay:a,dayOfWeek:i,sessionDuration:t,environmentalConditions:e[e.length-1].environmentalFactors},associatedAnalysis:e[e.length-1]};this.addUserFeedback(o)}updateTimePattern(e,t,s){const a=e.toString();if(this.behaviorPattern.timePatterns[a]){const e=this.behaviorPattern.timePatterns[a],i=this.config.learningRate;e.avgFocusScore=e.avgFocusScore*(1-i)+t*i,e.variance=e.variance*(1-i)+s*i,e.sampleCount++}else this.behaviorPattern.timePatterns[a]={avgFocusScore:t,variance:s,sampleCount:1}}updateWeeklyPattern(e,t,s,a){const i=e.toString();if(this.behaviorPattern.weeklyPatterns[i]){const e=this.behaviorPattern.weeklyPatterns[i],n=this.config.learningRate;e.avgFocusScore=e.avgFocusScore*(1-n)+s*n,e.variance=e.variance*(1-n)+a*n,e.activeHours.includes(t)||e.activeHours.push(t)}else this.behaviorPattern.weeklyPatterns[i]={avgFocusScore:s,activeHours:[t],variance:a}}calculateVariance(e){if(0===e.length)return 0;const t=e.reduce((e,t)=>e+t,0)/e.length;return e.reduce((e,s)=>e+Math.pow(s-t,2),0)/e.length}calculateImplicitFeedbackValue(e,t,s){let a=0;return a+=(e-50)/50*.5,a+=2*(Math.max(0,1-t/100)-.5)*.3,a+=2*(1-Math.abs(s-45)/45-.5)*.2,Math.max(-1,Math.min(1,a))}shouldTriggerAdaptation(){const e=(Date.now()-this.learningState.lastAdaptation)/36e5;return this.learningState.totalSessions>=this.config.minSessionsForAdaptation&&e>=24&&this.feedbackHistory.length>=10&&this.calculateAdaptationNeed()>this.config.adaptationThreshold}calculateAdaptationNeed(){if(0===this.feedbackHistory.length)return 0;const e=this.getRecentFeedbacks(this.config.shortTermWindowHours);if(0===e.length)return 0;const t=e.reduce((e,t)=>e+t.feedbackValue,0)/e.length,s=this.calculateVariance(e.map(e=>e.feedbackValue));return Math.max(Math.max(0,-t),Math.min(1,s))}getRecentFeedbacks(e){const t=Date.now()-60*e*60*1e3;return this.feedbackHistory.filter(e=>e.timestamp>=t)}performAdaptation(){const e=this.analyzeFeedbackPatterns();this.adaptPosturePreferences(e),this.adaptEnvironmentalPreferences(e),this.config.enableDynamicThresholds&&this.adaptDynamicThresholds(e),this.learningState.lastAdaptation=Date.now(),this.learningState.adaptationVersion++,this.learningState.learningProgress=Math.min(1,this.learningState.totalSessions/100),this.learningState.confidenceLevel=this.calculateModelConfidence()}analyzeFeedbackPatterns(){const e=this.getRecentFeedbacks(24*this.config.longTermWindowDays);return{avgFeedback:e.reduce((e,t)=>e+t.feedbackValue,0)/e.length,positiveFeedbackRatio:e.filter(e=>e.feedbackValue>0).length/e.length,timeOfDayCorrelations:this.analyzeTimeOfDayCorrelations(e),postureCorrelations:this.analyzePostureCorrelations(e),environmentalCorrelations:this.analyzeEnvironmentalCorrelations(e)}}analyzeTimeOfDayCorrelations(e){const t={};e.forEach(e=>{const s=e.context.timeOfDay.toString();t[s]||(t[s]=[]),t[s].push(e.feedbackValue)});const s={};return Object.keys(t).forEach(e=>{const a=t[e];s[e]=a.reduce((e,t)=>e+t,0)/a.length}),s}analyzePostureCorrelations(e){const t={headPositions:{},shoulderBalances:[],bodyLeans:[],feedbackValues:[]};return e.forEach(e=>{const s=e.associatedAnalysis,a=s.headPosition;t.headPositions[a]||(t.headPositions[a]=[]),t.headPositions[a].push(e.feedbackValue),t.shoulderBalances.push(s.shoulderBalance),t.bodyLeans.push(s.bodyLean),t.feedbackValues.push(e.feedbackValue)}),{preferredHeadPosition:this.findBestHeadPosition(t.headPositions),shoulderBalanceCorrelation:this.calculateCorrelation(t.shoulderBalances,t.feedbackValues),bodyLeanCorrelation:this.calculateCorrelation(t.bodyLeans,t.feedbackValues)}}analyzeEnvironmentalCorrelations(e){const t=[],s=[],a=[],i=[];return e.forEach(e=>{const n=e.associatedAnalysis.environmentalFactors;t.push(n.lighting.level),s.push(n.camera.angle),a.push(n.background.complexity),i.push(e.feedbackValue)}),{lightingCorrelation:this.calculateCorrelation(t,i),cameraAngleCorrelation:this.calculateCorrelation(s,i),backgroundComplexityCorrelation:this.calculateCorrelation(a,i),optimalLighting:this.findOptimalValue(t,i),optimalCameraAngle:this.findOptimalValue(s,i),optimalBackgroundComplexity:this.findOptimalValue(a,i)}}findBestHeadPosition(e){let t="center",s=-1;return Object.keys(e).forEach(a=>{const i=e[a],n=i.reduce((e,t)=>e+t,0)/i.length;n>s&&(s=n,t=a)}),t}calculateCorrelation(e,t){if(e.length!==t.length||0===e.length)return 0;const s=e.reduce((e,t)=>e+t,0)/e.length,a=t.reduce((e,t)=>e+t,0)/t.length;let i=0,n=0,r=0;for(let o=0;o<e.length;o++){const c=e[o]-s,l=t[o]-a;i+=c*l,n+=c*c,r+=l*l}return 0===n||0===r?0:i/Math.sqrt(n*r)}findOptimalValue(e,t){if(e.length!==t.length||0===e.length)return 0;let s=0,a=0;for(let i=0;i<e.length;i++){const n=Math.max(0,t[i]);s+=e[i]*n,a+=n}return a>0?s/a:e.reduce((e,t)=>e+t,0)/e.length}adaptPosturePreferences(e){this.config.thresholdAdaptationRate;const t=this.behaviorPattern.posturePreferences;if(e.postureCorrelations.preferredHeadPosition&&(t.preferredHeadPosition=e.postureCorrelations.preferredHeadPosition),Math.abs(e.postureCorrelations.shoulderBalanceCorrelation)>.3){const s=e.postureCorrelations.shoulderBalanceCorrelation>0?1.1:.9;t.tolerableShoulderImbalance*=s,t.tolerableShoulderImbalance=Math.max(.05,Math.min(.3,t.tolerableShoulderImbalance))}if(Math.abs(e.postureCorrelations.bodyLeanCorrelation)>.3){const s=e.postureCorrelations.bodyLeanCorrelation>0?1.1:.9;t.tolerableBodyLean*=s,t.tolerableBodyLean=Math.max(.05,Math.min(.3,t.tolerableBodyLean))}}adaptEnvironmentalPreferences(e){const t=this.behaviorPattern.environmentalPreferences,s=this.config.thresholdAdaptationRate;e.environmentalCorrelations.optimalLighting>0&&(t.optimalLighting=t.optimalLighting*(1-s)+e.environmentalCorrelations.optimalLighting*s),void 0!==e.environmentalCorrelations.optimalCameraAngle&&(t.preferredCameraAngle=t.preferredCameraAngle*(1-s)+e.environmentalCorrelations.optimalCameraAngle*s),e.environmentalCorrelations.optimalBackgroundComplexity>0&&(t.tolerableBackgroundComplexity=t.tolerableBackgroundComplexity*(1-s)+e.environmentalCorrelations.optimalBackgroundComplexity*s)}adaptDynamicThresholds(e){const t=this.behaviorPattern.posturePreferences.adaptedThresholds,s=this.config.thresholdAdaptationRate;e.avgFeedback<0?(t.focusThreshold*=1-s,t.confidenceThreshold*=1-.5*s):e.avgFeedback>.5&&(t.focusThreshold*=1+.5*s,t.confidenceThreshold*=1+.3*s),t.focusThreshold=Math.max(50,Math.min(90,t.focusThreshold)),t.confidenceThreshold=Math.max(.3,Math.min(.9,t.confidenceThreshold)),t.stabilityThreshold=Math.max(.2,Math.min(.8,t.stabilityThreshold))}calculateModelConfidence(){const e=Math.min(1,this.learningState.totalSessions/100),t=Math.min(1,this.feedbackHistory.length/50),s=this.getRecentFeedbacks(24*this.config.shortTermWindowHours);return.4*e+.4*t+.2*(s.length>0?Math.abs(s.reduce((e,t)=>e+t.feedbackValue,0)/s.length):0)}getPersonalizedThresholds(){return this.behaviorPattern.posturePreferences.adaptedThresholds}getEnvironmentalPreferences(){return this.behaviorPattern.environmentalPreferences}getExpectedFocusScoreForTime(e=Date.now()){const t=new Date(e),s=t.getHours(),a=t.getDay(),i=this.behaviorPattern.timePatterns[s.toString()],n=this.behaviorPattern.weeklyPatterns[a.toString()];return i&&n?(i.avgFocusScore+n.avgFocusScore)/2:i?i.avgFocusScore:n?n.avgFocusScore:70}predictUserPerformance(e){var t;const s=this.getExpectedFocusScoreForTime(e),a=this.learningState.confidenceLevel,i=[],n=new Date(e),r=n.getHours(),o=n.getDay(),c=this.behaviorPattern.timePatterns[r.toString()];if(c&&c.avgFocusScore<60&&i.push("在".concat(r,"点您的专注度通常较低，建议采取额外措施提高注意力")),(null==(t=this.behaviorPattern.weeklyPatterns[o.toString()])?void 0:t.avgFocusScore)<65){const e=["周日","周一","周二","周三","周四","周五","周六"];i.push("".concat(e[o],"您的整体表现通常较低，建议调整作息时间"))}return{expectedScore:s,confidence:a,recommendations:i}}getLearningState(){return{...this.learningState}}getBehaviorPattern(){return{...this.behaviorPattern}}resetLearningData(){this.feedbackHistory=[],this.initializeBehaviorPattern(),this.initializeLearningState()}exportLearningData(){return{config:this.config,feedbackHistory:this.feedbackHistory,behaviorPattern:this.behaviorPattern,learningState:this.learningState,personalBaseline:this.personalBaseline}}importLearningData(e){this.feedbackHistory=e.feedbackHistory||[],this.behaviorPattern=e.behaviorPattern||this.behaviorPattern,this.learningState=e.learningState||this.learningState,this.personalBaseline=e.personalBaseline||null}}class Tt{constructor(e={}){t(this,"config"),t(this,"frameBuffer",[]),t(this,"changeHistory",[]),t(this,"previousFrame",null),this.config={frameWindowSize:30,frameStride:1,minFramesForAnalysis:10,continuityThreshold:.7,motionSmoothnessFactor:.8,stabilityWindow:15,stabilityThreshold:.85,trendAnalysisEnabled:!0,trendSensitivity:.3,frameDropDetection:!0,inconsistencyDetection:!0,predictionEnabled:!0,predictionHorizon:5,...e}}addFrame(e){if(this.previousFrame){const t=this.calculateFrameChanges(this.previousFrame,e);this.changeHistory.push(t),this.changeHistory.length>2*this.config.frameWindowSize&&this.changeHistory.shift()}this.frameBuffer.push(e),this.frameBuffer.length>this.config.frameWindowSize&&this.frameBuffer.shift(),this.previousFrame=e}calculateFrameChanges(e,t){var s,a,i,n,r,o;const c=33,l={x:((null==(s=t.headOffset)?void 0:s.x)||0)-((null==(a=e.headOffset)?void 0:a.x)||0),y:((null==(i=t.headOffset)?void 0:i.y)||0)-((null==(n=e.headOffset)?void 0:n.y)||0),z:((null==(r=t.headOffset)?void 0:r.z)||0)-((null==(o=e.headOffset)?void 0:o.z)||0)},d={left:Math.abs(t.shoulderBalance-e.shoulderBalance),right:Math.abs(t.shoulderBalance-e.shoulderBalance)},h={x:Math.abs(t.bodyLean-e.bodyLean),y:0},u=Math.sqrt(l.x*l.x+l.y*l.y+l.z*l.z)/c,m=d.left/c,p=h.x/c,g=this.changeHistory[this.changeHistory.length-1],y=g?(u-g.velocities.headVelocity)/c:0,f=g?(m-g.velocities.shoulderVelocity)/c:0,x=g?(p-g.velocities.bodyVelocity)/c:0;return{frameIndex:this.frameBuffer.length,timestamp:Date.now(),positionChanges:{head:l,shoulders:d,bodyCenter:h},velocities:{headVelocity:u,shoulderVelocity:m,bodyVelocity:p},accelerations:{headAcceleration:y,shoulderAcceleration:f,bodyAcceleration:x}}}calculatePosturalStability(){if(this.changeHistory.length<5)return{overall:0,head:0,shoulders:0,body:0};const e=this.changeHistory.slice(-this.config.stabilityWindow),t=e.map(e=>Math.sqrt(e.positionChanges.head.x**2+e.positionChanges.head.y**2+e.positionChanges.head.z**2)),s=this.calculateStabilityScore(t),a=e.map(e=>e.positionChanges.shoulders.left),i=this.calculateStabilityScore(a),n=e.map(e=>e.positionChanges.bodyCenter.x),r=this.calculateStabilityScore(n);return{overall:.5*s+.3*i+.2*r,head:s,shoulders:i,body:r}}calculateStabilityScore(e){if(0===e.length)return 0;const t=e.reduce((e,t)=>e+t,0)/e.length,s=e.reduce((e,s)=>e+(s-t)**2,0)/e.length,a=Math.sqrt(s);return Math.max(0,1-a/.1)}calculateMovementContinuity(){if(this.changeHistory.length<10)return{overall:0,smoothness:0,predictability:0};const e=this.changeHistory.slice(-15),t=e.map(e=>e.accelerations.headAcceleration),s=this.calculateSmoothness(t),a=e.map(e=>e.velocities.headVelocity),i=this.calculatePredictability(a);return{overall:.6*s+.4*i,smoothness:s,predictability:i}}calculateSmoothness(e){if(e.length<3)return 0;const t=[];for(let a=2;a<e.length;a++){const s=e[a]-2*e[a-1]+e[a-2];t.push(Math.abs(s))}const s=t.reduce((e,t)=>e+t,0)/t.length;return Math.max(0,1-s/.005)}calculatePredictability(e){if(e.length<5)return 0;const t=e.length-1,s=e.reduce((e,t)=>e+t,0)/e.length;let a=0,i=0;for(let r=0;r<t;r++)a+=(e[r]-s)*(e[r+1]-s),i+=(e[r]-s)**2;const n=i>0?a/i:0;return Math.max(0,n)}analyzeTrends(){if(!this.config.trendAnalysisEnabled||this.frameBuffer.length<15)return{focusScoreTrend:"stable",postureQualityTrend:"stable",movementTrend:"stable"};const e=this.frameBuffer.slice(-15),t=e.map(e=>e.focusScore),s=this.calculateTrend(t),a=e.map(e=>e.stability),i=this.calculateTrend(a),n=this.changeHistory.slice(-10).map(e=>e.velocities.headVelocity+e.velocities.shoulderVelocity+e.velocities.bodyVelocity);return{focusScoreTrend:s,postureQualityTrend:i,movementTrend:this.calculateMovementTrend(n)}}calculateTrend(e){if(e.length<5)return"stable";const t=e.slice(0,Math.floor(e.length/2)),s=e.slice(Math.floor(e.length/2)),a=t.reduce((e,t)=>e+t,0)/t.length,i=s.reduce((e,t)=>e+t,0)/s.length-a;return i>this.config.trendSensitivity?"improving":i<-this.config.trendSensitivity?"declining":"stable"}calculateMovementTrend(e){if(e.length<5)return"stable";const t=e.slice(0,Math.floor(e.length/2)),s=e.slice(Math.floor(e.length/2)),a=t.reduce((e,t)=>e+t,0)/t.length,i=s.reduce((e,t)=>e+t,0)/s.length-a;return i>.5*this.config.trendSensitivity?"increasing":i<.5*-this.config.trendSensitivity?"decreasing":"stable"}calculateMultiFrameFocusScore(){if(0===this.frameBuffer.length)return{score:0,confidence:0};const e=this.frameBuffer.slice(-this.config.frameWindowSize);let t=0,s=0;e.forEach((a,i)=>{const n=Math.exp(i/e.length);t+=a.focusScore*n,s+=n});const a=s>0?t/s:0,i=this.calculatePosturalStability().overall,n=5*i,r=3*this.calculateMovementContinuity().overall,o=this.analyzeTrends();let c=0;return"improving"===o.focusScoreTrend?c=2:"declining"===o.focusScoreTrend&&(c=-2),{score:Math.max(0,Math.min(100,a+n+r+c)),confidence:e.reduce((e,t)=>e+t.confidence,0)/e.length*(e.length/this.config.frameWindowSize)*i}}detectAnomalies(){const e=[],t=[];if(this.config.inconsistencyDetection&&this.changeHistory.length>5){const s=this.changeHistory.slice(-10),a=s.reduce((e,t)=>e+t.velocities.headVelocity,0)/s.length;s.forEach((s,i)=>{s.velocities.headVelocity>3*a&&e.push(s.frameIndex),Math.abs(s.accelerations.headAcceleration)>.01&&t.push(s.frameIndex)})}return{frameDrops:[],suddenMovements:e,inconsistencies:t}}analyzeMultiFrame(){if(this.frameBuffer.length<this.config.minFramesForAnalysis)return null;const e=this.frameBuffer.length,t=33*e,s=this.calculatePosturalStability(),a=this.calculateMovementContinuity(),i=this.analyzeTrends(),n=this.calculateMultiFrameFocusScore(),r=this.detectAnomalies(),o=this.config.predictionEnabled?this.generatePredictions():void 0,c=this.generateMultiFrameRecommendations({posturalStability:s,movementContinuity:a,trends:i,anomalies:r});return{frameCount:e,timeSpan:t,averageFPS:30,posturalStability:s,movementContinuity:a,trends:i,multiFrameFocusScore:n.score,confidenceLevel:n.confidence,anomalies:r,predictions:o,recommendations:c}}generatePredictions(){if(this.frameBuffer.length<10)return{nextFrameFocusScore:0,stabilityForecast:0,recommendedActions:[]};const e=this.frameBuffer.slice(-10).map(e=>e.focusScore),t=this.calculateTrend(e),s=e[e.length-1],a=this.calculateAverageChange(e);let i=s;"improving"===t?i=Math.min(100,s+Math.abs(a)):"declining"===t&&(i=Math.max(0,s-Math.abs(a)));const n=this.calculatePosturalStability().overall,r=this.analyzeTrends().postureQualityTrend;let o=n;return"improving"===r?o=Math.min(1,n+.1):"declining"===r&&(o=Math.max(0,n-.1)),{nextFrameFocusScore:i,stabilityForecast:o,recommendedActions:this.generatePredictiveRecommendations(i,o,t)}}calculateAverageChange(e){if(e.length<2)return 0;let t=0;for(let s=1;s<e.length;s++)t+=e[s]-e[s-1];return t/(e.length-1)}generatePredictiveRecommendations(e,t,s){const a=[];return e<60&&a.push("预测显示专注度可能下降，建议主动调整姿态"),t<.6&&a.push("预测显示姿态稳定性可能降低，建议减少不必要的移动"),"declining"===s?a.push("检测到下降趋势，建议短暂休息或调整环境"):"improving"===s&&a.push("专注度呈上升趋势，继续保持当前状态"),a}generateMultiFrameRecommendations(e){const t=[];return e.posturalStability.overall<.7&&(t.push("姿态稳定性较低，建议减少频繁的位置调整"),e.posturalStability.head<.6&&t.push("头部移动较频繁，尝试保持头部相对固定"),e.posturalStability.shoulders<.6&&t.push("肩膀位置不够稳定，注意保持肩膀平衡")),e.movementContinuity.overall<.6&&(t.push("动作连续性有待改善，避免突然的大幅度移动"),e.movementContinuity.smoothness<.5&&t.push("动作平滑度不足，尝试更缓慢、平稳的调整")),"declining"===e.trends.focusScoreTrend&&t.push("专注度呈下降趋势，建议检查环境因素或考虑短暂休息"),"increasing"===e.trends.movementTrend&&t.push("检测到运动增加，如果感到疲劳建议适当休息"),e.anomalies.suddenMovements.length>2&&t.push("检测到多次突然移动，建议保持更稳定的坐姿"),t}reset(){this.frameBuffer=[],this.changeHistory=[],this.previousFrame=null}getDetailedStats(){if(0===this.frameBuffer.length)return null;const e=this.calculatePosturalStability(),t=this.calculateMovementContinuity(),s=this.analyzeTrends(),a=this.detectAnomalies();return{frameStatistics:{totalFrames:this.frameBuffer.length,averageFocusScore:this.frameBuffer.reduce((e,t)=>e+t.focusScore,0)/this.frameBuffer.length,averageConfidence:this.frameBuffer.reduce((e,t)=>e+t.confidence,0)/this.frameBuffer.length},stability:e,continuity:t,trends:s,anomalies:a}}}class jt{constructor(e={},s={},a={}){t(this,"config"),t(this,"historyData",[]),t(this,"personalBaseline",null),t(this,"environmentalData",{}),t(this,"timeSeriesAnalyzer"),t(this,"environmentalAnalyzer"),t(this,"adaptiveLearning"),t(this,"multiFrameAnalyzer"),t(this,"previousAnalysis",null),this.config={minVisibility:.6,focusThreshold:70,historySize:30,smoothingFactor:.3,stabilityThreshold:.1,lightAdaptationEnabled:!0,angleAdaptationEnabled:!0,personalizedLearningEnabled:!0,adaptationSpeed:.05,baselineCalibrationPeriod:300,...e},this.timeSeriesAnalyzer=new Et({kalmanProcessNoise:.1,kalmanMeasurementNoise:.5,smoothingWindow:8,adaptiveSmoothingEnabled:!0,outlierDetectionEnabled:!0,outlierThreshold:2,trendAnalysisWindow:15,seasonalityPeriod:30});const i={lightingAdaptation:{enabled:this.config.lightAdaptationEnabled,minLightLevel:.4,adaptationSpeed:.1,shadowDetectionEnabled:!0},cameraAdaptation:{enabled:this.config.angleAdaptationEnabled,angleToleranceRange:20,distanceOptimalRange:[.6,1.8],stabilityThreshold:.05},backgroundAdaptation:{enabled:!0,complexityThreshold:.6,contrastThreshold:.5,motionDetectionEnabled:!1},autoCalibration:{enabled:!0,calibrationPeriod:3e4,minSamplesForCalibration:15}};this.environmentalAnalyzer=new wt(i),this.adaptiveLearning=function(e){return new Nt(e)}(s),this.multiFrameAnalyzer=function(e){return new Tt(e)}(a)}calculateEnhancedAngle(e,t,s){var a,i,n;const r=e.x-t.x,o=e.y-t.y,c=s.x-t.x,l=s.y-t.y,d=r*c+o*l,h=Math.sqrt(r*r+o*o),u=Math.sqrt(c*c+l*l),m=Math.min((null!=(a=e.visibility)?a:1)*(null!=(i=t.visibility)?i:1)*(null!=(n=s.visibility)?n:1),2*Math.min(h,u));return{angle:Math.acos(Math.max(-1,Math.min(1,d/(h*u)))),confidence:m}}detectEnvironmentalFactors(e){return this.environmentalAnalyzer.analyzeEnvironmentalConditions(e)}detectSeatedPosture(e){if(!e||e.length<33)return{isSeated:!1,confidence:0};const t=e[St.LEFT_HIP],s=e[St.RIGHT_HIP],a=e[St.LEFT_KNEE],i=e[St.RIGHT_KNEE];if(![t,s,a,i].every(e=>{var t;return(null!=(t=e.visibility)?t:0)>=this.config.minVisibility}))return{isSeated:!1,confidence:0};const n=this.calculateEnhancedAngle({...t,y:t.y-.1},t,a),r=this.calculateEnhancedAngle({...s,y:s.y-.1},s,i),o=(n.angle+r.angle)/2,c=(n.confidence+r.confidence)/2,l=180*o/Math.PI;let d=50,h=130;const u=this.detectEnvironmentalFactors(e);if(u.camera.angle>10){const e=u.camera.angle/90*20;d-=e,h+=e}const m=l>d&&l<h;return{isSeated:m,confidence:c*(m?1:.5)}}analyzeHeadPositionEnhanced(e){var t,s,a;const i=e[St.NOSE],n=e[St.LEFT_SHOULDER],r=e[St.RIGHT_SHOULDER],o=this.config.minVisibility;if(![i,n,r].every(e=>{var t;return(null!=(t=e.visibility)?t:0)>=o}))return{position:"center",offset:{x:0,y:0,z:0},confidence:0};const c=(n.x+r.x)/2,l=(n.y+r.y)/2,d=(n.z+r.z)/2,h={x:i.x-c,y:i.y-l,z:i.z-d};let u=h;this.personalBaseline&&this.config.personalizedLearningEnabled&&(u={x:h.x-this.personalBaseline.avgHeadOffset.x,y:h.y-this.personalBaseline.avgHeadOffset.y,z:h.z-this.personalBaseline.avgHeadOffset.z});let m=.05,p=.05;this.detectEnvironmentalFactors(e).lighting.level<.7&&(m*=1.2,p*=1.2);const g=Math.min(null!=(t=i.visibility)?t:1,null!=(s=n.visibility)?s:1,null!=(a=r.visibility)?a:1);let y="center";return Math.abs(u.x)>m?y=u.x>0?"right":"left":Math.abs(u.z)>p&&(y=u.z>0?"back":"forward"),{position:y,offset:u,confidence:g}}calculateIntelligentFocusScore(e,t,s,a,i){const n=this.adaptiveLearning.getPersonalizedThresholds(),r=this.adaptiveLearning.getEnvironmentalPreferences();let o=100,c=1;const l={};"center"===e.position?l.headPositionScore=100:"forward"===e.position||"back"===e.position?l.headPositionScore=70:l.headPositionScore=50,this.adaptiveLearning.getBehaviorPattern().posturePreferences.preferredHeadPosition===e.position&&(l.headPositionScore=Math.min(100,1.2*l.headPositionScore));const d=this.adaptiveLearning.getBehaviorPattern().posturePreferences.tolerableShoulderImbalance;l.shoulderBalanceScore=Math.max(0,100-100*Math.abs(t)/d);const h=this.adaptiveLearning.getBehaviorPattern().posturePreferences.tolerableBodyLean;l.bodyLeanScore=Math.max(0,100-100*Math.abs(s)/h),l.seatedScore=a.isSeated?100:20,o=.4*l.headPositionScore+.25*l.shoulderBalanceScore+.25*l.bodyLeanScore+.1*l.seatedScore;let u=1;const m=Math.abs(i.lighting.level-r.optimalLighting),p=Math.abs(i.camera.angle-r.preferredCameraAngle);Math.abs(i.background.complexity-r.tolerableBackgroundComplexity),m<.2?u*=1.1:m>.4&&(u*=.9),p<10?u*=1.05:p>25&&(u*=.95),o*=u,c=Math.min(1,.4*e.confidence+.3*a.confidence+i.pose.visibility/1*.3);const g=n.confidenceThreshold;return c<g&&(o*=c/g),{score:Math.max(0,Math.min(100,o)),confidence:c,factors:l}}applyTemporalSmoothing(e,t){const s=this.timeSeriesAnalyzer.process(e,t);return{smoothed:s.smoothed,isOutlier:s.isOutlier,stats:s.stats}}calculateStability(){if(this.historyData.length<5)return 0;const e=this.historyData.slice(-10).map(e=>e.focusScore),t=e.reduce((e,t)=>e+t,0)/e.length,s=e.reduce((e,s)=>e+Math.pow(s-t,2),0)/e.length,a=Math.sqrt(s);return Math.max(0,1-a/50)}analyzeTrend(){if(this.historyData.length<10)return"stable";const e=this.historyData.slice(-10),t=this.historyData.slice(-20,-10);if(0===t.length)return"stable";const s=e.reduce((e,t)=>e+t.focusScore,0)/e.length-t.reduce((e,t)=>e+t.focusScore,0)/t.length;return s>5?"improving":s<-5?"declining":"stable"}mapTrendFromStats(e){switch(e){case"increasing":return"improving";case"decreasing":return"declining";default:return"stable"}}updatePersonalBaseline(e){if(!this.config.personalizedLearningEnabled)return;const t=Date.now();if(this.personalBaseline||(this.personalBaseline={avgHeadOffset:{x:0,y:0,z:0},avgShoulderBalance:0,avgBodyLean:0,preferredPosture:{headPosition:"center",shoulderTolerance:.1,bodyLeanTolerance:.1},calibrationCount:0,lastCalibration:t}),e.confidence>.8&&e.focusScore>60){const s=this.config.adaptationSpeed,a=this.personalBaseline;a.avgHeadOffset.x=a.avgHeadOffset.x*(1-s)+e.headOffset.x*s,a.avgHeadOffset.y=a.avgHeadOffset.y*(1-s)+e.headOffset.y*s,a.avgHeadOffset.z=a.avgHeadOffset.z*(1-s)+e.headOffset.z*s,a.avgShoulderBalance=a.avgShoulderBalance*(1-s)+e.shoulderBalance*s,a.avgBodyLean=a.avgBodyLean*(1-s)+e.bodyLean*s,a.calibrationCount++,a.lastCalibration=t}}generateIntelligentRecommendations(e){const t=[],{trend:s,stability:a,environmentalFactors:i,headOffset:n,shoulderBalance:r,bodyLean:o,personalizedThresholds:c,expectedScore:l}=e;if("declining"===s?t.push("检测到专注度下降趋势，建议短暂休息或调整坐姿"):"improving"===s&&t.push("专注度正在改善，保持当前状态"),c&&a<c.stabilityThreshold&&t.push("姿态稳定性较低，建议保持更稳定的坐姿"),l){const s=(new Date).getHours(),a=this.adaptiveLearning.predictUserPerformance(Date.now());a.expectedScore>75&&e.focusScore<.8*a.expectedScore&&t.push("根据您在".concat(s,"点的历史表现，您通常能达到更高的专注度")),a.recommendations.forEach(e=>{t.includes(e)||t.push(e)})}const d=this.adaptiveLearning.getEnvironmentalPreferences();i&&d&&(Math.abs(i.lighting.level-d.optimalLighting)>.3&&(i.lighting.level<d.optimalLighting?t.push("根据您的偏好，增加环境光线可能有助于提高专注度"):t.push("根据您的偏好，适当降低环境光线可能有助于提高专注度")),Math.abs(i.camera.angle-d.preferredCameraAngle)>15&&t.push("调整摄像头角度到您的偏好位置可能会改善检测效果"));const h=this.adaptiveLearning.getLearningState();if(h.learningProgress<.3?t.push("系统仍在学习您的习惯，持续使用将提供更准确的个人化建议"):h.confidenceLevel>.8&&t.push("基于您的使用习惯，系统已优化了检测标准"),n&&(Math.abs(n.x)>.05||Math.abs(n.y)>.05)&&t.push("调整头部位置到屏幕中央"),"number"==typeof r&&"number"==typeof o){const e=this.adaptiveLearning.getBehaviorPattern();Math.abs(r)>e.posturePreferences.tolerableShoulderImbalance&&t.push("注意保持肩膀平衡"),Math.abs(o)>e.posturePreferences.tolerableBodyLean&&t.push("避免身体过度倾斜，保持直立坐姿")}return i&&("poor"===i.lighting.quality&&t.push("改善照明条件以获得更好的检测效果"),i.background.motion&&t.push("减少背景干扰有助于提高检测精度")),t.slice(0,5)}analyze(e,t=Date.now(),s){if(!e||e.length<33)return{isSeated:!1,isFocused:!1,focusScore:0,headPosition:"center",shoulderBalance:0,bodyLean:0,warnings:["无法检测到有效的姿态数据"],confidence:0,stability:0,trend:"stable",personalizedScore:0,environmentalFactors:{lighting:{level:0,quality:"poor",uniformity:0,shadows:!1},camera:{angle:0,distance:1,stability:0,resolution:"low"},background:{complexity:1,contrast:0,motion:!1},pose:{visibility:0,occlusion:1,clarity:0}},recommendations:["请确保摄像头能够清晰捕捉您的姿态"]};const a=this.detectEnvironmentalFactors(e),i=this.detectSeatedPosture(e),n=this.analyzeHeadPositionEnhanced(e),r=this.calculateShoulderBalance(e),o=this.calculateBodyLean(e),c=this.calculateIntelligentFocusScore(n,r,o,i,a),l=this.applyTemporalSmoothing(c.score,t),d=l.smoothed,h=Math.max(l.stats.autocorrelation,this.calculateStability()),u=this.mapTrendFromStats(l.stats.trend),m=this.adaptiveLearning.getPersonalizedThresholds(),p=m.focusThreshold,g={timestamp:t,focusScore:d,headPosition:n.position,shoulderBalance:r,bodyLean:o,confidence:c.confidence,environmentalFactors:a};this.historyData.push(g),this.historyData.length>this.config.historySize&&this.historyData.shift();const y=this.adaptiveLearning.getExpectedFocusScoreForTime(t),f=d+.2*(d-y),x={isSeated:i.isSeated,isFocused:d>p,focusScore:d,headPosition:n.position,shoulderBalance:r,bodyLean:o,warnings:this.generateWarnings(n.position,r,o,i.isSeated),confidence:c.confidence,stability:h,trend:u,personalizedScore:Math.max(0,Math.min(100,f)),environmentalFactors:a,recommendations:this.generateIntelligentRecommendations({trend:u,stability:h,environmentalFactors:a,headOffset:n.offset,shoulderBalance:r,bodyLean:o,personalizedThresholds:m,expectedScore:y}),headOffset:n.offset};return this.multiFrameAnalyzer&&this.multiFrameAnalyzer.addFrame(x),this.previousAnalysis=x,x}calculateShoulderBalance(e){var t,s;const a=e[St.LEFT_SHOULDER],i=e[St.RIGHT_SHOULDER];if((null!=(t=a.visibility)?t:0)<this.config.minVisibility||(null!=(s=i.visibility)?s:0)<this.config.minVisibility)return 0;const n=i.y-a.y;return Math.max(-1,Math.min(1,10*n))}calculateBodyLean(e){const t=e[St.LEFT_SHOULDER],s=e[St.RIGHT_SHOULDER],a=e[St.LEFT_HIP],i=e[St.RIGHT_HIP];if([t,s,a,i].some(e=>{var t;return(null!=(t=e.visibility)?t:0)<this.config.minVisibility}))return 0;const n=(t.x+s.x)/2-(a.x+i.x)/2;return Math.max(-1,Math.min(1,5*n))}generateWarnings(e,t,s,a){const i=[];a||i.push("请保持坐姿进行学习"),"forward"===e?i.push("头部过于前倾，请调整坐姿"):"left"!==e&&"right"!==e||i.push("头部倾斜，请保持头部居中");const n=this.personalBaseline?this.personalBaseline.preferredPosture.shoulderTolerance:.3,r=this.personalBaseline?this.personalBaseline.preferredPosture.bodyLeanTolerance:.3;return Math.abs(t)>n&&i.push("肩膀不平衡，请调整坐姿"),Math.abs(s)>r&&i.push("身体倾斜过度，请坐直"),i}getPersonalBaseline(){return this.personalBaseline}resetPersonalBaseline(){this.personalBaseline=null,this.historyData=[]}getHistoryData(){return[...this.historyData]}getTimeSeriesStats(){return this.timeSeriesAnalyzer.getStats()}predictFocusScore(e=5){return this.timeSeriesAnalyzer.predict(e)}hasRecentOutliers(){const e=this.timeSeriesAnalyzer.getHistory();if(e.raw.length<5)return!1;const t=e.raw.slice(-5),s=t.reduce((e,t)=>e+t,0)/t.length,a=t.reduce((e,t)=>e+Math.pow(t-s,2),0)/t.length,i=Math.sqrt(a);return t.some(e=>Math.abs(e-s)>2*i)}resetTimeSeriesAnalyzer(){this.timeSeriesAnalyzer.reset()}updateConfig(e){this.config={...this.config,...e}}updateTimeSeriesConfig(e){this.timeSeriesAnalyzer.updateConfig(e)}recordSession(e){if(0===this.historyData.length)return;const t=this.historyData.map(e=>({isSeated:!0,isFocused:e.focusScore>this.config.focusThreshold,focusScore:e.focusScore,headPosition:e.headPosition,shoulderBalance:e.shoulderBalance,bodyLean:e.bodyLean,warnings:[],confidence:e.confidence,stability:.5,trend:"stable",personalizedScore:e.focusScore,environmentalFactors:e.environmentalFactors,recommendations:[]}));this.adaptiveLearning.recordSession(t,e)}addUserFeedback(e,t,s,a){const i=new Date,n={timestamp:Date.now(),type:"explicit",satisfied:e,comfort:t,sessionQuality:s,comments:a,sessionContext:{timeOfDay:i.getHours(),dayOfWeek:i.getDay(),sessionDuration:0}};this.adaptiveLearning.addUserFeedback(n)}predictUserPerformance(e){return this.adaptiveLearning.predictUserPerformance(e||Date.now())}getLearningState(){return this.adaptiveLearning.getLearningState()}getBehaviorPattern(){return this.adaptiveLearning.getBehaviorPattern()}resetLearningData(){this.adaptiveLearning.resetLearningData()}exportLearningData(){return this.adaptiveLearning.exportLearningData()}importLearningData(e){this.adaptiveLearning.importLearningData(e)}getEnvironmentalState(){return this.environmentalAnalyzer.getCurrentConditions()}getMultiFrameAnalysis(){return this.multiFrameAnalyzer.analyzeMultiFrame()}addFrameToMultiAnalysis(e){this.multiFrameAnalyzer.addFrame(e)}resetMultiFrameAnalysis(){this.multiFrameAnalyzer.reset()}getMultiFrameStats(){return this.multiFrameAnalyzer.getDetailedStats()}}const Rt=new jt;function _t(e,t,s){const a=e.x-t.x,i=e.y-t.y,n=s.x-t.x,r=s.y-t.y,o=a*n+i*r,c=Math.sqrt(a*a+i*i),l=Math.sqrt(n*n+r*r);return Math.acos(o/(c*l))}function It(e={}){var t,s,a,i,n,o;const{config:c={},videoElement:l,onPoseDetected:d,autoStart:h=!1,useEnhancedAnalysis:u=!1}=e,m={modelComplexity:1,smoothLandmarks:!0,enableSegmentation:!1,smoothSegmentation:!0,minDetectionConfidence:.7,minTrackingConfidence:.5,...c},[p,g]=r.useState({isActive:!1,isLoading:!1,error:null,currentPose:null,postureAnalysis:null,statistics:{totalDetections:0,goodPostureCount:0,averageFocusScore:0,sessionDuration:0}}),y=r.useRef(null),f=r.useRef(null),x=r.useRef(0),v=r.useRef(0),b=r.useCallback(async()=>{if(!y.current){g(e=>({...e,isLoading:!0,error:null}));try{const e=new vt.Pose({locateFile:e=>"https://cdn.jsdelivr.net/npm/@mediapipe/pose/".concat(e)});e.setOptions(m),e.onResults(e=>{if(!e.poseLandmarks)return;const t=function(e,t=!0){return t?function(e,t){return t||(t=new jt),t.analyze(e)}(e,Rt):function(e){if(!e||e.length<33)return{isSeated:!1,isFocused:!1,focusScore:0,headPosition:"center",shoulderBalance:0,bodyLean:0,warnings:["无法检测到有效的姿态数据"]};const t=function(e){var t,s,a,i;if(!e||e.length<33)return!1;const n=e[St.LEFT_HIP],r=e[St.RIGHT_HIP],o=e[St.LEFT_KNEE],c=e[St.RIGHT_KNEE],l=.5;if((null!=(t=n.visibility)?t:0)<l||(null!=(s=r.visibility)?s:0)<l||(null!=(a=o.visibility)?a:0)<l||(null!=(i=c.visibility)?i:0)<l)return!1;const d=(_t({...n,y:n.y-.1},n,o)+_t({...r,y:r.y-.1},r,c))/2*180/Math.PI;return d>60&&d<120}(e),s=function(e){var t,s,a;const i=e[St.NOSE],n=e[St.LEFT_SHOULDER],r=e[St.RIGHT_SHOULDER];if((null!=(t=i.visibility)?t:0)<.5||(null!=(s=n.visibility)?s:0)<.5||(null!=(a=r.visibility)?a:0)<.5)return"center";const o=(n.x+r.x)/2,c=(n.y+r.y)/2,l=(n.z+r.z)/2,d={x:i.x-o,y:i.y-c,z:i.z-l};return Math.abs(d.x)>.05?d.x>0?"right":"left":Math.abs(d.z)>.05?d.z>0?"back":"forward":"center"}(e),a=function(e){var t,s;const a=e[St.LEFT_SHOULDER],i=e[St.RIGHT_SHOULDER];if((null!=(t=a.visibility)?t:0)<.5||(null!=(s=i.visibility)?s:0)<.5)return 0;const n=i.y-a.y;return Math.max(-1,Math.min(1,10*n))}(e),i=function(e){var t,s,a,i;const n=e[St.LEFT_SHOULDER],r=e[St.RIGHT_SHOULDER],o=e[St.LEFT_HIP],c=e[St.RIGHT_HIP];if((null!=(t=n.visibility)?t:0)<.5||(null!=(s=r.visibility)?s:0)<.5||(null!=(a=o.visibility)?a:0)<.5||(null!=(i=c.visibility)?i:0)<.5)return 0;const l=(n.x+r.x)/2-(o.x+c.x)/2;return Math.max(-1,Math.min(1,5*l))}(e),n=function(e,t,s,a){let i=100;return a||(i-=30),"center"!==e&&(i-=20),i-=15*Math.abs(t),i-=20*Math.abs(s),Math.max(0,Math.min(100,i))}(s,a,i,t),r=function(e,t,s,a){const i=[];return a||i.push("请保持坐姿进行学习"),"forward"===e?i.push("头部过于前倾，请调整坐姿"):"left"!==e&&"right"!==e||i.push("头部倾斜，请保持头部居中"),Math.abs(t)>.3&&i.push("肩膀不平衡，请调整坐姿"),Math.abs(s)>.3&&i.push("身体倾斜过度，请坐直"),i}(s,a,i,t);return{isSeated:t,isFocused:n>70,focusScore:n,headPosition:s,shoulderBalance:a,bodyLean:i,warnings:r}}(e)}(e.poseLandmarks.map(e=>{var t;return{x:e.x,y:e.y,z:e.z,visibility:null!=(t=e.visibility)?t:1}}),u),s=function(e,t,s){const a=Date.now(),i=(e.averageFocusScore*e.totalDetections+t.focusScore)/(e.totalDetections+1);return{totalDetections:e.totalDetections+1,goodPostureCount:e.goodPostureCount+(t.isFocused?1:0),sessionDuration:a-s,averageFocusScore:i}}(p.statistics,t,x.current);g(a=>({...a,currentPose:e,postureAnalysis:t,statistics:s})),null==d||d(e,t)}),y.current=e,g(e=>({...e,isLoading:!1}))}catch(e){const t={message:e instanceof Error?e.message:"Failed to initialize pose detection",code:"INIT_ERROR",name:e instanceof Error?e.name:"PoseInitError"};g(e=>({...e,isLoading:!1,error:t}))}}},[m,d,p.statistics]),S=r.useCallback(async()=>{if(l){if(y.current||await b(),y.current)try{g(e=>({...e,isLoading:!0,error:null}));const e=new bt.Camera(l,{onFrame:async()=>{y.current&&l&&await y.current.send({image:l})},width:640,height:480});await e.start(),f.current=e,x.current=Date.now(),g(e=>({...e,isActive:!0,isLoading:!1,statistics:{totalDetections:0,goodPostureCount:0,averageFocusScore:0,sessionDuration:0}}))}catch(e){const t={message:e instanceof Error?e.message:"Failed to start pose detection",code:"START_ERROR",name:e instanceof Error?e.name:"PoseStartError"};g(e=>({...e,isLoading:!1,error:t}))}}else{const e={message:"Video element is required for pose detection",code:"NO_VIDEO_ELEMENT",name:"PoseDetectionError"};g(t=>({...t,error:e}))}},[l,b]),E=r.useCallback(()=>{f.current&&(f.current.stop(),f.current=null),v.current&&(cancelAnimationFrame(v.current),v.current=0),g(e=>({...e,isActive:!1,currentPose:null,postureAnalysis:null}))},[]),w=r.useCallback(()=>{x.current=Date.now(),g(e=>({...e,statistics:{totalDetections:0,goodPostureCount:0,averageFocusScore:0,sessionDuration:0}}))},[]),N=r.useCallback(e=>{y.current&&y.current.setOptions({...m,...e})},[m]),T=r.useCallback(async e=>{y.current||await b(),y.current&&await y.current.send({image:e})},[b]);return r.useEffect(()=>{h&&l&&!p.isActive&&S()},[h,l,p.isActive,S]),r.useEffect(()=>()=>{E(),y.current&&(y.current.close(),y.current=null)},[E]),{...p,startDetection:S,stopDetection:E,resetStatistics:w,updateConfig:N,detectPose:T,isGoodPosture:null!=(s=null==(t=p.postureAnalysis)?void 0:t.isFocused)&&s,postureScore:null!=(i=null==(a=p.postureAnalysis)?void 0:a.focusScore)?i:0,isDetecting:p.isActive&&!p.isLoading,hasWarnings:(null!=(o=null==(n=p.postureAnalysis)?void 0:n.warnings.length)?o:0)>0,sessionDuration:p.statistics.sessionDuration,goodPosturePercentage:p.statistics.totalDetections>0?p.statistics.goodPostureCount/p.statistics.totalDetections*100:0}}const kt=[[St.LEFT_EAR,St.LEFT_EYE_OUTER],[St.LEFT_EYE_OUTER,St.LEFT_EYE],[St.LEFT_EYE,St.LEFT_EYE_INNER],[St.LEFT_EYE_INNER,St.NOSE],[St.NOSE,St.RIGHT_EYE_INNER],[St.RIGHT_EYE_INNER,St.RIGHT_EYE],[St.RIGHT_EYE,St.RIGHT_EYE_OUTER],[St.RIGHT_EYE_OUTER,St.RIGHT_EAR],[St.MOUTH_LEFT,St.MOUTH_RIGHT],[St.LEFT_SHOULDER,St.RIGHT_SHOULDER],[St.LEFT_SHOULDER,St.LEFT_HIP],[St.RIGHT_SHOULDER,St.RIGHT_HIP],[St.LEFT_HIP,St.RIGHT_HIP],[St.LEFT_SHOULDER,St.LEFT_ELBOW],[St.LEFT_ELBOW,St.LEFT_WRIST],[St.LEFT_WRIST,St.LEFT_PINKY],[St.LEFT_WRIST,St.LEFT_INDEX],[St.LEFT_WRIST,St.LEFT_THUMB],[St.RIGHT_SHOULDER,St.RIGHT_ELBOW],[St.RIGHT_ELBOW,St.RIGHT_WRIST],[St.RIGHT_WRIST,St.RIGHT_PINKY],[St.RIGHT_WRIST,St.RIGHT_INDEX],[St.RIGHT_WRIST,St.RIGHT_THUMB],[St.LEFT_HIP,St.LEFT_KNEE],[St.LEFT_KNEE,St.LEFT_ANKLE],[St.LEFT_ANKLE,St.LEFT_HEEL],[St.LEFT_ANKLE,St.LEFT_FOOT_INDEX],[St.RIGHT_HIP,St.RIGHT_KNEE],[St.RIGHT_KNEE,St.RIGHT_ANKLE],[St.RIGHT_ANKLE,St.RIGHT_HEEL],[St.RIGHT_ANKLE,St.RIGHT_FOOT_INDEX]],At=({landmarks:e,postureAnalysis:t,width:s,className:a="",showKeypoints:i=!0,showConnections:n=!0,showConfidence:o=!1})=>{const c=r.useRef(null);return r.useEffect(()=>{const a=c.current;if(!a)return;const r=a.getContext("2d");r&&(r.clearRect(0,0,s,s),e&&0!==e.length&&(a.width=s,a.height=s,n&&function(e,t,s,a){const i=(null==a?void 0:a.isFocused)?"#10B981":"#EF4444";e.strokeStyle=i,e.lineWidth=2,e.lineCap="round",kt.forEach(([a,i])=>{var n,r;const o=t[a],c=t[i];if(!o||!c)return;const l=null!=(n=o.visibility)?n:1,d=null!=(r=c.visibility)?r:1;l>.5&&d>.5&&(e.globalAlpha=Math.min(l,d),e.beginPath(),e.moveTo(o.x*s,o.y*s),e.lineTo(c.x*s,c.y*s),e.stroke())}),e.globalAlpha=1}(r,e,s,t),i&&function(e,t,s,a,i){t.forEach((t,n)=>{var r;const o=null!=(r=t.visibility)?r:1;if(o<.5)return;const c=t.x*s,l=t.y*s,d=function(e){return[St.NOSE,St.LEFT_SHOULDER,St.RIGHT_SHOULDER,St.LEFT_HIP,St.RIGHT_HIP].includes(e)?6:4}(n),h=function(e,t){if(!t)return"#3B82F6";const s=[St.NOSE,St.LEFT_EAR,St.RIGHT_EAR],a=[St.LEFT_SHOULDER,St.RIGHT_SHOULDER];return s.includes(e)?"center"===t.headPosition?"#10B981":"#F59E0B":a.includes(e)?Math.abs(t.shoulderBalance)<.3?"#10B981":"#F59E0B":t.isFocused?"#10B981":"#EF4444"}(n,i);e.globalAlpha=o,e.fillStyle=h,e.beginPath(),e.arc(c,l,d,0,2*Math.PI),e.fill(),e.strokeStyle="#FFFFFF",e.lineWidth=1,e.stroke(),a&&o<1&&(e.fillStyle="#000000",e.font="10px Arial",e.fillText("".concat(Math.round(100*o),"%"),c+d+2,l-d))}),e.globalAlpha=1}(r,e,s,o,t),t&&function(e,t,s){const a=20,i=s-a-10;e.fillStyle=t.isFocused?"#10B981":"#EF4444",e.beginPath(),e.arc(i+10,20,10,0,2*Math.PI),e.fill(),e.strokeStyle="#FFFFFF",e.lineWidth=2,e.stroke(),e.fillStyle="#FFFFFF",e.font="bold 12px Arial",e.textAlign="center",e.textBaseline="middle";const n=t.isFocused?"✓":"!";e.fillText(n,i+10,20),e.textAlign="start",e.textBaseline="alphabetic"}(r,t,s)))},[e,t,s,i,n,o]),W.jsxs("div",{className:"pose-overlay ".concat(a),children:[W.jsx("canvas",{ref:c,width:s,height:s,className:"pose-canvas"}),t&&W.jsxs("div",{className:"pose-info",children:[W.jsxs("div",{className:"focus-score ".concat(t.isFocused?"good":"poor"),children:["专注度: ",Math.round(t.focusScore),"%"]}),t.warnings.length>0&&W.jsx("div",{className:"warnings",children:t.warnings.map((e,t)=>W.jsxs("div",{className:"warning-item",children:["⚠️ ",e]},t))})]})]})},Ct=({width:e=640,height:t=480,className:s="",onStatusChange:a,onPoseDetected:i,showControls:n=!0,enablePoseDetection:o=!0,showPoseOverlay:c=!0})=>{const{status:l,stream:d,error:h,isSupported:u,deviceId:m,videoRef:p,requestCamera:g,stopCamera:y,getDevices:f,switchDevice:x}=(()=>{const[e,t]=r.useState({status:"idle",stream:null,error:null,isSupported:!1}),s=r.useRef(null),a=r.useCallback(()=>{const e=!(!navigator.mediaDevices||!navigator.mediaDevices.getUserMedia);return t(t=>({...t,isSupported:e})),e},[]),i=r.useCallback(async e=>{if(a()){t(e=>({...e,status:"requesting",error:null}));try{const a={video:{width:{ideal:640},height:{ideal:480},facingMode:"user"},audio:!1},i=await navigator.mediaDevices.getUserMedia(e||a),n=i.getVideoTracks()[0],r=null==n?void 0:n.getSettings().deviceId;t(e=>({...e,status:"granted",stream:i,deviceId:r,error:null})),s.current&&(s.current.srcObject=i)}catch(h){let s;if(h instanceof Error)switch(h.name){case"NotAllowedError":case"PermissionDeniedError":s={code:"PERMISSION_DENIED",message:"摄像头权限被拒绝，请在浏览器设置中允许摄像头访问",name:h.name},t(e=>({...e,status:"denied",error:s}));break;case"NotFoundError":case"DevicesNotFoundError":s={code:"NO_CAMERA",message:"未找到可用的摄像头设备",name:h.name},t(e=>({...e,status:"unavailable",error:s}));break;case"NotReadableError":case"TrackStartError":s={code:"CAMERA_BUSY",message:"摄像头正被其他应用程序使用",name:h.name},t(e=>({...e,status:"error",error:s}));break;case"OverconstrainedError":case"ConstraintNotSatisfiedError":s={code:"CONSTRAINTS_ERROR",message:"摄像头不支持所请求的配置",name:h.name},t(e=>({...e,status:"error",error:s}));break;case"NotSupportedError":s={code:"NOT_SUPPORTED",message:"您的浏览器不支持摄像头功能",name:h.name},t(e=>({...e,status:"unavailable",error:s}));break;case"AbortError":s={code:"REQUEST_ABORTED",message:"摄像头请求被中断",name:h.name},t(e=>({...e,status:"error",error:s}));break;default:s={code:"UNKNOWN_ERROR",message:"摄像头访问失败: ".concat(h.message),name:h.name},t(e=>({...e,status:"error",error:s}))}else s={code:"UNKNOWN_ERROR",message:"摄像头访问失败，请重试",name:"UnknownError"},t(e=>({...e,status:"error",error:s}))}}else t(e=>({...e,status:"unavailable",error:{code:"NOT_SUPPORTED",message:"您的浏览器不支持摄像头功能",name:"NotSupportedError"}}))},[a]),n=r.useCallback(()=>{e.stream&&(e.stream.getTracks().forEach(e=>{e.stop()}),s.current&&(s.current.srcObject=null),t(e=>({...e,status:"idle",stream:null,error:null})))},[e.stream]),o=r.useCallback(async()=>{if(!navigator.permissions)return"prompt";try{return(await navigator.permissions.query({name:"camera"})).state}catch(h){return"prompt"}},[]),c=r.useCallback(async()=>{if(!navigator.mediaDevices||!navigator.mediaDevices.enumerateDevices)return[];try{return(await navigator.mediaDevices.enumerateDevices()).filter(e=>"videoinput"===e.kind)}catch(h){return[]}},[]),l=r.useCallback(async t=>{"granted"===e.status&&(n(),await i({video:{deviceId:{exact:t},width:{ideal:640},height:{ideal:480}},audio:!1}))},[e.status,n,i]);return r.useEffect(()=>()=>{e.stream&&e.stream.getTracks().forEach(e=>e.stop())},[e.stream]),r.useEffect(()=>{a()},[a]),{...e,videoRef:s,requestCamera:i,stopCamera:n,checkPermission:o,getDevices:c,switchDevice:l,checkSupport:a}})(),[v,b]=r.useState([]),[S,E]=r.useState(!1),[w,N]=r.useState({}),T=r.useCallback((e,t)=>{N({landmarks:e.poseLandmarks,analysis:t}),null==i||i(e,t)},[i]),{isActive:j,isLoading:R,error:_,startDetection:I,stopDetection:k,isGoodPosture:A,postureScore:C,sessionDuration:D,goodPosturePercentage:M}=It({videoElement:p.current,onPoseDetected:T,autoStart:!1});r.useEffect(()=>{a&&a("granted"===l&&j?"pose-active":l)},[l,j,a]);return W.jsx("div",{className:"camera-view ".concat(s),children:W.jsxs("div",{className:"camera-container",children:[W.jsxs("div",{className:"video-wrapper",style:{width:e,height:t},children:["granted"===l&&d?W.jsxs(W.Fragment,{children:[W.jsx("video",{ref:p,width:e,height:t,autoPlay:!0,muted:!0,playsInline:!0,className:"camera-video"}),c&&j&&W.jsx(At,{landmarks:w.landmarks,postureAnalysis:w.analysis,width:e,className:"video-pose-overlay"})]}):W.jsx("div",{className:"video-placeholder",children:W.jsx("div",{className:"placeholder-content",children:"requesting"===l?W.jsxs(W.Fragment,{children:[W.jsx("div",{className:"loading-spinner"}),W.jsx("p",{children:"正在启动摄像头..."})]}):"denied"===l||"error"===l?W.jsxs(W.Fragment,{children:[W.jsx("div",{className:"placeholder-icon",children:"📷"}),W.jsx("p",{children:"摄像头不可用"})]}):W.jsxs(W.Fragment,{children:[W.jsx("div",{className:"placeholder-icon",children:"📷"}),W.jsx("p",{children:"点击启动摄像头"}),o&&W.jsx("p",{className:"pose-hint",children:"🤸 将自动启用姿态检测"})]})})}),(S||R)&&"granted"===l&&W.jsxs("div",{className:"video-overlay",children:[W.jsx("div",{className:"loading-spinner"}),W.jsx("p",{children:S?"切换中...":"加载姿态检测..."})]})]}),(()=>{const e=[h,_].filter(Boolean);return 0===e.length?null:W.jsx("div",{className:"camera-error",children:e.map((e,t)=>W.jsxs("div",{className:"error-item",children:[W.jsx("div",{className:"error-icon",children:"⚠️"}),W.jsxs("div",{className:"error-content",children:[W.jsx("h4",{children:e===h?"摄像头访问失败":"姿态检测失败"}),W.jsx("p",{children:null==e?void 0:e.message}),e===h&&"PERMISSION_DENIED"===(null==h?void 0:h.code)&&W.jsxs("div",{className:"error-help",children:[W.jsx("p",{children:"解决方法："}),W.jsxs("ul",{children:[W.jsx("li",{children:"点击地址栏的摄像头图标"}),W.jsx("li",{children:'选择"始终允许"'}),W.jsx("li",{children:"刷新页面重试"})]})]}),e===h&&"NO_CAMERA"===(null==h?void 0:h.code)&&W.jsxs("div",{className:"error-help",children:[W.jsx("p",{children:"请确保："}),W.jsxs("ul",{children:[W.jsx("li",{children:"设备已连接摄像头"}),W.jsx("li",{children:"摄像头驱动程序正常"}),W.jsx("li",{children:"没有其他应用占用摄像头"})]})]})]})]},t))})})(),n?W.jsxs("div",{className:"camera-controls",children:[W.jsxs("div",{className:"control-row",children:["idle"===l||"denied"===l||"error"===l?W.jsx("button",{className:"control-btn primary",onClick:async()=>{E(!0),await g(),await(async()=>{const e=await f();b(e)})(),E(!1),o&&p.current&&setTimeout(()=>{I()},1e3)},disabled:!u||S,children:S?"启动中...":"🎥 启动摄像头"}):"granted"===l?W.jsxs(W.Fragment,{children:[W.jsx("button",{className:"control-btn secondary",onClick:()=>{j&&k(),y(),b([]),N({})},children:"⏹️ 停止摄像头"}),o&&W.jsx("button",{className:"control-btn ".concat(j?"primary":"secondary"),onClick:()=>{j?k():I()},disabled:R,children:R?"加载中...":j?"🤸 停止检测":"🚶 开始检测"})]}):null,!u&&W.jsx("div",{className:"unsupported-warning",children:"⚠️ 您的浏览器不支持摄像头功能"})]}),v.length>1&&"granted"===l&&W.jsxs("div",{className:"control-row",children:[W.jsx("label",{htmlFor:"device-select",children:"选择摄像头设备："}),W.jsx("select",{id:"device-select",value:m||"",onChange:e=>(async e=>{E(!0);const t=j;t&&k(),await x(e),E(!1),t&&o&&setTimeout(()=>{I()},1e3)})(e.target.value),disabled:S,children:v.map((e,t)=>W.jsx("option",{value:e.deviceId,children:e.label||"摄像头 ".concat(t+1)},e.deviceId))})]}),(()=>{const e={idle:{color:"#6B7280",text:"未启动",icon:"⚪"},requesting:{color:"#F59E0B",text:"请求中...",icon:"🟡"},granted:{color:"#10B981",text:"已连接",icon:"🟢"},denied:{color:"#EF4444",text:"权限被拒绝",icon:"🔴"},unavailable:{color:"#EF4444",text:"不可用",icon:"❌"},error:{color:"#EF4444",text:"错误",icon:"⚠️"}},t=e[l]||e.idle;return W.jsxs("div",{className:"camera-status",style:{color:t.color},children:[W.jsx("span",{className:"status-icon",children:t.icon}),W.jsx("span",{className:"status-text",children:t.text}),"granted"===l&&W.jsx("div",{className:"pose-status",children:W.jsx("span",{className:"pose-indicator ".concat(j?"active":"inactive"),title:j?"姿态检测已启用":"姿态检测已停用",children:j?"🤸":"🚶"})})]})})(),j&&w.analysis?W.jsxs("div",{className:"pose-stats",children:[W.jsxs("div",{className:"stat-row",children:[W.jsx("span",{className:"stat-label",children:"专注度:"}),W.jsxs("span",{className:"stat-value ".concat(A?"good":"poor"),children:[Math.round(C),"%"]})]}),W.jsxs("div",{className:"stat-row",children:[W.jsx("span",{className:"stat-label",children:"良好姿态率:"}),W.jsxs("span",{className:"stat-value",children:[Math.round(M),"%"]})]}),W.jsxs("div",{className:"stat-row",children:[W.jsx("span",{className:"stat-label",children:"检测时长:"}),W.jsxs("span",{className:"stat-value",children:[Math.floor(D/1e3),"秒"]})]})]}):null]}):null]})})},Dt=class e{constructor(){t(this,"state"),t(this,"listeners",new Map),t(this,"syncInterval",null),t(this,"websocket",null),t(this,"deviceInfo"),this.deviceInfo=this.generateDeviceInfo(),this.state={isConnected:!1,lastSyncTime:0,deviceId:this.deviceInfo.id,deviceType:this.deviceInfo.type,connectionMethod:"offline",syncQueue:[],conflictResolution:"latest"},this.initializeSync()}static getInstance(){return e.instance||(e.instance=new e),e.instance}generateDeviceInfo(){const e=/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);let t,s=[];return window.electronAPI?(t="desktop",s=["monitoring","notifications","file-system","system-integration"]):e?(t="mobile",s=["notifications","vibration","orientation","touch"]):(t="web",s=["notifications","storage"]),"serviceWorker"in navigator&&s.push("pwa"),"undefined"!=typeof WebSocket&&s.push("websocket"),{id:this.getOrCreateDeviceId(),type:t,platform:navigator.platform,userAgent:navigator.userAgent,lastSeen:Date.now(),isOnline:navigator.onLine,capabilities:s}}getOrCreateDeviceId(){let e=localStorage.getItem("deviceId");return e||(e="device_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9)),localStorage.setItem("deviceId",e)),e}async initializeSync(){try{if(await this.tryElectronSync())return this.state.connectionMethod="electron",this.state.isConnected=!0,void this.emit("connection-established",{method:"electron"});if(await this.tryWebSocketSync())return this.state.connectionMethod="websocket",this.state.isConnected=!0,void this.emit("connection-established",{method:"websocket"});if(await this.tryHttpSync())return this.state.connectionMethod="http",this.state.isConnected=!0,void this.emit("connection-established",{method:"http"});this.state.connectionMethod="offline",this.state.isConnected=!1,this.emit("connection-failed",{reason:"no-available-methods"})}catch(e){this.state.isConnected=!1,this.emit("sync-error",{error:e})}this.startPeriodicSync()}async tryElectronSync(){var e,t;try{if(!window.electronAPI)return!1;const s=window.electronAPI;return null==(e=s.onMonitoringUpdate)||e.call(s,e=>{this.handleRemoteUpdate({timestamp:Date.now(),type:"monitoring",action:"status-update",payload:e,deviceId:"desktop-main",deviceType:"desktop"})}),null==(t=s.onViolationDetected)||t.call(s,e=>{this.handleRemoteUpdate({timestamp:Date.now(),type:"monitoring",action:"violation-detected",payload:e,deviceId:"desktop-main",deviceType:"desktop"})}),!0}catch(s){return!1}}async tryWebSocketSync(){return new Promise(e=>{try{const t="ws://localhost:8080/sync";this.websocket=new WebSocket(t);const s=setTimeout(()=>{var t;null==(t=this.websocket)||t.close(),e(!1)},3e3);this.websocket.onopen=()=>{clearTimeout(s),this.setupWebSocketHandlers(),e(!0)},this.websocket.onerror=()=>{clearTimeout(s),e(!1)}}catch(t){e(!1)}})}setupWebSocketHandlers(){this.websocket&&(this.websocket.onmessage=e=>{try{const t=JSON.parse(e.data);this.handleRemoteUpdate(t)}catch(t){}},this.websocket.onclose=()=>{this.state.isConnected=!1,this.emit("connection-lost",{method:"websocket"}),setTimeout(()=>this.tryWebSocketSync(),5e3)},this.websocket.onerror=e=>{this.emit("sync-error",{error:e})},this.sendWebSocketMessage({timestamp:Date.now(),type:"settings",action:"device-register",payload:this.deviceInfo,deviceId:this.state.deviceId,deviceType:this.state.deviceType}))}sendWebSocketMessage(e){var t;(null==(t=this.websocket)?void 0:t.readyState)===WebSocket.OPEN&&this.websocket.send(JSON.stringify(e))}async tryHttpSync(){try{return(await fetch("/api/sync/ping",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({deviceId:this.state.deviceId,deviceInfo:this.deviceInfo})})).ok}catch(e){return!1}}startPeriodicSync(){this.syncInterval&&clearInterval(this.syncInterval),this.syncInterval=setInterval(()=>{this.performPeriodicSync()},3e4)}async performPeriodicSync(){try{await this.processSyncQueue(),this.deviceInfo.lastSeen=Date.now(),this.deviceInfo.isOnline=navigator.onLine,this.state.isConnected||await this.initializeSync()}catch(e){}}async processSyncQueue(){const e=[...this.state.syncQueue];this.state.syncQueue=[];for(const s of e)try{await this.sendSyncData(s)}catch(t){this.state.syncQueue.push(s)}}async sendSyncData(e){switch(this.state.connectionMethod){case"electron":await this.sendElectronSync(e);break;case"websocket":this.sendWebSocketMessage(e);break;case"http":await this.sendHttpSync(e);break;default:this.state.syncQueue.push(e)}}async sendElectronSync(e){}async sendHttpSync(e){const t=await fetch("/api/sync",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok)throw new Error("HTTP同步失败: ".concat(t.status))}handleRemoteUpdate(e){e.deviceId!==this.state.deviceId&&(this.hasConflict(e)?this.resolveConflict(e):(this.applyUpdate(e),this.state.lastSyncTime=Date.now(),this.emit("data-updated",e)))}hasConflict(e){return Math.abs(e.timestamp-Date.now())>6e4}resolveConflict(e){switch(this.state.conflictResolution){case"latest":e.timestamp>this.state.lastSyncTime&&this.applyUpdate(e);break;case"desktop-priority":"desktop"===e.deviceType&&this.applyUpdate(e);break;case"manual":this.emit("conflict-detected",e)}}applyUpdate(e){switch(e.type){case"monitoring":this.applyMonitoringUpdate(e);break;case"focus":this.applyFocusUpdate(e);break;case"reward":this.applyRewardUpdate(e);break;case"settings":this.applySettingsUpdate(e)}}applyMonitoringUpdate(e){switch(e.action){case"status-update":localStorage.setItem("sync-monitoring-status",JSON.stringify(e.payload));break;case"violation-detected":this.emit("violation-synced",e.payload);break;case"whitelist-updated":localStorage.setItem("sync-whitelist",JSON.stringify(e.payload))}}applyFocusUpdate(e){switch(e.action){case"session-start":case"session-end":case"session-pause":localStorage.setItem("sync-focus-session",JSON.stringify(e.payload)),this.emit("focus-session-synced",e.payload)}}applyRewardUpdate(e){switch(e.action){case"rewards-blocked":case"rewards-unblocked":localStorage.setItem("sync-reward-status",JSON.stringify(e.payload)),this.emit("reward-status-synced",e.payload)}}applySettingsUpdate(e){if("settings-changed"===e.action){const t={...JSON.parse(localStorage.getItem("focusSettings")||"{}"),...e.payload};localStorage.setItem("focusSettings",JSON.stringify(t)),this.emit("settings-synced",t)}}async sync(e,t,s){const a={timestamp:Date.now(),type:e,action:t,payload:s,deviceId:this.state.deviceId,deviceType:this.state.deviceType};if(this.state.isConnected)try{await this.sendSyncData(a)}catch(i){this.state.syncQueue.push(a)}else this.state.syncQueue.push(a)}getSyncState(){return{...this.state}}getDeviceInfo(){return{...this.deviceInfo}}async forceSync(){await this.initializeSync(),await this.processSyncQueue()}on(e,t){this.listeners.has(e)||this.listeners.set(e,[]),this.listeners.get(e).push(t)}off(e,t){const s=this.listeners.get(e);if(s){const e=s.indexOf(t);e>-1&&s.splice(e,1)}}emit(e,t){const s=this.listeners.get(e);s&&s.forEach(e=>{try{e(t)}catch(s){}})}destroy(){this.syncInterval&&(clearInterval(this.syncInterval),this.syncInterval=null),this.websocket&&(this.websocket.close(),this.websocket=null),this.listeners.clear(),e.instance=null}};t(Dt,"instance",null);let Mt=Dt;const Ot=({showDetails:e=!1,onSyncAction:t})=>{const[s,a]=r.useState(null),[i,n]=r.useState(null),[o,c]=r.useState(!1),[l,d]=r.useState([]),[h,u]=r.useState(!1);r.useEffect(()=>{const e=Mt.getInstance();a(e.getSyncState()),n(e.getDeviceInfo());const s=t=>{a(e.getSyncState()),m("连接建立","通过 ".concat(t.method," 建立连接"),"success")},i=t=>{a(e.getSyncState()),m("连接丢失","".concat(t.method," 连接断开"),"warning")},r=t=>{a(e.getSyncState()),m("数据更新","收到 ".concat(t.type," 类型的更新"),"info")},o=e=>{var t;m("同步错误",(null==(t=e.error)?void 0:t.message)||"未知错误","error")},c=e=>{m("冲突检测","检测到 ".concat(e.type," 数据冲突"),"warning"),null==t||t("conflict-detected")};e.on("connection-established",s),e.on("connection-lost",i),e.on("data-updated",r),e.on("sync-error",o),e.on("conflict-detected",c);const l=setInterval(()=>{a(e.getSyncState()),n(e.getDeviceInfo())},5e3);return()=>{clearInterval(l),e.off("connection-established",s),e.off("connection-lost",i),e.off("data-updated",r),e.off("sync-error",o),e.off("conflict-detected",c)}},[t]);const m=(e,t,s)=>{const a={id:Date.now(),timestamp:(new Date).toLocaleTimeString(),title:e,message:t,type:s};d(e=>[a,...e.slice(0,9)])},p=()=>{if(!(null==s?void 0:s.isConnected))return"🔴";switch(s.connectionMethod){case"electron":return"🖥️";case"websocket":return"🌐";case"http":return"📡";default:return"📱"}},g=()=>s?s.isConnected?{electron:"Electron同步",websocket:"WebSocket实时",http:"HTTP轮询",offline:"离线模式"}[s.connectionMethod]||"未知连接":"离线模式":"初始化中...";return e?W.jsxs("div",{className:"sync-status-panel",children:[W.jsxs("div",{className:"sync-header",onClick:()=>c(!o),children:[W.jsxs("div",{className:"sync-main-info",children:[W.jsx("span",{className:"sync-icon",children:p()}),W.jsxs("div",{className:"sync-details",children:[W.jsx("div",{className:"sync-status-text",children:g()}),W.jsxs("div",{className:"sync-meta",children:["最后同步: ",(()=>{if(!(null==s?void 0:s.lastSyncTime))return"从未同步";const e=Date.now()-s.lastSyncTime,t=Math.floor(e/6e4),a=Math.floor(e%6e4/1e3);return t>0?"".concat(t,"分钟前"):"".concat(a,"秒前")})(),(null==s?void 0:s.syncQueue)&&s.syncQueue.length>0&&W.jsxs("span",{className:"queue-info",children:[" | 队列: ",s.syncQueue.length]})]})]})]}),W.jsx("button",{className:"sync-action-btn ".concat(h?"syncing":""),onClick:e=>{e.stopPropagation(),(async()=>{if(!h){u(!0);try{const e=Mt.getInstance();await e.forceSync(),m("手动同步","强制同步完成","success"),null==t||t("force-sync-completed")}catch(e){m("同步失败","强制同步失败","error")}finally{u(!1)}}})()},disabled:h,children:h?"同步中...":"强制同步"})]}),o&&W.jsxs("div",{className:"sync-expanded-content",children:[W.jsxs("div",{className:"sync-section",children:[W.jsx("h4",{children:"设备信息"}),W.jsxs("div",{className:"device-info",children:[W.jsxs("div",{className:"device-item",children:[W.jsx("span",{className:"device-label",children:"设备ID:"}),W.jsx("span",{className:"device-value",children:null==i?void 0:i.id.slice(-8)})]}),W.jsxs("div",{className:"device-item",children:[W.jsx("span",{className:"device-label",children:"设备类型:"}),W.jsx("span",{className:"device-value",children:null==i?void 0:i.type})]}),W.jsxs("div",{className:"device-item",children:[W.jsx("span",{className:"device-label",children:"平台:"}),W.jsx("span",{className:"device-value",children:null==i?void 0:i.platform})]}),W.jsxs("div",{className:"device-item",children:[W.jsx("span",{className:"device-label",children:"在线状态:"}),W.jsx("span",{className:"device-value ".concat((null==i?void 0:i.isOnline)?"online":"offline"),children:(null==i?void 0:i.isOnline)?"在线":"离线"})]})]})]}),W.jsxs("div",{className:"sync-section",children:[W.jsx("h4",{children:"支持功能"}),W.jsx("div",{className:"capabilities",children:null==i?void 0:i.capabilities.map(e=>W.jsx("span",{className:"capability-tag",children:e},e))})]}),W.jsxs("div",{className:"sync-section",children:[W.jsx("h4",{children:"同步配置"}),W.jsx("div",{className:"sync-config",children:W.jsxs("div",{className:"config-item",children:[W.jsx("span",{className:"config-label",children:"冲突解决:"}),W.jsxs("select",{value:(null==s?void 0:s.conflictResolution)||"latest",onChange:e=>{null==t||t("set-conflict-resolution-".concat(e.target.value))},children:[W.jsx("option",{value:"latest",children:"最新优先"}),W.jsx("option",{value:"desktop-priority",children:"桌面端优先"}),W.jsx("option",{value:"manual",children:"手动解决"})]})]})})]}),W.jsxs("div",{className:"sync-section",children:[W.jsx("h4",{children:"同步历史"}),W.jsx("div",{className:"sync-history",children:0===l.length?W.jsx("div",{className:"no-history",children:"暂无同步记录"}):l.map(e=>W.jsxs("div",{className:"history-item ".concat(e.type),children:[W.jsx("div",{className:"history-time",children:e.timestamp}),W.jsxs("div",{className:"history-content",children:[W.jsx("div",{className:"history-title",children:e.title}),W.jsx("div",{className:"history-message",children:e.message})]})]},e.id))})]}),!1]})]}):W.jsxs("div",{className:"sync-status-simple",children:[W.jsx("span",{className:"sync-icon",children:p()}),W.jsx("span",{className:"sync-text",children:g()}),(null==s?void 0:s.syncQueue)&&s.syncQueue.length>0&&W.jsxs("span",{className:"sync-queue",children:["(",s.syncQueue.length,")"]})]})},Lt=()=>{var e;const[t,s]=r.useState(null),[a,i]=r.useState([]),[n,p]=r.useState([]),[g,y]=r.useState(null),[f,x]=r.useState(null),[v,b]=r.useState(""),[S,E]=r.useState(!0),[w,N]=r.useState(null),[T]=r.useState(()=>Mt.getInstance()),j="undefined"!=typeof window&&window.electronAPI;r.useEffect(()=>j?(R(),window.electronAPI.onMonitoringUpdate(_),window.electronAPI.onViolationDetected(I),()=>{window.electronAPI.removeAllListeners("monitoring-update"),window.electronAPI.removeAllListeners("violation-detected")}):(N("此功能仅在Electron桌面应用中可用"),void E(!1)),[j]);const R=async()=>{try{E(!0);const e=await window.electronAPI.getMonitoringStatus();s(e);const t=await window.electronAPI.getWhitelist();t.success&&s(e=>e?{...e,whitelistApps:t.data}:null);const a=await window.electronAPI.getInstalledApps();i(a);const n=await window.electronAPI.getAppHistory(1);p(n)}catch(e){N("初始化失败: ".concat(e))}finally{E(!1)}},_=e=>{y(e),e.isWhitelisted&&f&&x(null),T.sync("monitoring","status-update",{activeApp:e.activeApp,isWhitelisted:e.isWhitelisted,timestamp:Date.now()})},I=e=>{x(e),T.sync("monitoring","violation-detected",{activeApp:e.activeApp,violationTime:e.violationTime,timestamp:Date.now()})},k=async()=>{if(v.trim())try{const e=[...(null==t?void 0:t.whitelistApps)||[],v.trim()],a=await window.electronAPI.setWhitelist(e);a.success?(s(t=>t?{...t,whitelistApps:e}:null),b(""),T.sync("monitoring","whitelist-updated",{whitelist:e,action:"add",app:v.trim(),timestamp:Date.now()})):N(a.message)}catch(e){N("添加白名单失败: ".concat(e))}},A=e=>{const t=Math.floor(e/1e3),s=Math.floor(t/60),a=Math.floor(s/60);return a>0?"".concat(a,"小时").concat(s%60,"分钟"):s>0?"".concat(s,"分钟").concat(t%60,"秒"):"".concat(t,"秒")};return S?W.jsx("div",{className:"application-monitor",children:W.jsxs("div",{className:"loading-state",children:[W.jsx("div",{className:"loading-spinner"}),W.jsx("span",{className:"loading-text",children:"加载中..."})]})}):j?w?W.jsx("div",{className:"application-monitor",children:W.jsxs("div",{className:"error-state",children:[W.jsxs("div",{className:"error-header",children:[W.jsx(o,{className:"h-6 w-6"}),W.jsx("h3",{className:"error-title",children:"错误"})]}),W.jsx("p",{className:"error-message",children:w}),W.jsx("button",{onClick:()=>{N(null),R()},className:"retry-button",children:"重试"})]})}):W.jsxs("div",{className:"application-monitor",children:[W.jsx("div",{className:"monitor-header",children:W.jsxs("div",{className:"monitor-title",children:[W.jsxs("div",{children:[W.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"12px"},children:[W.jsx(c,{className:"h-8 w-8"}),W.jsx("h1",{children:"应用监控系统"})]}),W.jsx("p",{children:"管理应用白名单和监控专注状态"})]}),W.jsxs("div",{className:"status-indicator",children:[W.jsx(Ot,{showDetails:!1}),W.jsx("div",{className:"status-badge ".concat((null==t?void 0:t.isMonitoring)?"monitoring":"stopped"),children:(null==t?void 0:t.isMonitoring)?"监控中":"已停止"}),(null==t?void 0:t.isMonitoring)?W.jsxs("button",{onClick:async()=>{try{const e=await window.electronAPI.stopMonitoring();e.success?(s(e=>e?{...e,isMonitoring:!1}:null),y(null),x(null)):N(e.message)}catch(e){N("停止监控失败: ".concat(e))}},className:"control-button stop",children:[W.jsx(l,{className:"h-4 w-4"}),"停止监控"]}):W.jsxs("button",{onClick:async()=>{try{const e=await window.electronAPI.startMonitoring();e.success?s(e=>e?{...e,isMonitoring:!0}:null):N(e.message)}catch(e){N("启动监控失败: ".concat(e))}},className:"control-button start",children:[W.jsx(d,{className:"h-4 w-4"}),"开始监控"]})]})]})}),W.jsxs("div",{className:"status-grid",children:[W.jsxs("div",{className:"status-card current-app-card",children:[W.jsx("h3",{children:"当前应用"}),W.jsx("p",{children:(null==g?void 0:g.activeApp)||(null==(e=null==t?void 0:t.currentApp)?void 0:e.name)||"无"}),g&&W.jsx("div",{className:"app-status-badge ".concat(g.isWhitelisted?"authorized":"unauthorized"),children:g.isWhitelisted?"已允许":"未授权"})]}),W.jsxs("div",{className:"status-card whitelist-card",children:[W.jsx("h3",{children:"白名单应用"}),W.jsxs("p",{children:[(null==t?void 0:t.whitelistApps.length)||0," 个应用"]})]}),W.jsxs("div",{className:"status-card violation-card ".concat(f?"active":""),children:[W.jsx("h3",{children:"违规状态"}),f?W.jsxs("div",{children:[W.jsx("p",{style:{fontWeight:600},children:"检测到违规"}),W.jsxs("p",{style:{fontSize:"14px",opacity:.8},children:["持续时间: ",A(f.violationTime)]})]}):W.jsx("p",{children:"正常"})]})]}),f&&W.jsxs("div",{className:"violation-alert",children:[W.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"8px"},children:[W.jsx(o,{className:"h-5 w-5"}),W.jsx("h4",{children:"专注提醒"})]}),W.jsxs("p",{children:['检测到您正在使用非工作应用 "',f.activeApp,'"， 已持续 ',A(f.violationTime),"。请回到工作状态以继续获得奖励。"]}),g&&!g.isWhitelisted&&W.jsx("button",{onClick:async()=>{if(!(null==g?void 0:g.activeApp))return;const e=(null==t?void 0:t.whitelistApps)||[];if(!e.includes(g.activeApp))try{const t=[...e,g.activeApp],a=await window.electronAPI.setWhitelist(t);a.success?s(e=>e?{...e,whitelistApps:t}:null):N(a.message)}catch(a){N("添加当前应用到白名单失败: ".concat(a))}},className:"violation-button",children:"将当前应用添加到白名单"})]}),W.jsxs("div",{className:"whitelist-section",children:[W.jsxs("div",{className:"whitelist-header",children:[W.jsx(h,{className:"h-6 w-6 text-green-600"}),W.jsx("h2",{children:"白名单管理"})]}),W.jsxs("div",{className:"add-app-form",children:[W.jsx("input",{type:"text",value:v,onChange:e=>b(e.target.value),placeholder:"输入应用名称...",className:"app-input",onKeyPress:e=>"Enter"===e.key&&k()}),W.jsxs("button",{onClick:k,disabled:!v.trim(),className:"add-button",children:[W.jsx(u,{className:"h-4 w-4"}),"添加"]})]}),W.jsxs("div",{children:[null==t?void 0:t.whitelistApps.map((e,a)=>W.jsxs("div",{className:"whitelist-item",children:[W.jsx("span",{children:e}),W.jsxs("button",{onClick:()=>(async e=>{try{const a=((null==t?void 0:t.whitelistApps)||[]).filter(t=>t!==e),i=await window.electronAPI.setWhitelist(a);i.success?s(e=>e?{...e,whitelistApps:a}:null):N(i.message)}catch(a){N("移除白名单失败: ".concat(a))}})(e),className:"remove-button",children:[W.jsx(m,{className:"h-4 w-4"}),"移除"]})]},a)),(!(null==t?void 0:t.whitelistApps)||0===t.whitelistApps.length)&&W.jsxs("div",{className:"empty-state",children:[W.jsx(h,{}),W.jsx("p",{children:"暂无白名单应用"}),W.jsx("p",{children:"添加应用到白名单以允许在专注时间使用"})]})]})]}),W.jsxs("div",{className:"history-section",children:[W.jsx("div",{className:"history-header",children:W.jsx("h2",{children:"最近使用的应用"})}),W.jsxs("div",{className:"history-list",children:[n.map((e,t)=>W.jsxs("div",{className:"history-item",children:[W.jsxs("div",{children:[W.jsx("div",{style:{fontWeight:500,color:"#374151"},children:e.name}),W.jsx("div",{style:{fontSize:"14px",color:"#6b7280"},children:new Date(e.timestamp).toLocaleString()})]}),W.jsx("div",{style:{fontSize:"14px",color:"#6b7280"},children:e.windowTitle})]},t)),0===n.length&&W.jsxs("div",{className:"empty-state",children:[W.jsx(c,{}),W.jsx("p",{children:"暂无应用历史记录"}),W.jsx("p",{children:"开始监控后将显示应用使用历史"})]})]})]})]}):W.jsx("div",{className:"application-monitor",children:W.jsxs("div",{className:"error-state",children:[W.jsxs("div",{className:"error-header",children:[W.jsx(o,{className:"h-6 w-6 text-yellow-600"}),W.jsx("h3",{className:"error-title",children:"桌面功能不可用"})]}),W.jsx("p",{className:"error-message",children:"应用监控功能仅在Electron桌面应用中可用。请下载并使用桌面版本。"})]})})},Ft=()=>{const[e,t]=r.useState(null),[s,a]=r.useState({pomodoroLength:25,shortBreakLength:5,longBreakLength:15,longBreakInterval:4,autoStartBreaks:!1,autoStartPomodoros:!1,soundEnabled:!0,vibrationEnabled:!0,strictMode:!1,allowedApps:["自律农场","Clock","Timer"]}),[i,n]=r.useState(0),[o,h]=r.useState(0),[u,m]=r.useState({isConnected:!1,lastSyncTime:0}),[x,v]=r.useState(!1),[b,S]=r.useState(!1),E=r.useRef(null),[w]=r.useState(()=>Mt.getInstance()),N=/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent),T=window.matchMedia&&window.matchMedia("(display-mode: standalone)").matches;r.useEffect(()=>{O(),R(),P(),document.addEventListener("visibilitychange",j);const e=setInterval(R,3e4);return()=>{document.removeEventListener("visibilitychange",j),clearInterval(e),E.current&&clearInterval(E.current)}},[]),r.useEffect(()=>((null==e?void 0:e.isActive)&&i>0?E.current=setInterval(()=>{n(e=>e<=1?(k(),0):e-1)},1e3):E.current&&(clearInterval(E.current),E.current=null),()=>{E.current&&clearInterval(E.current)}),[null==e?void 0:e.isActive,i]);const j=()=>{document.hidden&&(null==e?void 0:e.isActive)&&s.strictMode&&A()},R=async()=>{var t;try{if(window.electronAPI){const s=await window.electronAPI.getMonitoringStatus();m({isConnected:!0,lastSyncTime:Date.now(),desktopStatus:{isMonitoring:s.isMonitoring,currentApp:(null==(t=s.currentApp)?void 0:t.name)||"Unknown",isViolating:null!==s.violationStartTime,focusSessionActive:(null==e?void 0:e.isActive)||!1}})}else m(e=>({...e,isConnected:!1}))}catch(s){m(e=>({...e,isConnected:!1}))}},_=(e,a)=>{let i;switch(e){case"pomodoro":i=s.pomodoroLength;break;case"short-break":i=s.shortBreakLength;break;case"long-break":i=s.longBreakLength;break;default:i=25}const r={id:B(),startTime:Date.now(),duration:i,isActive:!0,type:e,interruptions:0,goals:[],achievements:[]};t(r),n(60*i),w.sync("focus","session-start",{sessionId:r.id,type:r.type,duration:i,startTime:r.startTime,timestamp:Date.now()}),F("".concat(G(e),"开始"),"时长：".concat(i,"分钟")),s.vibrationEnabled&&navigator.vibrate&&navigator.vibrate([200,100,200])},I=()=>{if(!e)return;const s={...e,endTime:Date.now(),actualDuration:Date.now()-e.startTime,isActive:!1};t(null),n(0),M(s),F("专注结束","会话已结束")},k=()=>{if(!e)return;const a={...e,endTime:Date.now(),actualDuration:60*e.duration*1e3,isActive:!1};"pomodoro"===e.type&&h(e=>e+1),M(a),t(null),w.sync("focus","session-end",{sessionId:a.id,type:a.type,duration:a.duration,actualDuration:a.actualDuration,interruptions:a.interruptions,endTime:a.endTime,timestamp:Date.now()});const i=C();F("".concat(G(e.type),"完成！"),i?"下一个：".concat(G(i)):"休息一下吧"),s.vibrationEnabled&&navigator.vibrate&&navigator.vibrate([300,200,300,200,300]),i&&D(i)&&setTimeout(()=>{_(i)},3e3)},A=a=>{if(!(null==e?void 0:e.isActive))return;const i={...e,interruptions:e.interruptions+1};t(i),s.strictMode?(I(),F("严格模式","检测到违规，会话已结束")):F("检测到中断","请尽快回到专注状态")},C=()=>e?"pomodoro"===e.type?(o+1)%s.longBreakInterval===0?"long-break":"short-break":"pomodoro":null,D=e=>"pomodoro"===e?s.autoStartPomodoros:s.autoStartBreaks,M=e=>{const t=JSON.parse(localStorage.getItem("focusRecords")||"[]");t.push(e),t.length>100&&t.splice(0,t.length-100),localStorage.setItem("focusRecords",JSON.stringify(t))},O=()=>{const e=localStorage.getItem("focusSettings");if(e)try{a(JSON.parse(e))}catch(t){}},L=e=>{a(e),localStorage.setItem("focusSettings",JSON.stringify(e))},F=(e,t)=>{"Notification"in window&&"granted"===Notification.permission&&new Notification(e,{body:t,icon:"/icon-192x192.png",tag:"focus-mode"})},P=async()=>{"Notification"in window&&"default"===Notification.permission&&await Notification.requestPermission()},G=e=>({pomodoro:"专注时间","short-break":"短休息","long-break":"长休息",custom:"自定义"}[e]),B=()=>"session_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9)),U=e?(60*e.duration-i)/(60*e.duration)*100:0;return W.jsxs("div",{className:"focus-mode ".concat(b?"fullscreen":""),children:[W.jsxs("div",{className:"focus-header",children:[W.jsx("div",{className:"sync-status",children:W.jsx(Ot,{showDetails:!1,onSyncAction:e=>{"force-sync-completed"===e&&R()}})}),W.jsxs("div",{className:"device-indicator",children:[N?W.jsx(p,{size:16}):W.jsx(c,{size:16}),W.jsx("span",{children:T?"PWA模式":"浏览器模式"})]}),u.desktopStatus&&W.jsx("div",{className:"desktop-status",children:W.jsxs("span",{className:"status-indicator ".concat(u.desktopStatus.isViolating?"warning":"normal"),children:["桌面端: ",u.desktopStatus.currentApp]})})]}),W.jsx("div",{className:"timer-container",children:e?W.jsxs(W.Fragment,{children:[W.jsxs("div",{className:"session-type",children:[W.jsx(g,{size:24}),W.jsx("h2",{children:G(e.type)})]}),W.jsxs("div",{className:"timer-display",children:[W.jsx("div",{className:"time-text",children:(e=>{const t=Math.floor(e/60),s=e%60;return"".concat(t.toString().padStart(2,"0"),":").concat(s.toString().padStart(2,"0"))})(i)}),W.jsx("div",{className:"progress-ring",children:W.jsxs("svg",{width:"200",height:"200",viewBox:"0 0 200 200",children:[W.jsx("circle",{cx:"100",cy:"100",r:"90",fill:"none",stroke:"#e2e8f0",strokeWidth:"8"}),W.jsx("circle",{cx:"100",cy:"100",r:"90",fill:"none",stroke:"#3b82f6",strokeWidth:"8",strokeLinecap:"round",strokeDasharray:"".concat(2*Math.PI*90),strokeDashoffset:"".concat(2*Math.PI*90*(1-U/100)),transform:"rotate(-90 100 100)",className:"progress-circle"})]})})]}),W.jsxs("div",{className:"session-controls",children:[W.jsxs("button",{className:"control-btn ".concat(e.isActive?"pause":"play"),onClick:()=>{if(!e)return;const s={...e,isActive:!e.isActive};t(s),w.sync("focus","session-pause",{sessionId:s.id,isActive:s.isActive,remainingTime:i,timestamp:Date.now()}),s.isActive?F("专注继续","继续你的专注时光"):F("专注暂停","休息一下再继续")},children:[e.isActive?W.jsx(y,{size:24}):W.jsx(d,{size:24}),W.jsx("span",{children:e.isActive?"暂停":"继续"})]}),W.jsxs("button",{className:"control-btn stop",onClick:I,children:[W.jsx(l,{size:24}),W.jsx("span",{children:"停止"})]})]}),W.jsxs("div",{className:"session-info",children:[W.jsx("div",{className:"info-item",children:W.jsxs("span",{children:["番茄数：",o]})}),W.jsx("div",{className:"info-item",children:W.jsxs("span",{children:["中断次数：",e.interruptions]})})]})]}):W.jsxs("div",{className:"start-session",children:[W.jsx("h2",{children:"开始专注"}),W.jsxs("div",{className:"session-options",children:[W.jsxs("button",{className:"session-btn pomodoro",onClick:()=>_("pomodoro"),children:[W.jsx(g,{size:20}),W.jsx("span",{children:"番茄时钟"}),W.jsxs("small",{children:[s.pomodoroLength,"分钟"]})]}),W.jsxs("button",{className:"session-btn short-break",onClick:()=>_("short-break"),children:[W.jsx(g,{size:20}),W.jsx("span",{children:"短休息"}),W.jsxs("small",{children:[s.shortBreakLength,"分钟"]})]}),W.jsxs("button",{className:"session-btn long-break",onClick:()=>_("long-break"),children:[W.jsx(g,{size:20}),W.jsx("span",{children:"长休息"}),W.jsxs("small",{children:[s.longBreakLength,"分钟"]})]})]})]})}),W.jsxs("div",{className:"focus-footer",children:[W.jsxs("button",{className:"footer-btn",onClick:()=>v(!0),children:[W.jsx(f,{size:20}),W.jsx("span",{children:"设置"})]}),W.jsx("button",{className:"footer-btn",onClick:()=>{!b&&document.documentElement.requestFullscreen?(document.documentElement.requestFullscreen(),S(!0)):b&&document.exitFullscreen&&(document.exitFullscreen(),S(!1))},children:W.jsx("span",{children:b?"退出全屏":"全屏模式"})}),W.jsx("button",{className:"footer-btn",onClick:P,children:W.jsx("span",{children:"通知权限"})})]}),x&&W.jsx("div",{className:"settings-overlay",children:W.jsxs("div",{className:"settings-modal",children:[W.jsx("h3",{children:"专注设置"}),W.jsxs("div",{className:"settings-form",children:[W.jsxs("div",{className:"setting-group",children:[W.jsx("label",{children:"番茄时钟时长（分钟）"}),W.jsx("input",{type:"number",value:s.pomodoroLength,onChange:e=>L({...s,pomodoroLength:parseInt(e.target.value)||25}),min:"1",max:"120"})]}),W.jsxs("div",{className:"setting-group",children:[W.jsx("label",{children:"短休息时长（分钟）"}),W.jsx("input",{type:"number",value:s.shortBreakLength,onChange:e=>L({...s,shortBreakLength:parseInt(e.target.value)||5}),min:"1",max:"30"})]}),W.jsxs("div",{className:"setting-group",children:[W.jsx("label",{children:"长休息时长（分钟）"}),W.jsx("input",{type:"number",value:s.longBreakLength,onChange:e=>L({...s,longBreakLength:parseInt(e.target.value)||15}),min:"5",max:"60"})]}),W.jsx("div",{className:"setting-group",children:W.jsxs("label",{children:[W.jsx("input",{type:"checkbox",checked:s.autoStartBreaks,onChange:e=>L({...s,autoStartBreaks:e.target.checked})}),"自动开始休息"]})}),W.jsx("div",{className:"setting-group",children:W.jsxs("label",{children:[W.jsx("input",{type:"checkbox",checked:s.strictMode,onChange:e=>L({...s,strictMode:e.target.checked})}),"严格模式"]})}),W.jsx("div",{className:"setting-group",children:W.jsxs("label",{children:[W.jsx("input",{type:"checkbox",checked:s.soundEnabled,onChange:e=>L({...s,soundEnabled:e.target.checked})}),"声音提醒"]})}),W.jsx("div",{className:"setting-group",children:W.jsxs("label",{children:[W.jsx("input",{type:"checkbox",checked:s.vibrationEnabled,onChange:e=>L({...s,vibrationEnabled:e.target.checked})}),"震动提醒"]})})]}),W.jsx("div",{className:"settings-actions",children:W.jsx("button",{className:"btn-cancel",onClick:()=>v(!1),children:"关闭"})})]})})]})},Pt=()=>{const[e,t]=r.useState("recruit"),[s,a]=r.useState([]),[i,n]=r.useState([]),[o,c]=r.useState({totalUsers:0,activeTesters:0,completedTests:0,totalFeedback:0,avgRating:0,bugReports:0,featureRequests:0}),[l,d]=r.useState({name:"",email:"",age:"",occupation:"",experience:"",motivation:"",availability:"",deviceType:[]}),[h,u]=r.useState({category:"usability",severity:"medium",title:"",description:"",steps:"",expected:"",actual:"",rating:5}),[m,p]=r.useState(!1),[g,y]=r.useState(null),[f,T]=r.useState(null);r.useEffect(()=>{j()},[]);const j=()=>{const e=localStorage.getItem("testUsers"),t=localStorage.getItem("userFeedbacks");if(e){const s=JSON.parse(e);a(s),R(s,t?JSON.parse(t):[])}t&&n(JSON.parse(t))},R=(e,t)=>{const s=e.length,a=e.filter(e=>"testing"===e.status).length,i=e.filter(e=>"completed"===e.status).length,n=t.length,r=t.length>0?t.reduce((e,t)=>e+t.rating,0)/t.length:0,o=t.filter(e=>"bug"===e.category).length,l=t.filter(e=>"feature"===e.category).length;c({totalUsers:s,activeTesters:a,completedTests:i,totalFeedback:n,avgRating:r,bugReports:o,featureRequests:l})},_=e=>{switch(e){case"bug":return"🐛";case"feature":return"✨";case"usability":return"👥";case"performance":return"⚡";case"suggestion":return"💡";default:return"📝"}},I=e=>{switch(e){case"low":return"#10b981";case"medium":return"#f59e0b";case"high":return"#ef4444";case"critical":return"#dc2626";default:return"#6b7280"}};return W.jsxs("div",{className:"user-testing",children:[W.jsxs("div",{className:"testing-nav",children:[W.jsxs("button",{className:"nav-btn ".concat("recruit"===e?"active":""),onClick:()=>t("recruit"),children:[W.jsx(x,{size:20}),"用户招募"]}),W.jsxs("button",{className:"nav-btn ".concat("feedback"===e?"active":""),onClick:()=>t("feedback"),children:[W.jsx(v,{size:20}),"反馈收集"]}),W.jsxs("button",{className:"nav-btn ".concat("analytics"===e?"active":""),onClick:()=>t("analytics"),children:[W.jsx(b,{size:20}),"数据分析"]})]}),W.jsxs("div",{className:"testing-content",children:["recruit"===e&&W.jsxs("div",{className:"recruitment-section",children:[W.jsxs("div",{className:"recruitment-header",children:[W.jsxs("div",{className:"header-content",children:[W.jsx(x,{className:"header-icon"}),W.jsxs("div",{children:[W.jsx("h2",{children:"加入自律农场测试团队"}),W.jsx("p",{children:"帮助我们打造更好的自律管理工具，获得专属测试者奖励"})]})]}),W.jsxs("div",{className:"recruitment-stats",children:[W.jsxs("div",{className:"stat-item",children:[W.jsx("span",{className:"stat-number",children:o.totalUsers}),W.jsx("span",{className:"stat-label",children:"已招募用户"})]}),W.jsxs("div",{className:"stat-item",children:[W.jsx("span",{className:"stat-number",children:o.activeTesters}),W.jsx("span",{className:"stat-label",children:"活跃测试者"})]})]})]}),W.jsxs("div",{className:"recruitment-benefits",children:[W.jsx("h3",{children:"测试者专属福利"}),W.jsxs("div",{className:"benefits-grid",children:[W.jsxs("div",{className:"benefit-item",children:[W.jsx("div",{className:"benefit-icon",children:"🎁"}),W.jsx("h4",{children:"专属奖励"}),W.jsx("p",{children:"获得限定皮肤、道具和积分奖励"})]}),W.jsxs("div",{className:"benefit-item",children:[W.jsx("div",{className:"benefit-icon",children:"🚀"}),W.jsx("h4",{children:"抢先体验"}),W.jsx("p",{children:"优先体验最新功能和更新"})]}),W.jsxs("div",{className:"benefit-item",children:[W.jsx("div",{className:"benefit-icon",children:"💬"}),W.jsx("h4",{children:"直接反馈"}),W.jsx("p",{children:"与开发团队直接沟通，影响产品方向"})]}),W.jsxs("div",{className:"benefit-item",children:[W.jsx("div",{className:"benefit-icon",children:"👑"}),W.jsx("h4",{children:"测试者徽章"}),W.jsx("p",{children:"获得专属测试者身份标识"})]})]})]}),W.jsxs("form",{className:"recruitment-form",onSubmit:async e=>{e.preventDefault(),p(!0),T(null);try{if(!l.name||!l.email||!l.age)throw new Error("请填写所有必填字段");const e={id:"user_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9)),name:l.name,email:l.email,age:parseInt(l.age),occupation:l.occupation,experience:l.experience,motivation:l.motivation,availability:l.availability,deviceType:l.deviceType,registeredAt:Date.now(),status:"pending"},t=[...s,e];a(t),localStorage.setItem("testUsers",JSON.stringify(t)),R(t,i),d({name:"",email:"",age:"",occupation:"",experience:"",motivation:"",availability:"",deviceType:[]}),y("申请提交成功！我们会在24小时内与您联系。")}catch(t){T(t instanceof Error?t.message:"提交失败，请重试")}finally{p(!1)}},children:[W.jsx("h3",{children:"申请成为测试用户"}),W.jsxs("div",{className:"form-row",children:[W.jsxs("div",{className:"form-group",children:[W.jsx("label",{children:"姓名 *"}),W.jsx("input",{type:"text",value:l.name,onChange:e=>d(t=>({...t,name:e.target.value})),placeholder:"请输入您的姓名",required:!0})]}),W.jsxs("div",{className:"form-group",children:[W.jsx("label",{children:"邮箱 *"}),W.jsx("input",{type:"email",value:l.email,onChange:e=>d(t=>({...t,email:e.target.value})),placeholder:"请输入您的邮箱",required:!0})]})]}),W.jsxs("div",{className:"form-row",children:[W.jsxs("div",{className:"form-group",children:[W.jsx("label",{children:"年龄 *"}),W.jsx("input",{type:"number",value:l.age,onChange:e=>d(t=>({...t,age:e.target.value})),placeholder:"请输入您的年龄",min:"13",max:"100",required:!0})]}),W.jsxs("div",{className:"form-group",children:[W.jsx("label",{children:"职业"}),W.jsx("input",{type:"text",value:l.occupation,onChange:e=>d(t=>({...t,occupation:e.target.value})),placeholder:"请输入您的职业"})]})]}),W.jsxs("div",{className:"form-group",children:[W.jsx("label",{children:"设备类型 (可多选)"}),W.jsx("div",{className:"device-options",children:["Windows","macOS","Linux","Android","iOS","Web浏览器"].map(e=>W.jsxs("label",{className:"device-option",children:[W.jsx("input",{type:"checkbox",checked:l.deviceType.includes(e),onChange:()=>(e=>{d(t=>({...t,deviceType:t.deviceType.includes(e)?t.deviceType.filter(t=>t!==e):[...t.deviceType,e]}))})(e)}),W.jsx("span",{children:e})]},e))})]}),W.jsxs("div",{className:"form-group",children:[W.jsx("label",{children:"游戏/应用使用经验"}),W.jsxs("select",{value:l.experience,onChange:e=>d(t=>({...t,experience:e.target.value})),children:[W.jsx("option",{value:"",children:"请选择"}),W.jsx("option",{value:"beginner",children:"新手 (很少使用)"}),W.jsx("option",{value:"intermediate",children:"中级 (偶尔使用)"}),W.jsx("option",{value:"advanced",children:"高级 (经常使用)"}),W.jsx("option",{value:"expert",children:"专家 (重度用户)"})]})]}),W.jsxs("div",{className:"form-group",children:[W.jsx("label",{children:"参与动机"}),W.jsx("textarea",{value:l.motivation,onChange:e=>d(t=>({...t,motivation:e.target.value})),placeholder:"请简述您想参与测试的原因...",rows:3})]}),W.jsxs("div",{className:"form-group",children:[W.jsx("label",{children:"可用时间"}),W.jsxs("select",{value:l.availability,onChange:e=>d(t=>({...t,availability:e.target.value})),children:[W.jsx("option",{value:"",children:"请选择"}),W.jsx("option",{value:"weekdays",children:"工作日"}),W.jsx("option",{value:"weekends",children:"周末"}),W.jsx("option",{value:"evenings",children:"晚上"}),W.jsx("option",{value:"anytime",children:"任何时间"})]})]}),f&&W.jsxs("div",{className:"submit-message error",children:[W.jsx(S,{size:16}),f]}),g&&W.jsxs("div",{className:"submit-message success",children:[W.jsx(E,{size:16}),g]}),W.jsxs("button",{type:"submit",className:"submit-btn",disabled:m,children:[m?"提交中...":"申请加入测试",W.jsx(w,{size:16})]})]})]}),"feedback"===e&&W.jsxs("div",{className:"feedback-section",children:[W.jsxs("div",{className:"feedback-header",children:[W.jsx(v,{className:"header-icon"}),W.jsxs("div",{children:[W.jsx("h2",{children:"用户反馈中心"}),W.jsx("p",{children:"您的意见对我们很重要，帮助我们改进产品"})]})]}),W.jsxs("form",{className:"feedback-form",onSubmit:async e=>{e.preventDefault(),p(!0),T(null);try{if(!h.title||!h.description)throw new Error("请填写标题和描述");const e={id:"feedback_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9)),userId:"current_user",userName:"测试用户",category:h.category,severity:h.severity,title:h.title,description:h.description,steps:h.steps,expected:h.expected,actual:h.actual,rating:h.rating,timestamp:Date.now(),status:"new"},t=[...i,e];n(t),localStorage.setItem("userFeedbacks",JSON.stringify(t)),R(s,t),u({category:"usability",severity:"medium",title:"",description:"",steps:"",expected:"",actual:"",rating:5}),y("反馈提交成功！感谢您的宝贵意见。")}catch(t){T(t instanceof Error?t.message:"提交失败，请重试")}finally{p(!1)}},children:[W.jsx("h3",{children:"提交反馈"}),W.jsxs("div",{className:"form-row",children:[W.jsxs("div",{className:"form-group",children:[W.jsx("label",{children:"反馈类型"}),W.jsxs("select",{value:h.category,onChange:e=>u(t=>({...t,category:e.target.value})),children:[W.jsx("option",{value:"usability",children:"可用性问题"}),W.jsx("option",{value:"bug",children:"错误报告"}),W.jsx("option",{value:"feature",children:"功能建议"}),W.jsx("option",{value:"performance",children:"性能问题"}),W.jsx("option",{value:"suggestion",children:"其他建议"})]})]}),W.jsxs("div",{className:"form-group",children:[W.jsx("label",{children:"严重程度"}),W.jsxs("select",{value:h.severity,onChange:e=>u(t=>({...t,severity:e.target.value})),children:[W.jsx("option",{value:"low",children:"轻微"}),W.jsx("option",{value:"medium",children:"中等"}),W.jsx("option",{value:"high",children:"严重"}),W.jsx("option",{value:"critical",children:"紧急"})]})]})]}),W.jsxs("div",{className:"form-group",children:[W.jsx("label",{children:"标题 *"}),W.jsx("input",{type:"text",value:h.title,onChange:e=>u(t=>({...t,title:e.target.value})),placeholder:"简要描述问题或建议",required:!0})]}),W.jsxs("div",{className:"form-group",children:[W.jsx("label",{children:"详细描述 *"}),W.jsx("textarea",{value:h.description,onChange:e=>u(t=>({...t,description:e.target.value})),placeholder:"请详细描述您遇到的问题或建议...",rows:4,required:!0})]}),"bug"===h.category&&W.jsxs(W.Fragment,{children:[W.jsxs("div",{className:"form-group",children:[W.jsx("label",{children:"复现步骤"}),W.jsx("textarea",{value:h.steps,onChange:e=>u(t=>({...t,steps:e.target.value})),placeholder:"请描述如何复现这个问题...",rows:3})]}),W.jsxs("div",{className:"form-row",children:[W.jsxs("div",{className:"form-group",children:[W.jsx("label",{children:"期望结果"}),W.jsx("textarea",{value:h.expected,onChange:e=>u(t=>({...t,expected:e.target.value})),placeholder:"您期望发生什么...",rows:2})]}),W.jsxs("div",{className:"form-group",children:[W.jsx("label",{children:"实际结果"}),W.jsx("textarea",{value:h.actual,onChange:e=>u(t=>({...t,actual:e.target.value})),placeholder:"实际发生了什么...",rows:2})]})]})]}),W.jsxs("div",{className:"form-group",children:[W.jsx("label",{children:"整体评分"}),W.jsxs("div",{className:"rating-input",children:[[1,2,3,4,5].map(e=>W.jsx("button",{type:"button",className:"star-btn ".concat(e<=h.rating?"active":""),onClick:()=>u(t=>({...t,rating:e})),children:W.jsx(N,{size:20,fill:e<=h.rating?"#fbbf24":"none"})},e)),W.jsxs("span",{className:"rating-text",children:[h.rating," 星"]})]})]}),f&&W.jsxs("div",{className:"submit-message error",children:[W.jsx(S,{size:16}),f]}),g&&W.jsxs("div",{className:"submit-message success",children:[W.jsx(E,{size:16}),g]}),W.jsxs("button",{type:"submit",className:"submit-btn",disabled:m,children:[m?"提交中...":"提交反馈",W.jsx(w,{size:16})]})]}),W.jsxs("div",{className:"feedback-list",children:[W.jsx("h3",{children:"最近反馈"}),0===i.length?W.jsxs("div",{className:"no-feedback",children:[W.jsx(v,{size:48,className:"no-feedback-icon"}),W.jsx("p",{children:"暂无反馈记录"})]}):W.jsx("div",{className:"feedback-items",children:i.slice(0,10).map(e=>W.jsxs("div",{className:"feedback-item",children:[W.jsxs("div",{className:"feedback-header",children:[W.jsxs("div",{className:"feedback-meta",children:[W.jsx("span",{className:"feedback-icon",children:_(e.category)}),W.jsx("span",{className:"feedback-title",children:e.title}),W.jsx("span",{className:"feedback-severity",style:{color:I(e.severity)},children:e.severity})]}),W.jsx("div",{className:"feedback-rating",children:[1,2,3,4,5].map(t=>W.jsx(N,{size:12,fill:t<=e.rating?"#fbbf24":"none",stroke:t<=e.rating?"#fbbf24":"#e5e7eb"},t))})]}),W.jsx("p",{className:"feedback-description",children:e.description}),W.jsxs("div",{className:"feedback-footer",children:[W.jsxs("span",{className:"feedback-user",children:["by ",e.userName]}),W.jsx("span",{className:"feedback-time",children:new Date(e.timestamp).toLocaleDateString()}),W.jsx("span",{className:"feedback-status ".concat(e.status),children:e.status})]})]},e.id))})]})]}),"analytics"===e&&W.jsxs("div",{className:"analytics-section",children:[W.jsxs("div",{className:"analytics-header",children:[W.jsx(b,{className:"header-icon"}),W.jsxs("div",{children:[W.jsx("h2",{children:"测试数据分析"}),W.jsx("p",{children:"实时监控测试进度和反馈统计"})]})]}),W.jsxs("div",{className:"analytics-grid",children:[W.jsxs("div",{className:"analytics-card",children:[W.jsxs("div",{className:"card-header",children:[W.jsx(x,{size:24}),W.jsx("h3",{children:"用户统计"})]}),W.jsxs("div",{className:"card-content",children:[W.jsxs("div",{className:"stat-row",children:[W.jsx("span",{children:"总注册用户"}),W.jsx("span",{className:"stat-value",children:o.totalUsers})]}),W.jsxs("div",{className:"stat-row",children:[W.jsx("span",{children:"活跃测试者"}),W.jsx("span",{className:"stat-value",children:o.activeTesters})]}),W.jsxs("div",{className:"stat-row",children:[W.jsx("span",{children:"完成测试"}),W.jsx("span",{className:"stat-value",children:o.completedTests})]})]})]}),W.jsxs("div",{className:"analytics-card",children:[W.jsxs("div",{className:"card-header",children:[W.jsx(v,{size:24}),W.jsx("h3",{children:"反馈统计"})]}),W.jsxs("div",{className:"card-content",children:[W.jsxs("div",{className:"stat-row",children:[W.jsx("span",{children:"总反馈数"}),W.jsx("span",{className:"stat-value",children:o.totalFeedback})]}),W.jsxs("div",{className:"stat-row",children:[W.jsx("span",{children:"错误报告"}),W.jsx("span",{className:"stat-value",children:o.bugReports})]}),W.jsxs("div",{className:"stat-row",children:[W.jsx("span",{children:"功能建议"}),W.jsx("span",{className:"stat-value",children:o.featureRequests})]})]})]}),W.jsxs("div",{className:"analytics-card",children:[W.jsxs("div",{className:"card-header",children:[W.jsx(N,{size:24}),W.jsx("h3",{children:"满意度"})]}),W.jsxs("div",{className:"card-content",children:[W.jsxs("div",{className:"rating-display",children:[W.jsx("span",{className:"rating-number",children:o.avgRating.toFixed(1)}),W.jsx("div",{className:"rating-stars",children:[1,2,3,4,5].map(e=>W.jsx(N,{size:16,fill:e<=Math.round(o.avgRating)?"#fbbf24":"none",stroke:e<=Math.round(o.avgRating)?"#fbbf24":"#e5e7eb"},e))})]}),W.jsx("p",{className:"rating-text",children:"平均用户评分"})]})]})]}),W.jsxs("div",{className:"users-table",children:[W.jsx("h3",{children:"测试用户管理"}),W.jsxs("div",{className:"table-container",children:[W.jsxs("table",{children:[W.jsx("thead",{children:W.jsxs("tr",{children:[W.jsx("th",{children:"姓名"}),W.jsx("th",{children:"邮箱"}),W.jsx("th",{children:"设备"}),W.jsx("th",{children:"状态"}),W.jsx("th",{children:"注册时间"}),W.jsx("th",{children:"操作"})]})}),W.jsx("tbody",{children:s.map(e=>W.jsxs("tr",{children:[W.jsx("td",{children:e.name}),W.jsx("td",{children:e.email}),W.jsx("td",{children:W.jsxs("div",{className:"device-tags",children:[e.deviceType.slice(0,2).map(e=>W.jsx("span",{className:"device-tag",children:e},e)),e.deviceType.length>2&&W.jsxs("span",{className:"device-tag",children:["+",e.deviceType.length-2]})]})}),W.jsx("td",{children:W.jsx("span",{className:"status-badge ".concat(e.status),children:e.status})}),W.jsx("td",{children:new Date(e.registeredAt).toLocaleDateString()}),W.jsx("td",{children:W.jsx("button",{className:"action-btn",onClick:()=>{const t=s.map(t=>t.id===e.id?{...t,status:"pending"===t.status?"approved":"testing"}:t);a(t),localStorage.setItem("testUsers",JSON.stringify(t)),R(t,i)},children:"pending"===e.status?"批准":"推进"})})]},e.id))})]}),0===s.length&&W.jsxs("div",{className:"no-users",children:[W.jsx(x,{size:48,className:"no-users-icon"}),W.jsx("p",{children:"暂无测试用户"})]})]})]})]})]})]})},Gt=()=>{const[e,t]=r.useState([]),[s,a]=r.useState("overview"),[i,n]=r.useState(null),[o,c]=r.useState(null);r.useEffect(()=>{l()},[]);const l=()=>{const e=localStorage.getItem("testPlans");if(e){const s=JSON.parse(e);t(s),s.length>0&&!i&&n(s[0])}else{const e=h();t([e]),n(e),localStorage.setItem("testPlans",JSON.stringify([e]))}},h=()=>{const e=Date.now(),t=[{id:"task_".concat(e,"_1"),title:"用户注册和登录测试",description:"测试用户注册流程和登录功能的完整性",category:"functionality",priority:"high",estimatedTime:15,instructions:["打开应用首页",'点击"注册"按钮',"填写用户信息（用户名、邮箱、密码）",'点击"注册"提交',"验证邮箱激活流程","使用注册信息进行登录","验证登录后的用户状态"],expectedResults:["注册表单正常显示","信息验证功能正常","注册成功提示显示","邮箱激活链接有效","登录成功进入主界面","用户信息正确显示"],status:"pending",assignedUsers:[],createdAt:e,updatedAt:e},{id:"task_".concat(e,"_2"),title:"农场初始化测试",description:"测试新用户首次进入农场的初始化流程",category:"functionality",priority:"high",estimatedTime:20,instructions:["完成登录后进入农场界面","观察农场初始状态","完成新手教程引导","检查初始资源配置","测试基础操作提示","验证UI界面布局"],expectedResults:["农场界面正常加载","新手教程流畅运行","初始资源数量正确","操作提示清晰易懂","UI元素对齐正确","响应式布局适配良好"],status:"pending",assignedUsers:[],createdAt:e,updatedAt:e},{id:"task_".concat(e,"_3"),title:"种植功能测试",description:"测试作物种植的完整流程和功能",category:"functionality",priority:"high",estimatedTime:25,instructions:["点击空闲农田","选择作物类型进行种植","确认种植操作","观察种植后的农田状态","检查资源消耗情况","测试连续种植多个农田","验证种植限制和提示"],expectedResults:["农田选择界面正常","作物选择菜单显示完整","种植动画流畅播放","农田状态更新正确","资源扣除准确","批量种植功能正常","限制提示信息准确"],status:"pending",assignedUsers:[],createdAt:e,updatedAt:e},{id:"task_".concat(e,"_4"),title:"作物生长和收获测试",description:"测试作物生长时间机制和收获功能",category:"functionality",priority:"medium",estimatedTime:30,instructions:["种植不同生长周期的作物","观察作物生长状态变化","测试时间加速功能（如有）","在作物成熟时进行收获","验证收获奖励计算","检查经验值和资源增加","测试自动收获功能（如有）"],expectedResults:["生长状态显示准确","时间机制运行正常","成熟提示及时显示","收获操作响应正常","奖励计算准确无误","经验值正确增加","自动功能稳定运行"],status:"pending",assignedUsers:[],createdAt:e,updatedAt:e},{id:"task_".concat(e,"_5"),title:"商店和交易系统测试",description:"测试游戏内商店功能和交易机制",category:"functionality",priority:"medium",estimatedTime:20,instructions:["打开游戏商店界面","浏览不同类别的商品","尝试购买种子和道具","测试不同支付方式","验证购买限制","检查库存更新","测试出售功能（如有）"],expectedResults:["商店界面加载正常","商品分类清晰","购买流程顺畅","支付功能正常","限制机制有效","库存实时更新","出售功能正确"],status:"pending",assignedUsers:[],createdAt:e,updatedAt:e}],s=[{id:"task_".concat(e,"_6"),title:"应用监控权限授权测试",description:"测试桌面端应用监控权限的申请和授权流程",category:"functionality",priority:"high",estimatedTime:15,instructions:["启动桌面端应用","触发权限申请流程","在系统弹窗中授权监控权限","验证权限状态显示","测试权限被拒绝的情况","检查权限重新申请功能"],expectedResults:["权限申请流程明确","系统弹窗正常显示","授权后状态更新正确","拒绝权限时有适当提示","可以重新申请权限","权限状态实时同步"],status:"pending",assignedUsers:[],createdAt:e,updatedAt:e},{id:"task_".concat(e,"_7"),title:"应用使用监控测试",description:"测试对用户使用其他应用的监控和记录功能",category:"functionality",priority:"high",estimatedTime:25,instructions:["启用应用监控功能","打开和使用不同类型的应用","观察监控数据的记录","检查使用时长统计","验证应用分类准确性","测试实时监控状态","查看监控历史记录"],expectedResults:["监控功能正常启动","应用切换被准确捕获","使用时长计算正确","应用分类识别准确","实时状态更新及时","历史记录完整保存","数据统计准确无误"],status:"pending",assignedUsers:[],createdAt:e,updatedAt:e},{id:"task_".concat(e,"_8"),title:"白名单管理测试",description:"测试白名单应用的添加、删除和管理功能",category:"functionality",priority:"medium",estimatedTime:20,instructions:["打开白名单管理界面","添加常用工作应用到白名单","测试白名单应用的使用","验证白名单应用不计入限制","删除白名单中的应用","测试批量管理功能","检查白名单导入导出"],expectedResults:["白名单界面操作便捷","添加应用流程简单","白名单应用正确识别","时间限制规则生效","删除操作安全确认","批量操作功能完善","导入导出功能正常"],status:"pending",assignedUsers:[],createdAt:e,updatedAt:e},{id:"task_".concat(e,"_9"),title:"专注模式测试",description:"测试专注模式的启动、运行和结束流程",category:"functionality",priority:"high",estimatedTime:30,instructions:["设置专注时长和目标","启动专注模式","尝试打开非白名单应用","观察阻止提示和引导","测试紧急退出功能","完成专注后查看奖励","检查专注历史记录"],expectedResults:["专注设置界面清晰","专注模式正常启动","应用阻止功能有效","提示信息友好明确","紧急退出机制安全","奖励计算准确发放","历史记录详细完整"],status:"pending",assignedUsers:[],createdAt:e,updatedAt:e},{id:"task_".concat(e,"_10"),title:"统计报表功能测试",description:"测试使用统计和报表生成功能",category:"functionality",priority:"medium",estimatedTime:25,instructions:["查看日使用统计报表","检查周和月统计数据","测试不同时间范围筛选","验证应用分类统计","导出统计报表","检查数据可视化图表","测试统计数据的准确性"],expectedResults:["统计界面布局合理","多时间维度数据准确","筛选功能操作便捷","分类统计清晰明了","导出功能正常工作","图表显示美观直观","数据计算完全准确"],status:"pending",assignedUsers:[],createdAt:e,updatedAt:e}];return{id:"plan_".concat(e),name:"自律农场游戏测试计划",description:"全面测试自律农场游戏的核心功能、用户体验和性能表现",objectives:["验证核心游戏功能的正确性和稳定性","评估用户界面的易用性和直观性","测试应用监控功能的准确性和可靠性","检查跨平台兼容性和性能表现","评估整体用户体验和满意度","识别潜在的Bug和性能问题","收集用户反馈和改进建议"],targetAudience:["自律管理需求用户","游戏化应用用户","学生和职场人士","时间管理工具用户","效率提升追求者","游戏爱好者"],scenarios:[{id:"scenario_".concat(e,"_1"),name:"核心游戏功能测试",description:"测试农场建设、作物种植、收获等核心游戏功能",prerequisites:["已完成用户注册","已完成新手教程"],tasks:t,totalEstimatedTime:t.reduce((e,t)=>e+t.estimatedTime,0),status:"draft",createdAt:e,updatedAt:e},{id:"scenario_".concat(e,"_2"),name:"应用监控功能测试",description:"测试应用监控、白名单管理、专注模式等功能",prerequisites:["已安装桌面端应用","已授权应用监控权限"],tasks:s,totalEstimatedTime:s.reduce((e,t)=>e+t.estimatedTime,0),status:"draft",createdAt:e,updatedAt:e}],status:"planning",createdAt:e,updatedAt:e}},u=e=>({total:e.tasks.length,completed:e.tasks.filter(e=>"completed"===e.status).length,inProgress:e.tasks.filter(e=>"in-progress"===e.status).length,failed:e.tasks.filter(e=>"failed"===e.status).length}),m=()=>{if(!i)return{total:0,completed:0,inProgress:0,failed:0};let e=0,t=0,s=0,a=0;return i.scenarios.forEach(i=>{const n=u(i);e+=n.total,t+=n.completed,s+=n.inProgress,a+=n.failed}),{total:e,completed:t,inProgress:s,failed:a}};return W.jsxs("div",{className:"test-plan",children:[W.jsxs("div",{className:"plan-nav",children:[W.jsx("div",{className:"plan-selector",children:W.jsx("select",{value:(null==i?void 0:i.id)||"",onChange:t=>{const s=e.find(e=>e.id===t.target.value);n(s||null),c(null)},children:e.map(e=>W.jsx("option",{value:e.id,children:e.name},e.id))})}),W.jsxs("div",{className:"tab-nav",children:[W.jsxs("button",{className:"tab-btn ".concat("overview"===s?"active":""),onClick:()=>a("overview"),children:[W.jsx(T,{size:16}),"概览"]}),W.jsxs("button",{className:"tab-btn ".concat("scenarios"===s?"active":""),onClick:()=>a("scenarios"),children:[W.jsx(j,{size:16}),"场景"]}),W.jsxs("button",{className:"tab-btn ".concat("tasks"===s?"active":""),onClick:()=>a("tasks"),children:[W.jsx(R,{size:16}),"任务"]}),W.jsxs("button",{className:"tab-btn ".concat("progress"===s?"active":""),onClick:()=>a("progress"),children:[W.jsx(_,{size:16}),"进度"]})]})]}),W.jsxs("div",{className:"plan-content",children:["overview"===s&&(()=>{if(!i)return null;const e=m(),t=e.total>0?e.completed/e.total*100:0;return W.jsxs("div",{className:"test-overview",children:[W.jsxs("div",{className:"plan-header",children:[W.jsxs("div",{className:"plan-info",children:[W.jsx("h2",{children:i.name}),W.jsx("p",{children:i.description}),W.jsxs("div",{className:"plan-meta",children:[W.jsx("span",{className:"plan-status",children:i.status}),W.jsxs("span",{className:"plan-date",children:["创建时间: ",new Date(i.createdAt).toLocaleDateString()]})]})]}),W.jsxs("div",{className:"plan-stats",children:[W.jsxs("div",{className:"stat-card",children:[W.jsx("div",{className:"stat-number",children:i.scenarios.length}),W.jsx("div",{className:"stat-label",children:"测试场景"})]}),W.jsxs("div",{className:"stat-card",children:[W.jsx("div",{className:"stat-number",children:e.total}),W.jsx("div",{className:"stat-label",children:"测试任务"})]}),W.jsxs("div",{className:"stat-card",children:[W.jsxs("div",{className:"stat-number",children:[t.toFixed(0),"%"]}),W.jsx("div",{className:"stat-label",children:"完成进度"})]})]})]}),W.jsxs("div",{className:"progress-section",children:[W.jsx("h3",{children:"整体进度"}),W.jsx("div",{className:"progress-bar",children:W.jsx("div",{className:"progress-fill",style:{width:"".concat(t,"%")}})}),W.jsxs("div",{className:"progress-details",children:[W.jsxs("div",{className:"progress-item",children:[W.jsx("span",{className:"progress-dot completed"}),W.jsxs("span",{children:["已完成: ",e.completed]})]}),W.jsxs("div",{className:"progress-item",children:[W.jsx("span",{className:"progress-dot in-progress"}),W.jsxs("span",{children:["进行中: ",e.inProgress]})]}),W.jsxs("div",{className:"progress-item",children:[W.jsx("span",{className:"progress-dot failed"}),W.jsxs("span",{children:["失败: ",e.failed]})]})]})]}),W.jsxs("div",{className:"objectives-section",children:[W.jsx("h3",{children:"测试目标"}),W.jsx("ul",{className:"objectives-list",children:i.objectives.map((e,t)=>W.jsx("li",{children:e},t))})]}),W.jsxs("div",{className:"audience-section",children:[W.jsx("h3",{children:"目标用户群体"}),W.jsx("div",{className:"audience-tags",children:i.targetAudience.map((e,t)=>W.jsx("span",{className:"audience-tag",children:e},t))})]})]})})(),"scenarios"===s&&(i?W.jsxs("div",{className:"scenarios-section",children:[W.jsxs("div",{className:"scenarios-header",children:[W.jsx("h3",{children:"测试场景管理"}),W.jsx("p",{children:"管理和监控不同的测试场景和其中的任务"})]}),W.jsx("div",{className:"scenarios-grid",children:i.scenarios.map(e=>{const t=u(e),s=t.total>0?t.completed/t.total*100:0;return W.jsxs("div",{className:"scenario-card ".concat((null==o?void 0:o.id)===e.id?"selected":""),onClick:()=>c(e),children:[W.jsxs("div",{className:"scenario-header",children:[W.jsx("h4",{children:e.name}),W.jsx("span",{className:"scenario-status ".concat(e.status),children:"draft"===e.status?"草案":"active"===e.status?"进行中":"completed"===e.status?"已完成":"已归档"})]}),W.jsx("p",{className:"scenario-description",children:e.description}),W.jsxs("div",{className:"scenario-stats",children:[W.jsxs("div",{className:"stat-item",children:[W.jsx("span",{className:"stat-label",children:"任务总数"}),W.jsx("span",{className:"stat-value",children:t.total})]}),W.jsxs("div",{className:"stat-item",children:[W.jsx("span",{className:"stat-label",children:"已完成"}),W.jsx("span",{className:"stat-value",children:t.completed})]}),W.jsxs("div",{className:"stat-item",children:[W.jsx("span",{className:"stat-label",children:"预计时长"}),W.jsxs("span",{className:"stat-value",children:[e.totalEstimatedTime,"分钟"]})]})]}),W.jsxs("div",{className:"scenario-progress",children:[W.jsx("div",{className:"progress-bar small",children:W.jsx("div",{className:"progress-fill",style:{width:"".concat(s,"%")}})}),W.jsxs("span",{className:"progress-text",children:[s.toFixed(0),"%"]})]}),W.jsxs("div",{className:"scenario-prerequisites",children:[W.jsx("h5",{children:"前置条件:"}),W.jsx("ul",{children:e.prerequisites.map((e,t)=>W.jsx("li",{children:e},t))})]})]},e.id)})}),o&&W.jsxs("div",{className:"scenario-details",children:[W.jsxs("h4",{children:["场景详情: ",o.name]}),W.jsxs("div",{className:"scenario-actions",children:[W.jsxs("button",{className:"btn btn-primary",onClick:()=>{},children:[W.jsx(d,{size:16}),"启动场景"]}),W.jsxs("button",{className:"btn btn-secondary",onClick:()=>a("tasks"),children:[W.jsx(R,{size:16}),"查看任务"]})]})]})]}):null),"tasks"===s&&(()=>{if(!i)return null;const e=i.scenarios.flatMap(e=>e.tasks.map(t=>({...t,scenarioName:e.name}))),t=o?o.tasks:e;return W.jsxs("div",{className:"tasks-section",children:[W.jsxs("div",{className:"tasks-header",children:[W.jsx("h3",{children:o?"".concat(o.name," - 任务列表"):"所有测试任务"}),o&&W.jsx("button",{className:"btn btn-link",onClick:()=>c(null),children:"查看所有任务"})]}),W.jsxs("div",{className:"tasks-filters",children:[W.jsxs("div",{className:"filter-group",children:[W.jsx("label",{children:"按状态筛选:"}),W.jsxs("select",{onChange:e=>{},children:[W.jsx("option",{value:"",children:"全部"}),W.jsx("option",{value:"pending",children:"待执行"}),W.jsx("option",{value:"in-progress",children:"进行中"}),W.jsx("option",{value:"completed",children:"已完成"}),W.jsx("option",{value:"failed",children:"失败"}),W.jsx("option",{value:"skipped",children:"已跳过"})]})]}),W.jsxs("div",{className:"filter-group",children:[W.jsx("label",{children:"按优先级筛选:"}),W.jsxs("select",{onChange:e=>{},children:[W.jsx("option",{value:"",children:"全部"}),W.jsx("option",{value:"high",children:"高"}),W.jsx("option",{value:"medium",children:"中"}),W.jsx("option",{value:"low",children:"低"})]})]})]}),W.jsx("div",{className:"tasks-list",children:t.map(e=>W.jsxs("div",{className:"task-card",children:[W.jsxs("div",{className:"task-header",children:[W.jsxs("div",{className:"task-title",children:[W.jsx("h4",{children:e.title}),W.jsxs("div",{className:"task-badges",children:[W.jsx("span",{className:"priority-badge ".concat(e.priority),children:"high"===e.priority?"高":"medium"===e.priority?"中":"低"}),W.jsx("span",{className:"status-badge ".concat(e.status),children:"pending"===e.status?"待执行":"in-progress"===e.status?"进行中":"completed"===e.status?"已完成":"failed"===e.status?"失败":"已跳过"}),W.jsx("span",{className:"category-badge",children:"functionality"===e.category?"功能":"usability"===e.category?"可用性":"performance"===e.category?"性能":"compatibility"===e.category?"兼容性":"安全"})]})]}),W.jsxs("div",{className:"task-meta",children:[W.jsxs("span",{className:"estimated-time",children:[W.jsx(I,{size:14}),e.estimatedTime,"分钟"]}),!o&&W.jsx("span",{className:"scenario-name",children:e.scenarioName})]})]}),W.jsx("p",{className:"task-description",children:e.description}),W.jsxs("div",{className:"task-details",children:[W.jsxs("div",{className:"task-instructions",children:[W.jsx("h5",{children:"测试步骤:"}),W.jsx("ol",{children:e.instructions.map((e,t)=>W.jsx("li",{children:e},t))})]}),W.jsxs("div",{className:"task-expected",children:[W.jsx("h5",{children:"预期结果:"}),W.jsx("ul",{children:e.expectedResults.map((e,t)=>W.jsx("li",{children:e},t))})]})]}),W.jsxs("div",{className:"task-actions",children:[W.jsx("button",{className:"btn btn-sm btn-primary",onClick:()=>{},disabled:"completed"===e.status,children:"pending"===e.status?"开始测试":"in-progress"===e.status?"继续测试":"已完成"}),W.jsxs("button",{className:"btn btn-sm btn-secondary",onClick:()=>{},children:[W.jsx(k,{size:14}),"编辑"]})]}),e.notes&&W.jsxs("div",{className:"task-notes",children:[W.jsx("h5",{children:"备注:"}),W.jsx("p",{children:e.notes})]})]},e.id))})]})})(),"progress"===s&&(()=>{if(!i)return null;const e=m(),t=e.total>0?e.completed/e.total*100:0;return W.jsxs("div",{className:"progress-analysis",children:[W.jsxs("div",{className:"progress-header",children:[W.jsx("h3",{children:"测试进度分析"}),W.jsx("p",{children:"详细的测试进度统计和分析报告"})]}),W.jsx("div",{className:"progress-overview",children:W.jsxs("div",{className:"progress-card",children:[W.jsx("h4",{children:"整体进度"}),W.jsx("div",{className:"progress-circle",children:W.jsx("div",{className:"circle-progress",style:{"--progress":"".concat(t,"%")},children:W.jsxs("span",{className:"progress-value",children:[t.toFixed(0),"%"]})})}),W.jsxs("div",{className:"progress-breakdown",children:[W.jsxs("div",{className:"breakdown-item",children:[W.jsx("span",{className:"dot completed"}),W.jsxs("span",{children:["已完成: ",e.completed]})]}),W.jsxs("div",{className:"breakdown-item",children:[W.jsx("span",{className:"dot in-progress"}),W.jsxs("span",{children:["进行中: ",e.inProgress]})]}),W.jsxs("div",{className:"breakdown-item",children:[W.jsx("span",{className:"dot pending"}),W.jsxs("span",{children:["待执行: ",e.total-e.completed-e.inProgress-e.failed]})]}),W.jsxs("div",{className:"breakdown-item",children:[W.jsx("span",{className:"dot failed"}),W.jsxs("span",{children:["失败: ",e.failed]})]})]})]})}),W.jsxs("div",{className:"scenarios-progress",children:[W.jsx("h4",{children:"各场景进度"}),W.jsx("div",{className:"scenarios-list",children:i.scenarios.map(e=>{const t=u(e),s=t.total>0?t.completed/t.total*100:0;return W.jsxs("div",{className:"scenario-progress-item",children:[W.jsxs("div",{className:"scenario-info",children:[W.jsx("h5",{children:e.name}),W.jsxs("span",{className:"task-count",children:[t.completed,"/",t.total," 任务完成"]})]}),W.jsxs("div",{className:"scenario-progress-bar",children:[W.jsx("div",{className:"progress-bar",children:W.jsx("div",{className:"progress-fill",style:{width:"".concat(s,"%")}})}),W.jsxs("span",{className:"progress-percentage",children:[s.toFixed(0),"%"]})]}),W.jsx("div",{className:"scenario-time",children:W.jsxs("span",{children:["预计: ",e.totalEstimatedTime,"分钟"]})})]},e.id)})})]}),W.jsxs("div",{className:"time-analysis",children:[W.jsx("h4",{children:"时间分析"}),W.jsxs("div",{className:"time-stats",children:[W.jsxs("div",{className:"time-stat",children:[W.jsx("span",{className:"stat-label",children:"总预计时间"}),W.jsxs("span",{className:"stat-value",children:[i.scenarios.reduce((e,t)=>e+t.totalEstimatedTime,0),"分钟"]})]}),W.jsxs("div",{className:"time-stat",children:[W.jsx("span",{className:"stat-label",children:"平均任务时长"}),W.jsxs("span",{className:"stat-value",children:[e.total>0?(i.scenarios.reduce((e,t)=>e+t.totalEstimatedTime,0)/e.total).toFixed(0):0,"分钟"]})]})]})]})]})})()]})]})},Bt=()=>{const[e,t]=r.useState([]),[s,a]=r.useState(null),[i,n]=r.useState([]),[o,c]=r.useState("7d"),[l,d]=r.useState("all"),[h,u]=r.useState(!1);r.useEffect(()=>{u(!0);const e=localStorage.getItem("userFeedbacks");let s=[];if(e)try{s=JSON.parse(e)}catch(a){}if(s.length<10){const e=(()=>{const e=[],t=["usability","bug","feature","performance","other"],s=["low","medium","high","urgent"],a=["new","reviewed","in-progress","resolved","closed"],i=["游戏在种植时偶尔会卡顿","收获作物时金币显示错误","商店界面在手机上显示不完整","应用监控功能无法正常授权","专注模式下仍然收到通知","统计报表数据更新延迟","白名单设置保存失败","用户登录时出现闪退","作物生长时间计算错误","界面切换时动画卡顿"],n=["种植按钮位置可以更明显","商店分类导航需要优化","设置页面布局可以改进","教程指引不够清晰","操作反馈提示太少","色彩搭配可以更温馨","字体大小可以调整","导航结构需要简化","功能入口不够直观","新手引导需要改进"],r=["希望添加社交功能","建议增加成就系统","希望有更多作物种类","期望添加天气系统","建议增加农场装饰","希望有背景音乐","期望添加好友功能","建议增加日夜循环","希望有宠物系统","期望添加季节变化"];for(let o=0;o<150;o++){const c=t[Math.floor(Math.random()*t.length)];let l="";l="bug"===c?i[Math.floor(Math.random()*i.length)]:"usability"===c?n[Math.floor(Math.random()*n.length)]:"feature"===c?r[Math.floor(Math.random()*r.length)]:"".concat(c,"相关的反馈内容 ").concat(o+1);const d=s[Math.floor(Math.random()*s.length)],h=a[Math.floor(Math.random()*a.length)],u=Math.floor(5*Math.random())+1,m=Math.random()<.7?Math.floor(7*Math.random()):Math.floor(30*Math.random()),p=Date.now()-24*m*60*60*1e3-Math.floor(24*Math.random()*60*60*1e3);e.push({id:"feedback_".concat(o+1),userId:"user_".concat(Math.floor(50*Math.random())+1),type:c,severity:d,content:l,rating:u,timestamp:p,status:h,tags:["tag_".concat(Math.floor(10*Math.random())+1)],assignee:Math.random()>.5?"dev_".concat(Math.floor(5*Math.random())+1):void 0,..."bug"===c&&{bugDetails:{steps:"1. 打开应用 2. 进行操作 3. 观察问题",expected:"功能正常运行",actual:"出现错误或异常"}}})}return e})();s=[...s,...e.slice(0,100)]}t(s),u(!1)},[]);const m=r.useMemo(()=>{let t=[...e];const s=Date.now(),a={"1d":864e5,"7d":6048e5,"30d":2592e6,all:1/0};if(a[o]!==1/0){const e=s-a[o];t=t.filter(t=>t.timestamp>=e)}return"all"!==l&&(t=t.filter(e=>e.type===l||e.severity===l||e.status===l)),t},[e,o,l]);r.useEffect(()=>{if(0===m.length)return a(null),void n([]);const e=setTimeout(()=>{try{u(!0);const e=p(m);a(e);const t=g(m);n(t)}catch(e){a(null),n([])}finally{u(!1)}},300);return()=>clearTimeout(e)},[m]);const p=e=>{try{if(!e||!Array.isArray(e)||0===e.length)return{totalFeedbacks:0,averageRating:0,typeDistribution:{},severityDistribution:{},statusDistribution:{},ratingDistribution:{},timeAnalysis:{dailyTrends:[],hourlyDistribution:{}},topIssues:[],priorityRecommendations:[]};const t=e.length,s=e.reduce((e,t)=>e+(t.rating||0),0)/t,a={};e.forEach(e=>{const t=e.type||"other";a[t]=(a[t]||0)+1});const i={};e.forEach(e=>{const t=e.severity||"low";i[t]=(i[t]||0)+1});const n={};e.forEach(e=>{const t=e.status||"new";n[t]=(n[t]||0)+1});const r={};e.forEach(e=>{const t=e.rating||0;r[t]=(r[t]||0)+1});const o=[],c={},l={};e.forEach(e=>{try{const t=new Date(e.timestamp).toISOString().split("T")[0];l[t]||(l[t]=[]),l[t].push(e);const s=new Date(e.timestamp).getHours();isNaN(s)||(c[s]=(c[s]||0)+1)}catch(t){}}),Object.entries(l).forEach(([e,t])=>{if(t.length>0){const s=t.filter(e=>e.rating&&!isNaN(e.rating)),a=s.length>0?s.reduce((e,t)=>e+t.rating,0)/s.length:0;o.push({date:e,count:t.length,avgRating:a})}}),o.sort((e,t)=>e.date.localeCompare(t.date));const d=Object.entries(a).map(([t,s])=>{const a=e.filter(e=>e.type===t),i=a.filter(e=>e.rating&&!isNaN(e.rating)),n=i.length>0?i.reduce((e,t)=>e+t.rating,0)/i.length:0,r=y(a.map(e=>e.content||"").filter(e=>e.trim()).join(" "));return{category:t,count:s,avgRating:n,keywords:r.slice(0,5).map(e=>e.keyword)}}).sort((e,t)=>t.count-e.count),h=x(e);return{totalFeedbacks:t,averageRating:s,typeDistribution:a,severityDistribution:i,statusDistribution:n,ratingDistribution:r,timeAnalysis:{dailyTrends:o,hourlyDistribution:c},topIssues:d,priorityRecommendations:h}}catch(t){return{totalFeedbacks:0,averageRating:0,typeDistribution:{},severityDistribution:{},statusDistribution:{},ratingDistribution:{},timeAnalysis:{dailyTrends:[],hourlyDistribution:{}},topIssues:[],priorityRecommendations:[]}}},g=e=>{const t=e.map(e=>e.content).join(" ");return y(t)},y=e=>{try{if(!e||"string"!=typeof e||0===e.trim().length)return[];const t=["的","了","是","在","有","和","我","你","他","她","它","们","这","那","也","都","要","可以","能","会","就","还","不","没","很","非常","比较","更","最","太","好","没有","应该","建议","希望","需要","时候","问题","功能","用户","系统"],s=e.toLowerCase().replace(/[^\u4e00-\u9fa5a-z\s]/g," ").split(/\s+/).filter(e=>e.length>1&&!t.includes(e));if(0===s.length)return[];const a={};return s.forEach(e=>{a[e]=(a[e]||0)+1}),Object.entries(a).map(([e,t])=>({keyword:e,frequency:t,sentiment:f(e),associatedTypes:["usability","bug","feature"]})).sort((e,t)=>t.frequency-e.frequency).slice(0,20)}catch(t){return[]}},f=e=>["好","棒","优秀","喜欢","满意","完美","赞","不错","流畅","清晰","简单","方便"].some(t=>e.includes(t))?"positive":["差","坏","糟糕","讨厌","失望","错误","卡顿","闪退","缓慢","复杂","困难","问题"].some(t=>e.includes(t))?"negative":"neutral",x=e=>{const t=[],s=e.filter(e=>"urgent"===e.severity||"high"===e.severity);s.length>0&&t.push({id:"urgent_fixes",title:"修复紧急和高严重度问题",priority:"urgent",impact:"直接影响用户体验和产品稳定性",effort:"high",reasoning:"发现".concat(s.length,"个紧急或高严重度问题，需要立即处理")});const a=e.filter(e=>"usability"===e.type);a.length>.2*e.length&&t.push({id:"usability_improvements",title:"改进用户界面和交互体验",priority:"high",impact:"提升整体用户满意度",effort:"medium",reasoning:"可用性问题占比".concat((a.length/e.length*100).toFixed(1),"%，需要优化")});const i=e.filter(e=>"feature"===e.type);return i.length>0&&t.push({id:"feature_development",title:"开发用户最需要的新功能",priority:"medium",impact:"增加产品价值和用户粘性",effort:"high",reasoning:"收到".concat(i.length,"个功能请求，建议进行需求分析和优先级排序")}),e.filter(e=>"performance"===e.type).length>0&&t.push({id:"performance_optimization",title:"性能优化和稳定性提升",priority:"high",impact:"提升应用响应速度和稳定性",effort:"medium",reasoning:"性能相关问题需要技术优化解决"}),t};return h?W.jsx("div",{className:"feedback-analysis loading",children:W.jsx("div",{className:"loading-spinner",children:"加载中..."})}):W.jsxs("div",{className:"feedback-analysis",children:[W.jsxs("div",{className:"analysis-header",children:[W.jsx("h2",{children:"用户反馈分析报告"}),W.jsxs("div",{className:"analysis-controls",children:[W.jsxs("select",{value:o,onChange:e=>c(e.target.value),className:"time-range-select",children:[W.jsx("option",{value:"1d",children:"最近1天"}),W.jsx("option",{value:"7d",children:"最近7天"}),W.jsx("option",{value:"30d",children:"最近30天"}),W.jsx("option",{value:"all",children:"全部时间"})]}),W.jsxs("select",{value:l,onChange:e=>d(e.target.value),className:"filter-select",children:[W.jsx("option",{value:"all",children:"全部类型"}),W.jsx("option",{value:"usability",children:"可用性"}),W.jsx("option",{value:"bug",children:"错误报告"}),W.jsx("option",{value:"feature",children:"功能建议"}),W.jsx("option",{value:"performance",children:"性能问题"}),W.jsx("option",{value:"urgent",children:"紧急"}),W.jsx("option",{value:"high",children:"高优先级"})]})]})]}),s?W.jsxs("div",{className:"analysis-content",children:[W.jsxs("div",{className:"overview-stats",children:[W.jsxs("div",{className:"stat-card",children:[W.jsx("div",{className:"stat-number",children:s.totalFeedbacks}),W.jsx("div",{className:"stat-label",children:"总反馈数"})]}),W.jsxs("div",{className:"stat-card",children:[W.jsx("div",{className:"stat-number",children:s.averageRating.toFixed(1)}),W.jsx("div",{className:"stat-label",children:"平均评分"})]}),W.jsxs("div",{className:"stat-card",children:[W.jsx("div",{className:"stat-number",children:(s.statusDistribution.resolved||0)+(s.statusDistribution.closed||0)}),W.jsx("div",{className:"stat-label",children:"已解决"})]}),W.jsxs("div",{className:"stat-card",children:[W.jsxs("div",{className:"stat-number",children:[Math.round(((s.statusDistribution.resolved||0)+(s.statusDistribution.closed||0))/s.totalFeedbacks*100),"%"]}),W.jsx("div",{className:"stat-label",children:"解决率"})]})]}),W.jsxs("div",{className:"charts-grid",children:[(()=>{if(!s)return null;const{typeDistribution:e}=s,t=Object.values(e).reduce((e,t)=>e+t,0),a={usability:"可用性",bug:"错误报告",feature:"功能建议",performance:"性能问题",other:"其他"};return W.jsxs("div",{className:"chart-container",children:[W.jsx("h3",{children:"反馈类型分布"}),W.jsx("div",{className:"pie-chart",children:Object.entries(e).map(([e,s])=>{const i=s/t*100;return W.jsxs("div",{className:"chart-item",children:[W.jsx("div",{className:"chart-bar",children:W.jsx("div",{className:"chart-fill chart-".concat(e),style:{width:"".concat(i,"%")}})}),W.jsxs("div",{className:"chart-label",children:[W.jsx("span",{className:"type-name",children:a[e]||e}),W.jsxs("span",{className:"type-count",children:[s," (",i.toFixed(1),"%)"]})]})]},e)})})]})})(),(()=>{if(!s)return null;const{severityDistribution:e}=s,t={urgent:"紧急",high:"高",medium:"中等",low:"低"},a={urgent:"#ff4757",high:"#ff6b7a",medium:"#ffa502",low:"#2ed573"};return W.jsxs("div",{className:"severity-analysis",children:[W.jsx("h3",{children:"严重程度分析"}),W.jsx("div",{className:"severity-grid",children:Object.entries(e).map(([e,s])=>W.jsxs("div",{className:"severity-card",children:[W.jsx("div",{className:"severity-indicator",style:{backgroundColor:a[e]}}),W.jsxs("div",{className:"severity-info",children:[W.jsx("div",{className:"severity-label",children:t[e]}),W.jsx("div",{className:"severity-count",children:s})]})]},e))})]})})(),(()=>{if(!(null==s?void 0:s.timeAnalysis.dailyTrends.length))return null;const{dailyTrends:e}=s.timeAnalysis,t=Math.max(...e.map(e=>e.count));return W.jsxs("div",{className:"time-trends",children:[W.jsx("h3",{children:"每日反馈趋势"}),W.jsx("div",{className:"trend-chart",children:e.map((e,s)=>W.jsxs("div",{className:"trend-bar",children:[W.jsx("div",{className:"trend-fill",style:{height:"".concat(e.count/t*100,"%"),backgroundColor:e.avgRating>=4?"#2ed573":e.avgRating>=3?"#ffa502":"#ff4757"},title:"".concat(e.date,": ").concat(e.count,"条反馈, 平均评分").concat(e.avgRating.toFixed(1))}),W.jsx("div",{className:"trend-label",children:(()=>{const t=new Date(e.date);return"".concat(t.getMonth()+1,"/").concat(t.getDate())})()})]},e.date))})]})})(),i.length?W.jsxs("div",{className:"keyword-cloud",children:[W.jsx("h3",{children:"关键词分析"}),W.jsx("div",{className:"keywords",children:i.slice(0,15).map((e,t)=>W.jsxs("span",{className:"keyword keyword-".concat(e.sentiment),style:{fontSize:"".concat(Math.max(12,Math.min(24,2*e.frequency)),"px"),opacity:Math.max(.6,Math.min(1,e.frequency/10))},title:"出现频率: ".concat(e.frequency,"次, 情感倾向: ").concat("positive"===e.sentiment?"积极":"negative"===e.sentiment?"消极":"中性"),children:[e.keyword," (",e.frequency,")"]},"".concat(e.keyword,"_").concat(t)))})]}):W.jsxs("div",{className:"keyword-cloud",children:[W.jsx("h3",{children:"关键词分析"}),W.jsx("div",{className:"no-keywords",children:W.jsx("p",{children:"暂无关键词数据"})})]})]}),(()=>{if(!(null==s?void 0:s.priorityRecommendations.length))return null;const e={urgent:"#ff4757",high:"#ff6b7a",medium:"#ffa502",low:"#2ed573"};return W.jsxs("div",{className:"priority-recommendations",children:[W.jsx("h3",{children:"优化建议"}),W.jsx("div",{className:"recommendations-list",children:s.priorityRecommendations.map((t,s)=>W.jsxs("div",{className:"recommendation-card",children:[W.jsxs("div",{className:"recommendation-header",children:[W.jsx("div",{className:"priority-badge",style:{backgroundColor:e[t.priority]},children:t.priority.toUpperCase()}),W.jsx("h4",{children:t.title})]}),W.jsxs("div",{className:"recommendation-content",children:[W.jsxs("p",{children:[W.jsx("strong",{children:"影响:"})," ",t.impact]}),W.jsxs("p",{children:[W.jsx("strong",{children:"工作量:"})," ",t.effort]}),W.jsxs("p",{children:[W.jsx("strong",{children:"原因:"})," ",t.reasoning]})]})]},t.id))})]})})(),W.jsxs("div",{className:"detailed-analysis",children:[W.jsx("h3",{children:"详细分析"}),W.jsxs("div",{className:"top-issues",children:[W.jsx("h4",{children:"主要问题分类"}),s.topIssues.map((e,t)=>W.jsxs("div",{className:"issue-item",children:[W.jsxs("div",{className:"issue-header",children:[W.jsx("span",{className:"issue-category",children:e.category}),W.jsxs("span",{className:"issue-count",children:[e.count,"条"]}),W.jsxs("span",{className:"issue-rating",children:["评分: ",e.avgRating.toFixed(1)]})]}),W.jsxs("div",{className:"issue-keywords",children:["关键词: ",e.keywords.join(", ")]})]},e.category))]}),W.jsxs("div",{className:"hourly-distribution",children:[W.jsx("h4",{children:"小时分布分析"}),W.jsx("div",{className:"hourly-chart",children:Object.entries(s.timeAnalysis.hourlyDistribution).sort(([e],[t])=>parseInt(e)-parseInt(t)).map(([e,t])=>W.jsxs("div",{className:"hourly-bar",children:[W.jsx("div",{className:"hourly-count",children:t}),W.jsxs("div",{className:"hourly-time",children:[e,"时"]})]},e))})]})]})]}):W.jsx("div",{className:"no-data",children:W.jsx("p",{children:"暂无分析数据"})})]})};class Ut extends r.Component{constructor(e){super(e),t(this,"logErrorToService",(e,t)=>{e.message,e.stack,t.componentStack,(new Date).toISOString()}),t(this,"handleRetry",()=>{this.setState({hasError:!1,error:void 0,errorInfo:void 0})}),this.state={hasError:!1}}static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,t){this.setState({error:e,errorInfo:t}),this.logErrorToService(e,t)}render(){return this.state.hasError?this.props.fallback?this.props.fallback:W.jsx("div",{className:"error-boundary",children:W.jsxs("div",{className:"error-content",children:[W.jsx("div",{className:"error-icon",children:"⚠️"}),W.jsx("h2",{children:"哎呀，出现了错误"}),W.jsx("p",{children:"应用遇到了意外错误，我们已经记录了这个问题。"}),W.jsxs("div",{className:"error-actions",children:[W.jsx("button",{onClick:this.handleRetry,className:"retry-button",children:"重试"}),W.jsx("button",{onClick:()=>window.location.reload(),className:"reload-button",children:"刷新页面"})]}),!1]})}):this.props.children}}var zt,Ht=((zt=Ht||{}).FOCUS_COIN="focus_coin",zt.DISCIPLINE_TOKEN="discipline_token",zt.FUTURES_CRYSTAL="futures_crystal",zt.GOLDEN_HARVEST="golden_harvest",zt);const qt={[Ie.BASIC_FARM]:{id:Ie.BASIC_FARM,name:"基础农场盒",description:"包含基础农业物品，适合刚开始种植的农民",category:Re.AGRICULTURAL,icon:"📦",price:{currency:Ht.FOCUS_COIN,amount:100},guaranteedRarity:je.GRAY,dropRates:{[je.GRAY]:.65,[je.GREEN]:.28,[je.BLUE]:.06,[je.ORANGE]:.01,[je.GOLD]:0,[je.GOLD_RED]:0},itemPool:["wheat_seed_gray","corn_seed_gray","basic_hoe","wheat_seed_green","iron_plow","premium_wheat_seed","focus_coin_small","growth_booster"]},[Ie.PREMIUM_FARM]:{id:Ie.PREMIUM_FARM,name:"高级农场盒",description:"高质量农业装备和种子，提升农场效率",category:Re.AGRICULTURAL,icon:"🎁",price:{currency:Ht.FOCUS_COIN,amount:500},guaranteedRarity:je.GREEN,dropRates:{[je.GRAY]:.4,[je.GREEN]:.35,[je.BLUE]:.2,[je.ORANGE]:.04,[je.GOLD]:.01,[je.GOLD_RED]:0},itemPool:["wheat_seed_green","premium_wheat_seed","iron_plow","golden_wheat_seed","legendary_farm_tractor","focus_coin_large","growth_booster"],specialFeatures:{guaranteedCount:3,bonusChance:.15}},[Ie.LEGENDARY_FARM]:{id:Ie.LEGENDARY_FARM,name:"传说农场盒",description:"蕴含传说级农业装备的珍贵宝盒",category:Re.AGRICULTURAL,icon:"🏆",price:{currency:Ht.DISCIPLINE_TOKEN,amount:50},guaranteedRarity:je.BLUE,dropRates:{[je.GRAY]:.1,[je.GREEN]:.25,[je.BLUE]:.4,[je.ORANGE]:.2,[je.GOLD]:.04,[je.GOLD_RED]:.01},itemPool:["premium_wheat_seed","golden_wheat_seed","legendary_farm_tractor","mythical_harvest_altar","focus_coin_large"],specialFeatures:{guaranteedCount:5,pityTimer:20,bonusChance:.3}},[Ie.BASIC_INDUSTRIAL]:{id:Ie.BASIC_INDUSTRIAL,name:"基础工业盒",description:"包含基础工业原料和简单机械",category:Re.INDUSTRIAL,icon:"🔧",price:{currency:Ht.FOCUS_COIN,amount:120},guaranteedRarity:je.GRAY,dropRates:{[je.GRAY]:.7,[je.GREEN]:.25,[je.BLUE]:.04,[je.ORANGE]:.01,[je.GOLD]:0,[je.GOLD_RED]:0},itemPool:["iron_ore","steel_gear","processing_machine","focus_coin_small"]},[Ie.PREMIUM_INDUSTRIAL]:{id:Ie.PREMIUM_INDUSTRIAL,name:"高级工业盒",description:"先进的工业设备和高效机械",category:Re.INDUSTRIAL,icon:"⚙️",price:{currency:Ht.FOCUS_COIN,amount:600},guaranteedRarity:je.GREEN,dropRates:{[je.GRAY]:.35,[je.GREEN]:.4,[je.BLUE]:.2,[je.ORANGE]:.04,[je.GOLD]:.01,[je.GOLD_RED]:0},itemPool:["steel_gear","processing_machine","smart_factory_line","mega_factory_complex","focus_coin_large"],specialFeatures:{guaranteedCount:4,bonusChance:.2}},[Ie.LEGENDARY_INDUSTRIAL]:{id:Ie.LEGENDARY_INDUSTRIAL,name:"传说工业盒",description:"顶级工业科技的结晶",category:Re.INDUSTRIAL,icon:"🏭",price:{currency:Ht.DISCIPLINE_TOKEN,amount:60},guaranteedRarity:je.BLUE,dropRates:{[je.GRAY]:.05,[je.GREEN]:.2,[je.BLUE]:.45,[je.ORANGE]:.25,[je.GOLD]:.04,[je.GOLD_RED]:.01},itemPool:["processing_machine","smart_factory_line","mega_factory_complex","focus_coin_large"],specialFeatures:{guaranteedCount:5,pityTimer:15,bonusChance:.35}},[Ie.FUTURES_MYSTERY]:{id:Ie.FUTURES_MYSTERY,name:"期货神秘盒",description:"基于期货市场波动的神秘宝盒，内容随市场变化",category:"mixed",icon:"📊",price:{currency:Ht.FUTURES_CRYSTAL,amount:10},guaranteedRarity:je.GREEN,dropRates:{[je.GRAY]:.3,[je.GREEN]:.3,[je.BLUE]:.25,[je.ORANGE]:.12,[je.GOLD]:.02,[je.GOLD_RED]:.01},itemPool:["wheat_seed_green","premium_wheat_seed","golden_wheat_seed","steel_gear","processing_machine","smart_factory_line","focus_coin_large"],specialFeatures:{guaranteedCount:3,bonusChance:.25,limitedTime:!0}},[Ie.GOLDEN_TREASURE]:{id:Ie.GOLDEN_TREASURE,name:"金色宝藏盒",description:"传说中的金色宝藏，蕴含最珍贵的物品",category:"mixed",icon:"💎",price:{currency:Ht.GOLDEN_HARVEST,amount:5},guaranteedRarity:je.ORANGE,dropRates:{[je.GRAY]:0,[je.GREEN]:.05,[je.BLUE]:.2,[je.ORANGE]:.5,[je.GOLD]:.2,[je.GOLD_RED]:.05},itemPool:["golden_wheat_seed","legendary_farm_tractor","mythical_harvest_altar","smart_factory_line","mega_factory_complex","focus_coin_large"],specialFeatures:{guaranteedCount:7,pityTimer:10,bonusChance:.5}},[Ie.SYNTHESIS_BOX]:{id:Ie.SYNTHESIS_BOX,name:"合成专用盒",description:"专门用于合成的材料盒，提供各种合成原料",category:"mixed",icon:"🧪",price:{currency:Ht.FOCUS_COIN,amount:200},guaranteedRarity:je.GRAY,dropRates:{[je.GRAY]:.5,[je.GREEN]:.35,[je.BLUE]:.12,[je.ORANGE]:.02,[je.GOLD]:.01,[je.GOLD_RED]:0},itemPool:["wheat_seed_gray","corn_seed_gray","iron_ore","steel_gear","growth_booster","wheat_seed_green","premium_wheat_seed"],specialFeatures:{guaranteedCount:5,bonusChance:.1}}};class Yt{static generateRarity(e){const t=qt[e],s=Math.random();let a=0;for(const[i,n]of Object.entries(t.dropRates))if(a+=n,s<=a)return i;return je.GRAY}static selectRandomItem(e,t){let s=e;t&&(s=e.filter(e=>{const s=Xe[e];return s&&s.rarity===t})),0===s.length&&(s=e);const a=s[Math.floor(Math.random()*s.length)];return Xe[a]||null}static generateSingleItem(e,t,s=!1){const a=qt[e],i=t||this.generateRarity(e),n=this.selectRandomItem(a.itemPool,i);if(!n)return null;let r=1;if(n.stackable)switch(n.rarity){case je.GRAY:r=Math.floor(3*Math.random())+1;break;case je.GREEN:r=Math.floor(2*Math.random())+1;break;default:r=1}return{item:n,quantity:r,isGuaranteed:!!t,isBonus:s}}static generateLootbox(e,t){var s,a;const i=qt[e],n=Date.now();if(t){const e=i.price.amount,s=t[i.price.currency]||0;if(s<e)throw new Error("货币不足！需要 ".concat(e," ").concat(i.price.currency,"，但只有 ").concat(s))}const r=[],o={[je.GRAY]:0,[je.GREEN]:0,[je.BLUE]:0,[je.ORANGE]:0,[je.GOLD]:0,[je.GOLD_RED]:0},c=(null==(s=i.specialFeatures)?void 0:s.guaranteedCount)||1;for(let d=0;d<c;d++){let t;0===d&&i.guaranteedRarity&&(t=i.guaranteedRarity);const s=this.generateSingleItem(e,t);s&&(r.push(s),o[s.item.rarity]+=s.quantity)}if((null==(a=i.specialFeatures)?void 0:a.bonusChance)&&Math.random()<=i.specialFeatures.bonusChance){const t=this.generateSingleItem(e,void 0,!0);t&&(r.push(t),o[t.item.rarity]+=t.quantity)}const l=r.reduce((e,t)=>e+t.item.value*t.quantity,0);return{lootboxType:e,items:r,totalValue:l,rarityBreakdown:o,openedAt:n,cost:i.price}}static generateMultipleLootboxes(e,t,s){const a=[];for(let n=0;n<t;n++)try{const t=this.generateLootbox(e,s);a.push(t),s&&(s[t.cost.currency]-=t.cost.amount)}catch(i){break}return a}static getLootboxPreview(e){const t=qt[e],s=t.itemPool.map(e=>Xe[e]).filter(e=>null!=e),a=Object.entries(t.dropRates).reduce((e,[t,a])=>{const i=s.filter(e=>e.rarity===t);return 0===i.length?e:e+i.reduce((e,t)=>e+t.value,0)/i.length*a},0);return{config:t,possibleItems:s,expectedValue:Math.round(a)}}}const Wt={[je.GRAY]:"普通",[je.GREEN]:"优秀",[je.BLUE]:"稀有",[je.ORANGE]:"史诗",[je.GOLD]:"传说",[je.GOLD_RED]:"神话"},Vt={[Ht.FOCUS_COIN]:"专注币",[Ht.DISCIPLINE_TOKEN]:"自律代币",[Ht.FUTURES_CRYSTAL]:"期货水晶",[Ht.GOLDEN_HARVEST]:"金色收获"};je.GRAY,je.GREEN,je.BLUE,je.ORANGE,je.GOLD,je.GOLD_RED;const Qt={[je.GRAY]:"灰色",[je.GREEN]:"绿色",[je.BLUE]:"蓝色",[je.ORANGE]:"橙色",[je.GOLD]:"金色",[je.GOLD_RED]:"金红色"},Kt=({inventorySystem:e,onClose:t})=>{const[s,a]=r.useState(e.getState().items),[i,n]=r.useState([{id:1,item:null,position:{x:150,y:200}},{id:2,item:null,position:{x:250,y:200}},{id:3,item:null,position:{x:350,y:200}},{id:4,item:null,position:{x:200,y:280}},{id:5,item:null,position:{x:300,y:280}}]),[o,c]=r.useState(null),[l,d]=r.useState(null),[h,u]=r.useState(!1),m=r.useRef(null),p=r.useRef(null);A.useEffect(()=>e.subscribe(e=>{a(e.items)}),[e]);const g=r.useCallback((e,t)=>{const s=new Image;s.src="data:image/svg+xml,".concat(encodeURIComponent('\n      <svg xmlns="http://www.w3.org/2000/svg" width="60" height="60" viewBox="0 0 60 60">\n        <rect width="60" height="60" fill="'.concat(ke[e.rarity],'" opacity="0.8" rx="8"/>\n        <text x="30" y="40" text-anchor="middle" font-size="24">').concat(e.icon,"</text>\n      </svg>\n    "))),t.dataTransfer.setDragImage(s,30,30),t.dataTransfer.effectAllowed="move",c({item:e,dragImage:s})},[]),y=r.useCallback(()=>{c(null)},[]),f=r.useCallback((e,t)=>{if(e.preventDefault(),!o)return;n(e=>e.map(e=>e.id===t?{...e,item:o.item}:e));const s=e.currentTarget;s.classList.add("item-dropped"),setTimeout(()=>s.classList.remove("item-dropped"),500),c(null)},[o]),x=r.useCallback(e=>{n(t=>t.map(t=>t.id===e?{...t,item:null}:t))},[]),v=r.useCallback(e=>{e.preventDefault(),e.dataTransfer.dropEffect="move"},[]),b=r.useCallback(()=>{n(e=>e.map(e=>({...e,item:null})))},[]),S=r.useCallback(async()=>{const t=i.filter(e=>null!==e.item);t.length<2?alert("至少需要2个物品才能进行合成！"):(u(!0),setTimeout(()=>{const s=e.getAvailableRecipes(),a=t.map(e=>e.item);let i=null;for(const e of s)if(e.requiredItems.every(e=>a.filter(t=>{const s=t.rarity===e.rarity,a=!e.category||t.category===e.category;return s&&a}).length>=e.quantity)){i=e;break}if(i){const t=e.synthesize(i.id);d(t),t.success&&b()}else d({success:!1,consumedItems:a,message:"合成失败：找不到适合的配方"});u(!1),setTimeout(()=>d(null),3e3)},1500))},[i,e,b]);return W.jsxs("div",{className:"synthesis-workbench-modal fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center",style:{zIndex:15100},onClick:e=>{e.target===e.currentTarget&&t()},children:[W.jsxs("div",{className:"bg-gradient-to-b from-amber-50 to-amber-100 rounded-lg p-6 max-w-4xl w-full mx-4 relative",onClick:e=>e.stopPropagation(),children:[W.jsx("button",{onClick:t,className:"absolute top-4 right-4 text-gray-500 hover:text-gray-700 text-2xl",children:"×"}),W.jsxs("div",{className:"text-center mb-6",children:[W.jsx("h2",{className:"text-2xl font-bold text-amber-800 mb-2",children:"🧪 炼金工作台"}),W.jsx("p",{className:"text-amber-600",children:"拖拽物品到合成区域进行炼金合成"})]}),W.jsxs("div",{className:"flex gap-6",children:[W.jsxs("div",{className:"flex-1",children:[W.jsx("h3",{className:"text-lg font-semibold text-amber-700 mb-3",children:"📦 物品库存"}),W.jsxs("div",{className:"bg-white rounded-lg p-4 border-2 border-amber-200 max-h-96 overflow-y-auto",style:{minHeight:"300px"},children:[W.jsx("div",{className:"grid grid-cols-5 gap-2",children:(()=>{const e=[];return s.forEach(t=>{for(let s=0;s<t.quantity;s++)e.push({item:t,index:s})}),e.map(({item:e,index:t},s)=>W.jsxs("div",{draggable:!0,onDragStart:t=>g(e,t),onDragEnd:y,className:"inventory-slot group cursor-grab active:cursor-grabbing transform transition-all duration-200 hover:scale-105",style:{background:"linear-gradient(135deg, ".concat(ke[e.rarity],"15, ").concat(ke[e.rarity],"25)"),border:"3px solid ".concat(ke[e.rarity]),borderRadius:"12px",aspectRatio:"1",position:"relative",minHeight:"70px",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",padding:"6px",boxShadow:"0 2px 8px ".concat(ke[e.rarity],"30, inset 0 1px 0 rgba(255,255,255,0.2)")},title:"".concat(e.name," (").concat(Qt[e.rarity],")"),children:[W.jsx("div",{className:"absolute inset-0 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300",style:{background:"radial-gradient(circle at center, ".concat(ke[e.rarity],"40, transparent 70%)"),animation:"slot-glow 2s ease-in-out infinite alternate"}}),W.jsxs("div",{className:"relative z-10 text-center w-full",children:[W.jsx("div",{className:"text-lg mb-1 drop-shadow-sm",children:e.icon}),W.jsx("div",{className:"text-xs font-medium text-gray-800 truncate leading-tight",children:e.name}),W.jsx("div",{className:"absolute top-0.5 left-0.5",children:W.jsx("span",{className:"text-xs font-bold px-1 py-0.5 rounded",style:{backgroundColor:"".concat(ke[e.rarity],"80"),color:"white",fontSize:"8px"},children:Qt[e.rarity]})})]})]},"".concat(e.id,"-").concat(t,"-").concat(s)))})()}),0===s.length&&W.jsxs("div",{className:"text-center py-8 text-gray-500",children:[W.jsx("div",{className:"text-4xl mb-2",children:"📦"}),W.jsx("p",{children:"库存为空"}),W.jsx("p",{className:"text-xs",children:"请先收集一些物品"})]})]})]}),W.jsxs("div",{className:"flex-1",children:[W.jsx("h3",{className:"text-lg font-semibold text-amber-700 mb-3",children:"⚗️ 合成区域"}),W.jsxs("div",{ref:m,className:"bg-gradient-to-br from-amber-200 to-amber-300 rounded-lg p-4 border-2 border-amber-400 relative",style:{height:"400px"},children:[i.map(e=>W.jsx("div",{className:"synthesis-slot absolute w-16 h-16 border-4 border-dashed border-amber-600 rounded-lg bg-amber-100 flex items-center justify-center transition-all duration-300 ".concat(e.item?"has-item":""),style:{left:e.position.x,top:e.position.y,borderColor:e.item?ke[e.item.rarity]:"#d97706",backgroundColor:e.item?"".concat(ke[e.item.rarity],"30"):"#FEF3C7"},onDragOver:v,onDrop:t=>f(t,e.id),onClick:()=>e.item&&x(e.id),children:e.item?W.jsxs("div",{className:"text-center cursor-pointer hover:scale-110 transition-transform",children:[W.jsx("div",{className:"text-2xl",children:e.item.icon}),W.jsx("div",{className:"text-xs mt-1 opacity-75",children:e.item.name})]}):W.jsxs("div",{className:"text-amber-600 text-xs text-center opacity-60",children:["拖拽",W.jsx("br",{}),"物品"]})},e.id)),W.jsx("div",{ref:p,className:"result-slot absolute w-20 h-20 border-4 border-solid rounded-lg flex items-center justify-center transition-all duration-500 ".concat((null==l?void 0:l.success)?"has-result":""," ").concat(h?"animate-pulse":""),style:{left:225,top:100,backgroundColor:(null==l?void 0:l.success)&&l.resultItem?"".concat(ke[l.resultItem.rarity],"40"):"#FEF08A",borderColor:(null==l?void 0:l.success)&&l.resultItem?ke[l.resultItem.rarity]:"#EAB308"},children:l&&l.success&&l.resultItem?W.jsxs("div",{className:"text-center animate-bounce",children:[W.jsx("div",{className:"text-3xl mb-1",children:l.resultItem.icon}),W.jsx("div",{className:"text-xs font-bold",style:{color:ke[l.resultItem.rarity]},children:Qt[l.resultItem.rarity]})]}):h?W.jsxs("div",{className:"text-center",children:[W.jsx("div",{className:"text-3xl animate-spin mb-1",children:"⚡"}),W.jsx("div",{className:"text-xs text-amber-600",children:"炼金中"})]}):W.jsxs("div",{className:"text-yellow-600 text-xs text-center opacity-60",children:["结果",W.jsx("br",{}),"物品"]})}),W.jsx("div",{className:"absolute bottom-4 left-1/2 transform -translate-x-1/2",children:W.jsx("button",{onClick:S,disabled:h||i.filter(e=>e.item).length<2,className:"synthesize-button px-6 py-3 bg-gradient-to-r from-yellow-400 to-orange-500 text-white font-bold rounded-lg shadow-lg hover:from-yellow-500 hover:to-orange-600 disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-105 transition-all",children:h?W.jsxs("span",{className:"flex items-center",children:[W.jsx("span",{className:"animate-spin mr-2",children:"⚡"}),"炼金中..."]}):"🔥 开始炼金"})}),W.jsx("button",{onClick:b,className:"absolute top-4 right-4 px-3 py-1 bg-red-500 text-white text-sm rounded hover:bg-red-600 transition-colors",children:"清空"})]})]})]}),l&&W.jsx("div",{className:"fixed inset-0 flex items-center justify-center z-60 pointer-events-none",children:W.jsxs("div",{className:"synthesis-result-modal bg-white p-8 rounded-xl shadow-2xl border-4 pointer-events-auto max-w-md mx-4 relative overflow-hidden",children:[W.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-purple-100 via-blue-50 to-green-100 opacity-80"}),l.success&&W.jsxs(W.Fragment,{children:[W.jsx("div",{className:"celebration-particles absolute inset-0 pointer-events-none",children:Array.from({length:20}).map((e,t)=>W.jsx("div",{className:"particle absolute w-2 h-2 rounded-full bg-gradient-to-r from-yellow-400 to-orange-500",style:{left:"".concat(100*Math.random(),"%"),top:"".concat(100*Math.random(),"%"),animationDelay:"".concat(2*Math.random(),"s"),animationDuration:"".concat(2+2*Math.random(),"s")}},t))}),W.jsx("div",{className:"flash-overlay absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-50 transform -skew-x-12 animate-flash"})]}),W.jsxs("div",{className:"text-center relative z-10",children:[W.jsx("div",{className:"text-6xl mb-4 animate-bounce-enhanced",children:l.success?"✨":"💥"}),W.jsx("h3",{className:"text-xl font-bold mb-4 ".concat(l.success?"text-green-600":"text-red-600"," animate-pulse-glow"),children:l.message}),l.success&&l.resultItem&&W.jsxs("div",{className:"mt-6 p-4 border-4 rounded-xl bg-white shadow-inner relative",style:{borderColor:ke[l.resultItem.rarity]},children:[W.jsx("div",{className:"absolute inset-0 rounded-xl animate-ring-glow",style:{background:"radial-gradient(circle, ".concat(ke[l.resultItem.rarity],"30, transparent 70%)"),filter:"blur(8px)"}}),W.jsxs("div",{className:"relative z-10",children:[W.jsx("div",{className:"text-5xl mb-3 animate-icon-celebrate",children:l.resultItem.icon}),W.jsx("div",{className:"font-bold text-lg text-gray-800",children:l.resultItem.name}),W.jsx("div",{className:"text-sm font-bold mt-2 animate-text-shimmer",style:{color:ke[l.resultItem.rarity]},children:Qt[l.resultItem.rarity]})]}),(l.resultItem.rarity===je.GOLD||l.resultItem.rarity===je.GOLD_RED)&&W.jsx("div",{className:"absolute inset-0 pointer-events-none",children:Array.from({length:8}).map((e,t)=>W.jsx("div",{className:"absolute text-2xl animate-sparkle-orbit",style:{left:"50%",top:"50%",transformOrigin:"0 40px",animationDelay:"".concat(.2*t,"s"),transform:"rotate(".concat(45*t,"deg)")},children:"✨"},t))})]})]})]})}),h&&W.jsx("div",{className:"fixed inset-0 pointer-events-none z-50",children:W.jsxs("div",{className:"synthesis-process-effects absolute inset-0 flex items-center justify-center",children:[W.jsx("div",{className:"energy-ring w-64 h-64 border-4 border-blue-400 rounded-full animate-spin-slow opacity-60"}),W.jsx("div",{className:"energy-ring w-48 h-48 border-4 border-purple-400 rounded-full animate-spin-reverse opacity-80 absolute"}),W.jsx("div",{className:"energy-ring w-32 h-32 border-4 border-yellow-400 rounded-full animate-spin opacity-90 absolute"}),W.jsx("div",{className:"energy-core absolute w-16 h-16 bg-gradient-to-r from-blue-400 via-purple-500 to-pink-400 rounded-full animate-pulse-intense"}),Array.from({length:12}).map((e,t)=>W.jsx("div",{className:"energy-particle absolute w-3 h-3 bg-yellow-400 rounded-full animate-orbit",style:{animationDelay:"".concat(.1*t,"s"),transform:"rotate(".concat(30*t,"deg) translateX(100px)")}},t))]})})]}),W.jsx("style",{children:"\n        .synthesis-result-modal {\n          animation: result-modal-appear 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);\n        }\n\n        .particle {\n          animation: particle-float 3s ease-out infinite;\n        }\n\n        .flash-overlay {\n          animation: flash-sweep 0.8s ease-out;\n        }\n\n        .energy-ring {\n          border-style: dashed;\n          border-width: 3px;\n        }\n\n        .energy-core {\n          filter: blur(1px);\n        }\n\n        @keyframes result-modal-appear {\n          0% {\n            opacity: 0;\n            transform: scale(0.3) rotate(180deg);\n          }\n          70% {\n            transform: scale(1.1) rotate(-10deg);\n          }\n          100% {\n            opacity: 1;\n            transform: scale(1) rotate(0deg);\n          }\n        }\n\n        @keyframes particle-float {\n          0% {\n            opacity: 1;\n            transform: translateY(0) scale(0);\n          }\n          50% {\n            opacity: 1;\n            transform: translateY(-20px) scale(1);\n          }\n          100% {\n            opacity: 0;\n            transform: translateY(-40px) scale(0) rotate(180deg);\n          }\n        }\n\n        @keyframes flash-sweep {\n          0% {\n            transform: translateX(-100%) skewX(-12deg);\n            opacity: 0;\n          }\n          50% {\n            opacity: 1;\n          }\n          100% {\n            transform: translateX(100%) skewX(-12deg);\n            opacity: 0;\n          }\n        }\n\n        @keyframes bounce-enhanced {\n          0%, 20%, 53%, 80%, 100% {\n            transform: translate3d(0,0,0) scale(1);\n          }\n          40%, 43% {\n            transform: translate3d(0, -15px, 0) scale(1.1);\n          }\n          70% {\n            transform: translate3d(0, -8px, 0) scale(1.05);\n          }\n          90% {\n            transform: translate3d(0, -3px, 0) scale(1.02);\n          }\n        }\n\n        @keyframes pulse-glow {\n          0%, 100% {\n            text-shadow: 0 0 5px currentColor;\n          }\n          50% {\n            text-shadow: 0 0 20px currentColor, 0 0 30px currentColor;\n          }\n        }\n\n        @keyframes ring-glow {\n          0%, 100% {\n            opacity: 0.3;\n            transform: scale(1);\n          }\n          50% {\n            opacity: 0.8;\n            transform: scale(1.1);\n          }\n        }\n\n        @keyframes icon-celebrate {\n          0%, 100% {\n            transform: scale(1) rotate(0deg);\n          }\n          25% {\n            transform: scale(1.2) rotate(-5deg);\n          }\n          50% {\n            transform: scale(1.3) rotate(5deg);\n          }\n          75% {\n            transform: scale(1.1) rotate(-2deg);\n          }\n        }\n\n        @keyframes text-shimmer {\n          0% {\n            background-position: -200% center;\n          }\n          100% {\n            background-position: 200% center;\n          }\n        }\n\n        @keyframes sparkle-orbit {\n          0% {\n            transform: rotate(0deg) translateX(40px) rotate(0deg);\n            opacity: 0;\n          }\n          10% {\n            opacity: 1;\n          }\n          90% {\n            opacity: 1;\n          }\n          100% {\n            transform: rotate(360deg) translateX(40px) rotate(-360deg);\n            opacity: 0;\n          }\n        }\n\n        @keyframes spin-slow {\n          from {\n            transform: rotate(0deg);\n          }\n          to {\n            transform: rotate(360deg);\n          }\n        }\n\n        @keyframes spin-reverse {\n          from {\n            transform: rotate(360deg);\n          }\n          to {\n            transform: rotate(0deg);\n          }\n        }\n\n        @keyframes pulse-intense {\n          0%, 100% {\n            transform: scale(1);\n            opacity: 0.7;\n          }\n          50% {\n            transform: scale(1.3);\n            opacity: 1;\n          }\n        }\n\n        @keyframes orbit {\n          0% {\n            transform: rotate(0deg) translateX(100px) rotate(0deg);\n            opacity: 1;\n          }\n          100% {\n            transform: rotate(360deg) translateX(100px) rotate(-360deg);\n            opacity: 0.3;\n          }\n        }\n\n        .animate-bounce-enhanced {\n          animation: bounce-enhanced 2s infinite;\n        }\n\n        .animate-pulse-glow {\n          animation: pulse-glow 2s ease-in-out infinite;\n        }\n\n        .animate-ring-glow {\n          animation: ring-glow 2s ease-in-out infinite;\n        }\n\n        .animate-icon-celebrate {\n          animation: icon-celebrate 1.5s ease-in-out;\n        }\n\n        .animate-text-shimmer {\n          background: linear-gradient(90deg, currentColor, white, currentColor);\n          background-size: 200% 100%;\n          animation: text-shimmer 2s ease-in-out infinite;\n          background-clip: text;\n          -webkit-background-clip: text;\n        }\n\n        .animate-sparkle-orbit {\n          animation: sparkle-orbit 3s linear infinite;\n        }\n\n        .animate-spin-slow {\n          animation: spin-slow 4s linear infinite;\n        }\n\n        .animate-spin-reverse {\n          animation: spin-reverse 3s linear infinite;\n        }\n\n        .animate-pulse-intense {\n          animation: pulse-intense 1.5s ease-in-out infinite;\n        }\n\n        .animate-orbit {\n          animation: orbit 2s linear infinite;\n        }\n      "})]})},Xt=({inventorySystem:e,onClose:t})=>{const[s,a]=r.useState(e.getState()),[i,n]=r.useState("inventory"),[o,c]=r.useState(null),[l,d]=r.useState(null),[h,u]=r.useState(!1);r.useEffect(()=>e.subscribe(a),[e]);const m=e.getAvailableRecipes();return W.jsx("div",{className:"magical-inventory-modal",style:{position:"fixed",top:0,left:0,right:0,bottom:0,zIndex:15e3,display:"flex",alignItems:"center",justifyContent:"center",background:"rgba(0, 0, 0, 0.7)",backdropFilter:"blur(5px)",padding:"20px"},onClick:e=>{e.target===e.currentTarget&&t()},children:W.jsxs("div",{className:"magical-inventory-window",style:{width:"90vw",maxWidth:"1200px",height:"85vh",maxHeight:"800px",background:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",borderRadius:"16px",position:"relative",display:"flex",flexDirection:"column",overflow:"hidden",boxShadow:"0 25px 50px rgba(0, 0, 0, 0.5), 0 0 0 1px rgba(255, 255, 255, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.2)",animation:"fadeInScale 0.3s ease-out"},onClick:e=>e.stopPropagation(),children:[W.jsxs("div",{className:"absolute inset-0 opacity-20 pointer-events-none",children:[W.jsx("div",{className:"magical-particles"}),W.jsx("div",{className:"floating-orbs"})]}),W.jsxs("div",{className:"magical-header relative z-10 p-4 text-center border-b border-white border-opacity-20 flex-shrink-0",children:[W.jsx("h2",{className:"text-2xl font-bold text-white mb-1 drop-shadow-lg",children:"✨ 魔法物品背包 ✨"}),W.jsxs("p",{className:"text-white opacity-80 text-sm",children:[s.usedSlots,"/",s.maxSlots," 槽位已使用"]}),W.jsx("button",{onClick:e=>{e.stopPropagation(),t()},className:"absolute top-3 right-4 text-white hover:text-red-300 text-2xl font-bold transition-colors transform hover:scale-110 hover:rotate-90 duration-300",style:{width:"32px",height:"32px",display:"flex",alignItems:"center",justifyContent:"center",zIndex:10},children:"×"})]}),W.jsxs("div",{className:"magical-tabs relative z-10 flex flex-col items-center gap-3 p-4 border-b border-white border-opacity-20 flex-shrink-0",children:[W.jsxs("div",{className:"tab-buttons flex bg-black bg-opacity-30 rounded-full p-1",children:[W.jsx("button",{className:"px-3 py-2 text-xs rounded-full transition-all ".concat("inventory"===i?"bg-white text-purple-600 font-bold shadow-lg":"text-white hover:bg-white hover:bg-opacity-20"),onClick:e=>{e.stopPropagation(),n("inventory")},children:"📦 物品背包"}),W.jsxs("button",{className:"px-3 py-2 text-xs rounded-full transition-all ".concat("synthesis"===i?"bg-white text-purple-600 font-bold shadow-lg":"text-white hover:bg-white hover:bg-opacity-20"),onClick:e=>{e.stopPropagation(),n("synthesis")},children:["⚗️ 传统合成 ",m.length>0&&"(".concat(m.length,")")]})]}),W.jsxs("div",{className:"action-buttons flex flex-wrap gap-2 justify-center",children:[W.jsx("button",{className:"magical-btn px-3 py-1 bg-gradient-to-r from-yellow-400 to-orange-500 text-white font-bold text-xs rounded-lg shadow-lg hover:from-yellow-500 hover:to-orange-600 transform hover:scale-105 transition-all",onClick:e=>{e.stopPropagation(),u(!0)},children:"🧪 拖拽工作台"}),W.jsx("button",{className:"magical-btn px-3 py-1 bg-gradient-to-r from-green-400 to-cyan-500 text-white font-bold text-xs rounded-lg shadow-lg hover:from-green-500 hover:to-cyan-600 transform hover:scale-105 transition-all",onClick:t=>{t.stopPropagation(),(()=>{let t=0;m.forEach(s=>{e.synthesize(s.id).success&&t++}),d(t>0?{success:!0,consumedItems:[],message:"批量合成成功！完成了 ".concat(t," 次合成")}:{success:!1,consumedItems:[],message:"没有可用的合成配方"}),setTimeout(()=>d(null),3e3)})()},disabled:0===m.length,children:"⚡ 批量合成"}),W.jsx("button",{className:"magical-btn px-3 py-1 bg-gradient-to-r from-indigo-400 to-purple-500 text-white font-bold text-xs rounded-lg shadow-lg hover:from-indigo-500 hover:to-purple-600 transform hover:scale-105 transition-all",onClick:e=>{e.stopPropagation(),alert("整理背包功能即将推出！")},children:"📋 整理背包"})]})]}),W.jsx("div",{className:"magical-content flex-1 p-4 overflow-y-auto relative z-10",children:W.jsx("div",{className:"content-wrapper bg-white bg-opacity-95 rounded-xl p-4 shadow-xl backdrop-blur-sm h-full",children:"inventory"===i?W.jsxs("div",{className:"h-full flex flex-col",children:[W.jsxs("div",{className:"inventory-info mb-4 text-center",children:[W.jsx("p",{className:"text-sm text-gray-600 font-medium",children:"🎒 物品展示：每个物品单独显示，鼠标悬停查看详情"}),W.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"💎 品质越高，边框颜色越亮丽，还有特殊光效"})]}),W.jsx("div",{className:"flex-1 overflow-y-auto",children:(()=>{const t=e.getState().items;if(0===t.length)return W.jsxs("div",{className:"empty-inventory text-center py-12 text-gray-500",children:[W.jsx("div",{className:"text-6xl mb-4",children:"📦"}),W.jsx("h3",{className:"text-lg font-bold mb-2 text-gray-700",children:"魔法背包空空如也"}),W.jsx("p",{className:"text-sm",children:"快去开启神秘盲盒，收集珍贵物品吧！"})]});const s=[];return t.forEach(e=>{for(let t=0;t<e.quantity;t++)s.push({item:e,index:t})}),W.jsx("div",{style:{display:"grid",gridTemplateColumns:"repeat(8, 1fr)",gap:"12px",padding:"16px",backgroundColor:"#f8f9fa",borderRadius:"8px"},children:s.map(({item:e,index:t},s)=>W.jsxs("div",{style:{backgroundColor:"#ffffff",border:"3px solid ".concat(ke[e.rarity]||"#ccc"),borderRadius:"12px",minHeight:"100px",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",padding:"8px",position:"relative",cursor:"pointer",transition:"transform 0.2s ease, box-shadow 0.2s ease"},title:"".concat(e.name," (").concat(Qt[e.rarity],")"),onMouseEnter:t=>{t.currentTarget.style.transform="scale(1.05)",t.currentTarget.style.boxShadow="0 8px 16px ".concat(ke[e.rarity],"40")},onMouseLeave:e=>{e.currentTarget.style.transform="scale(1)",e.currentTarget.style.boxShadow="none"},children:[W.jsx("div",{style:{fontSize:"2rem",marginBottom:"4px",filter:"drop-shadow(0 2px 4px rgba(0,0,0,0.1))"},children:e.icon}),W.jsx("div",{style:{fontSize:"10px",fontWeight:"bold",textAlign:"center",color:"#333",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap",width:"100%",textShadow:"0 1px 2px rgba(255,255,255,0.8)"},children:e.name}),W.jsx("div",{style:{position:"absolute",top:"4px",left:"4px",fontSize:"8px",fontWeight:"bold",color:ke[e.rarity]||"#666",backgroundColor:"rgba(255,255,255,0.9)",padding:"2px 4px",borderRadius:"4px",textShadow:"none"},children:Qt[e.rarity]||"普通"}),W.jsx("div",{style:{position:"absolute",inset:"0",borderRadius:"12px",background:"linear-gradient(45deg, ".concat(ke[e.rarity],"20, transparent 50%, ").concat(ke[e.rarity],"20)"),opacity:"0.3",pointerEvents:"none"}})]},"".concat(e.id,"-").concat(t,"-").concat(s)))})})()})]}):W.jsxs("div",{className:"synthesis-tab space-y-4",children:[W.jsxs("div",{className:"text-center mb-4",children:[W.jsx("h3",{className:"text-lg font-bold text-blue-700",children:"物品合成"}),W.jsx("p",{className:"text-sm text-gray-600",children:"使用相同或特定品质的物品进行合成升级"})]}),0===m.length?W.jsxs("div",{className:"text-center py-8 text-gray-500",children:[W.jsx("div",{className:"text-4xl mb-2",children:"🔧"}),W.jsx("p",{children:"暂无可用的合成配方"}),W.jsx("p",{className:"text-xs",children:"收集更多物品来解锁合成功能"})]}):W.jsx("div",{className:"recipes-list space-y-3",children:m.map(t=>W.jsx("div",{className:"recipe-card p-3 border border-gray-300 rounded hover:border-blue-400 cursor-pointer transition-colors",onClick:()=>c(t),children:W.jsxs("div",{className:"flex justify-between items-start",children:[W.jsxs("div",{className:"flex-1",children:[W.jsx("h4",{className:"font-bold text-sm",children:t.name}),W.jsx("p",{className:"text-xs text-gray-600 mb-2",children:t.description}),W.jsxs("div",{className:"requirements text-xs",children:[W.jsx("span",{className:"font-medium",children:"需要材料："}),t.requiredItems.map((e,t)=>W.jsxs("span",{className:"ml-1",children:[t>0&&" + ",W.jsxs("span",{style:{color:ke[e.rarity]},children:[e.quantity,"个",Qt[e.rarity],e.category&&"(".concat(e.category===Re.AGRICULTURAL?"农产品":"工业品",")")]})]},t))]}),W.jsxs("div",{className:"result text-xs mt-1",children:[W.jsx("span",{className:"font-medium",children:"产出："}),W.jsxs("span",{style:{color:ke[t.resultRarity]},children:["1个",Qt[t.resultRarity],"物品"]}),W.jsxs("span",{className:"text-gray-500 ml-2",children:["(成功率: ",(100*t.successRate).toFixed(0),"%)"]})]})]}),W.jsx("button",{className:"synthesize-btn px-3 py-1 bg-blue-500 text-white text-xs rounded hover:bg-blue-600 transition-colors",onClick:s=>{s.stopPropagation(),(t=>{const s=e.synthesize(t);d(s),setTimeout(()=>d(null),3e3)})(t.id)},children:"合成"})]})},t.id))}),l&&W.jsx("div",{className:"synthesis-result-modal fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center",style:{zIndex:15200},children:W.jsx("div",{className:"bg-white p-6 rounded-lg max-w-sm mx-4",children:W.jsxs("div",{className:"text-center",children:[W.jsx("div",{className:"text-4xl mb-2",children:l.success?"✨":"💥"}),W.jsx("h3",{className:"text-lg font-bold mb-2 ".concat(l.success?"text-green-600":"text-red-600"),children:l.message}),l.success&&l.resultItem&&W.jsxs("div",{className:"result-item mb-3",children:[W.jsx("div",{className:"text-3xl mb-1",children:l.resultItem.icon}),W.jsx("div",{className:"font-medium",children:l.resultItem.name}),W.jsx("div",{className:"text-sm font-bold",style:{color:ke[l.resultItem.rarity]},children:Qt[l.resultItem.rarity]})]}),W.jsxs("div",{className:"consumed-items text-xs text-gray-600 mb-4",children:[W.jsx("span",{className:"font-medium",children:"消耗材料："}),l.consumedItems.map((e,t)=>W.jsxs("div",{children:[e.icon," ",e.name," ×",e.quantity]},t))]}),W.jsx("button",{className:"close-btn px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors",onClick:()=>d(null),children:"确定"})]})})})]})})}),h&&W.jsx("div",{className:"fixed inset-0",style:{zIndex:15100},children:W.jsx(Kt,{inventorySystem:e,onClose:()=>{u(!1)}})})]})})},Jt={[Ht.FOCUS_COIN]:1e4,[Ht.DISCIPLINE_TOKEN]:500,[Ht.FUTURES_CRYSTAL]:100,[Ht.GOLDEN_HARVEST]:20},Zt=({className:e="",onItemsReceived:t,itemManager:s})=>{const[a,i]=r.useState(Jt),[n,o]=r.useState(Ie.BASIC_FARM),[c,l]=r.useState([]),[d,h]=r.useState(!1),[u,m]=r.useState(!1),[p,g]=r.useState(!1),[y,f]=r.useState(!1),[x,v]=r.useState(null),[b]=r.useState(()=>s?null:new Je(200)),S=async(e=1)=>{h(!0);try{await new Promise(e=>setTimeout(e,800));const r=Yt.generateMultipleLootboxes(n,e,{...a});if(r.length>0){l(e=>[...r,...e]);const e=[];r.forEach(t=>{t.items.forEach(t=>{const a={id:"".concat(t.item.id,"_").concat(Date.now(),"_").concat(Math.random()),itemId:t.item.id,name:t.item.name,icon:t.item.icon,rarity:t.item.rarity,category:t.item.category,type:t.item.type,quantity:t.quantity,description:t.item.description||"",obtainedAt:Date.now()};e.push(a),s?s.addManualItem({id:a.id,name:a.name,description:a.description,category:a.category,type:a.type,rarity:a.rarity,icon:a.icon,value:t.item.value||10,quantity:a.quantity,stackable:!0,tradeable:!0,synthesizable:!0,source:{type:"lootbox",timestamp:Date.now()}}):b&&b.addItem(t.item,t.quantity)})}),t&&t(e);const n={...a};if(r.forEach(e=>{n[e.cost.currency]-=e.cost.amount}),i(n),1===r.length)v(r[0]),f(!0);else{const e=r.flatMap(e=>e.items),t={[je.GRAY]:0,[je.GREEN]:0,[je.BLUE]:0,[je.ORANGE]:0,[je.GOLD]:0,[je.GOLD_RED]:0};e.forEach(e=>{t[e.item.rarity]+=e.quantity});const s={lootboxType:r[0].lootboxType,items:e,totalValue:r.reduce((e,t)=>e+t.totalValue,0),rarityBreakdown:t,cost:{currency:r[0].cost.currency,amount:r.reduce((e,t)=>e+t.cost.amount,0)},openedAt:Date.now()};v(s),f(!0)}}}catch(r){alert(r instanceof Error?r.message:"开盒失败")}finally{h(!1)}},E=()=>{f(!1),v(null)},w=qt[n],N=a[w.price.currency]>=w.price.amount,T=Yt.getLootboxPreview(n);return W.jsxs("div",{className:"lootbox-tester ".concat(e),children:[W.jsxs("div",{className:"lootbox-tester__header",children:[W.jsx("h2",{children:"🎁 期货农产品盲盒测试器"}),W.jsx("p",{children:"测试基于中国期货农产品的盲盒开启系统"})]}),W.jsxs("div",{className:"currency-display",children:[W.jsx("h3",{children:"💰 当前货币"}),W.jsx("div",{className:"currency-grid",children:Object.entries(a).map(([e,t])=>W.jsxs("div",{className:"currency-item",children:[W.jsx("span",{className:"currency-name",children:Vt[e]}),W.jsx("span",{className:"currency-amount",children:t.toLocaleString()})]},e))}),W.jsx("button",{onClick:()=>{i(Jt)},className:"btn btn-secondary btn-sm",children:"重置货币"})]}),W.jsxs("div",{className:"lootbox-selector",children:[W.jsx("h3",{children:"📦 选择盲盒类型"}),W.jsx("div",{className:"lootbox-grid",children:Object.values(Ie).map(e=>{const t=qt[e],s=n===e,i=a[t.price.currency]>=t.price.amount;return W.jsxs("div",{className:"lootbox-card ".concat(s?"selected":""," ").concat(i?"":"unaffordable"),onClick:()=>o(e),children:[W.jsx("div",{className:"lootbox-icon",children:t.icon}),W.jsx("div",{className:"lootbox-name",children:t.name}),W.jsxs("div",{className:"lootbox-price",children:[t.price.amount," ",Vt[t.price.currency]]}),W.jsx("div",{className:"lootbox-desc",children:t.description})]},e)})})]}),W.jsxs("div",{className:"lootbox-actions",children:[W.jsxs("div",{className:"action-buttons",children:[W.jsx("button",{onClick:()=>S(1),disabled:d||!N,className:"btn btn-primary",children:d?"开启中...":"开启 1个"}),W.jsx("button",{onClick:()=>S(10),disabled:d||a[w.price.currency]<10*w.price.amount,className:"btn btn-primary",children:"开启 10个"}),W.jsx("button",{onClick:()=>m(!u),className:"btn btn-secondary",children:u?"隐藏预览":"显示预览"}),!s&&b&&W.jsx("button",{onClick:()=>g(!0),className:"btn btn-success",children:"📦 打开背包"}),s&&W.jsx("div",{className:"external-manager-hint",children:'💡 物品已自动添加到统一背包系统，请切换到"物品背包"标签查看'})]}),!N&&W.jsxs("div",{className:"insufficient-funds",children:["⚠️ 货币不足！需要 ",w.price.amount," ",Vt[w.price.currency]]})]}),u&&W.jsxs("div",{className:"lootbox-preview",children:[W.jsxs("h4",{children:["📊 ",w.name," 预览信息"]}),W.jsxs("div",{className:"preview-info",children:[W.jsxs("div",{className:"drop-rates",children:[W.jsx("h5",{children:"掉落概率"}),Object.entries(w.dropRates).map(([e,t])=>t>0&&W.jsxs("div",{className:"rate-item",children:[W.jsx("span",{className:"rarity-indicator",style:{backgroundColor:ke[e]}}),W.jsx("span",{className:"rarity-name",children:Wt[e]}),W.jsxs("span",{className:"rate-value",children:[(100*t).toFixed(1),"%"]})]},e))]}),W.jsx("div",{className:"expected-value",children:W.jsxs("strong",{children:["期望价值: ",T.expectedValue]})})]})]}),c.length>0&&W.jsxs("div",{className:"open-results",children:[W.jsxs("div",{className:"results-header",children:[W.jsxs("h3",{children:["🎉 开盒记录 (",c.length,")"]}),W.jsx("button",{onClick:()=>{l([])},className:"btn btn-secondary btn-sm",children:"清除记录"})]}),W.jsx("div",{className:"results-list",children:c.map((e,t)=>W.jsxs("div",{className:"result-item",children:[W.jsxs("div",{className:"result-header",children:[W.jsxs("span",{className:"result-box",children:[qt[e.lootboxType].icon," ",qt[e.lootboxType].name]}),W.jsxs("span",{className:"result-value",children:["总价值: ",e.totalValue]}),W.jsx("span",{className:"result-time",children:new Date(e.openedAt).toLocaleTimeString()})]}),W.jsx("div",{className:"result-items",children:e.items.map((e,t)=>W.jsxs("div",{className:"item-result ".concat(e.isBonus?"bonus":""," ").concat(e.isGuaranteed?"guaranteed":""),style:{borderLeftColor:ke[e.item.rarity]},children:[W.jsx("span",{className:"item-icon",children:e.item.icon}),W.jsxs("div",{className:"item-info",children:[W.jsx("div",{className:"item-name",children:e.item.name}),W.jsxs("div",{className:"item-details",children:[W.jsx("span",{className:"item-rarity",style:{color:ke[e.item.rarity]},children:Wt[e.item.rarity]}),e.quantity>1&&W.jsxs("span",{className:"item-quantity",children:["x",e.quantity]}),W.jsxs("span",{className:"item-value",children:["价值: ",e.item.value*e.quantity]})]})]}),e.isBonus&&W.jsx("span",{className:"bonus-tag",children:"🎁 奖励"}),e.isGuaranteed&&W.jsx("span",{className:"guaranteed-tag",children:"✅ 保底"})]},t))})]},t))})]}),p&&!s&&b&&W.jsx(Xt,{inventorySystem:b,onClose:()=>g(!1)}),y&&x&&W.jsx("div",{className:"items-modal",onClick:E,children:W.jsxs("div",{className:"items-modal__content",onClick:e=>e.stopPropagation(),children:[W.jsxs("div",{className:"modal-header",children:[W.jsx("h3",{children:"🎊 恭喜获得道具！"}),W.jsx("button",{className:"close-btn",onClick:E,children:"×"})]}),W.jsxs("div",{className:"lootbox-info",children:[W.jsx("div",{className:"lootbox-icon",children:qt[x.lootboxType].icon}),W.jsx("div",{className:"lootbox-name",children:qt[x.lootboxType].name}),W.jsx("div",{className:"open-time",children:new Date(x.openedAt).toLocaleString()})]}),W.jsx("div",{className:"items-grid",children:x.items.map((e,t)=>W.jsxs("div",{className:"modal-item-card ".concat(e.isBonus?"bonus":""," ").concat(e.isGuaranteed?"guaranteed":""),style:{borderColor:ke[e.item.rarity],animationDelay:"".concat(.1*t,"s")},children:[W.jsx("div",{className:"item-glow",style:{backgroundColor:ke[e.item.rarity]+"30"}}),W.jsx("div",{className:"item-icon-large",children:e.item.icon}),W.jsx("div",{className:"item-name",children:e.item.name}),W.jsx("div",{className:"item-rarity",style:{color:ke[e.item.rarity]},children:Wt[e.item.rarity]}),e.quantity>1&&W.jsxs("div",{className:"item-quantity",children:["x",e.quantity]}),W.jsxs("div",{className:"item-value",children:["💰 ",e.item.value*e.quantity]}),e.isBonus&&W.jsx("div",{className:"special-tag bonus-tag",children:"🎁 奖励"}),e.isGuaranteed&&W.jsx("div",{className:"special-tag guaranteed-tag",children:"✅ 保底"}),(e.item.rarity===je.GOLD||e.item.rarity===je.GOLD_RED)&&W.jsxs("div",{className:"legendary-effect",children:[W.jsx("div",{className:"sparkle sparkle-1",children:"✨"}),W.jsx("div",{className:"sparkle sparkle-2",children:"✨"}),W.jsx("div",{className:"sparkle sparkle-3",children:"✨"})]})]},t))}),W.jsxs("div",{className:"modal-summary",children:[W.jsxs("div",{className:"summary-row",children:[W.jsx("span",{children:"总价值:"}),W.jsxs("span",{className:"total-value",children:["💰 ",x.totalValue]})]}),W.jsxs("div",{className:"summary-row",children:[W.jsx("span",{children:"消耗:"}),W.jsxs("span",{className:"cost",children:[x.cost.amount," ",Vt[x.cost.currency]]})]}),x.totalValue>x.cost.amount&&W.jsxs("div",{className:"summary-row profit",children:[W.jsx("span",{children:"净收益:"}),W.jsxs("span",{className:"profit-value",children:["📈 +",x.totalValue-x.cost.amount]})]})]}),W.jsx("div",{className:"modal-actions",children:W.jsx("button",{onClick:E,className:"btn btn-primary btn-large",children:"🎉 太棒了！"})})]})}),W.jsx("style",{children:"\n        .lootbox-tester {\n          max-width: 1200px;\n          margin: 0 auto;\n          padding: 20px;\n          font-family: system-ui, -apple-system, sans-serif;\n        }\n\n        .lootbox-tester__header {\n          text-align: center;\n          margin-bottom: 30px;\n        }\n\n        .lootbox-tester__header h2 {\n          color: #2563eb;\n          margin-bottom: 10px;\n        }\n\n        .currency-display {\n          background: #f8fafc;\n          padding: 20px;\n          border-radius: 12px;\n          margin-bottom: 30px;\n        }\n\n        .currency-grid {\n          display: grid;\n          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n          gap: 15px;\n          margin-bottom: 15px;\n        }\n\n        .currency-item {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          background: white;\n          padding: 12px 16px;\n          border-radius: 8px;\n          border: 1px solid #e2e8f0;\n        }\n\n        .currency-name {\n          font-weight: 600;\n          color: #475569;\n        }\n\n        .currency-amount {\n          font-weight: 700;\n          color: #059669;\n        }\n\n        .lootbox-selector {\n          margin-bottom: 30px;\n        }\n\n        .lootbox-grid {\n          display: grid;\n          grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));\n          gap: 15px;\n          margin-top: 15px;\n        }\n\n        .lootbox-card {\n          background: white;\n          border: 2px solid #e2e8f0;\n          border-radius: 12px;\n          padding: 20px;\n          cursor: pointer;\n          transition: all 0.2s;\n          text-align: center;\n        }\n\n        .lootbox-card:hover {\n          border-color: #3b82f6;\n          transform: translateY(-2px);\n          box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);\n        }\n\n        .lootbox-card.selected {\n          border-color: #3b82f6;\n          background: #eff6ff;\n        }\n\n        .lootbox-card.unaffordable {\n          opacity: 0.6;\n          cursor: not-allowed;\n        }\n\n        .lootbox-icon {\n          font-size: 2.5rem;\n          margin-bottom: 10px;\n        }\n\n        .lootbox-name {\n          font-weight: 700;\n          font-size: 1.1rem;\n          margin-bottom: 8px;\n          color: #1e293b;\n        }\n\n        .lootbox-price {\n          color: #059669;\n          font-weight: 600;\n          margin-bottom: 8px;\n        }\n\n        .lootbox-desc {\n          font-size: 0.9rem;\n          color: #64748b;\n          line-height: 1.4;\n        }\n\n        .lootbox-actions {\n          background: #f8fafc;\n          padding: 20px;\n          border-radius: 12px;\n          margin-bottom: 30px;\n        }\n\n        .action-buttons {\n          display: flex;\n          gap: 15px;\n          flex-wrap: wrap;\n          margin-bottom: 15px;\n        }\n\n        .btn {\n          padding: 12px 24px;\n          border: none;\n          border-radius: 8px;\n          font-weight: 600;\n          cursor: pointer;\n          transition: all 0.2s;\n          min-width: 120px;\n        }\n\n        .btn:disabled {\n          opacity: 0.6;\n          cursor: not-allowed;\n        }\n\n        .btn-primary {\n          background: #3b82f6;\n          color: white;\n        }\n\n        .btn-primary:hover:not(:disabled) {\n          background: #2563eb;\n        }\n\n        .btn-secondary {\n          background: #6b7280;\n          color: white;\n        }\n\n        .btn-secondary:hover:not(:disabled) {\n          background: #4b5563;\n        }\n\n        .btn-success {\n          background: #059669;\n          color: white;\n        }\n\n        .btn-success:hover:not(:disabled) {\n          background: #047857;\n        }\n\n        .btn-sm {\n          padding: 8px 16px;\n          font-size: 0.9rem;\n          min-width: auto;\n        }\n\n        .insufficient-funds {\n          color: #dc2626;\n          font-weight: 600;\n          background: #fef2f2;\n          padding: 12px;\n          border-radius: 8px;\n          border: 1px solid #fecaca;\n        }\n\n        .lootbox-preview {\n          background: #f0f9ff;\n          padding: 20px;\n          border-radius: 12px;\n          margin-bottom: 30px;\n          border: 1px solid #bae6fd;\n        }\n\n        .drop-rates {\n          margin-bottom: 15px;\n        }\n\n        .rate-item {\n          display: flex;\n          align-items: center;\n          gap: 10px;\n          margin-bottom: 8px;\n        }\n\n        .rarity-indicator {\n          width: 16px;\n          height: 16px;\n          border-radius: 50%;\n        }\n\n        .rate-value {\n          margin-left: auto;\n          font-weight: 600;\n        }\n\n        .open-results {\n          background: white;\n          border-radius: 12px;\n          border: 1px solid #e2e8f0;\n        }\n\n        .results-header {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          padding: 20px;\n          border-bottom: 1px solid #e2e8f0;\n        }\n\n        .results-list {\n          max-height: 600px;\n          overflow-y: auto;\n        }\n\n        .result-item {\n          padding: 20px;\n          border-bottom: 1px solid #f1f5f9;\n        }\n\n        .result-item:last-child {\n          border-bottom: none;\n        }\n\n        .result-header {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          margin-bottom: 15px;\n          flex-wrap: wrap;\n          gap: 10px;\n        }\n\n        .result-box {\n          font-weight: 700;\n          color: #1e293b;\n        }\n\n        .result-value {\n          color: #059669;\n          font-weight: 600;\n        }\n\n        .result-time {\n          color: #64748b;\n          font-size: 0.9rem;\n        }\n\n        .result-items {\n          display: grid;\n          gap: 10px;\n        }\n\n        .item-result {\n          display: flex;\n          align-items: center;\n          gap: 15px;\n          padding: 12px;\n          background: #f8fafc;\n          border-radius: 8px;\n          border-left: 4px solid;\n          position: relative;\n        }\n\n        .item-result.bonus {\n          background: #fef3c7;\n        }\n\n        .item-result.guaranteed {\n          background: #d1fae5;\n        }\n\n        .item-icon {\n          font-size: 1.5rem;\n        }\n\n        .item-info {\n          flex: 1;\n        }\n\n        .item-name {\n          font-weight: 600;\n          color: #1e293b;\n          margin-bottom: 4px;\n        }\n\n        .item-details {\n          display: flex;\n          gap: 12px;\n          font-size: 0.9rem;\n          color: #64748b;\n        }\n\n        .item-rarity {\n          font-weight: 600;\n        }\n\n        .bonus-tag, .guaranteed-tag {\n          position: absolute;\n          top: 8px;\n          right: 8px;\n          font-size: 0.8rem;\n          background: white;\n          padding: 2px 6px;\n          border-radius: 4px;\n          border: 1px solid currentColor;\n        }\n\n        .external-manager-hint {\n          background: #e0f2fe;\n          color: #0277bd;\n          padding: 12px;\n          border-radius: 8px;\n          border: 1px solid #b3e5fc;\n          font-size: 0.9rem;\n          margin-top: 10px;\n        }\n\n        .items-modal {\n          position: fixed;\n          top: 0;\n          left: 0;\n          width: 100%;\n          height: 100%;\n          background: rgba(0, 0, 0, 0.8);\n          display: flex;\n          justify-content: center;\n          align-items: center;\n          z-index: 1000;\n          animation: modal-fade-in 0.3s ease-out;\n        }\n\n        .items-modal__content {\n          background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);\n          padding: 0;\n          border-radius: 16px;\n          max-width: 90vw;\n          max-height: 90vh;\n          overflow: auto;\n          box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);\n          animation: modal-slide-up 0.4s ease-out;\n          border: 3px solid #e2e8f0;\n        }\n\n        .modal-header {\n          background: linear-gradient(135deg, #4CAF50, #2E7D32);\n          color: white;\n          padding: 20px 30px;\n          border-radius: 13px 13px 0 0;\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          position: relative;\n          overflow: hidden;\n        }\n\n        .modal-header::before {\n          content: '';\n          position: absolute;\n          top: -50%;\n          left: -50%;\n          width: 200%;\n          height: 200%;\n          background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);\n          animation: shine 2s ease-in-out infinite;\n        }\n\n        .modal-header h3 {\n          margin: 0;\n          font-size: 1.5rem;\n          text-shadow: 2px 2px 4px rgba(0,0,0,0.3);\n        }\n\n        .close-btn {\n          background: rgba(255,255,255,0.2);\n          border: none;\n          color: white;\n          font-size: 1.5rem;\n          width: 35px;\n          height: 35px;\n          border-radius: 50%;\n          cursor: pointer;\n          transition: all 0.2s ease;\n        }\n\n        .close-btn:hover {\n          background: rgba(255,255,255,0.3);\n          transform: scale(1.1);\n        }\n\n        .lootbox-info {\n          text-align: center;\n          padding: 20px;\n          background: linear-gradient(135deg, #f0f9ff, #e0f2fe);\n          border-bottom: 2px solid #e2e8f0;\n        }\n\n        .lootbox-info .lootbox-icon {\n          font-size: 3rem;\n          margin-bottom: 8px;\n          animation: bounce 2s ease-in-out infinite;\n        }\n\n        .lootbox-info .lootbox-name {\n          font-size: 1.2rem;\n          font-weight: bold;\n          color: #1e293b;\n          margin-bottom: 4px;\n        }\n\n        .lootbox-info .open-time {\n          font-size: 0.9rem;\n          color: #64748b;\n        }\n\n        .items-grid {\n          display: grid;\n          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n          gap: 20px;\n          padding: 25px;\n          background: #f8fafc;\n          max-height: 65vh;\n          overflow-y: auto;\n          min-height: 300px;\n          border-radius: 12px;\n          box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);\n        }\n\n        .modal-item-card {\n          background: white;\n          border: 3px solid;\n          border-radius: 16px;\n          padding: 25px;\n          text-align: center;\n          position: relative;\n          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n          animation: item-appear 0.6s ease-out both;\n          overflow: hidden;\n          cursor: pointer;\n          min-height: 200px;\n          display: flex;\n          flex-direction: column;\n          justify-content: center;\n          align-items: center;\n        }\n\n        .modal-item-card:hover {\n          transform: translateY(-8px) scale(1.05);\n          box-shadow: 0 20px 40px rgba(0,0,0,0.15), 0 0 30px rgba(0,0,0,0.1);\n          z-index: 10;\n        }\n\n        .item-glow {\n          position: absolute;\n          top: -3px;\n          left: -3px;\n          right: -3px;\n          bottom: -3px;\n          border-radius: 16px;\n          z-index: -1;\n          animation: glow-pulse 2s ease-in-out infinite;\n          opacity: 0.6;\n        }\n\n        .item-icon-large {\n          font-size: 4rem;\n          margin-bottom: 15px;\n          animation: icon-float 3s ease-in-out infinite;\n          filter: drop-shadow(0 4px 8px rgba(0,0,0,0.2));\n        }\n\n        .modal-item-card .item-name {\n          font-size: 1.2rem;\n          font-weight: bold;\n          color: #1e293b;\n          margin-bottom: 10px;\n          line-height: 1.3;\n          max-width: 100%;\n          word-wrap: break-word;\n        }\n\n        .modal-item-card .item-rarity {\n          font-weight: 700;\n          font-size: 1rem;\n          margin-bottom: 12px;\n          text-transform: uppercase;\n          letter-spacing: 2px;\n          text-shadow: 1px 1px 2px rgba(0,0,0,0.3);\n        }\n\n        .modal-item-card .item-quantity {\n          position: absolute;\n          top: 15px;\n          right: 15px;\n          background: linear-gradient(135deg, #4CAF50, #45a049);\n          color: white;\n          padding: 6px 12px;\n          border-radius: 20px;\n          font-size: 0.9rem;\n          font-weight: bold;\n          box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);\n        }\n\n        .modal-item-card .item-value {\n          background: linear-gradient(135deg, #f59e0b, #d97706);\n          color: white;\n          padding: 8px 16px;\n          border-radius: 25px;\n          font-weight: bold;\n          font-size: 1rem;\n          margin-top: 12px;\n          display: inline-block;\n          box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);\n          min-width: 80px;\n        }\n\n        .special-tag {\n          position: absolute;\n          top: 12px;\n          left: 12px;\n          padding: 6px 12px;\n          border-radius: 12px;\n          font-size: 0.8rem;\n          font-weight: bold;\n          color: white;\n          z-index: 2;\n          box-shadow: 0 2px 8px rgba(0,0,0,0.2);\n        }\n\n        .bonus-tag {\n          background: linear-gradient(135deg, #f59e0b, #d97706);\n        }\n\n        .guaranteed-tag {\n          background: linear-gradient(135deg, #10b981, #059669);\n        }\n\n        .legendary-effect {\n          position: absolute;\n          top: 0;\n          left: 0;\n          right: 0;\n          bottom: 0;\n          pointer-events: none;\n          z-index: 1;\n          overflow: hidden;\n        }\n\n        .sparkle {\n          position: absolute;\n          font-size: 1.5rem;\n          animation: sparkle-rotate 3s linear infinite;\n          text-shadow: 0 0 10px currentColor;\n        }\n\n        .sparkle-1 {\n          top: 15%;\n          left: 20%;\n          animation-delay: 0s;\n        }\n\n        .sparkle-2 {\n          top: 25%;\n          right: 15%;\n          animation-delay: 1s;\n        }\n\n        .sparkle-3 {\n          bottom: 20%;\n          left: 15%;\n          animation-delay: 2s;\n        }\n\n        .modal-summary {\n          background: white;\n          padding: 25px 30px;\n          border-top: 2px solid #e2e8f0;\n        }\n\n        .summary-row {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          margin-bottom: 12px;\n          font-size: 1.1rem;\n        }\n\n        .summary-row:last-child {\n          margin-bottom: 0;\n        }\n\n        .summary-row.profit {\n          background: linear-gradient(135deg, #dcfce7, #bbf7d0);\n          padding: 12px 16px;\n          border-radius: 8px;\n          border: 2px solid #22c55e;\n        }\n\n        .total-value {\n          font-weight: bold;\n          color: #059669;\n          font-size: 1.2rem;\n        }\n\n        .cost {\n          font-weight: 600;\n          color: #dc2626;\n        }\n\n        .profit-value {\n          font-weight: bold;\n          color: #16a34a;\n          font-size: 1.1rem;\n        }\n\n        .modal-actions {\n          padding: 20px 30px;\n          background: #f8fafc;\n          border-radius: 0 0 13px 13px;\n          text-align: center;\n        }\n\n        .btn-large {\n          padding: 15px 40px;\n          font-size: 1.1rem;\n          font-weight: bold;\n          min-width: 180px;\n          background: linear-gradient(135deg, #4CAF50, #45a049);\n          transition: all 0.3s ease;\n        }\n\n        .btn-large:hover:not(:disabled) {\n          background: linear-gradient(135deg, #45a049, #3d8b40);\n          transform: translateY(-2px);\n          box-shadow: 0 8px 16px rgba(76, 175, 80, 0.3);\n        }\n\n        /* 动画关键帧 */\n        @keyframes modal-fade-in {\n          from { opacity: 0; }\n          to { opacity: 1; }\n        }\n\n        @keyframes modal-slide-up {\n          from { \n            opacity: 0;\n            transform: translateY(50px) scale(0.9);\n          }\n          to { \n            opacity: 1;\n            transform: translateY(0) scale(1);\n          }\n        }\n\n        @keyframes shine {\n          0% { transform: translateX(-100%) translateY(-100%) rotate(30deg); }\n          100% { transform: translateX(100%) translateY(100%) rotate(30deg); }\n        }\n\n        @keyframes bounce {\n          0%, 100% { transform: translateY(0); }\n          50% { transform: translateY(-10px); }\n        }\n\n        @keyframes item-appear {\n          from {\n            opacity: 0;\n            transform: translateY(30px) scale(0.8);\n          }\n          to {\n            opacity: 1;\n            transform: translateY(0) scale(1);\n          }\n        }\n\n        @keyframes glow-pulse {\n          0%, 100% { opacity: 0.5; }\n          50% { opacity: 0.8; }\n        }\n\n        @keyframes icon-float {\n          0%, 100% { transform: translateY(0); }\n          50% { transform: translateY(-8px); }\n        }\n\n        @keyframes sparkle-rotate {\n          0% { transform: rotate(0deg) scale(1); opacity: 1; }\n          25% { transform: rotate(90deg) scale(1.2); opacity: 0.8; }\n          50% { transform: rotate(180deg) scale(1); opacity: 0.6; }\n          75% { transform: rotate(270deg) scale(1.2); opacity: 0.8; }\n          100% { transform: rotate(360deg) scale(1); opacity: 1; }\n        }\n\n        /* 响应式调整 */\n        @media (max-width: 768px) {\n          .lootbox-tester {\n            padding: 15px;\n          }\n          \n          .action-buttons {\n            flex-direction: column;\n          }\n          \n          .btn {\n            width: 100%;\n          }\n          \n          .currency-grid {\n            grid-template-columns: 1fr;\n          }\n          \n          .lootbox-grid {\n            grid-template-columns: 1fr;\n          }\n          \n          .result-header {\n            flex-direction: column;\n            align-items: flex-start;\n          }\n          \n          .items-grid {\n            grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));\n            gap: 15px;\n            padding: 20px;\n          }\n          \n          .modal-item-card {\n            padding: 20px;\n            min-height: 180px;\n          }\n          \n          .item-icon-large {\n            font-size: 3rem;\n            margin-bottom: 12px;\n          }\n          \n          .modal-item-card .item-name {\n            font-size: 1.1rem;\n          }\n          \n          .modal-item-card .item-rarity {\n            font-size: 0.9rem;\n          }\n        }\n\n        @media (max-width: 480px) {\n          .items-grid {\n            grid-template-columns: repeat(3, 1fr);\n            gap: 12px;\n            padding: 15px;\n          }\n          \n          .modal-item-card {\n            padding: 15px;\n            min-height: 160px;\n          }\n          \n          .item-icon-large {\n            font-size: 2.5rem;\n          }\n        }\n\n        @media (max-width: 360px) {\n          .items-grid {\n            grid-template-columns: repeat(2, 1fr);\n            gap: 10px;\n            padding: 12px;\n          }\n          \n          .modal-item-card {\n            padding: 12px;\n            min-height: 140px;\n          }\n          \n          .item-icon-large {\n            font-size: 2rem;\n          }\n        }\n      "})]})},$t={currentFocusScore:0,averageFocusScore:0,focusSession:{startTime:null,duration:0,isActive:!1},farmStats:{knowledgeFlowers:0,strengthTrees:0,timeVeggies:0,meditationLotus:0,totalGrowthPoints:0},settings:{focusThreshold:75,growthRate:1,autoGrowth:!0},isPostureGood:!1,lastPostureUpdate:Date.now(),currentStreak:0,dailyStats:{totalFocusTime:0,goodPosturePercentage:0,plantsGrown:0}};function es(e,t){switch(t.type){case"UPDATE_POSTURE":{const{focusScore:s,isFocused:a}=t.payload,i=Date.now(),n=i-e.lastPostureUpdate;let r=e.currentStreak;return a&&s>=e.settings.focusThreshold?r+=n/1e3:r=0,{...e,currentFocusScore:s,isPostureGood:a,lastPostureUpdate:i,currentStreak:r,averageFocusScore:.9*e.averageFocusScore+.1*s}}case"START_FOCUS_SESSION":return{...e,focusSession:{startTime:Date.now(),duration:0,isActive:!0},currentStreak:0};case"END_FOCUS_SESSION":{const t=e.focusSession.startTime?Date.now()-e.focusSession.startTime:0;return{...e,focusSession:{startTime:null,duration:t,isActive:!1},dailyStats:{...e.dailyStats,totalFocusTime:e.dailyStats.totalFocusTime+t}}}case"GROW_PLANT":{const{type:s,count:a=1}=t.payload,i={...e.farmStats};switch(s){case"knowledge":i.knowledgeFlowers+=a;break;case"strength":i.strengthTrees+=a;break;case"time":i.timeVeggies+=a;break;case"meditation":i.meditationLotus+=a}return{...e,farmStats:i,dailyStats:{...e.dailyStats,plantsGrown:e.dailyStats.plantsGrown+a}}}case"ADD_GROWTH_POINTS":return{...e,farmStats:{...e.farmStats,totalGrowthPoints:e.farmStats.totalGrowthPoints+t.payload}};case"UPDATE_SETTINGS":return{...e,settings:{...e.settings,...t.payload}};case"RESET_DAILY_STATS":return{...e,dailyStats:{totalFocusTime:0,goodPosturePercentage:0,plantsGrown:0}};default:return e}}const ts=r.createContext(void 0),ss=({children:e})=>{const[t,s]=r.useReducer(es,$t),a=r.useCallback(e=>{s({type:"UPDATE_POSTURE",payload:e})},[]),i=r.useCallback(()=>{s({type:"START_FOCUS_SESSION"})},[]),n=r.useCallback(()=>{s({type:"END_FOCUS_SESSION"})},[]),o=r.useCallback((e,t=1)=>{s({type:"GROW_PLANT",payload:{type:e,count:t}})},[]),c=r.useCallback(e=>{s({type:"UPDATE_SETTINGS",payload:e})},[]),l=r.useCallback(()=>{const e=Math.floor(t.dailyStats.totalFocusTime/6e4),s=Math.floor(t.dailyStats.totalFocusTime%6e4/1e3);return"".concat(e,":").concat(s.toString().padStart(2,"0"))},[t.dailyStats.totalFocusTime]),d=r.useCallback(()=>{const e=Math.floor(t.currentStreak/60),s=Math.floor(t.currentStreak%60);return"".concat(e,":").concat(s.toString().padStart(2,"0"))},[t.currentStreak]),h=r.useCallback(()=>t.isPostureGood&&t.currentFocusScore>=t.settings.focusThreshold&&t.currentStreak>=30,[t.isPostureGood,t.currentFocusScore,t.settings.focusThreshold,t.currentStreak]),u={state:t,dispatch:s,updatePosture:a,startFocusSession:i,endFocusSession:n,growPlant:o,updateSettings:c,getFocusTimeFormatted:l,getCurrentStreakFormatted:d,shouldTriggerGrowth:h};return W.jsx(ts.Provider,{value:u,children:e})},as=class e{constructor(){t(this,"sessionStartTime",null),t(this,"sessionSkipCount",0)}async saveProgress(t){try{const s={...this.loadProgress(),...t,lastAccessTime:new Date,version:e.VERSION};return localStorage.setItem(e.STORAGE_KEY,JSON.stringify(s)),this.recordAnalytics("progress_saved",{step:t.currentStepIndex}),!0}catch(s){return!1}}loadProgress(){try{const t=localStorage.getItem(e.STORAGE_KEY);if(!t)return this.getDefaultProgress();const s=JSON.parse(t);return s.version!==e.VERSION?this.migrateProgress(s):{...s,lastAccessTime:new Date(s.lastAccessTime)}}catch(t){return this.getDefaultProgress()}}createBackup(t,s){try{const a=this.loadProgress(),i="backup_".concat(Date.now()),n={id:i,timestamp:new Date,data:a,reason:t,description:s},r=this.loadBackups();return r.push(n),r.length>e.MAX_BACKUPS&&r.splice(0,r.length-e.MAX_BACKUPS),localStorage.setItem(e.BACKUP_KEY,JSON.stringify(r)),this.recordAnalytics("backup_created",{reason:t,backupId:i}),!0}catch(a){return!1}}restoreFromBackup(t){try{const s=this.loadBackups().find(e=>e.id===t);return!!s&&(this.createBackup("manual","恢复前自动备份"),localStorage.setItem(e.STORAGE_KEY,JSON.stringify(s.data)),this.recordAnalytics("backup_restored",{backupId:t}),!0)}catch(s){return!1}}loadBackups(){try{const t=localStorage.getItem(e.BACKUP_KEY);return t?JSON.parse(t).map(e=>({...e,timestamp:new Date(e.timestamp),data:{...e.data,lastAccessTime:new Date(e.data.lastAccessTime)}})):[]}catch(t){return[]}}clearAllProgress(){try{return this.createBackup("manual","清除前备份"),localStorage.removeItem(e.STORAGE_KEY),this.recordAnalytics("progress_cleared"),!0}catch(t){return!1}}startSession(){this.sessionStartTime=new Date,this.sessionSkipCount=0,this.recordAnalytics("session_started")}endSession(){if(!this.sessionStartTime)return;const e=Date.now()-this.sessionStartTime.getTime(),t=this.loadProgress();this.saveProgress({totalTimeSpent:t.totalTimeSpent+e,sessionCount:t.sessionCount+1}),this.recordAnalytics("session_ended",{duration:e,skipCount:this.sessionSkipCount}),this.sessionStartTime=null,this.sessionSkipCount=0}recordSkip(){this.sessionSkipCount++;const e=this.loadProgress();this.saveProgress({skipCount:e.skipCount+1}),this.recordAnalytics("step_skipped")}getProgressStats(){const e=this.loadProgress(),t=Object.keys(e.tutorialProgress).length-1,s=Object.values(e.tutorialProgress).filter(Boolean).length-1;return{completionRate:t>0?s/t*100:0,totalStepsCompleted:e.completedSteps.length,averageSessionTime:e.sessionCount>0?e.totalTimeSpent/e.sessionCount:0,skipRate:e.sessionCount>0?e.skipCount/e.sessionCount*100:0,lastActiveDate:e.lastAccessTime}}exportProgress(){const t=this.loadProgress(),s=this.loadBackups(),a=this.loadAnalytics();return JSON.stringify({progress:t,backups:s,analytics:a,exportTime:(new Date).toISOString(),version:e.VERSION},null,2)}importProgress(t){try{const s=JSON.parse(t);if(!s.progress||!s.version)throw new Error("无效的导入数据格式");return this.createBackup("manual","导入前备份"),localStorage.setItem(e.STORAGE_KEY,JSON.stringify(s.progress)),s.backups&&localStorage.setItem(e.BACKUP_KEY,JSON.stringify(s.backups)),this.recordAnalytics("progress_imported",{version:s.version}),!0}catch(s){return!1}}recordAnalytics(t,s){try{const a=this.loadAnalytics();a.push({event:t,timestamp:new Date,data:s}),a.length>1e3&&a.splice(0,a.length-1e3),localStorage.setItem(e.ANALYTICS_KEY,JSON.stringify(a))}catch(a){}}loadAnalytics(){try{const t=localStorage.getItem(e.ANALYTICS_KEY);return t?JSON.parse(t):[]}catch(t){return[]}}getDefaultProgress(){return{tutorialProgress:{cameraSetup:!1,firstPlanting:!1,basicOperations:!1,completed:!1},currentStepIndex:0,completedSteps:[],lastAccessTime:new Date,totalTimeSpent:0,sessionCount:0,skipCount:0,version:e.VERSION}}migrateProgress(t){return{...this.getDefaultProgress(),...t,version:e.VERSION}}};t(as,"STORAGE_KEY","selfgame_tutorial_progress"),t(as,"BACKUP_KEY","selfgame_tutorial_backups"),t(as,"ANALYTICS_KEY","selfgame_tutorial_analytics"),t(as,"VERSION","1.0.0"),t(as,"MAX_BACKUPS",10);let is=as;const ns={isActive:!1,currentStepIndex:0,steps:[],completedSteps:[],isFirstTimeUser:!0,tutorialProgress:{cameraSetup:!1,firstPlanting:!1,basicOperations:!1,completed:!1}};function rs(e,t){switch(t.type){case"START_TUTORIAL":return{...e,isActive:!0,currentStepIndex:0,steps:t.payload.steps};case"NEXT_STEP":const s=Math.min(e.currentStepIndex+1,e.steps.length-1);return{...e,currentStepIndex:s};case"PREV_STEP":const a=Math.max(e.currentStepIndex-1,0);return{...e,currentStepIndex:a};case"SKIP_STEP":const i=Math.min(e.currentStepIndex+1,e.steps.length-1);return{...e,currentStepIndex:i};case"SET_STEP":const n=Math.max(0,Math.min(t.payload,e.steps.length-1));return{...e,currentStepIndex:n};case"COMPLETE_STEP":return{...e,completedSteps:[...e.completedSteps,t.payload]};case"UPDATE_PROGRESS":return{...e,tutorialProgress:{...e.tutorialProgress,...t.payload}};case"FINISH_TUTORIAL":return{...e,isActive:!1,currentStepIndex:0,isFirstTimeUser:!1,tutorialProgress:{...e.tutorialProgress,completed:!0}};case"RESET_TUTORIAL":return{...ns,isFirstTimeUser:!0};default:return e}}const os=r.createContext(void 0),cs="selfgame_tutorial_progress",ls=({children:e})=>{const[t,s]=r.useReducer(rs,ns),[a]=A.useState(()=>new is);r.useEffect(()=>{try{const e=localStorage.getItem(cs);if(e){const t=JSON.parse(e);s({type:"UPDATE_PROGRESS",payload:t}),t.completed&&s({type:"FINISH_TUTORIAL"})}}catch(e){}},[]),r.useEffect(()=>{try{localStorage.setItem(cs,JSON.stringify(t.tutorialProgress))}catch(e){}},[t.tutorialProgress]);const i=r.useCallback(e=>{s({type:"START_TUTORIAL",payload:{steps:e}}),a.startSession()},[a]),n=r.useCallback(()=>{s({type:"NEXT_STEP"})},[]),o=r.useCallback(()=>{s({type:"PREV_STEP"})},[]),c=r.useCallback(()=>{s({type:"SKIP_STEP"}),a.recordSkip()},[a]),l=r.useCallback(()=>{s({type:"FINISH_TUTORIAL"}),a.endSession()},[a]),d=r.useCallback(e=>{s({type:"SET_STEP",payload:e})},[]),h=r.useCallback(e=>{s({type:"COMPLETE_STEP",payload:e})},[]),u=r.useCallback(e=>{s({type:"UPDATE_PROGRESS",payload:e})},[]),m=r.useCallback(()=>{s({type:"RESET_TUTORIAL"})},[]),p=r.useCallback(()=>t.steps[t.currentStepIndex]||null,[t.steps,t.currentStepIndex]),g=r.useCallback(()=>t.currentStepIndex>=t.steps.length-1,[t.currentStepIndex,t.steps.length]),y=r.useCallback(()=>0===t.currentStepIndex,[t.currentStepIndex]),f=r.useCallback(()=>0===t.steps.length?0:(t.currentStepIndex+1)/t.steps.length*100,[t.currentStepIndex,t.steps.length]),x={state:t,dispatch:s,startTutorial:i,nextStep:n,prevStep:o,skipStep:c,finishTutorial:l,setStep:d,completeStep:h,updateProgress:u,resetTutorial:m,getCurrentStep:p,isLastStep:g,isFirstStep:y,getProgress:f,progressService:a};return W.jsx(os.Provider,{value:x,children:e})},ds=()=>{const e=r.useContext(os);if(void 0===e)throw new Error("useTutorial must be used within a TutorialProvider");return e},hs=({step:e,position:t,onNext:s,onPrev:a,onSkip:i,onFinish:n,onExitTutorial:o,canGoNext:c,canGoPrev:l,canSkip:d,currentIndex:h,totalSteps:u})=>{const[m,p]=r.useState({}),[g,y]=r.useState({});r.useEffect(()=>{const s=()=>{const s=320,a=200,i=12;let n=t.x-160,r=t.y-100,o="none";if(e.position)switch(e.position){case"top":r=t.y-a-i-10,o="bottom";break;case"bottom":r=t.y+i+10,o="top";break;case"left":n=t.x-s-i-10,r=t.y-100,o="right";break;case"right":n=t.x+i+10,r=t.y-100,o="left"}if(n=Math.max(20,Math.min(n,window.innerWidth-s-20)),r=Math.max(20,Math.min(r,window.innerHeight-a-20)),p({position:"fixed",left:n,top:r,width:s,minHeight:a,zIndex:1e4}),"none"!==o){const e=t.x-n,c=t.y-r;y({position:"absolute",left:"left"===o||"right"===o?"left"===o?-12:s:Math.max(i,Math.min(e,308)),top:"top"===o||"bottom"===o?"top"===o?-12:a:Math.max(i,Math.min(c,188)),width:0,height:0,border:"".concat(i,"px solid transparent"),..."top"===o&&{borderBottomColor:"white"},..."bottom"===o&&{borderTopColor:"white"},..."left"===o&&{borderRightColor:"white"},..."right"===o&&{borderLeftColor:"white"}})}else y({})};s();const a=()=>s();return window.addEventListener("resize",a),()=>window.removeEventListener("resize",a)},[t,e.position]);const f=(h+1)/u*100;return W.jsxs("div",{className:"tutorial-tooltip",style:m,children:[Object.keys(g).length>0&&W.jsx("div",{className:"tutorial-tooltip-arrow",style:g}),W.jsxs("div",{className:"tutorial-tooltip-header",children:[W.jsxs("div",{className:"tutorial-tooltip-title-row",children:[W.jsx("h3",{className:"tutorial-tooltip-title",children:e.title}),o&&W.jsx("button",{className:"tutorial-btn tutorial-btn-exit",onClick:o,title:"退出教程",children:"✕"})]}),W.jsxs("div",{className:"tutorial-tooltip-progress",children:[W.jsxs("span",{className:"tutorial-tooltip-counter",children:[h+1," / ",u]}),W.jsx("div",{className:"tutorial-tooltip-progress-bar",children:W.jsx("div",{className:"tutorial-tooltip-progress-fill",style:{width:"".concat(f,"%")}})})]})]}),W.jsx("div",{className:"tutorial-tooltip-content",children:W.jsx("p",{className:"tutorial-tooltip-description",children:e.description})}),W.jsx("div",{className:"tutorial-tooltip-footer",children:W.jsxs("div",{className:"tutorial-tooltip-actions",children:[o&&W.jsx("button",{className:"tutorial-btn tutorial-btn-exit-tutorial",onClick:o,style:{background:"linear-gradient(135deg, #dc2626, #ef4444)",color:"white",border:"none",fontWeight:"bold"},children:"🚪 退出教程"}),d&&W.jsx("button",{className:"tutorial-btn tutorial-btn-skip",onClick:i,children:"跳过"}),l&&W.jsx("button",{className:"tutorial-btn tutorial-btn-prev",onClick:a,children:"上一步"}),W.jsx("button",{className:"tutorial-btn tutorial-btn-next",onClick:c?s:n,children:c?"下一步":"完成"})]})})]})},us=({targetElement:e,style:t={},className:s=""})=>{const[a,i]=r.useState({});return r.useEffect(()=>{const s=()=>{const s=e.getBoundingClientRect(),a=t.padding||8;i({position:"fixed",left:s.left-a,top:s.top-a,width:s.width+2*a,height:s.height+2*a,borderRadius:t.borderRadius||"8px",zIndex:t.zIndex||9999,animation:t.animation||"tutorial-pulse 2s infinite",pointerEvents:"none",border:"3px solid #007bff",backgroundColor:"rgba(0, 123, 255, 0.1)",boxShadow:"0 0 20px rgba(0, 123, 255, 0.3)",transition:"all 0.3s ease"})};s();const a=()=>s(),n=()=>s();return window.addEventListener("resize",a),window.addEventListener("scroll",n),()=>{window.removeEventListener("resize",a),window.removeEventListener("scroll",n)}},[e,t]),W.jsx("div",{className:"tutorial-highlight ".concat(s),style:a})},ms=({type:e,targetElement:t,isActive:s,duration:a=3e3,intensity:i="medium",color:n="#3b82f6",onComplete:o})=>{const[c,l]=r.useState(null),[d,h]=r.useState(null);if(r.useEffect(()=>{if(!s||!t)return;const e=document.querySelector(t);if(!e)return;const i=e.getBoundingClientRect();h(i);const n=setTimeout(()=>{null==o||o()},a);return()=>clearTimeout(n)},[s,t,a,o]),!s||!d)return null;const u=(()=>{switch(i){case"low":return.7;case"high":return 1.3;default:return 1}})();return W.jsxs("div",{className:"tutorial-animations-container",children:["pointer"===e&&W.jsxs("div",{className:"tutorial-pointer",style:{left:d.right+10,top:d.top+d.height/2,animationDuration:"".concat(2/u,"s"),"--pointer-color":n},children:[W.jsx("div",{className:"pointer-arrow",children:"👉"}),W.jsx("div",{className:"pointer-trail"})]}),"spotlight"===e&&W.jsx("div",{className:"tutorial-spotlight",style:{left:d.left-20*u,top:d.top-20*u,width:d.width+40*u,height:d.height+40*u,"--spotlight-color":n}}),"ripple"===e&&W.jsxs("div",{className:"tutorial-ripple",style:{left:d.left+d.width/2,top:d.top+d.height/2,animationDuration:"".concat(3/u,"s"),"--ripple-color":n},children:[W.jsx("div",{className:"ripple-wave ripple-1"}),W.jsx("div",{className:"ripple-wave ripple-2"}),W.jsx("div",{className:"ripple-wave ripple-3"})]}),"breathe"===e&&W.jsx("div",{className:"tutorial-breathe",style:{left:d.left-15*u,top:d.top-15*u,width:d.width+30*u,height:d.height+30*u,animationDuration:"".concat(2/u,"s"),"--breathe-color":n}}),"glow"===e&&W.jsx("div",{className:"tutorial-glow",style:{left:d.left-10*u,top:d.top-10*u,width:d.width+20*u,height:d.height+20*u,"--glow-color":n}}),"float"===e&&W.jsxs("div",{className:"tutorial-float",style:{left:d.left+d.width/2,top:d.top-50*u,animationDuration:"".concat(4/u,"s"),"--float-color":n},children:[W.jsx("div",{className:"float-icon",children:"✨"}),W.jsxs("div",{className:"float-particles",children:[W.jsx("div",{className:"particle particle-1",children:"⭐"}),W.jsx("div",{className:"particle particle-2",children:"💫"}),W.jsx("div",{className:"particle particle-3",children:"✨"}),W.jsx("div",{className:"particle particle-4",children:"🌟"})]})]})]})},ps=({targetElement:e,isActive:t,animationSequence:s,onComplete:a})=>{const[i,n]=r.useState(0),[o,c]=r.useState(null);return r.useEffect(()=>{if(!t||0===s.length)return;let e=[];return s.forEach((t,i)=>{const r=setTimeout(()=>{n(i),c(t);const r=setTimeout(()=>{i===s.length-1&&(c(null),null==a||a())},t.duration);e.push(r)},t.delay);e.push(r)}),()=>{e.forEach(e=>clearTimeout(e))}},[t,s,a]),o?W.jsx(ms,{type:o.type,targetElement:e,isActive:!!o,duration:o.duration,intensity:o.intensity,color:o.color}):null},gs=[{type:"spotlight",delay:0,duration:2e3,intensity:"medium",color:"#10b981"},{type:"glow",delay:1500,duration:2e3,intensity:"low",color:"#3b82f6"}],ys=[{type:"ripple",delay:0,duration:2e3,intensity:"high",color:"#f59e0b"},{type:"pointer",delay:1e3,duration:3e3,intensity:"medium",color:"#ef4444"}],fs=[{type:"float",delay:0,duration:3e3,intensity:"high",color:"#10b981"},{type:"glow",delay:1500,duration:2e3,intensity:"medium",color:"#10b981"}],xs=[{type:"breathe",delay:0,duration:4e3,intensity:"low",color:"#8b5cf6"},{type:"glow",delay:2e3,duration:3e3,intensity:"medium",color:"#8b5cf6"}],vs=({className:e})=>{const{state:t,getCurrentStep:s,nextStep:a,prevStep:i,skipStep:n,finishTutorial:o,isLastStep:c,isFirstStep:l}=ds(),[d,h]=r.useState(null),[u,m]=r.useState({x:0,y:0,placement:"top"}),p=r.useRef(null),g=s();r.useEffect(()=>{if(!(null==g?void 0:g.targetElement))return void h(null);const e=()=>{const e=document.querySelector(g.targetElement);return!!e&&(h(e),!0)};if(!e()&&g.waitForElement){const t=setInterval(()=>{e()&&clearInterval(t)},100),s=setTimeout(()=>{clearInterval(t)},5e3);return()=>{clearInterval(t),clearTimeout(s)}}},[g]),r.useEffect(()=>{if(!d)return;const e=()=>{const e=d.getBoundingClientRect(),t=e.left+e.width/2,s=e.top+e.height/2;let a=t,i=s,n="top";if(null==g?void 0:g.position)switch(g.position){case"top":i=e.top-20,n="top";break;case"bottom":i=e.bottom+20,n="bottom";break;case"left":a=e.left-20,i=s,n="left";break;case"right":a=e.right+20,i=s,n="right";break;default:n="top"}m({x:a,y:i,placement:n})};return e(),window.addEventListener("resize",e),window.addEventListener("scroll",e),()=>{window.removeEventListener("resize",e),window.removeEventListener("scroll",e)}},[d,g]),r.useEffect(()=>{g&&(async()=>{try{g.onBeforeShow&&await g.onBeforeShow(),g.onAfterShow&&await g.onAfterShow()}catch(e){}})()},[g]);const y=async()=>{if(g)try{if(g.onBeforeNext&&!(await g.onBeforeNext()))return;c()?o():a(),g.onAfterNext&&await g.onAfterNext()}catch(e){}};return r.useEffect(()=>{const e=e=>{if(t.isActive)switch(e.key){case"Escape":!1!==(null==g?void 0:g.skipable)&&n();break;case"ArrowRight":case"Enter":e.preventDefault(),y();break;case"ArrowLeft":e.preventDefault(),l()||i()}};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[t.isActive,g,l,c]),t.isActive&&g?W.jsxs("div",{ref:p,className:"tutorial-overlay ".concat(e||""),onClick:e=>{e.target===e.currentTarget&&!1!==(null==g?void 0:g.skipable)&&n()},children:[W.jsx("div",{className:"tutorial-backdrop"}),d&&W.jsx(us,{targetElement:d,style:{borderRadius:"8px",padding:8,animation:"tutorial-pulse 2s infinite"}}),W.jsx(hs,{step:g,position:u,onNext:y,onPrev:i,onSkip:n,onFinish:o,onExitTutorial:o,canGoNext:!c(),canGoPrev:!l(),canSkip:!1!==g.skipable,currentIndex:t.currentStepIndex,totalSteps:t.steps.length}),W.jsx(ps,{targetElement:g.targetElement,isActive:!0,animationSequence:(f=g,"celebration"===f.action?fs:(null==(x=f.id)?void 0:x.includes("welcome"))||(null==(v=f.id)?void 0:v.includes("intro"))?gs:(null==(b=f.id)?void 0:b.includes("focus"))||(null==(S=f.id)?void 0:S.includes("concentrate"))?xs:"high"===f.priority||(null==(E=f.id)?void 0:E.includes("important"))?ys:gs)}),g.customComponent&&W.jsx(g.customComponent,{step:g,isActive:!0,onNext:y,onPrev:i,onSkip:n,onFinish:o,currentIndex:t.currentStepIndex,totalSteps:t.steps.length})]}):null;var f,x,v,b,S,E},bs={mobile:"(max-width: ".concat(375,"px)"),tablet:"(max-width: ".concat(768,"px)")};function Ss(){if("undefined"==typeof window)return"desktop";const e=window.innerWidth;return e<=375?"mobile":e<=768?"tablet":"desktop"}function Es(){return"undefined"==typeof window?"landscape":window.innerHeight>window.innerWidth?"portrait":"landscape"}function ws(e){const[t,s]=r.useState(()=>function(e){return"undefined"!=typeof window&&window.matchMedia(e).matches}(e));return r.useEffect(()=>function(e,t){if("undefined"==typeof window)return()=>{};const s=window.matchMedia(e),a=e=>t(e.matches);return t(s.matches),s.addEventListener("change",a),()=>s.removeEventListener("change",a)}(e,s),[e]),t}function Ns(){const[e,t]=r.useState(()=>Ss());return r.useEffect(()=>{const e=()=>t(Ss());return window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]),e}function Ts(){const[e,t]=r.useState(()=>Es());return r.useEffect(()=>{const e=()=>t(Es()),s=()=>{setTimeout(e,100)};return window.addEventListener("resize",e),window.addEventListener("orientationchange",s),()=>{window.removeEventListener("resize",e),window.removeEventListener("orientationchange",s)}},[]),e}const js=({children:e,maxWidth:t={mobile:"100%",tablet:"768px",desktop:"1200px"},fluid:s=!1,className:a="",style:i={}})=>{const n=Ns(),r={width:"100%",maxWidth:s?"100%":"number"==typeof t||"string"==typeof t?t:t[n]||t.desktop||"1200px",margin:"0 auto",padding:"mobile"===n?"0 16px":"0 24px",...i};return W.jsx("div",{className:"responsive-container ".concat(a),style:r,children:e})},Rs=({children:e,direction:t="row",align:s="start",justify:a="start",wrap:i=!1,gap:n=0,className:r="",style:o={}})=>{const c=Ns(),l={display:"flex",flexDirection:"string"==typeof t?t:t[c]||t.desktop||"row",alignItems:"start"===s?"flex-start":"end"===s?"flex-end":"center"===s?"center":"stretch",justifyContent:"start"===a?"flex-start":"end"===a?"flex-end":"center"===a?"center":"between"===a?"space-between":"around"===a?"space-around":"evenly"===a?"space-evenly":"flex-start",flexWrap:i?"wrap":"nowrap",gap:n,...o};return W.jsx("div",{className:"responsive-flex ".concat(r),style:l,children:e})},_s=({children:e,on:t,above:s,below:a,className:i=""})=>{const n=Ns(),r=ws(bs.mobile),o=ws(bs.tablet);return(()=>{if(t)return t.includes(n);if(s){if("mobile"===s)return!r;if("tablet"===s)return!o&&!r;if("desktop"===s)return!o&&!r}if(a){if("desktop"===a)return o||r;if("tablet"===a)return r}return!0})()?W.jsx("div",{className:"responsive-show ".concat(i),children:e}):null},Is=({children:e,mobile:t,tablet:s,desktop:a})=>{switch(Ns()){case"mobile":return W.jsx(W.Fragment,{children:t||e});case"tablet":return W.jsx(W.Fragment,{children:s||e});case"desktop":return W.jsx(W.Fragment,{children:a||e});default:return W.jsx(W.Fragment,{children:e})}},ks=({children:e,baseWidth:t=800,baseHeight:s=600,maintainAspectRatio:a=!0,className:i=""})=>{const n=Ns(),r=Ts();return W.jsx("div",{className:"responsive-game-canvas ".concat(i),style:(()=>{if("mobile"===n){const e=t/s;return"portrait"===r?{width:"100%",height:a?"".concat(.9*window.innerWidth/e,"px"):"auto",maxHeight:"60vh",margin:"0 auto"}:{width:"70vw",height:a?"".concat(.7*window.innerWidth/e,"px"):"auto",maxHeight:"80vh",margin:"0 auto"}}return"tablet"===n?{width:"portrait"===r?"90%":"70%",height:"auto",maxWidth:"".concat(t,"px"),maxHeight:"".concat(s,"px"),margin:"0 auto"}:{width:t,height:s,margin:"0 auto"}})(),children:e})},As=({children:e,collapsible:t=!0,defaultCollapsed:s=!1,className:a=""})=>{const i=Ns(),[n,r]=A.useState("mobile"===i||s);A.useEffect(()=>{"mobile"!==i||n||r(!0)},[i]);const o={width:"mobile"===i?n?"0":"100%":n?"60px":"320px",transition:"width 0.3s ease",overflow:"hidden",position:"mobile"===i?"fixed":"relative",top:"mobile"===i?0:"auto",left:"mobile"===i?0:"auto",height:"mobile"===i?"100vh":"auto",zIndex:"mobile"===i?1e3:"auto",backgroundColor:"rgba(255, 255, 255, 0.95)",backdropFilter:"blur(10px)"};return W.jsxs(W.Fragment,{children:["mobile"===i&&!n&&W.jsx("div",{className:"sidebar-overlay",style:{position:"fixed",top:0,left:0,right:0,bottom:0,backgroundColor:"rgba(0, 0, 0, 0.5)",zIndex:999},onClick:()=>r(!0)}),W.jsxs("div",{className:"responsive-sidebar ".concat(a),style:o,children:[t&&W.jsx("button",{className:"sidebar-toggle",onClick:()=>r(!n),style:{position:"absolute",top:"10px",right:"10px",zIndex:10,background:"rgba(255, 255, 255, 0.9)",border:"1px solid #ccc",borderRadius:"4px",padding:"5px 10px",cursor:"pointer"},children:n?"📊":"✕"}),W.jsx("div",{className:"sidebar-content",style:{opacity:n?0:1,transition:"opacity 0.3s ease",padding:n?0:"20px"},children:e})]})]})},Cs=[{id:"welcome",title:"欢迎来到自律农场！",description:"这是一个通过摄像头监测你的专注状态，帮助你养成良好习惯的农场游戏。让我们开始设置吧！",position:"center",skipable:!1,onAfterShow:async()=>{await new Promise(e=>setTimeout(e,500))}},{id:"camera-toggle",title:"开启摄像头",description:'首先，我们需要开启摄像头来监测你的姿态。点击"显示摄像头"按钮开始。',targetElement:".toggle-camera-btn",position:"bottom",action:"click",waitForElement:!0,onBeforeNext:async()=>null!==document.querySelector(".camera-section")},{id:"camera-permission",title:"允许摄像头权限",description:'浏览器会请求摄像头权限，请点击"允许"以便我们能够检测你的姿态。这些数据只在本地处理，不会上传到服务器。',targetElement:".camera-section",position:"right",waitForElement:!0,onBeforeNext:async()=>{var e;let t=0;for(;t<30;){const s=document.querySelector(".status-indicator");if(s&&(null==(e=s.textContent)?void 0:e.includes("🟢")))return!0;await new Promise(e=>setTimeout(e,500)),t++}return!1}},{id:"camera-position",title:"调整摄像头位置",description:"请调整你的坐姿，确保摄像头能清楚看到你的上半身。保持坐姿端正，这样系统能更好地检测你的专注状态。",targetElement:".main-camera",position:"right",onAfterShow:async()=>{await new Promise(e=>setTimeout(e,2e3))}},{id:"farm-intro",title:"你的专注农场",description:"这是你的专属农场！当你保持专注时，农场里的植物会茁壮成长。让我们来了解一下农场的各个区域。",targetElement:".game-section",position:"top"},{id:"control-panel",title:"控制面板",description:"这里显示你的专注状态、农场统计和操作按钮。你可以在这里查看当前的专注度和农场收成。",targetElement:".control-panel",position:"left"},{id:"focus-status",title:"专注状态监测",description:"这里实时显示你的专注度。当你坐姿端正、注视前方时，专注度会提高，农场里的植物就会开始生长！",targetElement:".focus-indicator",position:"left"},{id:"start-focus",title:"开始专注训练",description:'点击"开始专注学习"按钮来开始你的第一次专注会话。记住要保持良好的坐姿！',targetElement:".focus-btn",position:"left",action:"click",onBeforeNext:async()=>{const e=document.querySelector(".focus-btn");return(null==e?void 0:e.classList.contains("active"))||!1}},{id:"maintain-posture",title:"保持专注姿态",description:"很好！现在请保持端正的坐姿，注视前方。你会看到专注度逐渐提升，当达到一定水平时，农场里就会长出第一株植物！",targetElement:".focus-score",position:"left",onBeforeNext:async()=>{var e;let t=0;for(;t<60;){const s=document.querySelector(".focus-score");if(s){const t=s.textContent||"";if(parseInt((null==(e=t.match(/\d+/))?void 0:e[0])||"0")>=70)return!0}await new Promise(e=>setTimeout(e,500)),t++}return!0}},{id:"first-plant",title:"第一株植物诞生！",description:"恭喜！你已经成功种植了第一株知识花。继续保持专注，你的农场会越来越繁荣！",targetElement:".phaser-container",position:"top",onAfterShow:async()=>{await new Promise(e=>setTimeout(e,3e3))}},{id:"first-planting-celebration",title:"庆祝第一次种植！",description:"让我们为这个重要的里程碑庆祝一下！",position:"center",action:"celebration",customComponent:({step:e,onNext:t,currentIndex:s,totalSteps:a})=>{var i;const n=(null==(i=e.actionData)?void 0:i.plantType)||"knowledge",[o,c]=r.useState(!1),[l,d]=r.useState("appearing");r.useEffect(()=>{const e=setTimeout(()=>{c(!0),d("celebrating")},500),t=setTimeout(()=>{d("finishing")},3e3);return()=>{clearTimeout(e),clearTimeout(t)}},[]);const h=(s+1)/a*100;return W.jsxs("div",{className:"planting-celebration ".concat(l),children:[W.jsx("div",{className:"celebration-backdrop"}),o&&W.jsx("div",{className:"confetti-container",children:Array.from({length:50},(e,t)=>W.jsx("div",{className:"confetti confetti-".concat(t%6+1),style:{left:"".concat(100*Math.random(),"%"),animationDelay:"".concat(3*Math.random(),"s"),animationDuration:"".concat(3+2*Math.random(),"s")}},t))}),W.jsxs("div",{className:"celebration-content",children:[W.jsxs("div",{className:"celebration-icon",children:[W.jsx("div",{className:"plant-icon",children:(e=>{switch(e){case"knowledge":return"🌸";case"strength":return"🌳";case"time":return"🥬";case"meditation":return"🪷";default:return"🌱"}})(n)}),W.jsxs("div",{className:"sparkle-effects",children:[W.jsx("span",{className:"sparkle sparkle-1",children:"✨"}),W.jsx("span",{className:"sparkle sparkle-2",children:"⭐"}),W.jsx("span",{className:"sparkle sparkle-3",children:"🌟"})]})]}),W.jsxs("div",{className:"celebration-text",children:[W.jsx("h2",{className:"celebration-title",children:"🎉 恭喜你！"}),W.jsxs("p",{className:"celebration-subtitle",children:["你成功种植了第一株",(e=>{switch(e){case"knowledge":return"知识花";case"strength":return"力量树";case"time":return"时间菜";case"meditation":return"冥想莲";default:return"植物"}})(n),"！"]}),W.jsxs("div",{className:"celebration-description",children:[W.jsx("p",{children:"通过保持专注，你让农场焕发了生机。"}),W.jsx("p",{children:"继续保持良好的姿态，你的农场会越来越繁荣！"})]})]}),W.jsxs("div",{className:"achievement-badge",children:[W.jsx("div",{className:"badge-icon",children:"🏆"}),W.jsxs("div",{className:"badge-text",children:[W.jsx("span",{className:"badge-title",children:"新手农夫"}),W.jsx("span",{className:"badge-desc",children:"第一次种植成功"})]})]}),W.jsxs("div",{className:"celebration-footer",children:[W.jsxs("div",{className:"progress-info",children:[W.jsxs("span",{className:"progress-text",children:["引导进度: ",s+1," / ",a]}),W.jsx("div",{className:"progress-bar",children:W.jsx("div",{className:"progress-fill",style:{width:"".concat(h,"%")}})})]}),W.jsx("button",{className:"celebration-continue-btn",onClick:t,children:"继续农场之旅 🌱"})]})]})]})},actionData:{plantType:"knowledge"},skipable:!1},{id:"farm-stats",title:"📊 农场统计面板",description:"这里显示你培养的各种植物数量。知识花🌸来自专注学习，力量树🌳来自运动，时间菜🥬来自时间管理，冥想莲🪷来自冥想练习。",targetElement:".stats-grid",position:"left",onAfterShow:async()=>{const e=document.querySelector(".stats-grid");e&&(e.classList.add("tutorial-highlight-stats"),setTimeout(()=>{e.classList.remove("tutorial-highlight-stats")},3e3)),await new Promise(e=>setTimeout(e,2e3))}},{id:"daily-data",title:"📈 今日数据追踪",description:"查看你今天的专注时长和表现。建议每天至少专注30分钟，保持80%以上的专注度可获得更好的成长效果！",targetElement:".daily-stats",position:"left",onAfterShow:async()=>{const e=document.querySelector(".daily-stats");e&&(e.classList.add("tutorial-highlight-daily"),setTimeout(()=>{e.classList.remove("tutorial-highlight-daily")},3e3)),await new Promise(e=>setTimeout(e,2e3))}},{id:"rewards",title:"🎁 成就奖励系统",description:"完成每日目标获得奖励！🏆连续专注30分钟、🌱培养5株植物、⭐保持高专注度。达成目标会有特殊奖励！",targetElement:".reward-container",position:"left",onAfterShow:async()=>{const e=document.querySelector(".reward-container");e&&(e.classList.add("tutorial-highlight-rewards"),setTimeout(()=>{e.classList.remove("tutorial-highlight-rewards")},3e3)),await new Promise(e=>setTimeout(e,2e3))}},{id:"other-activities",title:"🛠️ 多样化活动中心",description:"探索不同的专注活动！💡专注学习培养知识花，🏃运动打卡培养力量树，⏰时间管理培养时间菜，🧘冥想练习培养冥想莲。",targetElement:".action-buttons",position:"left",onAfterShow:async()=>{const e=document.querySelector(".action-buttons");e&&(e.classList.add("tutorial-highlight-actions"),setTimeout(()=>{e.classList.remove("tutorial-highlight-actions")},3e3)),await new Promise(e=>setTimeout(e,2e3))}},{id:"tutorial-complete",title:"教程完成！",description:"恭喜你完成了所有的新手引导！现在你已经掌握了自律农场的基本操作。继续保持专注，让你的农场茁壮成长吧！",position:"center",skipable:!1,onAfterShow:async()=>{}}],Ds=[{id:"corn",name:"玉米",nameEn:"Corn",category:"grain",exchange:"DCE",icon:"🌽",basePrice:2800,description:"中国第一大粮食作物，主要用于饲料和工业原料",yieldRanges:{[je.GRAY]:{min:400,max:500},[je.GREEN]:{min:500,max:600},[je.BLUE]:{min:600,max:720},[je.ORANGE]:{min:720,max:850},[je.GOLD]:{min:850,max:1e3},[je.GOLD_RED]:{min:1e3,max:1200}}},{id:"soybean",name:"大豆",nameEn:"Soybean",category:"oilseed",exchange:"DCE",icon:"🫘",basePrice:4200,description:"重要的油料作物和蛋白质来源",yieldRanges:{[je.GRAY]:{min:120,max:150},[je.GREEN]:{min:150,max:180},[je.BLUE]:{min:180,max:220},[je.ORANGE]:{min:220,max:260},[je.GOLD]:{min:260,max:300},[je.GOLD_RED]:{min:300,max:350}}},{id:"wheat",name:"小麦",nameEn:"Wheat",category:"grain",exchange:"CZCE",icon:"🌾",basePrice:2600,description:"主要的粮食作物，制作面粉的原料",yieldRanges:{[je.GRAY]:{min:300,max:400},[je.GREEN]:{min:400,max:500},[je.BLUE]:{min:500,max:600},[je.ORANGE]:{min:600,max:720},[je.GOLD]:{min:720,max:850},[je.GOLD_RED]:{min:850,max:1e3}}},{id:"rice",name:"粳米",nameEn:"Rice",category:"grain",exchange:"CZCE",icon:"🍚",basePrice:3800,description:"中国主要的粮食作物之一",yieldRanges:{[je.GRAY]:{min:450,max:550},[je.GREEN]:{min:550,max:650},[je.BLUE]:{min:650,max:780},[je.ORANGE]:{min:780,max:920},[je.GOLD]:{min:920,max:1080},[je.GOLD_RED]:{min:1080,max:1300}}},{id:"rapeseed",name:"菜籽",nameEn:"Rapeseed",category:"oilseed",exchange:"CZCE",icon:"🌻",basePrice:5200,description:"重要的油料作物，菜籽油原料",yieldRanges:{[je.GRAY]:{min:100,max:130},[je.GREEN]:{min:130,max:160},[je.BLUE]:{min:160,max:200},[je.ORANGE]:{min:200,max:240},[je.GOLD]:{min:240,max:280},[je.GOLD_RED]:{min:280,max:330}}},{id:"peanut",name:"花生",nameEn:"Peanut",category:"oilseed",exchange:"CZCE",icon:"🥜",basePrice:7800,description:"重要的经济作物，可榨油也可食用",yieldRanges:{[je.GRAY]:{min:200,max:250},[je.GREEN]:{min:250,max:300},[je.BLUE]:{min:300,max:360},[je.ORANGE]:{min:360,max:430},[je.GOLD]:{min:430,max:500},[je.GOLD_RED]:{min:500,max:580}}},{id:"cotton",name:"棉花",nameEn:"Cotton",category:"fiber",exchange:"CZCE",icon:"☁️",basePrice:15e3,description:"重要的纤维作物，纺织工业原料",yieldRanges:{[je.GRAY]:{min:80,max:100},[je.GREEN]:{min:100,max:120},[je.BLUE]:{min:120,max:145},[je.ORANGE]:{min:145,max:170},[je.GOLD]:{min:170,max:200},[je.GOLD_RED]:{min:200,max:240}}},{id:"sugar",name:"白糖",nameEn:"Sugar",category:"sugar",exchange:"CZCE",icon:"🍯",basePrice:5800,description:"甘蔗和甜菜加工制成的食用糖",yieldRanges:{[je.GRAY]:{min:4e3,max:5e3},[je.GREEN]:{min:5e3,max:6e3},[je.BLUE]:{min:6e3,max:7200},[je.ORANGE]:{min:7200,max:8500},[je.GOLD]:{min:8500,max:1e4},[je.GOLD_RED]:{min:1e4,max:12e3}}},{id:"apple",name:"苹果",nameEn:"Apple",category:"fruit",exchange:"CZCE",icon:"🍎",basePrice:8500,description:"优质水果，营养丰富",yieldRanges:{[je.GRAY]:{min:2e3,max:2500},[je.GREEN]:{min:2500,max:3e3},[je.BLUE]:{min:3e3,max:3600},[je.ORANGE]:{min:3600,max:4200},[je.GOLD]:{min:4200,max:5e3},[je.GOLD_RED]:{min:5e3,max:6e3}}},{id:"red_jujube",name:"红枣",nameEn:"Red Jujube",category:"fruit",exchange:"CZCE",icon:"🔴",basePrice:12e3,description:"传统干果，药食同源",yieldRanges:{[je.GRAY]:{min:800,max:1e3},[je.GREEN]:{min:1e3,max:1200},[je.BLUE]:{min:1200,max:1450},[je.ORANGE]:{min:1450,max:1700},[je.GOLD]:{min:1700,max:2e3},[je.GOLD_RED]:{min:2e3,max:2400}}},{id:"live_pig",name:"生猪",nameEn:"Live Pig",category:"livestock",exchange:"DCE",icon:"🐷",basePrice:16e3,description:"重要的畜牧产品，肉类供应主力",yieldRanges:{[je.GRAY]:{min:90,max:110},[je.GREEN]:{min:110,max:130},[je.BLUE]:{min:130,max:150},[je.ORANGE]:{min:150,max:175},[je.GOLD]:{min:175,max:200},[je.GOLD_RED]:{min:200,max:230}}},{id:"egg",name:"鸡蛋",nameEn:"Egg",category:"livestock",exchange:"DCE",icon:"🥚",basePrice:8800,description:"重要的蛋白质来源",yieldRanges:{[je.GRAY]:{min:180,max:220},[je.GREEN]:{min:220,max:260},[je.BLUE]:{min:260,max:310},[je.ORANGE]:{min:310,max:360},[je.GOLD]:{min:360,max:420},[je.GOLD_RED]:{min:420,max:500}}},{id:"soybean_meal",name:"豆粕",nameEn:"Soybean Meal",category:"feed",exchange:"DCE",icon:"🌰",basePrice:3200,description:"大豆榨油后的副产品，重要的蛋白饲料",yieldRanges:{[je.GRAY]:{min:75,max:80},[je.GREEN]:{min:80,max:82},[je.BLUE]:{min:82,max:84},[je.ORANGE]:{min:84,max:86},[je.GOLD]:{min:86,max:88},[je.GOLD_RED]:{min:88,max:90}}}];function Ms(e){return Ds.find(t=>t.id===e)}function Os(e){return{[je.GRAY]:"普通",[je.GREEN]:"优质",[je.BLUE]:"稀有",[je.ORANGE]:"史诗",[je.GOLD]:"传说",[je.GOLD_RED]:"神话"}[e]}function Ls(e,t){const s=Ms(e);if(!s)return"未知品种";const a=Os(t);return"".concat(a).concat(s.name)}function Fs(e){const t={"玉米":"corn","大豆":"soybean","小麦":"wheat","粳米":"rice","菜籽":"rapeseed","花生":"peanut","棉花":"cotton","白糖":"sugar","苹果":"apple","红枣":"red_jujube","生猪":"live_pig","鸡蛋":"egg","豆粕":"soybean_meal"};for(const[s,a]of Object.entries(t))if(e.includes(s))return a;return"unknown_".concat(e.replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g,"_"))}function Ps(e){return["玉米","大豆","小麦","粳米","菜籽","花生","棉花","白糖","苹果","红枣","生猪","鸡蛋","豆粕"].some(t=>e.includes(t))}function Gs(e){const t=[je.GRAY,je.GREEN,je.BLUE,je.ORANGE,je.GOLD,je.GOLD_RED],s=t.indexOf(e);return s>=0&&s<t.length-1?t[s+1]:null}const Bs=({itemManager:e,className:t=""})=>{const[s,a]=r.useState([]),[i,n]=r.useState(null),[o,c]=r.useState(null),[l,d]=r.useState({success:!1,message:"",animation:""}),[h,u]=r.useState(!1),m=r.useCallback(()=>{const t=e.getAllItems();a(t)},[e]);A.useEffect(()=>{m();const t=()=>m();return e.onItemAdded(t),e.onItemRemoved(t),e.onItemUpdated(t),()=>{e.off("itemAdded",t),e.off("itemRemoved",t),e.off("itemUpdated",t)}},[e,m]);const p=()=>{n(null),c(null)},g=e=>{e.preventDefault(),e.dataTransfer.dropEffect="move"},y=()=>{c(null)},f=(e,t)=>!(!Ps(e.name)||!Ps(t.name))&&(e.rarity===t.rarity&&(Fs(e.name)===Fs(t.name)&&null!==Gs(e.rarity))),x=A.useMemo(()=>{const e={};return s.forEach(t=>{const s=Fs(t.name);e[s]||(e[s]=[]);for(let a=0;a<t.quantity;a++)e[s].push({...t,id:"".concat(t.id,"_").concat(a),quantity:1,originalId:t.id})}),e},[s]);return W.jsxs("div",{className:"chinese-futures-inventory ".concat(t),children:[l.message&&W.jsxs(W.Fragment,{children:[W.jsx("div",{className:"synthesis-backdrop",onClick:()=>d({success:!1,message:"",animation:""})}),W.jsx("div",{className:"synthesis-notification ".concat(l.animation),children:W.jsxs("div",{className:"notification-content",children:["processing"===l.animation&&"⚡ ","success"===l.animation&&"✨ ","failure"===l.animation&&"❌ ",l.message,l.resultItem&&W.jsxs("div",{className:"result-item",children:[W.jsx("span",{className:"result-icon",children:l.resultItem.icon}),W.jsx("span",{className:"result-name",children:l.resultItem.name})]})]})})]}),W.jsxs("div",{className:"synthesis-instructions",children:[W.jsx("h3",{children:"🎯 中国期货农产品背包合成"}),W.jsx("p",{children:"拖拽一个农产品到相同品种、相同品质的另一个农产品上进行合成升级"}),W.jsxs("div",{className:"success-rates",children:[W.jsx("span",{children:"成功率: "}),W.jsx("span",{style:{color:ke[je.GRAY]},children:"普通→优质 95%"}),W.jsx("span",{style:{color:ke[je.GREEN]},children:"优质→稀有 90%"}),W.jsx("span",{style:{color:ke[je.BLUE]},children:"稀有→史诗 85%"}),W.jsx("span",{style:{color:ke[je.ORANGE]},children:"史诗→传说 75%"}),W.jsx("span",{style:{color:ke[je.GOLD]},children:"传说→神话 60%"})]})]}),W.jsx("div",{className:"grouped-inventory",children:Object.entries(x).map(([t,s])=>{const a=Ms(t);return W.jsxs("div",{className:"variety-group",children:[W.jsxs("div",{className:"variety-header",children:[W.jsx("span",{className:"variety-icon",children:null==a?void 0:a.icon}),W.jsx("span",{className:"variety-name",children:null==a?void 0:a.name}),W.jsxs("span",{className:"variety-count",children:["(",s.length,")"]}),W.jsx("span",{className:"variety-exchange",children:null==a?void 0:a.exchange})]}),W.jsx("div",{className:"variety-items",children:s.map((t,s)=>{const a=(null==o?void 0:o.id)===t.id,r=i&&f(i,t);return W.jsxs("div",{draggable:!h,onDragStart:e=>((e,t)=>{n(e),t.dataTransfer.effectAllowed="move"})(t,e),onDragEnd:p,onDragOver:g,onDragEnter:e=>((e,t)=>{t.preventDefault(),c(e)})(t,e),onDragLeave:y,onDrop:s=>(async(t,s)=>{if(s.preventDefault(),c(null),i&&i.id!==t.id)return f(i,t)?void(await(async(t,s)=>{var a,i,n,r,o,c,l;u(!0),d({success:!1,message:"🔥 合成中...",animation:"processing"});try{await new Promise(e=>setTimeout(e,3e3));const h=t.rarity,u=Gs(h),p=(l=h,{[je.GRAY]:.95,[je.GREEN]:.9,[je.BLUE]:.85,[je.ORANGE]:.75,[je.GOLD]:.6,[je.GOLD_RED]:0}[l]||0);if(Math.random()<p&&u){d({success:!1,message:"✨ 合成成功！",animation:"success"}),await new Promise(e=>setTimeout(e,1e3));const r=Fs(t.name),o=Ms(r),c=function(e,t){const s=Ms(e);if(!s)return 0;const a=s.yieldRanges[t];return Math.floor(Math.random()*(a.max-a.min+1))+a.min}(r,u),l=e.addManualItem({name:Ls(r,u),icon:(null==o?void 0:o.icon)||"🌾",rarity:u,category:t.category,type:t.type,value:((null==o?void 0:o.basePrice)||1e3)*Math.pow(2,Object.values(je).indexOf(u)),quantity:1,description:"通过合成获得的".concat(Os(u),"品质").concat(null==o?void 0:o.name,"，产量: ").concat(c),source:{type:"lootbox",timestamp:Date.now()}}),h=t.originalId||t.id.split("_")[0],p=s.originalId||s.id.split("_")[0],g=e.getAllItems(),y=g.find(e=>e.id===h),f=g.find(e=>e.id===p);y&&(y.quantity>1?y.quantity-=1:null==(a=e.integratedItems)||a.delete(h)),f&&p!==h?f.quantity>1?f.quantity-=1:null==(i=e.integratedItems)||i.delete(p):y&&p===h&&(y.quantity>1?y.quantity-=1:null==(n=e.integratedItems)||n.delete(h)),d({success:!0,resultItem:{id:l.id,itemId:l.id,name:l.name,icon:l.icon,rarity:l.rarity,category:l.category,type:l.type,quantity:1,description:l.description||"",obtainedAt:Date.now()},message:"🎉 合成成功！获得 ".concat(l.name),animation:"success"}),m()}else{d({success:!1,message:"💥 合成失败！",animation:"failure"}),await new Promise(e=>setTimeout(e,1e3));const a=t.originalId||t.id.split("_")[0],i=s.originalId||s.id.split("_")[0],n=e.getAllItems(),l=n.find(e=>e.id===a),h=n.find(e=>e.id===i);l&&(l.quantity>1?l.quantity-=1:null==(r=e.integratedItems)||r.delete(a)),h&&i!==a?h.quantity>1?h.quantity-=1:null==(o=e.integratedItems)||o.delete(i):l&&i===a&&(l.quantity>1?l.quantity-=1:null==(c=e.integratedItems)||c.delete(a)),d({success:!1,message:"💥 合成失败！成功率: ".concat(Math.round(100*p),"%"),animation:"failure"}),m()}}catch(h){d({success:!1,message:"❌ 合成过程出现错误！",animation:"failure"})}finally{u(!1),setTimeout(()=>d({success:!1,message:"",animation:""}),4e3)}})(i,t)):(d({success:!1,message:"只能合成相同品种和品质的农产品！",animation:"failure"}),void setTimeout(()=>d({success:!1,message:"",animation:""}),3e3))})(t,s),className:"\n                        inventory-item \n                        ".concat(a?"drag-over":""," \n                        ").concat(r?"can-drop":"","\n                        ").concat((null==i?void 0:i.id)===t.id?"dragging":"","\n                      "),style:{background:"linear-gradient(135deg, ".concat(ke[t.rarity],"15, ").concat(ke[t.rarity],"25)"),border:"2px solid ".concat(a&&r?"#00FF00":a&&!r?"#FF0000":ke[t.rarity]),opacity:(null==i?void 0:i.id)===t.id?.5:1,transform:a?"scale(1.05)":"scale(1)",cursor:h?"not-allowed":"grab"},title:"".concat(t.name,"\n").concat(t.description,"\n拖拽到相同品种物品上合成"),children:[W.jsx("div",{className:"item-icon",children:t.icon}),W.jsxs("div",{className:"item-info",children:[W.jsx("div",{className:"item-name",children:t.name}),W.jsx("div",{className:"item-rarity",style:{color:ke[t.rarity]},children:Os(t.rarity)})]}),i&&t.id!==i.id&&W.jsx("div",{className:"synthesis-indicator ".concat(r?"compatible":"incompatible"),children:r?"✓":"✗"})]},t.id)})})]},t)})}),W.jsx("style",{children:"\n        .chinese-futures-inventory {\n          padding: 20px;\n          max-height: 70vh;\n          overflow-y: auto;\n        }\n\n        .synthesis-backdrop {\n          position: fixed;\n          top: 0;\n          left: 0;\n          width: 100%;\n          height: 100%;\n          background: rgba(0, 0, 0, 0.6);\n          z-index: 999;\n          animation: backdrop-fade-in 0.3s ease-out;\n        }\n\n        .synthesis-notification {\n          position: fixed;\n          top: 50%;\n          left: 50%;\n          transform: translate(-50%, -50%);\n          z-index: 1001;\n          padding: 25px 35px;\n          border-radius: 20px;\n          color: white;\n          font-weight: bold;\n          max-width: 450px;\n          min-width: 320px;\n          text-align: center;\n          box-shadow: 0 12px 40px rgba(0,0,0,0.5);\n          backdrop-filter: blur(15px);\n          animation: notification-scale-in 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);\n        }\n\n        .synthesis-notification.processing {\n          background: linear-gradient(45deg, #FF9800, #FFC107);\n          animation: pulse 1s infinite, glow 2s ease-in-out infinite alternate;\n        }\n\n        .synthesis-notification.success {\n          background: linear-gradient(45deg, #4CAF50, #8BC34A, #CDDC39);\n          animation: success-bounce 0.6s ease-out, sparkle 2s linear infinite;\n        }\n\n        .synthesis-notification.failure {\n          background: linear-gradient(45deg, #F44336, #E57373, #EF5350);\n          animation: failure-shake 0.5s ease-out, fade-pulse 1s ease-in-out;\n        }\n\n        .result-item {\n          display: flex;\n          align-items: center;\n          gap: 8px;\n          margin-top: 8px;\n          padding-top: 8px;\n          border-top: 1px solid rgba(255,255,255,0.3);\n          animation: item-appear 0.8s ease-out;\n        }\n\n        .result-icon {\n          font-size: 20px;\n          animation: icon-spin 1s ease-in-out;\n        }\n\n        .synthesis-instructions {\n          background: linear-gradient(135deg, #f8f9fa, #e9ecef);\n          padding: 15px;\n          border-radius: 8px;\n          margin-bottom: 20px;\n          border: 1px solid #dee2e6;\n        }\n\n        .synthesis-instructions h3 {\n          margin: 0 0 8px 0;\n          color: #2C5530;\n          text-shadow: 1px 1px 2px rgba(0,0,0,0.1);\n        }\n\n        .synthesis-instructions p {\n          margin: 0 0 10px 0;\n          color: #666;\n        }\n\n        .success-rates {\n          display: flex;\n          gap: 15px;\n          flex-wrap: wrap;\n          font-size: 12px;\n        }\n\n        .success-rates span:first-child {\n          font-weight: bold;\n          color: #333;\n        }\n\n        .grouped-inventory {\n          display: flex;\n          flex-direction: column;\n          gap: 20px;\n        }\n\n        .variety-group {\n          border: 1px solid #ddd;\n          border-radius: 12px;\n          overflow: hidden;\n          box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n          transition: transform 0.2s ease;\n        }\n\n        .variety-group:hover {\n          transform: translateY(-2px);\n          box-shadow: 0 4px 16px rgba(0,0,0,0.15);\n        }\n\n        .variety-header {\n          background: linear-gradient(45deg, #2C5530, #4CAF50);\n          color: white;\n          padding: 12px 15px;\n          display: flex;\n          align-items: center;\n          gap: 10px;\n          font-weight: bold;\n        }\n\n        .variety-icon {\n          font-size: 20px;\n          animation: gentle-pulse 2s ease-in-out infinite;\n        }\n\n        .variety-name {\n          flex: 1;\n        }\n\n        .variety-count {\n          background: rgba(255,255,255,0.2);\n          padding: 2px 8px;\n          border-radius: 12px;\n          font-size: 12px;\n        }\n\n        .variety-exchange {\n          background: rgba(255,255,255,0.3);\n          padding: 2px 6px;\n          border-radius: 4px;\n          font-size: 10px;\n        }\n\n        .variety-items {\n          padding: 15px;\n          display: grid;\n          grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));\n          gap: 10px;\n        }\n\n        .inventory-item {\n          aspect-ratio: 1;\n          border-radius: 8px;\n          display: flex;\n          flex-direction: column;\n          align-items: center;\n          justify-content: center;\n          transition: all 0.3s ease;\n          position: relative;\n          user-select: none;\n          box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n        }\n\n        .inventory-item:hover {\n          transform: scale(1.05) translateY(-2px);\n          box-shadow: 0 8px 16px rgba(0,0,0,0.2);\n        }\n\n        .inventory-item.drag-over {\n          box-shadow: 0 0 20px rgba(0,255,0,0.8);\n          animation: glow-green 0.5s ease-in-out infinite alternate;\n        }\n\n        .inventory-item.can-drop {\n          border-color: #00FF00 !important;\n          animation: ready-to-drop 1s ease-in-out infinite;\n        }\n\n        .inventory-item.dragging {\n          opacity: 0.6;\n          transform: scale(0.9) rotate(5deg);\n          box-shadow: 0 8px 16px rgba(0,0,0,0.3);\n        }\n\n        .item-icon {\n          font-size: 24px;\n          margin-bottom: 4px;\n          transition: transform 0.2s ease;\n        }\n\n        .inventory-item:hover .item-icon {\n          transform: scale(1.1);\n        }\n\n        .item-info {\n          text-align: center;\n        }\n\n        .item-name {\n          font-size: 11px;\n          font-weight: 600;\n          color: #333;\n          margin-bottom: 2px;\n        }\n\n        .item-rarity {\n          font-size: 9px;\n          font-weight: 500;\n        }\n\n        .synthesis-indicator {\n          position: absolute;\n          top: -5px;\n          right: -5px;\n          width: 20px;\n          height: 20px;\n          border-radius: 50%;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          font-size: 12px;\n          font-weight: bold;\n          color: white;\n          animation: indicator-pulse 1s ease-in-out infinite;\n        }\n\n        .synthesis-indicator.compatible {\n          background: #4CAF50;\n          box-shadow: 0 0 10px rgba(76, 175, 80, 0.6);\n        }\n\n        .synthesis-indicator.incompatible {\n          background: #F44336;\n          box-shadow: 0 0 10px rgba(244, 67, 54, 0.6);\n        }\n\n        @keyframes pulse {\n          0%, 100% { opacity: 1; transform: scale(1); }\n          50% { opacity: 0.8; transform: scale(1.05); }\n        }\n\n        @keyframes glow {\n          0% { box-shadow: 0 0 5px rgba(255, 152, 0, 0.5); }\n          100% { box-shadow: 0 0 20px rgba(255, 152, 0, 0.8), 0 0 30px rgba(255, 193, 7, 0.6); }\n        }\n\n        @keyframes success-bounce {\n          0% { transform: scale(0.5) translateY(-20px); opacity: 0; }\n          60% { transform: scale(1.1) translateY(0); opacity: 1; }\n          100% { transform: scale(1) translateY(0); opacity: 1; }\n        }\n\n        @keyframes sparkle {\n          0%, 100% { \n            background: linear-gradient(45deg, #4CAF50, #8BC34A, #CDDC39);\n          }\n          25% { \n            background: linear-gradient(45deg, #8BC34A, #CDDC39, #4CAF50);\n          }\n          50% { \n            background: linear-gradient(45deg, #CDDC39, #4CAF50, #8BC34A);\n          }\n          75% { \n            background: linear-gradient(45deg, #4CAF50, #CDDC39, #8BC34A);\n          }\n        }\n\n        @keyframes failure-shake {\n          0%, 100% { transform: translateX(0); }\n          25% { transform: translateX(-8px) rotate(-1deg); }\n          75% { transform: translateX(8px) rotate(1deg); }\n        }\n\n        @keyframes fade-pulse {\n          0%, 100% { opacity: 1; }\n          50% { opacity: 0.7; }\n        }\n\n        @keyframes item-appear {\n          0% { opacity: 0; transform: translateY(10px) scale(0.9); }\n          100% { opacity: 1; transform: translateY(0) scale(1); }\n        }\n\n        @keyframes icon-spin {\n          0% { transform: scale(1) rotate(0deg); }\n          50% { transform: scale(1.2) rotate(180deg); }\n          100% { transform: scale(1) rotate(360deg); }\n        }\n\n        @keyframes gentle-pulse {\n          0%, 100% { transform: scale(1); }\n          50% { transform: scale(1.05); }\n        }\n\n        @keyframes glow-green {\n          0% { box-shadow: 0 0 10px rgba(0, 255, 0, 0.5); }\n          100% { box-shadow: 0 0 25px rgba(0, 255, 0, 0.8); }\n        }\n\n        @keyframes ready-to-drop {\n          0%, 100% { border-width: 2px; }\n          50% { border-width: 3px; }\n        }\n\n        @keyframes indicator-pulse {\n          0%, 100% { transform: scale(1); opacity: 1; }\n          50% { transform: scale(1.1); opacity: 0.8; }\n        }\n\n        @keyframes backdrop-fade-in {\n          0% { opacity: 0; }\n          100% { opacity: 1; }\n        }\n\n        @keyframes notification-scale-in {\n          0% { \n            opacity: 0;\n            transform: translate(-50%, -50%) scale(0.3) rotate(180deg);\n          }\n          70% {\n            transform: translate(-50%, -50%) scale(1.1) rotate(-10deg);\n          }\n          100% { \n            opacity: 1;\n            transform: translate(-50%, -50%) scale(1) rotate(0deg);\n          }\n        }\n      "})]})},Us=()=>{const e=r.useRef(null),t=r.useRef(null),s=r.useRef(null),[a,i]=r.useState("game"),[n,o]=r.useState({totalItems:0,totalValue:0,plantsGrowing:0,synthesesCompleted:0,lootboxesOpened:0});r.useEffect(()=>{const i=document.querySelector('meta[name="viewport"]');if(i&&i.setAttribute("content","width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"),s.current||(s.current=new Ze,c()),t.current&&!e.current&&"game"===a){const s={...ft,parent:t.current,scene:[$e],width:800,height:600,scale:{mode:X.Scale.FIT,autoCenter:X.Scale.CENTER_BOTH,width:800,height:600}};e.current=new X.Game(s)}return()=>{e.current&&"game"!==a&&(e.current.destroy(!0),e.current=null)}},[a]);const c=()=>{if(!s.current)return;const e=s.current;e.onItemAdded(e=>{l()}),e.onItemRemoved(e=>{l()}),e.onItemsSynthesized(e=>{o(e=>({...e,synthesesCompleted:e.synthesesCompleted+1})),l()}),e.onItemPlanted(e=>{o(e=>({...e,plantsGrowing:e.plantsGrowing+1})),l()}),e.onItemHarvested(e=>{o(e=>({...e,plantsGrowing:Math.max(0,e.plantsGrowing-1)})),l()})},l=()=>{if(!s.current)return;const e=s.current.getAllItems(),t=e.reduce((e,t)=>e+t.quantity,0),a=e.reduce((e,t)=>e+(t.value||0)*t.quantity,0);o(e=>({...e,totalItems:t,totalValue:a}))},d=e=>({padding:"16px 20px",backgroundColor:e?"#4CAF50":"transparent",color:e?"white":"#2C5530",border:"none",borderRadius:"0",borderBottom:"1px solid #e0e0e0",cursor:"pointer",fontSize:"1rem",fontWeight:"bold",transition:"all 0.3s ease",textAlign:"left",width:"100%",display:"flex",alignItems:"center",gap:"10px",boxShadow:e?"inset -4px 0 0 #2E7D32":"none"});return W.jsxs("div",{style:{width:"100vw",height:"100vh",overflow:"hidden",backgroundColor:"#f5f5f5",fontFamily:"Arial, sans-serif",display:"flex",flexDirection:"column"},children:[W.jsxs("header",{style:{backgroundColor:"#2C5530",color:"white",padding:"15px 30px",boxShadow:"0 2px 8px rgba(0,0,0,0.2)",display:"flex",justifyContent:"space-between",alignItems:"center"},children:[W.jsx("h1",{style:{fontSize:"1.8rem",margin:0,display:"flex",alignItems:"center",gap:"10px"},children:"🌾 农产品期货游戏系统"}),W.jsxs("div",{style:{display:"flex",gap:"20px",fontSize:"0.9rem"},children:[W.jsxs("div",{children:["📦 物品: ",n.totalItems]}),W.jsxs("div",{children:["💰 价值: ¥",n.totalValue.toLocaleString()]}),W.jsxs("div",{children:["🌱 种植: ",n.plantsGrowing]}),W.jsxs("div",{children:["⚗️ 合成: ",n.synthesesCompleted]}),W.jsxs("div",{children:["🎁 开盒: ",n.lootboxesOpened]})]})]}),W.jsxs("div",{style:{flex:1,display:"flex",overflow:"hidden"},children:[W.jsxs("div",{style:{width:"260px",backgroundColor:"#f8f9fa",borderRight:"2px solid #e0e0e0",display:"flex",flexDirection:"column",boxShadow:"2px 0 8px rgba(0,0,0,0.1)"},children:[W.jsx("div",{style:{padding:"20px",borderBottom:"2px solid #e0e0e0",backgroundColor:"#ffffff",fontSize:"1.1rem",fontWeight:"bold",color:"#2C5530"},children:"🎯 专注模式菜单"}),W.jsxs("nav",{style:{flex:1},children:[W.jsxs("button",{onClick:()=>i("game"),style:d("game"===a),onMouseEnter:e=>{"game"!==a&&(e.currentTarget.style.backgroundColor="#e8f5e8",e.currentTarget.style.transform="translateX(4px)")},onMouseLeave:e=>{"game"!==a&&(e.currentTarget.style.backgroundColor="transparent",e.currentTarget.style.transform="translateX(0)")},children:[W.jsx("span",{children:"🎮"}),W.jsx("span",{children:"游戏场景"})]}),W.jsxs("button",{onClick:()=>i("lootbox"),style:d("lootbox"===a),onMouseEnter:e=>{"lootbox"!==a&&(e.currentTarget.style.backgroundColor="#e8f5e8",e.currentTarget.style.transform="translateX(4px)")},onMouseLeave:e=>{"lootbox"!==a&&(e.currentTarget.style.backgroundColor="transparent",e.currentTarget.style.transform="translateX(0)")},children:[W.jsx("span",{children:"🎁"}),W.jsx("span",{children:"期货盲盒"})]}),W.jsxs("button",{onClick:()=>i("inventory"),style:d("inventory"===a),onMouseEnter:e=>{"inventory"!==a&&(e.currentTarget.style.backgroundColor="#e8f5e8",e.currentTarget.style.transform="translateX(4px)")},onMouseLeave:e=>{"inventory"!==a&&(e.currentTarget.style.backgroundColor="transparent",e.currentTarget.style.transform="translateX(0)")},children:[W.jsx("span",{children:"🎒"}),W.jsx("span",{children:"物品背包(含合成)"})]})]})]}),W.jsxs("div",{style:{flex:1,backgroundColor:"white",padding:"20px",overflow:"auto",display:"flex",flexDirection:"column"},children:["game"===a&&W.jsxs("div",{style:{height:"100%",display:"flex",flexDirection:"column"},children:[W.jsx("div",{style:{flex:1,display:"flex",justifyContent:"center",alignItems:"center"},children:W.jsx("div",{ref:t,style:{border:"2px solid #ddd",borderRadius:"8px",overflow:"hidden",backgroundColor:"#87CEEB"}})}),W.jsxs("div",{style:{marginTop:"15px",padding:"15px",backgroundColor:"#f8f9fa",borderRadius:"6px",fontSize:"0.9rem",color:"#666"},children:["💡 ",W.jsx("strong",{children:"操作提示："}),"点击土地进行种植，等待作物生长后收获。使用左侧菜单切换不同功能。"]})]}),"lootbox"===a&&s.current&&W.jsx("div",{style:{height:"100%",display:"flex",flexDirection:"column"},children:W.jsx(Zt,{onItemsReceived:e=>{o(e=>({...e,lootboxesOpened:e.lootboxesOpened+1})),l()},itemManager:s.current})}),"inventory"===a&&s.current&&W.jsx("div",{style:{height:"100%",display:"flex",flexDirection:"column"},children:W.jsx(Bs,{itemManager:s.current,className:"game-inventory"})})]})]}),W.jsx("footer",{style:{backgroundColor:"#333",color:"#ccc",padding:"10px",textAlign:"center",fontSize:"0.8rem"},children:"💡 提示：通过盲盒获得农产品 → 在背包中拖拽相同品种合成升级 → 在农场种植收获"})]})};function zs(e,t){let s;try{s=e()}catch(a){return}return{getItem:e=>{var t;const a=e=>null===e?null:JSON.parse(e,void 0),i=null!=(t=s.getItem(e))?t:null;return i instanceof Promise?i.then(a):a(i)},setItem:(e,t)=>s.setItem(e,JSON.stringify(t,void 0)),removeItem:e=>s.removeItem(e)}}const Hs=e=>t=>{try{const s=e(t);return s instanceof Promise?s:{then:e=>Hs(e)(s),catch(e){return this}}}catch(s){return{then(e){return this},catch:e=>Hs(e)(s)}}};var qs,Ys=((qs=Ys||{}).COMMON="common",qs.EXCELLENT="excellent",qs.RARE="rare",qs.EPIC="epic",qs.LEGENDARY="legendary",qs);je.GRAY,je.GREEN,je.BLUE,je.ORANGE,je.GOLD;const Ws={common:"普通",excellent:"优质",rare:"稀有",epic:"史诗",legendary:"传说"},Vs={common:"#9E9E9E",excellent:"#4CAF50",rare:"#2196F3",epic:"#FF9800",legendary:"#FFD700"};var Qs,Ks,Xs=((Qs=Xs||{}).FOCUS_GLASSES="focus_glasses",Qs.FOCUS_HEADPHONES="focus_headphones",Qs.ENERGY_BRACELET="energy_bracelet",Qs.DISCIPLINE_CLOCK="discipline_clock",Qs),Js=((Ks=Js||{}).CORN="corn",Ks.WHEAT="wheat",Ks.SOYBEAN="soybean",Ks.COTTON="cotton",Ks.RAPESEED_OIL="rapeseed_oil",Ks.SOYBEAN_MEAL="soybean_meal",Ks.PALM_OIL="palm_oil",Ks.WHITE_SUGAR="white_sugar",Ks.APPLE="apple",Ks.LIVE_PIG="live_pig",Ks.RED_JUJUBE="red_jujube",Ks.SOYBEAN_OIL="soybean_oil",Ks.COPPER="copper",Ks.ALUMINUM="aluminum",Ks.LEAD="lead",Ks.ZINC="zinc",Ks.NICKEL="nickel",Ks.TIN="tin",Ks.GOLD="gold",Ks.SILVER="silver",Ks.REBAR="rebar",Ks.HOT_ROLLED_COIL="hot_rolled_coil",Ks.GLASS="glass",Ks.THERMAL_COAL="thermal_coal",Ks.COKE="coke",Ks.COKING_COAL="coking_coal",Ks.CRUDE_OIL="crude_oil",Ks.ASPHALT="asphalt",Ks.LPG="lpg",Ks);const Zs={common:{productionMultiplier:1,attributeBonus:3,synthesisSuccessBonus:0},excellent:{productionMultiplier:1.3,attributeBonus:6,synthesisSuccessBonus:0},rare:{productionMultiplier:1.7,attributeBonus:9,synthesisSuccessBonus:0},epic:{productionMultiplier:2.3,attributeBonus:12,synthesisSuccessBonus:0},legendary:{productionMultiplier:3.2,attributeBonus:15,synthesisSuccessBonus:0}},$s={baseSuccessRates:{common:.8,excellent:.6,rare:.4,epic:.2,legendary:.1},dailyFocusBonus:.05,maxFocusBonus:.2,focusTimeRequired:120},ea={[Js.CORN]:{name:"玉米",nameEn:"Corn",icon:"🌽",basePrice:2800,exchange:"DCE",contractCode:"C",baseProduction:500},[Js.WHEAT]:{name:"小麦",nameEn:"Wheat",icon:"🌾",basePrice:2600,exchange:"CZCE",contractCode:"WH",baseProduction:400},[Js.SOYBEAN]:{name:"大豆",nameEn:"Soybean",icon:"🫘",basePrice:4200,exchange:"DCE",contractCode:"A",baseProduction:150},[Js.COTTON]:{name:"棉花",nameEn:"Cotton",icon:"☁️",basePrice:15e3,exchange:"CZCE",contractCode:"CF",baseProduction:100},[Js.RAPESEED_OIL]:{name:"菜籽油",nameEn:"Rapeseed Oil",icon:"🛢️",basePrice:8500,exchange:"CZCE",contractCode:"OI",baseProduction:80},[Js.SOYBEAN_MEAL]:{name:"豆粕",nameEn:"Soybean Meal",icon:"🌰",basePrice:3200,exchange:"DCE",contractCode:"M",baseProduction:200},[Js.PALM_OIL]:{name:"棕榈油",nameEn:"Palm Oil",icon:"🥥",basePrice:7800,exchange:"DCE",contractCode:"P",baseProduction:120},[Js.WHITE_SUGAR]:{name:"白糖",nameEn:"White Sugar",icon:"🍯",basePrice:5800,exchange:"CZCE",contractCode:"SR",baseProduction:5e3},[Js.APPLE]:{name:"苹果",nameEn:"Apple",icon:"🍎",basePrice:8500,exchange:"CZCE",contractCode:"AP",baseProduction:2500},[Js.LIVE_PIG]:{name:"生猪",nameEn:"Live Pig",icon:"🐷",basePrice:16e3,exchange:"DCE",contractCode:"LH",baseProduction:110},[Js.RED_JUJUBE]:{name:"红枣",nameEn:"Red Jujube",icon:"🔴",basePrice:12e3,exchange:"CZCE",contractCode:"CJ",baseProduction:1e3},[Js.SOYBEAN_OIL]:{name:"豆油",nameEn:"Soybean Oil",icon:"🛢️",basePrice:7200,exchange:"DCE",contractCode:"Y",baseProduction:100}},ta={[Js.COPPER]:{name:"铜",nameEn:"Copper",icon:"🟤",basePrice:68e3,exchange:"SHFE",contractCode:"CU",baseProduction:10},[Js.ALUMINUM]:{name:"铝",nameEn:"Aluminum",icon:"⚪",basePrice:18e3,exchange:"SHFE",contractCode:"AL",baseProduction:15},[Js.LEAD]:{name:"铅",nameEn:"Lead",icon:"⚫",basePrice:15e3,exchange:"SHFE",contractCode:"PB",baseProduction:8},[Js.ZINC]:{name:"锌",nameEn:"Zinc",icon:"🔘",basePrice:25e3,exchange:"SHFE",contractCode:"ZN",baseProduction:12},[Js.NICKEL]:{name:"镍",nameEn:"Nickel",icon:"⚙️",basePrice:18e4,exchange:"SHFE",contractCode:"NI",baseProduction:5},[Js.TIN]:{name:"锡",nameEn:"Tin",icon:"🔩",basePrice:22e4,exchange:"SHFE",contractCode:"SN",baseProduction:3},[Js.GOLD]:{name:"黄金",nameEn:"Gold",icon:"🏅",basePrice:42e4,exchange:"SHFE",contractCode:"AU",baseProduction:1},[Js.SILVER]:{name:"白银",nameEn:"Silver",icon:"🥈",basePrice:5200,exchange:"SHFE",contractCode:"AG",baseProduction:20},[Js.REBAR]:{name:"螺纹钢",nameEn:"Rebar",icon:"🏗️",basePrice:4200,exchange:"SHFE",contractCode:"RB",baseProduction:500},[Js.HOT_ROLLED_COIL]:{name:"热轧卷板",nameEn:"Hot Rolled Coil",icon:"📏",basePrice:4100,exchange:"SHFE",contractCode:"HC",baseProduction:400},[Js.GLASS]:{name:"玻璃",nameEn:"Glass",icon:"🪟",basePrice:1800,exchange:"CZCE",contractCode:"FG",baseProduction:100},[Js.THERMAL_COAL]:{name:"动力煤",nameEn:"Thermal Coal",icon:"⚫",basePrice:850,exchange:"CZCE",contractCode:"ZC",baseProduction:2e3},[Js.COKE]:{name:"焦炭",nameEn:"Coke",icon:"⚫",basePrice:2800,exchange:"DCE",contractCode:"J",baseProduction:800},[Js.COKING_COAL]:{name:"焦煤",nameEn:"Coking Coal",icon:"⚫",basePrice:1900,exchange:"DCE",contractCode:"JM",baseProduction:1e3},[Js.CRUDE_OIL]:{name:"原油",nameEn:"Crude Oil",icon:"🛢️",basePrice:520,exchange:"INE",contractCode:"SC",baseProduction:50},[Js.ASPHALT]:{name:"沥青",nameEn:"Asphalt",icon:"🛤️",basePrice:3500,exchange:"SHFE",contractCode:"BU",baseProduction:200},[Js.LPG]:{name:"LPG",nameEn:"LPG",icon:"⛽",basePrice:4200,exchange:"DCE",contractCode:"PG",baseProduction:80}},sa={[Xs.FOCUS_GLASSES]:{name:"聚焦眼镜",nameEn:"Focus Glasses",icon:"👓",baseAttributes:{focusBonus:5,productionBonus:2,synthesisBonus:1,tradingBonus:0}},[Xs.FOCUS_HEADPHONES]:{name:"专注耳机",nameEn:"Focus Headphones",icon:"🎧",baseAttributes:{focusBonus:8,productionBonus:0,synthesisBonus:3,tradingBonus:1}},[Xs.ENERGY_BRACELET]:{name:"能量手环",nameEn:"Energy Bracelet",icon:"⌚",baseAttributes:{focusBonus:3,productionBonus:5,synthesisBonus:2,tradingBonus:2}},[Xs.DISCIPLINE_CLOCK]:{name:"自律时钟",nameEn:"Discipline Clock",icon:"⏰",baseAttributes:{focusBonus:6,productionBonus:3,synthesisBonus:4,tradingBonus:3}}},aa=[...function(){const e=[];return Object.entries(ea).forEach(([t,s])=>{Object.values(Ys).forEach(a=>{const i=Zs[a],n=s.baseProduction*i.productionMultiplier,r={id:"".concat(t,"_").concat(a),name:"".concat(s.name),nameEn:s.nameEn,description:"".concat(Zs[a]?"优质":"普通","的").concat(s.name,"，产量范围 ").concat(Math.floor(.8*n),"-").concat(Math.floor(1.2*n)),quality:a,category:Re.AGRICULTURAL,type:_e.CROP,icon:s.icon,baseValue:s.basePrice,currentValue:s.basePrice,stackable:!0,tradeable:!0,synthesizable:a!==Ys.LEGENDARY,productionRange:{min:Math.floor(.8*n),max:Math.floor(1.2*n)},futuresData:{exchange:s.exchange,contractCode:s.contractCode,basePrice:s.basePrice,volatility:.05,lastUpdateTime:Date.now()},metadata:{category:"agricultural",futuresProduct:t}};e.push(r)})}),e}(),...function(){const e=[];return Object.entries(ta).forEach(([t,s])=>{Object.values(Ys).forEach(a=>{const i=Zs[a],n=s.baseProduction*i.productionMultiplier,r={id:"".concat(t,"_").concat(a),name:"".concat(s.name),nameEn:s.nameEn,description:"".concat(Zs[a]?"优质":"普通","的").concat(s.name,"，产量范围 ").concat(Math.floor(.8*n),"-").concat(Math.floor(1.2*n)),quality:a,category:Re.INDUSTRIAL,type:_e.RAW_MATERIAL,icon:s.icon,baseValue:s.basePrice,currentValue:s.basePrice,stackable:!0,tradeable:!0,synthesizable:a!==Ys.LEGENDARY,productionRange:{min:Math.floor(.8*n),max:Math.floor(1.2*n)},futuresData:{exchange:s.exchange,contractCode:s.contractCode,basePrice:s.basePrice,volatility:.08,lastUpdateTime:Date.now()},metadata:{category:"industrial",futuresProduct:t}};e.push(r)})}),e}(),...function(){const e=[];return Object.entries(sa).forEach(([t,s])=>{Object.values(Ys).forEach(a=>{const i=1+Zs[a].attributeBonus/100,n={id:"".concat(t,"_").concat(a),name:"".concat(s.name),nameEn:s.nameEn,description:"".concat(Zs[a]?"优质":"普通","的").concat(s.name,"，提供各种属性加成"),quality:a,category:Re.INDUSTRIAL,type:t,icon:s.icon,baseValue:1e3*(Object.values(Ys).indexOf(a)+1),currentValue:1e3*(Object.values(Ys).indexOf(a)+1),stackable:!1,tradeable:!0,synthesizable:a!==Ys.LEGENDARY,equipmentAttributes:{focusBonus:Math.floor(s.baseAttributes.focusBonus*i),productionBonus:Math.floor(s.baseAttributes.productionBonus*i),synthesisBonus:Math.floor(s.baseAttributes.synthesisBonus*i),tradingBonus:Math.floor(s.baseAttributes.tradingBonus*i)},metadata:{category:"equipment",equipmentType:t}};e.push(n)})}),e}()];function ia(e){return aa.find(t=>t.id===e)}function na(e){return aa.filter(t=>{var s;return(null==(s=t.metadata)?void 0:s.futuresProduct)===e})}class ra{constructor(){t(this,"focusTimeTracker",{dailyFocusTime:0,lastUpdateTime:Date.now(),streak:0,totalFocusTime:0}),t(this,"synthesisHistory",[]),t(this,"currentSession",null)}canSynthesize(e,t){return e.id.split("_")[0]===t.id.split("_")[0]&&e.quality===t.quality&&!(!e.synthesizable||!t.synthesizable)&&e.quality!==Ys.LEGENDARY}getTargetQuality(e){const t=Object.values(Ys),s=t.indexOf(e);return s>=t.length-1?null:t[s+1]}calculateBaseSuccessRate(e){return $s.baseSuccessRates[e]||0}calculateFocusBonus(){(new Date).toDateString()!==new Date(this.focusTimeTracker.lastUpdateTime).toDateString()&&(this.focusTimeTracker.dailyFocusTime=0,this.focusTimeTracker.lastUpdateTime=Date.now());const e=this.focusTimeTracker.dailyFocusTime,t=$s.focusTimeRequired;if(e>=t){const s=Math.floor(e/t),a=$s.maxFocusBonus/$s.dailyFocusBonus;return Math.min(s,a)*$s.dailyFocusBonus}return 0}calculateEquipmentBonus(e){let t=0;return e.forEach(e=>{var s;(null==(s=e.equipmentAttributes)?void 0:s.synthesisBonus)&&(t+=e.equipmentAttributes.synthesisBonus/100)}),t}createSynthesisSession(e,t,s=[]){if(!this.canSynthesize(e,t))return null;const a=this.getTargetQuality(e.quality);if(!a)return null;const i=this.calculateBaseSuccessRate(e.quality),n=this.calculateFocusBonus(),r=this.calculateEquipmentBonus(s),o=Math.min(1,i+n+r),c={id:"synthesis_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9)),inputItems:[e,t],targetQuality:a,baseSuccessRate:i,focusBonus:n,equipmentBonus:r,finalSuccessRate:o,status:"ready"};return this.currentSession=c,c}executeSynthesis(e){const t=this.currentSession;if(!t||t.id!==e||"ready"!==t.status)return null;t.status="processing",t.startTime=Date.now();const s=Math.random()<t.finalSuccessRate,a=t.inputItems[0],i=a.id.split("_")[0],n="".concat(i,"_").concat(t.targetQuality);let r;s&&(r=ia(n),r&&(r={...r,id:"".concat(r.id,"_").concat(Date.now()),currentValue:this.calculateSynthesizedItemValue(r)}));const o={success:s,inputItems:t.inputItems,outputItem:r,successRate:t.finalSuccessRate,bonusApplied:t.focusBonus+t.equipmentBonus,message:s?"合成成功！获得了".concat(t.targetQuality===Ys.LEGENDARY?"传说":Zs[t.targetQuality]?"优质":"普通","品质的").concat(a.name,"！"):"合成失败，材料已消耗",timestamp:Date.now(),experience:this.calculateExperience(t,s)};return t.result=o,t.status=s?"completed":"failed",t.endTime=Date.now(),this.synthesisHistory.push(o),this.currentSession=null,o}calculateSynthesizedItemValue(e){const t=Object.values(Ys).indexOf(e.quality)+1;return Math.floor(e.baseValue*t*1.2)}calculateExperience(e,t){const s=10*Object.values(Ys).indexOf(e.targetQuality);return t?s:Math.floor(.3*s)}updateFocusTime(e){if((new Date).toDateString()!==new Date(this.focusTimeTracker.lastUpdateTime).toDateString()){const e=Math.floor((Date.now()-this.focusTimeTracker.lastUpdateTime)/864e5);1===e&&this.focusTimeTracker.dailyFocusTime>=$s.focusTimeRequired?this.focusTimeTracker.streak+=1:e>1&&(this.focusTimeTracker.streak=0),this.focusTimeTracker.dailyFocusTime=0}this.focusTimeTracker.dailyFocusTime+=e,this.focusTimeTracker.totalFocusTime+=e,this.focusTimeTracker.lastUpdateTime=Date.now()}getSynthesisStats(){const e=this.synthesisHistory.length,t=this.synthesisHistory.filter(e=>e.success).length;return{totalAttempts:e,successCount:t,successRate:e>0?t/e:0,qualityStats:Object.values(Ys).reduce((e,t)=>(e[t]={attempts:this.synthesisHistory.filter(e=>{var s;return(null==(s=e.inputItems[0])?void 0:s.quality)===t}).length,successes:this.synthesisHistory.filter(e=>{var s;return e.success&&(null==(s=e.inputItems[0])?void 0:s.quality)===t}).length},e),{}),focusTimeTracker:this.focusTimeTracker,recentResults:this.synthesisHistory.slice(-10)}}getRecommendedSynthesis(e){const t=[],s=new Map;return e.forEach(e=>{const t=e.id.split("_")[0];s.has(t)||s.set(t,[]),s.get(t).push(e)}),s.forEach(e=>{const s=new Map;e.forEach(e=>{s.has(e.quality)||s.set(e.quality,[]),s.get(e.quality).push(e)}),s.forEach((e,s)=>{if(e.length>=2&&s!==Ys.LEGENDARY){const a=e[0],i=e[1];if(this.canSynthesize(a,i)){const e=this.calculateBaseSuccessRate(s),n=this.calculateFocusBonus(),r=Math.min(1,e+n);t.push({item1:a,item2:i,successRate:r})}}})}),t.sort((e,t)=>t.successRate-e.successRate)}reset(){this.synthesisHistory=[],this.currentSession=null,this.focusTimeTracker={dailyFocusTime:0,lastUpdateTime:Date.now(),streak:0,totalFocusTime:0}}}class oa{constructor(){t(this,"priceData",new Map),t(this,"tradeOrders",[]),t(this,"priceHistory",new Map),t(this,"lastPriceUpdate",0),this.initializePrices()}initializePrices(){aa.forEach(e=>{const t={itemId:e.id,currentPrice:e.baseValue,basePrice:e.baseValue,priceHistory:[{timestamp:Date.now(),price:e.baseValue,volume:0}],dailyChange:0,weeklyTrend:0,volume:0,lastUpdateTime:Date.now()};this.priceData.set(e.id,t),this.priceHistory.set(e.id,[t.priceHistory[0]])})}async fetchRealFuturesPrice(e){return await new Promise(e=>setTimeout(e,100)),{C:{symbol:"C",price:2850,change:25,changePercent:.89,volume:158420,timestamp:Date.now()},WH:{symbol:"WH",price:2680,change:-15,changePercent:-.56,volume:89340,timestamp:Date.now()},A:{symbol:"A",price:4280,change:45,changePercent:1.06,volume:203570,timestamp:Date.now()},CF:{symbol:"CF",price:15250,change:-120,changePercent:-.78,volume:45230,timestamp:Date.now()},CU:{symbol:"CU",price:69500,change:850,changePercent:1.24,volume:125680,timestamp:Date.now()},AL:{symbol:"AL",price:18400,change:-200,changePercent:-1.08,volume:234560,timestamp:Date.now()},AU:{symbol:"AU",price:425e3,change:2500,changePercent:.59,volume:89670,timestamp:Date.now()}}[e]||null}async updateDailyPrices(){const e=Date.now();if(!(e-this.lastPriceUpdate<864e5)){for(const t of aa){const s=this.priceData.get(t.id);if(!s||!t.futuresData)continue;const a=await this.fetchRealFuturesPrice(t.futuresData.contractCode);let i=s.currentPrice,n=0;if(a){const e=a.changePercent/100,r=t.futuresData.basePrice*e*.8;i=Math.max(s.basePrice+r,.5*s.basePrice),n=.8*e}else{const e=t.futuresData.volatility,a=2*(Math.random()-.5)*.1,r=s.currentPrice*a*e;i=Math.max(s.currentPrice+r,.3*s.basePrice),n=a*e}s.currentPrice=Math.round(i),s.dailyChange=n,s.lastUpdateTime=e;const r={timestamp:e,price:s.currentPrice,volume:(null==a?void 0:a.volume)||Math.floor(1e5*Math.random())};s.priceHistory.push(r),s.priceHistory.length>30&&(s.priceHistory=s.priceHistory.slice(-30)),this.calculateWeeklyTrend(t.id)}this.lastPriceUpdate=e}}calculateWeeklyTrend(e){const t=this.priceData.get(e);if(!t)return;const s=Date.now()-6048e5,a=t.priceHistory.filter(e=>e.timestamp>=s);if(a.length>=2){const e=a[0].price,s=a[a.length-1].price;t.weeklyTrend=(s-e)/e}}updatePriceByTrading(e,t,s){const a=this.priceData.get(e);if(!a)return;const i=Math.min(t/1e3,.05),n=s?i:-i;a.currentPrice=Math.round(a.currentPrice*(1+n)),a.volume+=t,a.lastUpdateTime=Date.now()}createBuyOrder(e,t,s,a){const i=ia(t);if(!i||!i.tradeable)return null;const n={id:"order_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9)),itemId:t,playerId:e,type:"buy",quantity:s,pricePerUnit:a,totalPrice:s*a,status:"pending",createTime:Date.now()};return this.tradeOrders.push(n),n}createSellOrder(e,t,s,a){const i=ia(t);if(!i||!i.tradeable)return null;const n={id:"order_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9)),itemId:t,playerId:e,type:"sell",quantity:s,pricePerUnit:a,totalPrice:s*a,status:"pending",createTime:Date.now()};return this.tradeOrders.push(n),n}matchOrders(){const e=this.tradeOrders.filter(e=>"buy"===e.type&&"pending"===e.status),t=this.tradeOrders.filter(e=>"sell"===e.type&&"pending"===e.status);e.forEach(e=>{const s=t.filter(t=>t.itemId===e.itemId&&t.pricePerUnit<=e.pricePerUnit&&t.playerId!==e.playerId).sort((e,t)=>e.pricePerUnit-t.pricePerUnit);for(const t of s){if("completed"===e.status)break;const s=Math.min(e.quantity,t.quantity),a=t.pricePerUnit;e.quantity-=s,t.quantity-=s,0===e.quantity&&(e.status="completed",e.completeTime=Date.now()),0===t.quantity&&(t.status="completed",t.completeTime=Date.now()),this.updatePriceByTrading(e.itemId,s,!0);const i=this.priceData.get(e.itemId);i&&i.priceHistory.push({timestamp:Date.now(),price:a,volume:s})}}),this.cleanExpiredOrders()}cleanExpiredOrders(){const e=Date.now()-2592e5;this.tradeOrders=this.tradeOrders.filter(t=>!(t.createTime<e&&"pending"===t.status&&(t.status="cancelled",1)))}getItemPrice(e){return this.priceData.get(e)||null}getMarketOverview(){const e=this.priceData.size;return{totalItems:e,risingItems:Array.from(this.priceData.values()).filter(e=>e.dailyChange>0).length,fallingItems:Array.from(this.priceData.values()).filter(e=>e.dailyChange<0).length,totalVolume:Array.from(this.priceData.values()).reduce((e,t)=>e+t.volume,0),averageChange:Array.from(this.priceData.values()).reduce((e,t)=>e+t.dailyChange,0)/e,activeOrders:this.tradeOrders.filter(e=>"pending"===e.status).length,lastUpdateTime:this.lastPriceUpdate}}getHotItems(e=10){return Array.from(this.priceData.values()).sort((e,t)=>t.volume-e.volume).slice(0,e)}getTopGainers(e=10){return Array.from(this.priceData.values()).filter(e=>e.dailyChange>0).sort((e,t)=>t.dailyChange-e.dailyChange).slice(0,e)}getTopLosers(e=10){return Array.from(this.priceData.values()).filter(e=>e.dailyChange<0).sort((e,t)=>e.dailyChange-t.dailyChange).slice(0,e)}getPlayerOrders(e){return this.tradeOrders.filter(t=>t.playerId===e)}cancelOrder(e,t){const s=this.tradeOrders.find(s=>s.id===e&&s.playerId===t);return!(!s||"pending"!==s.status||(s.status="cancelled",0))}calculateTradingFee(e){return Math.round(.02*e)}calculateListingFee(e){return Math.round(.01*e)}}const ca=B()((pa=(e,t)=>({inventory:[],maxSlots:100,equippedItems:[],synthesisSystem:new ra,currentSynthesisSession:null,synthesisHistory:[],dailyFocusTime:0,marketSystem:new oa,playerOrders:[],marketPrices:new Map,totalItemsObtained:0,totalSynthesisAttempts:0,totalTradeVolume:0,addItem:(s,a=1)=>{const i=t();if(i.inventory.reduce((e,t)=>e+(t.stackable?1:t.quantity),0)+(s.stackable?1:a)>i.maxSlots)return!1;if(i.inventory.find(e=>e.id===s.id)&&s.stackable)e(e=>({inventory:e.inventory.map(e=>e.id===s.id?{...e,quantity:e.quantity+a}:e),totalItemsObtained:e.totalItemsObtained+a}));else{const t={...s,quantity:a,obtainedAt:Date.now(),isEquipped:!1};e(e=>({inventory:[...e.inventory,t],totalItemsObtained:e.totalItemsObtained+a}))}return!0},removeItem:(s,a=1)=>{const i=t().inventory.find(e=>e.id===s);return!(!i||i.quantity<a||(i.quantity===a?e(e=>({inventory:e.inventory.filter(e=>e.id!==s)})):e(e=>({inventory:e.inventory.map(e=>e.id===s?{...e,quantity:e.quantity-a}:e)})),0))},equipItem:s=>{const a=t(),i=a.inventory.find(e=>e.id===s);if(!i||!i.equipmentAttributes||i.isEquipped)return!1;const n=i.type,r=a.equippedItems.find(e=>e.type===n);return r&&t().unequipItem(r.id),e(e=>({inventory:e.inventory.map(e=>e.id===s?{...e,isEquipped:!0}:e),equippedItems:[...e.equippedItems.filter(e=>e.id!==s),{...i,isEquipped:!0}]})),!0},unequipItem:s=>!!t().equippedItems.find(e=>e.id===s)&&(e(e=>({inventory:e.inventory.map(e=>e.id===s?{...e,isEquipped:!1}:e),equippedItems:e.equippedItems.filter(e=>e.id!==s)})),!0),getItemQuantity:e=>{const s=t().inventory.find(t=>t.id===e);return s?s.quantity:0},createSynthesisSession:(s,a)=>{const i=t(),n=i.inventory.find(e=>e.id===s),r=i.inventory.find(e=>e.id===a);if(!n||!r||n.quantity<1||r.quantity<1)return!1;const o=i.synthesisSystem.createSynthesisSession(n,r,i.equippedItems);return!!o&&(e({currentSynthesisSession:o}),!0)},executeSynthesis:async()=>{const s=t();if(!s.currentSynthesisSession)return null;const a=s.synthesisSystem.executeSynthesis(s.currentSynthesisSession.id);return a?(a.inputItems.forEach(e=>{t().removeItem(e.id,1)}),a.success&&a.outputItem&&t().addItem(a.outputItem,1),e(e=>({currentSynthesisSession:null,synthesisHistory:[...e.synthesisHistory,a],totalSynthesisAttempts:e.totalSynthesisAttempts+1})),a):null},cancelSynthesis:()=>{e({currentSynthesisSession:null})},updateFocusTime:s=>{t().synthesisSystem.updateFocusTime(s),e(e=>({dailyFocusTime:e.dailyFocusTime+s}))},createBuyOrder:(s,a,i)=>{const n=t().marketSystem.createBuyOrder("player",s,a,i);return!!n&&(e(e=>({playerOrders:[...e.playerOrders,n]})),!0)},createSellOrder:(s,a,i)=>{const n=t(),r=n.inventory.find(e=>e.id===s);if(!r||r.quantity<a)return!1;const o=n.marketSystem.createSellOrder("player",s,a,i);return!!o&&(t().removeItem(s,a),e(e=>({playerOrders:[...e.playerOrders,o]})),!0)},cancelOrder:s=>{const a=t(),i=a.marketSystem.cancelOrder(s,"player");if(i){const i=a.playerOrders.find(e=>e.id===s);if(i&&"sell"===i.type){const e=ia(i.itemId);e&&t().addItem(e,i.quantity)}e(e=>({playerOrders:e.playerOrders.filter(e=>e.id!==s)}))}return i},updateMarketPrices:async()=>{const s=t();await s.marketSystem.updateDailyPrices(),s.marketSystem.matchOrders();const a=new Map;s.inventory.forEach(e=>{const t=s.marketSystem.getItemPrice(e.id);t&&a.set(e.id,t)}),e({marketPrices:a})},getInventoryByCategory:()=>{const e=t(),s={};return e.inventory.forEach(e=>{const t=e.category;s[t]||(s[t]=[]),s[t].push(e)}),s},getInventoryByQuality:()=>{const e=t(),s={};return Object.values(Ys).forEach(e=>{s[e]=[]}),e.inventory.forEach(e=>{s[e.quality].push(e)}),s},getSynthesisRecommendations:()=>{const e=t();return e.synthesisSystem.getRecommendedSynthesis(e.inventory)},getMarketOverview:()=>t().marketSystem.getMarketOverview(),reset:()=>{e({inventory:[],equippedItems:[],currentSynthesisSession:null,synthesisHistory:[],dailyFocusTime:0,playerOrders:[],marketPrices:new Map,totalItemsObtained:0,totalSynthesisAttempts:0,totalTradeVolume:0,synthesisSystem:new ra,marketSystem:new oa})}}),"getStorage"in(ga={name:"item-store",partialize:e=>({inventory:e.inventory,equippedItems:e.equippedItems,synthesisHistory:e.synthesisHistory,dailyFocusTime:e.dailyFocusTime,totalItemsObtained:e.totalItemsObtained,totalSynthesisAttempts:e.totalSynthesisAttempts,totalTradeVolume:e.totalTradeVolume})})||"serialize"in ga||"deserialize"in ga?((e,t)=>(s,a,i)=>{let n={getStorage:()=>localStorage,serialize:JSON.stringify,deserialize:JSON.parse,partialize:e=>e,version:0,merge:(e,t)=>({...t,...e}),...t},r=!1;const o=new Set,c=new Set;let l;try{l=n.getStorage()}catch(y){}if(!l)return e((...e)=>{s(...e)},a,i);const d=Hs(n.serialize),h=()=>{const e=n.partialize({...a()});let t;const s=d({state:e,version:n.version}).then(e=>l.setItem(n.name,e)).catch(e=>{t=e});if(t)throw t;return s},u=i.setState;i.setState=(e,t)=>{u(e,t),h()};const m=e((...e)=>{s(...e),h()},a,i);let p;const g=()=>{var e;if(!l)return;r=!1,o.forEach(e=>e(a()));const t=(null==(e=n.onRehydrateStorage)?void 0:e.call(n,a()))||void 0;return Hs(l.getItem.bind(l))(n.name).then(e=>{if(e)return n.deserialize(e)}).then(e=>{if(e){if("number"!=typeof e.version||e.version===n.version)return e.state;if(n.migrate)return n.migrate(e.state,e.version)}}).then(e=>{var t;return p=n.merge(e,null!=(t=a())?t:m),s(p,!0),h()}).then(()=>{null==t||t(p,void 0),r=!0,c.forEach(e=>e(p))}).catch(e=>{null==t||t(void 0,e)})};return i.persist={setOptions:e=>{n={...n,...e},e.getStorage&&(l=e.getStorage())},clearStorage:()=>{null==l||l.removeItem(n.name)},getOptions:()=>n,rehydrate:()=>g(),hasHydrated:()=>r,onHydrate:e=>(o.add(e),()=>{o.delete(e)}),onFinishHydration:e=>(c.add(e),()=>{c.delete(e)})},g(),p||m})(pa,ga):((e,t)=>(s,a,i)=>{let n={storage:zs(()=>localStorage),partialize:e=>e,version:0,merge:(e,t)=>({...t,...e}),...t},r=!1;const o=new Set,c=new Set;let l=n.storage;if(!l)return e((...e)=>{s(...e)},a,i);const d=()=>{const e=n.partialize({...a()});return l.setItem(n.name,{state:e,version:n.version})},h=i.setState;i.setState=(e,t)=>{h(e,t),d()};const u=e((...e)=>{s(...e),d()},a,i);let m;i.getInitialState=()=>u;const p=()=>{var e,t;if(!l)return;r=!1,o.forEach(e=>{var t;return e(null!=(t=a())?t:u)});const i=(null==(t=n.onRehydrateStorage)?void 0:t.call(n,null!=(e=a())?e:u))||void 0;return Hs(l.getItem.bind(l))(n.name).then(e=>{if(e){if("number"!=typeof e.version||e.version===n.version)return[!1,e.state];if(n.migrate)return[!0,n.migrate(e.state,e.version)]}return[!1,void 0]}).then(e=>{var t;const[i,r]=e;if(m=n.merge(r,null!=(t=a())?t:u),s(m,!0),i)return d()}).then(()=>{null==i||i(m,void 0),m=a(),r=!0,c.forEach(e=>e(m))}).catch(e=>{null==i||i(void 0,e)})};return i.persist={setOptions:e=>{n={...n,...e},e.storage&&(l=e.storage)},clearStorage:()=>{null==l||l.removeItem(n.name)},getOptions:()=>n,rehydrate:()=>p(),hasHydrated:()=>r,onHydrate:e=>(o.add(e),()=>{o.delete(e)}),onFinishHydration:e=>(c.add(e),()=>{c.delete(e)})},n.skipHydration||p(),m||u})(pa,ga))),la=({item:e,quantity:t=1,showPrice:s=!1,onSelect:a,isSelected:i=!1,showEquipButton:n=!1})=>{const{equipItem:r,unequipItem:o,inventory:c}=ca(),l=c.find(t=>t.id===e.id),d=(null==l?void 0:l.isEquipped)||!1;return W.jsxs("div",{className:"\n        relative p-4 rounded-lg border-2 cursor-pointer transition-all\n        ".concat(i?"ring-2 ring-blue-500":"","\n        hover:shadow-lg\n      "),style:{borderColor:Vs[e.quality]},onClick:()=>null==a?void 0:a(e),children:[W.jsx("div",{className:"absolute inset-0 rounded-lg opacity-10",style:{backgroundColor:Vs[e.quality]}}),W.jsxs("div",{className:"relative text-4xl mb-2 text-center",children:[e.icon,t>1&&W.jsx("span",{className:"absolute -top-1 -right-1 bg-blue-600 text-white text-xs px-1 rounded-full",children:t})]}),W.jsxs("h3",{className:"font-bold text-sm text-center mb-1",style:{color:Vs[e.quality]},children:[Ws[e.quality],e.name]}),W.jsx("p",{className:"text-xs text-gray-600 text-center mb-2 line-clamp-2",children:e.description}),e.equipmentAttributes&&W.jsxs("div",{className:"grid grid-cols-2 gap-1 text-xs mb-2",children:[W.jsxs("div",{className:"flex items-center",children:[W.jsx(O,{className:"w-3 h-3 mr-1"}),"专注+",e.equipmentAttributes.focusBonus,"%"]}),W.jsxs("div",{className:"flex items-center",children:[W.jsx(L,{className:"w-3 h-3 mr-1"}),"产量+",e.equipmentAttributes.productionBonus,"%"]})]}),s&&W.jsx("div",{className:"text-center",children:W.jsxs("span",{className:"text-sm font-bold text-green-600",children:["¥",e.currentValue.toLocaleString()]})}),n&&e.equipmentAttributes&&W.jsx("button",{onClick:t=>{t.stopPropagation(),d?o(e.id):r(e.id)},className:"\n            w-full mt-2 px-2 py-1 rounded text-xs font-bold\n            ".concat(d?"bg-red-500 text-white hover:bg-red-600":"bg-blue-500 text-white hover:bg-blue-600","\n          "),children:d?"卸下":"装备"})]})},da=()=>{const{inventory:e,currentSynthesisSession:t,createSynthesisSession:s,executeSynthesis:a,getSynthesisRecommendations:i}=ca(),[n,o]=r.useState(null),[c,l]=r.useState(null),[d,h]=r.useState(!1),u=i();return W.jsxs("div",{className:"bg-white rounded-lg shadow-lg p-6",children:[W.jsxs("h2",{className:"text-2xl font-bold mb-4 flex items-center",children:[W.jsx(D,{className:"w-6 h-6 mr-2"}),"道具合成"]}),W.jsxs("div",{className:"grid grid-cols-3 gap-4 items-center mb-6",children:[W.jsx("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-4 min-h-32 flex items-center justify-center",children:n?W.jsx(la,{item:n,onSelect:()=>o(null)}):W.jsxs("div",{className:"text-gray-500 text-center",children:[W.jsx(C,{className:"w-8 h-8 mx-auto mb-2"}),"选择第一个道具"]})}),W.jsxs("div",{className:"text-center",children:[W.jsx("button",{onClick:async()=>{if(n&&c&&s(n.id,c.id)){h(!0);try{await a()&&setTimeout(()=>{o(null),l(null),h(!1)},2e3)}catch(e){h(!1)}}},disabled:!n||!c||d,className:"\n              px-6 py-3 rounded-lg font-bold text-white transition-all\n              ".concat(n&&c&&!d?"bg-blue-500 hover:bg-blue-600 transform hover:scale-105":"bg-gray-400 cursor-not-allowed","\n            "),children:d?W.jsxs("div",{className:"flex items-center",children:[W.jsx(g,{className:"w-4 h-4 mr-2 animate-spin"}),"合成中..."]}):"开始合成"}),t&&W.jsxs("div",{className:"mt-2 text-sm",children:["成功率: ",(100*t.finalSuccessRate).toFixed(1),"%",W.jsxs("div",{className:"text-xs text-gray-600",children:["基础: ",(100*t.baseSuccessRate).toFixed(1),"%",t.focusBonus>0&&W.jsxs("span",{children:[" + 专注: ",(100*t.focusBonus).toFixed(1),"%"]}),t.equipmentBonus>0&&W.jsxs("span",{children:[" + 装备: ",(100*t.equipmentBonus).toFixed(1),"%"]})]})]})]}),W.jsx("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-4 min-h-32 flex items-center justify-center",children:c?W.jsx(la,{item:c,onSelect:()=>l(null)}):W.jsxs("div",{className:"text-gray-500 text-center",children:[W.jsx(C,{className:"w-8 h-8 mx-auto mb-2"}),"选择第二个道具"]})})]}),u.length>0&&W.jsxs("div",{className:"mt-6",children:[W.jsx("h3",{className:"text-lg font-bold mb-3",children:"推荐合成"}),W.jsx("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-2",children:u.slice(0,6).map((e,t)=>W.jsx("div",{className:"p-3 border rounded-lg cursor-pointer hover:bg-gray-50",onClick:()=>{o(e.item1),l(e.item2)},children:W.jsxs("div",{className:"flex items-center justify-between",children:[W.jsx("span",{className:"text-sm",children:e.item1.name}),W.jsxs("span",{className:"text-green-600 text-sm font-bold",children:[(100*e.successRate).toFixed(0),"%"]})]})},t))})]}),W.jsxs("div",{className:"mt-6",children:[W.jsx("h3",{className:"text-lg font-bold mb-3",children:"背包道具"}),W.jsx("div",{className:"grid grid-cols-4 md:grid-cols-6 lg:grid-cols-8 gap-2 max-h-64 overflow-y-auto",children:e.filter(e=>e.synthesizable&&e.quantity>0).map(e=>W.jsx(la,{item:e,quantity:e.quantity,onSelect:e=>{n?c?(o(e),l(null)):l(e):o(e)},isSelected:(null==n?void 0:n.id)===e.id||(null==c?void 0:c.id)===e.id},e.id))})]})]})},ha=()=>{const[e,t]=r.useState("inventory"),{inventory:s,equippedItems:a,getInventoryByCategory:i}=ca(),n=i();return W.jsxs("div",{className:"min-h-screen bg-gray-100 p-4",children:[W.jsx("div",{className:"flex justify-center mb-6",children:W.jsx("div",{className:"flex bg-white rounded-lg shadow",children:[{key:"inventory",label:"背包",icon:C},{key:"synthesis",label:"合成",icon:D},{key:"market",label:"市场",icon:M}].map(({key:s,label:a,icon:i})=>W.jsxs("button",{onClick:()=>t(s),className:"\n                flex items-center px-6 py-3 transition-colors\n                ".concat(e===s?"bg-blue-500 text-white":"text-gray-600 hover:text-gray-800 hover:bg-gray-50","\n                ").concat("inventory"===s?"rounded-l-lg":"","\n                ").concat("market"===s?"rounded-r-lg":"","\n              "),children:[W.jsx(i,{className:"w-5 h-5 mr-2"}),a]},s))})}),W.jsxs("div",{className:"bg-white rounded-lg shadow-lg p-4 mb-6",children:[W.jsxs("h3",{className:"text-lg font-bold mb-3 flex items-center",children:[W.jsx(N,{className:"w-5 h-5 mr-2"}),"已装备"]}),W.jsx("div",{className:"flex space-x-2",children:a.length>0?a.map(e=>W.jsx(la,{item:e,showEquipButton:!0},e.id)):W.jsx("div",{className:"text-gray-500 text-center py-4",children:"暂无装备"})})]}),"inventory"===e&&W.jsxs("div",{className:"bg-white rounded-lg shadow-lg p-6",children:[W.jsxs("h2",{className:"text-2xl font-bold mb-4 flex items-center",children:[W.jsx(C,{className:"w-6 h-6 mr-2"}),"道具背包"]}),Object.entries(n).map(([e,t])=>W.jsxs("div",{className:"mb-6",children:[W.jsx("h3",{className:"text-lg font-bold mb-3 capitalize",children:"agricultural"===e?"农业产品":"工业产品"}),W.jsx("div",{className:"grid grid-cols-4 md:grid-cols-6 lg:grid-cols-8 gap-3",children:t.map(e=>W.jsx(la,{item:e,quantity:e.quantity,showPrice:!0,showEquipButton:!!e.equipmentAttributes},e.id))})]},e)),0===s.length&&W.jsx("div",{className:"text-center text-gray-500 py-8",children:"背包为空"})]}),"synthesis"===e&&W.jsx(da,{})]})},ua=()=>{const{addItem:e,updateFocusTime:t,updateMarketPrices:s,inventory:a,totalItemsObtained:i}=ca();r.useEffect(()=>{n()},[]);const n=()=>{[...na(Js.CORN).filter(e=>e.quality===Ys.COMMON||e.quality===Ys.EXCELLENT),...na(Js.WHEAT).filter(e=>e.quality===Ys.COMMON||e.quality===Ys.EXCELLENT),...na(Js.SOYBEAN).filter(e=>e.quality===Ys.COMMON),...na(Js.COPPER).filter(e=>e.quality===Ys.COMMON),...na(Js.GOLD).filter(e=>e.quality===Ys.COMMON||e.quality===Ys.EXCELLENT),...aa.filter(e=>e.equipmentAttributes&&(e.quality===Ys.COMMON||e.quality===Ys.EXCELLENT)).slice(0,4)].forEach(t=>{const s=Math.floor(5*Math.random())+1;e(t,s)}),t(90),s()};return W.jsxs("div",{className:"min-h-screen bg-gray-50",children:[W.jsx("div",{className:"bg-white shadow-lg p-4 mb-4",children:W.jsxs("div",{className:"max-w-6xl mx-auto",children:[W.jsx("h1",{className:"text-3xl font-bold text-center mb-4",children:"🌾 中国期货道具系统演示 🏭"}),W.jsxs("div",{className:"flex justify-center space-x-4 mb-4",children:[W.jsxs("div",{className:"text-center",children:[W.jsx("div",{className:"text-2xl font-bold text-blue-600",children:a.length}),W.jsx("div",{className:"text-sm text-gray-600",children:"道具种类"})]}),W.jsxs("div",{className:"text-center",children:[W.jsx("div",{className:"text-2xl font-bold text-green-600",children:i}),W.jsx("div",{className:"text-sm text-gray-600",children:"总获得数量"})]}),W.jsxs("div",{className:"text-center",children:[W.jsx("div",{className:"text-2xl font-bold text-purple-600",children:aa.length}),W.jsx("div",{className:"text-sm text-gray-600",children:"可用道具"})]})]}),W.jsxs("div",{className:"flex justify-center space-x-3",children:[W.jsx("button",{onClick:()=>{aa.filter(e=>!a.some(t=>t.id===e.id)).sort(()=>Math.random()-.5).slice(0,5).forEach(t=>{e(t,Math.floor(3*Math.random())+1)})},className:"px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors",children:"🎁 添加随机道具"}),W.jsx("button",{onClick:()=>{t(30),alert("专注会话完成！获得30分钟专注时间加成")},className:"px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors",children:"🧘 模拟专注会话"}),W.jsx("button",{onClick:async()=>{await s(),alert("市场价格已更新！")},className:"px-4 py-2 bg-yellow-500 text-white rounded-lg hover:bg-yellow-600 transition-colors",children:"📈 刷新市场价格"})]})]})}),W.jsx("div",{className:"max-w-7xl mx-auto",children:W.jsx(ha,{})}),W.jsxs("div",{className:"max-w-6xl mx-auto mt-8 bg-white rounded-lg shadow-lg p-6",children:[W.jsx("h2",{className:"text-2xl font-bold mb-4",children:"💡 系统特色说明"}),W.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[W.jsxs("div",{className:"bg-blue-50 p-4 rounded-lg",children:[W.jsx("h3",{className:"font-bold text-lg mb-2 text-blue-800",children:"🌾 农业产品线"}),W.jsxs("ul",{className:"text-sm space-y-1",children:[W.jsx("li",{children:"• 玉米、小麦、大豆等12种期货品种"}),W.jsx("li",{children:"• 5个品质等级：普通→优质→稀有→史诗→传说"}),W.jsx("li",{children:"• 产量范围随品质递增：100-120 至 320-400"}),W.jsx("li",{children:"• 基于真实期货交易所数据"})]})]}),W.jsxs("div",{className:"bg-green-50 p-4 rounded-lg",children:[W.jsx("h3",{className:"font-bold text-lg mb-2 text-green-800",children:"🏭 工业产品线"}),W.jsxs("ul",{className:"text-sm space-y-1",children:[W.jsx("li",{children:"• 铜、铝、黄金等17种工业期货"}),W.jsx("li",{children:"• 涵盖有色金属、贵金属、能源化工"}),W.jsx("li",{children:"• 价格波动率更高，适合高风险交易"}),W.jsx("li",{children:"• 可合成高价值装备道具"})]})]}),W.jsxs("div",{className:"bg-purple-50 p-4 rounded-lg",children:[W.jsx("h3",{className:"font-bold text-lg mb-2 text-purple-800",children:"⚡ 装备系统"}),W.jsxs("ul",{className:"text-sm space-y-1",children:[W.jsx("li",{children:"• 聚焦眼镜、专注耳机、能量手环、自律时钟"}),W.jsx("li",{children:"• 提供专注、产量、合成、交易加成"}),W.jsx("li",{children:"• 属性随品质提升：+3% 至 +15%"}),W.jsx("li",{children:"• 支持装备/卸下，同类型装备互斥"})]})]}),W.jsxs("div",{className:"bg-orange-50 p-4 rounded-lg",children:[W.jsx("h3",{className:"font-bold text-lg mb-2 text-orange-800",children:"🔮 合成机制"}),W.jsxs("ul",{className:"text-sm space-y-1",children:[W.jsx("li",{children:"• 相同道具+相同品质才能合成"}),W.jsx("li",{children:"• 成功率：80%→60%→40%→20%"}),W.jsx("li",{children:"• 每日专注120分钟额外+5%成功率"}),W.jsx("li",{children:"• 装备加成最高+20%成功率"})]})]}),W.jsxs("div",{className:"bg-red-50 p-4 rounded-lg",children:[W.jsx("h3",{className:"font-bold text-lg mb-2 text-red-800",children:"📊 市场交易"}),W.jsxs("ul",{className:"text-sm space-y-1",children:[W.jsx("li",{children:"• 每日价格波动最多±10%"}),W.jsx("li",{children:"• 基于真实期货价格更新基准"}),W.jsx("li",{children:"• 玩家交易行为影响价格"}),W.jsx("li",{children:"• 支持买单/卖单匹配交易"})]})]}),W.jsxs("div",{className:"bg-indigo-50 p-4 rounded-lg",children:[W.jsx("h3",{className:"font-bold text-lg mb-2 text-indigo-800",children:"🎯 专注激励"}),W.jsxs("ul",{className:"text-sm space-y-1",children:[W.jsx("li",{children:"• 通过摄像头姿态检测专注状态"}),W.jsx("li",{children:"• 专注时间累积影响合成成功率"}),W.jsx("li",{children:"• 连续专注建立习惯养成"}),W.jsx("li",{children:"• 与农场养成玩法深度结合"})]})]})]}),W.jsxs("div",{className:"mt-6 p-4 bg-yellow-50 rounded-lg",children:[W.jsx("h3",{className:"font-bold text-lg mb-2 text-yellow-800",children:"🚀 技术实现亮点"}),W.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm",children:[W.jsxs("div",{children:[W.jsx("strong",{children:"前端技术栈："}),W.jsxs("ul",{className:"mt-1 space-y-1",children:[W.jsx("li",{children:"• React 18.2.0 + TypeScript 5.0.2"}),W.jsx("li",{children:"• Zustand 4.4.1 状态管理"}),W.jsx("li",{children:"• Tailwind CSS 3.3.3 样式"}),W.jsx("li",{children:"• Recharts 图表展示"}),W.jsx("li",{children:"• Lucide React 图标库"})]})]}),W.jsxs("div",{children:[W.jsx("strong",{children:"系统架构："}),W.jsxs("ul",{className:"mt-1 space-y-1",children:[W.jsx("li",{children:"• 模块化 TypeScript 架构"}),W.jsx("li",{children:"• 清晰的类型定义和接口"}),W.jsx("li",{children:"• 可扩展的道具数据结构"}),W.jsx("li",{children:"• 持久化状态存储"}),W.jsx("li",{children:"• 响应式组件设计"})]})]})]})]})]})]})},ma=()=>{const e=r.useRef(null),t=r.useRef(null),[s,a]=r.useState("idle"),[i,n]=r.useState(!1),[o,c]=r.useState(!1),[l,d]=r.useState(!1),[h,u]=r.useState(!1),[m,p]=r.useState(!1),[g,y]=r.useState(!1),[f,x]=r.useState(!1),[v,b]=r.useState(!1),[S,E]=r.useState(!1),[w,N]=r.useState(!1),[T,j]=r.useState(!1),[R]=r.useState(()=>{const e=new Je(200);return e.addItem({id:"wheat-common",name:"普通小麦",icon:"🌾",rarity:je.GRAY,category:Re.AGRICULTURAL,type:_e.CROP,description:"常见的农产品",value:10,stackable:!0,tradeable:!0,synthesizable:!0,metadata:{yieldMultiplier:1,futuresPrice:2850}},15),e.addItem({id:"stone-common",name:"普通石材",icon:"🪨",rarity:je.GRAY,category:Re.INDUSTRIAL,type:_e.RAW_MATERIAL,description:"常见的建筑材料",value:8,stackable:!0,tradeable:!0,synthesizable:!0,metadata:{efficiency:1,futuresPrice:2200}},20),e.addItem({id:"rice-green",name:"优质稻米",icon:"🍚",rarity:je.GREEN,category:Re.AGRICULTURAL,type:_e.CROP,description:"绿色有机稻米",value:25,stackable:!0,tradeable:!0,synthesizable:!0,metadata:{yieldMultiplier:1.2,futuresPrice:2950}},12),e.addItem({id:"copper-uncommon",name:"精制铜材",icon:"🟫",rarity:je.GREEN,category:Re.INDUSTRIAL,type:_e.RAW_MATERIAL,description:"品质不错的工业铜材",value:35,stackable:!0,tradeable:!0,synthesizable:!0,metadata:{efficiency:1.3,futuresPrice:3100}},9),e.addItem({id:"corn-rare",name:"稀有玉米",icon:"🌽",rarity:je.BLUE,category:Re.AGRICULTURAL,type:_e.CROP,description:"优质玉米种子",value:50,stackable:!0,tradeable:!0,synthesizable:!0,metadata:{yieldMultiplier:1.5,futuresPrice:2680}},8),e.addItem({id:"silver-rare",name:"稀有银材",icon:"⚪",rarity:je.BLUE,category:Re.INDUSTRIAL,type:_e.RAW_MATERIAL,description:"高纯度银材",value:75,stackable:!0,tradeable:!0,synthesizable:!0,metadata:{efficiency:1.6,futuresPrice:3500}},6),e.addItem({id:"steel-epic",name:"史诗钢材",icon:"⚙️",rarity:je.ORANGE,category:Re.INDUSTRIAL,type:_e.RAW_MATERIAL,description:"高品质工业材料",value:200,stackable:!0,tradeable:!0,synthesizable:!0,metadata:{efficiency:1.8,futuresPrice:3950}},3),e.addItem({id:"diamond-legendary",name:"传奇钻石",icon:"💎",rarity:je.GOLD,category:Re.INDUSTRIAL,type:_e.RAW_MATERIAL,description:"极其珍贵的宝石",value:1e3,stackable:!0,tradeable:!0,synthesizable:!0,metadata:{efficiency:3,futuresPrice:8500}},1),e.addItem({id:"oil-epic",name:"史诗原油",icon:"🛢️",rarity:je.ORANGE,category:Re.INDUSTRIAL,type:_e.RAW_MATERIAL,description:"高纯度工业原油",value:300,stackable:!0,tradeable:!0,synthesizable:!0,metadata:{efficiency:2.2,futuresPrice:4200}},5),e.addItem({id:"gold-legendary",name:"传奇黄金",icon:"🏆",rarity:je.GOLD,category:Re.INDUSTRIAL,type:_e.RAW_MATERIAL,description:"纯金材质，极其珍贵",value:800,stackable:!0,tradeable:!0,synthesizable:!0,metadata:{efficiency:2.8,futuresPrice:7500}},2),e.addItem({id:"mythril-mythic",name:"神话秘银",icon:"✨",rarity:je.GOLD_RED,category:Re.INDUSTRIAL,type:_e.RAW_MATERIAL,description:"传说中的神秘金属",value:2e3,stackable:!0,tradeable:!0,synthesizable:!0,metadata:{efficiency:4,futuresPrice:15e3}},1),e}),_=Ns();Ts();const I="undefined"!=typeof window&&("ontouchstart"in window||navigator.maxTouchPoints>0||navigator.msMaxTouchPoints>0),{state:k,updatePosture:A,startFocusSession:C,endFocusSession:D,getFocusTimeFormatted:M,getCurrentStreakFormatted:O,shouldTriggerGrowth:L,growPlant:F}=(()=>{const e=r.useContext(ts);if(void 0===e)throw new Error("useGame must be used within a GameProvider");return e})(),{state:P,startTutorial:G,updateProgress:B,finishTutorial:U}=ds();return r.useEffect(()=>{if(P.isFirstTimeUser&&!P.tutorialProgress.completed){const e=setTimeout(()=>{G(Cs)},1e3);return()=>clearTimeout(e)}},[P.isFirstTimeUser,P.tutorialProgress.completed,G]),r.useEffect(()=>{if(t.current&&!e.current){const s=()=>{const e=800;if("mobile"===_){const t=Math.min(window.innerWidth/e,.9);return{width:Math.floor(e*t),height:Math.floor(600*t)}}return"tablet"===_?{width:Math.min(e,.8*window.innerWidth),height:Math.min(600,.7*window.innerHeight)}:{width:e,height:600}},{width:a,height:i}=s(),n={type:X.AUTO,width:a,height:i,parent:t.current,backgroundColor:"#87CEEB",scene:[le],scale:{mode:X.Scale.FIT,autoCenter:X.Scale.CENTER_BOTH,width:a,height:i},physics:{default:"arcade",arcade:{gravity:{x:0,y:0},debug:!1}}};e.current=new X.Game(n),e.current.scene.scenes[0]&&e.current.scene.scenes[0].events.on("plantGrown",e=>{F(e.type)})}return()=>{e.current&&(e.current.destroy(!0),e.current=null)}},[]),r.useEffect(()=>{var t;(null==(t=e.current)?void 0:t.scene.scenes[0])&&(e.current.scene.scenes[0],L())},[k,L]),T?W.jsxs("div",{style:{position:"relative"},children:[W.jsx("button",{onClick:()=>j(!1),style:{position:"fixed",top:"20px",left:"20px",zIndex:1e4,padding:"10px 20px",backgroundColor:"#4CAF50",color:"white",border:"none",borderRadius:"6px",cursor:"pointer",fontSize:"1rem",fontWeight:"bold",boxShadow:"0 2px 8px rgba(0,0,0,0.2)"},children:"← 返回主页"}),W.jsx(ua,{})]}):w?W.jsxs("div",{style:{position:"relative"},children:[W.jsx("button",{onClick:()=>N(!1),style:{position:"fixed",top:"20px",left:"20px",zIndex:1e4,padding:"10px 20px",backgroundColor:"#4CAF50",color:"white",border:"none",borderRadius:"6px",cursor:"pointer",fontSize:"1rem",fontWeight:"bold",boxShadow:"0 2px 8px rgba(0,0,0,0.2)"},children:"← 返回主页"}),W.jsx(Us,{})]}):W.jsxs("div",{className:"app",children:[W.jsxs(js,{children:[W.jsxs("header",{className:"app-header",children:[W.jsx(Is,{mobile:W.jsxs("div",{children:[W.jsx("h1",{style:{fontSize:"1.8rem"},children:"🌱 自律农场"}),W.jsx("p",{style:{fontSize:"1rem"},children:"让自律变成快乐的游戏体验！"})]}),tablet:W.jsxs("div",{children:[W.jsx("h1",{style:{fontSize:"2.2rem"},children:"🌱 自律农场 - 习惯养成"}),W.jsx("p",{style:{fontSize:"1.1rem"},children:"通过摄像头监测，让自律变成快乐的游戏体验！"})]}),desktop:W.jsxs("div",{children:[W.jsx("h1",{children:"🌱 自律农场 - 习惯养成游戏"}),W.jsx("p",{children:"通过摄像头监测，让自律变成快乐的游戏体验！"})]}),children:W.jsxs("div",{children:[W.jsx("h1",{children:"🌱 自律农场 - 习惯养成游戏"}),W.jsx("p",{children:"通过摄像头监测，让自律变成快乐的游戏体验！"})]})}),W.jsxs(Rs,{className:"camera-status-bar",direction:"mobile"===_?"column":"row",align:"center",justify:"mobile"===_?"center":"between",gap:"mobile"===_?8:16,children:[W.jsxs(Rs,{align:"center",gap:8,children:[W.jsx("span",{className:"status-indicator status-".concat(s),children:"granted"===s?"🟢":"requesting"===s?"🟡":"denied"===s||"error"===s?"🔴":"⚪"}),W.jsxs("span",{style:{fontSize:"mobile"===_?"0.9rem":"1rem"},children:["摄像头状态: ","idle"===s?"未启动":"requesting"===s?"请求中...":"granted"===s?"已连接":"denied"===s?"权限被拒绝":"error"===s?"错误":"不可用"]})]}),W.jsxs(Rs,{gap:8,children:[W.jsx("button",{className:"toggle-camera-btn",onClick:()=>n(!i),style:{padding:I?"12px 16px":"8px 16px",fontSize:"mobile"===_?"0.9rem":"1rem"},children:i?"隐藏摄像头":"显示摄像头"}),W.jsx("button",{onClick:()=>N(!0),style:{padding:I?"12px 16px":"8px 16px",fontSize:"mobile"===_?"0.9rem":"1rem",backgroundColor:"#FF9800",color:"white",border:"none",borderRadius:"4px",cursor:"pointer",fontWeight:"bold"},children:"🎮 期货游戏系统"})]})]})]}),W.jsx("div",{className:"top-action-bar",children:W.jsxs("div",{className:"action-bar-container",children:[W.jsxs("div",{className:"action-bar-section",children:[W.jsx("h4",{children:"🎯 专注功能"}),W.jsxs("div",{className:"action-bar-buttons",children:[W.jsxs("button",{className:"top-action-btn focus-btn ".concat(k.focusSession.isActive?"active":""),onClick:k.focusSession.isActive?D:C,children:["💡 ",k.focusSession.isActive?"结束专注":"开始专注学习"]}),W.jsxs("button",{className:"top-action-btn focus-mode-btn ".concat(l?"active":""),onClick:()=>d(!l),children:["🎯 ",l?"关闭专注模式":"手机专注模式"]})]})]}),W.jsxs("div",{className:"action-bar-section",children:[W.jsx("h4",{children:"🛠️ 系统工具"}),W.jsxs("div",{className:"action-bar-buttons",children:[W.jsxs("button",{className:"top-action-btn monitor-btn ".concat(o?"active":""),onClick:()=>c(!o),children:["🖥️ ",o?"关闭应用监控":"打开应用监控"]}),W.jsxs("button",{className:"top-action-btn testing-btn ".concat(h?"active":""),onClick:()=>u(!h),children:["👥 ",h?"关闭用户测试":"用户测试中心"]}),W.jsxs("button",{className:"top-action-btn plan-btn ".concat(m?"active":""),onClick:()=>p(!m),children:["📋 ",m?"关闭测试计划":"测试计划管理"]}),W.jsxs("button",{className:"top-action-btn analysis-btn ".concat(g?"active":""),onClick:()=>y(!g),children:["📊 ",g?"关闭反馈分析":"反馈分析报告"]})]})]}),W.jsxs("div",{className:"action-bar-section",children:[W.jsx("h4",{children:"🎮 游戏系统"}),W.jsxs("div",{className:"action-bar-buttons",children:[W.jsxs("button",{className:"top-action-btn lootbox-btn ".concat(f?"active":""),onClick:()=>x(!f),children:["🎁 ",f?"关闭盲盒测试":"期货盲盒测试"]}),W.jsx("button",{className:"top-action-btn agricultural-btn",onClick:()=>(()=>{if(e.current&&(e.current.destroy(!0),e.current=null),t.current){const s={...ft,parent:t.current,scene:[$e]};e.current=new X.Game(s)}})(),children:"🌾 启动农产品系统"}),W.jsxs("button",{className:"top-action-btn inventory-btn ".concat(v?"active":""),onClick:()=>b(!v),children:["🎒 ",v?"关闭背包":"物品背包"]}),W.jsx("button",{className:"top-action-btn item-system-btn",onClick:()=>j(!0),style:{backgroundColor:"#9C27B0",color:"white"},children:"⚡ 道具系统演示"})]})]}),W.jsxs("div",{className:"action-bar-section",children:[W.jsx("h4",{children:"💪 生活管理"}),W.jsxs("div",{className:"action-bar-buttons",children:[W.jsx("button",{className:"top-action-btn exercise-btn",children:"💪 开始运动打卡"}),W.jsx("button",{className:"top-action-btn sleep-btn",children:"😴 设置作息时间"}),W.jsx("button",{className:"top-action-btn meditate-btn",children:"🧘 开始冥想练习"}),!P.tutorialProgress.completed&&W.jsx("button",{className:"top-action-btn tutorial-skip-btn",onClick:()=>{U(),B({completed:!0})},children:"⏭️ 跳过新手引导"})]})]})]})}),W.jsxs("main",{className:"app-main",children:[o&&W.jsxs("div",{className:"app-monitor-overlay",children:[W.jsxs("div",{className:"monitor-header",children:[W.jsx("h2",{children:"🖥️ 应用监控系统"}),W.jsx("button",{className:"close-monitor-btn",onClick:()=>c(!1),children:"✕ 关闭"})]}),W.jsx(Lt,{})]}),l&&W.jsxs("div",{className:"focus-mode-overlay",children:[W.jsxs("div",{className:"focus-mode-header",children:[W.jsx("h2",{children:"🎯 手机专注模式"}),W.jsx("button",{className:"close-focus-btn",onClick:()=>d(!1),children:"✕ 关闭"})]}),W.jsx(Ft,{})]}),h&&W.jsxs("div",{className:"user-testing-overlay",children:[W.jsxs("div",{className:"testing-header",children:[W.jsx("h2",{children:"👥 用户测试中心"}),W.jsx("button",{className:"close-testing-btn",onClick:()=>u(!1),children:"✕ 关闭"})]}),W.jsx(Pt,{})]}),m&&W.jsxs("div",{className:"test-plan-overlay",children:[W.jsxs("div",{className:"plan-header",children:[W.jsx("h2",{children:"📋 测试计划管理"}),W.jsx("button",{className:"close-plan-btn",onClick:()=>p(!1),children:"✕ 关闭"})]}),W.jsx(Gt,{})]}),g&&W.jsxs("div",{className:"feedback-analysis-overlay",children:[W.jsxs("div",{className:"analysis-header",children:[W.jsx("h2",{children:"📊 反馈分析报告"}),W.jsx("button",{className:"close-analysis-btn",onClick:()=>y(!1),children:"✕ 关闭"})]}),W.jsx(Bt,{})]}),f&&W.jsxs("div",{className:"lootbox-tester-overlay",children:[W.jsxs("div",{className:"tester-header",children:[W.jsx("h2",{children:"🎁 期货农产品盲盒测试"}),W.jsx("button",{className:"close-tester-btn",onClick:()=>x(!1),children:"✕ 关闭"})]}),W.jsx(Zt,{})]}),W.jsxs(Rs,{direction:"row",gap:"mobile"===_?16:"tablet"===_?24:32,align:"start",children:[W.jsxs(As,{collapsible:!0,defaultCollapsed:"mobile"===_,className:"control-panel left-panel",children:[W.jsx("div",{className:"panel-header",children:W.jsx("h2",{style:{margin:0,fontSize:"1.3rem",color:"#2C5530",textAlign:"center",padding:"10px 0",borderBottom:"2px solid #e0e0e0",background:"linear-gradient(135deg, #f8f9fa, #e9ecef)"},children:"📊 状态监控面板"})}),W.jsxs("div",{className:"panel-section",children:[W.jsx("h3",{children:"🎯 专注状态"}),W.jsxs("div",{className:"focus-indicator",children:[W.jsxs("div",{className:"focus-status ".concat(k.isPostureGood?"good":"poor"),children:[W.jsxs("div",{className:"focus-score",children:["专注度: ",Math.round(k.currentFocusScore),"%"]}),W.jsxs("div",{className:"focus-streak",children:["连续专注: ",O()]}),W.jsxs("div",{className:"focus-average",children:["平均分数: ",Math.round(k.averageFocusScore),"%"]})]}),k.focusSession.isActive&&W.jsxs("div",{className:"session-info",children:[W.jsx("span",{className:"session-indicator",children:"🔥"}),W.jsx("span",{children:"专注会话进行中"})]})]})]}),W.jsxs("div",{className:"panel-section",children:[W.jsx("h3",{children:"📊 农场统计"}),W.jsxs("div",{className:"stats-grid",children:[W.jsxs("div",{className:"stat-item",children:[W.jsx("span",{className:"stat-label",children:"知识花"}),W.jsx("span",{className:"stat-value",children:k.farmStats.knowledgeFlowers})]}),W.jsxs("div",{className:"stat-item",children:[W.jsx("span",{className:"stat-label",children:"力量树"}),W.jsx("span",{className:"stat-value",children:k.farmStats.strengthTrees})]}),W.jsxs("div",{className:"stat-item",children:[W.jsx("span",{className:"stat-label",children:"时间菜"}),W.jsx("span",{className:"stat-value",children:k.farmStats.timeVeggies})]}),W.jsxs("div",{className:"stat-item",children:[W.jsx("span",{className:"stat-label",children:"冥想莲"}),W.jsx("span",{className:"stat-value",children:k.farmStats.meditationLotus})]})]}),W.jsx("div",{className:"growth-points",children:W.jsxs("span",{children:["成长积分: ",k.farmStats.totalGrowthPoints]})})]}),W.jsxs("div",{className:"panel-section",children:[W.jsx("h3",{children:"📈 今日数据"}),W.jsxs("div",{className:"daily-stats",children:[W.jsxs("div",{className:"stat-row",children:[W.jsx("span",{children:"专注时长:"}),W.jsx("span",{children:M()})]}),W.jsxs("div",{className:"stat-row",children:[W.jsx("span",{children:"良好姿态:"}),W.jsxs("span",{children:[Math.round(k.dailyStats.goodPosturePercentage),"%"]})]}),W.jsxs("div",{className:"stat-row",children:[W.jsx("span",{children:"植物生长:"}),W.jsxs("span",{children:[k.dailyStats.plantsGrown,"株"]})]})]})]}),W.jsxs("div",{className:"panel-section",children:[W.jsx("h3",{children:"🎁 今日奖励"}),W.jsxs("div",{className:"reward-container",children:[W.jsxs("div",{className:"reward-item ".concat(k.currentStreak>=1800?"completed":"pending"),children:[W.jsx("span",{children:"🏆"}),W.jsx("span",{children:"连续专注30分钟"})]}),W.jsxs("div",{className:"reward-item ".concat(k.dailyStats.plantsGrown>=5?"completed":"pending"),children:[W.jsx("span",{children:"⭐"}),W.jsx("span",{children:"培养5株植物"})]}),W.jsxs("div",{className:"reward-item ".concat(k.averageFocusScore>=80?"completed":"pending"),children:[W.jsx("span",{children:"💎"}),W.jsx("span",{children:"平均专注度80%+"})]})]})]})]}),W.jsxs("div",{style:{flex:1,display:"flex",flexDirection:"column",gap:"20px"},children:[W.jsx(_s,{on:i?["mobile","tablet","desktop"]:[],children:W.jsxs("section",{className:"camera-section",children:[W.jsx("h2",{style:{fontSize:"mobile"===_?"1.2rem":"1.5rem"},children:"📹 行为监测"}),W.jsx(Ct,{width:"mobile"===_?280:320,height:"mobile"===_?210:240,onStatusChange:e=>{a(e),"granted"!==e||k.focusSession.isActive?"granted"!==e&&k.focusSession.isActive&&D():C()},onPoseDetected:(e,t)=>{A(t)},showControls:!0,className:"main-camera"})]})}),W.jsxs("section",{className:"game-section",children:[W.jsx("h2",{style:{fontSize:"mobile"===_?"1.2rem":"1.5rem"},children:"🎮 农场管理"}),W.jsx(ks,{baseWidth:800,baseHeight:600,maintainAspectRatio:!0,children:W.jsx("div",{ref:t,className:"phaser-container"})})]})]})]})]}),W.jsxs("footer",{className:"app-footer",children:[W.jsx("p",{children:"🌟 坚持每一天，收获更好的自己！"}),W.jsxs("div",{className:"debug-info",children:[W.jsxs("small",{children:["Debug: 专注 ",k.isPostureGood?"✓":"✗"," | 分数 ",Math.round(k.currentFocusScore)," | 连续 ",Math.round(k.currentStreak),"s | 应该生长 ",L()?"✓":"✗"]}),W.jsxs("div",{style:{marginTop:"8px",display:"flex",gap:"8px",flexWrap:"wrap"},children:[W.jsx("button",{onClick:()=>G(Cs),style:{padding:"4px 8px",fontSize:"11px",background:"#007bff",color:"white",border:"none",borderRadius:"4px",cursor:"pointer"},children:"开始引导"}),W.jsx("button",{onClick:()=>U(),style:{padding:"4px 8px",fontSize:"11px",background:"#dc3545",color:"white",border:"none",borderRadius:"4px",cursor:"pointer"},children:"结束引导"}),W.jsx("button",{onClick:()=>B({completed:!1}),style:{padding:"4px 8px",fontSize:"11px",background:"#ffc107",color:"black",border:"none",borderRadius:"4px",cursor:"pointer"},children:"重置进度"}),W.jsx("button",{onClick:()=>{U(),B({completed:!0})},style:{padding:"4px 8px",fontSize:"11px",background:"#28a745",color:"white",border:"none",borderRadius:"4px",cursor:"pointer"},children:"跳过教程"})]})]})]})]}),v&&W.jsx(Xt,{inventorySystem:R,onClose:()=>b(!1)}),W.jsx(vs,{})]})};var pa,ga;Q.createRoot(document.getElementById("root")).render(W.jsx(A.StrictMode,{children:W.jsx(()=>W.jsx(ls,{children:W.jsx(ss,{children:W.jsx(Ut,{children:W.jsx(ma,{})})})}),{})}));export{s as __vite_legacy_guard};
