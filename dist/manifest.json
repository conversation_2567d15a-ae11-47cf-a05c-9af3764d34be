{"name": "自律农场 - 智能习惯养成游戏", "short_name": "自律农场", "description": "一款通过摄像头监测行为帮助用户建立自律习惯的农场经营游戏", "version": "0.1.0", "manifest_version": 2, "icons": [{"src": "/icons/icon-72x72.png", "sizes": "72x72", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-96x96.png", "sizes": "96x96", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-128x128.png", "sizes": "128x128", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-144x144.png", "sizes": "144x144", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-152x152.png", "sizes": "152x152", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-192x192.png", "sizes": "192x192", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-384x384.png", "sizes": "384x384", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-512x512.png", "sizes": "512x512", "type": "image/png", "purpose": "maskable any"}], "start_url": "/", "scope": "/", "display": "standalone", "orientation": "portrait-primary", "theme_color": "#4ade80", "background_color": "#ffffff", "lang": "zh-CN", "dir": "ltr", "categories": ["games", "productivity", "lifestyle", "education"], "shortcuts": [{"name": "开始农场", "short_name": "农场", "description": "进入农场游戏", "url": "/?shortcut=farm", "icons": [{"src": "/icons/shortcut-farm.png", "sizes": "96x96"}]}, {"name": "专注模式", "short_name": "专注", "description": "启动专注模式", "url": "/?shortcut=focus", "icons": [{"src": "/icons/shortcut-focus.png", "sizes": "96x96"}]}, {"name": "应用监控", "short_name": "监控", "description": "查看应用使用情况", "url": "/?shortcut=monitor", "icons": [{"src": "/icons/shortcut-monitor.png", "sizes": "96x96"}]}], "screenshots": [{"src": "/screenshots/desktop-1.png", "sizes": "1280x720", "type": "image/png", "form_factor": "wide", "label": "自律农场主界面"}, {"src": "/screenshots/mobile-1.png", "sizes": "390x844", "type": "image/png", "form_factor": "narrow", "label": "移动端农场界面"}], "related_applications": [], "prefer_related_applications": false, "permissions": ["camera", "microphone", "notifications"], "features": ["CrossOriginIsolated"]}