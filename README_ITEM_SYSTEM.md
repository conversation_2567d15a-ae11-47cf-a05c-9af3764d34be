# 期货游戏道具与合成系统

这是一个完整的基于中国期货品种的游戏道具和合成系统，包含农业产品、工业产品和装备类道具，以及专注时间加成的合成机制。

## 系统特性

### 🎯 核心功能
- **完整道具系统**：农业产品、工业产品、装备类道具三大类别
- **品质系统**：普通、优质、稀有、史诗、传说五个品质等级
- **合成机制**：两个相同道具合成更高品质，成功率基于品质和专注时间
- **装备系统**：聚焦眼镜、专注耳机、能量手环、自律时钟四种装备
- **专注时间加成**：每日专注120分钟可获得最高+20%合成成功率
- **状态持久化**：使用Zustand持久化存储

### 📊 道具分类

#### 农业产品（基于中国期货品种）
- **谷物类**：玉米(C)、小麦(WH)、大豆(A)
- **油脂类**：豆油(Y)、棕榈油(P)、菜籽油(OI)、豆粕(M)
- **软商品**：棉花(CF)、白糖(SR)、苹果(AP)、红枣(CJ)
- **畜牧产品**：生猪(LH)

#### 工业产品（基于中国期货品种）
- **有色金属**：铜(CU)、铝(AL)、铅(PB)、锌(ZN)、镍(NI)、锡(SN)
- **贵金属**：黄金(AU)、白银(AG)
- **黑色金属**：螺纹钢(RB)、热轧卷板(HC)
- **建材**：玻璃(FG)
- **能源化工**：动力煤(ZC)、焦炭(J)、焦煤(JM)、原油(SC)、沥青(BU)、LPG(PG)

#### 装备类道具
- **聚焦眼镜** (头部装备)：提升专注力
- **专注耳机** (头部装备)：降噪增强专注
- **能量手环** (手腕装备)：监测活力状态
- **自律时钟** (桌面装备)：时间管理助手

### 🎨 品质系统

| 品质 | 中文名 | 产量范围 | 装备属性加成 | 合成成功率 | 颜色 |
|------|--------|----------|--------------|------------|------|
| common | 普通 | 100-120 | +3% | 80% | 灰色 |
| good | 优质 | 130-160 | +6% | 60% | 绿色 |
| rare | 稀有 | 170-220 | +9% | 40% | 蓝色 |
| epic | 史诗 | 230-300 | +12% | 20% | 紫色 |
| legendary | 传说 | 320-400 | +15% | 10% | 橙色 |

### ⚗️ 合成机制

#### 基本规则
- 需要两个**相同品种**且**相同品质**的道具
- 合成成功获得下一品质等级的道具
- 合成失败消耗材料但不获得道具
- 传说品质无法进一步合成

#### 成功率计算
```
最终成功率 = 基础成功率 + 专注时间加成 + 连续专注加成
```

#### 专注时间加成
- 30-59分钟：+5%
- 60-89分钟：+10%
- 90-119分钟：+15%
- 120+分钟：+20%

#### 连续专注加成
- 3-6天：+3%
- 7-13天：+5%
- 14-29天：+7%
- 30+天：+10%

## 技术架构

### 📁 文件结构
```
src/
├── types/
│   └── enhanced-items.ts          # 道具类型定义
├── systems/
│   └── SynthesisSystem.ts         # 合成系统逻辑
├── stores/
│   └── gameStore.ts               # Zustand状态管理
├── components/
│   ├── ItemCard.tsx               # 道具卡片组件
│   ├── SynthesisPanel.tsx         # 合成界面组件
│   └── GameDemo.tsx               # 完整演示组件
└── utils/
    └── ItemFactory.ts             # 道具工厂函数
```

### 🔧 技术栈
- **TypeScript**：类型安全的开发体验
- **React**：组件化UI开发
- **Zustand**：轻量级状态管理
- **Tailwind CSS**：实用优先的样式框架

## 使用方法

### 1. 基础设置

```typescript
import { GameDemo } from './components/GameDemo'

function App() {
  return <GameDemo />
}
```

### 2. 创建道具

```typescript
import { ItemFactory, AgriculturalVariety, Quality } from './utils/ItemFactory'

// 创建单个道具
const corn = ItemFactory.createItem(AgriculturalVariety.CORN, Quality.RARE)

// 批量创建
const corns = ItemFactory.createItems(AgriculturalVariety.CORN, Quality.COMMON, 5)

// 随机创建
const randomItem = ItemFactory.createRandomItem()

// 创建演示数据
const demoItems = ItemFactory.createDemoInventory()
```

### 3. 使用状态管理

```typescript
import { useGameStore } from './stores/gameStore'

function InventoryComponent() {
  const { 
    inventory, 
    addItem, 
    removeItem, 
    getFilteredItems,
    openSynthesis,
    performSynthesis 
  } = useGameStore()

  const handleAddItem = () => {
    const newItem = ItemFactory.createRandomItem()
    addItem(newItem)
  }

  return (
    <div>
      <button onClick={handleAddItem}>添加道具</button>
      <button onClick={openSynthesis}>打开合成</button>
      
      <div className="grid grid-cols-4 gap-4">
        {getFilteredItems().map(item => (
          <ItemCard key={item.id} item={item} />
        ))}
      </div>
    </div>
  )
}
```

### 4. 合成系统使用

```typescript
import { SynthesisSystem } from './systems/SynthesisSystem'

const synthesisSystem = new SynthesisSystem()

// 更新专注时间
synthesisSystem.updateFocusTime(60) // 添加60分钟专注时间

// 执行合成
const materials = [item1, item2] // 两个相同品种和品质的道具
const result = synthesisSystem.synthesize(materials)

console.log(result.success) // 合成是否成功
console.log(result.successRate) // 成功率
console.log(result.appliedBonuses) // 应用的加成
```

### 5. 自定义道具卡片

```typescript
import { ItemCard } from './components/ItemCard'

<ItemCard
  item={item}
  isSelected={false}
  showDetails={true}
  onClick={(item) => console.log('点击道具:', item.name)}
  onDoubleClick={(item) => console.log('双击道具:', item.name)}
/>
```

## API 参考

### 类型定义

#### GameItem
```typescript
type GameItem = AgriculturalItem | IndustrialItem | EquipmentItem

interface BaseItem {
  id: string
  name: string
  description: string
  category: ItemCategory
  quality: Quality
  icon: string
  baseValue: number
  stackable: boolean
  tradeable: boolean
  obtainedAt?: number
}
```

#### 合成结果
```typescript
interface SynthesisResult {
  success: boolean
  resultItem?: GameItem
  consumedItems: GameItem[]
  successRate: number
  appliedBonuses: string[]
  message: string
  timestamp: number
}
```

### 主要方法

#### ItemFactory
- `createItem(variety, quality)` - 创建道具
- `createItems(variety, quality, count)` - 批量创建
- `createRandomItem(category?)` - 随机创建
- `createDemoInventory()` - 创建演示数据

#### SynthesisSystem
- `synthesize(materials, recipe?)` - 执行合成
- `updateFocusTime(minutes)` - 更新专注时间
- `getFocusTimeState()` - 获取专注状态

#### useGameStore
- `addItem(item)` - 添加道具
- `removeItem(itemId)` - 移除道具
- `openSynthesis()` / `closeSynthesis()` - 控制合成界面
- `performSynthesis()` - 执行合成
- `getFilteredItems()` - 获取筛选后的道具

## 演示功能

运行演示组件可以体验：

1. **背包系统**
   - 道具展示和筛选
   - 分类和品质过滤
   - 随机道具生成

2. **合成系统**
   - 拖拽选择材料
   - 成功率实时计算
   - 合成动画效果
   - 专注时间模拟

3. **装备系统**
   - 装备槽位展示
   - 属性加成计算
   - 装备状态管理

4. **统计系统**
   - 游戏数据统计
   - 成功率分析
   - 专注时间追踪

## 扩展建议

### 功能扩展
1. **盲盒系统**：添加随机开箱功能
2. **交易系统**：玩家间道具交易
3. **成就系统**：合成和收集成就
4. **拍卖行**：道具拍卖机制

### 技术优化
1. **动画优化**：使用Framer Motion增强动效
2. **性能优化**：虚拟滚动处理大量道具
3. **数据持久化**：IndexedDB存储大数据
4. **网络同步**：多端数据同步

## 注意事项

1. **TypeScript错误**：当前gameStore.ts中有一些类型错误需要修复
2. **性能考虑**：大量道具时建议使用虚拟滚动
3. **数据验证**：生产环境需要加强数据校验
4. **错误处理**：需要完善错误边界和异常处理

## 许可证

此项目仅用于演示和学习目的。 