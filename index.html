<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/farm-icon.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="自律农场 - 通过摄像头监测行为帮助用户建立自律习惯的农场经营游戏" />
    <title>🌱 自律农场 - Self Discipline Farm</title>
    <style>
      .quick-access {
        position: fixed;
        top: 10px;
        right: 10px;
        z-index: 9999;
        padding: 8px 12px;
        background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
        color: white;
        text-decoration: none;
        border-radius: 8px;
        font-weight: bold;
        box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);
        transition: all 0.3s ease;
        font-size: 14px;
      }
      .quick-access:hover {
        background: linear-gradient(135deg, #ff5252, #ff7b7b);
        transform: translateY(-2px);
        box-shadow: 0 6px 16px rgba(255, 107, 107, 0.4);
      }
    </style>
  </head>
  <body>
    <a href="/test-lootbox.html" class="quick-access">
      🎁 期货盲盒演示
    </a>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html> 