import { envDevelopment } from './env.development';
import { envProduction } from './env.production';
import { envTesting } from './env.testing';

// 环境类型
export type Environment = 'development' | 'production' | 'test';

// 通用环境配置类型
export type EnvironmentConfig = {
  NODE_ENV: string;
  APP_TITLE: string;
  APP_VERSION: string;
  API_URL: string;
  BUILD_TYPE: string;
  DEBUG: boolean;
  ENABLE_ANALYTICS: boolean;
  ENABLE_SENTRY: boolean;
  ENABLE_PWA: boolean;
  HOT_RELOAD: boolean;
  SOURCE_MAP: boolean;
  MOCK_DATA: boolean;
  LOG_LEVEL: string;
  [key: string]: any;
};

// 环境配置映射
const environments: Record<Environment, EnvironmentConfig> = {
  development: envDevelopment,
  production: envProduction,
  test: envTesting
};

// 获取当前环境
export function getCurrentEnvironment(): Environment {
  const env = (import.meta as any).env?.MODE || process.env.NODE_ENV || 'development';
  return (env === 'production' || env === 'test') ? env : 'development';
}

// 获取当前环境配置
export function getConfig(): EnvironmentConfig {
  const currentEnv = getCurrentEnvironment();
  return environments[currentEnv];
}

// 检查是否为生产环境
export function isProduction(): boolean {
  return getCurrentEnvironment() === 'production';
}

// 检查是否为开发环境
export function isDevelopment(): boolean {
  return getCurrentEnvironment() === 'development';
}

// 检查是否为测试环境
export function isTesting(): boolean {
  return getCurrentEnvironment() === 'test';
}

// 获取API基础URL
export function getApiUrl(): string {
  return getConfig().API_URL;
}

// 获取应用标题
export function getAppTitle(): string {
  return getConfig().APP_TITLE;
}

// 获取应用版本
export function getAppVersion(): string {
  return getConfig().APP_VERSION;
}

// 检查是否启用调试模式
export function isDebugEnabled(): boolean {
  return getConfig().DEBUG;
}

// 检查是否启用PWA
export function isPWAEnabled(): boolean {
  return getConfig().ENABLE_PWA;
}

// 导出当前配置（默认导出）
export default getConfig(); 