// CDN配置管理系统
export interface CDNConfig {
  enabled: boolean
  provider: CDNProvider
  domain?: string
  regions?: string[]
  customDomain?: string
  cacheTTL?: number
  fallbackEnabled?: boolean
  monitoring?: {
    enabled: boolean
    endpoint?: string
  }
}

export enum CDNProvider {
  JSDELIVR = 'jsdelivr',
  CLOUDFLARE = 'cloudflare', 
  AWS_CLOUDFRONT = 'aws-cloudfront',
  AZURE_CDN = 'azure-cdn',
  GOOGLE_CLOUD_CDN = 'google-cloud-cdn',
  CUSTOM = 'custom'
}

export interface CDNProviderConfig {
  provider: CDNProvider
  name: string
  baseUrl: string
  features: CDNFeature[]
  pricing: 'free' | 'paid' | 'enterprise'
  regions: string[]
  maxFileSize?: string
  cacheTTL: {
    min: number
    max: number
    default: number
  }
  supportedFileTypes: string[]
  setup: {
    requiresAccount: boolean
    apiKey?: boolean
    customDomain?: boolean
    ssl?: boolean
  }
}

export enum CDNFeature {
  IMAGE_OPTIMIZATION = 'image-optimization',
  COMPRESSION = 'compression',
  CUSTOM_DOMAIN = 'custom-domain',
  SSL_TERMINATION = 'ssl-termination',
  EDGE_COMPUTING = 'edge-computing',
  ANALYTICS = 'analytics',
  DDoS_PROTECTION = 'ddos-protection',
  WAF = 'web-application-firewall',
  API_ACCELERATION = 'api-acceleration',
  VIDEO_STREAMING = 'video-streaming'
}

// CDN提供商配置
export const CDN_PROVIDERS: Record<CDNProvider, CDNProviderConfig> = {
  [CDNProvider.JSDELIVR]: {
    provider: CDNProvider.JSDELIVR,
    name: 'jsDelivr',
    baseUrl: 'https://cdn.jsdelivr.net',
    features: [
      CDNFeature.COMPRESSION,
      CDNFeature.SSL_TERMINATION,
      CDNFeature.ANALYTICS
    ],
    pricing: 'free',
    regions: ['global'],
    maxFileSize: '20MB',
    cacheTTL: {
      min: 3600,    // 1小时
      max: ********, // 1年
      default: 86400 // 1天
    },
    supportedFileTypes: [
      'js', 'css', 'json', 'xml', 'svg', 'png', 'jpg', 'jpeg', 
      'gif', 'webp', 'ico', 'woff', 'woff2', 'ttf', 'eot'
    ],
    setup: {
      requiresAccount: false,
      apiKey: false,
      customDomain: false,
      ssl: true
    }
  },

  [CDNProvider.CLOUDFLARE]: {
    provider: CDNProvider.CLOUDFLARE,
    name: 'Cloudflare',
    baseUrl: 'https://cdnjs.cloudflare.com',
    features: [
      CDNFeature.IMAGE_OPTIMIZATION,
      CDNFeature.COMPRESSION,
      CDNFeature.CUSTOM_DOMAIN,
      CDNFeature.SSL_TERMINATION,
      CDNFeature.EDGE_COMPUTING,
      CDNFeature.ANALYTICS,
      CDNFeature.DDoS_PROTECTION,
      CDNFeature.WAF
    ],
    pricing: 'paid',
    regions: ['global', 'americas', 'europe', 'asia-pacific'],
    maxFileSize: '100MB',
    cacheTTL: {
      min: 30,       // 30秒
      max: ********, // 1年
      default: 14400 // 4小时
    },
    supportedFileTypes: ['*'],
    setup: {
      requiresAccount: true,
      apiKey: true,
      customDomain: true,
      ssl: true
    }
  },

  [CDNProvider.AWS_CLOUDFRONT]: {
    provider: CDNProvider.AWS_CLOUDFRONT,
    name: 'AWS CloudFront',
    baseUrl: 'https://d123456789.cloudfront.net',
    features: [
      CDNFeature.IMAGE_OPTIMIZATION,
      CDNFeature.COMPRESSION,
      CDNFeature.CUSTOM_DOMAIN,
      CDNFeature.SSL_TERMINATION,
      CDNFeature.EDGE_COMPUTING,
      CDNFeature.ANALYTICS,
      CDNFeature.DDoS_PROTECTION,
      CDNFeature.API_ACCELERATION
    ],
    pricing: 'paid',
    regions: ['us-east-1', 'us-west-2', 'eu-west-1', 'ap-southeast-1'],
    maxFileSize: '20GB',
    cacheTTL: {
      min: 0,
      max: ********, // 1年
      default: 86400 // 1天
    },
    supportedFileTypes: ['*'],
    setup: {
      requiresAccount: true,
      apiKey: true,
      customDomain: true,
      ssl: true
    }
  },

  [CDNProvider.AZURE_CDN]: {
    provider: CDNProvider.AZURE_CDN,
    name: 'Azure CDN',
    baseUrl: 'https://mycdn.azureedge.net',
    features: [
      CDNFeature.IMAGE_OPTIMIZATION,
      CDNFeature.COMPRESSION,
      CDNFeature.CUSTOM_DOMAIN,
      CDNFeature.SSL_TERMINATION,
      CDNFeature.ANALYTICS,
      CDNFeature.DDoS_PROTECTION
    ],
    pricing: 'paid',
    regions: ['north-america', 'europe', 'asia'],
    maxFileSize: '2GB',
    cacheTTL: {
      min: 1,
      max: ********, // 1年
      default: 7200  // 2小时
    },
    supportedFileTypes: ['*'],
    setup: {
      requiresAccount: true,
      apiKey: true,
      customDomain: true,
      ssl: true
    }
  },

  [CDNProvider.GOOGLE_CLOUD_CDN]: {
    provider: CDNProvider.GOOGLE_CLOUD_CDN,
    name: 'Google Cloud CDN',
    baseUrl: 'https://storage.googleapis.com',
    features: [
      CDNFeature.COMPRESSION,
      CDNFeature.CUSTOM_DOMAIN,
      CDNFeature.SSL_TERMINATION,
      CDNFeature.ANALYTICS,
      CDNFeature.DDoS_PROTECTION
    ],
    pricing: 'paid',
    regions: ['global'],
    maxFileSize: '5TB',
    cacheTTL: {
      min: 0,
      max: ********, // 1年
      default: 3600  // 1小时
    },
    supportedFileTypes: ['*'],
    setup: {
      requiresAccount: true,
      apiKey: true,
      customDomain: true,
      ssl: true
    }
  },

  [CDNProvider.CUSTOM]: {
    provider: CDNProvider.CUSTOM,
    name: 'Custom CDN',
    baseUrl: 'https://your-cdn-domain.com',
    features: [CDNFeature.CUSTOM_DOMAIN],
    pricing: 'enterprise',
    regions: ['custom'],
    maxFileSize: 'unlimited',
    cacheTTL: {
      min: 0,
      max: ********,
      default: 86400
    },
    supportedFileTypes: ['*'],
    setup: {
      requiresAccount: true,
      apiKey: false,
      customDomain: true,
      ssl: true
    }
  }
}

// 默认CDN配置
export const DEFAULT_CDN_CONFIG: CDNConfig = {
  enabled: false,
  provider: CDNProvider.JSDELIVR,
  cacheTTL: 86400, // 1天
  fallbackEnabled: true,
  monitoring: {
    enabled: false
  }
}

// 环境特定的CDN配置
export const getEnvironmentCDNConfig = (env: string): CDNConfig => {
  const configs: Record<string, Partial<CDNConfig>> = {
    development: {
      enabled: false,
      provider: CDNProvider.JSDELIVR
    },
    testing: {
      enabled: true,
      provider: CDNProvider.JSDELIVR,
      fallbackEnabled: true
    },
    staging: {
      enabled: true,
      provider: CDNProvider.CLOUDFLARE,
      cacheTTL: 3600, // 1小时
      fallbackEnabled: true,
      monitoring: {
        enabled: true
      }
    },
    production: {
      enabled: true,
      provider: CDNProvider.CLOUDFLARE,
      cacheTTL: 86400, // 1天
      fallbackEnabled: true,
      monitoring: {
        enabled: true
      }
    }
  }

  return {
    ...DEFAULT_CDN_CONFIG,
    ...configs[env]
  }
}

// CDN URL生成器
export class CDNUrlGenerator {
  private config: CDNConfig
  private providerConfig: CDNProviderConfig

  constructor(config: CDNConfig) {
    this.config = config
    this.providerConfig = CDN_PROVIDERS[config.provider]
  }

  /**
   * 生成CDN资源URL
   */
  generateAssetUrl(assetPath: string, version?: string): string {
    if (!this.config.enabled) {
      return assetPath
    }

    const baseUrl = this.config.customDomain || this.config.domain || this.providerConfig.baseUrl
    const versionSuffix = version ? `@${version}` : ''
    
    // 移除开头的斜杠
    const cleanPath = assetPath.replace(/^\//, '')
    
    switch (this.config.provider) {
      case CDNProvider.JSDELIVR:
        return `${baseUrl}/npm/selfgame${versionSuffix}/${cleanPath}`
        
      case CDNProvider.CLOUDFLARE:
        return `${baseUrl}/${cleanPath}`
        
      case CDNProvider.AWS_CLOUDFRONT:
        return `${baseUrl}/${cleanPath}`
        
      case CDNProvider.AZURE_CDN:
        return `${baseUrl}/${cleanPath}`
        
      case CDNProvider.GOOGLE_CLOUD_CDN:
        return `${baseUrl}/${cleanPath}`
        
      case CDNProvider.CUSTOM:
        return `${baseUrl}/${cleanPath}`
        
      default:
        return assetPath
    }
  }

  /**
   * 生成预加载链接
   */
  generatePreloadLinks(assets: string[]): string[] {
    return assets.map(asset => {
      const cdnUrl = this.generateAssetUrl(asset)
      return `<link rel="preload" href="${cdnUrl}" as="${this.getAssetType(asset)}">`
    })
  }

  /**
   * 获取资源类型
   */
  private getAssetType(assetPath: string): string {
    const ext = assetPath.split('.').pop()?.toLowerCase()
    
    switch (ext) {
      case 'js':
        return 'script'
      case 'css':
        return 'style'
      case 'woff':
      case 'woff2':
      case 'ttf':
      case 'eot':
        return 'font'
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'webp':
      case 'svg':
        return 'image'
      case 'mp4':
      case 'webm':
      case 'ogg':
        return 'video'
      case 'mp3':
      case 'wav':
      case 'flac':
        return 'audio'
      default:
        return 'fetch'
    }
  }
}

// CDN性能监控
export interface CDNPerformanceMetrics {
  provider: CDNProvider
  region: string
  loadTime: number
  cacheHitRate: number
  errorRate: number
  bandwidth: number
  timestamp: number
}

export class CDNMonitor {
  private metrics: CDNPerformanceMetrics[] = []

  /**
   * 记录性能指标
   */
  recordMetrics(metrics: CDNPerformanceMetrics) {
    this.metrics.push(metrics)
    
    // 保持最近1000条记录
    if (this.metrics.length > 1000) {
      this.metrics = this.metrics.slice(-1000)
    }
  }

  /**
   * 获取性能报告
   */
  getPerformanceReport(timeRange: number = 3600000): {
    averageLoadTime: number
    cacheHitRate: number
    errorRate: number
    totalBandwidth: number
  } {
    const cutoff = Date.now() - timeRange
    const recentMetrics = this.metrics.filter(m => m.timestamp > cutoff)
    
    if (recentMetrics.length === 0) {
      return {
        averageLoadTime: 0,
        cacheHitRate: 0,
        errorRate: 0,
        totalBandwidth: 0
      }
    }

    return {
      averageLoadTime: recentMetrics.reduce((sum, m) => sum + m.loadTime, 0) / recentMetrics.length,
      cacheHitRate: recentMetrics.reduce((sum, m) => sum + m.cacheHitRate, 0) / recentMetrics.length,
      errorRate: recentMetrics.reduce((sum, m) => sum + m.errorRate, 0) / recentMetrics.length,
      totalBandwidth: recentMetrics.reduce((sum, m) => sum + m.bandwidth, 0)
    }
  }

  /**
   * 检查CDN健康状态
   */
  async checkCDNHealth(cdnUrl: string): Promise<{
    isHealthy: boolean
    responseTime: number
    statusCode?: number
    error?: string
  }> {
    const start = performance.now()
    
    try {
      const response = await fetch(`${cdnUrl}/health-check.json`, {
        method: 'HEAD',
        cache: 'no-cache'
      })
      
      const responseTime = performance.now() - start
      
      return {
        isHealthy: response.ok,
        responseTime,
        statusCode: response.status
      }
    } catch (error) {
      return {
        isHealthy: false,
        responseTime: performance.now() - start,
        error: error instanceof Error ? error.message : String(error)
      }
    }
  }
}

export default {
  CDN_PROVIDERS,
  DEFAULT_CDN_CONFIG,
  getEnvironmentCDNConfig,
  CDNUrlGenerator,
  CDNMonitor
} 