export interface CDNConfig {
    enabled: boolean;
    provider: CDNProvider;
    domain?: string;
    regions?: string[];
    customDomain?: string;
    cacheTTL?: number;
    fallbackEnabled?: boolean;
    monitoring?: {
        enabled: boolean;
        endpoint?: string;
    };
}
export declare enum CDNProvider {
    JSDELIVR = "jsdelivr",
    CLOUDFLARE = "cloudflare",
    AWS_CLOUDFRONT = "aws-cloudfront",
    AZURE_CDN = "azure-cdn",
    GOOGLE_CLOUD_CDN = "google-cloud-cdn",
    CUSTOM = "custom"
}
export interface CDNProviderConfig {
    provider: CDNProvider;
    name: string;
    baseUrl: string;
    features: CDNFeature[];
    pricing: 'free' | 'paid' | 'enterprise';
    regions: string[];
    maxFileSize?: string;
    cacheTTL: {
        min: number;
        max: number;
        default: number;
    };
    supportedFileTypes: string[];
    setup: {
        requiresAccount: boolean;
        apiKey?: boolean;
        customDomain?: boolean;
        ssl?: boolean;
    };
}
export declare enum CDNFeature {
    IMAGE_OPTIMIZATION = "image-optimization",
    COMPRESSION = "compression",
    CUSTOM_DOMAIN = "custom-domain",
    SSL_TERMINATION = "ssl-termination",
    EDGE_COMPUTING = "edge-computing",
    ANALYTICS = "analytics",
    DDoS_PROTECTION = "ddos-protection",
    WAF = "web-application-firewall",
    API_ACCELERATION = "api-acceleration",
    VIDEO_STREAMING = "video-streaming"
}
export declare const CDN_PROVIDERS: Record<CDNProvider, CDNProviderConfig>;
export declare const DEFAULT_CDN_CONFIG: CDNConfig;
export declare const getEnvironmentCDNConfig: (env: string) => CDNConfig;
export declare class CDNUrlGenerator {
    private config;
    private providerConfig;
    constructor(config: CDNConfig);
    /**
     * 生成CDN资源URL
     */
    generateAssetUrl(assetPath: string, version?: string): string;
    /**
     * 生成预加载链接
     */
    generatePreloadLinks(assets: string[]): string[];
    /**
     * 获取资源类型
     */
    private getAssetType;
}
export interface CDNPerformanceMetrics {
    provider: CDNProvider;
    region: string;
    loadTime: number;
    cacheHitRate: number;
    errorRate: number;
    bandwidth: number;
    timestamp: number;
}
export declare class CDNMonitor {
    private metrics;
    /**
     * 记录性能指标
     */
    recordMetrics(metrics: CDNPerformanceMetrics): void;
    /**
     * 获取性能报告
     */
    getPerformanceReport(timeRange?: number): {
        averageLoadTime: number;
        cacheHitRate: number;
        errorRate: number;
        totalBandwidth: number;
    };
    /**
     * 检查CDN健康状态
     */
    checkCDNHealth(cdnUrl: string): Promise<{
        isHealthy: boolean;
        responseTime: number;
        statusCode?: number;
        error?: string;
    }>;
}
declare const _default: {
    CDN_PROVIDERS: Record<CDNProvider, CDNProviderConfig>;
    DEFAULT_CDN_CONFIG: CDNConfig;
    getEnvironmentCDNConfig: (env: string) => CDNConfig;
    CDNUrlGenerator: typeof CDNUrlGenerator;
    CDNMonitor: typeof CDNMonitor;
};
export default _default;
