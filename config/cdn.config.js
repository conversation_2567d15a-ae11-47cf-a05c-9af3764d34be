var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var _a;
export var CDNProvider;
(function (CDNProvider) {
    CDNProvider["JSDELIVR"] = "jsdelivr";
    CDNProvider["CLOUDFLARE"] = "cloudflare";
    CDNProvider["AWS_CLOUDFRONT"] = "aws-cloudfront";
    CDNProvider["AZURE_CDN"] = "azure-cdn";
    CDNProvider["GOOGLE_CLOUD_CDN"] = "google-cloud-cdn";
    CDNProvider["CUSTOM"] = "custom";
})(CDNProvider || (CDNProvider = {}));
export var CDNFeature;
(function (CDNFeature) {
    CDNFeature["IMAGE_OPTIMIZATION"] = "image-optimization";
    CDNFeature["COMPRESSION"] = "compression";
    CDNFeature["CUSTOM_DOMAIN"] = "custom-domain";
    CDNFeature["SSL_TERMINATION"] = "ssl-termination";
    CDNFeature["EDGE_COMPUTING"] = "edge-computing";
    CDNFeature["ANALYTICS"] = "analytics";
    CDNFeature["DDoS_PROTECTION"] = "ddos-protection";
    CDNFeature["WAF"] = "web-application-firewall";
    CDNFeature["API_ACCELERATION"] = "api-acceleration";
    CDNFeature["VIDEO_STREAMING"] = "video-streaming";
})(CDNFeature || (CDNFeature = {}));
// CDN提供商配置
export var CDN_PROVIDERS = (_a = {},
    _a[CDNProvider.JSDELIVR] = {
        provider: CDNProvider.JSDELIVR,
        name: 'jsDelivr',
        baseUrl: 'https://cdn.jsdelivr.net',
        features: [
            CDNFeature.COMPRESSION,
            CDNFeature.SSL_TERMINATION,
            CDNFeature.ANALYTICS
        ],
        pricing: 'free',
        regions: ['global'],
        maxFileSize: '20MB',
        cacheTTL: {
            min: 3600, // 1小时
            max: ********, // 1年
            default: 86400 // 1天
        },
        supportedFileTypes: [
            'js', 'css', 'json', 'xml', 'svg', 'png', 'jpg', 'jpeg',
            'gif', 'webp', 'ico', 'woff', 'woff2', 'ttf', 'eot'
        ],
        setup: {
            requiresAccount: false,
            apiKey: false,
            customDomain: false,
            ssl: true
        }
    },
    _a[CDNProvider.CLOUDFLARE] = {
        provider: CDNProvider.CLOUDFLARE,
        name: 'Cloudflare',
        baseUrl: 'https://cdnjs.cloudflare.com',
        features: [
            CDNFeature.IMAGE_OPTIMIZATION,
            CDNFeature.COMPRESSION,
            CDNFeature.CUSTOM_DOMAIN,
            CDNFeature.SSL_TERMINATION,
            CDNFeature.EDGE_COMPUTING,
            CDNFeature.ANALYTICS,
            CDNFeature.DDoS_PROTECTION,
            CDNFeature.WAF
        ],
        pricing: 'paid',
        regions: ['global', 'americas', 'europe', 'asia-pacific'],
        maxFileSize: '100MB',
        cacheTTL: {
            min: 30, // 30秒
            max: ********, // 1年
            default: 14400 // 4小时
        },
        supportedFileTypes: ['*'],
        setup: {
            requiresAccount: true,
            apiKey: true,
            customDomain: true,
            ssl: true
        }
    },
    _a[CDNProvider.AWS_CLOUDFRONT] = {
        provider: CDNProvider.AWS_CLOUDFRONT,
        name: 'AWS CloudFront',
        baseUrl: 'https://d123456789.cloudfront.net',
        features: [
            CDNFeature.IMAGE_OPTIMIZATION,
            CDNFeature.COMPRESSION,
            CDNFeature.CUSTOM_DOMAIN,
            CDNFeature.SSL_TERMINATION,
            CDNFeature.EDGE_COMPUTING,
            CDNFeature.ANALYTICS,
            CDNFeature.DDoS_PROTECTION,
            CDNFeature.API_ACCELERATION
        ],
        pricing: 'paid',
        regions: ['us-east-1', 'us-west-2', 'eu-west-1', 'ap-southeast-1'],
        maxFileSize: '20GB',
        cacheTTL: {
            min: 0,
            max: ********, // 1年
            default: 86400 // 1天
        },
        supportedFileTypes: ['*'],
        setup: {
            requiresAccount: true,
            apiKey: true,
            customDomain: true,
            ssl: true
        }
    },
    _a[CDNProvider.AZURE_CDN] = {
        provider: CDNProvider.AZURE_CDN,
        name: 'Azure CDN',
        baseUrl: 'https://mycdn.azureedge.net',
        features: [
            CDNFeature.IMAGE_OPTIMIZATION,
            CDNFeature.COMPRESSION,
            CDNFeature.CUSTOM_DOMAIN,
            CDNFeature.SSL_TERMINATION,
            CDNFeature.ANALYTICS,
            CDNFeature.DDoS_PROTECTION
        ],
        pricing: 'paid',
        regions: ['north-america', 'europe', 'asia'],
        maxFileSize: '2GB',
        cacheTTL: {
            min: 1,
            max: ********, // 1年
            default: 7200 // 2小时
        },
        supportedFileTypes: ['*'],
        setup: {
            requiresAccount: true,
            apiKey: true,
            customDomain: true,
            ssl: true
        }
    },
    _a[CDNProvider.GOOGLE_CLOUD_CDN] = {
        provider: CDNProvider.GOOGLE_CLOUD_CDN,
        name: 'Google Cloud CDN',
        baseUrl: 'https://storage.googleapis.com',
        features: [
            CDNFeature.COMPRESSION,
            CDNFeature.CUSTOM_DOMAIN,
            CDNFeature.SSL_TERMINATION,
            CDNFeature.ANALYTICS,
            CDNFeature.DDoS_PROTECTION
        ],
        pricing: 'paid',
        regions: ['global'],
        maxFileSize: '5TB',
        cacheTTL: {
            min: 0,
            max: ********, // 1年
            default: 3600 // 1小时
        },
        supportedFileTypes: ['*'],
        setup: {
            requiresAccount: true,
            apiKey: true,
            customDomain: true,
            ssl: true
        }
    },
    _a[CDNProvider.CUSTOM] = {
        provider: CDNProvider.CUSTOM,
        name: 'Custom CDN',
        baseUrl: 'https://your-cdn-domain.com',
        features: [CDNFeature.CUSTOM_DOMAIN],
        pricing: 'enterprise',
        regions: ['custom'],
        maxFileSize: 'unlimited',
        cacheTTL: {
            min: 0,
            max: ********,
            default: 86400
        },
        supportedFileTypes: ['*'],
        setup: {
            requiresAccount: true,
            apiKey: false,
            customDomain: true,
            ssl: true
        }
    },
    _a);
// 默认CDN配置
export var DEFAULT_CDN_CONFIG = {
    enabled: false,
    provider: CDNProvider.JSDELIVR,
    cacheTTL: 86400, // 1天
    fallbackEnabled: true,
    monitoring: {
        enabled: false
    }
};
// 环境特定的CDN配置
export var getEnvironmentCDNConfig = function (env) {
    var configs = {
        development: {
            enabled: false,
            provider: CDNProvider.JSDELIVR
        },
        testing: {
            enabled: true,
            provider: CDNProvider.JSDELIVR,
            fallbackEnabled: true
        },
        staging: {
            enabled: true,
            provider: CDNProvider.CLOUDFLARE,
            cacheTTL: 3600, // 1小时
            fallbackEnabled: true,
            monitoring: {
                enabled: true
            }
        },
        production: {
            enabled: true,
            provider: CDNProvider.CLOUDFLARE,
            cacheTTL: 86400, // 1天
            fallbackEnabled: true,
            monitoring: {
                enabled: true
            }
        }
    };
    return __assign(__assign({}, DEFAULT_CDN_CONFIG), configs[env]);
};
// CDN URL生成器
var CDNUrlGenerator = /** @class */ (function () {
    function CDNUrlGenerator(config) {
        this.config = config;
        this.providerConfig = CDN_PROVIDERS[config.provider];
    }
    /**
     * 生成CDN资源URL
     */
    CDNUrlGenerator.prototype.generateAssetUrl = function (assetPath, version) {
        if (!this.config.enabled) {
            return assetPath;
        }
        var baseUrl = this.config.customDomain || this.config.domain || this.providerConfig.baseUrl;
        var versionSuffix = version ? "@".concat(version) : '';
        // 移除开头的斜杠
        var cleanPath = assetPath.replace(/^\//, '');
        switch (this.config.provider) {
            case CDNProvider.JSDELIVR:
                return "".concat(baseUrl, "/npm/selfgame").concat(versionSuffix, "/").concat(cleanPath);
            case CDNProvider.CLOUDFLARE:
                return "".concat(baseUrl, "/").concat(cleanPath);
            case CDNProvider.AWS_CLOUDFRONT:
                return "".concat(baseUrl, "/").concat(cleanPath);
            case CDNProvider.AZURE_CDN:
                return "".concat(baseUrl, "/").concat(cleanPath);
            case CDNProvider.GOOGLE_CLOUD_CDN:
                return "".concat(baseUrl, "/").concat(cleanPath);
            case CDNProvider.CUSTOM:
                return "".concat(baseUrl, "/").concat(cleanPath);
            default:
                return assetPath;
        }
    };
    /**
     * 生成预加载链接
     */
    CDNUrlGenerator.prototype.generatePreloadLinks = function (assets) {
        var _this = this;
        return assets.map(function (asset) {
            var cdnUrl = _this.generateAssetUrl(asset);
            return "<link rel=\"preload\" href=\"".concat(cdnUrl, "\" as=\"").concat(_this.getAssetType(asset), "\">");
        });
    };
    /**
     * 获取资源类型
     */
    CDNUrlGenerator.prototype.getAssetType = function (assetPath) {
        var _a;
        var ext = (_a = assetPath.split('.').pop()) === null || _a === void 0 ? void 0 : _a.toLowerCase();
        switch (ext) {
            case 'js':
                return 'script';
            case 'css':
                return 'style';
            case 'woff':
            case 'woff2':
            case 'ttf':
            case 'eot':
                return 'font';
            case 'jpg':
            case 'jpeg':
            case 'png':
            case 'gif':
            case 'webp':
            case 'svg':
                return 'image';
            case 'mp4':
            case 'webm':
            case 'ogg':
                return 'video';
            case 'mp3':
            case 'wav':
            case 'flac':
                return 'audio';
            default:
                return 'fetch';
        }
    };
    return CDNUrlGenerator;
}());
export { CDNUrlGenerator };
var CDNMonitor = /** @class */ (function () {
    function CDNMonitor() {
        this.metrics = [];
    }
    /**
     * 记录性能指标
     */
    CDNMonitor.prototype.recordMetrics = function (metrics) {
        this.metrics.push(metrics);
        // 保持最近1000条记录
        if (this.metrics.length > 1000) {
            this.metrics = this.metrics.slice(-1000);
        }
    };
    /**
     * 获取性能报告
     */
    CDNMonitor.prototype.getPerformanceReport = function (timeRange) {
        if (timeRange === void 0) { timeRange = 3600000; }
        var cutoff = Date.now() - timeRange;
        var recentMetrics = this.metrics.filter(function (m) { return m.timestamp > cutoff; });
        if (recentMetrics.length === 0) {
            return {
                averageLoadTime: 0,
                cacheHitRate: 0,
                errorRate: 0,
                totalBandwidth: 0
            };
        }
        return {
            averageLoadTime: recentMetrics.reduce(function (sum, m) { return sum + m.loadTime; }, 0) / recentMetrics.length,
            cacheHitRate: recentMetrics.reduce(function (sum, m) { return sum + m.cacheHitRate; }, 0) / recentMetrics.length,
            errorRate: recentMetrics.reduce(function (sum, m) { return sum + m.errorRate; }, 0) / recentMetrics.length,
            totalBandwidth: recentMetrics.reduce(function (sum, m) { return sum + m.bandwidth; }, 0)
        };
    };
    /**
     * 检查CDN健康状态
     */
    CDNMonitor.prototype.checkCDNHealth = function (cdnUrl) {
        return __awaiter(this, void 0, void 0, function () {
            var start, response, responseTime, error_1;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        start = performance.now();
                        _a.label = 1;
                    case 1:
                        _a.trys.push([1, 3, , 4]);
                        return [4 /*yield*/, fetch("".concat(cdnUrl, "/health-check.json"), {
                                method: 'HEAD',
                                cache: 'no-cache'
                            })];
                    case 2:
                        response = _a.sent();
                        responseTime = performance.now() - start;
                        return [2 /*return*/, {
                                isHealthy: response.ok,
                                responseTime: responseTime,
                                statusCode: response.status
                            }];
                    case 3:
                        error_1 = _a.sent();
                        return [2 /*return*/, {
                                isHealthy: false,
                                responseTime: performance.now() - start,
                                error: error_1 instanceof Error ? error_1.message : String(error_1)
                            }];
                    case 4: return [2 /*return*/];
                }
            });
        });
    };
    return CDNMonitor;
}());
export { CDNMonitor };
export default {
    CDN_PROVIDERS: CDN_PROVIDERS,
    DEFAULT_CDN_CONFIG: DEFAULT_CDN_CONFIG,
    getEnvironmentCDNConfig: getEnvironmentCDNConfig,
    CDNUrlGenerator: CDNUrlGenerator,
    CDNMonitor: CDNMonitor
};
