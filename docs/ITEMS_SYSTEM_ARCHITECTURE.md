# 期货游戏道具系统架构文档

## 概述

本文档描述了期货游戏道具系统的解耦化架构设计，旨在提供清晰的分层结构、良好的可扩展性和易于维护的代码组织。

## 系统架构

### 1. 架构分层

```
┌─────────────────────────────────────────┐
│               UI层 (Presentation)        │
│  ┌─────────────┐  ┌─────────────────┐   │
│  │   组件库     │  │    页面组件      │   │
│  └─────────────┘  └─────────────────┘   │
└─────────────────────────────────────────┘
                    │
┌─────────────────────────────────────────┐
│              应用服务层 (Application)      │
│  ┌─────────────┐  ┌─────────────────┐   │
│  │  道具服务    │  │   合成服务       │   │
│  │  盲盒服务    │  │   装备服务       │   │
│  └─────────────┘  └─────────────────┘   │
└─────────────────────────────────────────┘
                    │
┌─────────────────────────────────────────┐
│               业务层 (Domain)             │
│  ┌─────────────┐  ┌─────────────────┐   │
│  │  道具实体    │  │   业务规则       │   │
│  │  值对象      │  │   领域事件       │   │
│  └─────────────┘  └─────────────────┘   │
└─────────────────────────────────────────┘
                    │
┌─────────────────────────────────────────┐
│             基础设施层 (Infrastructure)    │
│  ┌─────────────┐  ┌─────────────────┐   │
│  │ 数据存储     │  │   事件总线       │   │
│  │ 配置管理     │  │   工具库         │   │
│  └─────────────┘  └─────────────────┘   │
└─────────────────────────────────────────┘
```

### 2. 核心设计原则

#### 2.1 依赖反转原则 (DIP)
- 高层模块不依赖低层模块，都依赖于抽象
- 抽象不依赖于细节，细节依赖于抽象

#### 2.2 单一职责原则 (SRP)
- 每个类只负责一个职责
- 变化的原因只有一个

#### 2.3 开闭原则 (OCP)
- 对扩展开放，对修改关闭
- 通过抽象实现可扩展性

#### 2.4 接口隔离原则 (ISP)
- 客户端不应该依赖它不需要的接口
- 使用多个专门的接口，而不是一个总的接口

## 详细设计

### 3. 核心抽象层

#### 3.1 道具抽象接口

```typescript
// 核心道具接口
interface IItem {
  readonly id: ItemId
  readonly metadata: ItemMetadata
  readonly properties: ItemProperties
  readonly state: ItemState
}

// 道具仓库接口
interface IItemRepository {
  findById(id: ItemId): Promise<IItem | null>
  findByCategory(category: ItemCategory): Promise<IItem[]>
  save(item: IItem): Promise<void>
  delete(id: ItemId): Promise<void>
}

// 道具工厂接口
interface IItemFactory {
  createItem(template: ItemTemplate, quality: Quality): IItem
  createFromConfig(config: ItemConfig): IItem
}
```

#### 3.2 服务抽象接口

```typescript
// 道具服务接口
interface IItemService {
  getItem(id: ItemId): Promise<IItem | null>
  createItem(template: ItemTemplate, quality: Quality): Promise<IItem>
  updateItem(id: ItemId, updates: Partial<ItemProperties>): Promise<void>
  deleteItem(id: ItemId): Promise<void>
}

// 合成服务接口
interface ISynthesisService {
  synthesize(recipe: SynthesisRecipe, materials: IItem[]): Promise<SynthesisResult>
  getRecipes(category?: ItemCategory): Promise<SynthesisRecipe[]>
  validateMaterials(recipe: SynthesisRecipe, materials: IItem[]): boolean
}

// 盲盒服务接口
interface ILootboxService {
  openLootbox(type: LootboxType, currency: Currency): Promise<LootboxResult>
  getAvailableLootboxes(): Promise<LootboxConfig[]>
  previewLootbox(type: LootboxType): Promise<LootboxPreview>
}
```

### 4. 事件系统

#### 4.1 领域事件

```typescript
// 基础事件接口
interface IDomainEvent {
  readonly eventId: string
  readonly eventType: string
  readonly timestamp: Date
  readonly aggregateId: string
  readonly eventData: Record<string, any>
}

// 道具相关事件
interface ItemCreatedEvent extends IDomainEvent {
  eventType: 'ItemCreated'
  eventData: {
    itemId: string
    itemType: ItemCategory
    quality: Quality
    creator: string
  }
}

interface ItemSynthesizedEvent extends IDomainEvent {
  eventType: 'ItemSynthesized'
  eventData: {
    resultItem: IItem
    materials: IItem[]
    recipe: SynthesisRecipe
    success: boolean
  }
}
```

#### 4.2 事件总线

```typescript
interface IEventBus {
  publish(event: IDomainEvent): Promise<void>
  subscribe<T extends IDomainEvent>(
    eventType: string, 
    handler: (event: T) => Promise<void>
  ): void
  unsubscribe(eventType: string, handler: Function): void
}
```

### 5. 配置系统

#### 5.1 配置管理器

```typescript
interface IConfigManager {
  get<T>(key: string): T
  set<T>(key: string, value: T): void
  getSection<T>(section: string): T
  reload(): Promise<void>
}

// 道具配置
interface ItemConfig {
  categories: ItemCategoryConfig[]
  qualities: QualityConfig[]
  synthesis: SynthesisConfig
  lootboxes: LootboxConfig[]
}
```

### 6. 数据持久化

#### 6.1 仓库模式

```typescript
// 基础仓库接口
interface IRepository<T, ID> {
  findById(id: ID): Promise<T | null>
  save(entity: T): Promise<void>
  delete(id: ID): Promise<void>
  findAll(): Promise<T[]>
}

// 查询规格接口
interface ISpecification<T> {
  isSatisfiedBy(item: T): boolean
  and(other: ISpecification<T>): ISpecification<T>
  or(other: ISpecification<T>): ISpecification<T>
}
```

### 7. 状态管理

#### 7.1 状态管理器

```typescript
interface IStateManager {
  getState<T>(key: string): T
  setState<T>(key: string, value: T): void
  subscribe<T>(key: string, listener: (value: T) => void): () => void
  dispatch(action: Action): void
}

// 状态切片
interface ItemStateSlice {
  items: Record<string, IItem>
  inventory: InventoryState
  equipment: EquipmentState
  synthesis: SynthesisState
}
```

## 实现指南

### 8. 依赖注入容器

```typescript
class DIContainer {
  private services = new Map<string, any>()
  
  register<T>(token: string, implementation: T): void
  resolve<T>(token: string): T
  singleton<T>(token: string, factory: () => T): void
}

// 服务注册
const container = new DIContainer()
container.register('IItemRepository', new ItemRepository())
container.register('IItemFactory', new ItemFactory())
container.register('IItemService', new ItemService(
  container.resolve('IItemRepository'),
  container.resolve('IItemFactory')
))
```

### 9. 错误处理

```typescript
// 业务错误基类
abstract class BusinessError extends Error {
  abstract readonly code: string
  abstract readonly category: ErrorCategory
}

// 道具相关错误
class ItemNotFoundError extends BusinessError {
  readonly code = 'ITEM_NOT_FOUND'
  readonly category = ErrorCategory.BUSINESS
}

class SynthesisFailedError extends BusinessError {
  readonly code = 'SYNTHESIS_FAILED'
  readonly category = ErrorCategory.BUSINESS
}
```

### 10. 扩展点

#### 10.1 插件系统

```typescript
interface IPlugin {
  readonly name: string
  readonly version: string
  initialize(context: PluginContext): Promise<void>
  dispose(): Promise<void>
}

interface PluginContext {
  container: DIContainer
  eventBus: IEventBus
  configManager: IConfigManager
}
```

#### 10.2 中间件系统

```typescript
interface IMiddleware<T> {
  execute(context: T, next: () => Promise<T>): Promise<T>
}

class MiddlewarePipeline<T> {
  private middlewares: IMiddleware<T>[] = []
  
  use(middleware: IMiddleware<T>): void
  execute(context: T): Promise<T>
}
```

## 迁移策略

### 11. 渐进式重构

#### 阶段1: 抽象层建立
1. 创建核心抽象接口
2. 建立事件系统
3. 实现配置管理

#### 阶段2: 服务层重构
1. 重构ItemService
2. 重构SynthesisService
3. 重构LootboxService

#### 阶段3: 基础设施完善
1. 实现依赖注入
2. 完善错误处理
3. 添加日志记录

#### 阶段4: UI层解耦
1. 重构React组件
2. 实现Hooks封装
3. 优化状态管理

### 12. 质量保证

#### 12.1 测试策略
- 单元测试：每个服务类和工具函数
- 集成测试：服务间协作
- 端到端测试：完整业务流程

#### 12.2 性能监控
- 性能指标收集
- 内存使用监控
- 响应时间追踪

## 总结

这个解耦化架构提供了：

1. **清晰的职责分离**：每层有明确的职责
2. **良好的可测试性**：依赖注入使单元测试更容易
3. **高可扩展性**：通过抽象接口支持新功能添加
4. **松耦合**：各模块间通过接口通信
5. **易于维护**：清晰的代码组织和文档

这个架构为期货游戏项目的后续开发提供了坚实的基础，支持快速迭代和功能扩展。 