# 🆘 自律农场帮助中心

欢迎来到自律农场帮助中心！这里提供了全面的使用指南、技术文档和常见问题解答，帮助您充分利用自律农场的所有功能。

## 🔍 快速搜索

<div class="search-container">
  <input type="text" placeholder="搜索帮助内容..." class="search-input" />
  <div class="search-suggestions">
    <div class="popular-searches">
      <h4>热门搜索</h4>
      <span class="tag">摄像头权限</span>
      <span class="tag">作物不生长</span>
      <span class="tag">数据备份</span>
      <span class="tag">专注度检测</span>
    </div>
  </div>
</div>

---

## 📚 文档分类

### 👥 用户文档
适合普通用户查阅的使用指南和帮助文档

<div class="doc-cards">
  <div class="doc-card user-docs">
    <div class="card-icon">📖</div>
    <h3><a href="../user-manual/index.md">用户手册</a></h3>
    <p>完整的使用指南，从新手入门到高级功能，帮助您快速掌握自律农场的所有功能。</p>
    <div class="card-tags">
      <span class="tag">新手必读</span>
      <span class="tag">功能详解</span>
      <span class="tag">故障排除</span>
    </div>
    <div class="card-actions">
      <a href="../user-manual/index.md" class="btn primary">开始阅读</a>
      <a href="#user-quick-start" class="btn secondary">快速入门</a>
    </div>
  </div>

  <div class="doc-card user-docs">
    <div class="card-icon">❓</div>
    <h3><a href="../user-manual/faq.md">常见问题</a></h3>
    <p>收录了用户最常遇到的问题和详细解答，按分类整理，快速找到解决方案。</p>
    <div class="card-tags">
      <span class="tag">问题解答</span>
      <span class="tag">故障诊断</span>
      <span class="tag">使用技巧</span>
    </div>
    <div class="card-actions">
      <a href="../user-manual/faq.md" class="btn primary">查看FAQ</a>
      <a href="#troubleshooting" class="btn secondary">故障排除</a>
    </div>
  </div>
</div>

### 🛠️ 开发者文档
适合开发者和高级用户的技术文档

<div class="doc-cards">
  <div class="doc-card dev-docs">
    <div class="card-icon">🏗️</div>
    <h3><a href="../developer/index.md">开发者指南</a></h3>
    <p>详细的技术文档，包含架构设计、开发环境配置、构建部署等开发相关信息。</p>
    <div class="card-tags">
      <span class="tag">技术架构</span>
      <span class="tag">开发环境</span>
      <span class="tag">构建部署</span>
    </div>
    <div class="card-actions">
      <a href="../developer/index.md" class="btn primary">开发文档</a>
      <a href="#development-setup" class="btn secondary">环境配置</a>
    </div>
  </div>

  <div class="doc-card dev-docs">
    <div class="card-icon">🔌</div>
    <h3><a href="../developer/api.md">API 参考</a></h3>
    <p>完整的API接口文档，包含所有服务的接口定义、参数说明和使用示例。</p>
    <div class="card-tags">
      <span class="tag">接口文档</span>
      <span class="tag">数据格式</span>
      <span class="tag">代码示例</span>
    </div>
    <div class="card-actions">
      <a href="../developer/api.md" class="btn primary">API文档</a>
      <a href="#api-examples" class="btn secondary">代码示例</a>
    </div>
  </div>
</div>

---

## 🚀 快速导航

### 新用户入门 {#user-quick-start}
1. **[系统要求检查](../user-manual/index.md#系统要求)** - 确认您的设备符合运行要求
2. **[下载安装指南](../user-manual/index.md#首次使用)** - 获取并安装应用程序
3. **[摄像头设置](../user-manual/index.md#摄像头设置)** - 配置摄像头获得最佳体验
4. **[开始第一次种植](../user-manual/index.md#开始种植)** - 体验核心游戏功能
5. **[了解作物系统](../user-manual/index.md#作物系统)** - 掌握四种作物的特点

### 常见问题快速解决 {#troubleshooting}
- **[摄像头无法启动](../user-manual/faq.md#摄像头无法启动怎么办)** ➜ 权限和驱动问题
- **[检测不到动作](../user-manual/faq.md#为什么检测不到我的动作)** ➜ 位置和光线调整
- **[作物不生长](../user-manual/faq.md#作物为什么不生长了)** ➜ 专注度和设置检查
- **[应用运行缓慢](../user-manual/faq.md#应用运行缓慢怎么办)** ➜ 性能优化建议
- **[数据同步问题](../user-manual/faq.md#同步失败怎么办)** ➜ 网络和账户检查

### 开发者快速入门 {#development-setup}
1. **[环境要求](../developer/index.md#系统要求)** - Node.js、Git等工具安装
2. **[项目克隆](../developer/index.md#克隆项目)** - 获取源代码
3. **[依赖安装](../developer/index.md#安装依赖)** - npm install
4. **[开发服务器](../developer/index.md#启动开发服务器)** - 启动开发环境
5. **[构建部署](../developer/index.md#构建项目)** - 打包发布

### API使用示例 {#api-examples}
- **[摄像头服务](../developer/api.md#cameraservice-api)** ➜ 设备管理和权限处理
- **[检测服务](../developer/api.md#detectionservice-api)** ➜ 姿态检测和专注度计算
- **[数据管理](../developer/api.md#databasemanager-api)** ➜ 用户数据CRUD操作
- **[游戏引擎](../developer/api.md#gamemanager-api)** ➜ 农场和作物管理
- **[IPC通信](../developer/api.md#electron-ipc-api)** ➜ 主进程渲染进程通信

---

## 🏷️ 按主题浏览

### 功能主题
<div class="topic-grid">
  <div class="topic-item">
    <h4>📸 摄像头功能</h4>
    <ul>
      <li><a href="../user-manual/index.md#摄像头设置">摄像头配置</a></li>
      <li><a href="../user-manual/faq.md#摄像头相关">常见问题</a></li>
      <li><a href="../developer/api.md#cameraservice-api">API接口</a></li>
    </ul>
  </div>

  <div class="topic-item">
    <h4>🌱 游戏系统</h4>
    <ul>
      <li><a href="../user-manual/index.md#核心功能">作物系统</a></li>
      <li><a href="../user-manual/index.md#农场管理">农场管理</a></li>
      <li><a href="../developer/api.md#游戏引擎api">游戏API</a></li>
    </ul>
  </div>

  <div class="topic-item">
    <h4>⚙️ 设置配置</h4>
    <ul>
      <li><a href="../user-manual/index.md#设置和个性化">个性化设置</a></li>
      <li><a href="../user-manual/faq.md#设置和配置">配置问题</a></li>
      <li><a href="../developer/api.md#settingsservice-api">设置API</a></li>
    </ul>
  </div>

  <div class="topic-item">
    <h4>💾 数据管理</h4>
    <ul>
      <li><a href="../user-manual/index.md#数据和隐私">数据备份</a></li>
      <li><a href="../user-manual/faq.md#数据同步">同步问题</a></li>
      <li><a href="../developer/api.md#数据存储api">存储API</a></li>
    </ul>
  </div>

  <div class="topic-item">
    <h4>🔧 故障排除</h4>
    <ul>
      <li><a href="../user-manual/index.md#故障排除">常见故障</a></li>
      <li><a href="../user-manual/faq.md#技术问题">技术问题</a></li>
      <li><a href="../developer/index.md#故障排查">开发调试</a></li>
    </ul>
  </div>

  <div class="topic-item">
    <h4>🛡️ 隐私安全</h4>
    <ul>
      <li><a href="../user-manual/faq.md#隐私和安全">隐私保护</a></li>
      <li><a href="../developer/api.md#认证和安全">安全机制</a></li>
      <li><a href="#privacy-policy">隐私政策</a></li>
    </ul>
  </div>
</div>

---

## 📞 获取支持

### 在线帮助
- **搜索帮助文档** - 使用页面顶部的搜索功能
- **浏览分类内容** - 按照功能主题查找相关信息
- **查看视频教程** - [官方教程频道](https://www.selfdisciplinefarm.com/tutorials)

### 联系支持团队
<div class="support-channels">
  <div class="support-card">
    <div class="support-icon">📧</div>
    <h4>邮件支持</h4>
    <p><EMAIL></p>
    <small>24小时内回复</small>
  </div>

  <div class="support-card">
    <div class="support-icon">💬</div>
    <h4>在线客服</h4>
    <p>工作日 9:00-18:00</p>
    <small>即时响应</small>
  </div>

  <div class="support-card">
    <div class="support-icon">🌐</div>
    <h4>社区论坛</h4>
    <p>community.selfdisciplinefarm.com</p>
    <small>用户互助</small>
  </div>
</div>

### 反馈和建议
- **应用内反馈** - 设置 → 帮助与反馈 → 问题反馈
- **GitHub Issues** - [提交技术问题](https://github.com/selfdisciplinefarm/issues)
- **功能建议** - [建议新功能](https://github.com/selfdisciplinefarm/discussions)

---

## 🔄 文档更新日志

| 版本 | 更新日期 | 更新内容 |
|------|----------|----------|
| 1.0.0 | 2024-06-24 | 初版发布，包含完整的用户手册、FAQ、开发者文档和API参考 |
| - | - | 后续更新将在此记录 |

---

## 🎯 改进帮助中心

我们持续改进帮助中心的内容和体验。如果您有任何建议，请通过以下方式告诉我们：

- **内容改进** - 指出不清楚或错误的内容
- **结构优化** - 建议更好的信息组织方式
- **功能需求** - 希望添加的新功能或工具
- **用户体验** - 使用过程中的困难和建议

**联系邮箱**: <EMAIL>

---

<style>
.search-container {
  max-width: 600px;
  margin: 20px auto;
  position: relative;
}

.search-input {
  width: 100%;
  padding: 12px 20px;
  font-size: 16px;
  border: 2px solid #ddd;
  border-radius: 25px;
  outline: none;
  transition: border-color 0.3s;
}

.search-input:focus {
  border-color: #4CAF50;
}

.doc-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin: 20px 0;
}

.doc-card {
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 20px;
  background: #fff;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  transition: transform 0.2s, box-shadow 0.2s;
}

.doc-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.card-icon {
  font-size: 48px;
  text-align: center;
  margin-bottom: 15px;
}

.card-tags {
  margin: 15px 0;
}

.tag {
  display: inline-block;
  background: #f0f0f0;
  color: #666;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  margin: 2px;
}

.card-actions {
  margin-top: 15px;
}

.btn {
  display: inline-block;
  padding: 8px 16px;
  margin: 4px;
  text-decoration: none;
  border-radius: 4px;
  font-size: 14px;
  transition: background-color 0.2s;
}

.btn.primary {
  background: #4CAF50;
  color: white;
}

.btn.secondary {
  background: #f0f0f0;
  color: #333;
}

.topic-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin: 20px 0;
}

.topic-item {
  background: #f9f9f9;
  padding: 15px;
  border-radius: 6px;
  border-left: 4px solid #4CAF50;
}

.support-channels {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin: 20px 0;
}

.support-card {
  text-align: center;
  padding: 20px;
  background: #f5f5f5;
  border-radius: 8px;
}

.support-icon {
  font-size: 36px;
  margin-bottom: 10px;
}
</style>

*帮助中心最后更新：2024年6月24日* 