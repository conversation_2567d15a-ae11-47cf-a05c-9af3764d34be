
# 代码签名证书配置指南

## Windows 证书配置

### 获取证书
1. 从证书颁发机构购买代码签名证书（如 DigiCert、Sectigo、Comodo）
2. 下载 .p12 或 .pfx 格式的证书文件
3. 记录证书密码

### 环境变量配置
```bash
# Windows证书文件路径（绝对路径）
WIN_CSC_LINK=/path/to/certificate.p12

# Windows证书密码
WIN_CSC_KEY_PASSWORD=your_certificate_password

# Windows证书主题名称（可选）
WIN_CSC_NAME="Your Company Name"

# 时间戳服务器（可选）
WIN_TIMESTAMP_SERVER=http://timestamp.digicert.com
```

## macOS 证书配置

### 获取证书
1. 注册 Apple Developer 账号
2. 在 Keychain Access 中申请 Developer ID Application 证书
3. 或使用 Xcode 自动管理证书

### 环境变量配置
```bash
# macOS证书身份名称
MAC_CSC_NAME="Developer ID Application: Your Name (TEAM_ID)"

# Apple Team ID（用于公证）
APPLE_TEAM_ID=YOUR_TEAM_ID

# Apple ID（用于公证）
APPLE_ID=<EMAIL>

# App专用密码（用于公证）
APPLE_APP_PASSWORD=your_app_specific_password
```

### 公证配置
1. 登录 https://appleid.apple.com/
2. 生成 App专用密码
3. 配置 APPLE_APP_PASSWORD 环境变量

## 开发环境

开发环境会自动生成自签名证书，无需配置生产证书。

## 证书安全建议

1. 不要将证书文件提交到版本控制系统
2. 使用环境变量或安全的密钥管理服务存储证书信息
3. 定期更新证书
4. 限制证书文件的访问权限
5. 在CI/CD环境中使用加密的环境变量

## 故障排除

### Windows
- 确保 Windows SDK 已安装（包含 signtool）
- 检查证书文件路径和密码
- 验证证书是否在有效期内

### macOS
- 确保 Xcode Command Line Tools 已安装
- 检查证书是否在 Keychain 中
- 验证 Apple Developer 账号状态
