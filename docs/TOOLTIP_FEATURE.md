# 道具工具提示功能说明

## 功能概述

为期货游戏系统中的道具背包添加了鼠标悬停显示详细信息的工具提示功能。当用户将鼠标悬停在道具上时，会显示包含产量信息在内的详细属性。

## 组件结构

### 1. Tooltip.tsx
通用的工具提示组件，适用于 `GameItem` 类型的道具。

**特性：**
- 鼠标跟随定位
- 防止超出视窗边界
- 根据道具品质显示不同的颜色主题
- 支持淡入动画

**显示内容：**
- 基础信息（类型、价值、可堆叠、可交易）
- 农业产品：日产量范围、当前倍率、期货代码、期货价格
- 工业产品：效率、耐久度、容量、工业类型
- 装备道具：专注加成、生产加成、品质加成、持续时间

### 2. InventoryTooltip.tsx
专门用于库存系统的工具提示组件，适用于 `InventoryItem` 类型。

**特性：**
- 适配库存系统的数据结构
- 显示获得时间和数量信息
- 根据品质等级调整显示内容

### 3. FuturesProductTooltip.tsx ⭐ 新增
专门用于期货系统的工具提示组件，适用于期货产品数据结构。

**特性：**
- 适配期货产品的数据结构
- 简洁显示核心信息：产量、类型、品质
- 高亮突出产量信息
- 紧凑的布局设计

**显示内容：**
- 品质等级：头部彩色标签显示
- 品种类型：谷物类、油料作物、水果类等
- 产量信息：精确的产量范围和单位（重点高亮）

## 使用方法

### 在 ItemCard 组件中
```tsx
// 自动启用工具提示（默认行为）
<ItemCard item={item} showTooltip={true} />

// 禁用工具提示
<ItemCard item={item} showTooltip={false} />
```

### 直接使用 Tooltip 组件
```tsx
<Tooltip item={gameItem}>
  <div>任何要添加工具提示的内容</div>
</Tooltip>
```

### 在库存面板中
库存面板自动为每个物品格子包装了 `InventoryTooltip` 组件。

### 在期货系统中 ⭐ 新增
期货背包自动为每个期货产品包装了 `FuturesProductTooltip` 组件。

## 显示的产量信息

### 农业产品（通用系统）
- **日产量范围**：显示最小和最大日产量
- **当前倍率**：生产效率百分比
- **期货代码**：对应的期货品种代码
- **期货价格**：当前期货价格（如果有）

### 期货农产品（期货系统）⭐ 新增
- **品种类型**：显示所属分类（谷物类、油料作物、水果类、畜牧类等）
- **精确产量范围**：根据品质等级显示具体产量范围
- **产量单位**：根据品种类型显示正确单位（公斤/亩、公斤/头等）
- **高亮显示**：产量信息使用特殊背景突出显示

#### 期货产量示例：
- **玉米**：400-1200公斤/亩（根据品质：普通→神话）
- **大豆**：120-350公斤/亩
- **生猪**：90-230公斤/头
- **苹果**：2000-6000公斤/亩

### 工业产品
- **效率百分比**：工业设备的工作效率
- **耐久度**：设备剩余使用寿命
- **容量**：处理能力
- **工业类型**：设备分类

### 装备道具
- **专注加成**：提升专注度的百分比
- **生产加成**：提升生产效率的百分比
- **品质加成**：提升产品品质的百分比
- **持续时间**：装备效果持续小时数

## 视觉特性

### 品质相关颜色
- 工具提示边框颜色与道具品质对应
- 高亮显示重要信息（如产量）
- 品质标签使用对应的背景色

### 产量指示器
- 农业产品在卡片右上角显示 📈 图标
- 期货产品的产量指示器带有呼吸光效 ⭐ 新增
- 表示该道具具有产量属性

### 简洁设计 ⭐ 更新
- **紧凑布局**：工具提示尺寸从320-380px减少到250-300px
- **重点突出**：产量信息使用高亮背景和彩色边框
- **信息精简**：只显示最核心的产量、类型、品质信息

### 动画效果
- 工具提示出现时有淡入和缩放动画
- 持续时间 0.2 秒，使用 ease-out 缓动
- 期货产量指示器有呼吸光效 ⭐ 新增

## 应用范围

### ✅ 已支持的系统
1. **通用道具系统**：GameDemo、ItemCard 组件
2. **库存系统**：InventoryPanel 组件
3. **统一背包系统**：UnifiedInventoryPanel 组件
4. **期货系统**：ChineseFuturesInventory 组件 ⭐ 新增

### 📍 支持的场景
- 道具背包查看
- 合成工作台
- 盲盒开启结果
- 期货农产品背包 ⭐ 新增

## 响应式设计

- 工具提示会自动检测屏幕边界
- 当接近边界时会调整显示位置
- 确保工具提示完全可见

## 性能优化

- 只在鼠标悬停时创建工具提示
- 鼠标离开时立即销毁
- 使用事件监听器优化位置更新

## 可扩展性

工具提示组件设计为易于扩展：
- 可以轻松添加新的道具类型支持
- 样式通过 CSS-in-JS 实现，易于定制
- 组件接口简洁，易于在其他地方复用
- 期货系统展示了如何为特定数据结构创建专用组件 ⭐ 新增

## 注意事项

1. 确保传入的道具对象包含必要的属性
2. 工具提示具有高 z-index (99999)，确保显示在最上层
3. 鼠标事件不会阻止原有的点击等交互功能
4. 期货系统使用专门的数据映射和类型安全处理 ⭐ 新增

## 修复记录 ⭐ 新增

### 2024年问题修复：期货系统工具提示缺失
- **问题**：期货系统的物品背包中鼠标悬停没有显示产量
- **原因**：ChineseFuturesInventory 组件没有集成工具提示功能
- **解决**：创建 FuturesProductTooltip 专用组件并集成
- **文档**：详见 [docs/FUTURES_TOOLTIP_FIX.md](./FUTURES_TOOLTIP_FIX.md)

### 2024年优化：期货系统工具提示简化
- **反馈**：用户希望工具提示只显示产量、类型、品质等级
- **优化**：简化 FuturesProductTooltip 组件，移除冗余信息
- **改进**：尺寸更紧凑，信息更聚焦，产量高亮显示
- **文档**：详见 [docs/TOOLTIP_SIMPLIFIED.md](./TOOLTIP_SIMPLIFIED.md) 