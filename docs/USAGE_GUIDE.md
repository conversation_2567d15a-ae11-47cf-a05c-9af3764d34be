# 解耦化道具系统使用指南

## 概述

本指南展示如何使用解耦化的道具系统架构进行开发。该架构提供了清晰的分层结构、依赖注入、事件驱动和配置管理等功能。

## 快速开始

### 1. 基础设置

```typescript
import { container } from '../src/core/infrastructure/DIContainer'
import { eventBus } from '../src/core/infrastructure/EventBus'
import { configManager } from '../src/core/infrastructure/ConfigManager'

// 注册基础服务
container.registerSingleton('IEventBus', () => eventBus)
container.registerSingleton('IConfigManager', () => configManager)
```

### 2. 创建道具实体

```typescript
import { Item } from '../src/core/domain/Item'
import { ItemId, ItemLocation } from '../src/core/abstractions/IItem'
import { Quality, ItemCategory } from '../src/types/enhanced-items'

// 创建一个新道具
const itemId = new ItemId('corn_001')
const cornItem = new Item(
  itemId,
  {
    name: '玉米',
    description: '基础农业期货品种',
    icon: '🌽',
    category: ItemCategory.AGRICULTURAL,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    quality: Quality.COMMON,
    baseValue: 100,
    stackable: true,
    tradeable: true,
    attributes: {
      yieldMultiplier: 1.0,
      futuresPrice: 2800
    }
  },
  {
    isEquipped: false,
    quantity: 10,
    location: ItemLocation.INVENTORY
  }
)

console.log('道具信息:', cornItem.getFullInfo())
```

### 3. 使用依赖注入

```typescript
// 注册服务
class ItemService {
  static dependencies = ['IItemRepository', 'IEventBus']

  constructor(
    private repository: IItemRepository,
    private eventBus: IEventBus
  ) {}

  async createItem(config: ItemConfig): Promise<OperationResult<IItem>> {
    try {
      const item = ItemFactory.createFromConfig(config)
      await this.repository.save(item)
      
      // 发布事件
      await this.eventBus.publish({
        eventId: generateEventId(),
        eventType: 'ItemCreated',
        timestamp: new Date(),
        aggregateId: item.id.value,
        eventData: {
          itemId: item.id.value,
          itemType: item.metadata.category,
          quality: item.properties.quality,
          template: 'corn',
          source: 'creation'
        }
      })

      return { success: true, data: item }
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      }
    }
  }
}

// 注册服务
container.registerSingleton('IItemService', ItemService)

// 使用服务
const itemService = container.resolve<ItemService>('IItemService')
```

### 4. 事件处理

```typescript
import { BaseEventHandler } from '../src/core/infrastructure/EventBus'
import { ItemCreatedEvent } from '../src/core/abstractions/IEvents'

class ItemCreatedHandler extends BaseEventHandler<ItemCreatedEvent> {
  protected readonly supportedEventTypes = ['ItemCreated']

  async handle(event: ItemCreatedEvent): Promise<void> {
    console.log(`新道具已创建: ${event.eventData.itemId}`)
    
    // 更新统计信息
    await this.updateItemStatistics(event.eventData)
    
    // 检查成就解锁
    await this.checkAchievements(event.eventData)
  }

  private async updateItemStatistics(data: any): Promise<void> {
    // 实现统计更新逻辑
  }

  private async checkAchievements(data: any): Promise<void> {
    // 实现成就检查逻辑
  }
}

// 注册事件处理器
const handler = new ItemCreatedHandler()
eventBus.subscribe('ItemCreated', handler)
```

### 5. 配置管理

```typescript
// 读取配置
const baseSuccessRate = configManager.get<number>('synthesis.baseSuccessRate')
const maxSlots = configManager.get<number>('inventory.maxSlots')

// 更新配置
configManager.set('synthesis.baseSuccessRate', 0.85)

// 订阅配置变更
const unsubscribe = configManager.subscribe('synthesis.baseSuccessRate', (newValue) => {
  console.log('合成成功率已更新为:', newValue)
})

// 取消订阅
// unsubscribe()
```

## 高级用法

### 1. 自定义仓库实现

```typescript
import { IItemRepository } from '../src/core/abstractions/IRepository'

class LocalStorageItemRepository implements IItemRepository {
  private readonly storageKey = 'items'

  async findById(id: ItemId): Promise<IItem | null> {
    const items = this.getAllItems()
    return items.find(item => item.id.equals(id)) || null
  }

  async save(item: IItem): Promise<void> {
    const items = this.getAllItems()
    const existingIndex = items.findIndex(i => i.id.equals(item.id))
    
    if (existingIndex >= 0) {
      items[existingIndex] = item
    } else {
      items.push(item)
    }
    
    this.saveAllItems(items)
  }

  async delete(id: ItemId): Promise<void> {
    const items = this.getAllItems()
    const filteredItems = items.filter(item => !item.id.equals(id))
    this.saveAllItems(filteredItems)
  }

  async findAll(): Promise<IItem[]> {
    return this.getAllItems()
  }

  async exists(id: ItemId): Promise<boolean> {
    const item = await this.findById(id)
    return item !== null
  }

  // ... 实现其他必需的方法

  private getAllItems(): IItem[] {
    const data = localStorage.getItem(this.storageKey)
    if (!data) return []
    
    const itemsData = JSON.parse(data)
    return itemsData.map((data: any) => Item.deserialize(data))
  }

  private saveAllItems(items: IItem[]): void {
    const serializedItems = items.map(item => item.serialize())
    localStorage.setItem(this.storageKey, JSON.stringify(serializedItems))
  }
}

// 注册自定义仓库
container.registerSingleton('IItemRepository', LocalStorageItemRepository)
```

### 2. 自定义事件处理器

```typescript
class SynthesisEventHandler extends BaseEventHandler<SynthesisCompletedEvent> {
  protected readonly supportedEventTypes = ['SynthesisCompleted']

  async handle(event: SynthesisCompletedEvent): Promise<void> {
    const { result, materials } = event.eventData

    if (result.success) {
      // 记录成功合成
      await this.recordSuccessfulSynthesis(result, materials)
      
      // 更新用户经验
      await this.updateUserExperience(event.userId, result.experience || 0)
      
      // 发布成就相关事件
      if (result.resultItem && this.isRareItem(result.resultItem)) {
        await eventBus.publish({
          eventId: generateEventId(),
          eventType: 'AchievementUnlocked',
          timestamp: new Date(),
          aggregateId: event.userId || 'system',
          eventData: {
            achievementId: 'rare_synthesis',
            achievementName: '稀有合成师',
            category: 'synthesis',
            rewards: {
              currency: [{ type: 'focus_coin', amount: 500 }]
            }
          }
        })
      }
    } else {
      // 记录失败合成
      await this.recordFailedSynthesis(materials)
    }
  }

  private async recordSuccessfulSynthesis(result: any, materials: any[]): Promise<void> {
    // 实现成功记录逻辑
  }

  private async updateUserExperience(userId: string | undefined, experience: number): Promise<void> {
    // 实现经验更新逻辑
  }

  private isRareItem(item: IItem): boolean {
    return [Quality.RARE, Quality.EPIC, Quality.LEGENDARY].includes(item.properties.quality)
  }

  private async recordFailedSynthesis(materials: any[]): Promise<void> {
    // 实现失败记录逻辑
  }
}
```

### 3. 作用域管理

```typescript
// 创建请求作用域
const requestScope = container.createScope()

// 在作用域内执行业务逻辑
await container.executeInScope(requestScope, async () => {
  const itemService = container.resolve<IItemService>('IItemService')
  const synthesisService = container.resolve<ISynthesisService>('ISynthesisService')
  
  // 执行业务操作
  const item1 = await itemService.createItem(config1)
  const item2 = await itemService.createItem(config2)
  
  if (item1.success && item2.success) {
    const synthesisResult = await synthesisService.synthesize(recipe, [item1.data!, item2.data!])
    // 处理合成结果
  }
})

// 作用域自动清理
requestScope.dispose()
```

### 4. 中间件模式

```typescript
import { MiddlewarePipeline, IMiddleware } from '../src/core/infrastructure/DIContainer'

// 日志中间件
class LoggingMiddleware implements IMiddleware<any> {
  async execute(context: any, next: () => Promise<any>): Promise<any> {
    console.log(`开始执行: ${context.action}`)
    const startTime = Date.now()
    
    try {
      const result = await next()
      const duration = Date.now() - startTime
      console.log(`执行完成: ${context.action} (${duration}ms)`)
      return result
    } catch (error) {
      console.error(`执行失败: ${context.action}`, error)
      throw error
    }
  }
}

// 验证中间件
class ValidationMiddleware implements IMiddleware<any> {
  async execute(context: any, next: () => Promise<any>): Promise<any> {
    if (!this.validateContext(context)) {
      throw new Error('上下文验证失败')
    }
    return await next()
  }

  private validateContext(context: any): boolean {
    // 实现验证逻辑
    return true
  }
}

// 使用中间件管道
const pipeline = new MiddlewarePipeline()
pipeline.use(new LoggingMiddleware())
pipeline.use(new ValidationMiddleware())

const result = await pipeline.execute({
  action: 'createItem',
  data: itemConfig
})
```

## 测试策略

### 1. 单元测试

```typescript
import { Item } from '../src/core/domain/Item'
import { Quality, ItemCategory } from '../src/types/enhanced-items'

describe('Item Entity', () => {
  let item: Item

  beforeEach(() => {
    item = new Item(/* ... 测试数据 ... */)
  })

  it('应该正确计算品质加成', () => {
    const bonus = item.getQualityBonus()
    expect(bonus).toBe(0.03) // 普通品质 3% 加成
  })

  it('应该能与相同道具堆叠', () => {
    const otherItem = new Item(/* 相同配置 */)
    expect(item.canStackWith(otherItem)).toBe(true)
  })

  it('应该能正确序列化和反序列化', () => {
    const serialized = item.serialize()
    const deserialized = Item.deserialize(serialized)
    expect(deserialized.id.equals(item.id)).toBe(true)
  })
})
```

### 2. 集成测试

```typescript
describe('Item Service Integration', () => {
  let container: DIContainer
  let eventBus: EventBus
  let itemService: IItemService

  beforeEach(async () => {
    container = new DIContainer()
    eventBus = new EventBus()
    
    // 注册测试用的服务
    container.registerSingleton('IEventBus', () => eventBus)
    container.registerSingleton('IItemRepository', MemoryItemRepository)
    container.registerSingleton('IItemService', ItemService)
    
    itemService = container.resolve<IItemService>('IItemService')
  })

  it('应该能创建道具并发布事件', async () => {
    const events: IDomainEvent[] = []
    eventBus.subscribe('ItemCreated', {
      canHandle: () => true,
      handle: async (event) => events.push(event)
    })

    const result = await itemService.createItem(testItemConfig)

    expect(result.success).toBe(true)
    expect(events).toHaveLength(1)
    expect(events[0].eventType).toBe('ItemCreated')
  })
})
```

## 性能优化

### 1. 延迟加载

```typescript
class LazyItemFactory implements IItemFactory {
  private templates: Map<string, ItemTemplate> = new Map()

  createItem(template: ItemTemplate, quality: Quality): IItem {
    // 延迟创建复杂属性
    return new Proxy(new Item(/* 基础配置 */), {
      get(target, prop) {
        if (prop === 'complexAttributes' && !target.hasComplexAttributes) {
          target.initializeComplexAttributes()
        }
        return target[prop]
      }
    })
  }
}
```

### 2. 缓存策略

```typescript
class CachedItemRepository implements IItemRepository {
  private cache = new Map<string, IItem>()
  private cacheTTL = 5 * 60 * 1000 // 5分钟

  async findById(id: ItemId): Promise<IItem | null> {
    const cacheKey = id.value
    const cached = this.cache.get(cacheKey)
    
    if (cached && this.isCacheValid(cached)) {
      return cached
    }

    const item = await this.actualRepository.findById(id)
    if (item) {
      this.cache.set(cacheKey, item)
    }
    
    return item
  }

  private isCacheValid(item: any): boolean {
    const now = Date.now()
    return (now - item._cacheTime) < this.cacheTTL
  }
}
```

## 最佳实践

### 1. 错误处理

```typescript
class ItemServiceWithErrorHandling implements IItemService {
  async createItem(config: ItemConfig): Promise<OperationResult<IItem>> {
    try {
      // 验证输入
      this.validateConfig(config)
      
      // 执行业务逻辑
      const item = await this.doCreateItem(config)
      
      return { success: true, data: item }
    } catch (error) {
      if (error instanceof ValidationError) {
        return { 
          success: false, 
          error: '配置验证失败',
          warnings: [error.message]
        }
      }
      
      if (error instanceof BusinessError) {
        return { success: false, error: error.message }
      }
      
      // 记录未知错误
      console.error('Unexpected error in createItem:', error)
      return { success: false, error: '系统内部错误' }
    }
  }
}
```

### 2. 监控和日志

```typescript
class MonitoredEventBus extends EventBus {
  async publish(event: IDomainEvent): Promise<void> {
    const startTime = Date.now()
    
    try {
      await super.publish(event)
      
      // 记录成功指标
      this.recordMetric('event.published', Date.now() - startTime, {
        eventType: event.eventType,
        success: true
      })
    } catch (error) {
      // 记录失败指标
      this.recordMetric('event.published', Date.now() - startTime, {
        eventType: event.eventType,
        success: false,
        error: error.message
      })
      throw error
    }
  }

  private recordMetric(name: string, duration: number, tags: Record<string, any>): void {
    // 发送到监控系统
    console.log(`Metric: ${name}`, { duration, ...tags })
  }
}
```

## 总结

这个解耦化的道具系统架构提供了：

1. **清晰的分层结构** - 分离关注点，易于维护
2. **依赖注入** - 提高可测试性和灵活性  
3. **事件驱动** - 实现松耦合的系统集成
4. **配置管理** - 灵活的运行时配置
5. **可扩展性** - 支持插件和中间件
6. **类型安全** - TypeScript 提供编译时检查

通过遵循这些模式和最佳实践，可以构建出高质量、易维护、可扩展的游戏系统。 