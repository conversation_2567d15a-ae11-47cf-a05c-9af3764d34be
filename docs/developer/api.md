# 🔌 自律农场 API 参考文档

本文档详细说明自律农场应用中各种API接口、数据格式和使用方法。

## 📋 目录

1. [服务层API](#服务层api)
2. [Electron IPC API](#electron-ipc-api)
3. [数据存储API](#数据存储api)
4. [MediaPipe集成API](#mediapipe集成api)
5. [游戏引擎API](#游戏引擎api)
6. [数据格式定义](#数据格式定义)

---

## 🔧 服务层API

### CameraService API

#### `initializeCamera()`
初始化摄像头服务
```typescript
async initializeCamera(): Promise<void>
```

**返回值**: Promise\<void\>
**异常**: CameraInitializationError

#### `getAvailableDevices()`
获取可用的摄像头设备列表
```typescript
async getAvailableDevices(): Promise<MediaDeviceInfo[]>
```

**返回值**: 摄像头设备信息数组

#### `switchCamera(deviceId: string)`
切换到指定的摄像头设备
```typescript
async switchCamera(deviceId: string): Promise<void>
```

**参数**:
- `deviceId`: 目标摄像头设备ID

### DetectionService API

#### `startPoseDetection(videoElement: HTMLVideoElement)`
开始姿态检测
```typescript
startPoseDetection(videoElement: HTMLVideoElement): void
```

**参数**:
- `videoElement`: HTML视频元素

#### `calculateFocusScore(landmarks: NormalizedLandmark[])`
计算专注度分数
```typescript
calculateFocusScore(landmarks: NormalizedLandmark[]): number
```

**参数**:
- `landmarks`: MediaPipe姿态关键点数据

**返回值**: 0-100的专注度分数

### SettingsService API

#### `loadSettings()`
加载应用设置
```typescript
loadSettings(): Settings
```

**返回值**: 完整的设置对象

#### `saveSettings(settings: Partial<Settings>)`
保存设置
```typescript
saveSettings(settings: Partial<Settings>): void
```

**参数**:
- `settings`: 部分设置对象

---

## ⚡ Electron IPC API

### 主进程 → 渲染进程

#### `camera-permission-result`
摄像头权限请求结果
```typescript
interface CameraPermissionResult {
  granted: boolean;
  error?: string;
}
```

#### `window-state-changed`
窗口状态变化通知
```typescript
interface WindowStateData {
  isMaximized: boolean;
  isMinimized: boolean;
  bounds: Electron.Rectangle;
}
```

### 渲染进程 → 主进程

#### `request-camera-permission`
请求摄像头权限
```typescript
ipcRenderer.invoke('request-camera-permission'): Promise<boolean>
```

#### `minimize-window`
最小化窗口
```typescript
ipcRenderer.send('minimize-window'): void
```

#### `toggle-fullscreen`
切换全屏模式
```typescript
ipcRenderer.send('toggle-fullscreen'): void
```

---

## 💾 数据存储API

### DatabaseManager API

#### `saveUserData(data: UserData)`
保存用户数据
```typescript
async saveUserData(data: UserData): Promise<void>
```

**参数**:
- `data`: 用户数据对象

#### `loadUserData()`
加载用户数据
```typescript
async loadUserData(): Promise<UserData | null>
```

**返回值**: 用户数据或null

#### `exportData()`
导出所有数据
```typescript
async exportData(): Promise<Blob>
```

**返回值**: 包含所有数据的Blob对象

#### `importData(data: Blob)`
导入数据
```typescript
async importData(data: Blob): Promise<void>
```

**参数**:
- `data`: 要导入的数据Blob

---

## 📸 MediaPipe集成API

### Pose检测API

#### 配置选项
```typescript
interface PoseConfig {
  modelComplexity: 0 | 1 | 2;
  smoothLandmarks: boolean;
  enableSegmentation: boolean;
  minDetectionConfidence: number;
  minTrackingConfidence: number;
}
```

#### 检测结果
```typescript
interface PoseResults {
  poseLandmarks: NormalizedLandmark[];
  poseWorldLandmarks: Landmark[];
  segmentationMask?: ImageData;
}
```

#### 关键点索引
```typescript
enum PoseLandmark {
  NOSE = 0,
  LEFT_EYE_INNER = 1,
  LEFT_EYE = 2,
  LEFT_EYE_OUTER = 3,
  RIGHT_EYE_INNER = 4,
  RIGHT_EYE = 5,
  RIGHT_EYE_OUTER = 6,
  LEFT_EAR = 7,
  RIGHT_EAR = 8,
  // ... 更多关键点
}
```

---

## 🎮 游戏引擎API

### GameManager API

#### `initializeGame()`
初始化游戏
```typescript
initializeGame(): void
```

#### `plantCrop(position: GridPosition, cropType: CropType)`
种植作物
```typescript
plantCrop(position: GridPosition, cropType: CropType): void
```

**参数**:
- `position`: 网格位置
- `cropType`: 作物类型

#### `harvestCrop(position: GridPosition)`
收获作物
```typescript
harvestCrop(position: GridPosition): HarvestResult
```

**参数**:
- `position`: 网格位置

**返回值**: 收获结果对象

---

## 📊 数据格式定义

### 用户数据格式
```typescript
interface UserData {
  id: string;
  profile: UserProfile;
  gameProgress: GameProgress;
  statistics: UserStatistics;
  settings: UserSettings;
  achievements: Achievement[];
  createdAt: Date;
  updatedAt: Date;
}
```

### 游戏进度格式
```typescript
interface GameProgress {
  level: number;
  experience: number;
  farmLayout: FarmTile[][];
  inventory: InventoryItem[];
  unlockedFeatures: string[];
}
```

### 专注度数据格式
```typescript
interface FocusData {
  timestamp: Date;
  score: number;
  landmarks: NormalizedLandmark[];
  sessionId: string;
  duration: number;
}
```

### 设置数据格式
```typescript
interface Settings {
  general: GeneralSettings;
  camera: CameraSettings;
  detection: DetectionSettings;
  audio: AudioSettings;
  notifications: NotificationSettings;
  privacy: PrivacySettings;
}
```

---

## 🔐 认证和安全

### 数据加密
所有敏感数据使用AES-256加密存储：

```typescript
interface EncryptedData {
  data: string;          // Base64编码的加密数据
  iv: string;           // 初始化向量
  salt: string;         // 盐值
  algorithm: 'aes-256-gcm';
}
```

### 权限验证
```typescript
interface PermissionCheck {
  camera: boolean;
  microphone?: boolean;
  notifications: boolean;
  fileSystem: boolean;
}
```

---

## 📡 事件系统

### 游戏事件
```typescript
enum GameEvent {
  CROP_PLANTED = 'crop_planted',
  CROP_HARVESTED = 'crop_harvested',
  LEVEL_UP = 'level_up',
  ACHIEVEMENT_UNLOCKED = 'achievement_unlocked'
}
```

### 检测事件
```typescript
enum DetectionEvent {
  FOCUS_CHANGED = 'focus_changed',
  POSE_DETECTED = 'pose_detected',
  DISTRACTION_DETECTED = 'distraction_detected'
}
```

---

## 🌐 网络API（计划中）

### 同步API
```typescript
interface SyncAPI {
  uploadUserData(data: UserData): Promise<SyncResult>;
  downloadUserData(userId: string): Promise<UserData>;
  resolveConflicts(conflicts: DataConflict[]): Promise<UserData>;
}
```

### 社交API（计划中）
```typescript
interface SocialAPI {
  getFriends(): Promise<Friend[]>;
  shareAchievement(achievementId: string): Promise<void>;
  sendFarmVisit(friendId: string): Promise<void>;
}
```

---

## 📝 使用示例

### 基本使用流程
```typescript
// 1. 初始化服务
const cameraService = new CameraService();
const detectionService = new DetectionService();
const gameManager = new GameManager();

// 2. 启动摄像头
await cameraService.initializeCamera();

// 3. 开始检测
detectionService.startPoseDetection(videoElement);

// 4. 监听检测结果
detectionService.on('pose_detected', (results) => {
  const focusScore = detectionService.calculateFocusScore(results.landmarks);
  gameManager.updateFocusLevel(focusScore);
});

// 5. 种植作物
gameManager.plantCrop({ x: 2, y: 1 }, CropType.KNOWLEDGE_FLOWER);
```

### 数据保存示例
```typescript
// 保存用户数据
const userData: UserData = {
  id: 'user-123',
  profile: { name: '用户名', avatar: 'avatar.png' },
  gameProgress: { level: 5, experience: 1250 },
  // ... 其他数据
};

await databaseManager.saveUserData(userData);
```

---

*最后更新：2024年6月*
*API版本：1.0.0* 