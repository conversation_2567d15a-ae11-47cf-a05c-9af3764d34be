# 🛠️ 自律农场开发者文档

本文档为自律农场项目的开发者提供全面的技术指导和实施细节。

## 📋 目录

1. [项目概览](#项目概览)
2. [技术架构](#技术架构)
3. [开发环境设置](#开发环境设置)
4. [代码结构](#代码结构)
5. [构建和部署](#构建和部署)
6. [核心模块说明](#核心模块说明)
7. [开发规范](#开发规范)
8. [测试策略](#测试策略)
9. [性能优化](#性能优化)
10. [故障排查](#故障排查)

---

## 🌟 项目概览

### 项目信息
- **项目名称**: 自律农场 (Self Discipline Farm)
- **版本**: 0.1.0
- **描述**: 一款通过摄像头监测行为帮助用户建立自律习惯的农场经营游戏
- **许可证**: MIT
- **开发语言**: TypeScript
- **主要框架**: React + Electron + Phaser.js

### 项目目标
- 创建一个游戏化的自律训练应用
- 利用计算机视觉技术监测用户行为
- 提供跨平台的桌面应用体验
- 建立可扩展的游戏系统架构

### 核心特性
- 🎮 基于Phaser.js的游戏引擎
- 📸 MediaPipe计算机视觉集成
- 🖥️ Electron跨平台桌面应用
- ⚛️ React现代UI组件系统
- 🔄 Zustand状态管理
- 📊 数据可视化和分析

---

## 🏗️ 技术架构

### 整体架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    自律农场应用架构                          │
├─────────────────────────────────────────────────────────────┤
│                    Electron主进程                           │
│  ┌─────────────────┐  ┌─────────────────┐ ┌──────────────┐   │
│  │   窗口管理      │  │   系统集成      │ │   IPC通信    │   │
│  └─────────────────┘  └─────────────────┘ └──────────────┘   │
├─────────────────────────────────────────────────────────────┤
│                    渲染进程 (React)                         │
│  ┌─────────────────┐  ┌─────────────────┐ ┌──────────────┐   │
│  │   UI组件层      │  │   状态管理      │ │   业务逻辑   │   │
│  │   (React)       │  │   (Zustand)     │ │   (Services) │   │
│  └─────────────────┘  └─────────────────┘ └──────────────┘   │
├─────────────────────────────────────────────────────────────┤
│                    游戏引擎层                               │
│  ┌─────────────────┐  ┌─────────────────┐ ┌──────────────┐   │
│  │   Phaser.js     │  │   游戏场景      │ │   游戏对象   │   │
│  │   引擎核心      │  │   管理          │ │   管理       │   │
│  └─────────────────┘  └─────────────────┘ └──────────────┘   │
├─────────────────────────────────────────────────────────────┤
│                    计算机视觉层                             │
│  ┌─────────────────┐  ┌─────────────────┐ ┌──────────────┐   │
│  │   MediaPipe     │  │   姿态检测      │ │   行为分析   │   │
│  │   集成          │  │   算法          │ │   引擎       │   │
│  └─────────────────┘  └─────────────────┘ └──────────────┘   │
├─────────────────────────────────────────────────────────────┤
│                    数据存储层                               │
│  ┌─────────────────┐  ┌─────────────────┐ ┌──────────────┐   │
│  │   本地存储      │  │   设置管理      │ │   同步服务   │   │
│  │   (IndexedDB)   │  │   (JSON)        │ │   (Cloud)    │   │
│  └─────────────────┘  └─────────────────┘ └──────────────┘   │
└─────────────────────────────────────────────────────────────┘
```

### 技术栈详解

#### 前端技术栈
- **React 18.2.0**: 现代React框架，支持并发特性
- **TypeScript 5.0.2**: 类型安全的JavaScript超集
- **Vite 6.3.5**: 快速的构建工具和开发服务器
- **Zustand 4.4.1**: 轻量级状态管理库
- **Lucide React**: 现代图标库

#### 游戏引擎
- **Phaser.js 3.90.0**: 2D游戏引擎
- **场景管理**: 支持多场景切换
- **物理引擎**: 内置Arcade物理系统
- **动画系统**: 补间动画和精灵动画

#### 计算机视觉
- **MediaPipe**: Google开源的计算机视觉框架
  - `@mediapipe/pose`: 姿态检测
  - `@mediapipe/camera_utils`: 摄像头工具
  - `@mediapipe/drawing_utils`: 绘图工具

#### 桌面应用
- **Electron 36.4.0**: 跨平台桌面应用框架
- **Electron Builder**: 应用打包和分发工具
- **IPC通信**: 主进程与渲染进程通信

#### 开发工具
- **ESLint**: 代码质量检查
- **Prettier**: 代码格式化
- **PostCSS**: CSS后处理器
- **Rollup**: 模块打包器

---

## 🚀 开发环境设置

### 系统要求
- **Node.js**: >= 16.0.0
- **npm**: >= 8.0.0
- **Git**: 最新版本
- **Python**: 2.7 或 3.x (构建原生模块需要)

### 快速开始

#### 1. 克隆项目
```bash
git clone https://github.com/your-org/self-discipline-farm.git
cd self-discipline-farm
```

#### 2. 安装依赖
```bash
npm install
```

#### 3. 启动开发服务器
```bash
# Web开发模式
npm run dev

# Electron开发模式
npm run electron:dev
```

#### 4. 构建项目
```bash
# Web构建
npm run build

# Electron构建
npm run electron:build

# 完整打包
npm run dist
```

### 开发脚本说明

#### 核心开发脚本
```bash
# 开发服务器
npm run dev                    # Vite开发服务器
npm run electron:dev           # Electron + Vite开发

# 构建相关
npm run build                  # 标准构建
npm run build:prod             # 生产构建（包含优化）
npm run build:analyze          # 构建 + 包大小分析
npm run electron:build         # Electron构建

# 代码质量
npm run lint                   # ESLint检查
npm run lint:fix               # 自动修复ESLint问题
npm run format                 # Prettier格式化
npm run type-check             # TypeScript类型检查

# 打包分发
npm run dist                   # 单平台打包
npm run dist:all               # 全平台打包
npm run dist:signed            # 签名打包

# 性能分析
npm run performance:check      # 性能检查
npm run performance:lighthouse # Lighthouse分析
```

#### 专业脚本
```bash
# CDN管理
npm run cdn:status             # CDN状态检查
npm run cdn:deploy             # CDN部署
npm run cdn:test               # CDN测试

# 代码签名
npm run code-sign:check        # 检查签名状态
npm run code-sign:init         # 初始化签名配置
npm run code-sign:guide        # 签名指导

# 农业系统专用
npm run dev:agriculture        # 农业模式开发
npm run build:agriculture      # 农业模式构建
npm run test:agriculture       # 农业模块测试
```

### IDE配置

#### VS Code推荐插件
```json
{
  "recommendations": [
    "ms-vscode.vscode-typescript-next",
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "ms-vscode.vscode-eslint",
    "formulahendry.auto-rename-tag",
    "christian-kohler.path-intellisense"
  ]
}
```

#### VS Code设置
```json
{
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "typescript.preferences.importModuleSpecifier": "relative",
  "emmet.includeLanguages": {
    "javascript": "javascriptreact",
    "typescript": "typescriptreact"
  }
}
```

---

## 📁 代码结构

### 项目目录结构
```
self-discipline-farm/
├── src/                        # 源代码目录
│   ├── components/             # React组件
│   │   ├── ui/                # 通用UI组件
│   │   ├── game/              # 游戏相关组件
│   │   ├── camera/            # 摄像头组件
│   │   ├── settings/          # 设置组件
│   │   └── charts/            # 图表组件
│   ├── game/                  # Phaser.js游戏逻辑
│   │   ├── scenes/            # 游戏场景
│   │   ├── objects/           # 游戏对象
│   │   ├── managers/          # 游戏管理器
│   │   └── config/            # 游戏配置
│   ├── services/              # 业务服务层
│   │   ├── CameraService.ts   # 摄像头服务
│   │   ├── DetectionService.ts # 检测服务
│   │   ├── GameService.ts     # 游戏服务
│   │   ├── SettingsService.ts # 设置服务
│   │   └── SyncService.ts     # 同步服务
│   ├── stores/                # Zustand状态管理
│   │   ├── gameStore.ts       # 游戏状态
│   │   ├── cameraStore.ts     # 摄像头状态
│   │   └── settingsStore.ts   # 设置状态
│   ├── types/                 # TypeScript类型定义
│   │   ├── game.types.ts      # 游戏类型
│   │   ├── camera.types.ts    # 摄像头类型
│   │   └── settings.types.ts  # 设置类型
│   ├── utils/                 # 工具函数
│   │   ├── camera.utils.ts    # 摄像头工具
│   │   ├── game.utils.ts      # 游戏工具
│   │   └── validation.utils.ts # 验证工具
│   ├── storage/               # 数据存储
│   │   ├── DatabaseManager.ts # 数据库管理
│   │   └── CacheManager.ts    # 缓存管理
│   ├── hooks/                 # React自定义Hook
│   ├── contexts/              # React Context
│   └── assets/                # 静态资源
├── electron/                  # Electron相关代码
│   ├── main.ts               # 主进程
│   ├── preload.ts            # 预加载脚本
│   └── ipc/                  # IPC通信
├── public/                    # 公共静态资源
├── docs/                      # 文档目录
├── scripts/                   # 构建和工具脚本
├── config/                    # 配置文件
└── dist/                      # 构建输出目录
```

### 关键文件说明

#### 核心入口文件
- `src/main.tsx`: React应用入口
- `src/App.tsx`: 主应用组件
- `electron/main.ts`: Electron主进程
- `electron/preload.ts`: 预加载脚本

#### 配置文件
- `vite.config.ts`: Vite构建配置
- `electron-builder.config.js`: Electron打包配置
- `tsconfig.json`: TypeScript配置
- `package.json`: 项目依赖和脚本

#### 游戏核心文件
- `src/game/config/GameConfig.ts`: 游戏配置
- `src/game/scenes/FarmScene.ts`: 农场场景
- `src/game/managers/CropManager.ts`: 作物管理器
- `src/game/objects/Crop.ts`: 作物对象

---

## 🔧 构建和部署

### 构建流程

#### 1. 开发构建
```bash
# 快速开发构建
npm run build-no-check

# 标准开发构建（包含类型检查）
npm run build
```

#### 2. 生产构建
```bash
# 生产构建（完整优化）
npm run build:prod

# 带分析的生产构建
npm run build:analyze
```

#### 3. Electron构建
```bash
# 编译Electron代码
npm run electron:build

# 准备分发包
npm run electron:prepare

# 打包应用
npm run dist
```

### 构建优化

#### Vite优化配置
```typescript
// vite.config.ts
export default defineConfig({
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          phaser: ['phaser'],
          mediapipe: ['@mediapipe/pose', '@mediapipe/camera_utils']
        }
      }
    },
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true
      }
    }
  }
})
```

#### 性能监控
```bash
# 运行性能检查
npm run performance:check

# Lighthouse分析
npm run performance:lighthouse

# 包大小分析
npm run build:analyze
```

### 部署策略

#### 多平台打包
```bash
# Windows平台
npm run electron:pack:win

# macOS平台
npm run electron:pack:mac

# Linux平台
npm run electron:pack:linux

# 全平台打包
npm run dist:all
```

#### 代码签名
```bash
# 检查签名状态
npm run code-sign:check

# 初始化签名配置
npm run code-sign:init

# 签名构建
npm run dist:signed
```

#### CDN部署
```bash
# 部署到CDN
npm run cdn:deploy

# 测试CDN
npm run cdn:test

# 监控CDN状态
npm run cdn:monitor
```

---

## 🧩 核心模块说明

### 1. 摄像头系统

#### CameraService
```typescript
class CameraService {
  private stream: MediaStream | null = null;
  private videoElement: HTMLVideoElement | null = null;
  
  async initializeCamera(): Promise<void>
  async requestPermissions(): Promise<boolean>
  getAvailableDevices(): Promise<MediaDeviceInfo[]>
  switchCamera(deviceId: string): Promise<void>
  startDetection(): void
  stopDetection(): void
}
```

#### 主要功能
- 摄像头设备枚举和选择
- 权限管理和错误处理
- 视频流管理和优化
- MediaPipe集成接口

### 2. 检测系统

#### DetectionService
```typescript
class DetectionService {
  private pose: Pose | null = null;
  private isDetecting: boolean = false;
  
  async initialize(): Promise<void>
  startPoseDetection(videoElement: HTMLVideoElement): void
  stopPoseDetection(): void
  onResults(results: Results): void
  calculateFocusScore(landmarks: NormalizedLandmark[]): number
}
```

#### 检测算法
- 姿态关键点检测
- 专注度评分算法
- 行为模式识别
- 实时数据处理

### 3. 游戏系统

#### GameManager
```typescript
class GameManager {
  private scene: Phaser.Scene;
  private cropManager: CropManager;
  private farmGrid: FarmGrid;
  
  initializeGame(): void
  updateFocusLevel(level: number): void
  plantCrop(position: GridPosition, cropType: CropType): void
  harvestCrop(position: GridPosition): void
  saveGameState(): void
  loadGameState(): void
}
```

#### 游戏机制
- 农场网格系统
- 作物生长算法
- 专注度与游戏进度关联
- 成就和奖励系统

### 4. 数据管理

#### DatabaseManager
```typescript
class DatabaseManager {
  private db: IDBDatabase | null = null;
  
  async initialize(): Promise<void>
  async saveUserData(data: UserData): Promise<void>
  async loadUserData(): Promise<UserData | null>
  async exportData(): Promise<Blob>
  async importData(data: Blob): Promise<void>
  async clearAllData(): Promise<void>
}
```

#### 存储策略
- IndexedDB本地存储
- 数据加密和压缩
- 自动备份机制
- 跨设备同步

### 5. 设置系统

#### SettingsService
```typescript
class SettingsService {
  private settings: Settings;
  
  loadSettings(): Settings
  saveSettings(settings: Partial<Settings>): void
  resetToDefaults(): void
  exportSettings(): string
  importSettings(data: string): boolean
  validateSettings(settings: unknown): boolean
}
```

#### 设置管理
- 配置验证和迁移
- 默认值管理
- 设置导入导出
- 实时配置更新

---

## 📜 开发规范

### 代码风格

#### TypeScript规范
```typescript
// 接口和类型定义
interface UserData {
  id: string;
  name: string;
  level: number;
  createdAt: Date;
}

// 枚举定义
enum CropType {
  KNOWLEDGE_FLOWER = 'knowledge_flower',
  POWER_TREE = 'power_tree',
  TIME_VEGETABLE = 'time_vegetable',
  MEDITATION_LOTUS = 'meditation_lotus'
}

// 类定义
class CropManager {
  private crops: Map<string, Crop> = new Map();
  
  public addCrop(crop: Crop): void {
    this.crops.set(crop.id, crop);
  }
  
  public getCrop(id: string): Crop | undefined {
    return this.crops.get(id);
  }
}
```

#### React组件规范
```tsx
// 函数组件
interface Props {
  title: string;
  onAction: () => void;
  isLoading?: boolean;
}

export const MyComponent: React.FC<Props> = ({ 
  title, 
  onAction, 
  isLoading = false 
}) => {
  const [state, setState] = useState<string>('');
  
  useEffect(() => {
    // 副作用逻辑
  }, []);
  
  const handleClick = useCallback(() => {
    onAction();
  }, [onAction]);
  
  return (
    <div className="component-container">
      <h2>{title}</h2>
      {isLoading ? <Spinner /> : <Button onClick={handleClick} />}
    </div>
  );
};
```

### 文件命名规范
- **组件文件**: PascalCase (例: `CameraPanel.tsx`)
- **服务文件**: PascalCase + Service (例: `CameraService.ts`)
- **工具文件**: camelCase + .utils (例: `camera.utils.ts`)
- **类型文件**: camelCase + .types (例: `game.types.ts`)
- **常量文件**: camelCase + .constants (例: `game.constants.ts`)

### Git提交规范
```bash
# 提交类型
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式调整
refactor: 重构
test: 测试相关
chore: 构建过程或辅助工具的变动

# 提交示例
git commit -m "feat(camera): add device switching functionality"
git commit -m "fix(game): resolve crop growth calculation issue"
git commit -m "docs(api): update MediaPipe integration guide"
```

### 代码审查清单
- [ ] 类型定义完整准确
- [ ] 错误处理机制完善
- [ ] 性能影响评估
- [ ] 安全性检查
- [ ] 单元测试覆盖
- [ ] 文档更新
- [ ] 向后兼容性

---

## 🧪 测试策略

### 测试框架
- **单元测试**: Vitest
- **集成测试**: React Testing Library
- **端到端测试**: Playwright (计划中)
- **性能测试**: Lighthouse CI

### 测试结构
```
src/
├── __tests__/              # 全局测试
├── components/
│   └── __tests__/          # 组件测试
├── services/
│   └── __tests__/          # 服务测试
├── utils/
│   └── __tests__/          # 工具函数测试
└── game/
    └── __tests__/          # 游戏逻辑测试
```

### 测试示例
```typescript
// CameraService.test.ts
import { describe, it, expect, vi } from 'vitest';
import { CameraService } from '../CameraService';

describe('CameraService', () => {
  it('should initialize camera successfully', async () => {
    const service = new CameraService();
    const mockGetUserMedia = vi.fn().mockResolvedValue({});
    
    global.navigator.mediaDevices = {
      getUserMedia: mockGetUserMedia
    } as any;
    
    await service.initializeCamera();
    
    expect(mockGetUserMedia).toHaveBeenCalled();
  });
});
```

### 测试覆盖率目标
- **单元测试**: 80%+
- **集成测试**: 主要用户流程
- **关键模块**: 90%+

---

## ⚡ 性能优化

### 渲染优化
- React.memo使用
- useCallback和useMemo优化
- 虚拟化长列表
- 图片懒加载
- 组件分割和动态导入

### 游戏性能优化
- 对象池管理
- 纹理图集优化
- 动画帧率控制
- 内存泄漏防范

### 构建优化
- 代码分割策略
- Tree-shaking配置
- 静态资源压缩
- CDN资源优化

### 性能监控
```typescript
// 性能监控示例
class PerformanceMonitor {
  static measureRender(componentName: string) {
    return (target: any, propertyKey: string, descriptor: PropertyDescriptor) => {
      const originalMethod = descriptor.value;
      
      descriptor.value = function(...args: any[]) {
        const start = performance.now();
        const result = originalMethod.apply(this, args);
        const end = performance.now();
        
        console.log(`${componentName}.${propertyKey} took ${end - start}ms`);
        return result;
      };
    };
  }
}
```

---

## 🔍 故障排查

### 常见问题和解决方案

#### 1. MediaPipe初始化失败
```typescript
// 解决方案：检查资源加载和权限
try {
  await this.pose.initialize();
} catch (error) {
  if (error.message.includes('Permission denied')) {
    // 处理权限问题
    await this.requestCameraPermission();
  } else if (error.message.includes('Resource loading')) {
    // 处理资源加载问题
    await this.loadMediaPipeResources();
  }
}
```

#### 2. Electron窗口管理问题
```typescript
// 主进程窗口创建
function createWindow() {
  const win = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js')
    }
  });
  
  // 处理窗口事件
  win.on('ready-to-show', () => {
    win.show();
  });
}
```

#### 3. 性能问题诊断
```bash
# 性能分析工具
npm run performance:check
npm run build:analyze

# 内存泄漏检测
npm run dev -- --inspect
```

### 调试工具
- Chrome DevTools
- React Developer Tools
- Electron DevTools
- Phaser Inspector

### 日志系统
```typescript
enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3
}

class Logger {
  static log(level: LogLevel, message: string, data?: any) {
    const timestamp = new Date().toISOString();
    const prefix = `[${timestamp}] ${LogLevel[level]}:`;
    
    switch (level) {
      case LogLevel.DEBUG:
        console.debug(prefix, message, data);
        break;
      case LogLevel.INFO:
        console.info(prefix, message, data);
        break;
      case LogLevel.WARN:
        console.warn(prefix, message, data);
        break;
      case LogLevel.ERROR:
        console.error(prefix, message, data);
        break;
    }
  }
}
```

---

## 🤝 贡献指南

### 开发流程
1. Fork项目到个人仓库
2. 创建功能分支
3. 实现功能并编写测试
4. 运行代码质量检查
5. 提交Pull Request
6. 代码审查和合并

### 分支策略
- `main`: 稳定的生产版本
- `develop`: 开发主分支
- `feature/*`: 功能开发分支
- `hotfix/*`: 紧急修复分支
- `release/*`: 版本发布分支

### 提交前检查
```bash
# 运行完整的预提交检查
npm run precommit

# 或者手动运行各项检查
npm run format
npm run lint:fix
npm run type-check
npm run test
```

---

## 📚 相关资源

### 技术文档
- [React官方文档](https://reactjs.org/docs)
- [Phaser.js文档](https://phaser.io/docs)
- [MediaPipe指南](https://mediapipe.dev/)
- [Electron文档](https://electronjs.org/docs)
- [TypeScript手册](https://www.typescriptlang.org/docs)

### 项目资源
- [API参考文档](./api-reference.md)
- [架构决策记录](./architecture-decisions.md)
- [性能基准测试](./performance-benchmarks.md)
- [安全性指南](./security-guidelines.md)

### 社区
- [项目GitHub](https://github.com/your-org/self-discipline-farm)
- [问题跟踪](https://github.com/your-org/self-discipline-farm/issues)
- [讨论区](https://github.com/your-org/self-discipline-farm/discussions)

---

**持续改进这个文档是我们共同的责任。如果您发现任何问题或有改进建议，请提交Issue或Pull Request！**

*最后更新：2024年6月*
*文档版本：1.0.0* 